param(
    [switch]$d,
    [string[]]$i = @()
)

# 设置工作目录
$workingDir = Split-Path -Parent $MyInvocation.MyCommand.Path
Set-Location $workingDir

# 确定编译配置
$configuration = if ($d) { "Debug" } else { "Release" }
Write-Host "Model: $configuration"

# 处理忽略目录参数
$ignoreArgs = @()
foreach ($dir in $i) {
    $ignoreArgs += "-i"
    $ignoreArgs += $dir
}

if ($ignoreArgs.Count -gt 0) {
    Write-Host "忽略目录: $($i -join ', ')"
}

# 编译所有协议项目
#dotnet build ../06-Protocols/EdgeGateway.Protocols.sln -c $configuration

# 运行发布工具
$debugArg = if ($d) { "-d" } else { "" }
dotnet run --project ../06-Protocols/Tools/ProtocolPublisher/ProtocolPublisher.csproj -- $debugArg $ignoreArgs 