#!/bin/bash

# 设置错误时退出
set -e

# 颜色定义
RED='\033[0;31m'
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
BLUE='\033[0;34m'
NC='\033[0m' # No Color

# 打印带颜色的日志函数
log_info() {
    echo -e "${BLUE}[INFO]${NC} $1"
}

log_success() {
    echo -e "${GREEN}[SUCCESS]${NC} $1"
}

log_warning() {
    echo -e "${YELLOW}[WARNING]${NC} $1"
}

log_error() {
    echo -e "${RED}[ERROR]${NC} $1"
}

print_separator() {
    echo -e "\n${BLUE}=================================================${NC}"
}

# 检查是否以 root 权限运行
if [ "$EUID" -ne 0 ]; then 
    log_error "请使用 sudo 运行此脚本"
    exit 1
fi

print_separator
log_info "开始环境安装流程..."
print_separator

# 在文件开头的函数定义部分添加以下函数
print_step() {
    echo -e "\n${BLUE}[步骤 $1/$2]${NC} $3"
    echo -e "${BLUE}----------------------------------------${NC}"
}

print_substep() {
    echo -e "${GREEN}[+]${NC} $1"
}

print_progress() {
    local current=$1
    local total=$2
    local width=50
    local percentage=$((current * 100 / total))
    local completed=$((width * current / total))
    local remaining=$((width - completed))
    
    printf "${BLUE}["
    printf '%*s' "$completed" | tr ' ' '#'
    printf '%*s' "$remaining" | tr ' ' '-'
    printf "]${NC} %d%%\n" "$percentage"
}

# 添加组件日志分隔函数
print_component_log() {
    local component=$1
    echo -e "\n${BLUE}┌──────────────────────────────────────┐${NC}"
    echo -e "${BLUE}│${NC} ${GREEN}$component${NC}"
    echo -e "${BLUE}└──────────────────────────────────────┘${NC}"
}

# 添加组件完成日志函数
print_component_complete() {
    local component=$1
    local version=$2
    echo -e "${BLUE}----------------------------------------${NC}"
    echo -e "${GREEN}✓ $component 配置完成${NC}${YELLOW}${version:+ ($version)}${NC}\n"
}

# 检查组件是否已安装的函数
check_installed() {
    if command -v $1 &> /dev/null; then
        return 0  # 已安装
    else
        return 1  # 未安装
    fi
}

# 在文件开头的函数定义部分添加以下函数
install_basic_tools() {
    print_component_log "检查基础工具"
    
    # 检查并安装 curl
    if ! check_installed curl; then
        print_substep "安装 curl..."
        apt-get update -qq
        apt-get install -y curl >/dev/null 2>&1
        if check_installed curl; then
            print_substep "curl 安装成功"
        else
            log_error "curl 安装失败"
            exit 1
        fi
    else
        print_substep "检测到 curl 已安装: $(curl --version | head -n1 | cut -d' ' -f2)"
    fi
    
    # 检查并安装 wget
    if ! check_installed wget; then
        print_substep "安装 wget..."
        apt-get update -qq
        apt-get install -y wget >/dev/null 2>&1
        if check_installed wget; then
            print_substep "wget 安装成功"
        else
            log_error "wget 安装失败"
            exit 1
        fi
    else
        print_substep "检测到 wget 已安装: $(wget --version | head -n1 | cut -d' ' -f3)"
    fi
    
    print_component_complete "基础工具" "curl $(curl --version | head -n1 | cut -d' ' -f2), wget $(wget --version | head -n1 | cut -d' ' -f3)"
}

# 修改主要安装步骤的计数
TOTAL_STEPS=5  # 增加一个步骤
CURRENT_STEP=0

print_separator
log_info "开始环境安装流程..."
print_progress $CURRENT_STEP $TOTAL_STEPS

# 添加基础工具安装步骤
CURRENT_STEP=$((CURRENT_STEP + 1))
install_basic_tools
print_progress $CURRENT_STEP $TOTAL_STEPS

# 1. .NET 9 SDK
CURRENT_STEP=$((CURRENT_STEP + 1))
print_component_log "安装 .NET 9 SDK"

if check_installed dotnet && [[ $(dotnet --version) == 9.* ]]; then
    print_substep "检测到 .NET 9 已安装: $(dotnet --version)"
    print_component_complete ".NET 9" "$(dotnet --version)"
else
    print_substep "添加 Microsoft 包源..."
    # 删除旧的包源配置（如果存在）
    rm -f /etc/apt/sources.list.d/microsoft-prod.list
    rm -f /etc/apt/sources.list.d/microsoft-prod.list.save
    rm -f packages-microsoft-prod.deb

    # 添加 Microsoft 包签名密钥
    wget https://packages.microsoft.com/config/ubuntu/20.04/packages-microsoft-prod.deb -O packages-microsoft-prod.deb 2>/dev/null
    dpkg -i packages-microsoft-prod.deb
    rm packages-microsoft-prod.deb

    print_substep "更新系统包索引..."
    apt-get update -qq

    print_substep "安装 HTTPS 传输支持..."
    apt-get install -y apt-transport-https >/dev/null 2>&1

    print_substep "添加 .NET 包源..."
    wget https://dot.net/v1/dotnet-install.sh -O dotnet-install.sh 2>/dev/null
    chmod +x dotnet-install.sh
    ./dotnet-install.sh --version latest --install-dir /usr/share/dotnet
    rm dotnet-install.sh

    # 创建符号链接
    ln -sf /usr/share/dotnet/dotnet /usr/bin/dotnet

    print_substep "安装 .NET 9 SDK..."
    apt-get update -qq
    apt-get install -y dotnet-sdk-9.0 >/dev/null 2>&1 || {
        log_warning "通过包管理器安装失败，尝试使用安装脚本..."
        ./dotnet-install.sh --version latest --install-dir /usr/share/dotnet
    }

    # 验证安装
    if command -v dotnet &> /dev/null; then
        log_success ".NET 9 安装完成 ($(dotnet --version))"
    else
        log_error ".NET 9 安装失败"
        exit 1
    fi
    print_component_complete ".NET 9" "$(dotnet --version)"
fi

print_progress $CURRENT_STEP $TOTAL_STEPS

# 2. Nginx
CURRENT_STEP=$((CURRENT_STEP + 1))
print_component_log "安装 Nginx"

if check_installed nginx; then
    print_substep "检测到 Nginx 已安装: $(nginx -v 2>&1 | cut -d'/' -f2)"
    print_substep "更新 Nginx 配置..."
else
    print_substep "安装 Nginx 服务..."
    apt-get install -y nginx >/dev/null 2>&1
fi

print_substep "配置 Nginx..."
cat > /etc/nginx/conf.d/edgegatewayui.conf <<'EOF'
server {
  listen 5006;
  #server_name localhost;
	location /api/{
		proxy_read_timeout 600;
        rewrite ^/(.*) /$1 break;
        proxy_pass http://127.0.0.1:5005;
    }

	location /ws/  {   
		proxy_pass http://127.0.0.1:5005/;        #通过配置端口指向部署websocker的项目
		proxy_http_version 1.1;    
		proxy_set_header Upgrade $http_upgrade;    
		proxy_set_header Connection "Upgrade";    
		proxy_set_header X-real-ip $remote_addr;
		proxy_set_header X-Forwarded-For $remote_addr;
	}

	location / {
        root   /opt/edgegatewayui/;
	    try_files $uri /index.html;
        index  index.html index.htm;
  }
}
EOF

print_substep "验证 Nginx 配置..."
nginx -t

print_substep "重启 Nginx 服务..."
systemctl restart nginx

print_component_complete "Nginx" "$(nginx -v 2>&1 | cut -d'/' -f2)"

print_progress $CURRENT_STEP $TOTAL_STEPS

# 3. Node.js 和 PM2
CURRENT_STEP=$((CURRENT_STEP + 1))
print_component_log "安装 Node.js 和 PM2"

# Node.js
if check_installed node; then
    NODE_VERSION=$(node -v)
    # 检查 Node.js 版本是否低于 v18
    if [[ "${NODE_VERSION:1}" < "18" ]]; then
        print_substep "检测到旧版本 Node.js (${NODE_VERSION})，正在更新..."
        # 卸载旧版本
        apt-get remove -y nodejs npm >/dev/null 2>&1
        apt-get autoremove -y >/dev/null 2>&1
        # 清理旧的 Node.js 源
        rm -rf /etc/apt/sources.list.d/nodesource.list*
        # 重新安装新版本
        curl -fsSL https://deb.nodesource.com/setup_18.x | bash - >/dev/null 2>&1
        apt-get install -y nodejs >/dev/null 2>&1
        NODE_VERSION=$(node -v)
        print_substep "Node.js 已更新到: ${NODE_VERSION}"
    else
        print_substep "检测到 Node.js 已安装: ${NODE_VERSION}"
    fi
else
    print_substep "安装 Node.js..."
    curl -fsSL https://deb.nodesource.com/setup_18.x | bash - >/dev/null 2>&1
    apt-get install -y nodejs >/dev/null 2>&1
    NODE_VERSION=$(node -v)
    print_substep "Node.js 安装完成: ${NODE_VERSION}"
fi

# PM2
if check_installed pm2; then
    PM2_VERSION=$(pm2 -v)
    print_substep "检测到 PM2 已安装: ${PM2_VERSION}"
else
    print_substep "安装 PM2..."
    npm install -g pm2 >/dev/null 2>&1
    
    PM2_VERSION=$(pm2 -v)
    print_substep "PM2 安装完成: ${PM2_VERSION}"
fi

print_component_complete "Node.js 和 PM2" "${NODE_VERSION}, PM2 ${PM2_VERSION}"
print_progress $CURRENT_STEP $TOTAL_STEPS

# 4. EdgeGateway 配置
CURRENT_STEP=$((CURRENT_STEP + 1))
print_component_log "配置 EdgeGateway 服务"

# 检查目录是否存在
if [ -d "/opt/edgegateway" ]; then
    print_substep "检测到 EdgeGateway 目录已存在"
else
    print_substep "创建应用目录结构..."
    mkdir -p /opt/edgegateway/logs
    chmod 755 /opt/edgegateway
fi

# 检查 PM2 配置是否存在
if [ -f "/opt/edgegateway/ecosystem.config.json" ]; then
    print_substep "检测到 PM2 配置文件已存在"
else
    print_substep "创建 PM2 配置文件..."
    cat > /opt/edgegateway/ecosystem.config.json <<'EOF'
{
  "apps": [
    {
      "name": "edgegateway",
      "cwd": "/opt/edgegateway",
      "script": "dotnet",
      "args": "EdgeGateway.dll",
      "watch": false,
      "exec_mode": "fork",
      "autorestart": true,
      "max_restarts": 5,
      "restart_delay": 4000,
      "exp_backoff_restart_delay": 100,
      "error_file": "/opt/edgegateway/logs/err.log",
      "out_file": "/opt/edgegateway/logs/out.log",
      "env": {
        "ASPNETCORE_ENVIRONMENT": "Production",
        "DOTNET_PRINT_TELEMETRY_MESSAGE": "false"
      } 
    }
  ]
}
EOF
fi

print_substep "配置 PM2 开机自启..."
pm2 startup ubuntu -u $USER --hp $HOME >/dev/null 2>&1

if [ -f "/opt/edgegateway/EdgeGateway.dll" ]; then
    print_substep "启动 EdgeGateway 服务..."
    cd /opt/edgegateway
    pm2 start ecosystem.config.json
    pm2 save
    log_success "EdgeGateway 服务已启动"
else
    log_warning "未检测到 EdgeGateway.dll，请部署程序后手动启动服务"
fi

print_component_complete "EdgeGateway 服务配置" ""
print_progress $CURRENT_STEP $TOTAL_STEPS

# 安装完成信息
print_separator
log_success "🎉 环境安装完成！"
print_separator

# 打印安装信息摘要（使用表格样式）
echo -e "\n${GREEN}📦 已安装组件：${NC}"
echo -e "┌─────────────┬────────────────────┐"
echo -e "│ 组件        │ 版本               │"
echo -e "├─────────────┼────────────────────┤"
echo -e "│ .NET SDK    │ $(dotnet --version) │"
echo -e "│ Nginx       │ $(nginx -v 2>&1 | cut -d'/' -f2) │"
echo -e "│ Node.js     │ $(node -v) │"
echo -e "│ PM2         │ $(pm2 -v) │"
echo -e "└─────────────┴────────────────────┘"

echo -e "\n${YELLOW}📝 重要信息：${NC}"
echo -e "┌──────────────┬─────────────────────────────────────┐"
echo -e "│ 配置项       │ 路径                                │"
echo -e "├──────────────┼─────────────────────────────────────┤"
echo -e "│ 程序目录     │ /opt/edgegateway                    │"
echo -e "│ 日志目录     │ /opt/edgegateway/logs              │"
echo -e "│ Nginx 配置   │ /etc/nginx/sites-available/default  │"
echo -e "│ PM2 配置     │ /opt/edgegateway/ecosystem.config.json │"
echo -e "└──────────────┴─────────────────────────────────────┘"

echo -e "\n${GREEN}🛠 常用命令：${NC}"
echo -e "┌─────────────┬────────────────────────────────────┐"
echo -e "│ 操作        │ 命令                               │"
echo -e "├─────────────┼────────────────────────────────────┤"
echo -e "│ 启动服务    │ cd /opt/edgegateway && pm2 start ecosystem.config.json │"
echo -e "│ 查看状态    │ pm2 status                         │"
echo -e "│ 查看日志    │ pm2 logs edgegateway               │"
echo -e "│ 重启服务    │ pm2 restart edgegateway            │"
echo -e "│ 停止服务    │ pm2 stop edgegateway               │"
echo -e "└─────────────┴────────────────────────────────────┘"

print_separator