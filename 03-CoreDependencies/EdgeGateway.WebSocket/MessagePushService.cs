namespace EdgeGateway.WebSocket;

/// <summary>
/// 消息推送服务
/// </summary>
public class MessagePushService : IMessagePushService
{
    /// <summary>
    /// 消息推送上下文
    /// </summary>
    private readonly IHubContext<WebSocketHub> _hubContext;

    /// <summary>
    /// 构造函数
    /// </summary>
    /// <param name="hubContext">消息推送上下文</param>
    public MessagePushService(IHubContext<WebSocketHub> hubContext)
    {
        _hubContext = hubContext;
    }

    /// <summary>
    /// 向指定主题推送消息
    /// </summary>
    /// <param name="topic">主题</param>
    /// <param name="message">消息内容</param>
    /// <returns></returns>
    public async Task PushMessageAsync(string topic, object message)
    {
        // 检查主题是否有订阅者
        if (WebSocketHub.HasSubscribers(topic))
        {
            await _hubContext.Clients.Group(topic).SendAsync(topic, message);
        }
    }
}