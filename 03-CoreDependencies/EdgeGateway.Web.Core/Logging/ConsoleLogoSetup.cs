using Microsoft.Extensions.DependencyInjection;
using Spectre.Console;

namespace EdgeGateway.Web.Core.Logging;

/// <summary>
///     控制台logo
/// </summary>
public static class ConsoleLogoSetup
{
    /// <summary>
    /// 添加logo
    /// </summary>
    /// <param name="services"></param>
    public static void AddLogoDisplay(this IServiceCollection services)
    {
        // 使用ASCII艺术输出logo
        AnsiConsole.Write(
            new FigletText("Fusion Track")
                .Color(Color.Green)
                .Centered());
    }
}