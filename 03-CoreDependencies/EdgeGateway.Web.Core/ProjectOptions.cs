using EdgeGateway.Core.Options;
using Microsoft.Extensions.DependencyInjection;

namespace EdgeGateway.Web.Core;

public static class ProjectOptions
{
    /// <summary>
    ///     注册项目配置选项
    /// </summary>
    /// <param name="services"></param>
    /// <returns></returns>
    public static IServiceCollection AddProjectOptions(this IServiceCollection services)
    {
        services.AddConfigurableOptions<EventBusOptions>();
        services.AddConfigurableOptions<DatabaseBackupOptions>();
        services.AddConfigurableOptions<WebSocketProxyOptions>();
        return services;
    }
}