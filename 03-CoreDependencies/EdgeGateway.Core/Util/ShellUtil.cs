namespace EdgeGateway.Core.Util;

/// <summary>
///     系统Shell命令
/// </summary>
[SuppressSniffer]
public static class ShellUtil
{   
    /// <summary>
    ///     Bash命令
    /// </summary>
    /// <param name="command"></param>
    /// <returns></returns>
    public static async Task<string> Bash(string command)
    {
        var escapedArgs = command.Replace("\"", "\\\"");
        var process = new Process
        {
            StartInfo = new ProcessStartInfo
            {
                FileName = "/bin/bash",
                Arguments = $"-c \"{escapedArgs}\"",
                RedirectStandardOutput = true,
                UseShellExecute = false,
                CreateNoWindow = true
            }
        };
        process.Start();
        var result = await process.StandardOutput.ReadToEndAsync();
        await process.WaitForExitAsync();
        process.Dispose();
        return result;
    }

    /// <summary>
    ///     cmd命令
    /// </summary>
    /// <param name="fileName"></param>
    /// <param name="args"></param>
    /// <returns></returns>
    public static async Task<string> Cmd(string fileName, string args)
    {
        var info = new ProcessStartInfo
        {
            FileName = fileName,
            Arguments = args,
            RedirectStandardOutput = true,
            CreateNoWindow = true,
            WindowStyle = ProcessWindowStyle.Hidden
        };
        using var process = Process.Start(info);
        var output = await process?.StandardOutput.ReadToEndAsync()!;
        return output;
    }
}