using System.Threading.Channels;
using Furion.ConfigurableOptions;

namespace EdgeGateway.Core.Options;

/// <summary>
///     事件总线配置.
/// </summary>
public class EventBusOptions : IConfigurableOptions
{
    /// <summary>
    ///     事件总线类型.
    /// </summary>
    public EventBusType EventBusType { get; set; }

    /// <summary>
    ///     通道消息容量
    /// </summary>
    public int Capacity { get; set; }

    /// <summary>
    ///     通道主题
    /// </summary>
    public string ChannelKey { get; set; }

    /// <summary>
    ///     超出最大容量消息策略：0等待；1删除最新的；2删除最早的；3删除写入的
    /// </summary>
    public BoundedChannelFullMode FullMode { get; set; }
}

/// <summary>
///     事件总线自定义事件源存储器类型.
/// </summary>
public enum EventBusType
{
    /// <summary>
    ///     内存.
    /// </summary>
    Memory,

    /// <summary>
    ///     Redis.
    /// </summary>
    Redis
}