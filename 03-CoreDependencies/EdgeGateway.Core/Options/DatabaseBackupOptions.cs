using Furion.ConfigurableOptions;

namespace EdgeGateway.Core.Options;

/// <summary>
/// 数据库备份配置选项
/// </summary>
public class DatabaseBackupOptions : IConfigurableOptions
{
  /// <summary>
  /// 备份存储根目录
  /// </summary>
  public string BackupRootPath { get; set; } = "/usr/local/data/backups";

  /// <summary>
  /// 自动备份配置
  /// </summary>
  public AutoBackupOptions AutoBackup { get; set; } = new();

  /// <summary>
  /// 需要备份的数据库列表
  /// 为空时备份所有数据库
  /// </summary>
  public HashSet<string> Databases { get; set; } = new();
}

/// <summary>
/// 自动备份配置
/// </summary>
public class AutoBackupOptions
{
  /// <summary>
  /// 是否启用自动备份
  /// </summary>
  public bool Enabled { get; set; } = true;

  /// <summary>
  /// 备份时间(HH:mm)
  /// </summary>
  public string BackupTime { get; set; } = "02:00";

  /// <summary>
  /// 备份间隔(天)
  /// </summary>
  public int Interval { get; set; } = 1;

  /// <summary>
  /// 保留天数(0表示永久保留)
  /// </summary>
  public int RetentionDays { get; set; } = 30;

  /// <summary>
  /// 每个数据库保留的备份数量(0表示不限制)
  /// </summary>
  public int KeepBackupCount { get; set; } = 7;

  /// <summary>
  /// 备份失败重试次数
  /// </summary>
  public int RetryCount { get; set; } = 3;
}