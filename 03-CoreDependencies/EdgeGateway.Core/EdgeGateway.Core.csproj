<Project Sdk="Microsoft.NET.Sdk">


    <PropertyGroup>
        <TargetFramework>net9.0</TargetFramework>
        <NoWarn>1701;1702;1591</NoWarn>
        <DocumentationFile>EdgeGateway.Core.xml</DocumentationFile>
    </PropertyGroup>


    <PropertyGroup Condition=" '$(Configuration)' == 'Release' ">
      <DebugType>none</DebugType>
    </PropertyGroup>


    <ItemGroup>
        <None Remove="EdgeGateway.Core.xml"/>
        <None Remove="EdgeGateway.Core.csproj.DotSettings" />
        <None Remove="EdgeSnoop.Core.xml" />
    </ItemGroup>
    
    <ItemGroup>
        <PackageReference Include="Furion.Extras.Authentication.JwtBearer" Version="4.9.7.107" />
        <PackageReference Include="Furion.Extras.ObjectMapper.Mapster" Version="4.9.7.107" />
        <PackageReference Include="Furion.Pure" Version="4.9.7.107" />
        <PackageReference Include="ini-parser-netstandard" Version="2.5.3" />
        <PackageReference Include="MQTTnet.AspNetCore" Version="5.0.1.1416" />
        <PackageReference Include="NPOI" Version="2.7.4" />
        <PackageReference Include="Spectre.Console" Version="0.50.0" />
        <PackageReference Include="UAParser" Version="3.1.47" />
        <PackageReference Include="Yitter.IdGenerator" Version="1.0.15" />
        
    </ItemGroup>

</Project>
