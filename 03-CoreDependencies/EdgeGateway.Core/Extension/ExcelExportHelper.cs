using System.Data;
using NPOI.SS.UserModel;
using NPOI.XSSF.UserModel;

namespace EdgeGateway.Core.Extension;

/// <summary>
/// Excel导出帮助类
/// </summary>
public static class ExcelExportHelper
{
    /// <summary>
    /// 导出Excel
    /// </summary>
    /// <param name="dataTable">数据表</param>
    /// <param name="sheetName">工作表名称</param>
    /// <returns>内存流</returns>
    public static MemoryStream ExportToExcel(DataTable dataTable, string sheetName)
    {
        var workbook = new XSSFWorkbook();
        var sheet = workbook.CreateSheet(sheetName);
        
        // 创建表头行
        var headerRow = sheet.CreateRow(0);
        for (var i = 0; i < dataTable.Columns.Count; i++)
        {
            var cell = headerRow.CreateCell(i);
            cell.SetCellValue(dataTable.Columns[i].ColumnName);
            
            // 设置表头样式
            var headerStyle = workbook.CreateCellStyle();
            var headerFont = workbook.CreateFont();
            headerFont.IsBold = true;
            headerStyle.SetFont(headerFont);
            cell.CellStyle = headerStyle;
        }

        // 写入数据行
        for (var i = 0; i < dataTable.Rows.Count; i++)
        {
            var row = sheet.CreateRow(i + 1);
            for (var j = 0; j < dataTable.Columns.Count; j++)
            {
                var cell = row.CreateCell(j);
                var value = dataTable.Rows[i][j];
                
                if (value != null)
                {
                    switch (value)
                    {
                        case int intValue:
                            cell.SetCellValue(intValue);
                            break;
                        case double doubleValue:
                            cell.SetCellValue(doubleValue);
                            break;
                        case DateTime dateValue:
                            cell.SetCellValue(dateValue);
                            break;
                        case bool boolValue:
                            cell.SetCellValue(boolValue);
                            break;
                        default:
                            cell.SetCellValue(value.ToString());
                            break;
                    }
                }
            }
        }

        // 自动调整列宽
        for (var i = 0; i < dataTable.Columns.Count; i++)
        {
            sheet.AutoSizeColumn(i);
        }

        // 写入到内存流
        var ms = new MemoryStream();
        workbook.Write(ms, true);
        ms.Position = 0;
        
        return ms;
    }
} 