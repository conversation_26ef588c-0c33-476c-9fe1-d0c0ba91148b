using Microsoft.Extensions.DependencyInjection;

namespace EdgeGateway_Test;

public class DeviceTest : IClassFixture<WebApplicationFactory<Program>>
{
    private readonly WebApplicationFactory<Program> _factory;

    public DeviceTest(WebApplicationFactory<Program> factory)
    {
        _factory = factory;

        // _factory = factory.WithWebHostBuilder(builder =>
        // {
        //     builder.UseEnvironment("Stage");   // 设置环境
        // });

        // 初始化 Furion
        Serve.RunNative(services =>
        {
            // 注册远程服务
            services.AddHttpRemote();
        });
    }

    // [Fact]
    // public async Task 测试请求百度()
    // {
    //     var rep = await "https://www.baidu.com".GetAsync();
    //     Assert.True(rep.IsSuccessStatusCode);
    // }

    // [Theory]
    // [InlineData(1, 2)]
    // [InlineData(3, 4)]
    // [InlineData(5, 7)]
    // public void 带参数测试(int i, int j)
    // {
    //     Assert.NotEqual(0, (i + j) % 2);
    // }

    [Fact]
    public async Task DevicePageTest()
    {
        using var client = _factory.CreateClient();
        using var response = await client.GetAsync("/api/device/page");
        response.EnsureSuccessStatusCode();
        // Assert.True(response.IsSuccessStatusCode);
        var result = await response.Content.ReadFromJsonAsync<SqlSugarPagedList<Device>>();
        Assert.NotNull(result);
    }
    
    [Theory]
    [InlineData("{\"Identifier\":\"SHProduct\",\"Description\":\"\",\"DeviceConfig\":{\"IpAddress\":\"127.0.0.1\",\"Port\":9600,\"Da2\":0,\"PlcType\":1,\"DataFormat\":0,\"StringReverse\":2,\"ReceiveUntilEmpty\":2,\"CombinedRead\":1,\"Timeout\":5000},\"Driver\":{\"DriverName\":\"OmronFinsNet\",\"DriverType\":\"PLC\"}}")]
    public async Task DeviceCreateTest(string data)
    {
        using var client = _factory.CreateClient();
        using var response = await client.PostAsync("/api/device/add", new StringContent(data));
        response.EnsureSuccessStatusCode();
    }
}