using System.Net;
using System.Text;
using EdgeGateway.Core.Extension;
using Furion.EventBus;
using Furion.FriendlyException;
using Microsoft.Extensions.DependencyInjection;
using Xunit.Abstractions;

namespace EdgeGateway_Test.System;

/// <summary>
/// </summary>
public class EventTest : IClassFixture<WebApplicationFactory<Program>>
{
    private readonly WebApplicationFactory<Program> _factory;
    private readonly ITestOutputHelper _testOutputHelper;

    public EventTest(WebApplicationFactory<Program> factory,  ITestOutputHelper testOutputHelper)
    {
        _factory = factory;
        _testOutputHelper = testOutputHelper;
        Serve.RunNative(services =>
        {
            // 注册远程服务
            services.AddEventBus();
        });
        // Task.Factory.StartNew(() =>
        // {
        //        var number = 1;
        //     while (true)
        //     {
        //         number += 1;
        //         MessageCenter.PublishAsync("xxx", number);
        //     }
        // });
    }
    

    [Fact]
    public async Task EventSubTest()
    {
        await Task.Delay(1000 * 100);
        await  MessageCenter.Subscribe("xxx", async ctx =>
        {
            _testOutputHelper.WriteLine("我是动态的");
            await Task.CompletedTask;
        });
    }
}