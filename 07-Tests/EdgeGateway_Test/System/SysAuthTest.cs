using EdgeGateway.Base.Auth.Dto;

namespace EdgeGateway_Test.System;

public class SysAuthTest : IClassFixture<WebApplicationFactory<Program>>
{
    private readonly WebApplicationFactory<Program> _factory;

    public SysAuthTest(WebApplicationFactory<Program> factory)
    {
        _factory = factory;
    }

    /// <summary>
    ///     登录测试
    /// </summary>
    [Theory]
    [InlineData("{\"Account\":\"admin\",\"Password\":\"e10adc3949ba59abbe56e057f20f883e\"}")]
    [InlineData("{\"Account\":\"superAdmin\",\"Password\":\"e10adc3949ba59abbe56e057f20f883e\"}")]
    public async Task LoginTest(string data)
    {
        using var client = _factory.CreateClient();
        using var response = await client.PostAsync("/api/sysAuth/login", new StringContent(data));
        response.EnsureSuccessStatusCode();
        var result = await response.Content.ReadFromJsonAsync<LoginOutput>();
        Assert.NotNull(result);
    }

    /// <summary>
    ///     登录账号信息
    /// </summary>
    [Fact]
    public async Task UserInfoTest()
    {
        using var client = _factory.CreateClient();
        using var response = await client.GetAsync("/api/sysAuth/userInfo");
        response.EnsureSuccessStatusCode();
        var result = await response.Content.ReadFromJsonAsync<LoginUserOutput>();
        Assert.NotNull(result);
    }
}