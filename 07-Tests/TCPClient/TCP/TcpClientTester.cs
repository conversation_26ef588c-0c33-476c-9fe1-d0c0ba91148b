using System.Net.Sockets;
using System.Text;

namespace EdgeGateway_Test.Driver.TCP;

/// <summary>
///     TCP客户端测试工具，用于连接服务端并定时发送指定报文
/// </summary>
public class TcpClientTester : IDisposable
{
    private readonly TcpClient _tcpClient;
    private NetworkStream? _stream;
    private readonly Timer _timer;
    private readonly List<string> _messages;
    private readonly object _lock = new();

    // 添加接收消息相关字段
    private readonly List<ReceivedMessage> _receivedMessages = new();
    private readonly object _receiveLock = new();
    private CancellationTokenSource? _receiveCts;
    private Task? _receiveTask;
    private bool _isReceiving = false;
    private int _maxReceivedMessages = 100; // 最多保存100条接收消息

    /// <summary>
    ///     当前连接状态
    /// </summary>
    public bool IsConnected { get; private set; }

    /// <summary>
    ///     服务器IP地址
    /// </summary>
    public string ServerIp { get; }

    /// <summary>
    ///     服务器端口
    /// </summary>
    public int ServerPort { get; }

    /// <summary>
    ///     是否正在接收消息
    /// </summary>
    public bool IsReceiving => _isReceiving;

    /// <summary>
    ///     初始化TCP客户端测试工具
    /// </summary>
    /// <param name="serverIp">服务器IP地址</param>
    /// <param name="serverPort">服务器端口</param>
    public TcpClientTester(string serverIp, int serverPort)
    {
        ServerIp = serverIp;
        ServerPort = serverPort;
        _tcpClient = new TcpClient();
        _messages = new List<string>();
        IsConnected = false;

        // 创建一个定时器，但不立即启动
        _timer = new Timer(SendMessages, null, Timeout.Infinite, Timeout.Infinite);
    }

    /// <summary>
    ///     连接到服务器
    /// </summary>
    /// <returns>连接结果</returns>
    public async Task<bool> ConnectAsync()
    {
        try
        {
            if (IsConnected)
            {
                Console.WriteLine("已经连接到服务器");
                return true;
            }

            Console.WriteLine($"尝试连接到服务器 {ServerIp}:{ServerPort}...");
            await _tcpClient.ConnectAsync(ServerIp, ServerPort);

            if (_tcpClient.Connected)
            {
                _stream = _tcpClient.GetStream();
                IsConnected = true;
                Console.WriteLine($"成功连接到服务器 {ServerIp}:{ServerPort}");

                // 连接成功后自动启动接收消息
                StartReceiving();

                return true;
            }

            Console.WriteLine($"无法连接到服务器 {ServerIp}:{ServerPort}");
            return false;
        }
        catch (Exception ex)
        {
            Console.WriteLine($"连接服务器时发生错误: {ex.Message}");
            return false;
        }
    }

    /// <summary>
    ///     断开服务器连接
    /// </summary>
    public void Disconnect()
    {
        try
        {
            StopSending();
            StopReceiving(); // 停止接收消息

            if (_stream != null)
            {
                _stream.Close();
                _stream = null;
            }

            _tcpClient.Close();
            IsConnected = false;
            Console.WriteLine("已断开服务器连接");
        }
        catch (Exception ex)
        {
            Console.WriteLine($"断开连接时发生错误: {ex.Message}");
        }
    }

    /// <summary>
    ///     设置要发送的报文
    /// </summary>
    /// <param name="messages">报文内容列表</param>
    public void SetMessages(List<string> messages)
    {
        lock (_lock)
        {
            _messages.Clear();
            _messages.AddRange(messages);
            Console.WriteLine($"已设置 {messages.Count} 条报文");
        }
    }

    /// <summary>
    ///     添加要发送的报文
    /// </summary>
    /// <param name="message">报文内容</param>
    public void AddMessage(string message)
    {
        lock (_lock)
        {
            _messages.Add(message);
            Console.WriteLine($"已添加报文: {message}");
        }
    }

    /// <summary>
    ///     清空所有报文
    /// </summary>
    public void ClearMessages()
    {
        lock (_lock)
        {
            _messages.Clear();
            Console.WriteLine("已清空所有报文");
        }
    }

    /// <summary>
    ///     开始定时发送报文
    /// </summary>
    /// <param name="intervalMs">发送间隔(毫秒)，默认1000ms</param>
    public void StartSending(int intervalMs = 1000)
    {
        if (!IsConnected)
        {
            Console.WriteLine("未连接到服务器，无法开始发送");
            return;
        }

        if (_messages.Count == 0)
        {
            Console.WriteLine("没有设置报文，无法开始发送");
            return;
        }

        // 启动定时器，立即开始第一次发送，之后每隔指定时间发送一次
        _timer.Change(0, intervalMs);
        Console.WriteLine($"已开始定时发送报文，间隔: {intervalMs}ms");
    }

    /// <summary>
    ///     停止定时发送报文
    /// </summary>
    public void StopSending()
    {
        // 停止定时器
        _timer.Change(Timeout.Infinite, Timeout.Infinite);
        Console.WriteLine("已停止定时发送报文");
    }

    /// <summary>
    ///     定时器回调函数，用于发送报文
    /// </summary>
    private void SendMessages(object? state)
    {
        if (!IsConnected || _stream == null)
        {
            Console.WriteLine("未连接到服务器，无法发送报文");
            StopSending();
            return;
        }

        lock (_lock)
        {
            if (_messages.Count == 0)
            {
                Console.WriteLine("没有设置报文，停止发送");
                StopSending();
                return;
            }

            try
            {
                foreach (var message in _messages)
                {
                    byte[] data;

                    // 检查是否是十六进制消息（以"HEX:"开头）
                    if (message.StartsWith("HEX:"))
                    {
                        // 解码Base64字符串获取原始字节数据
                        var base64Content = message.Substring(4); // 跳过"HEX:"前缀
                        data = Convert.FromBase64String(base64Content);

                        // 构建十六进制字符串用于日志输出
                        var hexOutput = new StringBuilder();
                        foreach (var b in data) hexOutput.Append(b.ToString("X2")).Append(' ');

                        Console.WriteLine($"已发送十六进制报文: {hexOutput.ToString().Trim()}");
                    }
                    else
                    {
                        // 普通文本消息，直接使用UTF8编码
                        data = Encoding.UTF8.GetBytes(message);
                        Console.WriteLine($"已发送报文: {message}");
                    }

                    _stream.Write(data, 0, data.Length);
                }
            }
            catch (Exception ex)
            {
                Console.WriteLine($"发送报文时发生错误: {ex.Message}");
                IsConnected = false;
                StopSending();
            }
        }
    }

    /// <summary>
    ///     获取当前设置的报文列表
    /// </summary>
    /// <returns>报文列表</returns>
    public List<string> GetMessages()
    {
        lock (_lock)
        {
            return new List<string>(_messages);
        }
    }

    /// <summary>
    ///     启动消息接收线程
    /// </summary>
    public void StartReceiving()
    {
        if (!IsConnected || _stream == null)
        {
            Console.WriteLine("未连接到服务器，无法开始接收消息");
            return;
        }

        if (_isReceiving)
        {
            Console.WriteLine("已经在接收消息中");
            return;
        }

        // 创建取消令牌
        _receiveCts = new CancellationTokenSource();
        _isReceiving = true;

        // 启动接收线程
        _receiveTask = Task.Run(async () =>
        {
            try
            {
                Console.WriteLine("开始接收服务端消息");
                byte[] buffer = new byte[4096];

                while (!_receiveCts.Token.IsCancellationRequested && _stream != null && IsConnected)
                {
                    try
                    {
                        // 检查是否有可读取的数据
                        if (_stream.DataAvailable)
                        {
                            // 读取数据
                            int bytesRead = await _stream.ReadAsync(buffer, 0, buffer.Length, _receiveCts.Token);
                            if (bytesRead > 0)
                            {
                                // 记录接收时间
                                DateTime receiveTime = DateTime.Now;

                                // 提取数据
                                byte[] receivedData = new byte[bytesRead];
                                Array.Copy(buffer, receivedData, bytesRead);

                                // 转换为十六进制字符串
                                StringBuilder hexString = new StringBuilder(bytesRead * 3);
                                foreach (byte b in receivedData)
                                {
                                    hexString.Append(b.ToString("X2")).Append(' ');
                                }

                                // 创建接收消息对象
                                var message = new ReceivedMessage
                                {
                                    Time = receiveTime,
                                    HexData = hexString.ToString().Trim(),
                                    RawData = receivedData
                                };

                                // 添加到消息列表
                                lock (_receiveLock)
                                {
                                    _receivedMessages.Add(message);

                                    // 如果超过最大消息数量，删除最旧的消息
                                    while (_receivedMessages.Count > _maxReceivedMessages)
                                    {
                                        _receivedMessages.RemoveAt(0);
                                    }
                                }

                                Console.WriteLine($"接收到消息: {message.HexData} ({bytesRead} 字节)");
                            }
                        }

                        // 短暂等待以避免CPU使用率过高
                        await Task.Delay(10, _receiveCts.Token);
                    }
                    catch (OperationCanceledException)
                    {
                        break; // 操作被取消
                    }
                    catch (Exception ex)
                    {
                        Console.WriteLine($"接收消息时发生错误: {ex.Message}");

                        // 如果连接已断开，退出循环
                        if (!IsConnected) break;

                        // 短暂等待后重试
                        await Task.Delay(100, _receiveCts.Token);
                    }
                }
            }
            catch (OperationCanceledException)
            {
                // 接收任务被取消
            }
            catch (Exception ex)
            {
                Console.WriteLine($"接收消息线程发生错误: {ex.Message}");
            }
            finally
            {
                _isReceiving = false;
                Console.WriteLine("已停止接收服务端消息");
            }
        }, _receiveCts.Token);

        Console.WriteLine("已启动消息接收线程");
    }

    /// <summary>
    ///     停止接收消息
    /// </summary>
    public void StopReceiving()
    {
        if (_receiveCts != null)
        {
            _receiveCts.Cancel();
            _receiveCts.Dispose();
            _receiveCts = null;
            _isReceiving = false;
            Console.WriteLine("已停止接收消息");
        }
    }

    /// <summary>
    ///     获取接收到的消息列表
    /// </summary>
    /// <param name="count">最大返回数量，默认返回全部</param>
    /// <returns>接收消息列表</returns>
    public List<ReceivedMessage> GetReceivedMessages(int count = 0)
    {
        lock (_receiveLock)
        {
            if (count <= 0 || count >= _receivedMessages.Count)
            {
                return new List<ReceivedMessage>(_receivedMessages);
            }

            // 返回最近的count条消息
            return _receivedMessages.Skip(_receivedMessages.Count - count).ToList();
        }
    }

    /// <summary>
    ///     清空接收到的消息
    /// </summary>
    public void ClearReceivedMessages()
    {
        lock (_receiveLock)
        {
            _receivedMessages.Clear();
            Console.WriteLine("已清空接收消息列表");
        }
    }

    /// <summary>
    ///     设置最大保存的接收消息数量
    /// </summary>
    /// <param name="maxCount">最大消息数量</param>
    public void SetMaxReceivedMessages(int maxCount)
    {
        if (maxCount > 0)
        {
            _maxReceivedMessages = maxCount;
            Console.WriteLine($"已设置最大接收消息数量为 {maxCount}");

            // 如果当前消息数量超过最大值，删除旧消息
            lock (_receiveLock)
            {
                while (_receivedMessages.Count > _maxReceivedMessages)
                {
                    _receivedMessages.RemoveAt(0);
                }
            }
        }
    }

    /// <summary>
    ///     获取可读的报文列表（发送消息）
    /// </summary>
    /// <returns>可读的报文列表</returns>
    public List<string> GetReadableMessages()
    {
        lock (_lock)
        {
            return new List<string>(_messages);
        }
    }

    /// <summary>
    ///     释放资源
    /// </summary>
    public void Dispose()
    {
        StopSending();
        StopReceiving();
        Disconnect();
        _timer.Dispose();
        GC.SuppressFinalize(this);
    }
}

/// <summary>
///     接收消息类，表示从服务端接收到的一条消息
/// </summary>
public class ReceivedMessage
{
    /// <summary>
    ///     接收时间
    /// </summary>
    public DateTime Time { get; set; }

    /// <summary>
    ///     十六进制格式的数据
    /// </summary>
    public string HexData { get; set; } = string.Empty;

    /// <summary>
    ///     原始字节数据
    /// </summary>
    public byte[] RawData { get; set; } = Array.Empty<byte>();

    /// <summary>
    ///     获取ASCII格式的数据（如果可显示）
    /// </summary>
    public string AsciiData
    {
        get
        {
            try
            {
                return Encoding.ASCII.GetString(RawData.Where(b => b >= 32 && b <= 126).ToArray());
            }
            catch
            {
                return string.Empty;
            }
        }
    }
}