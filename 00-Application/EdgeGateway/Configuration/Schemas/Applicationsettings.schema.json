{"type": "object", "title": "应用程序设置", "properties": {"Update": {"type": "object", "title": "更新设置", "description": "系统更新相关的配置", "properties": {"EncryptionKey": {"type": "string", "title": "加密密钥", "description": "更新包加密使用的Base64编码密钥", "default": "your-base64-encoded-key-here"}, "IV": {"type": "string", "title": "初始化向量", "description": "加密算法使用的Base64编码初始化向量", "default": "your-base64-encoded-iv-here"}, "RemoteApiUrl": {"type": "string", "title": "远程API地址", "description": "获取更新信息的远程API地址", "format": "uri", "default": "https://api.example.com/updates"}}}, "SystemSetting": {"type": "object", "title": "系统设置", "description": "系统的基本设置和显示信息", "properties": {"SystemName": {"type": "string", "title": "系统名称", "description": "系统的名称，显示在界面上", "default": "Edge Gateway"}, "Description": {"type": "string", "title": "系统描述", "description": "系统的简要描述", "default": "边缘网关管理系统"}, "LogoUrl": {"type": "string", "title": "Logo地址", "description": "系统Logo的URL路径", "default": "/logo.png"}, "FaviconUrl": {"type": "string", "title": "网站图标地址", "description": "浏览器标签页显示的网站图标URL路径", "default": "/favicon.ico"}, "Copyright": {"type": "string", "title": "版权信息", "description": "系统的版权信息，显示在页脚", "default": "© 2024 Edge Gateway. All Rights Reserved."}, "IcpNumber": {"type": "string", "title": "ICP备案号", "description": "网站的ICP备案号（中国境内网站需要）", "default": ""}, "AutoUpdate": {"type": "boolean", "title": "自动更新", "description": "是否启用系统自动更新功能", "default": false}}}}}