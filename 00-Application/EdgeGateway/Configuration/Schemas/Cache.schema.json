{"type": "object", "title": "缓存配置", "properties": {"Cache": {"type": "object", "title": "缓存设置", "description": "系统缓存的相关设置", "properties": {"Prefix": {"type": "string", "title": "缓存前缀", "description": "全局缓存键的前缀", "default": "edgeGateway_"}, "CacheType": {"type": "string", "title": "缓存类型", "description": "使用的缓存类型", "enum": ["Memory", "Redis"], "default": "Memory"}, "Redis": {"type": "object", "title": "Redis配置", "description": "使用Redis作为缓存时的配置", "properties": {"Configuration": {"type": "string", "title": "连接字符串", "description": "Redis服务器的连接字符串", "default": "server=127.0.0.1:6379;password=;db=0;"}, "Prefix": {"type": "string", "title": "Redis前缀", "description": "Redis缓存键的前缀", "default": "edgeGateway_"}, "MaxMessageSize": {"type": "string", "title": "最大消息大小", "description": "Redis消息的最大大小（字节）", "default": "1048576"}, "AutoDetect": {"type": "boolean", "title": "自动检测集群", "description": "是否自动检测Redis集群节点", "default": false}}}}}}}