{"type": "object", "title": "告警配置", "properties": {"Alarm": {"type": "object", "properties": {"General": {"type": "object", "title": "通用设置", "description": "报警系统的通用配置设置", "properties": {"EnableAutoAcknowledge": {"type": "boolean", "title": "启用自动确认", "description": "是否自动确认报警", "default": true}, "EnableAutoCleanup": {"type": "boolean", "title": "启用自动清理", "description": "是否自动清理报警记录", "default": true}, "AutoAcknowledgeTimeoutHours": {"type": "integer", "title": "自动确认超时时间", "description": "自动确认报警的超时时间（小时）", "minimum": 1, "maximum": 168, "default": 24}}}, "Notification": {"type": "object", "title": "通知设置", "description": "报警通知相关配置", "properties": {"EnableWebSocket": {"type": "boolean", "title": "启用WebSocket", "description": "是否通过WebSocket发送通知", "default": true}, "EnableEmail": {"type": "boolean", "title": "启用Email", "description": "是否通过Email发送通知", "default": false}, "Email": {"type": "object", "title": "Email设置", "description": "Email通知的配置", "properties": {"SmtpServer": {"type": "string", "title": "SMTP服务器", "description": "SMTP服务器地址", "default": "smtp.example.com"}, "SmtpPort": {"type": "integer", "title": "SMTP端口", "description": "SMTP服务器端口", "minimum": 1, "maximum": 65535, "default": 587}, "FromEmail": {"type": "string", "title": "发件人邮箱", "description": "发送邮件的邮箱地址", "format": "email", "default": "<EMAIL>"}, "FromName": {"type": "string", "title": "发件人名称", "description": "发送邮件的显示名称", "default": "Alarm System"}, "UseSsl": {"type": "boolean", "title": "使用SSL", "description": "是否使用SSL加密连接", "default": true}, "Username": {"type": "string", "title": "用户名", "description": "SMTP服务器认证用户名"}, "Password": {"type": "string", "title": "密码", "description": "SMTP服务器认证密码", "format": "password"}, "Recipients": {"type": "array", "title": "收件人列表", "description": "接收报警通知的邮箱列表", "items": {"type": "string", "format": "email"}}}}}}, "Storage": {"type": "object", "title": "存储设置", "description": "报警数据存储相关配置", "properties": {"RetentionDays": {"type": "integer", "title": "保留天数", "description": "报警数据保留的天数", "minimum": 1, "maximum": 365, "default": 30}, "CleanupIntervalHours": {"type": "integer", "title": "清理间隔", "description": "自动清理的时间间隔（小时）", "minimum": 1, "maximum": 168, "default": 24}, "StatisticsIntervalHours": {"type": "integer", "title": "统计间隔", "description": "报警统计的时间间隔（小时）", "minimum": 1, "maximum": 24, "default": 1}}}, "Retry": {"type": "object", "title": "重试设置", "description": "报警通知失败后的重试配置", "properties": {"MaxRetryCount": {"type": "integer", "title": "最大重试次数", "description": "通知失败后的最大重试次数", "minimum": 0, "maximum": 10, "default": 3}, "RetryInterval": {"type": "string", "title": "重试间隔", "description": "重试的时间间隔（格式：HH:MM:SS）", "pattern": "^([0-9]{2}):([0-9]{2}):([0-9]{2})$", "default": "00:00:05"}, "BatchSize": {"type": "integer", "title": "批量大小", "description": "批量处理的大小", "minimum": 1, "maximum": 1000, "default": 100}}}}}}}