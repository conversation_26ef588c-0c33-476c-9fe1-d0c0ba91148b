{"type": "object", "title": "API文档配置", "properties": {"SpecificationDocumentSettings": {"type": "object", "title": "API文档设置", "description": "Swagger API文档的相关配置", "properties": {"DocumentTitle": {"type": "string", "title": "文档标题", "description": "API文档的标题", "default": "EdgeGateway 边缘数据采集平台"}, "GroupOpenApiInfos": {"type": "array", "title": "API分组信息", "description": "API文档的分组信息配置", "items": {"type": "object", "title": "分组配置", "properties": {"Group": {"type": "string", "title": "分组名称", "description": "API分组的名称"}, "Title": {"type": "string", "title": "标题", "description": "分组的标题"}, "Description": {"type": "string", "title": "描述", "description": "分组的描述信息"}, "Version": {"type": "string", "title": "版本", "description": "API版本号", "pattern": "^\\d+\\.\\d+\\.\\d+$"}, "Contact": {"type": "object", "title": "联系人信息", "description": "API文档的联系人信息", "properties": {"Name": {"type": "string", "title": "名称", "description": "联系人名称"}, "Email": {"type": "string", "title": "邮箱", "description": "联系人邮箱", "format": "email"}, "Url": {"type": "string", "title": "网址", "description": "联系人网址", "format": "uri"}}}}}}, "DefaultGroupName": {"type": "string", "title": "默认分组名", "description": "默认的API分组名称", "default": "<PERSON><PERSON><PERSON>"}, "DocExpansionState": {"type": "string", "title": "文档展开状态", "description": "API文档的默认展开状态", "enum": ["List", "Full", "None"], "default": "None"}, "EnableAllGroups": {"type": "boolean", "title": "启用所有分组", "description": "是否启用所有API分组", "default": true}, "LoginInfo": {"type": "object", "title": "登录信息", "description": "Swagger文档的登录配置", "properties": {"Enabled": {"type": "boolean", "title": "启用登录", "description": "是否启用Swagger登录", "default": true}, "CheckUrl": {"type": "string", "title": "检查URL", "description": "登录状态检查的URL", "default": "/api/swagger/checkUrl"}, "SubmitUrl": {"type": "string", "title": "提交URL", "description": "登录表单提交的URL", "default": "/api/swagger/submitUrl"}}}, "EnumToNumber": {"type": "boolean", "title": "枚举转数字", "description": "是否将枚举类型生成为值类型", "default": true}}}}}