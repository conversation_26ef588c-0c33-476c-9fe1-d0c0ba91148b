{"type": "object", "title": "应用程序配置", "properties": {"Urls": {"type": "string", "title": "服务地址", "description": "HTTP服务监听地址和端口", "default": "http://*:5005"}, "AllowedHosts": {"type": "string", "title": "允许的主机", "description": "允许访问的主机配置", "default": "*"}, "WebSocketProxy": {"type": "object", "title": "WebSocket代理配置", "description": "WebSocket代理相关设置", "properties": {"Enabled": {"type": "boolean", "title": "启用WebSocket代理", "description": "是否启用WebSocket代理功能", "default": false}, "ServerUrl": {"type": "string", "title": "服务器地址", "description": "WebSocket代理服务器地址", "default": "ws://localhost:5000/gateway"}, "MaxRetryDelayMs": {"type": "integer", "title": "最大重试延迟", "description": "连接失败后的最大重试延迟时间(毫秒)", "minimum": 1000, "maximum": 300000, "default": 30000}, "ConnectionTimeoutSeconds": {"type": "integer", "title": "连接超时时间", "description": "WebSocket连接超时时间(秒)", "minimum": 5, "maximum": 300, "default": 30}, "KeepAliveIntervalSeconds": {"type": "integer", "title": "心跳间隔", "description": "WebSocket心跳间隔时间(秒)", "minimum": 5, "maximum": 300, "default": 30}}}, "AppSettings": {"type": "object", "title": "应用设置", "description": "应用程序通用设置", "properties": {"InjectSpecificationDocument": {"type": "boolean", "title": "注入规范文档", "description": "是否注入API规范文档(Swagger)", "default": true}, "ExternalAssemblies": {"type": "array", "title": "外部程序集", "description": "需要加载的外部程序集或插件目录", "items": {"type": "string"}, "default": ["plugins"]}}}, "DynamicApiControllerSettings": {"type": "object", "title": "动态API控制器设置", "description": "动态API控制器的相关配置", "properties": {"CamelCaseSeparator": {"type": "string", "title": "驼峰命名分隔符", "description": "驼峰命名的分隔符", "default": ""}, "SplitCamelCase": {"type": "boolean", "title": "切割驼峰命名", "description": "是否切割骆驼(驼峰)/帕斯卡命名", "default": false}, "LowercaseRoute": {"type": "boolean", "title": "小写路由", "description": "是否使用小写路由格式", "default": false}, "AsLowerCamelCase": {"type": "boolean", "title": "小驼峰命名", "description": "是否使用小驼峰命名（首字母小写）", "default": true}, "KeepVerb": {"type": "boolean", "title": "保留动词", "description": "是否保留动作方法请求谓词", "default": false}, "KeepName": {"type": "boolean", "title": "保留原名", "description": "是否保持原有名称不处理", "default": false}}}, "FriendlyExceptionSettings": {"type": "object", "title": "友好异常设置", "description": "友好异常处理的相关配置", "properties": {"DefaultErrorMessage": {"type": "string", "title": "默认错误消息", "description": "默认的错误提示消息", "default": "系统异常，请联系管理员"}, "ThrowBah": {"type": "boolean", "title": "抛出业务异常", "description": "是否将Oops.Oh默认抛出为业务异常", "default": true}, "LogError": {"type": "boolean", "title": "记录错误日志", "description": "是否输出异常日志", "default": false}}}, "CorsAccessorSettings": {"type": "object", "title": "跨域设置", "description": "跨域访问相关配置", "properties": {"WithExposedHeaders": {"type": "array", "title": "暴露的头信息", "description": "CORS响应中允许暴露的头信息", "items": {"type": "string"}}, "SignalRSupport": {"type": "boolean", "title": "SignalR支持", "description": "是否启用SignalR跨域支持", "default": true}}}, "SnowId": {"type": "object", "title": "雪花ID设置", "description": "分布式ID生成器的相关配置", "properties": {"WorkerId": {"type": "integer", "title": "工作机器ID", "description": "机器码，全局唯一", "minimum": 0, "maximum": 63, "default": 1}, "WorkerIdBitLength": {"type": "integer", "title": "机器码位长", "description": "机器码的位长度", "minimum": 1, "maximum": 19, "default": 6}, "SeqBitLength": {"type": "integer", "title": "序列数位长", "description": "序列数的位长度", "minimum": 3, "maximum": 21, "default": 6}, "WorkerPrefix": {"type": "string", "title": "工作前缀", "description": "缓存前缀", "default": "iotGateway_"}}}}}