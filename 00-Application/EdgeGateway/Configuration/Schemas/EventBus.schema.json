{"type": "object", "title": "事件总线配置", "properties": {"EventBus": {"type": "object", "title": "事件总线设置", "description": "系统事件总线的相关配置", "properties": {"EventBusType": {"type": "string", "title": "事件总线类型", "description": "使用的事件总线类型", "enum": ["Memory", "Redis"], "default": "Memory"}, "Capacity": {"type": "integer", "title": "通道容量", "description": "通道的最大容量", "minimum": 100, "maximum": 100000, "default": 5000}, "ChannelKey": {"type": "string", "title": "通道主题", "description": "事件总线通道的主题名称", "default": "eventsource_queue"}, "FullMode": {"type": "integer", "title": "满载策略", "description": "通道超出最大容量时的消息处理策略", "enum": [0, 1, 2, 3], "enumNames": ["等待", "删除最新的", "删除最早的", "删除写入的"], "default": 0}}}}}