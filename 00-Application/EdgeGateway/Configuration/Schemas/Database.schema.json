{"type": "object", "title": "数据库配置", "properties": {"DbConnection": {"type": "object", "title": "数据库连接配置", "description": "数据库连接的相关配置", "properties": {"EnableConsoleSql": {"type": "boolean", "title": "启用控制台SQL", "description": "是否在控制台打印SQL语句", "default": false}, "ConnectionConfigs": {"type": "array", "title": "连接配置列表", "description": "数据库连接配置列表", "items": {"type": "object", "title": "连接配置", "properties": {"ConfigId": {"type": "string", "title": "配置ID", "description": "数据库配置的唯一标识"}, "DbType": {"type": "string", "title": "数据库类型", "description": "数据库类型", "enum": ["MySql", "SqlServer", "Sqlite", "Oracle", "PostgreSQL", "Dm", "Kdbndp", "<PERSON>", "MySqlConnector", "Access", "OpenGauss", "QuestDB", "HG", "ClickHouse", "GBase", "Odbc", "Custom"]}, "ConnectionString": {"type": "string", "title": "连接字符串", "description": "数据库连接字符串"}, "DbSettings": {"type": "object", "title": "数据库设置", "description": "数据库级别的设置", "properties": {"EnableInitDb": {"type": "boolean", "title": "启用库初始化", "description": "是否启用数据库初始化", "default": true}, "EnableDiffLog": {"type": "boolean", "title": "启用差异日志", "description": "是否启用库表差异日志", "default": false}, "EnableUnderLine": {"type": "boolean", "title": "启用下划线", "description": "是否启用驼峰转下划线", "default": false}}}, "TableSettings": {"type": "object", "title": "表设置", "description": "表级别的设置", "properties": {"EnableInitTable": {"type": "boolean", "title": "启用表初始化", "description": "是否启用表初始化", "default": true}, "EnableIncreTable": {"type": "boolean", "title": "启用增量表更新", "description": "是否启用表增量更新", "default": false}}}, "SeedSettings": {"type": "object", "title": "种子设置", "description": "种子数据的设置", "properties": {"EnableInitSeed": {"type": "boolean", "title": "启用种子初始化", "description": "是否启用种子初始化", "default": true}, "EnableIncreSeed": {"type": "boolean", "title": "启用增量种子更新", "description": "是否启用种子增量更新", "default": false}}}}}}}}, "DatabaseBackup": {"type": "object", "title": "数据库备份配置", "description": "数据库备份的相关配置", "properties": {"BackupRootPath": {"type": "string", "title": "备份根路径", "description": "备份文件保存的根路径", "default": "/usr/local/data/backups"}, "AutoBackup": {"type": "object", "title": "自动备份设置", "description": "自动备份的相关设置", "properties": {"Enabled": {"type": "boolean", "title": "启用自动备份", "description": "是否启用自动备份功能", "default": true}, "BackupTime": {"type": "string", "title": "备份时间", "description": "每天自动备份的时间点(HH:MM格式)", "pattern": "^([0-1]?[0-9]|2[0-3]):[0-5][0-9]$", "default": "02:00"}, "Interval": {"type": "integer", "title": "备份间隔", "description": "自动备份的间隔天数", "minimum": 1, "maximum": 30, "default": 1}, "RetentionDays": {"type": "integer", "title": "保留天数", "description": "备份文件保留的天数", "minimum": 1, "maximum": 365, "default": 30}, "KeepBackupCount": {"type": "integer", "title": "保留备份数量", "description": "保留的备份文件数量", "minimum": 1, "maximum": 100, "default": 7}, "RetryCount": {"type": "integer", "title": "重试次数", "description": "备份失败后的重试次数", "minimum": 0, "maximum": 10, "default": 3}, "RetryInterval": {"type": "integer", "title": "重试间隔", "description": "备份重试的间隔时间(秒)", "minimum": 1, "maximum": 300, "default": 5}}}, "Databases": {"type": "array", "title": "数据库列表", "description": "需要备份的数据库文件列表", "items": {"type": "string"}}}}}}