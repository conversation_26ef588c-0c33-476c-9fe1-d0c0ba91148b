{"Logging": {"LogLevel": {"Default": "Information", "Microsoft.AspNetCore": "Warning", "Microsoft.EntityFrameworkCore": "Information", "System.Logging.ScheduleService": "Error", "Microsoft.Hosting.Lifetime": "Warning"}, "File": {"Enabled": false, "FileName": "logs/{0:yyyyMMdd}_{1}.log", "Append": true, "MinimumLevel": "Information", "FileSizeLimitBytes": 10485760, "MaxRollingFiles": 30}, "Database": {"Enabled": true, "MinimumLevel": "Information"}, "Monitor": {"GlobalEnabled": false, "IncludeOfMethods": [], "ExcludeOfMethods": [], "BahLogLevel": "Information", "WithReturnValue": true, "ReturnValueThreshold": 500, "JsonBehavior": "None", "JsonIndented": false, "UseUtcTimestamp": false, "ConsoleLog": true}}}