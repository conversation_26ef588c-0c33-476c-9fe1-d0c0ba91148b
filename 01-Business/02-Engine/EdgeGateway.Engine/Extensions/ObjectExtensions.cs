using System.Text.Json;
using Newtonsoft.Json;
using Newtonsoft.Json.Linq;

namespace EdgeGateway.Engine.Extensions;

/// <summary>
/// 对象扩展方法
/// </summary>
public static class ObjectExtensions
{
  /// <summary>
  /// 将对象转换为JavaScript引擎可用的值
  /// </summary>
  public static object GetJsValue(this object value)
  {
    if (value == null)
      return null;

    return value switch
    {
      // 基础类型直接返回
      string or int or long or float or double or decimal or bool => value,

      // JSON元素类型转换
      JsonElement element => ConvertJsonElement(element),
      System.Text.Json.Nodes.JsonNode node => node.Deserialize<object>(),

      // Newtonsoft.Json类型转换
      JToken token => ConvertJToken(token),

      // 字典类型转换
      IDictionary<string, object> dict => dict.ToDictionary(
          kvp => kvp.Key,
          kvp => kvp.Value?.GetJsValue()
      ),

      // 集合类型转换
      IEnumerable<object> collection => collection.Select(item => item?.GetJsValue()).ToArray(),

      // 其他对象类型序列化后再反序列化
      _ => JsonConvert.DeserializeObject<object>(
          JsonConvert.SerializeObject(value)
      )
    };
  }

  private static object ConvertJsonElement(JsonElement element)
  {
    return element.ValueKind switch
    {
      JsonValueKind.Object => element.EnumerateObject()
          .ToDictionary(p => p.Name, p => p.Value.GetJsValue()),
      JsonValueKind.Array => element.EnumerateArray()
          .Select(e => e.GetJsValue())
          .ToArray(),
      JsonValueKind.String => element.GetString(),
      JsonValueKind.Number => element.TryGetInt64(out var l) ? l : element.GetDouble(),
      JsonValueKind.True => true,
      JsonValueKind.False => false,
      JsonValueKind.Null => null,
      _ => null
    };
  }

  private static object ConvertJToken(JToken token)
  {
    return token.Type switch
    {
      JTokenType.Object => token.Children<JProperty>()
          .ToDictionary(p => p.Name, p => p.Value.GetJsValue()),
      JTokenType.Array => token.Select(t => t.GetJsValue()).ToArray(),
      JTokenType.Integer => token.Value<long>(),
      JTokenType.Float => token.Value<double>(),
      JTokenType.String => token.Value<string>(),
      JTokenType.Boolean => token.Value<bool>(),
      JTokenType.Null => null,
      _ => token.ToString()
    };
  }
}