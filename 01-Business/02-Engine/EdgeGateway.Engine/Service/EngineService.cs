using Furion.DynamicApiController;
using Microsoft.AspNetCore.Mvc;
using EdgeGateway.Engine.Attributes;
using Furion.SpecificationDocument;
using EdgeGateway.SqlSugar.SqlSugar;
using System.Dynamic;
using EdgeGateway.Shared.Utils;

namespace EdgeGateway.Engine.Service;

/// <summary>
///     引擎服务
/// </summary>
[ApiDescriptionSettings("引擎中心")]
[Route("/api/engine/")]
public class EngineService : ITransient, IDynamicApiController
{
  /// <summary>
  /// JavaScript脚本引擎对象池
  /// </summary>
  private readonly JsScriptEnginePool _enginePool;

  /// <summary>
  ///     设备访问器
  /// </summary>
  private readonly SqlSugarRepository<Device.Entity.Device> _device;

  /// <summary>
  /// 构造函数
  /// </summary>
  /// <param name="enginePool"> JavaScript脚本引擎对象池 </param>
  /// <param name="device"> 设备仓储 </param>
  public EngineService(JsScriptEnginePool enginePool, SqlSugarRepository<Device.Entity.Device> device)
  {
    _enginePool = enginePool;
    _device = device;
  }

  /// <summary>
  ///     脚本方法集合
  /// </summary>
  /// <returns></returns>
  [HttpGet("method/list")]
  [OperationId(nameof(GetList))]
  public async Task<dynamic> GetList()
  {
    // 常用方法
    var commonlyUsedMethods = new Dictionary<string, List<dynamic>>()
        {
            {
                "常用方法", []
            }
        };
    // 分类方法
    var methods = new Dictionary<string, List<dynamic>>();
    // 程序集
    var assembly = Assembly.GetExecutingAssembly();
    // 获取系统脚本类
    var classesWithAttribute = assembly.GetTypes()
        .Where(type => Attribute.IsDefined(type, typeof(EngineAttribute)));
    // 遍历系统脚本
    foreach (var classType in classesWithAttribute)
    {
      // 开放脚本方法
      var methodsWithAttribute = classType.GetMethods()
          .Where(method => Attribute.IsDefined(method, typeof(EngineMethodAttribute)));
      // 遍历系统开放脚本方法
      foreach (var method in methodsWithAttribute)
      {
        var custom = method.CustomAttributes.LastOrDefault();
        if (custom == null)
          continue;
        // 分组
        var type = custom.ConstructorArguments[4].Value?.ToString();
        // 常用方法
        var commonlyUsedMethod = custom.ConstructorArguments[5].Value;
        if (string.IsNullOrEmpty(type))
          continue;
        var methodData = new
        {
          // 方法函数
          value = custom.ConstructorArguments[0].Value,
          // 名称
          name = custom.ConstructorArguments[1].Value,
          // 描述
          desc = custom.ConstructorArguments[2].Value,
          // 示例代码
          sampleCode = custom.ConstructorArguments[3].Value,
        };
        // 是否标记为常用方法
        if (commonlyUsedMethod != null && Convert.ToBoolean(commonlyUsedMethod))
        {
          commonlyUsedMethods["常用方法"].Add(methodData);
        }
        // 添加到分类方法中
        if (methods.TryGetValue(type, out var value))
          value.Add(methodData);
        else
          methods.Add(type, [methodData]);
      }
    }
    return new
    {
      commonlyUsedMethods,
      methods
    };
  }

  /// <summary>
  /// 生成适合大模型使用的文档格式
  /// </summary>
  /// <returns>结构化的文档字符串</returns>
  [HttpGet("model/doc")]
  [OperationId(nameof(GetModelDoc))]
  public async Task<string> GetModelDoc()
  {
    var sb = new StringBuilder();

    // 添加文档标题
    sb.AppendLine("# fusiontrack 脚本引擎文档");
    sb.AppendLine();

    // 添加设备属性部分
    sb.AppendLine("## 设备属性");
    sb.AppendLine();
    sb.AppendLine("在脚本中可以通过`dev`对象访问当前设备的属性：");
    sb.AppendLine();
    sb.AppendLine("```javascript");
    sb.AppendLine("// 当前设备属性访问示例");
    sb.AppendLine("var deviceInfo = {");
    sb.AppendLine("  id: dev.id,           // 设备ID");
    sb.AppendLine("  identifier: dev.identifier,    // 设备标识符");
    sb.AppendLine("  name: dev.name,         // 设备名称");
    sb.AppendLine("  status: dev.status,       // 设备状态");
    sb.AppendLine("  lastActiveTime: dev.lastActiveTime, // 最后活动时间");
    sb.AppendLine("  onlineTime: dev.onlineTime   // 在线时长（单位：秒）");
    sb.AppendLine("};");
    sb.AppendLine("return deviceInfo;");
    sb.AppendLine("```");
    sb.AppendLine();

    // 获取脚本方法
    var assembly = Assembly.GetExecutingAssembly();
    var classesWithAttribute = assembly.GetTypes()
        .Where(type => Attribute.IsDefined(type, typeof(EngineAttribute)));

    // 添加方法分类部分
    sb.AppendLine("## 可用方法");
    sb.AppendLine();

    // 用于存储分类方法
    var methodsByCategory = new Dictionary<string, List<(string Method, string Name, string Desc, string SampleCode)>>();

    // 遍历系统脚本类
    foreach (var classType in classesWithAttribute)
    {
      // 获取开放脚本方法
      var methodsWithAttribute = classType.GetMethods()
          .Where(method => Attribute.IsDefined(method, typeof(EngineMethodAttribute)));

      // 遍历系统开放脚本方法
      foreach (var method in methodsWithAttribute)
      {
        var custom = method.CustomAttributes.LastOrDefault();
        if (custom == null)
          continue;

        // 获取方法信息
        var methodValue = custom.ConstructorArguments[0].Value?.ToString();
        var methodName = custom.ConstructorArguments[1].Value?.ToString();
        var methodDesc = custom.ConstructorArguments[2].Value?.ToString();
        var sampleCode = custom.ConstructorArguments[3].Value?.ToString();
        var category = custom.ConstructorArguments[4].Value?.ToString();

        if (string.IsNullOrEmpty(category) || string.IsNullOrEmpty(methodValue))
          continue;

        // 添加到分类方法中
        if (!methodsByCategory.TryGetValue(category, out var methods))
        {
          methods = new List<(string, string, string, string)>();
          methodsByCategory[category] = methods;
        }

        methods.Add((methodValue, methodName, methodDesc, sampleCode));
      }
    }

    // 按分类生成文档
    foreach (var category in methodsByCategory.Keys.OrderBy(k => k))
    {
      sb.AppendLine($"### {category}");
      sb.AppendLine();

      foreach (var (methodValue, methodName, methodDesc, sampleCode) in methodsByCategory[category].OrderBy(m => m.Method))
      {
        sb.AppendLine($"#### {methodName}");
        sb.AppendLine();
        sb.AppendLine($"**语法:** `{methodValue}`");
        sb.AppendLine();
        sb.AppendLine($"**描述:** {methodDesc}");
        sb.AppendLine();
        sb.AppendLine("**示例:**");
        sb.AppendLine("```javascript");
        sb.AppendLine(sampleCode);
        sb.AppendLine("```");
        sb.AppendLine();
      }
    }

    // 添加使用提示
    sb.AppendLine("## 使用提示");
    sb.AppendLine();
    sb.AppendLine("### 脚本执行环境");
    sb.AppendLine();
    sb.AppendLine("1. 脚本是**一次性执行**的，不保留状态，每次调用都会创建新的执行环境");
    sb.AppendLine("2. 脚本使用Jint引擎执行，支持ES5语法和部分ES6特性");
    sb.AppendLine("3. 脚本执行有超时限制，复杂计算可能导致执行中断");
    sb.AppendLine("4. **必须使用明确的return语句**作为脚本的最后一行，返回计算结果");
    sb.AppendLine();

    sb.AppendLine("### 变量定义最佳实践");
    sb.AppendLine();
    sb.AppendLine("1. 优先使用`var`定义变量，避免使用`const`和`let`");
    sb.AppendLine("2. 不要依赖变量提升特性，始终在使用前声明变量");
    sb.AppendLine("3. 避免使用全局变量，所有变量应在函数内部定义");
    sb.AppendLine("4. 示例：");
    sb.AppendLine("```javascript");
    sb.AppendLine("// 推荐写法");
    sb.AppendLine("var temperature = 25;");
    sb.AppendLine("var humidity = 60;");
    sb.AppendLine("var result = temperature + humidity;");
    sb.AppendLine("return result;");
    sb.AppendLine("");
    sb.AppendLine("// 不推荐写法");
    sb.AppendLine("// const temperature = 25; // 避免使用const");
    sb.AppendLine("// let humidity = 60;     // 避免使用let");
    sb.AppendLine("// temperature + humidity; // 避免隐式返回");
    sb.AppendLine("```");
    sb.AppendLine();

    sb.AppendLine("### 函数定义");
    sb.AppendLine();
    sb.AppendLine("1. 使用函数声明或函数表达式定义函数");
    sb.AppendLine("2. 避免使用箭头函数（=>）");
    sb.AppendLine("3. 函数内部必须使用return语句返回结果");
    sb.AppendLine("4. 示例：");
    sb.AppendLine("```javascript");
    sb.AppendLine("// 推荐写法");
    sb.AppendLine("function calculateAverage(a, b) {");
    sb.AppendLine("  return (a + b) / 2;");
    sb.AppendLine("}");
    sb.AppendLine("");
    sb.AppendLine("// 或者");
    sb.AppendLine("var calculateAverage = function(a, b) {");
    sb.AppendLine("  return (a + b) / 2;");
    sb.AppendLine("};");
    sb.AppendLine("");
    sb.AppendLine("// 使用函数");
    sb.AppendLine("var avg = calculateAverage(10, 20);");
    sb.AppendLine("system.Log('平均值: ' + avg);");
    sb.AppendLine("return avg;");
    sb.AppendLine("");
    sb.AppendLine("// 不推荐写法");
    sb.AppendLine("// const calculateAverage = (a, b) => (a + b) / 2; // 避免箭头函数");
    sb.AppendLine("```");
    sb.AppendLine();

    sb.AppendLine("### 数据访问");
    sb.AppendLine();
    sb.AppendLine("1. 通过`device.语法`获取设备的标签采集数据");
    sb.AppendLine("2. 通过`dev`对象访问当前设备的系统属性（如果有）");
    sb.AppendLine("3. 通过`tags`对象使用点操作符访问嵌套的标签属性");
    sb.AppendLine("4. 示例：");
    sb.AppendLine("```javascript");
    sb.AppendLine("// 获取设备的标签采集数据");
    sb.AppendLine("var prevTemperature = device.Prev('device1', 'temperature')");
    sb.AppendLine("var temperature = device.Get('device1', 'temperature');");
    sb.AppendLine("");
    sb.AppendLine("// 访问当前设备的系统属性");
    sb.AppendLine("var deviceId = dev.id;");
    sb.AppendLine("var deviceName = dev.name;");
    sb.AppendLine("");
    sb.AppendLine("// 使用点操作符访问标签属性（支持嵌套结构）");
    sb.AppendLine("var dayProductName = tags.dayProduct.name; // 访问dayProduct标签的name属性");
    sb.AppendLine("var dayProductValue = tags.dayProduct.value; // 访问dayProduct标签的value属性");
    sb.AppendLine("var temperatureValue = tags.temperature.value; // 访问temperature标签的value属性");
    sb.AppendLine("");
    sb.AppendLine("// 返回处理结果");
    sb.AppendLine("return {");
    sb.AppendLine("  prevTemp: prevTemperature,");
    sb.AppendLine("  currentTemp: temperature,");
    sb.AppendLine("  deviceInfo: { id: deviceId, name: deviceName },");
    sb.AppendLine("  productName: dayProductName");
    sb.AppendLine("};");
    sb.AppendLine("```");
    sb.AppendLine();

    sb.AppendLine("### 调试与日志");
    sb.AppendLine();
    sb.AppendLine("1. 使用`system.Log()`输出调试信息");
    sb.AppendLine("2. 日志会被记录并返回给调用者");
    sb.AppendLine("3. 示例：");
    sb.AppendLine("```javascript");
    sb.AppendLine("// 输出简单值");
    sb.AppendLine("system.Log('处理开始');");
    sb.AppendLine("system.Log(42);");
    sb.AppendLine("");
    sb.AppendLine("// 输出对象");
    sb.AppendLine("var sensorData = {name: 'sensor', value: 23.5};");
    sb.AppendLine("system.Log(sensorData);");
    sb.AppendLine("");
    sb.AppendLine("// 处理后返回结果");
    sb.AppendLine("return {");
    sb.AppendLine("  status: 'success',");
    sb.AppendLine("  data: sensorData,");
    sb.AppendLine("  timestamp: dt.Now()");
    sb.AppendLine("};");
    sb.AppendLine("```");
    sb.AppendLine();

    sb.AppendLine("### 返回值规范");
    sb.AppendLine();
    sb.AppendLine("1. **必须使用明确的return语句**作为脚本的最后一行");
    sb.AppendLine("2. 不要依赖JavaScript的隐式返回特性（最后一个表达式的值）");
    sb.AppendLine("3. 返回值可以是任何有效的JavaScript值（数字、字符串、对象、数组等）");
    sb.AppendLine("4. 示例：");
    sb.AppendLine("```javascript");
    sb.AppendLine("// 正确的返回方式");
    sb.AppendLine("var result = calculateValue();");
    sb.AppendLine("return result;");
    sb.AppendLine("");
    sb.AppendLine("// 或者直接返回");
    sb.AppendLine("return calculateValue();");
    sb.AppendLine("");
    sb.AppendLine("// 不正确的返回方式");
    sb.AppendLine("// calculateValue(); // 没有明确的return语句");
    sb.AppendLine("// result;          // 没有使用return关键字");
    sb.AppendLine("```");
    sb.AppendLine();

    sb.AppendLine("### 常见限制与注意事项");
    sb.AppendLine();
    sb.AppendLine("1. 不支持ES6+的高级特性（如Promise、async/await、类等）");
    sb.AppendLine("2. 不支持DOM和BOM对象（如window、document等）");
    sb.AppendLine("3. 避免使用递归或复杂循环，可能导致性能问题");
    sb.AppendLine("4. 脚本执行是同步的，长时间运行可能导致超时");
    sb.AppendLine("5. 使用提供的工具函数（如日期处理、加密等）而非自行实现复杂算法");
    sb.AppendLine();

    sb.AppendLine("### 最佳实践示例");
    sb.AppendLine();
    sb.AppendLine("```javascript");
    sb.AppendLine("// 温度单位转换示例");
    sb.AppendLine("function celsiusToFahrenheit(celsius) {");
    sb.AppendLine("  return (celsius * 9/5) + 32;");
    sb.AppendLine("}");
    sb.AppendLine("");
    sb.AppendLine("// 获取输入数据");
    sb.AppendLine("var inputTemp = device.Prev(dev.identifier, 'temperature');");
    sb.AppendLine("system.Log('输入温度: ' + inputTemp + '°C');");
    sb.AppendLine("");
    sb.AppendLine("// 处理数据");
    sb.AppendLine("var result = {");
    sb.AppendLine("  deviceId: dev.id,");
    sb.AppendLine("  deviceName: dev.name,");
    sb.AppendLine("  originalTemp: inputTemp,");
    sb.AppendLine("  convertedTemp: celsiusToFahrenheit(inputTemp),");
    sb.AppendLine("  timestamp: dt.Now()");
    sb.AppendLine("};");
    sb.AppendLine("");
    sb.AppendLine("system.Log('处理结果:');");
    sb.AppendLine("system.Log(result);");
    sb.AppendLine("");
    sb.AppendLine("// 返回结果");
    sb.AppendLine("return result;");
    sb.AppendLine("```");

    return sb.ToString();
  }

  /// <summary>
  /// 执行脚本 封装成一个参数
  /// </summary>
  /// <param name="dto"> 执行脚本DTO </param>
  /// <returns></returns>
  [HttpPost("execute")]
  [OperationId(nameof(Execute))]
  public async Task<dynamic> Execute(ExecuteScriptDto dto)
  {
    // 如果设备ID存在，则获取设备
    if (dto.DeviceId.HasValue && dto.DeviceId.Value > 0)
    {
      try
      {
        // 获取设备
        var device = await _device.AsQueryable()
        .Where(x => x.Id == dto.DeviceId)
        .Includes(x => x.DeviceLabel)
        .FirstAsync() ?? throw new Exception($"设备 {dto.DeviceId} 不存在");

        // 设置设备基本信息
        dto.Data["dev"] = new
        {
          id = dto.DeviceId, // 设备ID  
          identifier = device.Identifier, // 设备标识符
          name = device.Name, // 设备名称
          status = device.Status, // 设备状态
          lastActiveTime = device.LastActiveTime, // 最后活动时间
          onlineTime = 0.0 // 在线时长
        };

        // 创建支持点操作符访问的嵌套标签结构
        if (device.DeviceLabel != null && device.DeviceLabel.Any())
        {
          // 使用工具类创建嵌套标签结构
          dto.Data["tags"] = TagsHelper.CreateNestedTagsObject(
            device.DeviceLabel.ToDictionary(l => l.Identifier, l => l),
            label => label.Name,
            label => label.CurrentValue,
            label => label.UpdateTime?.ToUnixTimeMilliseconds(),
            label => label.TransitionType.ToString()
          );
        }
        else
        {
          // 如果没有标签，提供空对象
          dto.Data["tags"] = new ExpandoObject();
        }
      }
      catch (Exception ex)
      {
        // 记录错误但继续执行
        Console.WriteLine($"处理设备标签时出错: {ex.Message}");
        // 提供空标签对象以防止脚本执行失败
        dto.Data["tags"] = new ExpandoObject();
      }
    }

    // 执行脚本
    var (result, log, elapsedMilliseconds) = await _enginePool.ExecuteScriptWithLogAsync("default", dto.Script, dto.Data);
    return new
    {
      result,
      log,
      elapsedMilliseconds
    };
  }
}