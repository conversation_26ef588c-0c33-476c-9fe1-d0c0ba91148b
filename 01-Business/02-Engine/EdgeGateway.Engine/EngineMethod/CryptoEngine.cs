using EdgeGateway.Engine.Attributes;

namespace EdgeGateway.Engine.EngineMethod;

/// <summary>
///     加解密方法
/// </summary>
[Engine]
public class CryptoEngine : ISingleton
{
  /// <summary>
  ///     MD5加密
  /// </summary>
  /// <param name="input">需要加密的字符串</param>
  /// <param name="is32">是否返回32位长度的加密字符串，默认true</param>
  /// <returns>返回加密后的字符串</returns>
  [EngineMethod("crypto.MD5('input', is32)",
      "MD5加密",
      "对字符串进行MD5加密\n参数一: 'input' 需要加密的字符串\n参数二: 'is32' 是否返回32位长度(true)，否则返回16位长度(false)，默认true",
      "var md5str = crypto.MD5('hello world', true);\nreturn md5str;",
      "加密解密",
      result: "该方法返回MD5加密后的字符串，根据is32参数返回32位或16位长度的十六进制字符串。例如：'5EB63BBBE01EEED093CB22BB8F5ACDC3'（32位）或'BBE01EEED093CB22'（16位）。")]
  public string MD5(string input, bool is32 = true)
  {
    using var md5 = System.Security.Cryptography.MD5.Create();
    var result = md5.ComputeHash(System.Text.Encoding.UTF8.GetBytes(input));
    var strResult = BitConverter.ToString(result).Replace("-", "");
    return is32 ? strResult : strResult.Substring(8, 16);
  }

  /// <summary>
  ///     Base64加密
  /// </summary>
  /// <param name="input">需要加密的字符串</param>
  /// <returns>返回Base64加密后的字符串</returns>
  [EngineMethod("crypto.Encode('input')",
      "Base64编码",
      "将字符串进行Base64编码\n参数一: 'input' 输入字符串",
      "var encoded = crypto.Encode('hello');\nreturn encoded;",
      "加密",
      result: "该方法返回Base64编码后的字符串。例如：输入'hello'返回'aGVsbG8='。")]
  public string Encode(string input)
  {
    var bytes = System.Text.Encoding.UTF8.GetBytes(input);
    return Convert.ToBase64String(bytes);
  }

  /// <summary>
  ///     Base64解密
  /// </summary>
  /// <param name="input">需要解密的Base64字符串</param>
  /// <returns>返回解密后的字符串</returns>
  [EngineMethod("crypto.Decode('input')",
      "Base64解码",
      "将Base64编码的字符串解码\n参数一: 'input' Base64编码的字符串",
      "var decoded = crypto.Decode('aGVsbG8=');\nreturn decoded;",
      "加密",
      result: "该方法返回Base64解码后的原始字符串。例如：输入'aGVsbG8='返回'hello'。如果输入的不是有效的Base64编码将抛出异常。")]
  public string Decode(string input)
  {
    try
    {
      var bytes = Convert.FromBase64String(input);
      return System.Text.Encoding.UTF8.GetString(bytes);
    }
    catch (Exception ex)
    {
      throw Oops.Oh($"Base64解密失败: {ex.Message}");
    }
  }

  /// <summary>
  ///     SHA1加密
  /// </summary>
  /// <param name="input">需要加密的字符串</param>
  /// <returns>返回SHA1加密后的字符串</returns>
  [EngineMethod("crypto.SHA1('input')",
      "SHA1加密",
      "对字符串进行SHA1加密\n参数一: 'input' 需要加密的字符串",
      "var sha1str = crypto.SHA1('hello world');\nreturn sha1str;",
      "加密解密",
      result: "该方法返回SHA1加密后的40位十六进制字符串。例如：'2AAE6C35C94FCFB415DBE95F408B9CE91EE846ED'。")]
  public string SHA1(string input)
  {
    using var sha1 = System.Security.Cryptography.SHA1.Create();
    var result = sha1.ComputeHash(System.Text.Encoding.UTF8.GetBytes(input));
    return BitConverter.ToString(result).Replace("-", "");
  }

  /// <summary>
  ///     SHA256加密
  /// </summary>
  /// <param name="input">需要加密的字符串</param>
  /// <returns>返回SHA256加密后的字符串</returns>
  [EngineMethod("crypto.SHA256('input')",
      "SHA256加密",
      "对字符串进行SHA256加密\n参数一: 'input' 需要加密的字符串",
      "var sha256str = crypto.SHA256('hello world');\nreturn sha256str;",
      "加密解密",
      result: "该方法返回SHA256加密后的64位十六进制字符串。例如：'B94D27B9934D3E08A52E52D7DA7DABFAC484EFE37A5380EE9088F7ACE2EFCDE9'。")]
  public string SHA256(string input)
  {
    using var sha256 = System.Security.Cryptography.SHA256.Create();
    var result = sha256.ComputeHash(System.Text.Encoding.UTF8.GetBytes(input));
    return BitConverter.ToString(result).Replace("-", "");
  }
}