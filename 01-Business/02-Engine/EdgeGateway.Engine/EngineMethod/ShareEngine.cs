using System.Globalization;
using EdgeGateway.Engine.Attributes;
using IniParser;

namespace EdgeGateway.Engine.EngineMethod;

/// <summary>
///     操作变量
/// </summary>
[Engine]
public class ShareEngine : ISingleton, IDisposable
{
    /// <summary>
    /// 读写锁
    /// </summary>
    private readonly ReaderWriterLockSlim _rwLock = new ReaderWriterLockSlim();

    /// <summary>
    /// 内存缓存
    /// </summary>
    private readonly ConcurrentDictionary<string, (string Value, DateTime? ExpireTime)> _cache = new();

    /// <summary>
    /// 写入队列
    /// </summary>
    private readonly ConcurrentQueue<(string Key, string Value, DateTime? ExpireTime)> _writeQueue = new();

    /// <summary>
    /// 写入定时器
    /// </summary>
    private readonly Timer _writeTimer;

    /// <summary>
    /// 清理定时器
    /// </summary>
    private readonly Timer _cleanupTimer;

    /// <summary>
    /// 文件路径
    /// </summary>
    private readonly string _filePath;

    /// <summary>
    /// 解析器
    /// </summary>
    private readonly FileIniDataParser _parser;

    /// <summary>
    /// 变量
    /// </summary>
    private const string Variable = "Label";

    /// <summary>
    /// 时间
    /// </summary>
    private const string Time = "Time";

    /// <summary>
    /// 构造函数
    /// </summary>
    public ShareEngine()
    {
        // 默认当前目录
        var configDirPath = Directory.GetCurrentDirectory() + "/Configs";
        // 检查目录是否存在
        if (!Directory.Exists(configDirPath))
            Directory.CreateDirectory(configDirPath);
        // 变量存储路径
        _filePath = configDirPath + "/variable.ini";
        if (!File.Exists(_filePath))
            File.Create(_filePath).Close();
        _parser = new FileIniDataParser();

        // 初始化缓存
        LoadFromFile();

        // 启动写入定时器 (每5秒批量写入一次)
        _writeTimer = new Timer(ProcessWriteQueue, null, 5000, 5000);

        // 启动清理定时器 (每5秒清理一次过期数据)
        _cleanupTimer = new Timer(CleanupExpiredItems, null, 5000, 5000);
    }

    /// <summary>
    /// 从文件加载数据到缓存
    /// </summary>
    private void LoadFromFile()
    {
        try
        {
            _rwLock.EnterReadLock();
            var data = _parser.ReadFile(_filePath);
            var timeCol = data[Time];
            var varCol = data[Variable];

            if (timeCol != null && varCol != null)
            {
                foreach (var keyData in varCol)
                {
                    DateTime? expireTime = null;
                    if (timeCol.ContainsKey(keyData.KeyName) && DateTime.TryParse(timeCol[keyData.KeyName], out var time))
                    {
                        expireTime = time;
                    }
                    _cache.TryAdd(keyData.KeyName, (keyData.Value, expireTime));
                }
            }
        }
        finally
        {
            _rwLock.ExitReadLock();
        }
    }

    /// <summary>
    /// 处理写入队列
    /// </summary>
    private void ProcessWriteQueue(object state)
    {
        if (_writeQueue.IsEmpty)
            return;

        var batchItems = new List<(string Key, string Value, DateTime? ExpireTime)>();
        while (_writeQueue.TryDequeue(out var item))
        {
            batchItems.Add(item);
        }

        if (batchItems.Count == 0)
            return;

        try
        {
            _rwLock.EnterWriteLock();
            var data = _parser.ReadFile(_filePath);

            foreach (var (key, value, expireTime) in batchItems)
            {
                data[Variable][key] = value;
                if (expireTime.HasValue)
                {
                    data[Time][key] = expireTime.Value.ToString("yyyy-MM-dd HH:mm:ss");
                }
                else
                {
                    data[Time].RemoveKey(key);
                }
            }

            _parser.WriteFile(_filePath, data);
        }
        finally
        {
            _rwLock.ExitWriteLock();
        }
    }

    /// <summary>
    /// 清理过期数据
    /// </summary>
    private void CleanupExpiredItems(object state)
    {
        var now = DateTime.Now;
        var expiredKeys = _cache
            .Where(kvp => kvp.Value.ExpireTime.HasValue && kvp.Value.ExpireTime.Value <= now)
            .Select(kvp => kvp.Key)
            .ToList();

        foreach (var key in expiredKeys)
        {
            if (_cache.TryRemove(key, out _))
            {
                _writeQueue.Enqueue((key, string.Empty, null));
            }
        }
    }

    /// <summary>
    /// 设置变量
    /// </summary>
    /// <param name="key">变量</param>
    /// <param name="value">值</param>
    /// <returns></returns>
    [EngineMethod("store.Set('key',value)",
        "设置变量",
        "设置全局共享变量\n参数一: 'key' 变量名\n参数二: 'value' 变量值",
        "store.Set('count', 10);\n// 返回：true",
        "变量",
        result: "该方法返回布尔值，表示变量是否设置成功。例如：设置'count'变量值为10返回true。")]
    public bool Set(string key, object? value)
    {
        var isValidValue = IsValidValue(value);
        if (!isValidValue)
            return false;

        var strValue = value!.ToString();
        _cache.AddOrUpdate(key, (strValue, null), (_, _) => (strValue, null));
        _writeQueue.Enqueue((key, strValue, null));
        return true;
    }

    /// <summary>
    ///     缓存变量
    /// </summary>
    /// <param name="key"></param>
    /// <param name="value"></param>
    /// <param name="expire">到期时间，单位(秒)</param>
    /// <returns></returns>
    [EngineMethod("store.Set('key',value,expire)",
        "设置变量-?秒后过期",
        "设置全局共享变量\n参数一: 'key' 变量名\n参数二: 'value' 变量值\n参数三: 'expire' 到期时间，单位(秒)",
        "store.Set('count', 10, 10);\n// 返回：true",
        "变量",
        result: "该方法返回布尔值，表示变量是否设置成功。例如：设置'count'变量值为10返回true。")]
    public bool Set(string key, object? value, int expire)
    {
        var isValidValue = IsValidValue(value);
        if (!isValidValue)
            return false;

        var expireTime = DateTime.Now.AddSeconds(expire);
        var strValue = value!.ToString();
        _cache.AddOrUpdate(key, (strValue, expireTime), (_, _) => (strValue, expireTime));
        _writeQueue.Enqueue((key, strValue, expireTime));
        return true;
    }

    /// <summary>
    ///     缓存变量
    /// </summary>
    /// <param name="key"></param>
    /// <param name="value"></param>
    /// <param name="expire">到期时间</param>
    /// <returns></returns>
    [EngineMethod("store.Set('key',value,expire)",
        "设置变量-指定时间后过期",
        "设置全局共享变量\n参数一: 'key' 变量名\n参数二: 'value' 变量值\n参数三: 'expire' 到期时间",
        "store.Set('count', 10, '2025-03-10 10:00:00');\n// 返回：true",
        "变量",
        result: "该方法返回布尔值，表示变量是否设置成功。例如：设置'count'变量值为10返回true。")]
    public bool Set(string key, object? value, string expire)
    {
        var isValidValue = IsValidValue(value);
        if (!isValidValue)
            return false;

        var expireTime = DateTime.Now.Add(CalculateTimeUntilExecution(expire));
        var strValue = value!.ToString();
        _cache.AddOrUpdate(key, (strValue, expireTime), (_, _) => (strValue, expireTime));
        _writeQueue.Enqueue((key, strValue, expireTime));
        return true;
    }

    /// <summary>
    ///     获取变量
    /// </summary>
    /// <param name="key"></param>
    /// <returns></returns>
    [EngineMethod("store.Get('key')",
        "获取变量",
        "获取全局共享变量\n参数一: 'key' 变量名",
        "var value = store.Get('count');\nreturn value;",
        "变量",
        result: "该方法返回Object类型值。例如：获取'count'变量值返回10。")]
    public object? Get(string key)
    {
        if (key == null)
            return null;

        if (_cache.TryGetValue(key, out var item))
        {
            if (item.ExpireTime.HasValue && item.ExpireTime.Value <= DateTime.Now)
            {
                Delete(key);
                return null;
            }
            return item.Value;
        }
        return null;
    }

    /// <summary>
    ///     删除变量
    /// </summary>
    /// <param name="key"></param>
    /// <returns></returns>
    /// <exception cref="AppFriendlyException"></exception>
    [EngineMethod("store.Delete('key')",
        "删除变量",
        "删除全局共享变量\n参数一: 'key' 变量名",
        "store.Delete('count');\n// 返回：true",
        "变量",
        result: "该方法返回布尔值，表示变量是否删除成功。例如：删除'count'变量返回true。")]
    public bool Delete(string key)
    {
        if (key == null)
            return false;

        if (_cache.TryRemove(key, out _))
        {
            _writeQueue.Enqueue((key, string.Empty, null));
            return true;
        }
        return false;
    }

    /// <summary>
    ///     判断变量是否存在
    /// </summary>
    /// <param name="key">变量名</param>
    /// <returns>存在返回true，不存在返回false</returns>
    [EngineMethod("store.Exists('key')",
        "判断变量是否存在",
        "判断全局共享变量是否存在\n参数一: 'key' 变量名",
        "var exists = store.Exists('count');\nreturn exists;",
        "变量",
        result: "该方法返回布尔值，表示变量是否存在。例如：判断'count'变量是否存在返回true。")]
    public bool Exists(string key)
    {
        if (key == null)
            return false;

        return _cache.TryGetValue(key, out var item) &&
               (!item.ExpireTime.HasValue || item.ExpireTime.Value > DateTime.Now);
    }

    /// <summary>
    ///     判断多个变量是否都存在
    /// </summary>
    /// <param name="keys">变量名数组</param>
    /// <returns>全部存在返回true，任一不存在返回false</returns>
    [EngineMethod("store.ExistsAll(['key1', 'key2', ...])",
        "判断多个变量是否都存在",
        "判断多个全局共享变量是否都存在\n参数一: ['key1', 'key2', ...] 变量名数组",
        "var allExist = store.ExistsAll(['count', 'name']);\nreturn allExist;",
        "变量",
        result: "该方法返回布尔值，表示变量是否存在。例如：判断'count'变量是否存在返回true。")]
    public bool ExistsAll(string[] keys)
    {
        if (keys == null || keys.Length == 0)
            return false;

        var now = DateTime.Now;
        return keys.All(key =>
            _cache.TryGetValue(key, out var item) &&
            (!item.ExpireTime.HasValue || item.ExpireTime.Value > now));
    }

    /// <summary>
    ///     判断多个变量中是否至少有一个存在
    /// </summary>
    /// <param name="keys">变量名数组</param>
    /// <returns>任一存在返回true，全部不存在返回false</returns>
    [EngineMethod("store.ExistsAny(['key1', 'key2', ...])",
        "判断多个变量中是否至少有一个存在",
        "判断多个全局共享变量中是否至少有一个存在\n参数一: ['key1', 'key2', ...] 变量名数组",
        "var anyExist = store.ExistsAny(['count', 'name']);\nreturn anyExist;",
        "变量",
        result: "该方法返回布尔值，表示变量是否存在。例如：判断'count'变量是否存在返回true。")]
    public bool ExistsAny(string[] keys)
    {
        if (keys == null || keys.Length == 0)
            return false;

        var now = DateTime.Now;
        return keys.Any(key =>
            _cache.TryGetValue(key, out var item) &&
            (!item.ExpireTime.HasValue || item.ExpireTime.Value > now));
    }

    /// <summary>
    ///     获取所有变量名
    /// </summary>
    /// <returns>所有变量名的数组</returns>
    [EngineMethod("store.GetAllKeys()",
        "获取所有变量名",
        "获取所有全局共享变量的变量名",
        "var allKeys = store.GetAllKeys();\nreturn allKeys;",
        "变量",
        result: "该方法返回字符串数组，表示所有全局共享变量的变量名。例如：获取所有变量名返回['count', 'name', ...]。")]
    public string[] GetAllKeys()
    {
        var now = DateTime.Now;
        return _cache
            .Where(kvp => !kvp.Value.ExpireTime.HasValue || kvp.Value.ExpireTime.Value > now)
            .Select(kvp => kvp.Key)
            .ToArray();
    }

    /// <summary>
    ///     获取所有变量
    /// </summary>
    /// <returns>包含所有变量的字典</returns>
    [EngineMethod("store.GetAll()",
        "获取所有变量",
        "获取所有全局共享变量",
        "var allVariables = store.GetAll();\nreturn allVariables;",
        "变量",
        result: "该方法返回字典类型值，表示所有全局共享变量的键值对。例如：获取所有变量返回{'count': 10, 'name': 'test'}。")]
    public Dictionary<string, string> GetAll()
    {
        var now = DateTime.Now;
        return _cache
            .Where(kvp => !kvp.Value.ExpireTime.HasValue || kvp.Value.ExpireTime.Value > now)
            .ToDictionary(kvp => kvp.Key, kvp => kvp.Value.Value);
    }

    #region private

    /// <summary>
    ///     验证并检查值是否合法
    /// </summary>
    /// <param name="value"></param>
    /// <returns></returns>
    private bool IsValidValue(object? value)
    {
        return value switch
        {
            null => false,
            double d => !double.IsNaN(d),
            _ => true
        };
    }

    /// <summary>
    ///     计算距离指定时间还有多长时间
    /// </summary>
    /// <param name="expirationTime"></param>
    /// <returns></returns>
    /// <exception cref="AppFriendlyException"></exception>
    private TimeSpan CalculateTimeUntilExecution(string expirationTime)
    {
        // 当前时间和指定执行时间
        var dateTimeString = DateTime.TryParseExact(expirationTime, "yyyy-MM-dd HH:mm:ss", CultureInfo.InvariantCulture, DateTimeStyles.None, out _)
            ? expirationTime
            : DateTime.Now.ToString("yyyy-MM-dd") + " " + expirationTime;
        if (!DateTime.TryParse(dateTimeString, out var targetTime)) throw Oops.Oh("错误时间格式");
        var currentTime = DateTime.Now;
        // 当前时间已经超过了指定执行时间，将执行时间加上一天
        if (currentTime > targetTime)
            targetTime = targetTime.AddDays(1);
        // 计算距离指定时间还有多长时间
        var remainingTime = targetTime - currentTime;
        return remainingTime;
    }

    #endregion

    /// <summary>
    /// 释放资源
    /// </summary>
    public void Dispose()
    {
        _writeTimer?.Dispose(); // 释放写入定时器   
        _cleanupTimer?.Dispose(); // 释放清理定时器
        _rwLock?.Dispose(); // 释放读写锁

        // 确保最后的写入队列被处理
        ProcessWriteQueue(null);
    }
}