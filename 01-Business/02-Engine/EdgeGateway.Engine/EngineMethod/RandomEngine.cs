using EdgeGateway.Engine.Attributes;
using Yitter.IdGenerator;

namespace EdgeGateway.Engine.EngineMethod;

/// <summary>
///     随机数生成方法
/// </summary>
[Engine]
public class RandomEngine : ISingleton
{
  private readonly Random _random = new();

  /// <summary>
  ///     生成指定范围的随机整数
  /// </summary>
  /// <param name="min">最小值（包含）</param>
  /// <param name="max">最大值（包含）</param>
  /// <returns>返回随机整数</returns>
  [EngineMethod("random.Integer(min, max)",
      "生成随机整数",
      "生成指定范围的随机整数\n参数一: 'min' 最小值（包含）\n参数二: 'max' 最大值（包含）",
      "var num = random.Integer(1, 100);\nreturn num; // 返回1-100之间的随机数",
      "随机数",
      result: "该方法返回min到max之间的随机整数（包含min和max）。例如：random.Integer(1, 100)可能返回42。")]
  public int Integer(int min, int max)
  {
    if (min > max)
      throw Oops.Oh("最小值不能大于最大值");
    return _random.Next(min, max + 1);
  }

  /// <summary>
  ///     生成指定范围的随机浮点数
  /// </summary>
  /// <param name="min">最小值（包含）</param>
  /// <param name="max">最大值（不包含）</param>
  /// <param name="decimals">小数位数（0-15）</param>
  /// <returns>返回随机浮点数</returns>
  [EngineMethod("random.Double(min, max, decimals)",
      "生成随机浮点数",
      "生成指定范围的随机浮点数\n参数一: 'min' 最小值（包含）\n参数二: 'max' 最大值（不包含）\n参数三: 'decimals' 小数位数（0-15），默认2",
      "var num = random.Double(0, 1, 2);\nreturn num; // 返回0-1之间的随机小数，保留2位小数",
      "随机数",
      result: "该方法返回min到max之间的随机浮点数（包含min，不包含max），精确到指定的小数位数。例如：random.Double(0, 1, 2)可能返回0.42。")]
  public double Double(double min, double max, int decimals = 2)
  {
    if (min >= max)
      throw Oops.Oh("最小值必须小于最大值");
    if (decimals < 0 || decimals > 15)
      throw Oops.Oh("小数位数必须在0-15之间");

    var value = min + _random.NextDouble() * (max - min);
    return Math.Round(value, decimals);
  }

  /// <summary>
  ///     生成随机布尔值
  /// </summary>
  /// <param name="truePercent">返回true的概率（0-100），默认50</param>
  /// <returns>返回随机布尔值</returns>
  [EngineMethod("random.Boolean(truePercent)",
      "生成随机布尔值",
      "生成随机布尔值\n参数一: 'truePercent' 返回true的概率（0-100），默认50",
      "var bool = random.Boolean(30);\nreturn bool; // 30%概率返回true",
      "随机数",
      result: "该方法返回随机布尔值（true或false），返回true的概率由truePercent参数指定。例如：random.Boolean(30)有30%的概率返回true，70%的概率返回false。")]
  public bool Boolean(int truePercent = 50)
  {
    if (truePercent < 0 || truePercent > 100)
      throw Oops.Oh("概率必须在0-100之间");
    return _random.Next(100) < truePercent;
  }

  /// <summary>
  ///     从数组中随机选择一个元素
  /// </summary>
  /// <param name="array">源数组</param>
  /// <returns>返回随机选择的元素</returns>
  [EngineMethod("random.Pick(array)",
      "随机选择元素",
      "从数组中随机选择一个元素\n参数一: 'array' 源数组",
      "var item = random.Pick([1,2,3,4,5]);\nreturn item; // 返回数组中的随机一个元素",
      "随机数",
      result: "该方法返回数组中随机选择的一个元素，类型与数组元素类型一致。例如：random.Pick([1,2,3,4,5])可能返回3。如果数组为空，则返回null。")]
  public object? Pick(object[] array)
  {
    if (array == null || array.Length == 0)
      return null;
    return array[_random.Next(array.Length)];
  }

  /// <summary>
  ///     生成UUID
  /// </summary>
  /// <param name="removeDash">是否移除短横线，默认true</param>
  /// <param name="uppercase">是否大写，默认false</param>
  /// <returns>返回UUID字符串</returns>
  [EngineMethod("random.UUID(removeDash, uppercase)",
      "生成UUID",
      "生成UUID字符串\n参数一: 'removeDash' 是否移除短横线，默认true\n参数二: 'uppercase' 是否大写，默认false",
      "var uuid = random.UUID(true, true);\nreturn uuid;",
      "随机数",
      result: "该方法返回UUID字符串。根据removeDash和uppercase参数决定格式。例如：'a1b2c3d4e5f6'（无横线小写）、'A1B2C3D4E5F6'（无横线大写）、'a1b2-c3d4-e5f6'（有横线小写）或'A1B2-C3D4-E5F6'（有横线大写）。")]
  public string UUID(bool removeDash = true, bool uppercase = false)
  {
    var guid = Guid.NewGuid().ToString();
    if (removeDash)
      guid = guid.Replace("-", "");
    return uppercase ? guid.ToUpper() : guid.ToLower();
  }

  /// <summary>
  ///     生成随机字符串
  /// </summary>
  /// <param name="length">长度</param>
  /// <param name="useNumber">是否使用数字</param>
  /// <param name="useLower">是否使用小写字母</param>
  /// <param name="useUpper">是否使用大写字母</param>
  /// <param name="useSpecial">是否使用特殊字符</param>
  /// <returns>返回随机字符串</returns>
  [EngineMethod("random.String(length, useNumber, useLower, useUpper, useSpecial)",
      "生成随机字符串",
      "生成指定长度的随机字符串\n参数一: 'length' 字符串长度\n参数二: 'useNumber' 是否使用数字\n参数三: 'useLower' 是否使用小写字母\n参数四: 'useUpper' 是否使用大写字母\n参数五: 'useSpecial' 是否使用特殊字符",
      "var str = random.String(8, true, true, true, false);\nreturn str;",
      "随机数",
      result: "该方法返回指定长度的随机字符串，字符范围由参数控制。例如：random.String(8, true, true, true, false)可能返回'a7Bx2cD9'，包含数字、小写和大写字母。")]
  public string String(int length, bool useNumber = true, bool useLower = true, bool useUpper = true, bool useSpecial = false)
  {
    if (length <= 0)
      throw Oops.Oh("长度必须大于0");

    var chars = string.Empty;
    if (useNumber) chars += "0123456789";
    if (useLower) chars += "abcdefghijklmnopqrstuvwxyz";
    if (useUpper) chars += "ABCDEFGHIJKLMNOPQRSTUVWXYZ";
    if (useSpecial) chars += "!@#$%^&*()_+-=[]{}|;:,.<>?";

    if (string.IsNullOrEmpty(chars))
      throw Oops.Oh("至少需要选择一种字符类型");

    var random = new Random();
    return new string(Enumerable.Repeat(chars, length)
        .Select(s => s[random.Next(s.Length)]).ToArray());
  }

  /// <summary>
  ///     生成随机中文字符串
  /// </summary>
  /// <param name="length">长度</param>
  /// <returns>返回随机中文字符串</returns>
  [EngineMethod("random.Chinese(length)",
      "生成随机中文",
      "生成指定长度的随机中文字符串\n参数一: 'length' 字符串长度",
      "var str = random.Chinese(4);\nreturn str;",
      "随机数",
      result: "该方法返回指定长度的随机中文字符串。例如：random.Chinese(4)可能返回'你好世界'，包含4个随机中文字符。")]
  public string Chinese(int length)
  {
    if (length <= 0)
      throw Oops.Oh("长度必须大于0");

    var random = new Random();
    var result = new StringBuilder();
    for (var i = 0; i < length; i++)
    {
      // 生成汉字的Unicode编码范围：\u4e00-\u9fa5
      var val = random.Next(0x4e00, 0x9fa5 + 1);
      result.Append(char.ConvertFromUtf32(val));
    }
    return result.ToString();
  }

  /// <summary>
  ///     生成雪花ID
  /// </summary>
  /// <returns>返回雪花ID</returns>
  [EngineMethod("random.SnowflakeId()",
      "生成雪花ID",
      "生成分布式唯一ID（雪花算法）\n",
      "var id = random.SnowflakeId();\nreturn id;",
      "随机数",
      result: "该方法返回一个基于雪花算法生成的分布式唯一ID（长整型）。例如：1234567890123456789。每次调用都会生成不同的ID值，可用于分布式系统中的唯一标识。")]
  public long SnowflakeId()
  {
    return YitIdHelper.NextId();
  }
}