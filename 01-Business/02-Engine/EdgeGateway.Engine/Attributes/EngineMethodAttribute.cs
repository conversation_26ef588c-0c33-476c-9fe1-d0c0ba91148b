namespace EdgeGateway.Engine.Attributes;

/// <summary>
///     脚本方法类
/// </summary>
[AttributeUsage(AttributeTargets.Method)]
public class EngineMethodAttribute : Attribute
{
    /// <summary>
    /// 是否记录方法调用日志
    /// </summary>
    public bool LogMethodCall { get; set; }

    /// <summary>
    ///     构造函数
    /// </summary>
    /// <param name="method">方法</param>
    /// <param name="name">名称</param>
    /// <param name="desc">描述</param>
    /// <param name="sampleCode">示例代码</param>
    /// <param name="type">类型</param>
    /// <param name="commonlyUsedMethod">是否常用方法</param>
    /// <param name="logMethodCall">是否记录方法调用日志</param>
    /// <param name="result">返回结果</param>
    public EngineMethodAttribute(string method, string name, string desc, string sampleCode, string type, bool commonlyUsedMethod = false, bool logMethodCall = true, string result = "该方法返回字符串")
    {
        Method = method;
        Desc = desc;
        SampleCode = sampleCode;
        Type = type;
        CommonlyUsedMethod = commonlyUsedMethod;
        Name = name;
        LogMethodCall = logMethodCall;
        Result = result;
    }

    /// <summary>
    ///     返回结果
    /// </summary>
    public string Result { get; set; }

    /// <summary>
    ///     方法
    /// </summary>
    public string Method { get; set; }

    /// <summary>
    ///     方法说明
    /// </summary>
    public string Name { get; set; }

    /// <summary>
    ///     方法具体描述
    /// </summary>
    public string Desc { get; set; }

    /// <summary>
    ///     示例代码
    /// </summary>
    public string SampleCode { get; set; }

    /// <summary>
    /// 分类
    /// </summary>
    public string Type { get; set; }

    /// <summary>
    /// 常用方法
    /// </summary>
    public bool CommonlyUsedMethod { get; }
}