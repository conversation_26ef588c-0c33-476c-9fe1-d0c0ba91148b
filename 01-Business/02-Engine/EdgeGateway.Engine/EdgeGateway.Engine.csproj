<Project Sdk="Microsoft.NET.Sdk">

    <PropertyGroup>
        <TargetFramework>net9.0</TargetFramework>
        <ImplicitUsings>enable</ImplicitUsings>
        <Nullable>enable</Nullable>
    </PropertyGroup>

    <PropertyGroup Condition=" '$(Configuration)' == 'Release' ">
      <DebugType>none</DebugType>
    </PropertyGroup>

    <ItemGroup>
      <PackageReference Include="Jint" Version="4.4.1" />
      
    </ItemGroup>

    <ItemGroup>
      <ProjectReference Include="..\..\..\03-CoreDependencies\EdgeGateway.Core\EdgeGateway.Core.csproj" />
      <ProjectReference Include="..\..\..\04-CommonTools\EdgeGateway.SqlSugar\EdgeGateway.SqlSugar.csproj" />
      <ProjectReference Include="..\..\01-Device\EdgeGateway.Device.Entity\EdgeGateway.Device.Entity.csproj" />
      <ProjectReference Include="..\..\00-System\EdgeGateway.Shared\EdgeGateway.Shared.csproj" />
      <ProjectReference Include="..\..\03-Storage\EdgeGateway.TimeSeriesStorage.Abstractions\EdgeGateway.TimeSeriesStorage.Abstractions.csproj" />
    </ItemGroup>

</Project>
