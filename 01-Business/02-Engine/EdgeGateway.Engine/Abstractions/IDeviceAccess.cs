namespace EdgeGateway.Engine.Abstractions;

/// <summary>
/// 设备访问接口
/// </summary>
public interface IDeviceAccess
{
  /// <summary>
  /// 写入单个属性值
  /// </summary>
  Task<bool> WriteValue(string deviceId, string propertyKey, object value);

  /// <summary>
  /// 批量写入属性值
  /// </summary>
  Task<bool> WriteBatch(string deviceId, Dictionary<string, object> values);

  /// <summary>
  /// 设置PayLoad中的单个变量值
  /// </summary>
  Task<bool> SetPayLoadVariable(string deviceId, string propertyKey, object value);

  /// <summary>
  /// 批量设置PayLoad中的变量值
  /// </summary>
  Task<bool> SetPayLoadVariableBatch(string deviceId, Dictionary<string, object> values);
}