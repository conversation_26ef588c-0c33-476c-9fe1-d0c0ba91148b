using System.Data;
using System.Dynamic;
using EdgeGateway.Engine.EngineMethod;
using Jint;

namespace EdgeGateway.Engine.EngineActions;

/// <summary>
///     执行脚本公共基类
/// </summary>
public class JsScriptEngine : ITransient, IDisposable
{
    // 当前上下文
    private static readonly AsyncLocal<ScriptLogContext> CurrentContext = new();
    // 引擎
    public readonly Jint.Engine Engine;
    // 日志上下文
    public readonly ScriptLogContext LogContext;
    // 默认变量
    private readonly Dictionary<string, object> _defaultVariables;

    /// <summary>
    ///     构造函数
    /// </summary>
    /// <param name="shareEngine"> 共享引擎 </param>
    /// <param name="dateTimeEngine"> 日期引擎 </param>
    /// <param name="systemEngine"> 系统引擎 </param>
    /// <param name="httpRequestEngine"> 请求引擎 </param>
    /// <param name="fileEngine"> 文件引擎 </param>
    /// <param name="cryptoEngine"> 加密引擎 </param>
    /// <param name="radixEngine"> 基数引擎 </param>
    /// <param name="randomEngine"> 随机引擎 </param>
    /// <param name="deviceEngine"> 设备引擎 </param>
    /// <param name="tcpEngine"> TCP引擎 </param>
    /// <param name="udpEngine"> UDP引擎 </param>
    /// <param name="serialEngine"> 串行引擎 </param>
    /// <param name="mqttEngine"> MQTT引擎 </param>
    /// <param name="timeSeriesEngine"> 时序数据引擎 </param>
    public JsScriptEngine(
        ShareEngine shareEngine,
        DateTimeEngine dateTimeEngine,
        SystemEngine systemEngine,
        HttpRequestEngine httpRequestEngine,
        FileEngine fileEngine,
        CryptoEngine cryptoEngine,
        RadixEngine radixEngine,
        RandomEngine randomEngine,
        DeviceEngine deviceEngine,
        TcpEngine tcpEngine,
        UdpEngine udpEngine,
        SerialEngine serialEngine,
        MqttEngine mqttEngine,
        TimeSeriesEngine timeSeriesEngine)
    {
        Engine = new Jint.Engine(options =>
        {
            // // 限制内存分配为4MB
            // options.LimitMemory(5_000_000);
            //
            // // 设置执行超时为4秒
            // options.TimeoutInterval(TimeSpan.FromSeconds(5));

            // // 限制最大执行语句数
            // options.MaxStatements(1000);
        });

        LogContext = new ScriptLogContext();
        SetCurrentContext(LogContext);

        _defaultVariables = new Dictionary<string, object>
        {
            { "store", shareEngine },
            { "system", systemEngine },
            { "dt", dateTimeEngine },
            { "http", httpRequestEngine },
            { "file", fileEngine },
            { "crypto", cryptoEngine },
            { "radix", radixEngine },
            { "random", randomEngine },
            { "device", deviceEngine },
            { "tcp", tcpEngine },
            { "udp", udpEngine },
            { "serial", serialEngine },
            { "mqtt", mqttEngine },
            { "ts", timeSeriesEngine }
        };

        // 初始化默认变量
        InitializeDefaultVariables();
    }

    /// <summary>
    ///     初始化默认变量
    /// </summary>
    private void InitializeDefaultVariables()
    {
        // 设置默认变量
        foreach (var variable in _defaultVariables)
            // 设置默认变量
            Engine.SetValue(variable.Key, variable.Value);
    }

    /// <summary>
    ///     重置引擎
    /// </summary>
    public void ResetEngine()
    {
        // 重置默认变量
        InitializeDefaultVariables();
        // 清除日志
        LogContext.Clear();
    }

    /// <summary>
    ///     执行脚本
    /// </summary>
    /// <param name="jsContent"></param>
    /// <param name="args"></param>
    /// <returns></returns>
    /// <exception cref="Exception"></exception>
    public object CallFunction(string jsContent, object args)
    {
        try
        {
            // 设置当前上下文
            CurrentContext.Value = LogContext;

            // 处理DataTable类型    
            if (args.GetType() == typeof(DataTable))
            {
                // 将DataTable转换为List<dynamic>
                var convertedData = ConvertDataTableToList((DataTable)args);
                // 将转换后的数据设置为data变量
                Engine.SetValue("data", convertedData);
            }
            else
            {
                // 将其他类型数据设置为data变量
                Engine.SetValue("data", args);
            }

            var result = Engine.Evaluate(jsContent, new ScriptParsingOptions
            {
                // 允许宽松解析
                Tolerant = true,
                // 允许在函数外部返回值
                AllowReturnOutsideFunction = true
            }).ToObject();

            // 执行完后清理
            Engine.SetValue("data", string.Empty);
            // 强制垃圾回收 
            GC.Collect();

            return result;
        }
        finally
        {
            // 清除当前上下文
            CurrentContext.Value = null;
            // 清除日志
            LogContext.Clear();
        }
    }

    /// <summary>
    ///     DataTable转List
    /// </summary>
    /// <param name="dataTable"></param>
    /// <returns></returns>
    private List<dynamic> ConvertDataTableToList(DataTable dataTable)
    {
        List<dynamic> list = new();

        foreach (DataRow row in dataTable.Rows)
        {
            var expando = new ExpandoObject() as IDictionary<string, object>;
            foreach (DataColumn column in dataTable.Columns) expando[column.ColumnName] = row[column];
            list.Add(expando);
        }

        return list;
    }

    public void Dispose()
    {
        // 重置引擎
        ResetEngine();
        // 释放引擎
        Engine?.Dispose();
    }

    /// <summary>
    ///  获取当前上下文的静态方法
    /// </summary>
    /// <returns></returns>
    public static ScriptLogContext? GetCurrentContext()
    {
        return CurrentContext.Value;
    }

    /// <summary>
    /// 设置当前上下文的静态方法
    /// </summary>
    /// <param name="context">上下文</param>
    public static void SetCurrentContext(ScriptLogContext? context)
    {
        CurrentContext.Value = context;
    }
}