namespace EdgeGateway.Pipeline.Entity.Model;

/// <summary>
/// 表达式配置
/// </summary>
public class ExpressionConfig
{
    /// <summary>
    /// 条件表达式
    /// 例如: "Type == 'Temperature' && Value > 0"
    /// </summary>
    public string Condition { get; set; }

    /// <summary>
    /// 数据映射配置列表
    /// </summary>
    public List<DataMapping> DataMappings { get; set; } = new();
}

/// <summary>
/// 数据映射配置
/// </summary>
public class DataMapping
{
    /// <summary>
    /// 目标字段名
    /// </summary>
    public string TargetField { get; set; }

    /// <summary>
    /// 数据来源类型
    /// </summary>
    public DataSourceType SourceType { get; set; }

    /// <summary>
    /// 数据来源值
    /// 当 SourceType 为 Static 时，直接使用该值
    /// 当 SourceType 为 Property 时，该值表示属性名
    /// </summary>
    public string SourceValue { get; set; }
}

/// <summary>
/// 数据来源类型
/// </summary>
public enum DataSourceType
{
    /// <summary>
    /// 静态值
    /// </summary>
    Static,

    /// <summary>
    /// 属性值
    /// </summary>
    Property
} 