namespace EdgeGateway.Pipeline.Entity.Model;

/// <summary>
/// 经过处理后的数据负载
/// </summary>
public class ProcessedPayLoad
{
  /// <summary>
  /// 唯一标识符
  /// </summary>
  public string Id { get; set; } = Guid.NewGuid().ToString();

  /// <summary>
  /// 处理时间戳
  /// </summary>
  public long ProcessedTimestamp { get; set; } = DateTimeOffset.UtcNow.ToUnixTimeMilliseconds();

  /// <summary>
  /// 源设备标识符列表
  /// </summary>
  public List<string> SourceDeviceIds { get; set; } = new List<string>();

  /// <summary>
  /// 处理后的数据内容
  /// </summary>
  public string ProcessedData { get; set; }

  /// <summary>
  /// 处理器标识列表，表示此数据经过了哪些处理器处理
  /// </summary>
  public List<string> ProcessorIds { get; set; } = new List<string>();

  /// <summary>
  /// 处理元数据，保存处理过程中的关键信息
  /// </summary>
  public Dictionary<string, object> Metadata { get; set; } = new Dictionary<string, object>();

  /// <summary>
  /// 关联的原始数据是否已保存
  /// </summary>
  public bool IsRawDataSaved { get; set; }

  /// <summary>
  /// 关联的原始数据存储位置
  /// </summary>
  public string RawDataLocation { get; set; }
}