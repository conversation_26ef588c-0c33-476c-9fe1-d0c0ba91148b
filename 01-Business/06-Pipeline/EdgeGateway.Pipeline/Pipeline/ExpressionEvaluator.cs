using System.Collections.Concurrent;
using Newtonsoft.Json.Linq;

namespace EdgeGateway.Pipeline.Pipeline;

/// <summary>
///     表达式计算器接口
/// </summary>
public interface IExpressionEvaluator
{
    /// <summary>
    ///     评估表达式
    /// </summary>
    /// <param name="expression">表达式字符串</param>
    /// <param name="data">数据对象</param>
    /// <returns>表达式计算结果</returns>
    Task<bool> EvaluateAsync(string expression, object data);
}

/// <summary>
///     简单表达式计算器
///     支持基本的比较运算和逻辑运算
/// </summary>
public class ExpressionEvaluator : IExpressionEvaluator
{
    /// <summary>
    /// 日志记录器
    /// </summary>
    private readonly ILogger<ExpressionEvaluator> _logger;

    /// <summary>
    /// 表达式缓存
    /// </summary>
    private readonly ConcurrentDictionary<string, Func<object, bool>> _expressionCache = new();

    /// <summary>
    /// 构造函数
    /// </summary>
    /// <param name="logger">日志记录器</param>
    public ExpressionEvaluator(ILogger<ExpressionEvaluator> logger)
    {
        _logger = logger;
    }

    /// <summary>
    ///     评估表达式
    /// </summary>
    /// <param name="expression">条件表达式，例如: "Value > 100"</param>
    /// <param name="data">要评估的数据对象</param>
    /// <returns>表达式计算结果</returns>
    public Task<bool> EvaluateAsync(string expression, object data)
    {
        try
        {
            if (string.IsNullOrWhiteSpace(expression)) return Task.FromResult(true);

            var func = _expressionCache.GetOrAdd(expression, expr => BuildExpressionFunc(expr));
            return Task.FromResult(func(data));
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "表达式计算失败: {Expression}", expression);
            throw new ExpressionEvaluationException($"表达式计算失败: {expression}", ex);
        }
    }

    /// <summary>
    /// 执行表达式
    /// </summary>
    /// <param name="expression"></param>
    /// <returns></returns>
    private Func<object, bool> BuildExpressionFunc(string expression)
    {
        return data =>
        {
            // 解析表达式
            var tokens = ParseExpression(expression);

            // 计算表达式
            return EvaluateTokens(tokens, data);
        };
    }

    /// <summary>
    /// 对表达式进行解析
    /// </summary>
    /// <param name="expression"></param>
    /// <returns></returns>
    private List<ExpressionToken> ParseExpression(string expression)
    {
        var tokens = new List<ExpressionToken>();

        // 定义所有支持的运算符，按长度降序排列以确保正确匹配
        var operators = new[] { ">=", "<=", "==", "!=", ">", "<", "&&", "||" };

        // 分割表达式
        var currentPos = 0;
        var expressionLength = expression.Length;
        var currentToken = "";

        while (currentPos < expressionLength)
        {
            var remainingExpression = expression.Substring(currentPos);
            var foundOperator = operators.FirstOrDefault(op => remainingExpression.StartsWith(op));

            if (foundOperator != null)
            {
                // 处理当前累积的标记
                var trimmedToken = currentToken.Trim();
                if (!string.IsNullOrEmpty(trimmedToken))
                {
                    tokens.Add(new ExpressionToken { Type = TokenType.Field, Value = trimmedToken });
                }

                // 添加运算符
                tokens.Add(new ExpressionToken { Type = TokenType.Operator, Value = foundOperator });

                currentPos += foundOperator.Length;
                currentToken = "";
            }
            else
            {
                currentToken += expression[currentPos];
                currentPos++;
            }
        }

        // 处理最后一个值
        var finalToken = currentToken.Trim();
        if (!string.IsNullOrEmpty(finalToken))
        {
            tokens.Add(new ExpressionToken { Type = TokenType.Value, Value = finalToken });
        }

        return tokens;
    }

    /// <summary>
    ///     评估表达式
    /// </summary>
    /// <param name="tokens">表达式令牌</param>
    /// <param name="data">数据对象</param>
    /// <returns>表达式计算结果</returns>
    /// <exception cref="ExpressionEvaluationException">当表达式格式无效时抛出异常</exception>
    private bool EvaluateTokens(List<ExpressionToken> tokens, object data)
    {
        if (tokens.Count < 3)
        {
            throw new ExpressionEvaluationException("无效的表达式格式");
        }

        var result = true;
        var currentOperator = "&&";

        for (var i = 0; i < tokens.Count; i += 3)
        {
            // 确保有足够的标记来评估
            if (i + 1 >= tokens.Count)
            {
                break;
            }

            var field = tokens[i].Value;
            var op = tokens[i + 1].Value;
            var value = tokens[i + 2].Value;

            // 获取字段值
            var propertyValue = GetPropertyValue(data, field);

            // 比较值
            var comparison = CompareValuesWithOperator(propertyValue, value, op);

            // 应用逻辑运算符
            result = ApplyOperator(result, comparison, currentOperator);

            // 获取下一个逻辑运算符（如果存在）
            if (i + 3 < tokens.Count)
            {
                currentOperator = tokens[i + 3].Value;
                i++; // 跳过逻辑运算符
            }
        }

        return result;
    }

    /// <summary>
    /// 获取属性值
    /// </summary>
    /// <param name="data">数据对象</param>
    /// <param name="propertyPath">属性路径</param>
    /// <returns>属性值</returns>
    /// <exception cref="ExpressionEvaluationException">当属性不存在时抛出异常</exception>
    private object GetPropertyValue(object data, string propertyPath)
    {
        try
        {
            if (data == null) return null;

            // 处理 PayLoad 对象
            if (data is PayLoad payload)
            {
                // 如果是直接访问 Labels 中的标签值
                if (payload.Labels.ContainsKey(propertyPath))
                {
                    return payload.Labels[propertyPath].Value;
                }
            }

            // 如果是字符串，尝试解析为 JSON
            if (data is string jsonString)
            {
                var jObject = JObject.Parse(jsonString);
                // 尝试从 Labels 字典中获取值
                if (jObject["Labels"] != null && jObject["Labels"][propertyPath] != null)
                {
                    return jObject["Labels"][propertyPath]["Value"];
                }
            }

            // 如果是普通对象，使用反射
            var type = data.GetType();
            var property = type.GetProperty(propertyPath);
            if (property != null)
            {
                return property.GetValue(data);
            }

            throw new ExpressionEvaluationException($"属性不存在: {propertyPath}");
        }
        catch (Exception ex)
        {
            throw new ExpressionEvaluationException($"获取属性值失败: {propertyPath}", ex);
        }
    }

    /// <summary>
    /// 比较值
    /// </summary>
    /// <param name="value1">值1</param>
    /// <param name="value2">值2</param>
    /// <param name="op">运算符</param>
    /// <returns>比较结果</returns>
    private bool CompareValuesWithOperator(object value1, object value2, string op)
    {
        if (value1 == null && value2 == null) return op == "==";
        if (value1 == null || value2 == null) return op == "!=";

        // 处理 JValue 类型
        if (value1 is JValue jValue1)
        {
            value1 = jValue1.Value;
        }

        // 尝试数值比较
        if (double.TryParse(value1.ToString(), out var num1) &&
            double.TryParse(value2.ToString(), out var num2))
        {
            return op switch
            {
                "==" => num1 == num2,
                "!=" => num1 != num2,
                ">" => num1 > num2,
                "<" => num1 < num2,
                ">=" => num1 >= num2,
                "<=" => num1 <= num2,
                _ => throw new ExpressionEvaluationException($"不支持的运算符: {op}")
            };
        }

        // 字符串比较
        var str1 = value1.ToString();
        var str2 = value2.ToString();
        return op switch
        {
            "==" => str1.Equals(str2, StringComparison.OrdinalIgnoreCase),
            "!=" => !str1.Equals(str2, StringComparison.OrdinalIgnoreCase),
            _ => throw new ExpressionEvaluationException($"字符串比较不支持运算符: {op}")
        };
    }

    /// <summary>
    /// 应用逻辑运算符
    /// </summary>
    /// <param name="current">当前结果</param>
    /// <param name="next">下一个结果</param>
    /// <param name="op">运算符</param>
    /// <returns>逻辑运算结果</returns>
    /// <exception cref="ExpressionEvaluationException">当运算符不支持时抛出异常</exception>
    private bool ApplyOperator(bool current, bool next, string op)
    {
        return op switch
        {
            "&&" => current && next,
            "||" => current || next,
            _ => throw new ExpressionEvaluationException($"不支持的运算符: {op}")
        };
    }
}

/// <summary>
///     表达式计算异常
/// </summary>
public class ExpressionEvaluationException : Exception
{
    /// <summary>
    /// 构造函数
    /// </summary>
    /// <param name="message">消息</param>
    public ExpressionEvaluationException(string message) : base(message)
    {

    }

    /// <summary>
    /// 构造函数
    /// </summary>
    /// <param name="message">消息</param>
    /// <param name="innerException">内部异常</param>
    public ExpressionEvaluationException(string message, Exception innerException)
        : base(message, innerException)
    {
    }
}

/// <summary>
///     表达式令牌类型
/// </summary>
internal enum TokenType
{
    /// <summary>   
    /// 字段
    /// </summary>
    Field,
    /// <summary>
    /// 值
    /// </summary>
    Value,
    /// <summary>
    /// 运算符
    /// </summary>
    Operator
}

/// <summary>
///     表达式令牌
/// </summary>
internal class ExpressionToken
{
    /// <summary>
    /// 令牌类型
    /// </summary>
    public TokenType Type { get; set; }

    /// <summary>
    /// 令牌值
    /// </summary>
    public string Value { get; set; }
}