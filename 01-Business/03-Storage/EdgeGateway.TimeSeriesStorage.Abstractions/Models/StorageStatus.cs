namespace EdgeGateway.TimeSeriesStorage.Abstractions.Models;

/// <summary>
/// 存储状态信息
/// </summary>
public class StorageStatus
{
  /// <summary>
  /// 总存储空间(字节)
  /// </summary>
  public long TotalSpace { get; set; }

  /// <summary>
  /// 已用存储空间(字节)
  /// </summary>
  public long UsedSpace { get; set; }

  /// <summary>
  /// 可用存储空间(字节)
  /// </summary>
  public long FreeSpace { get; set; }

  /// <summary>
  /// 设备总数
  /// </summary>
  public int DeviceCount { get; set; }

  /// <summary>
  /// 数据点总数
  /// </summary>
  public long DataPointCount { get; set; }

  /// <summary>
  /// 最早数据时间
  /// </summary>
  public DateTime? EarliestDataTime { get; set; }

  /// <summary>
  /// 最新数据时间
  /// </summary>
  public DateTime? LatestDataTime { get; set; }
}