namespace EdgeGateway.TimeSeriesStorage.Abstractions.Models.Requests;

/// <summary>
/// 时序数据删除请求
/// </summary>
public class TimeSeriesDeleteRequest
{
  /// <summary>
  /// 设备ID
  /// </summary>
  public required string DeviceId { get; set; }

  /// <summary>
  /// 开始时间
  /// </summary>
  public DateTime? StartTime { get; set; } = DateTime.UtcNow.AddMinutes(-5);

  /// <summary>
  /// 结束时间
  /// </summary>
  public DateTime? EndTime { get; set; } = DateTime.UtcNow;

  /// <summary>
  /// 是否删除所有数据
  /// </summary>
  public bool DeleteAll { get; set; }
}