namespace EdgeGateway.TimeSeriesStorage.Abstractions.Models.Requests;

/// <summary>
/// 时序数据统计请求
/// </summary>
public class TimeSeriesStatisticsRequest
{
  /// <summary>
  /// 设备ID
  /// </summary>
  public required string DeviceId { get; set; }

  /// <summary>
  /// 标签名称列表
  /// </summary>
  public IEnumerable<string> TagNames { get; set; } = new List<string>();

  /// <summary>
  /// 开始时间
  /// </summary>
  public DateTime StartTime { get; set; } = DateTime.UtcNow.AddMinutes(-5);

  /// <summary>
  /// 结束时间
  /// </summary>
  public DateTime EndTime { get; set; } = DateTime.UtcNow;
}