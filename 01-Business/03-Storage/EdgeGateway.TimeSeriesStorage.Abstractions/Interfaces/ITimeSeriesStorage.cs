using EdgeGateway.TimeSeriesStorage.Abstractions.Models;

namespace EdgeGateway.TimeSeriesStorage.Abstractions.Interfaces;

/// <summary>
/// 时序数据存储接口
/// </summary>
public interface ITimeSeriesStorage
{
    /// <summary>
    /// 存储类型
    /// </summary>
    string StorageType { get; }

    #region 写入操作

    /// <summary>
    /// 写入单条时序数据
    /// </summary>
    /// <param name="data">时序数据</param>
    /// <param name="cancellationToken">取消令牌</param>
    Task WriteAsync(TimeSeriesData data, CancellationToken cancellationToken = default);

    /// <summary>
    /// 批量写入时序数据
    /// </summary>
    /// <param name="dataList">时序数据列表</param>
    /// <param name="cancellationToken">取消令牌</param>
    Task WriteBatchAsync(IEnumerable<TimeSeriesData> dataList, CancellationToken cancellationToken = default);

    #endregion

    #region 查询操作

    /// <summary>
    /// 查询时序数据
    /// </summary>
    /// <param name="deviceId">设备ID</param>
    /// <param name="tagNames">标签名称列表，为空时查询所有标签</param>
    /// <param name="startTime">开始时间</param>
    /// <param name="endTime">结束时间</param>
    /// <param name="cancellationToken">取消令牌</param>
    Task<IEnumerable<TimeSeriesData>> QueryAsync(
        string deviceId,
        IEnumerable<string> tagNames,
        DateTime startTime,
        DateTime endTime,
        CancellationToken cancellationToken = default);

    /// <summary>
    /// 分页查询时序数据
    /// </summary>
    /// <param name="deviceId">设备ID</param>
    /// <param name="tagNames">标签名称列表，为空时查询所有标签</param>
    /// <param name="startTime">开始时间</param>
    /// <param name="endTime">结束时间</param>
    /// <param name="pageSize">每页大小</param>
    /// <param name="pageNumber">页码</param>
    /// <param name="cancellationToken">取消令牌</param>
    Task<PagedResult<TimeSeriesData>> QueryPagedAsync(
        string deviceId,
        IEnumerable<string> tagNames,
        DateTime startTime,
        DateTime endTime,
        int pageSize,
        int pageNumber,
        bool descending = false,
        CancellationToken cancellationToken = default);

    /// <summary>
    /// 获取设备的最新数据
    /// </summary>
    /// <param name="deviceId">设备ID</param>
    /// <param name="tagNames">标签名称列表，为空时查询所有标签</param>
    /// <param name="cancellationToken">取消令牌</param>
    Task<TimeSeriesData> GetLatestAsync(
        string deviceId,
        IEnumerable<string> tagNames = null,
        CancellationToken cancellationToken = default);

    /// <summary>
    /// 获取多个设备的最新数据
    /// </summary>
    /// <param name="deviceIds">设备ID列表</param>
    /// <param name="tagNames">标签名称列表，为空时查询所有标签</param>
    /// <param name="cancellationToken">取消令牌</param>
    Task<IDictionary<string, TimeSeriesData>> GetLatestBatchAsync(
        IEnumerable<string> deviceIds,
        IEnumerable<string> tagNames = null,
        CancellationToken cancellationToken = default);

    #endregion

    #region 删除操作

    /// <summary>
    /// 删除指定时间范围内的数据
    /// </summary>
    /// <param name="deviceId">设备ID</param>
    /// <param name="startTime">开始时间</param>
    /// <param name="endTime">结束时间</param>
    /// <param name="cancellationToken">取消令牌</param>
    Task<bool> DeleteAsync(
        string deviceId,
        DateTime startTime,
        DateTime endTime,
        CancellationToken cancellationToken = default);

    /// <summary>
    /// 删除设备的所有数据
    /// </summary>
    /// <param name="deviceId">设备ID</param>
    /// <param name="cancellationToken">取消令牌</param>
    Task<bool> DeleteDeviceAsync(string deviceId, CancellationToken cancellationToken = default);

    /// <summary>
    /// 批量删除设备数据
    /// </summary>
    /// <param name="deviceIds">设备ID列表</param>
    /// <param name="cancellationToken">取消令牌</param>
    Task<bool> DeleteDeviceBatchAsync(
        IEnumerable<string> deviceIds,
        CancellationToken cancellationToken = default);

    #endregion

    #region 统计操作

    /// <summary>
    /// 获取数据统计信息
    /// </summary>
    /// <param name="deviceId">设备ID</param>
    /// <param name="tagNames">标签名称列表</param>
    /// <param name="startTime">开始时间</param>
    /// <param name="endTime">结束时间</param>
    /// <param name="cancellationToken">取消令牌</param>
    Task<TimeSeriesStatistics> GetStatisticsAsync(
        string deviceId,
        IEnumerable<string> tagNames,
        DateTime startTime,
        DateTime endTime,
        CancellationToken cancellationToken = default);

    /// <summary>
    /// 获取存储状态信息
    /// </summary>
    /// <param name="cancellationToken">取消令牌</param>
    Task<StorageStatus> GetStorageStatusAsync(CancellationToken cancellationToken = default);

    #endregion

    /// <summary>
    /// 获取性能报告
    /// </summary>
    /// <returns>性能报告</returns>
    Task<PerformanceReport> GetPerformanceReportAsync();

    /// <summary>
    /// 获取查询建议
    /// </summary>
    /// <param name="deviceId">设备ID</param>
    /// <param name="tagNames">标签ID</param>
    /// <returns>查询建议</returns>
    Task<QuerySuggestion> GetQuerySuggestionAsync(string deviceId, IEnumerable<string> tagNames = null);
}