using Furion.ConfigurableOptions;

namespace EdgeGateway.TimeSeriesStorage.Abstractions.Configurations;

/// <summary>
/// SQLite时序数据存储配置选项
/// </summary>
public class SQLiteStorageOptions : IConfigurableOptions
{
  /// <summary>
  /// 数据库文件路径
  /// 默认值: "Data/timeseries.db"
  /// </summary>
  /// <remarks>
  /// 支持相对路径和绝对路径，建议使用相对路径便于部署
  /// </remarks>
  public string DatabasePath { get; set; } = "Data/timeseries.db";

  /// <summary>
  /// 数据保留天数
  /// 默认值: 7天
  /// </summary>
  /// <remarks>
  /// 超过保留天数的数据将被自动清理，以控制存储空间使用
  /// </remarks>
  public int RetentionDays { get; set; } = 7;

  /// <summary>
  /// 写入缓冲区大小
  /// 默认值: 200条记录
  /// </summary>
  /// <remarks>
  /// 控制内存中缓存的最大记录数，需要根据设备内存大小调整
  /// </remarks>
  public int BufferSize { get; set; } = 200;

  /// <summary>
  /// 批量写入大小
  /// 默认值: 50条记录
  /// </summary>
  /// <remarks>
  /// 每次写入数据库的批次大小，过大会占用更多内存，过小会影响写入性能
  /// </remarks>
  public int BatchSize { get; set; } = 50;

  /// <summary>
  /// 刷新间隔(秒)
  /// 默认值: 2秒
  /// </summary>
  /// <remarks>
  /// 缓冲区自动刷新到数据库的时间间隔，间隔越短实时性越好但写入次数会增加
  /// </remarks>
  public int FlushInterval { get; set; } = 2;

  /// <summary>
  /// 最小可用空间(GB)
  /// 默认值: 3GB
  /// </summary>
  /// <remarks>
  /// 当可用空间小于此值时，将停止写入新数据
  /// </remarks>
  public double MinimumFreeSpaceGB { get; set; } = 3.0;
}