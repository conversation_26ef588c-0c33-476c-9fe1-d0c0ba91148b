namespace EdgeGateway.Device.Entity;

/// <summary>
/// 数据上报方式
/// </summary>
public enum DataReportTypeEnum
{
    /// <summary>
    /// 始终上报 - 设备所有数据正常采集并上报
    /// </summary>
    Always = 0,
    
    /// <summary>
    /// 数据变化时上报 - 设备中任意点位发生变化时才上报该设备所有数据
    /// </summary>
    OnValueChanged = 1,
    
    /// <summary>
    /// 从不上报 - 设备正常采集但不上报数据（仅本地存储）
    /// </summary>
    Never = 2,
    
    /// <summary>
    /// 按需上报
    /// </summary>
    OnDemand = 3,
    
    /// <summary>
    /// 按需上报（设备关机依然上报）
    /// </summary>
    OnDemandWithPowerOff = 4
} 