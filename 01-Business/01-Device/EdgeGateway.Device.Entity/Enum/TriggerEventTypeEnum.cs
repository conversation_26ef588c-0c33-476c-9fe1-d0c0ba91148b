namespace EdgeGateway.Device.Entity
{
  /// <summary>
  /// 触发事件类型枚举
  /// </summary>
  public enum TriggerEventTypeEnum
  {
    /// <summary>
    /// 属性触发 - 根据设备属性值的变化触发事件
    /// </summary>
    属性触发 = 0,

    /// <summary>
    /// 状态触发 - 根据设备连接状态的变化触发事件
    /// </summary>
    状态触发 = 1,

    /// <summary>
    /// 混合触发 - 同时满足多种条件时触发事件
    /// </summary>
    混合触发 = 2,

    /// <summary>
    /// 定时触发 - 根据设定的时间规则定期触发事件
    /// </summary>
    定时触发 = 3,

    /// <summary>
    /// 手动触发 - 由用户或外部系统手动触发的事件
    /// </summary>
    手动触发 = 4,

    /// <summary>
    /// 条件定时触发 - 满足条件后按照时间规则触发
    /// </summary>
    条件定时触发 = 5,

    /// <summary>
    /// 数据变更触发 - 根据数据变更触发事件
    /// </summary>
    DataChange = 6
  }
}