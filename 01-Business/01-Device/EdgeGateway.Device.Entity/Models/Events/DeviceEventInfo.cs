using System;

namespace EdgeGateway.Device.Entity.Models.Events
{
  /// <summary>
  /// 设备事件信息，包含基本事件数据和部分健康状态
  /// </summary>
  public class DeviceEventInfo
  {
    /// <summary>
    /// 事件ID
    /// </summary>
    public long Id { get; set; }

    /// <summary>
    /// 设备ID
    /// </summary>
    public long DeviceId { get; set; }

    /// <summary>
    /// 事件名称
    /// </summary>
    public string Name { get; set; }

    /// <summary>
    /// 是否启用
    /// </summary>
    public bool Enabled { get; set; }

    /// <summary>
    /// 最后触发时间
    /// </summary>
    public DateTime? LastTriggerTime { get; set; }

    /// <summary>
    /// 触发次数
    /// </summary>
    public int TriggerCount { get; set; }

    /// <summary>
    /// 创建时间
    /// </summary>
    public DateTime CreatedTime { get; set; }

    /// <summary>
    /// 更新时间
    /// </summary>
    public DateTime UpdatedTime { get; set; }

    /// <summary>
    /// 健康状态
    /// </summary>
    public EventHealthStatusEnum HealthStatus { get; set; }

    /// <summary>
    /// 内存状态
    /// </summary>
    public EventMemoryStatusEnum MemoryStatus { get; set; }
  }
}