namespace EdgeGateway.Device.Entity.Models.Events;

/// <summary>
///     事件详细信息，用于缓存完整的事件数据
/// </summary>
public class EventDetailInfo
{
  /// <summary>
  ///     基本事件信息
  /// </summary>
  public DeviceEvent Event { get; set; }

  /// <summary>
  ///     事件健康状态
  /// </summary>
  public EventHealthStatus HealthStatus { get; set; }

  /// <summary>
  ///     事件中使用的所有触发条件
  /// </summary>
  public List<EventTrigger> Triggers { get; set; }

  /// <summary>
  ///     事件中使用的所有动作
  /// </summary>
  public List<EventAction> Actions { get; set; }

  /// <summary>
  ///     事件关联的调度任务
  /// </summary>
  public List<EventScheduleTask> ScheduleTasks { get; set; }

  /// <summary>
  ///     构造函数
  /// </summary>
  public EventDetailInfo()
    {
        Triggers = new List<EventTrigger>();
        Actions = new List<EventAction>();
        ScheduleTasks = new List<EventScheduleTask>();
    }
}