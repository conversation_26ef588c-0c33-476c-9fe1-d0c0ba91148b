using System;
using System.Collections.Generic;

namespace EdgeGateway.Device.Entity.Models.Events
{
  /// <summary>
  /// 设备事件数据，用于传递设备数据并触发事件判断
  /// </summary>
  public class DeviceEventData
  {
    /// <summary>
    /// 设备ID
    /// </summary>
    public long DeviceId { get; set; }

    /// <summary>
    /// 设备标识符
    /// </summary>
    public string DeviceIdentifier { get; set; }

    /// <summary>
    /// 事件发生时间戳
    /// </summary>
    public long Timestamp { get; set; }

    /// <summary>
    /// 标签数据集合，键为标签标识符，值为标签值
    /// </summary>
    public Dictionary<string, TagData> TagValues { get; set; } = new Dictionary<string, TagData>();

    /// <summary>
    /// 设备状态
    /// </summary>
    public bool DeviceStatus { get; set; }

    /// <summary>
    /// 创建一个新的设备事件数据实例
    /// </summary>
    /// <param name="deviceId">设备ID</param>
    /// <param name="deviceIdentifier">设备标识符</param>
    /// <param name="timestamp">时间戳</param>
    /// <param name="deviceStatus">设备状态</param>
    public DeviceEventData(long deviceId, string deviceIdentifier, long timestamp, bool deviceStatus)
    {
      DeviceId = deviceId;
      DeviceIdentifier = deviceIdentifier;
      Timestamp = timestamp;
      DeviceStatus = deviceStatus;
    }

    /// <summary>
    /// 添加标签数据
    /// </summary>
    /// <param name="tagIdentifier">标签标识符</param>
    /// <param name="tagName">标签名称</param>
    /// <param name="value">标签值</param>
    /// <param name="dataType">数据类型</param>
    public void AddTagValue(string tagIdentifier, string tagName, object value, string dataType)
    {
      TagValues[tagIdentifier] = new TagData
      {
        Identifier = tagIdentifier,
        Name = tagName,
        Value = value,
        DataType = dataType,
        Timestamp = Timestamp
      };
    }
  }

  /// <summary>
  /// 标签数据
  /// </summary>
  public class TagData
  {
    /// <summary>
    /// 标签标识符
    /// </summary>
    public string Identifier { get; set; }

    /// <summary>
    /// 标签名称
    /// </summary>
    public string Name { get; set; }

    /// <summary>
    /// 标签值
    /// </summary>
    public object Value { get; set; }

    /// <summary>
    /// 数据类型
    /// </summary>
    public string DataType { get; set; }

    /// <summary>
    /// 时间戳
    /// </summary>
    public long Timestamp { get; set; }
  }
}