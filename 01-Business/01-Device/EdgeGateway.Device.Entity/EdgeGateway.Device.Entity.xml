<?xml version="1.0"?>
<doc>
    <assembly>
        <name>EdgeGateway.Device.Entity</name>
    </assembly>
    <members>
        <member name="T:EdgeGateway.Device.Entity.Const.MessageTopics">
            <summary>
            消息主题常量
            </summary>
        </member>
        <member name="F:EdgeGateway.Device.Entity.Const.MessageTopics.DeviceStatusPrefix">
            <summary>
            设备状态主题前缀
            </summary>
        </member>
        <member name="M:EdgeGateway.Device.Entity.Const.MessageTopics.GetDeviceStatusTopic(System.Int64)">
            <summary>
            获取设备状态主题
            </summary>
            <param name="deviceId">设备ID</param>
            <returns></returns>
        </member>
        <member name="M:EdgeGateway.Device.Entity.Const.MessageTopics.GetDeviceMetricsTopic(System.Int64)">
            <summary>
            获取设备指标主题
            </summary>
            <param name="deviceId">设备ID</param>
            <returns></returns>
        </member>
        <member name="M:EdgeGateway.Device.Entity.Const.MessageTopics.GetDeviceDebugTopic(System.Int64)">
            <summary>
            获取设备调试主题
            </summary>
            <param name="deviceId">设备ID</param>
            <returns></returns>
        </member>
        <member name="M:EdgeGateway.Device.Entity.Const.MessageTopics.GetDeviceDataTopic(System.Int64)">
            <summary>
            获取设备数据主题
            </summary>
            <param name="deviceId">设备ID</param>
            <returns></returns>
        </member>
        <member name="M:EdgeGateway.Device.Entity.Const.MessageTopics.GetDevicePointCollectionTopic(System.Int64)">
            <summary>
            获取设备点位采集主题
            </summary>
            <param name="deviceId">设备ID</param>
            <returns></returns>
        </member>
        <member name="F:EdgeGateway.Device.Entity.Const.MessageTopics.DeviceOnlineData">
            <summary>
            设备数据在线主题
            </summary>
        </member>
        <member name="P:EdgeGateway.Device.Entity.Dto.BatchEnableInput.Ids">
            <summary>
                设备ID列表
            </summary>
        </member>
        <member name="P:EdgeGateway.Device.Entity.Dto.BatchEnableInput.Enable">
            <summary>
                是否启用
            </summary>
        </member>
        <member name="T:EdgeGateway.Device.Entity.Dto.BatchRestartInput">
            <summary>
                批量重启设备输入模型
            </summary>
        </member>
        <member name="P:EdgeGateway.Device.Entity.Dto.BatchRestartInput.Ids">
            <summary>
                设备ID列表
            </summary>
        </member>
        <member name="T:EdgeGateway.Device.Entity.Dto.Device.CollectingDeviceDto">
            <summary>
            正在采集的设备信息DTO
            </summary>
        </member>
        <member name="P:EdgeGateway.Device.Entity.Dto.Device.CollectingDeviceDto.Id">
            <summary>
            设备ID
            </summary>
        </member>
        <member name="P:EdgeGateway.Device.Entity.Dto.Device.CollectingDeviceDto.Identifier">
            <summary>
            设备标识符
            </summary>
        </member>
        <member name="P:EdgeGateway.Device.Entity.Dto.Device.CollectingDeviceDto.Name">
            <summary>
            设备名称
            </summary>
        </member>
        <member name="P:EdgeGateway.Device.Entity.Dto.Device.CollectingDeviceDto.Description">
            <summary>
            设备描述
            </summary>
        </member>
        <member name="P:EdgeGateway.Device.Entity.Dto.Device.CollectingDeviceDto.ChannelName">
            <summary>
            通道名称
            </summary>
        </member>
        <member name="P:EdgeGateway.Device.Entity.Dto.Device.CollectingDeviceDto.DriverName">
            <summary>
            驱动名称
            </summary>
        </member>
        <member name="P:EdgeGateway.Device.Entity.Dto.Device.CollectingDeviceDto.IsConnect">
            <summary>
            是否连接
            </summary>
        </member>
        <member name="P:EdgeGateway.Device.Entity.Dto.Device.CollectingDeviceDto.Status">
            <summary>
            设备状态
            </summary>
        </member>
        <member name="P:EdgeGateway.Device.Entity.Dto.Device.CollectingDeviceDto.RunStatus">
            <summary>
            运行状态：active-活跃, paused-暂停, stopped-停止, error-错误
            </summary>
        </member>
        <member name="P:EdgeGateway.Device.Entity.Dto.Device.CollectingDeviceDto.LastActiveTime">
            <summary>
            最后活动时间
            </summary>
        </member>
        <member name="P:EdgeGateway.Device.Entity.Dto.Device.CollectingDeviceDto.CollectionPeriod">
            <summary>
            采集周期(毫秒)
            </summary>
        </member>
        <member name="P:EdgeGateway.Device.Entity.Dto.Device.CollectingDeviceDto.TotalDataPoints">
            <summary>
            数据点总数
            </summary>
        </member>
        <member name="P:EdgeGateway.Device.Entity.Dto.Device.CollectingDeviceDto.EnabledDataPoints">
            <summary>
            启用的数据点数量
            </summary>
        </member>
        <member name="P:EdgeGateway.Device.Entity.Dto.Device.CollectingDeviceDto.ReportType">
            <summary>
            数据上报方式
            </summary>
        </member>
        <member name="P:EdgeGateway.Device.Entity.Dto.Device.CollectingDeviceDto.GroupTags">
            <summary>
            分组标签
            </summary>
        </member>
        <member name="P:EdgeGateway.Device.Entity.Dto.Device.CollectingDeviceDto.Location">
            <summary>
            设备位置
            </summary>
        </member>
        <member name="P:EdgeGateway.Device.Entity.Dto.Device.CollectingDeviceDto.CollectionStartTime">
            <summary>
            采集开始时间
            </summary>
        </member>
        <member name="P:EdgeGateway.Device.Entity.Dto.Device.CollectingDeviceDto.TodayOnlineMinutes">
            <summary>
            今日在线时长(分钟)
            </summary>
        </member>
        <member name="T:EdgeGateway.Device.Entity.Dto.Device.DeviceDiagnosticDto">
            <summary>
            设备诊断响应
            </summary>
        </member>
        <member name="P:EdgeGateway.Device.Entity.Dto.Device.DeviceDiagnosticDto.DeviceId">
            <summary>
            设备ID
            </summary>
        </member>
        <member name="P:EdgeGateway.Device.Entity.Dto.Device.DeviceDiagnosticDto.DeviceIdentifier">
            <summary>
            设备标识符
            </summary>
        </member>
        <member name="P:EdgeGateway.Device.Entity.Dto.Device.DeviceDiagnosticDto.DeviceName">
            <summary>
            设备名称
            </summary>
        </member>
        <member name="P:EdgeGateway.Device.Entity.Dto.Device.DeviceDiagnosticDto.DiagnosticTime">
            <summary>
            诊断时间
            </summary>
        </member>
        <member name="P:EdgeGateway.Device.Entity.Dto.Device.DeviceDiagnosticDto.OverallHealthScore">
            <summary>
            综合健康评分（0-100）
            </summary>
        </member>
        <member name="P:EdgeGateway.Device.Entity.Dto.Device.DeviceDiagnosticDto.OverallStatus">
            <summary>
            综合状态
            </summary>
        </member>
        <member name="P:EdgeGateway.Device.Entity.Dto.Device.DeviceDiagnosticDto.Connection">
            <summary>
            连接诊断信息
            </summary>
        </member>
        <member name="P:EdgeGateway.Device.Entity.Dto.Device.DeviceDiagnosticDto.Performance">
            <summary>
            性能诊断信息
            </summary>
        </member>
        <member name="P:EdgeGateway.Device.Entity.Dto.Device.DeviceDiagnosticDto.Health">
            <summary>
            健康诊断信息
            </summary>
        </member>
        <member name="P:EdgeGateway.Device.Entity.Dto.Device.DeviceDiagnosticDto.Configuration">
            <summary>
            配置诊断信息
            </summary>
        </member>
        <member name="P:EdgeGateway.Device.Entity.Dto.Device.DeviceDiagnosticDto.Data">
            <summary>
            数据诊断信息
            </summary>
        </member>
        <member name="P:EdgeGateway.Device.Entity.Dto.Device.DeviceDiagnosticDto.Recommendations">
            <summary>
            诊断建议列表
            </summary>
        </member>
        <member name="P:EdgeGateway.Device.Entity.Dto.Device.DeviceDiagnosticDto.History">
            <summary>
            历史诊断数据
            </summary>
        </member>
        <member name="T:EdgeGateway.Device.Entity.Dto.Device.ConnectionDiagnostic">
            <summary>
            连接诊断信息
            </summary>
        </member>
        <member name="P:EdgeGateway.Device.Entity.Dto.Device.ConnectionDiagnostic.IsConnected">
            <summary>
            连接状态
            </summary>
        </member>
        <member name="P:EdgeGateway.Device.Entity.Dto.Device.ConnectionDiagnostic.ConnectionQuality">
            <summary>
            连接质量评分（0-100）
            </summary>
        </member>
        <member name="P:EdgeGateway.Device.Entity.Dto.Device.ConnectionDiagnostic.NetworkLatency">
            <summary>
            网络延迟（毫秒）
            </summary>
        </member>
        <member name="P:EdgeGateway.Device.Entity.Dto.Device.ConnectionDiagnostic.ProtocolStatus">
            <summary>
            协议状态
            </summary>
        </member>
        <member name="P:EdgeGateway.Device.Entity.Dto.Device.ConnectionDiagnostic.LastConnectedTime">
            <summary>
            最后连接时间
            </summary>
        </member>
        <member name="P:EdgeGateway.Device.Entity.Dto.Device.ConnectionDiagnostic.LastActiveTime">
            <summary>
            最后活动时间
            </summary>
        </member>
        <member name="P:EdgeGateway.Device.Entity.Dto.Device.ConnectionDiagnostic.ConnectionFailureCount">
            <summary>
            连接失败次数
            </summary>
        </member>
        <member name="P:EdgeGateway.Device.Entity.Dto.Device.ConnectionDiagnostic.ConnectionStability">
            <summary>
            连接稳定性（过去24小时连接成功率）
            </summary>
        </member>
        <member name="T:EdgeGateway.Device.Entity.Dto.Device.PerformanceDiagnostic">
            <summary>
            性能诊断信息
            </summary>
        </member>
        <member name="P:EdgeGateway.Device.Entity.Dto.Device.PerformanceDiagnostic.AvgResponseTime">
            <summary>
            平均响应时间（毫秒）
            </summary>
        </member>
        <member name="P:EdgeGateway.Device.Entity.Dto.Device.PerformanceDiagnostic.MaxResponseTime">
            <summary>
            最大响应时间（毫秒）
            </summary>
        </member>
        <member name="P:EdgeGateway.Device.Entity.Dto.Device.PerformanceDiagnostic.CpuUsage">
            <summary>
            CPU使用率（%）
            </summary>
        </member>
        <member name="P:EdgeGateway.Device.Entity.Dto.Device.PerformanceDiagnostic.MemoryUsage">
            <summary>
            内存使用量（MB）
            </summary>
        </member>
        <member name="P:EdgeGateway.Device.Entity.Dto.Device.PerformanceDiagnostic.DataThroughput">
            <summary>
            数据吞吐量（每秒处理数据点数）
            </summary>
        </member>
        <member name="P:EdgeGateway.Device.Entity.Dto.Device.PerformanceDiagnostic.QueueDelay">
            <summary>
            队列延迟（毫秒）
            </summary>
        </member>
        <member name="P:EdgeGateway.Device.Entity.Dto.Device.PerformanceDiagnostic.Trend">
            <summary>
            性能趋势（过去24小时）
            </summary>
        </member>
        <member name="T:EdgeGateway.Device.Entity.Dto.Device.HealthDiagnostic">
            <summary>
            健康诊断信息
            </summary>
        </member>
        <member name="P:EdgeGateway.Device.Entity.Dto.Device.HealthDiagnostic.HealthStatus">
            <summary>
            健康状态
            </summary>
        </member>
        <member name="P:EdgeGateway.Device.Entity.Dto.Device.HealthDiagnostic.HealthScore">
            <summary>
            健康评分（0-100）
            </summary>
        </member>
        <member name="P:EdgeGateway.Device.Entity.Dto.Device.HealthDiagnostic.TriggerSuccessRate">
            <summary>
            触发成功率（%）
            </summary>
        </member>
        <member name="P:EdgeGateway.Device.Entity.Dto.Device.HealthDiagnostic.TotalTriggerCount">
            <summary>
            总触发次数
            </summary>
        </member>
        <member name="P:EdgeGateway.Device.Entity.Dto.Device.HealthDiagnostic.FailureCount">
            <summary>
            失败次数
            </summary>
        </member>
        <member name="P:EdgeGateway.Device.Entity.Dto.Device.HealthDiagnostic.LastErrorMessage">
            <summary>
            最后错误信息
            </summary>
        </member>
        <member name="P:EdgeGateway.Device.Entity.Dto.Device.HealthDiagnostic.LastErrorTime">
            <summary>
            最后错误时间
            </summary>
        </member>
        <member name="P:EdgeGateway.Device.Entity.Dto.Device.HealthDiagnostic.AvailabilityScore">
            <summary>
            可用性评分（过去7天）
            </summary>
        </member>
        <member name="T:EdgeGateway.Device.Entity.Dto.Device.ConfigurationDiagnostic">
            <summary>
            配置诊断信息
            </summary>
        </member>
        <member name="P:EdgeGateway.Device.Entity.Dto.Device.ConfigurationDiagnostic.IsConfigurationValid">
            <summary>
            配置有效性
            </summary>
        </member>
        <member name="P:EdgeGateway.Device.Entity.Dto.Device.ConfigurationDiagnostic.ConfigurationCompleteness">
            <summary>
            配置完整性评分（0-100）
            </summary>
        </member>
        <member name="P:EdgeGateway.Device.Entity.Dto.Device.ConfigurationDiagnostic.RequiredConfigCount">
            <summary>
            必填配置项数量
            </summary>
        </member>
        <member name="P:EdgeGateway.Device.Entity.Dto.Device.ConfigurationDiagnostic.ConfiguredCount">
            <summary>
            已配置项数量
            </summary>
        </member>
        <member name="P:EdgeGateway.Device.Entity.Dto.Device.ConfigurationDiagnostic.MissingConfigurations">
            <summary>
            缺失的配置项
            </summary>
        </member>
        <member name="P:EdgeGateway.Device.Entity.Dto.Device.ConfigurationDiagnostic.InvalidConfigurations">
            <summary>
            无效的配置项
            </summary>
        </member>
        <member name="P:EdgeGateway.Device.Entity.Dto.Device.ConfigurationDiagnostic.DriverVersion">
            <summary>
            驱动版本信息
            </summary>
        </member>
        <member name="P:EdgeGateway.Device.Entity.Dto.Device.ConfigurationDiagnostic.IsLatestVersion">
            <summary>
            是否为最新版本
            </summary>
        </member>
        <member name="T:EdgeGateway.Device.Entity.Dto.Device.DataDiagnostic">
            <summary>
            数据诊断信息
            </summary>
        </member>
        <member name="P:EdgeGateway.Device.Entity.Dto.Device.DataDiagnostic.IsDataCollecting">
            <summary>
            数据采集状态
            </summary>
        </member>
        <member name="P:EdgeGateway.Device.Entity.Dto.Device.DataDiagnostic.DataQuality">
            <summary>
            数据质量评分（0-100）
            </summary>
        </member>
        <member name="P:EdgeGateway.Device.Entity.Dto.Device.DataDiagnostic.LastDataTime">
            <summary>
            最后数据采集时间
            </summary>
        </member>
        <member name="P:EdgeGateway.Device.Entity.Dto.Device.DataDiagnostic.TotalDataPoints">
            <summary>
            数据点总数
            </summary>
        </member>
        <member name="P:EdgeGateway.Device.Entity.Dto.Device.DataDiagnostic.ActiveDataPoints">
            <summary>
            活跃数据点数量
            </summary>
        </member>
        <member name="P:EdgeGateway.Device.Entity.Dto.Device.DataDiagnostic.CollectionFrequency">
            <summary>
            数据采集频率（次/分钟）
            </summary>
        </member>
        <member name="P:EdgeGateway.Device.Entity.Dto.Device.DataDiagnostic.DataLossRate">
            <summary>
            数据丢失率（%）
            </summary>
        </member>
        <member name="P:EdgeGateway.Device.Entity.Dto.Device.DataDiagnostic.AnomalousDataCount">
            <summary>
            异常数据点数量
            </summary>
        </member>
        <member name="T:EdgeGateway.Device.Entity.Dto.Device.DiagnosticRecommendation">
            <summary>
            诊断建议
            </summary>
        </member>
        <member name="P:EdgeGateway.Device.Entity.Dto.Device.DiagnosticRecommendation.Level">
            <summary>
            建议级别
            </summary>
        </member>
        <member name="P:EdgeGateway.Device.Entity.Dto.Device.DiagnosticRecommendation.Type">
            <summary>
            建议类型
            </summary>
        </member>
        <member name="P:EdgeGateway.Device.Entity.Dto.Device.DiagnosticRecommendation.Title">
            <summary>
            建议标题
            </summary>
        </member>
        <member name="P:EdgeGateway.Device.Entity.Dto.Device.DiagnosticRecommendation.Description">
            <summary>
            建议描述
            </summary>
        </member>
        <member name="P:EdgeGateway.Device.Entity.Dto.Device.DiagnosticRecommendation.Action">
            <summary>
            操作建议
            </summary>
        </member>
        <member name="T:EdgeGateway.Device.Entity.Dto.Device.HistoryDiagnosticData">
            <summary>
            历史诊断数据
            </summary>
        </member>
        <member name="P:EdgeGateway.Device.Entity.Dto.Device.HistoryDiagnosticData.DiagnosticHistory">
            <summary>
            诊断历史记录
            </summary>
        </member>
        <member name="P:EdgeGateway.Device.Entity.Dto.Device.HistoryDiagnosticData.PerformanceHistory">
            <summary>
            性能历史记录
            </summary>
        </member>
        <member name="P:EdgeGateway.Device.Entity.Dto.Device.HistoryDiagnosticData.TotalCount">
            <summary>
            总记录数
            </summary>
        </member>
        <member name="P:EdgeGateway.Device.Entity.Dto.Device.HistoryDiagnosticData.CurrentPage">
            <summary>
            当前页码
            </summary>
        </member>
        <member name="P:EdgeGateway.Device.Entity.Dto.Device.HistoryDiagnosticData.PageSize">
            <summary>
            页大小
            </summary>
        </member>
        <member name="T:EdgeGateway.Device.Entity.Dto.Device.DeviceDiagnosticStatus">
            <summary>
            设备诊断状态
            </summary>
        </member>
        <member name="F:EdgeGateway.Device.Entity.Dto.Device.DeviceDiagnosticStatus.Excellent">
            <summary>
            良好
            </summary>
        </member>
        <member name="F:EdgeGateway.Device.Entity.Dto.Device.DeviceDiagnosticStatus.Good">
            <summary>
            正常
            </summary>
        </member>
        <member name="F:EdgeGateway.Device.Entity.Dto.Device.DeviceDiagnosticStatus.Warning">
            <summary>
            警告
            </summary>
        </member>
        <member name="F:EdgeGateway.Device.Entity.Dto.Device.DeviceDiagnosticStatus.Error">
            <summary>
            错误
            </summary>
        </member>
        <member name="F:EdgeGateway.Device.Entity.Dto.Device.DeviceDiagnosticStatus.Critical">
            <summary>
            严重
            </summary>
        </member>
        <member name="T:EdgeGateway.Device.Entity.Dto.Device.PerformanceTrend">
            <summary>
            性能趋势
            </summary>
        </member>
        <member name="F:EdgeGateway.Device.Entity.Dto.Device.PerformanceTrend.Improving">
            <summary>
            改善中
            </summary>
        </member>
        <member name="F:EdgeGateway.Device.Entity.Dto.Device.PerformanceTrend.Stable">
            <summary>
            稳定
            </summary>
        </member>
        <member name="F:EdgeGateway.Device.Entity.Dto.Device.PerformanceTrend.Declining">
            <summary>
            下降中
            </summary>
        </member>
        <member name="T:EdgeGateway.Device.Entity.Dto.Device.RecommendationLevel">
            <summary>
            建议级别
            </summary>
        </member>
        <member name="F:EdgeGateway.Device.Entity.Dto.Device.RecommendationLevel.Info">
            <summary>
            信息
            </summary>
        </member>
        <member name="F:EdgeGateway.Device.Entity.Dto.Device.RecommendationLevel.Suggestion">
            <summary>
            建议
            </summary>
        </member>
        <member name="F:EdgeGateway.Device.Entity.Dto.Device.RecommendationLevel.Warning">
            <summary>
            警告
            </summary>
        </member>
        <member name="F:EdgeGateway.Device.Entity.Dto.Device.RecommendationLevel.Critical">
            <summary>
            严重
            </summary>
        </member>
        <member name="T:EdgeGateway.Device.Entity.Dto.Device.DeviceDiagnosticInput">
            <summary>
            设备诊断请求输入
            </summary>
        </member>
        <member name="P:EdgeGateway.Device.Entity.Dto.Device.DeviceDiagnosticInput.DeviceId">
            <summary>
            设备ID
            </summary>
        </member>
        <member name="P:EdgeGateway.Device.Entity.Dto.Device.DeviceDiagnosticInput.IncludeConnection">
            <summary>
            是否包含连接诊断
            </summary>
        </member>
        <member name="P:EdgeGateway.Device.Entity.Dto.Device.DeviceDiagnosticInput.IncludePerformance">
            <summary>
            是否包含性能诊断
            </summary>
        </member>
        <member name="P:EdgeGateway.Device.Entity.Dto.Device.DeviceDiagnosticInput.IncludeHealth">
            <summary>
            是否包含健康诊断
            </summary>
        </member>
        <member name="P:EdgeGateway.Device.Entity.Dto.Device.DeviceDiagnosticInput.IncludeConfiguration">
            <summary>
            是否包含配置诊断
            </summary>
        </member>
        <member name="P:EdgeGateway.Device.Entity.Dto.Device.DeviceDiagnosticInput.IncludeData">
            <summary>
            是否包含数据诊断
            </summary>
        </member>
        <member name="P:EdgeGateway.Device.Entity.Dto.Device.DeviceDiagnosticInput.IncludeHistory">
            <summary>
            是否包含历史诊断数据
            </summary>
        </member>
        <member name="P:EdgeGateway.Device.Entity.Dto.Device.DeviceDiagnosticInput.HistoryDays">
            <summary>
            历史数据查询天数（默认7天）
            </summary>
        </member>
        <member name="P:EdgeGateway.Device.Entity.Dto.Device.DeviceDiagnosticInput.HistoryPage">
            <summary>
            历史数据页码
            </summary>
        </member>
        <member name="P:EdgeGateway.Device.Entity.Dto.Device.DeviceDiagnosticInput.HistoryPageSize">
            <summary>
            历史数据页大小
            </summary>
        </member>
        <member name="T:EdgeGateway.Device.Entity.Dto.Label.DeviceLabelBasicOutput">
            <summary>
            设备标签基本信息输出
            </summary>
        </member>
        <member name="P:EdgeGateway.Device.Entity.Dto.Label.DeviceLabelBasicOutput.Identifier">
            <summary>
            标识符
            </summary>
        </member>
        <member name="P:EdgeGateway.Device.Entity.Dto.Label.DeviceLabelBasicOutput.Name">
            <summary>
            名称
            </summary>
        </member>
        <member name="P:EdgeGateway.Device.Entity.Dto.Label.DeviceLabelBasicOutput.DataType">
            <summary>
            数据类型
            </summary>
        </member>
        <member name="T:EdgeGateway.Device.Entity.Dto.Label.VariableDataImportInput">
            <summary>
            标签数据导入参数
            </summary>
        </member>
        <member name="P:EdgeGateway.Device.Entity.Dto.Label.VariableDataImportInput.DeviceId">
            <summary>
            设备ID
            </summary>
        </member>
        <member name="P:EdgeGateway.Device.Entity.Dto.Label.VariableDataImportInput.Labels">
            <summary>
            标签列表
            </summary>
        </member>
        <member name="P:EdgeGateway.Device.Entity.Dto.Label.VariableDataImportInput.ImportMode">
            <summary>
            导入模式
            </summary>
        </member>
        <member name="P:EdgeGateway.Device.Entity.Dto.Label.VariableDataImportInput.IgnoreFields">
            <summary>
            忽略的字段列表
            </summary>
        </member>
        <member name="T:EdgeGateway.Device.Entity.Dto.Template.CreateFromDeviceInput">
            <summary>
            从设备创建模板输入
            </summary>
        </member>
        <member name="P:EdgeGateway.Device.Entity.Dto.Template.CreateFromDeviceInput.DeviceId">
            <summary>
            源设备ID
            </summary>
        </member>
        <member name="P:EdgeGateway.Device.Entity.Dto.Template.CreateFromDeviceInput.Name">
            <summary>
            模板名称
            </summary>
        </member>
        <member name="P:EdgeGateway.Device.Entity.Dto.Template.CreateFromDeviceInput.Description">
            <summary>
            模板描述
            </summary>
        </member>
        <member name="P:EdgeGateway.Device.Entity.Dto.Template.CreateFromDeviceInput.LabelIds">
            <summary>
            选择的点位ID列表
            </summary>
        </member>
        <member name="T:EdgeGateway.Device.Entity.Dto.Template.DeviceTemplateApplyInput">
            <summary>
            应用设备模板到设备输入
            </summary>
        </member>
        <member name="P:EdgeGateway.Device.Entity.Dto.Template.DeviceTemplateApplyInput.TemplateId">
            <summary>
            模板ID
            </summary>
        </member>
        <member name="P:EdgeGateway.Device.Entity.Dto.Template.DeviceTemplateApplyInput.DeviceIds">
            <summary>
            目标设备ID列表
            </summary>
        </member>
        <member name="P:EdgeGateway.Device.Entity.Dto.Template.DeviceTemplateApplyInput.OverwriteConflicts">
            <summary>
            冲突处理策略：true=覆盖现有点位，false=跳过冲突点位
            </summary>
        </member>
        <member name="T:EdgeGateway.Device.Entity.Dto.Template.DeviceTemplateOutput">
            <summary>
            设备模板输出
            </summary>
        </member>
        <member name="P:EdgeGateway.Device.Entity.Dto.Template.DeviceTemplateOutput.Id">
            <summary>
            模板ID
            </summary>
        </member>
        <member name="P:EdgeGateway.Device.Entity.Dto.Template.DeviceTemplateOutput.Name">
            <summary>
            模板名称
            </summary>
        </member>
        <member name="P:EdgeGateway.Device.Entity.Dto.Template.DeviceTemplateOutput.Description">
            <summary>
            模板描述
            </summary>
        </member>
        <member name="P:EdgeGateway.Device.Entity.Dto.Template.DeviceTemplateOutput.DriverName">
            <summary>
            驱动名称
            </summary>
        </member>
        <member name="P:EdgeGateway.Device.Entity.Dto.Template.DeviceTemplateOutput.CreateTime">
            <summary>
            创建时间
            </summary>
        </member>
        <member name="P:EdgeGateway.Device.Entity.Dto.Template.DeviceTemplateOutput.LabelCount">
            <summary>
            点位数量
            </summary>
        </member>
        <member name="P:EdgeGateway.Device.Entity.Dto.Template.DeviceTemplateOutput.Labels">
            <summary>
            模板点位列表（详情时返回）
            </summary>
        </member>
        <member name="T:EdgeGateway.Device.Entity.Dto.Template.DeviceTemplateLabelOutput">
            <summary>
            设备模板点位输出
            </summary>
        </member>
        <member name="P:EdgeGateway.Device.Entity.Dto.Template.DeviceTemplateLabelOutput.Id">
            <summary>
            点位ID
            </summary>
        </member>
        <member name="P:EdgeGateway.Device.Entity.Dto.Template.DeviceTemplateLabelOutput.Identifier">
            <summary>
            标识符
            </summary>
        </member>
        <member name="P:EdgeGateway.Device.Entity.Dto.Template.DeviceTemplateLabelOutput.Name">
            <summary>
            变量名
            </summary>
        </member>
        <member name="P:EdgeGateway.Device.Entity.Dto.Template.DeviceTemplateLabelOutput.TransitionType">
            <summary>
            转换数据类型
            </summary>
        </member>
        <member name="P:EdgeGateway.Device.Entity.Dto.Template.DeviceTemplateLabelOutput.ValueSource">
            <summary>
            数据来源
            </summary>
        </member>
        <member name="P:EdgeGateway.Device.Entity.Dto.Template.DeviceTemplateLabelOutput.ActionOrder">
            <summary>
            执行优先级
            </summary>
        </member>
        <member name="P:EdgeGateway.Device.Entity.Dto.Template.DeviceTemplateLabelOutput.Content">
            <summary>
            默认值/脚本内容/表达式
            </summary>
        </member>
        <member name="P:EdgeGateway.Device.Entity.Dto.Template.DeviceTemplateLabelOutput.SendType">
            <summary>
            上送方式
            </summary>
        </member>
        <member name="P:EdgeGateway.Device.Entity.Dto.Template.DeviceTemplateLabelOutput.Period">
            <summary>
            采集周期
            </summary>
        </member>
        <member name="P:EdgeGateway.Device.Entity.Dto.Template.DeviceTemplateLabelOutput.Enable">
            <summary>
            是否启用
            </summary>
        </member>
        <member name="P:EdgeGateway.Device.Entity.Dto.Template.DeviceTemplateLabelOutput.ProtectType">
            <summary>
            读写方式
            </summary>
        </member>
        <member name="P:EdgeGateway.Device.Entity.Dto.Template.DeviceTemplateLabelOutput.DataType">
            <summary>
            读取数据类型
            </summary>
        </member>
        <member name="P:EdgeGateway.Device.Entity.Dto.Template.DeviceTemplateLabelOutput.RegisterAddress">
            <summary>
            读取地址
            </summary>
        </member>
        <member name="P:EdgeGateway.Device.Entity.Dto.Template.DeviceTemplateLabelOutput.Description">
            <summary>
            描述
            </summary>
        </member>
        <member name="T:EdgeGateway.Device.Entity.DevicePageInput">
            <summary>
            设备列表请求
            </summary>
        </member>
        <member name="P:EdgeGateway.Device.Entity.DevicePageInput.ChannelId">
            <summary>
            通道ID
            </summary>
        </member>
        <member name="T:EdgeGateway.Device.Entity.DeviceAddInput">
            <summary>
                新增设备
            </summary>
        </member>
        <member name="P:EdgeGateway.Device.Entity.DeviceAddInput.Identifier">
            <summary>
                设备Id
            </summary>
        </member>
        <member name="P:EdgeGateway.Device.Entity.DeviceAddInput.Name">
            <summary>
             设备名称
            </summary>
        </member>
        <member name="P:EdgeGateway.Device.Entity.DeviceAddInput.Description">
            <summary>
                描述
            </summary>
        </member>
        <member name="P:EdgeGateway.Device.Entity.DeviceAddInput.DeviceConfig">
            <summary>
            配置
            </summary>
        </member>
        <member name="P:EdgeGateway.Device.Entity.DeviceAddInput.DeviceInfo">
            <summary>
            基本配置
            </summary>
        </member>
        <member name="P:EdgeGateway.Device.Entity.DeviceAddInput.Driver">
            <summary>
                采集协议
            </summary>
        </member>
        <member name="P:EdgeGateway.Device.Entity.DeviceAddInput.ChannelId">
            <summary>
                通道ID
            </summary>
        </member>
        <member name="P:EdgeGateway.Device.Entity.DeviceAddInput.GroupTags">
            <summary>
            分组标签
            </summary>
        </member>
        <member name="P:EdgeGateway.Device.Entity.DeviceAddInput.Location">
            <summary>
            设备位置
            </summary>
        </member>
        <member name="P:EdgeGateway.Device.Entity.DeviceAddInput.LogLevel">
            <summary>
            设备日志等级
            </summary>
        </member>
        <member name="T:EdgeGateway.Device.Entity.DeviceUpdateInput">
            <summary>
            设备修改
            </summary>
        </member>
        <member name="P:EdgeGateway.Device.Entity.DeviceUpdateInput.Id">
            <summary>
            设备ID
            </summary>
        </member>
        <member name="T:EdgeGateway.Device.Entity.ManualReadInput">
            <summary>
            手动读取参数
            </summary>
        </member>
        <member name="P:EdgeGateway.Device.Entity.ManualReadInput.DeviceId">
            <summary>
            设备ID
            </summary>
        </member>
        <member name="P:EdgeGateway.Device.Entity.ManualReadInput.Address">
            <summary>
            地址
            </summary>
        </member>
        <member name="P:EdgeGateway.Device.Entity.ManualReadInput.Length">
            <summary>
            长度
            </summary>
        </member>
        <member name="P:EdgeGateway.Device.Entity.ManualReadInput.DataType">
            <summary>
            数据类型
            </summary>
        </member>
        <member name="P:EdgeGateway.Device.Entity.ManualReadInput.Encoding">
            <summary>
            字符编码
            </summary>
        </member>
        <member name="T:EdgeGateway.Device.Entity.ManualWriteInput">
            <summary>
            手动写入参数
            </summary>
        </member>
        <member name="P:EdgeGateway.Device.Entity.ManualWriteInput.DeviceId">
            <summary>
            设备ID
            </summary>  
        </member>
        <member name="P:EdgeGateway.Device.Entity.ManualWriteInput.Address">
            <summary>
            地址
            </summary>
        </member>
        <member name="P:EdgeGateway.Device.Entity.ManualWriteInput.Value">
            <summary>
            写入值
            </summary>
        </member>
        <member name="P:EdgeGateway.Device.Entity.ManualWriteInput.DataType">
            <summary>
            数据类型
            </summary>
        </member>
        <member name="P:EdgeGateway.Device.Entity.ManualWriteInput.Length">
            <summary>
            长度
            </summary>
        </member>
        <member name="P:EdgeGateway.Device.Entity.ManualWriteInput.Encoding">
            <summary>
            字符编码
            </summary>
        </member>
        <member name="T:EdgeGateway.Device.Entity.CollectingDevicePageInput">
            <summary>
            正在采集设备分页查询参数
            </summary>
        </member>
        <member name="P:EdgeGateway.Device.Entity.CollectingDevicePageInput.ChannelId">
            <summary>
            通道ID
            </summary>
        </member>
        <member name="P:EdgeGateway.Device.Entity.CollectingDevicePageInput.IsConnected">
            <summary>
            连接状态筛选：true-已连接，false-未连接，null-全部
            </summary>
        </member>
        <member name="T:EdgeGateway.Device.Entity.DeviceSelectDto">
            <summary>
            </summary>
        </member>
        <member name="P:EdgeGateway.Device.Entity.DeviceSelectDto.Id">
            <summary>
                主键
            </summary>
        </member>
        <member name="P:EdgeGateway.Device.Entity.DeviceSelectDto.Name">
            <summary>
                设备名称
            </summary>
        </member>
        <member name="P:EdgeGateway.Device.Entity.DeviceSelectDto.Identifier">
            <summary>
                设备Id
            </summary>
        </member>
        <member name="T:EdgeGateway.Device.Entity.Model.EdgeChannelPageInput">
            <summary>
                通讯管道列表请求
            </summary>
        </member>
        <member name="T:EdgeGateway.Device.Entity.Model.EdgeChannelAddInput">
            <summary>
                通讯管道新增
            </summary>
        </member>
        <member name="P:EdgeGateway.Device.Entity.Model.EdgeChannelAddInput.ChannelName">
            <summary>
                通道名称
            </summary>
        </member>
        <member name="P:EdgeGateway.Device.Entity.Model.EdgeChannelAddInput.Enable">
            <summary>
                状态 ：启用；禁用
            </summary>
        </member>
        <member name="P:EdgeGateway.Device.Entity.Model.EdgeChannelAddInput.Serial">
            <summary>
                串口号
            </summary>
        </member>
        <member name="P:EdgeGateway.Device.Entity.Model.EdgeChannelAddInput.BaudRate">
            <summary>
                波特率
            </summary>
        </member>
        <member name="P:EdgeGateway.Device.Entity.Model.EdgeChannelAddInput.DataBits">
            <summary>
                数据位
            </summary>
        </member>
        <member name="P:EdgeGateway.Device.Entity.Model.EdgeChannelAddInput.Stop">
            <summary>
                停止位
            </summary>
        </member>
        <member name="P:EdgeGateway.Device.Entity.Model.EdgeChannelAddInput.Checkout">
            <summary>
                校验位
            </summary>
        </member>
        <member name="P:EdgeGateway.Device.Entity.Model.EdgeChannelAddInput.ThreadCount">
            <summary>
                线程数量
            </summary>
        </member>
        <member name="T:EdgeGateway.Device.Entity.BatchAddResult">
            <summary>
            批量添加标签结果
            </summary>
        </member>
        <member name="P:EdgeGateway.Device.Entity.BatchAddResult.TotalCount">
            <summary>
            总数量
            </summary>
        </member>
        <member name="P:EdgeGateway.Device.Entity.BatchAddResult.SuccessCount">
            <summary>
            成功数量
            </summary>
        </member>
        <member name="P:EdgeGateway.Device.Entity.BatchAddResult.SkippedCount">
            <summary>
            跳过数量（已存在）
            </summary>
        </member>
        <member name="P:EdgeGateway.Device.Entity.BatchAddResult.FailedCount">
            <summary>
            失败数量
            </summary>
        </member>
        <member name="P:EdgeGateway.Device.Entity.BatchAddResult.SuccessIdentifiers">
            <summary>
            成功添加的标签标识符列表
            </summary>
        </member>
        <member name="P:EdgeGateway.Device.Entity.BatchAddResult.SkippedIdentifiers">
            <summary>
            跳过的标签标识符列表
            </summary>
        </member>
        <member name="P:EdgeGateway.Device.Entity.BatchAddResult.ErrorMessage">
            <summary>
            错误信息
            </summary>
        </member>
        <member name="T:EdgeGateway.Device.Entity.Label.BatchWriteInput">
            <summary>
            批量写入参数
            </summary>
        </member>
        <member name="P:EdgeGateway.Device.Entity.Label.BatchWriteInput.Identifier">
            <summary>
            标签标识符（可选）
            </summary>
        </member>
        <member name="P:EdgeGateway.Device.Entity.Label.BatchWriteInput.Address">
            <summary>
            写入地址
            </summary>
        </member>
        <member name="P:EdgeGateway.Device.Entity.Label.BatchWriteInput.Value">
            <summary>
            写入值
            </summary>
        </member>
        <member name="P:EdgeGateway.Device.Entity.Label.BatchWriteInput.DataType">
            <summary>
            数据类型（可选）
            </summary>
        </member>
        <member name="P:EdgeGateway.Device.Entity.Label.BatchWriteInput.Length">
            <summary>
            长度（可选，默认为1）
            </summary>
        </member>
        <member name="P:EdgeGateway.Device.Entity.Label.BatchWriteInput.Encoding">
            <summary>
            编码（可选）
            </summary>
        </member>
        <member name="T:EdgeGateway.Device.Entity.DeviceVariablePageInput">
            <summary>
                采集点列表
            </summary>
        </member>
        <member name="P:EdgeGateway.Device.Entity.DeviceVariablePageInput.DeviceId">
            <summary>
                设备Id
            </summary>
        </member>
        <member name="T:EdgeGateway.Device.Entity.DeviceVariableAddInput">
            <summary>
                采集点新增
            </summary>
        </member>
        <member name="P:EdgeGateway.Device.Entity.DeviceVariableAddInput.DeviceId">
            <summary>
                所属设备
            </summary>
        </member>
        <member name="P:EdgeGateway.Device.Entity.DeviceVariableAddInput.Identifier">
            <summary>
                标识符
            </summary>
        </member>
        <member name="P:EdgeGateway.Device.Entity.DeviceVariableAddInput.Name">
            <summary>
                变量名
            </summary>
        </member>
        <member name="P:EdgeGateway.Device.Entity.DeviceVariableAddInput.TransitionType">
            <summary>
                转换数据类型
            </summary>
        </member>
        <member name="P:EdgeGateway.Device.Entity.DeviceVariableAddInput.ValueSource">
            <summary>
                数据来源
            </summary>
        </member>
        <member name="P:EdgeGateway.Device.Entity.DeviceVariableAddInput.ActionOrder">
            <summary>
                执行优先级:0最优
            </summary>
        </member>
        <member name="P:EdgeGateway.Device.Entity.DeviceVariableAddInput.Custom">
            <summary>
                为属性值添加自定义
            </summary>
        </member>
        <member name="P:EdgeGateway.Device.Entity.DeviceVariableAddInput.Content">
            <summary>
                默认值/脚本内容/表达式
            </summary>
        </member>
        <member name="P:EdgeGateway.Device.Entity.DeviceVariableAddInput.SendType">
            <summary>
                上送方式
            </summary>
        </member>
        <member name="P:EdgeGateway.Device.Entity.DeviceVariableAddInput.Tags">
            <summary>
                标签
            </summary>
        </member>
        <member name="P:EdgeGateway.Device.Entity.DeviceVariableAddInput.Period">
            <summary>
                采集周期
            </summary>
        </member>
        <member name="P:EdgeGateway.Device.Entity.DeviceVariableAddInput.ArchiveTime">
            <summary>
                强制归档时间
            </summary>
        </member>
        <member name="P:EdgeGateway.Device.Entity.DeviceVariableAddInput.UploadInterval">
            <summary>
                定时上报周期（单位：秒）
            </summary>
        </member>
        <member name="P:EdgeGateway.Device.Entity.DeviceVariableAddInput.Length">
            <summary>
                长度(属性值小数点保留位数长度)
            </summary>
        </member>
        <member name="P:EdgeGateway.Device.Entity.DeviceVariableAddInput.Description">
            <summary>
                描述
            </summary>
        </member>
        <member name="P:EdgeGateway.Device.Entity.DeviceVariableAddInput.ProtectType">
            <summary>
                读写方式
            </summary>
        </member>
        <member name="P:EdgeGateway.Device.Entity.DeviceVariableAddInput.DataType">
            <summary>
                读取数据类型
            </summary>
        </member>
        <member name="P:EdgeGateway.Device.Entity.DeviceVariableAddInput.Encoding">
            <summary>
                字符串编码
            </summary>
        </member>
        <member name="P:EdgeGateway.Device.Entity.DeviceVariableAddInput.Method">
            <summary>
                读取方法
            </summary>
        </member>
        <member name="P:EdgeGateway.Device.Entity.DeviceVariableAddInput.RegisterAddress">
            <summary>
                读取地址
            </summary>
        </member>
        <member name="P:EdgeGateway.Device.Entity.DeviceVariableAddInput.ReadLength">
            <summary>
                读取长度
            </summary>
        </member>
        <member name="M:EdgeGateway.Device.Entity.DeviceVariableAddInput.Validate(System.ComponentModel.DataAnnotations.ValidationContext)">
            <summary>
                加强校验
            </summary>
            <param name="validationContext"></param>
            <returns></returns>
            <exception cref="T:System.ArgumentOutOfRangeException"></exception>
        </member>
        <member name="T:EdgeGateway.Device.Entity.DeviceVariableEditInput">
            <summary>
                采集点修改
            </summary>
        </member>
        <member name="P:EdgeGateway.Device.Entity.DeviceVariableEditInput.Id">
            <summary>
                采集点Id
            </summary>
        </member>
        <member name="T:EdgeGateway.Device.Entity.VariableImportInput">
            <summary>
                导入采集点
            </summary>
        </member>
        <member name="P:EdgeGateway.Device.Entity.VariableImportInput.DeviceId">
            <summary>
                设备ID
            </summary>
        </member>
        <member name="P:EdgeGateway.Device.Entity.VariableImportInput.File">
            <summary>
                Excel文件
            </summary>
        </member>
        <member name="P:EdgeGateway.Device.Entity.VariableImportInput.ImportMode">
            <summary>
                导入模式
            </summary>
        </member>
        <member name="P:EdgeGateway.Device.Entity.VariableImportInput.IgnoreFields">
            <summary>
                忽略的字段
            </summary>
        </member>
        <member name="T:EdgeGateway.Device.Entity.ImportModeEnum">
            <summary>
                导入模式
            </summary>
        </member>
        <member name="F:EdgeGateway.Device.Entity.ImportModeEnum.Override">
            <summary>
                覆盖
            </summary>
        </member>
        <member name="F:EdgeGateway.Device.Entity.ImportModeEnum.Merge">
            <summary>
                合并
            </summary>
        </member>
        <member name="F:EdgeGateway.Device.Entity.ImportModeEnum.Append">
            <summary>
                追加
            </summary>
        </member>
        <member name="T:EdgeGateway.Device.Entity.DeviceVariableEditFieldsInput">
            <summary>
                批量编辑字段
            </summary>
        </member>
        <member name="P:EdgeGateway.Device.Entity.DeviceVariableEditFieldsInput.Id">
            <summary>
                采集点ID
            </summary>
        </member>
        <member name="P:EdgeGateway.Device.Entity.DeviceVariableEditFieldsInput.Fields">
            <summary>
                字典方式记录字段名和值
            </summary>
        </member>
        <member name="T:EdgeGateway.Device.Entity.DeviceLabelSelectInput">
            <summary>
                标签选择输入参数
            </summary>
        </member>
        <member name="P:EdgeGateway.Device.Entity.DeviceLabelSelectInput.DeviceId">
            <summary>
                设备ID
            </summary>
        </member>
        <member name="T:EdgeGateway.Device.Entity.DeviceLabelSelectOutput">
            <summary>
                标签选择输出
            </summary>
        </member>
        <member name="P:EdgeGateway.Device.Entity.DeviceLabelSelectOutput.Id">
            <summary>
                标签ID
            </summary>
        </member>
        <member name="P:EdgeGateway.Device.Entity.DeviceLabelSelectOutput.Name">
            <summary>
                标签名称
            </summary>
        </member>
        <member name="P:EdgeGateway.Device.Entity.DeviceLabelSelectOutput.Identifier">
            <summary>
                标签标识
            </summary>
        </member>
        <member name="P:EdgeGateway.Device.Entity.DeviceLabelSelectOutput.DeviceId">
            <summary>
                设备ID
            </summary>
        </member>
        <member name="P:EdgeGateway.Device.Entity.DeviceLabelSelectOutput.TransitionType">
            <summary>
                转换类型
            </summary>
        </member>
        <member name="T:EdgeGateway.Device.Entity.LabelWriteInput">
            <summary>
            标签写入输入参数
            </summary>
        </member>
        <member name="P:EdgeGateway.Device.Entity.LabelWriteInput.Identifier">
            <summary>
            标签标识符
            </summary>
        </member>
        <member name="P:EdgeGateway.Device.Entity.LabelWriteInput.Value">
            <summary>
            写入值
            </summary>
        </member>
        <member name="T:EdgeGateway.Device.Entity.Device">
            <summary>
            </summary>
        </member>
        <member name="P:EdgeGateway.Device.Entity.Device.Identifier">
            <summary>
                设备标识符
            </summary>
        </member>
        <member name="P:EdgeGateway.Device.Entity.Device.Name">
            <summary>
                设备名称
            </summary>
        </member>
        <member name="P:EdgeGateway.Device.Entity.Device.DeviceInfo">
            <summary>
               基本配置
            </summary>
        </member>
        <member name="P:EdgeGateway.Device.Entity.Device.Driver">
            <summary>
                驱动信息
            </summary>
        </member>
        <member name="P:EdgeGateway.Device.Entity.Device.Enable">
            <summary>
                是否启动
            </summary>
        </member>
        <member name="P:EdgeGateway.Device.Entity.Device.DeviceConfigs">
            <summary>
                设备配置
            </summary>
        </member>
        <member name="P:EdgeGateway.Device.Entity.Device.GroupTags">
            <summary>
                分组标签
            </summary>
        </member>
        <member name="P:EdgeGateway.Device.Entity.Device.Location">
            <summary>
                设备位置
            </summary>
        </member>
        <member name="P:EdgeGateway.Device.Entity.Device.Index">
            <summary>
                排序
            </summary>
        </member>
        <member name="P:EdgeGateway.Device.Entity.Device.Description">
            <summary>
                描述
            </summary>
        </member>
        <member name="P:EdgeGateway.Device.Entity.Device.ChannelId">
            <summary>
            通道Id
            </summary>
        </member>
        <member name="P:EdgeGateway.Device.Entity.Device.AllowWrite">
            <summary>
            是否允许写入
            </summary>
        </member>
        <member name="P:EdgeGateway.Device.Entity.Device.LogLevel">
            <summary>
            设备日志等级
            </summary>
        </member>
        <member name="P:EdgeGateway.Device.Entity.Device.DriverName">
            <summary>
                协议名称
            </summary>
        </member>
        <member name="P:EdgeGateway.Device.Entity.Device.IsConnect">
            <summary>
                是否连接
            </summary>
        </member>
        <member name="P:EdgeGateway.Device.Entity.Device.Status">
            <summary>
                是否连接
            </summary>
        </member>
        <member name="P:EdgeGateway.Device.Entity.Device.EdgeChannel">
            <summary>
                边缘通道
            </summary>
        </member>
        <member name="P:EdgeGateway.Device.Entity.Device.DeviceLabel">
            <summary>
                设备属性
            </summary>
        </member>
        <member name="T:EdgeGateway.Device.Entity.DeviceInfo">
            <summary>
            设备基本配置信息
            </summary>
        </member>
        <member name="M:EdgeGateway.Device.Entity.DeviceInfo.#ctor(System.Int32,System.Int32,System.Int32,EdgeGateway.Device.Entity.DataReportTypeEnum,System.Boolean)">
            <summary>
            
            </summary>
            <param name="minPeriod">轮询时间</param>
            <param name="waitTime">恢复就绪等待时间</param>
            <param name="reConnTime">恢复时间</param>
            <param name="reportType">数据上报方式</param>
            <param name="storeHistoryData">是否存储历史数据</param>
        </member>
        <member name="P:EdgeGateway.Device.Entity.DeviceInfo.MinPeriod">
            <summary>
              设备轮询的时间周期
            </summary>
        </member>
        <member name="P:EdgeGateway.Device.Entity.DeviceInfo.WaitTime">
            <summary>
                恢复就绪等待时间
            </summary>
        </member>
        <member name="P:EdgeGateway.Device.Entity.DeviceInfo.ReConnTime">
            <summary>
                恢复时间
            </summary>
        </member>
        <member name="P:EdgeGateway.Device.Entity.DeviceInfo.ReportType">
            <summary>
            数据上报方式
            </summary>
        </member>
        <member name="P:EdgeGateway.Device.Entity.DeviceInfo.StoreHistoryData">
            <summary>
                存储历史数据
            </summary>
        </member>
        <member name="T:EdgeGateway.Device.Entity.DeviceDriver">
            <summary>
                驱动信息
            </summary>
        </member>
        <member name="P:EdgeGateway.Device.Entity.DeviceDriver.DriverName">
            <summary>
                协议名称
            </summary>
        </member>
        <member name="P:EdgeGateway.Device.Entity.DeviceDriver.Version">
            <summary>
                协议版本
            </summary>
        </member>
        <member name="T:EdgeGateway.Device.Entity.DeviceConfig">
            <summary>
            设备连接配置
            </summary>
        </member>
        <member name="P:EdgeGateway.Device.Entity.DeviceConfig.Identifier">
            <summary>
                标识
            </summary>
        </member>
        <member name="P:EdgeGateway.Device.Entity.DeviceConfig.Description">
            <summary>
                描述
            </summary>
        </member>
        <member name="P:EdgeGateway.Device.Entity.DeviceConfig.Value">
            <summary>
                值
            </summary>
        </member>
        <member name="P:EdgeGateway.Device.Entity.DeviceConfig.EnumInfo">
            <summary>
                备注
            </summary>
        </member>
        <member name="P:EdgeGateway.Device.Entity.DeviceConfig.GroupName">
            <summary>
                分组名称
            </summary>
        </member>
        <member name="P:EdgeGateway.Device.Entity.DeviceConfig.IsRequired">
            <summary>
                是否是必填
            </summary>
        </member>
        <member name="P:EdgeGateway.Device.Entity.DeviceConfig.Display">
            <summary>
                是否显示
            </summary>
        </member>
        <member name="P:EdgeGateway.Device.Entity.DeviceConfig.DisplayExpress">
            <summary>
                字段显示条件表达式
            </summary>
        </member>
        <member name="P:EdgeGateway.Device.Entity.DeviceConfig.Order">
            <summary>
                排序,越小越在前
            </summary>
        </member>
        <member name="P:EdgeGateway.Device.Entity.DeviceConfig.Type">
            <summary>
                输入框类型
            </summary>
        </member>
        <member name="T:EdgeGateway.Device.Entity.DeviceEvent">
            <summary>
                设备事件实体
            </summary>
        </member>
        <member name="P:EdgeGateway.Device.Entity.DeviceEvent.DeviceId">
            <summary>
                设备ID
            </summary>
        </member>
        <member name="P:EdgeGateway.Device.Entity.DeviceEvent.EventName">
            <summary>
                事件名称
            </summary>
        </member>
        <member name="P:EdgeGateway.Device.Entity.DeviceEvent.Description">
            <summary>
                事件描述
            </summary>
        </member>
        <member name="P:EdgeGateway.Device.Entity.DeviceEvent.Enable">
            <summary>
                事件是否启用
            </summary>
        </member>
        <member name="P:EdgeGateway.Device.Entity.DeviceEvent.TriggerEventType">
            <summary>
                触发事件类型
            </summary>
        </member>
        <member name="P:EdgeGateway.Device.Entity.DeviceEvent.CustomEventWhereList">
            <summary>
                自定义事件条件列表
            </summary>
        </member>
        <member name="P:EdgeGateway.Device.Entity.DeviceEvent.TimerConfig">
            <summary>
                定时触发配置 - 当TriggerEventType为定时触发或条件定时触发时使用
            </summary>
        </member>
        <member name="P:EdgeGateway.Device.Entity.DeviceEvent.Priority">
            <summary>
                事件优先级 - 用于控制事件处理的顺序和重要程度
            </summary>
        </member>
        <member name="P:EdgeGateway.Device.Entity.DeviceEvent.EventGroup">
            <summary>
                事件分组 - 用于对事件进行分类管理
            </summary>
        </member>
        <member name="P:EdgeGateway.Device.Entity.DeviceEvent.LastTime">
            <summary>
                最后触发时间
            </summary>
        </member>
        <member name="P:EdgeGateway.Device.Entity.DeviceEvent.TriggerCount">
            <summary>
                触发次数
            </summary>
        </member>
        <member name="P:EdgeGateway.Device.Entity.DeviceEvent.Actions">
            <summary>
                事件动作 - 定义事件触发后要执行的动作列表
            </summary>
        </member>
        <member name="P:EdgeGateway.Device.Entity.DeviceEvent.StatusBufferTime">
            <summary>
                状态缓冲时间(毫秒) - 防止状态频繁变化导致事件重复触发
            </summary>
        </member>
        <member name="P:EdgeGateway.Device.Entity.DeviceEvent.SaveTriggerHistory">
            <summary>
                是否保存触发历史 - 控制是否记录事件触发日志
            </summary>
        </member>
        <member name="P:EdgeGateway.Device.Entity.DeviceEvent.HistoryRetentionDays">
            <summary>
                历史记录保留天数 - 事件日志保留的天数，0表示永久保存
            </summary>
        </member>
        <member name="P:EdgeGateway.Device.Entity.DeviceEvent.Device">
            <summary>
                关联设备
            </summary>
        </member>
        <member name="P:EdgeGateway.Device.Entity.DeviceEvent.EventLogs">
            <summary>
                事件日志列表
            </summary>
        </member>
        <member name="T:EdgeGateway.Device.Entity.EntityTimerTriggerConfig">
            <summary>
                定时触发配置
            </summary>
        </member>
        <member name="P:EdgeGateway.Device.Entity.EntityTimerTriggerConfig.ExpressionType">
            <summary>
                定时表达式类型
            </summary>
        </member>
        <member name="P:EdgeGateway.Device.Entity.EntityTimerTriggerConfig.Expression">
            <summary>
                定时表达式 - Cron表达式或其他类型的时间表达式
            </summary>
        </member>
        <member name="P:EdgeGateway.Device.Entity.EntityTimerTriggerConfig.StartTime">
            <summary>
                开始时间 - 定时任务开始执行的时间，为空表示立即开始
            </summary>
        </member>
        <member name="P:EdgeGateway.Device.Entity.EntityTimerTriggerConfig.EndTime">
            <summary>
                结束时间 - 定时任务停止执行的时间，为空表示永不停止
            </summary>
        </member>
        <member name="P:EdgeGateway.Device.Entity.EntityTimerTriggerConfig.MaxTriggerCount">
            <summary>
                最大执行次数 - 定时任务最多执行的次数，为0表示不限制
            </summary>
        </member>
        <member name="P:EdgeGateway.Device.Entity.EntityTimerTriggerConfig.MissedFireHandling">
            <summary>
                是否在错过触发时补偿执行
            </summary>
        </member>
        <member name="T:EdgeGateway.Device.Entity.TimerExpressionTypeEnum">
            <summary>
                定时表达式类型枚举
            </summary>
        </member>
        <member name="F:EdgeGateway.Device.Entity.TimerExpressionTypeEnum.Cron">
            <summary>
                Cron表达式
            </summary>
        </member>
        <member name="F:EdgeGateway.Device.Entity.TimerExpressionTypeEnum.SimpleInterval">
            <summary>
                简单间隔 - 按固定间隔触发，单位为秒
            </summary>
        </member>
        <member name="F:EdgeGateway.Device.Entity.TimerExpressionTypeEnum.DailyTime">
            <summary>
                每日特定时间 - 例如每天8:30
            </summary>
        </member>
        <member name="F:EdgeGateway.Device.Entity.TimerExpressionTypeEnum.WeeklyTime">
            <summary>
                每周特定时间 - 例如每周一8:30
            </summary>
        </member>
        <member name="F:EdgeGateway.Device.Entity.TimerExpressionTypeEnum.MonthlyTime">
            <summary>
                每月特定时间 - 例如每月1日8:30
            </summary>
        </member>
        <member name="T:EdgeGateway.Device.Entity.CustomEventWhere">
            <summary>
                自定义事件条件
            </summary>
        </member>
        <member name="P:EdgeGateway.Device.Entity.CustomEventWhere.Id">
            <summary>
                条件ID
            </summary>
        </member>
        <member name="P:EdgeGateway.Device.Entity.CustomEventWhere.EventId">
            <summary>
                事件ID
            </summary>
        </member>
        <member name="P:EdgeGateway.Device.Entity.CustomEventWhere.PropertyIdentifier">
            <summary>
                属性标识
            </summary>
        </member>
        <member name="P:EdgeGateway.Device.Entity.CustomEventWhere.Operator">
            <summary>
                操作符
            </summary>
        </member>
        <member name="P:EdgeGateway.Device.Entity.CustomEventWhere.CompareValue">
            <summary>
                比较值
            </summary>
        </member>
        <member name="P:EdgeGateway.Device.Entity.CustomEventWhere.LogicalRelation">
            <summary>
                逻辑关系
            </summary>
        </member>
        <member name="P:EdgeGateway.Device.Entity.CustomEventWhere.SortOrder">
            <summary>
                排序顺序
            </summary>
        </member>
        <member name="P:EdgeGateway.Device.Entity.CustomEventWhere.Duration">
            <summary>
                持续时间(秒) - 条件需要持续满足的时间，用于过滤短暂波动
            </summary>
        </member>
        <member name="T:EdgeGateway.Device.Entity.EventAction">
            <summary>
                事件动作
            </summary>
        </member>
        <member name="P:EdgeGateway.Device.Entity.EventAction.Id">
            <summary>
                动作ID
            </summary>
        </member>
        <member name="P:EdgeGateway.Device.Entity.EventAction.ActionType">
            <summary>
                动作类型
            </summary>
        </member>
        <member name="P:EdgeGateway.Device.Entity.EventAction.Name">
            <summary>
                动作名称
            </summary>
        </member>
        <member name="P:EdgeGateway.Device.Entity.EventAction.Parameters">
            <summary>
                动作参数 - JSON格式存储的动作参数
            </summary>
        </member>
        <member name="P:EdgeGateway.Device.Entity.EventAction.Order">
            <summary>
                执行顺序
            </summary>
        </member>
        <member name="P:EdgeGateway.Device.Entity.EventAction.Enabled">
            <summary>
                是否启用
            </summary>
        </member>
        <member name="T:EdgeGateway.Device.Entity.DeviceEventLog">
            <summary>
                设备事件日志实体 - 记录设备事件的触发历史
            </summary>
        </member>
        <member name="P:EdgeGateway.Device.Entity.DeviceEventLog.DeviceId">
            <summary>
                设备ID
            </summary>
        </member>
        <member name="P:EdgeGateway.Device.Entity.DeviceEventLog.DeviceEventId">
            <summary>
                设备事件ID
            </summary>
        </member>
        <member name="P:EdgeGateway.Device.Entity.DeviceEventLog.EventType">
            <summary>
                事件类型
            </summary>
        </member>
        <member name="P:EdgeGateway.Device.Entity.DeviceEventLog.Title">
            <summary>
                日志标题
            </summary>
        </member>
        <member name="P:EdgeGateway.Device.Entity.DeviceEventLog.Description">
            <summary>
                日志描述
            </summary>
        </member>
        <member name="P:EdgeGateway.Device.Entity.DeviceEventLog.DeviceStatus">
            <summary>
                触发时的设备状态
            </summary>
        </member>
        <member name="P:EdgeGateway.Device.Entity.DeviceEventLog.TriggerConditions">
            <summary>
                触发条件 - JSON格式存储触发事件时满足的条件
            </summary>
        </member>
        <member name="P:EdgeGateway.Device.Entity.DeviceEventLog.ExecutedActions">
            <summary>
                执行的动作 - JSON格式存储事件触发后执行的动作
            </summary>
        </member>
        <member name="P:EdgeGateway.Device.Entity.DeviceEventLog.ActionResults">
            <summary>
                执行结果 - JSON格式存储动作执行的结果
            </summary>
        </member>
        <member name="P:EdgeGateway.Device.Entity.DeviceEventLog.IsSuccessful">
            <summary>
                是否执行成功
            </summary>
        </member>
        <member name="P:EdgeGateway.Device.Entity.DeviceEventLog.ErrorMessage">
            <summary>
                错误信息
            </summary>
        </member>
        <member name="P:EdgeGateway.Device.Entity.DeviceEventLog.ProcessingTime">
            <summary>
                处理时长(毫秒)
            </summary>
        </member>
        <member name="P:EdgeGateway.Device.Entity.DeviceEventLog.AdditionalData">
            <summary>
                附加数据 - JSON格式存储的附加信息
            </summary>
        </member>
        <member name="P:EdgeGateway.Device.Entity.DeviceEventLog.Device">
            <summary>
                关联设备
            </summary>
        </member>
        <member name="P:EdgeGateway.Device.Entity.DeviceEventLog.DeviceEvent">
            <summary>
                关联设备事件
            </summary>
        </member>
        <member name="T:EdgeGateway.Device.Entity.EventLogCondition">
            <summary>
                事件日志条件 - 记录触发时满足的条件
            </summary>
        </member>
        <member name="P:EdgeGateway.Device.Entity.EventLogCondition.PropertyIdentifier">
            <summary>
                属性标识
            </summary>
        </member>
        <member name="P:EdgeGateway.Device.Entity.EventLogCondition.PropertyName">
            <summary>
                属性名称
            </summary>
        </member>
        <member name="P:EdgeGateway.Device.Entity.EventLogCondition.Operator">
            <summary>
                操作符
            </summary>
        </member>
        <member name="P:EdgeGateway.Device.Entity.EventLogCondition.ExpectedValue">
            <summary>
                条件值
            </summary>
        </member>
        <member name="P:EdgeGateway.Device.Entity.EventLogCondition.ActualValue">
            <summary>
                实际值
            </summary>
        </member>
        <member name="P:EdgeGateway.Device.Entity.EventLogCondition.IsSatisfied">
            <summary>
                是否满足条件
            </summary>
        </member>
        <member name="T:EdgeGateway.Device.Entity.EventLogAction">
            <summary>
                事件日志动作 - 记录执行的动作
            </summary>
        </member>
        <member name="P:EdgeGateway.Device.Entity.EventLogAction.ActionId">
            <summary>
                动作ID
            </summary>
        </member>
        <member name="P:EdgeGateway.Device.Entity.EventLogAction.ActionType">
            <summary>
                动作类型
            </summary>
        </member>
        <member name="P:EdgeGateway.Device.Entity.EventLogAction.Name">
            <summary>
                动作名称
            </summary>
        </member>
        <member name="P:EdgeGateway.Device.Entity.EventLogAction.Parameters">
            <summary>
                动作参数
            </summary>
        </member>
        <member name="P:EdgeGateway.Device.Entity.EventLogAction.Order">
            <summary>
                执行顺序
            </summary>
        </member>
        <member name="T:EdgeGateway.Device.Entity.EventLogActionResult">
            <summary>
                事件日志动作结果 - 记录动作执行的结果
            </summary>
        </member>
        <member name="P:EdgeGateway.Device.Entity.EventLogActionResult.ActionId">
            <summary>
                动作ID
            </summary>
        </member>
        <member name="P:EdgeGateway.Device.Entity.EventLogActionResult.IsSuccessful">
            <summary>
                是否执行成功
            </summary>
        </member>
        <member name="P:EdgeGateway.Device.Entity.EventLogActionResult.Result">
            <summary>
                执行结果
            </summary>
        </member>
        <member name="P:EdgeGateway.Device.Entity.EventLogActionResult.ErrorMessage">
            <summary>
                错误信息
            </summary>
        </member>
        <member name="P:EdgeGateway.Device.Entity.EventLogActionResult.ExecutionTime">
            <summary>
                执行时间
            </summary>
        </member>
        <member name="P:EdgeGateway.Device.Entity.EventLogActionResult.ExecutionTime_ms">
            <summary>
                执行耗时(毫秒)
            </summary>
        </member>
        <member name="T:EdgeGateway.Device.Entity.DeviceLabel">
            <summary>
                设备标签
            </summary>
        </member>
        <member name="P:EdgeGateway.Device.Entity.DeviceLabel.DeviceId">
            <summary>
                所属设备
            </summary>
        </member>
        <member name="P:EdgeGateway.Device.Entity.DeviceLabel.Identifier">
            <summary>
                标识符
            </summary>
        </member>
        <member name="P:EdgeGateway.Device.Entity.DeviceLabel.Name">
            <summary>
                变量名
            </summary>
        </member>
        <member name="P:EdgeGateway.Device.Entity.DeviceLabel.TransitionType">
            <summary>
                转换数据类型
            </summary>
        </member>
        <member name="P:EdgeGateway.Device.Entity.DeviceLabel.ValueSource">
            <summary>
                数据来源
            </summary>
        </member>
        <member name="P:EdgeGateway.Device.Entity.DeviceLabel.ActionOrder">
            <summary>
                执行优先级:0最优
            </summary>
        </member>
        <member name="P:EdgeGateway.Device.Entity.DeviceLabel.Custom">
            <summary>
                自定义映射规则
            </summary>
        </member>
        <member name="P:EdgeGateway.Device.Entity.DeviceLabel.Content">
            <summary>
                默认值/脚本内容/表达式
            </summary>
        </member>
        <member name="P:EdgeGateway.Device.Entity.DeviceLabel.SendType">
            <summary>
                上送方式
            </summary>
        </member>
        <member name="P:EdgeGateway.Device.Entity.DeviceLabel.Tags">
            <summary>
                标签
            </summary>
        </member>
        <member name="P:EdgeGateway.Device.Entity.DeviceLabel.Period">
            <summary>
                采集周期
            </summary>
        </member>
        <member name="P:EdgeGateway.Device.Entity.DeviceLabel.ArchiveTime">
            <summary>
                强制归档时间
            </summary>
        </member>
        <member name="P:EdgeGateway.Device.Entity.DeviceLabel.UploadInterval">
            <summary>
                定时上报周期（单位：秒）
            </summary>
        </member>
        <member name="P:EdgeGateway.Device.Entity.DeviceLabel.Length">
            <summary>
                长度(属性值小数点保留位数长度)
            </summary>
        </member>
        <member name="P:EdgeGateway.Device.Entity.DeviceLabel.Unit">
            <summary>
                单位
            </summary>
        </member>
        <member name="P:EdgeGateway.Device.Entity.DeviceLabel.Description">
            <summary>
                描述
            </summary>
        </member>
        <member name="P:EdgeGateway.Device.Entity.DeviceLabel.Enable">
            <summary>
                是否启动
            </summary>
        </member>
        <member name="P:EdgeGateway.Device.Entity.DeviceLabel.ProtectType">
            <summary>
                读写方式
            </summary>
        </member>
        <member name="P:EdgeGateway.Device.Entity.DeviceLabel.DataType">
            <summary>
                读取数据类型
            </summary>
        </member>
        <member name="P:EdgeGateway.Device.Entity.DeviceLabel.Encoding">
            <summary>
                字符串编码
            </summary>
        </member>
        <member name="P:EdgeGateway.Device.Entity.DeviceLabel.Method">
            <summary>
                读取方法
            </summary>
        </member>
        <member name="P:EdgeGateway.Device.Entity.DeviceLabel.RegisterAddress">
            <summary>
                读取地址
            </summary>
        </member>
        <member name="P:EdgeGateway.Device.Entity.DeviceLabel.ReadLength">
            <summary>
                读取长度
            </summary>
        </member>
        <member name="P:EdgeGateway.Device.Entity.DeviceLabel.Device">
            <summary>
                设备
            </summary>
        </member>
        <member name="P:EdgeGateway.Device.Entity.DeviceLabel.CurrentValue">
            <summary>
                当前值
            </summary>
        </member>
        <member name="P:EdgeGateway.Device.Entity.DeviceLabel.UpdateTime">
            <summary>
                最后更新时间
            </summary>
        </member>
        <member name="T:EdgeGateway.Device.Entity.DeviceTemplate">
            <summary>
            设备模板
            </summary>
        </member>
        <member name="P:EdgeGateway.Device.Entity.DeviceTemplate.Name">
            <summary>
            模板名称
            </summary>
        </member>
        <member name="P:EdgeGateway.Device.Entity.DeviceTemplate.Description">
            <summary>
            模板描述
            </summary>
        </member>
        <member name="P:EdgeGateway.Device.Entity.DeviceTemplate.DriverName">
            <summary>
            驱动名称
            </summary>
        </member>
        <member name="P:EdgeGateway.Device.Entity.DeviceTemplate.CreateTime">
            <summary>
            创建时间
            </summary>
        </member>
        <member name="P:EdgeGateway.Device.Entity.DeviceTemplate.Labels">
            <summary>
            模板下的点位列表
            </summary>
        </member>
        <member name="T:EdgeGateway.Device.Entity.DeviceTemplateLabel">
            <summary>
                设备模板标签
            </summary>
        </member>
        <member name="P:EdgeGateway.Device.Entity.DeviceTemplateLabel.TemplateId">
            <summary>
                所属模板ID
            </summary>
        </member>
        <member name="P:EdgeGateway.Device.Entity.DeviceTemplateLabel.Identifier">
            <summary>
                标识符
            </summary>
        </member>
        <member name="P:EdgeGateway.Device.Entity.DeviceTemplateLabel.Name">
            <summary>
                变量名
            </summary>
        </member>
        <member name="P:EdgeGateway.Device.Entity.DeviceTemplateLabel.TransitionType">
            <summary>
                转换数据类型
            </summary>
        </member>
        <member name="P:EdgeGateway.Device.Entity.DeviceTemplateLabel.ValueSource">
            <summary>
                数据来源
            </summary>
        </member>
        <member name="P:EdgeGateway.Device.Entity.DeviceTemplateLabel.ActionOrder">
            <summary>
                执行优先级:0最优
            </summary>
        </member>
        <member name="P:EdgeGateway.Device.Entity.DeviceTemplateLabel.Custom">
            <summary>
                自定义映射规则
            </summary>
        </member>
        <member name="P:EdgeGateway.Device.Entity.DeviceTemplateLabel.Content">
            <summary>
                默认值/脚本内容/表达式
            </summary>
        </member>
        <member name="P:EdgeGateway.Device.Entity.DeviceTemplateLabel.SendType">
            <summary>
                上送方式
            </summary>
        </member>
        <member name="P:EdgeGateway.Device.Entity.DeviceTemplateLabel.Tags">
            <summary>
                标签
            </summary>
        </member>
        <member name="P:EdgeGateway.Device.Entity.DeviceTemplateLabel.Period">
            <summary>
                采集周期
            </summary>
        </member>
        <member name="P:EdgeGateway.Device.Entity.DeviceTemplateLabel.ArchiveTime">
            <summary>
                强制归档时间
            </summary>
        </member>
        <member name="P:EdgeGateway.Device.Entity.DeviceTemplateLabel.Length">
            <summary>
                长度(属性值小数点保留位数长度)
            </summary>
        </member>
        <member name="P:EdgeGateway.Device.Entity.DeviceTemplateLabel.Unit">
            <summary>
                单位
            </summary>
        </member>
        <member name="P:EdgeGateway.Device.Entity.DeviceTemplateLabel.Description">
            <summary>
                描述
            </summary>
        </member>
        <member name="P:EdgeGateway.Device.Entity.DeviceTemplateLabel.Enable">
            <summary>
                是否启动
            </summary>
        </member>
        <member name="P:EdgeGateway.Device.Entity.DeviceTemplateLabel.ProtectType">
            <summary>
                读写方式
            </summary>
        </member>
        <member name="P:EdgeGateway.Device.Entity.DeviceTemplateLabel.DataType">
            <summary>
                读取数据类型
            </summary>
        </member>
        <member name="P:EdgeGateway.Device.Entity.DeviceTemplateLabel.Encoding">
            <summary>
                字符串编码
            </summary>
        </member>
        <member name="P:EdgeGateway.Device.Entity.DeviceTemplateLabel.Method">
            <summary>
                读取方法
            </summary>
        </member>
        <member name="P:EdgeGateway.Device.Entity.DeviceTemplateLabel.RegisterAddress">
            <summary>
                读取地址
            </summary>
        </member>
        <member name="P:EdgeGateway.Device.Entity.DeviceTemplateLabel.ReadLength">
            <summary>
                读取长度
            </summary>
        </member>
        <member name="T:EdgeGateway.Device.Entity.DeviceWriteLog">
            <summary>
            设备写入日志
            </summary>
        </member>
        <member name="P:EdgeGateway.Device.Entity.DeviceWriteLog.DeviceId">
            <summary>
            设备ID
            </summary>
        </member>
        <member name="P:EdgeGateway.Device.Entity.DeviceWriteLog.LabelIdentifier">
            <summary>
            标签标识符
            </summary>
        </member>
        <member name="P:EdgeGateway.Device.Entity.DeviceWriteLog.Address">
            <summary>
            写入地址
            </summary>
        </member>
        <member name="P:EdgeGateway.Device.Entity.DeviceWriteLog.Value">
            <summary>
            写入值
            </summary>
        </member>
        <member name="P:EdgeGateway.Device.Entity.DeviceWriteLog.DataType">
            <summary>
            数据类型
            </summary>
        </member>
        <member name="P:EdgeGateway.Device.Entity.DeviceWriteLog.Length">
            <summary>
            数据长度
            </summary>
        </member>
        <member name="P:EdgeGateway.Device.Entity.DeviceWriteLog.Encoding">
            <summary>
            编码方式
            </summary>
        </member>
        <member name="P:EdgeGateway.Device.Entity.DeviceWriteLog.SourceType">
            <summary>
            写入来源类型
            </summary>
        </member>
        <member name="P:EdgeGateway.Device.Entity.DeviceWriteLog.WriteTime">
            <summary>
            写入时间
            </summary>
        </member>
        <member name="P:EdgeGateway.Device.Entity.DeviceWriteLog.Success">
            <summary>
            写入状态
            </summary>
        </member>
        <member name="P:EdgeGateway.Device.Entity.DeviceWriteLog.ErrorMessage">
            <summary>
            错误信息
            </summary>
        </member>
        <member name="T:EdgeGateway.Device.Entity.WriteSourceType">
            <summary>
            写入来源类型
            </summary>
        </member>
        <member name="F:EdgeGateway.Device.Entity.WriteSourceType.Manual">
            <summary>
            手动写入
            </summary>
        </member>
        <member name="F:EdgeGateway.Device.Entity.WriteSourceType.RemoteApi">
            <summary>
            远程API写入
            </summary>
        </member>
        <member name="F:EdgeGateway.Device.Entity.WriteSourceType.LocalScript">
            <summary>
            本地脚本写入
            </summary>
        </member>
        <member name="F:EdgeGateway.Device.Entity.WriteSourceType.ScheduledTask">
            <summary>
            定时任务写入
            </summary>
        </member>
        <member name="F:EdgeGateway.Device.Entity.WriteSourceType.EventTriggered">
            <summary>
            事件触发写入
            </summary>
        </member>
        <member name="F:EdgeGateway.Device.Entity.WriteSourceType.Other">
            <summary>
            其他来源
            </summary>
        </member>
        <member name="T:EdgeGateway.Device.Entity.EdgeChannel">
            <summary>
                边缘通道
            </summary>
        </member>
        <member name="P:EdgeGateway.Device.Entity.EdgeChannel.ChannelName">
            <summary>
                通道名称
            </summary>
        </member>
        <member name="P:EdgeGateway.Device.Entity.EdgeChannel.Serial">
            <summary>
                串口号
            </summary>
        </member>
        <member name="P:EdgeGateway.Device.Entity.EdgeChannel.BaudRate">
            <summary>
                波特率
            </summary>
        </member>
        <member name="P:EdgeGateway.Device.Entity.EdgeChannel.DataBits">
            <summary>
                数据位
            </summary>
        </member>
        <member name="P:EdgeGateway.Device.Entity.EdgeChannel.Stop">
            <summary>
                停止位
            </summary>
        </member>
        <member name="P:EdgeGateway.Device.Entity.EdgeChannel.Checkout">
            <summary>
                校验位
            </summary>
        </member>
        <member name="P:EdgeGateway.Device.Entity.EdgeChannel.ThreadCount">
            <summary>
                线程数量
            </summary>
        </member>
        <member name="P:EdgeGateway.Device.Entity.EdgeChannel.Enable">
            <summary>
                状态 ：启用；禁用
            </summary>
        </member>
        <member name="P:EdgeGateway.Device.Entity.EdgeChannel.DeviceCount">
            <summary>
                设备数量
            </summary>
        </member>
        <member name="T:EdgeGateway.Device.Entity.EventDiagnosticInfo">
            <summary>
                事件诊断信息实体 - 记录事件执行的诊断数据
            </summary>
        </member>
        <member name="P:EdgeGateway.Device.Entity.EventDiagnosticInfo.DeviceEventId">
            <summary>
                设备事件ID
            </summary>
        </member>
        <member name="P:EdgeGateway.Device.Entity.EventDiagnosticInfo.DeviceId">
            <summary>
                设备ID
            </summary>
        </member>
        <member name="P:EdgeGateway.Device.Entity.EventDiagnosticInfo.SessionId">
            <summary>
                诊断会话ID - 用于关联同一次诊断的多条记录
            </summary>
        </member>
        <member name="P:EdgeGateway.Device.Entity.EventDiagnosticInfo.DiagnosticType">
            <summary>
                诊断类型
            </summary>
        </member>
        <member name="P:EdgeGateway.Device.Entity.EventDiagnosticInfo.Level">
            <summary>
                诊断级别
            </summary>
        </member>
        <member name="P:EdgeGateway.Device.Entity.EventDiagnosticInfo.Title">
            <summary>
                诊断标题
            </summary>
        </member>
        <member name="P:EdgeGateway.Device.Entity.EventDiagnosticInfo.Message">
            <summary>
                诊断信息
            </summary>
        </member>
        <member name="P:EdgeGateway.Device.Entity.EventDiagnosticInfo.ConditionDetails">
            <summary>
                条件评估详情 - 记录条件评估的详细信息
            </summary>
        </member>
        <member name="P:EdgeGateway.Device.Entity.EventDiagnosticInfo.ScheduleTaskStatus">
            <summary>
                调度任务状态 - 如果是定时任务，记录调度状态
            </summary>
        </member>
        <member name="P:EdgeGateway.Device.Entity.EventDiagnosticInfo.PerformanceMetrics">
            <summary>
                事件处理性能指标
            </summary>
        </member>
        <member name="P:EdgeGateway.Device.Entity.EventDiagnosticInfo.EnvironmentInfo">
            <summary>
                环境信息 - 记录诊断时的环境信息
            </summary>
        </member>
        <member name="P:EdgeGateway.Device.Entity.EventDiagnosticInfo.DiagnosticCode">
            <summary>
                诊断代码
            </summary>
        </member>
        <member name="P:EdgeGateway.Device.Entity.EventDiagnosticInfo.SuggestedAction">
            <summary>
                建议操作
            </summary>
        </member>
        <member name="P:EdgeGateway.Device.Entity.EventDiagnosticInfo.IsResolved">
            <summary>
                问题是否已解决
            </summary>
        </member>
        <member name="P:EdgeGateway.Device.Entity.EventDiagnosticInfo.ResolvedTime">
            <summary>
                解决时间
            </summary>
        </member>
        <member name="P:EdgeGateway.Device.Entity.EventDiagnosticInfo.Resolution">
            <summary>
                解决方法
            </summary>
        </member>
        <member name="P:EdgeGateway.Device.Entity.EventDiagnosticInfo.DeviceEvent">
            <summary>
                关联设备事件
            </summary>
        </member>
        <member name="P:EdgeGateway.Device.Entity.EventDiagnosticInfo.Device">
            <summary>
                关联设备
            </summary>
        </member>
        <member name="T:EdgeGateway.Device.Entity.ConditionEvaluationDetail">
            <summary>
                条件评估详情 - 记录条件评估的详细信息
            </summary>
        </member>
        <member name="P:EdgeGateway.Device.Entity.ConditionEvaluationDetail.ConditionId">
            <summary>
                条件ID
            </summary>
        </member>
        <member name="P:EdgeGateway.Device.Entity.ConditionEvaluationDetail.Description">
            <summary>
                条件描述
            </summary>
        </member>
        <member name="P:EdgeGateway.Device.Entity.ConditionEvaluationDetail.PropertyIdentifier">
            <summary>
                属性标识
            </summary>
        </member>
        <member name="P:EdgeGateway.Device.Entity.ConditionEvaluationDetail.Operator">
            <summary>
                操作符
            </summary>
        </member>
        <member name="P:EdgeGateway.Device.Entity.ConditionEvaluationDetail.ExpectedValue">
            <summary>
                期望值
            </summary>
        </member>
        <member name="P:EdgeGateway.Device.Entity.ConditionEvaluationDetail.ActualValue">
            <summary>
                实际值
            </summary>
        </member>
        <member name="P:EdgeGateway.Device.Entity.ConditionEvaluationDetail.IsSatisfied">
            <summary>
                是否满足条件
            </summary>
        </member>
        <member name="P:EdgeGateway.Device.Entity.ConditionEvaluationDetail.EvaluationTime">
            <summary>
                评估时间
            </summary>
        </member>
        <member name="P:EdgeGateway.Device.Entity.ConditionEvaluationDetail.DetailedReason">
            <summary>
                详细原因
            </summary>
        </member>
        <member name="P:EdgeGateway.Device.Entity.ConditionEvaluationDetail.ValueType">
            <summary>
                值数据类型
            </summary>
        </member>
        <member name="P:EdgeGateway.Device.Entity.ConditionEvaluationDetail.IsKeyCondition">
            <summary>
                是否为关键条件
            </summary>
        </member>
        <member name="T:EdgeGateway.Device.Entity.ScheduleTaskDiagnostic">
            <summary>
                调度任务诊断 - 记录调度任务的诊断信息
            </summary>
        </member>
        <member name="P:EdgeGateway.Device.Entity.ScheduleTaskDiagnostic.TaskId">
            <summary>
                任务ID
            </summary>
        </member>
        <member name="P:EdgeGateway.Device.Entity.ScheduleTaskDiagnostic.TaskIdentifier">
            <summary>
                任务标识符
            </summary>
        </member>
        <member name="P:EdgeGateway.Device.Entity.ScheduleTaskDiagnostic.Status">
            <summary>
                任务状态
            </summary>
        </member>
        <member name="P:EdgeGateway.Device.Entity.ScheduleTaskDiagnostic.ExistsInMemory">
            <summary>
                内存中是否存在
            </summary>
        </member>
        <member name="P:EdgeGateway.Device.Entity.ScheduleTaskDiagnostic.IsRegistered">
            <summary>
                是否已注册到调度器
            </summary>
        </member>
        <member name="P:EdgeGateway.Device.Entity.ScheduleTaskDiagnostic.LastExecuteTime">
            <summary>
                上次执行时间
            </summary>
        </member>
        <member name="P:EdgeGateway.Device.Entity.ScheduleTaskDiagnostic.NextExecuteTime">
            <summary>
                下次执行时间
            </summary>
        </member>
        <member name="P:EdgeGateway.Device.Entity.ScheduleTaskDiagnostic.MissedReason">
            <summary>
                当前错过执行的原因
            </summary>
        </member>
        <member name="P:EdgeGateway.Device.Entity.ScheduleTaskDiagnostic.SchedulerHealth">
            <summary>
                调度器健康状态
            </summary>
        </member>
        <member name="P:EdgeGateway.Device.Entity.ScheduleTaskDiagnostic.ThreadState">
            <summary>
                线程状态
            </summary>
        </member>
        <member name="P:EdgeGateway.Device.Entity.ScheduleTaskDiagnostic.LastError">
            <summary>
                最后错误信息
            </summary>
        </member>
        <member name="T:EdgeGateway.Device.Entity.EventPerformanceMetrics">
            <summary>
                事件处理性能指标 - 记录事件处理的性能数据
            </summary>
        </member>
        <member name="P:EdgeGateway.Device.Entity.EventPerformanceMetrics.ConditionEvaluationTime">
            <summary>
                条件评估耗时(毫秒)
            </summary>
        </member>
        <member name="P:EdgeGateway.Device.Entity.EventPerformanceMetrics.ActionExecutionTime">
            <summary>
                动作执行耗时(毫秒)
            </summary>
        </member>
        <member name="P:EdgeGateway.Device.Entity.EventPerformanceMetrics.TotalProcessingTime">
            <summary>
                总处理耗时(毫秒)
            </summary>
        </member>
        <member name="P:EdgeGateway.Device.Entity.EventPerformanceMetrics.CpuUsage">
            <summary>
                CPU使用率(%)
            </summary>
        </member>
        <member name="P:EdgeGateway.Device.Entity.EventPerformanceMetrics.MemoryUsage">
            <summary>
                内存使用(KB)
            </summary>
        </member>
        <member name="P:EdgeGateway.Device.Entity.EventPerformanceMetrics.QueueDelay">
            <summary>
                队列延迟(毫秒)
            </summary>
        </member>
        <member name="P:EdgeGateway.Device.Entity.EventPerformanceMetrics.DataFetchTime">
            <summary>
                数据获取耗时(毫秒)
            </summary>
        </member>
        <member name="P:EdgeGateway.Device.Entity.EventPerformanceMetrics.LoggingTime">
            <summary>
                日志记录耗时(毫秒)
            </summary>
        </member>
        <member name="T:EdgeGateway.Device.Entity.DiagnosticTypeEnum">
            <summary>
                诊断类型枚举
            </summary>
        </member>
        <member name="F:EdgeGateway.Device.Entity.DiagnosticTypeEnum.ConditionEvaluation">
            <summary>
                条件评估
            </summary>
        </member>
        <member name="F:EdgeGateway.Device.Entity.DiagnosticTypeEnum.ScheduleTask">
            <summary>
                调度任务
            </summary>
        </member>
        <member name="F:EdgeGateway.Device.Entity.DiagnosticTypeEnum.ActionExecution">
            <summary>
                动作执行
            </summary>
        </member>
        <member name="F:EdgeGateway.Device.Entity.DiagnosticTypeEnum.SystemResource">
            <summary>
                系统资源
            </summary>
        </member>
        <member name="F:EdgeGateway.Device.Entity.DiagnosticTypeEnum.DataFlow">
            <summary>
                数据流
            </summary>
        </member>
        <member name="F:EdgeGateway.Device.Entity.DiagnosticTypeEnum.Configuration">
            <summary>
                配置问题
            </summary>
        </member>
        <member name="F:EdgeGateway.Device.Entity.DiagnosticTypeEnum.HealthCheck">
            <summary>
                健康检查
            </summary>
        </member>
        <member name="T:EdgeGateway.Device.Entity.DiagnosticLevelEnum">
            <summary>
                诊断级别枚举
            </summary>
        </member>
        <member name="F:EdgeGateway.Device.Entity.DiagnosticLevelEnum.Verbose">
            <summary>
                详细信息
            </summary>
        </member>
        <member name="F:EdgeGateway.Device.Entity.DiagnosticLevelEnum.Debug">
            <summary>
                调试信息
            </summary>
        </member>
        <member name="F:EdgeGateway.Device.Entity.DiagnosticLevelEnum.Info">
            <summary>
                一般信息
            </summary>
        </member>
        <member name="F:EdgeGateway.Device.Entity.DiagnosticLevelEnum.Warning">
            <summary>
                警告
            </summary>
        </member>
        <member name="F:EdgeGateway.Device.Entity.DiagnosticLevelEnum.Error">
            <summary>
                错误
            </summary>
        </member>
        <member name="F:EdgeGateway.Device.Entity.DiagnosticLevelEnum.Critical">
            <summary>
                严重错误
            </summary>
        </member>
        <member name="T:EdgeGateway.Device.Entity.EventHealthStatus">
            <summary>
                事件健康状态实体 - 记录和监控事件的健康状态
            </summary>
        </member>
        <member name="P:EdgeGateway.Device.Entity.EventHealthStatus.DeviceEventId">
            <summary>
                设备事件ID
            </summary>
        </member>
        <member name="P:EdgeGateway.Device.Entity.EventHealthStatus.DeviceId">
            <summary>
                设备ID
            </summary>
        </member>
        <member name="P:EdgeGateway.Device.Entity.EventHealthStatus.EventName">
            <summary>
                事件名称
            </summary>
        </member>
        <member name="P:EdgeGateway.Device.Entity.EventHealthStatus.Status">
            <summary>
                健康状态
            </summary>
        </member>
        <member name="P:EdgeGateway.Device.Entity.EventHealthStatus.LastStatusChangeTime">
            <summary>
                最后一次状态变更时间
            </summary>
        </member>
        <member name="P:EdgeGateway.Device.Entity.EventHealthStatus.LastTriggerTime">
            <summary>
                最后一次触发时间
            </summary>
        </member>
        <member name="P:EdgeGateway.Device.Entity.EventHealthStatus.LastDiagnosticTime">
            <summary>
                最后一次诊断时间
            </summary>
        </member>
        <member name="P:EdgeGateway.Device.Entity.EventHealthStatus.TriggerCount">
            <summary>
                触发计数
            </summary>
        </member>
        <member name="P:EdgeGateway.Device.Entity.EventHealthStatus.FailureCount">
            <summary>
                失败计数
            </summary>
        </member>
        <member name="P:EdgeGateway.Device.Entity.EventHealthStatus.LastSuccessRate">
            <summary>
                上次成功率(%)
            </summary>
        </member>
        <member name="P:EdgeGateway.Device.Entity.EventHealthStatus.AvgExecutionTime">
            <summary>
                平均执行时间(毫秒)
            </summary>
        </member>
        <member name="P:EdgeGateway.Device.Entity.EventHealthStatus.MaxExecutionTime">
            <summary>
                最大执行时间(毫秒)
            </summary>
        </member>
        <member name="P:EdgeGateway.Device.Entity.EventHealthStatus.LastMissedScheduleTime">
            <summary>
                最近一次错过调度的时间
            </summary>
        </member>
        <member name="P:EdgeGateway.Device.Entity.EventHealthStatus.MissedScheduleCount">
            <summary>
                错过调度计数
            </summary>
        </member>
        <member name="P:EdgeGateway.Device.Entity.EventHealthStatus.MemoryStatus">
            <summary>
                内存中状态
            </summary>
        </member>
        <member name="P:EdgeGateway.Device.Entity.EventHealthStatus.LastCheckTime">
            <summary>
                上次状态检查时间
            </summary>
        </member>
        <member name="P:EdgeGateway.Device.Entity.EventHealthStatus.StatusDetail">
            <summary>
                健康状态详情
            </summary>
        </member>
        <member name="P:EdgeGateway.Device.Entity.EventHealthStatus.LastErrorMessage">
            <summary>
                最后错误信息
            </summary>
        </member>
        <member name="P:EdgeGateway.Device.Entity.EventHealthStatus.LastErrorStack">
            <summary>
                最后错误堆栈
            </summary>
        </member>
        <member name="P:EdgeGateway.Device.Entity.EventHealthStatus.PerformanceHistory">
            <summary>
                性能历史记录 - 记录最近的性能指标
            </summary>
        </member>
        <member name="P:EdgeGateway.Device.Entity.EventHealthStatus.EventType">
            <summary>
                事件类型
            </summary>
        </member>
        <member name="P:EdgeGateway.Device.Entity.EventHealthStatus.MonitoringEnabled">
            <summary>
                监控启用状态
            </summary>
        </member>
        <member name="P:EdgeGateway.Device.Entity.EventHealthStatus.NeedsAttention">
            <summary>
                需要关注
            </summary>
        </member>
        <member name="P:EdgeGateway.Device.Entity.EventHealthStatus.Priority">
            <summary>
                优先级
            </summary>
        </member>
        <member name="P:EdgeGateway.Device.Entity.EventHealthStatus.EventGroup">
            <summary>
                事件分组
            </summary>
        </member>
        <member name="P:EdgeGateway.Device.Entity.EventHealthStatus.DeviceEvent">
            <summary>
                关联设备事件
            </summary>
        </member>
        <member name="P:EdgeGateway.Device.Entity.EventHealthStatus.Device">
            <summary>
                关联设备
            </summary>
        </member>
        <member name="P:EdgeGateway.Device.Entity.EventHealthStatus.DiagnosticInfos">
            <summary>
                关联的诊断信息列表
            </summary>
        </member>
        <member name="P:EdgeGateway.Device.Entity.EventHealthStatus.ScheduleTasks">
            <summary>
                关联的调度任务
            </summary>
        </member>
        <member name="T:EdgeGateway.Device.Entity.PerformanceHistoryEntry">
            <summary>
                性能历史记录项
            </summary>
        </member>
        <member name="P:EdgeGateway.Device.Entity.PerformanceHistoryEntry.RecordTime">
            <summary>
                记录时间
            </summary>
        </member>
        <member name="P:EdgeGateway.Device.Entity.PerformanceHistoryEntry.ExecutionTime">
            <summary>
                执行时间(毫秒)
            </summary>
        </member>
        <member name="P:EdgeGateway.Device.Entity.PerformanceHistoryEntry.CpuUsage">
            <summary>
                CPU使用率(%)
            </summary>
        </member>
        <member name="P:EdgeGateway.Device.Entity.PerformanceHistoryEntry.MemoryUsage">
            <summary>
                内存使用(KB)
            </summary>
        </member>
        <member name="P:EdgeGateway.Device.Entity.PerformanceHistoryEntry.TriggerResult">
            <summary>
                触发结果
            </summary>
        </member>
        <member name="P:EdgeGateway.Device.Entity.PerformanceHistoryEntry.ActionCount">
            <summary>
                执行动作数量
            </summary>
        </member>
        <member name="T:EdgeGateway.Device.Entity.EventHealthStatusEnum">
            <summary>
                事件健康状态枚举
            </summary>
        </member>
        <member name="F:EdgeGateway.Device.Entity.EventHealthStatusEnum.Normal">
            <summary>
                正常
            </summary>
        </member>
        <member name="F:EdgeGateway.Device.Entity.EventHealthStatusEnum.Warning">
            <summary>
                警告
            </summary>
        </member>
        <member name="F:EdgeGateway.Device.Entity.EventHealthStatusEnum.Error">
            <summary>
                错误
            </summary>
        </member>
        <member name="F:EdgeGateway.Device.Entity.EventHealthStatusEnum.Critical">
            <summary>
                严重
            </summary>
        </member>
        <member name="F:EdgeGateway.Device.Entity.EventHealthStatusEnum.Unknown">
            <summary>
                未知
            </summary>
        </member>
        <member name="F:EdgeGateway.Device.Entity.EventHealthStatusEnum.Disabled">
            <summary>
                已禁用
            </summary>
        </member>
        <member name="F:EdgeGateway.Device.Entity.EventHealthStatusEnum.PerformanceIssue">
            <summary>
                性能问题
            </summary>
        </member>
        <member name="F:EdgeGateway.Device.Entity.EventHealthStatusEnum.SchedulingIssue">
            <summary>
                调度问题
            </summary>
        </member>
        <member name="T:EdgeGateway.Device.Entity.EventMemoryStatusEnum">
            <summary>
                事件内存状态枚举
            </summary>
        </member>
        <member name="F:EdgeGateway.Device.Entity.EventMemoryStatusEnum.Active">
            <summary>
                活跃 - 事件在内存中并正常运行
            </summary>
        </member>
        <member name="F:EdgeGateway.Device.Entity.EventMemoryStatusEnum.Inactive">
            <summary>
                不活跃 - 事件未加载到内存中
            </summary>
        </member>
        <member name="F:EdgeGateway.Device.Entity.EventMemoryStatusEnum.Paused">
            <summary>
                暂停 - 事件在内存中但已暂停
            </summary>
        </member>
        <member name="F:EdgeGateway.Device.Entity.EventMemoryStatusEnum.Error">
            <summary>
                错误状态 - 事件在内存中但处于错误状态
            </summary>
        </member>
        <member name="F:EdgeGateway.Device.Entity.EventMemoryStatusEnum.Initializing">
            <summary>
                正在初始化 - 事件正在加载中
            </summary>
        </member>
        <member name="F:EdgeGateway.Device.Entity.EventMemoryStatusEnum.Unloading">
            <summary>
                正在卸载 - 事件正在卸载中
            </summary>
        </member>
        <member name="F:EdgeGateway.Device.Entity.EventMemoryStatusEnum.Unknown">
            <summary>
                未知 - 无法确定事件状态
            </summary>
        </member>
        <member name="T:EdgeGateway.Device.Entity.EventScheduleTask">
            <summary>
                事件调度任务实体 - 管理定时触发事件的调度状态
            </summary>
        </member>
        <member name="P:EdgeGateway.Device.Entity.EventScheduleTask.DeviceEventId">
            <summary>
                设备事件ID
            </summary>
        </member>
        <member name="P:EdgeGateway.Device.Entity.EventScheduleTask.DeviceId">
            <summary>
                设备ID
            </summary>
        </member>
        <member name="P:EdgeGateway.Device.Entity.EventScheduleTask.TaskName">
            <summary>
                任务名称
            </summary>
        </member>
        <member name="P:EdgeGateway.Device.Entity.EventScheduleTask.TaskIdentifier">
            <summary>
                任务标识符 - 用于在调度系统中唯一标识任务
            </summary>
        </member>
        <member name="P:EdgeGateway.Device.Entity.EventScheduleTask.TaskType">
            <summary>
                任务类型 - 定时触发或条件定时触发
            </summary>
        </member>
        <member name="P:EdgeGateway.Device.Entity.EventScheduleTask.Status">
            <summary>
                任务状态
            </summary>
        </member>
        <member name="P:EdgeGateway.Device.Entity.EventScheduleTask.ExpressionType">
            <summary>
                定时表达式类型
            </summary>
        </member>
        <member name="P:EdgeGateway.Device.Entity.EventScheduleTask.Expression">
            <summary>
                定时表达式
            </summary>
        </member>
        <member name="P:EdgeGateway.Device.Entity.EventScheduleTask.LastExecuteTime">
            <summary>
                上次执行时间
            </summary>
        </member>
        <member name="P:EdgeGateway.Device.Entity.EventScheduleTask.NextExecuteTime">
            <summary>
                下次执行时间
            </summary>
        </member>
        <member name="P:EdgeGateway.Device.Entity.EventScheduleTask.StartTime">
            <summary>
                开始时间
            </summary>
        </member>
        <member name="P:EdgeGateway.Device.Entity.EventScheduleTask.EndTime">
            <summary>
                结束时间
            </summary>
        </member>
        <member name="P:EdgeGateway.Device.Entity.EventScheduleTask.ExecuteCount">
            <summary>
                执行次数
            </summary>
        </member>
        <member name="P:EdgeGateway.Device.Entity.EventScheduleTask.MaxExecuteCount">
            <summary>
                最大执行次数 - 0表示不限制
            </summary>
        </member>
        <member name="P:EdgeGateway.Device.Entity.EventScheduleTask.MissedCount">
            <summary>
                错过执行次数
            </summary>
        </member>
        <member name="P:EdgeGateway.Device.Entity.EventScheduleTask.SuccessCount">
            <summary>
                成功执行次数
            </summary>
        </member>
        <member name="P:EdgeGateway.Device.Entity.EventScheduleTask.FailureCount">
            <summary>
                失败执行次数
            </summary>
        </member>
        <member name="P:EdgeGateway.Device.Entity.EventScheduleTask.LastExecuteResult">
            <summary>
                上次执行结果
            </summary>
        </member>
        <member name="P:EdgeGateway.Device.Entity.EventScheduleTask.LastErrorMessage">
            <summary>
                上次错误信息
            </summary>
        </member>
        <member name="P:EdgeGateway.Device.Entity.EventScheduleTask.MemoryStatus">
            <summary>
                内存状态 - 任务在内存中的状态
            </summary>
        </member>
        <member name="P:EdgeGateway.Device.Entity.EventScheduleTask.TaskConfig">
            <summary>
                任务配置 - JSON存储的任务配置
            </summary>
        </member>
        <member name="P:EdgeGateway.Device.Entity.EventScheduleTask.DeviceEvent">
            <summary>
                关联设备事件
            </summary>
        </member>
        <member name="P:EdgeGateway.Device.Entity.EventScheduleTask.Device">
            <summary>
                关联设备
            </summary>
        </member>
        <member name="P:EdgeGateway.Device.Entity.EventScheduleTask.TaskLogs">
            <summary>
                任务执行日志
            </summary>
        </member>
        <member name="T:EdgeGateway.Device.Entity.TaskMemoryStatus">
            <summary>
                任务内存状态 - 记录任务在内存中的状态信息
            </summary>
        </member>
        <member name="P:EdgeGateway.Device.Entity.TaskMemoryStatus.IsRegistered">
            <summary>
                是否已注册到调度器
            </summary>
        </member>
        <member name="P:EdgeGateway.Device.Entity.TaskMemoryStatus.RegisterTime">
            <summary>
                注册时间
            </summary>
        </member>
        <member name="P:EdgeGateway.Device.Entity.TaskMemoryStatus.SchedulerId">
            <summary>
                调度器ID
            </summary>
        </member>
        <member name="P:EdgeGateway.Device.Entity.TaskMemoryStatus.IsPaused">
            <summary>
                是否已暂停
            </summary>
        </member>
        <member name="P:EdgeGateway.Device.Entity.TaskMemoryStatus.PauseTime">
            <summary>
                暂停时间
            </summary>
        </member>
        <member name="P:EdgeGateway.Device.Entity.TaskMemoryStatus.LastCheckTime">
            <summary>
                最后检查时间
            </summary>
        </member>
        <member name="P:EdgeGateway.Device.Entity.TaskMemoryStatus.ScheduleNode">
            <summary>
                调度节点
            </summary>
        </member>
        <member name="P:EdgeGateway.Device.Entity.TaskMemoryStatus.ThreadId">
            <summary>
                线程ID
            </summary>
        </member>
        <member name="P:EdgeGateway.Device.Entity.TaskMemoryStatus.ProcessorId">
            <summary>
                处理器ID
            </summary>
        </member>
        <member name="P:EdgeGateway.Device.Entity.TaskMemoryStatus.MemoryUsage">
            <summary>
                内存使用(KB)
            </summary>
        </member>
        <member name="T:EdgeGateway.Device.Entity.EventTaskLog">
            <summary>
                事件任务日志 - 记录任务执行日志
            </summary>
        </member>
        <member name="P:EdgeGateway.Device.Entity.EventTaskLog.TaskId">
            <summary>
                任务ID
            </summary>
        </member>
        <member name="P:EdgeGateway.Device.Entity.EventTaskLog.DeviceEventId">
            <summary>
                设备事件ID
            </summary>
        </member>
        <member name="P:EdgeGateway.Device.Entity.EventTaskLog.ExecuteTime">
            <summary>
                执行时间
            </summary>
        </member>
        <member name="P:EdgeGateway.Device.Entity.EventTaskLog.IsSuccess">
            <summary>
                执行结果
            </summary>
        </member>
        <member name="P:EdgeGateway.Device.Entity.EventTaskLog.ResultMessage">
            <summary>
                执行结果消息
            </summary>
        </member>
        <member name="P:EdgeGateway.Device.Entity.EventTaskLog.ExecutionTime">
            <summary>
                执行耗时(毫秒)
            </summary>
        </member>
        <member name="P:EdgeGateway.Device.Entity.EventTaskLog.ErrorMessage">
            <summary>
                错误信息
            </summary>
        </member>
        <member name="P:EdgeGateway.Device.Entity.EventTaskLog.StackTrace">
            <summary>
                堆栈跟踪
            </summary>
        </member>
        <member name="P:EdgeGateway.Device.Entity.EventTaskLog.ExecutionNode">
            <summary>
                执行节点
            </summary>
        </member>
        <member name="P:EdgeGateway.Device.Entity.EventTaskLog.DetailData">
            <summary>
                详细数据 - JSON格式存储的详细执行数据
            </summary>
        </member>
        <member name="P:EdgeGateway.Device.Entity.EventTaskLog.Task">
            <summary>
                关联调度任务
            </summary>
        </member>
        <member name="T:EdgeGateway.Device.Entity.ScheduleTaskStatusEnum">
            <summary>
                调度任务状态枚举
            </summary>
        </member>
        <member name="F:EdgeGateway.Device.Entity.ScheduleTaskStatusEnum.Created">
            <summary>
                已创建
            </summary>
        </member>
        <member name="F:EdgeGateway.Device.Entity.ScheduleTaskStatusEnum.Scheduled">
            <summary>
                已调度
            </summary>
        </member>
        <member name="F:EdgeGateway.Device.Entity.ScheduleTaskStatusEnum.Running">
            <summary>
                运行中
            </summary>
        </member>
        <member name="F:EdgeGateway.Device.Entity.ScheduleTaskStatusEnum.Paused">
            <summary>
                已暂停
            </summary>
        </member>
        <member name="F:EdgeGateway.Device.Entity.ScheduleTaskStatusEnum.Completed">
            <summary>
                已完成
            </summary>
        </member>
        <member name="F:EdgeGateway.Device.Entity.ScheduleTaskStatusEnum.Canceled">
            <summary>
                已取消
            </summary>
        </member>
        <member name="F:EdgeGateway.Device.Entity.ScheduleTaskStatusEnum.Error">
            <summary>
                出错
            </summary>
        </member>
        <member name="F:EdgeGateway.Device.Entity.ScheduleTaskStatusEnum.Expired">
            <summary>
                已过期
            </summary>
        </member>
        <member name="T:EdgeGateway.Device.Entity.DataReportTypeEnum">
            <summary>
            数据上报方式
            </summary>
        </member>
        <member name="F:EdgeGateway.Device.Entity.DataReportTypeEnum.Always">
            <summary>
            始终上报 - 设备所有数据正常采集并上报
            </summary>
        </member>
        <member name="F:EdgeGateway.Device.Entity.DataReportTypeEnum.OnValueChanged">
            <summary>
            数据变化时上报 - 设备中任意点位发生变化时才上报该设备所有数据
            </summary>
        </member>
        <member name="F:EdgeGateway.Device.Entity.DataReportTypeEnum.Never">
            <summary>
            从不上报 - 设备正常采集但不上报数据（仅本地存储）
            </summary>
        </member>
        <member name="F:EdgeGateway.Device.Entity.DataReportTypeEnum.OnDemand">
            <summary>
            按需上报
            </summary>
        </member>
        <member name="F:EdgeGateway.Device.Entity.DataReportTypeEnum.OnDemandWithPowerOff">
            <summary>
            按需上报（设备关机依然上报）
            </summary>
        </member>
        <member name="T:EdgeGateway.Device.Entity.TriggerEventTypeEnum">
            <summary>
            触发事件类型枚举
            </summary>
        </member>
        <member name="F:EdgeGateway.Device.Entity.TriggerEventTypeEnum.属性触发">
            <summary>
            属性触发 - 根据设备属性值的变化触发事件
            </summary>
        </member>
        <member name="F:EdgeGateway.Device.Entity.TriggerEventTypeEnum.状态触发">
            <summary>
            状态触发 - 根据设备连接状态的变化触发事件
            </summary>
        </member>
        <member name="F:EdgeGateway.Device.Entity.TriggerEventTypeEnum.混合触发">
            <summary>
            混合触发 - 同时满足多种条件时触发事件
            </summary>
        </member>
        <member name="F:EdgeGateway.Device.Entity.TriggerEventTypeEnum.定时触发">
            <summary>
            定时触发 - 根据设定的时间规则定期触发事件
            </summary>
        </member>
        <member name="F:EdgeGateway.Device.Entity.TriggerEventTypeEnum.手动触发">
            <summary>
            手动触发 - 由用户或外部系统手动触发的事件
            </summary>
        </member>
        <member name="F:EdgeGateway.Device.Entity.TriggerEventTypeEnum.条件定时触发">
            <summary>
            条件定时触发 - 满足条件后按照时间规则触发
            </summary>
        </member>
        <member name="F:EdgeGateway.Device.Entity.TriggerEventTypeEnum.DataChange">
            <summary>
            数据变更触发 - 根据数据变更触发事件
            </summary>
        </member>
        <member name="T:EdgeGateway.Device.Entity.Models.Driver">
            <summary>
                驱动信息
            </summary>
        </member>
        <member name="P:EdgeGateway.Device.Entity.Models.Driver.DriverName">
            <summary>
                协议名称
            </summary>
        </member>
        <member name="P:EdgeGateway.Device.Entity.Models.Driver.FileName">
            <summary>
                驱动文件
            </summary>
        </member>
        <member name="P:EdgeGateway.Device.Entity.Models.Driver.Type">
            <summary>
            </summary>
        </member>
        <member name="P:EdgeGateway.Device.Entity.Models.Driver.Version">
            <summary>
                协议版本
            </summary>
        </member>
        <member name="P:EdgeGateway.Device.Entity.Models.Driver.Manufacturer">
            <summary>
                驱动厂商
            </summary>
        </member>
        <member name="P:EdgeGateway.Device.Entity.Models.Driver.DriverType">
            <summary>
                驱动类型：PLC；CNC
            </summary>
        </member>
        <member name="P:EdgeGateway.Device.Entity.Models.Driver.CreateVariables">
            <summary>
                生成标签
            </summary>
        </member>
        <member name="P:EdgeGateway.Device.Entity.Models.Driver.IsNet">
            <summary>
                是否为网口设备
            </summary>
        </member>
        <member name="P:EdgeGateway.Device.Entity.Models.Driver.Description">
            <summary>
                协议描述
            </summary>
        </member>
        <member name="P:EdgeGateway.Device.Entity.Models.Driver.ConnectionConfig">
            <summary>
                连接配置，ip,port...
            </summary>
        </member>
        <member name="P:EdgeGateway.Device.Entity.Models.Driver.PropertyConfiguration">
            <summary>
                属性配置,动作,读写，地址,数据类型...
            </summary>
        </member>
        <member name="T:EdgeGateway.Device.Entity.Models.DriverConfigs">
            <summary>
                协议配置
            </summary>
        </member>
        <member name="P:EdgeGateway.Device.Entity.Models.DriverConfigs.ConfigName">
            <summary>
                配置项名称
            </summary>
        </member>
        <member name="P:EdgeGateway.Device.Entity.Models.DriverConfigs.Identifier">
            <summary>
                标识
            </summary>
        </member>
        <member name="P:EdgeGateway.Device.Entity.Models.DriverConfigs.Value">
            <summary>
                值
            </summary>
        </member>
        <member name="P:EdgeGateway.Device.Entity.Models.DriverConfigs.Description">
            <summary>
                描述
            </summary>
        </member>
        <member name="P:EdgeGateway.Device.Entity.Models.DriverConfigs.EnumInfo">
            <summary>
                枚举扩展
            </summary>
        </member>
        <member name="P:EdgeGateway.Device.Entity.Models.DriverConfigs.ConfigGroupName">
            <summary>
                配置项分组
            </summary>
        </member>
        <member name="P:EdgeGateway.Device.Entity.Models.DriverConfigs.Required">
            <summary>
                必填项
            </summary>
        </member>
        <member name="P:EdgeGateway.Device.Entity.Models.DriverConfigs.Display">
            <summary>
                显示
            </summary>
        </member>
        <member name="P:EdgeGateway.Device.Entity.Models.DriverConfigs.DisplayExpress">
            <summary>
                显示条件表达式
            </summary>
        </member>
        <member name="P:EdgeGateway.Device.Entity.Models.DriverConfigs.Order">
            <summary>
                排序,越小越在前
            </summary>
        </member>
        <member name="P:EdgeGateway.Device.Entity.Models.DriverConfigs.Type">
            <summary>
                输入框类型
            </summary>
        </member>
        <member name="T:EdgeGateway.Device.Entity.Models.Events.DeviceEventData">
            <summary>
            设备事件数据，用于传递设备数据并触发事件判断
            </summary>
        </member>
        <member name="P:EdgeGateway.Device.Entity.Models.Events.DeviceEventData.DeviceId">
            <summary>
            设备ID
            </summary>
        </member>
        <member name="P:EdgeGateway.Device.Entity.Models.Events.DeviceEventData.DeviceIdentifier">
            <summary>
            设备标识符
            </summary>
        </member>
        <member name="P:EdgeGateway.Device.Entity.Models.Events.DeviceEventData.Timestamp">
            <summary>
            事件发生时间戳
            </summary>
        </member>
        <member name="P:EdgeGateway.Device.Entity.Models.Events.DeviceEventData.TagValues">
            <summary>
            标签数据集合，键为标签标识符，值为标签值
            </summary>
        </member>
        <member name="P:EdgeGateway.Device.Entity.Models.Events.DeviceEventData.DeviceStatus">
            <summary>
            设备状态
            </summary>
        </member>
        <member name="M:EdgeGateway.Device.Entity.Models.Events.DeviceEventData.#ctor(System.Int64,System.String,System.Int64,System.Boolean)">
            <summary>
            创建一个新的设备事件数据实例
            </summary>
            <param name="deviceId">设备ID</param>
            <param name="deviceIdentifier">设备标识符</param>
            <param name="timestamp">时间戳</param>
            <param name="deviceStatus">设备状态</param>
        </member>
        <member name="M:EdgeGateway.Device.Entity.Models.Events.DeviceEventData.AddTagValue(System.String,System.String,System.Object,System.String)">
            <summary>
            添加标签数据
            </summary>
            <param name="tagIdentifier">标签标识符</param>
            <param name="tagName">标签名称</param>
            <param name="value">标签值</param>
            <param name="dataType">数据类型</param>
        </member>
        <member name="T:EdgeGateway.Device.Entity.Models.Events.TagData">
            <summary>
            标签数据
            </summary>
        </member>
        <member name="P:EdgeGateway.Device.Entity.Models.Events.TagData.Identifier">
            <summary>
            标签标识符
            </summary>
        </member>
        <member name="P:EdgeGateway.Device.Entity.Models.Events.TagData.Name">
            <summary>
            标签名称
            </summary>
        </member>
        <member name="P:EdgeGateway.Device.Entity.Models.Events.TagData.Value">
            <summary>
            标签值
            </summary>
        </member>
        <member name="P:EdgeGateway.Device.Entity.Models.Events.TagData.DataType">
            <summary>
            数据类型
            </summary>
        </member>
        <member name="P:EdgeGateway.Device.Entity.Models.Events.TagData.Timestamp">
            <summary>
            时间戳
            </summary>
        </member>
        <member name="T:EdgeGateway.Device.Entity.Models.Events.DeviceEventInfo">
            <summary>
            设备事件信息，包含基本事件数据和部分健康状态
            </summary>
        </member>
        <member name="P:EdgeGateway.Device.Entity.Models.Events.DeviceEventInfo.Id">
            <summary>
            事件ID
            </summary>
        </member>
        <member name="P:EdgeGateway.Device.Entity.Models.Events.DeviceEventInfo.DeviceId">
            <summary>
            设备ID
            </summary>
        </member>
        <member name="P:EdgeGateway.Device.Entity.Models.Events.DeviceEventInfo.Name">
            <summary>
            事件名称
            </summary>
        </member>
        <member name="P:EdgeGateway.Device.Entity.Models.Events.DeviceEventInfo.Enabled">
            <summary>
            是否启用
            </summary>
        </member>
        <member name="P:EdgeGateway.Device.Entity.Models.Events.DeviceEventInfo.LastTriggerTime">
            <summary>
            最后触发时间
            </summary>
        </member>
        <member name="P:EdgeGateway.Device.Entity.Models.Events.DeviceEventInfo.TriggerCount">
            <summary>
            触发次数
            </summary>
        </member>
        <member name="P:EdgeGateway.Device.Entity.Models.Events.DeviceEventInfo.CreatedTime">
            <summary>
            创建时间
            </summary>
        </member>
        <member name="P:EdgeGateway.Device.Entity.Models.Events.DeviceEventInfo.UpdatedTime">
            <summary>
            更新时间
            </summary>
        </member>
        <member name="P:EdgeGateway.Device.Entity.Models.Events.DeviceEventInfo.HealthStatus">
            <summary>
            健康状态
            </summary>
        </member>
        <member name="P:EdgeGateway.Device.Entity.Models.Events.DeviceEventInfo.MemoryStatus">
            <summary>
            内存状态
            </summary>
        </member>
        <member name="T:EdgeGateway.Device.Entity.Models.Events.EventCondition">
            <summary>
            事件条件模型，用于定义触发事件的条件
            </summary>
        </member>
        <member name="P:EdgeGateway.Device.Entity.Models.Events.EventCondition.Id">
            <summary>
            事件条件ID
            </summary>
        </member>
        <member name="P:EdgeGateway.Device.Entity.Models.Events.EventCondition.Name">
            <summary>
            事件条件名称
            </summary>
        </member>
        <member name="P:EdgeGateway.Device.Entity.Models.Events.EventCondition.ConditionType">
            <summary>
            事件条件类型
            </summary>
        </member>
        <member name="P:EdgeGateway.Device.Entity.Models.Events.EventCondition.TagIdentifier">
            <summary>
            标签标识符，适用于TagValue类型条件
            </summary>
        </member>
        <member name="P:EdgeGateway.Device.Entity.Models.Events.EventCondition.Operator">
            <summary>
            比较运算符
            </summary>
        </member>
        <member name="P:EdgeGateway.Device.Entity.Models.Events.EventCondition.ComparisonValue">
            <summary>
            比较值
            </summary>
        </member>
        <member name="P:EdgeGateway.Device.Entity.Models.Events.EventCondition.DataType">
            <summary>
            数据类型
            </summary>
        </member>
        <member name="P:EdgeGateway.Device.Entity.Models.Events.EventCondition.EnableDebouce">
            <summary>
            是否启用防抖
            </summary>
        </member>
        <member name="P:EdgeGateway.Device.Entity.Models.Events.EventCondition.DebouceTime">
            <summary>
            防抖时间(毫秒)
            </summary>
        </member>
        <member name="P:EdgeGateway.Device.Entity.Models.Events.EventCondition.LastTriggerTime">
            <summary>
            上次触发时间
            </summary>
        </member>
        <member name="P:EdgeGateway.Device.Entity.Models.Events.EventCondition.SubConditions">
            <summary>
            子条件列表，用于复合条件
            </summary>
        </member>
        <member name="P:EdgeGateway.Device.Entity.Models.Events.EventCondition.SubConditionRelation">
            <summary>
            子条件关系类型
            </summary>
        </member>
        <member name="M:EdgeGateway.Device.Entity.Models.Events.EventCondition.Evaluate(EdgeGateway.Device.Entity.Models.Events.DeviceEventData)">
            <summary>
            验证条件是否满足
            </summary>
            <param name="eventData">设备事件数据</param>
            <returns>是否满足条件</returns>
        </member>
        <member name="M:EdgeGateway.Device.Entity.Models.Events.EventCondition.EvaluateDeviceStatus(EdgeGateway.Device.Entity.Models.Events.DeviceEventData)">
            <summary>
            评估设备状态条件
            </summary>
        </member>
        <member name="M:EdgeGateway.Device.Entity.Models.Events.EventCondition.EvaluateTagValue(EdgeGateway.Device.Entity.Models.Events.DeviceEventData)">
            <summary>
            评估标签值条件
            </summary>
        </member>
        <member name="M:EdgeGateway.Device.Entity.Models.Events.EventCondition.EvaluateCompound(EdgeGateway.Device.Entity.Models.Events.DeviceEventData)">
            <summary>
            评估复合条件
            </summary>
        </member>
        <member name="M:EdgeGateway.Device.Entity.Models.Events.EventCondition.CompareValues``1(``0,``0)">
            <summary>
            比较两个可比较值
            </summary>
        </member>
        <member name="M:EdgeGateway.Device.Entity.Models.Events.EventCondition.CompareStringValues(System.String,System.String)">
            <summary>
            比较两个字符串值
            </summary>
        </member>
        <member name="M:EdgeGateway.Device.Entity.Models.Events.EventCondition.CompareBoolValues(System.Boolean,System.Boolean)">
            <summary>
            比较布尔值
            </summary>
        </member>
        <member name="T:EdgeGateway.Device.Entity.Models.Events.EventConditionType">
            <summary>
            事件条件类型
            </summary>
        </member>
        <member name="F:EdgeGateway.Device.Entity.Models.Events.EventConditionType.DeviceStatus">
            <summary>
            设备状态
            </summary>
        </member>
        <member name="F:EdgeGateway.Device.Entity.Models.Events.EventConditionType.TagValue">
            <summary>
            标签值
            </summary>
        </member>
        <member name="F:EdgeGateway.Device.Entity.Models.Events.EventConditionType.Compound">
            <summary>
            复合条件
            </summary>
        </member>
        <member name="T:EdgeGateway.Device.Entity.Models.Events.ComparisonOperator">
            <summary>
            比较运算符
            </summary>
        </member>
        <member name="F:EdgeGateway.Device.Entity.Models.Events.ComparisonOperator.Equal">
            <summary>
            等于
            </summary>
        </member>
        <member name="F:EdgeGateway.Device.Entity.Models.Events.ComparisonOperator.NotEqual">
            <summary>
            不等于
            </summary>
        </member>
        <member name="F:EdgeGateway.Device.Entity.Models.Events.ComparisonOperator.GreaterThan">
            <summary>
            大于
            </summary>
        </member>
        <member name="F:EdgeGateway.Device.Entity.Models.Events.ComparisonOperator.GreaterThanOrEqual">
            <summary>
            大于等于
            </summary>
        </member>
        <member name="F:EdgeGateway.Device.Entity.Models.Events.ComparisonOperator.LessThan">
            <summary>
            小于
            </summary>
        </member>
        <member name="F:EdgeGateway.Device.Entity.Models.Events.ComparisonOperator.LessThanOrEqual">
            <summary>
            小于等于
            </summary>
        </member>
        <member name="F:EdgeGateway.Device.Entity.Models.Events.ComparisonOperator.Contains">
            <summary>
            包含
            </summary>
        </member>
        <member name="F:EdgeGateway.Device.Entity.Models.Events.ComparisonOperator.NotContains">
            <summary>
            不包含
            </summary>
        </member>
        <member name="F:EdgeGateway.Device.Entity.Models.Events.ComparisonOperator.StartsWith">
            <summary>
            开头是
            </summary>
        </member>
        <member name="F:EdgeGateway.Device.Entity.Models.Events.ComparisonOperator.EndsWith">
            <summary>
            结尾是
            </summary>
        </member>
        <member name="T:EdgeGateway.Device.Entity.Models.Events.LogicalOperator">
            <summary>
            逻辑运算符
            </summary>
        </member>
        <member name="F:EdgeGateway.Device.Entity.Models.Events.LogicalOperator.And">
            <summary>
            与
            </summary>
        </member>
        <member name="F:EdgeGateway.Device.Entity.Models.Events.LogicalOperator.Or">
            <summary>
            或
            </summary>
        </member>
        <member name="T:EdgeGateway.Device.Entity.Models.Events.EventDetailInfo">
            <summary>
                事件详细信息，用于缓存完整的事件数据
            </summary>
        </member>
        <member name="P:EdgeGateway.Device.Entity.Models.Events.EventDetailInfo.Event">
            <summary>
                基本事件信息
            </summary>
        </member>
        <member name="P:EdgeGateway.Device.Entity.Models.Events.EventDetailInfo.HealthStatus">
            <summary>
                事件健康状态
            </summary>
        </member>
        <member name="P:EdgeGateway.Device.Entity.Models.Events.EventDetailInfo.Triggers">
            <summary>
                事件中使用的所有触发条件
            </summary>
        </member>
        <member name="P:EdgeGateway.Device.Entity.Models.Events.EventDetailInfo.Actions">
            <summary>
                事件中使用的所有动作
            </summary>
        </member>
        <member name="P:EdgeGateway.Device.Entity.Models.Events.EventDetailInfo.ScheduleTasks">
            <summary>
                事件关联的调度任务
            </summary>
        </member>
        <member name="M:EdgeGateway.Device.Entity.Models.Events.EventDetailInfo.#ctor">
            <summary>
                构造函数
            </summary>
        </member>
        <member name="T:EdgeGateway.Device.Entity.Models.Events.CreateEventRequest">
            <summary>
            创建设备事件请求模型
            </summary>
        </member>
        <member name="P:EdgeGateway.Device.Entity.Models.Events.CreateEventRequest.DeviceId">
            <summary>
            设备ID
            </summary>
        </member>
        <member name="P:EdgeGateway.Device.Entity.Models.Events.CreateEventRequest.EventName">
            <summary>
            事件名称
            </summary>
        </member>
        <member name="P:EdgeGateway.Device.Entity.Models.Events.CreateEventRequest.Description">
            <summary>
            事件描述
            </summary>
        </member>
        <member name="P:EdgeGateway.Device.Entity.Models.Events.CreateEventRequest.Condition">
            <summary>
            事件条件
            </summary>
        </member>
        <member name="P:EdgeGateway.Device.Entity.Models.Events.CreateEventRequest.TriggerEventType">
            <summary>
            触发事件类型
            </summary>
        </member>
        <member name="P:EdgeGateway.Device.Entity.Models.Events.CreateEventRequest.TimerConfig">
            <summary>
            定时触发配置 - 当TriggerEventType为定时触发或条件定时触发时使用
            </summary>
        </member>
        <member name="P:EdgeGateway.Device.Entity.Models.Events.CreateEventRequest.Actions">
            <summary>
            事件动作列表
            </summary>
        </member>
        <member name="P:EdgeGateway.Device.Entity.Models.Events.CreateEventRequest.Priority">
            <summary>
            事件优先级
            </summary>
        </member>
        <member name="P:EdgeGateway.Device.Entity.Models.Events.CreateEventRequest.EventGroup">
            <summary>
            事件分组
            </summary>
        </member>
        <member name="T:EdgeGateway.Device.Entity.Models.Events.UpdateEventRequest">
            <summary>
            更新设备事件请求模型
            </summary>
        </member>
        <member name="P:EdgeGateway.Device.Entity.Models.Events.UpdateEventRequest.EventName">
            <summary>
            事件名称
            </summary>
        </member>
        <member name="P:EdgeGateway.Device.Entity.Models.Events.UpdateEventRequest.Description">
            <summary>
            事件描述
            </summary>
        </member>
        <member name="P:EdgeGateway.Device.Entity.Models.Events.UpdateEventRequest.Condition">
            <summary>
            事件条件
            </summary>
        </member>
        <member name="P:EdgeGateway.Device.Entity.Models.Events.UpdateEventRequest.TriggerEventType">
            <summary>
            触发事件类型
            </summary>
        </member>
        <member name="P:EdgeGateway.Device.Entity.Models.Events.UpdateEventRequest.TimerConfig">
            <summary>
            定时触发配置
            </summary>
        </member>
        <member name="P:EdgeGateway.Device.Entity.Models.Events.UpdateEventRequest.Actions">
            <summary>
            事件动作列表
            </summary>
        </member>
        <member name="P:EdgeGateway.Device.Entity.Models.Events.UpdateEventRequest.Priority">
            <summary>
            事件优先级
            </summary>
        </member>
        <member name="P:EdgeGateway.Device.Entity.Models.Events.UpdateEventRequest.EventGroup">
            <summary>
            事件分组
            </summary>
        </member>
        <member name="T:EdgeGateway.Device.Entity.Models.Events.EventResponse">
            <summary>
            设备事件基本信息响应模型
            </summary>
        </member>
        <member name="P:EdgeGateway.Device.Entity.Models.Events.EventResponse.Id">
            <summary>
            事件ID
            </summary>
        </member>
        <member name="P:EdgeGateway.Device.Entity.Models.Events.EventResponse.DeviceId">
            <summary>
            设备ID
            </summary>
        </member>
        <member name="P:EdgeGateway.Device.Entity.Models.Events.EventResponse.DeviceIdentifier">
            <summary>
            设备标识符
            </summary>
        </member>
        <member name="P:EdgeGateway.Device.Entity.Models.Events.EventResponse.EventName">
            <summary>
            事件名称
            </summary>
        </member>
        <member name="P:EdgeGateway.Device.Entity.Models.Events.EventResponse.Description">
            <summary>
            事件描述
            </summary>
        </member>
        <member name="P:EdgeGateway.Device.Entity.Models.Events.EventResponse.Enabled">
            <summary>
            是否启用
            </summary>
        </member>
        <member name="P:EdgeGateway.Device.Entity.Models.Events.EventResponse.TriggerEventType">
            <summary>
            触发事件类型
            </summary>
        </member>
        <member name="P:EdgeGateway.Device.Entity.Models.Events.EventResponse.Priority">
            <summary>
            事件优先级
            </summary>
        </member>
        <member name="P:EdgeGateway.Device.Entity.Models.Events.EventResponse.EventGroup">
            <summary>
            事件分组
            </summary>
        </member>
        <member name="P:EdgeGateway.Device.Entity.Models.Events.EventResponse.LastTriggerTime">
            <summary>
            上次触发时间
            </summary>
        </member>
        <member name="P:EdgeGateway.Device.Entity.Models.Events.EventResponse.TriggerCount">
            <summary>
            触发次数
            </summary>
        </member>
        <member name="P:EdgeGateway.Device.Entity.Models.Events.EventResponse.CreateTime">
            <summary>
            创建时间
            </summary>
        </member>
        <member name="P:EdgeGateway.Device.Entity.Models.Events.EventResponse.UpdateTime">
            <summary>
            更新时间
            </summary>
        </member>
        <member name="P:EdgeGateway.Device.Entity.Models.Events.EventResponse.HealthStatus">
            <summary>
            健康状态
            </summary>
        </member>
        <member name="T:EdgeGateway.Device.Entity.Models.Events.EventDetailResponse">
            <summary>
            设备事件详情响应模型
            </summary>
        </member>
        <member name="P:EdgeGateway.Device.Entity.Models.Events.EventDetailResponse.Condition">
            <summary>
            事件条件
            </summary>
        </member>
        <member name="P:EdgeGateway.Device.Entity.Models.Events.EventDetailResponse.TimerConfig">
            <summary>
            定时触发配置
            </summary>
        </member>
        <member name="P:EdgeGateway.Device.Entity.Models.Events.EventDetailResponse.Actions">
            <summary>
            事件动作列表
            </summary>
        </member>
        <member name="P:EdgeGateway.Device.Entity.Models.Events.EventDetailResponse.StatusBufferTime">
            <summary>
            状态缓冲时间(毫秒)
            </summary>
        </member>
        <member name="P:EdgeGateway.Device.Entity.Models.Events.EventDetailResponse.SaveTriggerHistory">
            <summary>
            是否保存触发历史
            </summary>
        </member>
        <member name="P:EdgeGateway.Device.Entity.Models.Events.EventDetailResponse.HistoryRetentionDays">
            <summary>
            历史记录保留天数
            </summary>
        </member>
        <member name="T:EdgeGateway.Device.Entity.Models.Events.EventHealthStatusResponse">
            <summary>
            事件健康状态响应模型
            </summary>
        </member>
        <member name="P:EdgeGateway.Device.Entity.Models.Events.EventHealthStatusResponse.EventId">
            <summary>
            事件ID
            </summary>
        </member>
        <member name="P:EdgeGateway.Device.Entity.Models.Events.EventHealthStatusResponse.DeviceId">
            <summary>
            设备ID
            </summary>
        </member>
        <member name="P:EdgeGateway.Device.Entity.Models.Events.EventHealthStatusResponse.EventName">
            <summary>
            事件名称
            </summary>
        </member>
        <member name="P:EdgeGateway.Device.Entity.Models.Events.EventHealthStatusResponse.Status">
            <summary>
            健康状态
            </summary>
        </member>
        <member name="P:EdgeGateway.Device.Entity.Models.Events.EventHealthStatusResponse.LastStatusChangeTime">
            <summary>
            最后一次状态变更时间
            </summary>
        </member>
        <member name="P:EdgeGateway.Device.Entity.Models.Events.EventHealthStatusResponse.LastTriggerTime">
            <summary>
            最后一次触发时间
            </summary>
        </member>
        <member name="P:EdgeGateway.Device.Entity.Models.Events.EventHealthStatusResponse.TriggerCount">
            <summary>
            触发计数
            </summary>
        </member>
        <member name="P:EdgeGateway.Device.Entity.Models.Events.EventHealthStatusResponse.FailureCount">
            <summary>
            失败计数
            </summary>
        </member>
        <member name="P:EdgeGateway.Device.Entity.Models.Events.EventHealthStatusResponse.LastSuccessRate">
            <summary>
            上次成功率(%)
            </summary>
        </member>
        <member name="P:EdgeGateway.Device.Entity.Models.Events.EventHealthStatusResponse.AvgExecutionTime">
            <summary>
            平均执行时间(毫秒)
            </summary>
        </member>
        <member name="P:EdgeGateway.Device.Entity.Models.Events.EventHealthStatusResponse.MaxExecutionTime">
            <summary>
            最大执行时间(毫秒)
            </summary>
        </member>
        <member name="P:EdgeGateway.Device.Entity.Models.Events.EventHealthStatusResponse.MemoryStatus">
            <summary>
            内存中状态
            </summary>
        </member>
        <member name="P:EdgeGateway.Device.Entity.Models.Events.EventHealthStatusResponse.PerformanceHistory">
            <summary>
            性能历史记录
            </summary>
        </member>
        <member name="T:EdgeGateway.Device.Entity.Models.Events.EventScheduleTaskResponse">
            <summary>
            事件调度任务响应模型
            </summary>
        </member>
        <member name="P:EdgeGateway.Device.Entity.Models.Events.EventScheduleTaskResponse.Id">
            <summary>
            任务ID
            </summary>
        </member>
        <member name="P:EdgeGateway.Device.Entity.Models.Events.EventScheduleTaskResponse.DeviceEventId">
            <summary>
            设备事件ID
            </summary>
        </member>
        <member name="P:EdgeGateway.Device.Entity.Models.Events.EventScheduleTaskResponse.DeviceId">
            <summary>
            设备ID
            </summary>
        </member>
        <member name="P:EdgeGateway.Device.Entity.Models.Events.EventScheduleTaskResponse.TaskName">
            <summary>
            任务名称
            </summary>
        </member>
        <member name="P:EdgeGateway.Device.Entity.Models.Events.EventScheduleTaskResponse.TaskIdentifier">
            <summary>
            任务标识符
            </summary>
        </member>
        <member name="P:EdgeGateway.Device.Entity.Models.Events.EventScheduleTaskResponse.TaskType">
            <summary>
            任务类型
            </summary>
        </member>
        <member name="P:EdgeGateway.Device.Entity.Models.Events.EventScheduleTaskResponse.Status">
            <summary>
            任务状态
            </summary>
        </member>
        <member name="P:EdgeGateway.Device.Entity.Models.Events.EventScheduleTaskResponse.ExpressionType">
            <summary>
            定时表达式类型
            </summary>
        </member>
        <member name="P:EdgeGateway.Device.Entity.Models.Events.EventScheduleTaskResponse.Expression">
            <summary>
            定时表达式
            </summary>
        </member>
        <member name="P:EdgeGateway.Device.Entity.Models.Events.EventScheduleTaskResponse.LastExecuteTime">
            <summary>
            上次执行时间
            </summary>
        </member>
        <member name="P:EdgeGateway.Device.Entity.Models.Events.EventScheduleTaskResponse.NextExecuteTime">
            <summary>
            下次执行时间
            </summary>
        </member>
        <member name="P:EdgeGateway.Device.Entity.Models.Events.EventScheduleTaskResponse.StartTime">
            <summary>
            开始时间
            </summary>
        </member>
        <member name="P:EdgeGateway.Device.Entity.Models.Events.EventScheduleTaskResponse.EndTime">
            <summary>
            结束时间
            </summary>
        </member>
        <member name="P:EdgeGateway.Device.Entity.Models.Events.EventScheduleTaskResponse.ExecuteCount">
            <summary>
            执行次数
            </summary>
        </member>
        <member name="P:EdgeGateway.Device.Entity.Models.Events.EventScheduleTaskResponse.MaxExecuteCount">
            <summary>
            最大执行次数
            </summary>
        </member>
        <member name="P:EdgeGateway.Device.Entity.Models.Events.EventScheduleTaskResponse.MissedCount">
            <summary>
            错过执行次数
            </summary>
        </member>
        <member name="P:EdgeGateway.Device.Entity.Models.Events.EventScheduleTaskResponse.SuccessCount">
            <summary>
            成功执行次数
            </summary>
        </member>
        <member name="P:EdgeGateway.Device.Entity.Models.Events.EventScheduleTaskResponse.FailureCount">
            <summary>
            失败执行次数
            </summary>
        </member>
        <member name="P:EdgeGateway.Device.Entity.Models.Events.EventScheduleTaskResponse.LastExecuteResult">
            <summary>
            上次执行结果
            </summary>
        </member>
        <member name="T:EdgeGateway.Device.Entity.Models.Events.EventDiagnosticInfoResponse">
            <summary>
            事件诊断信息响应模型
            </summary>
        </member>
        <member name="P:EdgeGateway.Device.Entity.Models.Events.EventDiagnosticInfoResponse.Id">
            <summary>
            诊断信息ID
            </summary>
        </member>
        <member name="P:EdgeGateway.Device.Entity.Models.Events.EventDiagnosticInfoResponse.DeviceEventId">
            <summary>
            设备事件ID
            </summary>
        </member>
        <member name="P:EdgeGateway.Device.Entity.Models.Events.EventDiagnosticInfoResponse.DeviceId">
            <summary>
            设备ID
            </summary>
        </member>
        <member name="P:EdgeGateway.Device.Entity.Models.Events.EventDiagnosticInfoResponse.SessionId">
            <summary>
            诊断会话ID
            </summary>
        </member>
        <member name="P:EdgeGateway.Device.Entity.Models.Events.EventDiagnosticInfoResponse.DiagnosticType">
            <summary>
            诊断类型
            </summary>
        </member>
        <member name="P:EdgeGateway.Device.Entity.Models.Events.EventDiagnosticInfoResponse.Level">
            <summary>
            诊断级别
            </summary>
        </member>
        <member name="P:EdgeGateway.Device.Entity.Models.Events.EventDiagnosticInfoResponse.Title">
            <summary>
            诊断标题
            </summary>
        </member>
        <member name="P:EdgeGateway.Device.Entity.Models.Events.EventDiagnosticInfoResponse.Message">
            <summary>
            诊断信息
            </summary>
        </member>
        <member name="P:EdgeGateway.Device.Entity.Models.Events.EventDiagnosticInfoResponse.ConditionDetails">
            <summary>
            条件评估详情
            </summary>
        </member>
        <member name="P:EdgeGateway.Device.Entity.Models.Events.EventDiagnosticInfoResponse.PerformanceMetrics">
            <summary>
            事件处理性能指标
            </summary>
        </member>
        <member name="P:EdgeGateway.Device.Entity.Models.Events.EventDiagnosticInfoResponse.EnvironmentInfo">
            <summary>
            环境信息
            </summary>
        </member>
        <member name="P:EdgeGateway.Device.Entity.Models.Events.EventDiagnosticInfoResponse.DiagnosticCode">
            <summary>
            诊断代码
            </summary>
        </member>
        <member name="P:EdgeGateway.Device.Entity.Models.Events.EventDiagnosticInfoResponse.SuggestedAction">
            <summary>
            建议操作
            </summary>
        </member>
        <member name="P:EdgeGateway.Device.Entity.Models.Events.EventDiagnosticInfoResponse.IsResolved">
            <summary>
            问题是否已解决
            </summary>
        </member>
        <member name="P:EdgeGateway.Device.Entity.Models.Events.EventDiagnosticInfoResponse.ResolvedTime">
            <summary>
            解决时间
            </summary>
        </member>
        <member name="P:EdgeGateway.Device.Entity.Models.Events.EventDiagnosticInfoResponse.CreateTime">
            <summary>
            创建时间
            </summary>
        </member>
        <member name="T:EdgeGateway.Device.Entity.Models.Events.EventTrigger">
            <summary>
            事件触发器，用于定义和管理触发事件的条件和逻辑
            </summary>
        </member>
        <member name="P:EdgeGateway.Device.Entity.Models.Events.EventTrigger.Id">
            <summary>
            触发器ID
            </summary>
        </member>
        <member name="P:EdgeGateway.Device.Entity.Models.Events.EventTrigger.Name">
            <summary>
            触发器名称
            </summary>
        </member>
        <member name="P:EdgeGateway.Device.Entity.Models.Events.EventTrigger.Description">
            <summary>
            触发器描述
            </summary>
        </member>
        <member name="P:EdgeGateway.Device.Entity.Models.Events.EventTrigger.TriggerType">
            <summary>
            触发事件类型
            </summary>
        </member>
        <member name="P:EdgeGateway.Device.Entity.Models.Events.EventTrigger.Condition">
            <summary>
            触发条件
            </summary>
        </member>
        <member name="P:EdgeGateway.Device.Entity.Models.Events.EventTrigger.TimerConfig">
            <summary>
            定时触发配置，当触发类型为定时触发时使用
            </summary>
        </member>
        <member name="P:EdgeGateway.Device.Entity.Models.Events.EventTrigger.Enabled">
            <summary>
            是否启用
            </summary>
        </member>
        <member name="P:EdgeGateway.Device.Entity.Models.Events.EventTrigger.LastTriggerTime">
            <summary>
            上次触发时间
            </summary>
        </member>
        <member name="P:EdgeGateway.Device.Entity.Models.Events.EventTrigger.TriggerCount">
            <summary>
            触发次数
            </summary>
        </member>
        <member name="P:EdgeGateway.Device.Entity.Models.Events.EventTrigger.DebounceTime">
            <summary>
            防抖时间(毫秒)，防止频繁触发
            </summary>
        </member>
        <member name="P:EdgeGateway.Device.Entity.Models.Events.EventTrigger.Order">
            <summary>
            执行顺序
            </summary>
        </member>
        <member name="P:EdgeGateway.Device.Entity.Models.Events.EventTrigger.Actions">
            <summary>
            触发后要执行的动作列表
            </summary>
        </member>
        <member name="P:EdgeGateway.Device.Entity.Models.Events.EventTrigger.LastErrorMessage">
            <summary>
            最后错误信息
            </summary>
        </member>
        <member name="M:EdgeGateway.Device.Entity.Models.Events.EventTrigger.EvaluateCondition(EdgeGateway.Device.Entity.Models.Events.DeviceEventData)">
            <summary>
            评估触发条件
            </summary>
            <param name="eventData">设备事件数据</param>
            <returns>是否满足触发条件</returns>
        </member>
        <member name="M:EdgeGateway.Device.Entity.Models.Events.EventTrigger.EvaluatePropertyCondition(EdgeGateway.Device.Entity.Models.Events.DeviceEventData)">
            <summary>
            评估属性触发条件
            </summary>
            <param name="eventData">设备事件数据</param>
            <returns>是否满足条件</returns>
        </member>
        <member name="M:EdgeGateway.Device.Entity.Models.Events.EventTrigger.EvaluateStatusCondition(EdgeGateway.Device.Entity.Models.Events.DeviceEventData)">
            <summary>
            评估状态触发条件
            </summary>
            <param name="eventData">设备事件数据</param>
            <returns>是否满足条件</returns>
        </member>
        <member name="M:EdgeGateway.Device.Entity.Models.Events.EventTrigger.EvaluateDataChangeCondition(EdgeGateway.Device.Entity.Models.Events.DeviceEventData)">
            <summary>
            评估数据变更触发条件
            </summary>
            <param name="eventData">设备事件数据</param>
            <returns>是否满足条件</returns>
        </member>
        <member name="M:EdgeGateway.Device.Entity.Models.Events.EventTrigger.RecordTrigger">
            <summary>
            记录触发
            </summary>
        </member>
        <member name="M:EdgeGateway.Device.Entity.Models.Events.EventTrigger.CanTrigger">
            <summary>
            检查是否可以触发（考虑防抖）
            </summary>
            <returns>是否可以触发</returns>
        </member>
        <member name="T:EdgeGateway.Device.Entity.Models.Events.TimerTriggerConfig">
            <summary>
            定时触发配置
            </summary>
        </member>
        <member name="P:EdgeGateway.Device.Entity.Models.Events.TimerTriggerConfig.TriggerType">
            <summary>
            触发类型：周期性或Cron表达式
            </summary>
        </member>
        <member name="P:EdgeGateway.Device.Entity.Models.Events.TimerTriggerConfig.Interval">
            <summary>
            触发间隔（秒），适用于周期性触发
            </summary>
        </member>
        <member name="P:EdgeGateway.Device.Entity.Models.Events.TimerTriggerConfig.CronExpression">
            <summary>
            Cron表达式，适用于Cron触发
            </summary>
        </member>
        <member name="P:EdgeGateway.Device.Entity.Models.Events.TimerTriggerConfig.StartTime">
            <summary>
            触发开始时间
            </summary>
        </member>
        <member name="P:EdgeGateway.Device.Entity.Models.Events.TimerTriggerConfig.EndTime">
            <summary>
            触发结束时间
            </summary>
        </member>
        <member name="P:EdgeGateway.Device.Entity.Models.Events.TimerTriggerConfig.MaxTriggerCount">
            <summary>
            最大触发次数，0表示无限制
            </summary>
        </member>
        <member name="T:EdgeGateway.Device.Services.Helpers.DeviceLogMessage">
            <summary>
            设备日志消息
            </summary>
        </member>
        <member name="P:EdgeGateway.Device.Services.Helpers.DeviceLogMessage.Type">
            <summary>
            日志类型
            </summary>
        </member>
        <member name="P:EdgeGateway.Device.Services.Helpers.DeviceLogMessage.DeviceId">
            <summary>
            设备ID
            </summary>
        </member>
        <member name="P:EdgeGateway.Device.Services.Helpers.DeviceLogMessage.Message">
            <summary>
            日志消息
            </summary>
        </member>
        <member name="P:EdgeGateway.Device.Services.Helpers.DeviceLogMessage.Timestamp">
            <summary>
            时间戳
            </summary>
        </member>
        <member name="T:EdgeGateway.Device.Services.Helpers.PointCollectionLogMessage">
            <summary>
            点位采集日志消息
            </summary>
        </member>
        <member name="P:EdgeGateway.Device.Services.Helpers.PointCollectionLogMessage.DeviceId">
            <summary>
            设备ID
            </summary>
        </member>
        <member name="P:EdgeGateway.Device.Services.Helpers.PointCollectionLogMessage.PointIdentifier">
            <summary>
            点位标识
            </summary>
        </member>
        <member name="P:EdgeGateway.Device.Services.Helpers.PointCollectionLogMessage.PointName">
            <summary>
            点位名称
            </summary>
        </member>
        <member name="P:EdgeGateway.Device.Services.Helpers.PointCollectionLogMessage.OriginalValue">
            <summary>
            原始值
            </summary>
        </member>
        <member name="P:EdgeGateway.Device.Services.Helpers.PointCollectionLogMessage.ProcessedValue">
            <summary>
            处理后的值（表达式处理后）
            </summary>
        </member>
        <member name="P:EdgeGateway.Device.Services.Helpers.PointCollectionLogMessage.Status">
            <summary>
            采集状态
            </summary>
        </member>
        <member name="P:EdgeGateway.Device.Services.Helpers.PointCollectionLogMessage.ErrorMessage">
            <summary>
            错误信息
            </summary>
        </member>
        <member name="P:EdgeGateway.Device.Services.Helpers.PointCollectionLogMessage.ElapsedMs">
            <summary>
            采集耗时（毫秒）
            </summary>
        </member>
        <member name="P:EdgeGateway.Device.Services.Helpers.PointCollectionLogMessage.Timestamp">
            <summary>
            时间戳
            </summary>
        </member>
        <member name="P:EdgeGateway.Device.Services.Helpers.PointCollectionLogMessage.HasExpressionProcessing">
            <summary>
            是否有表达式处理
            </summary>
        </member>
        <member name="T:EdgeGateway.Device.Services.Helpers.DeviceLogHelper">
            <summary>
            设备日志帮助类
            </summary>
        </member>
        <member name="M:EdgeGateway.Device.Services.Helpers.DeviceLogHelper.CreateLogMessage(System.String,System.String,System.String)">
            <summary>
            创建设备日志消息
            </summary>
            <param name="deviceId">设备ID</param>
            <param name="type">日志类型</param>
            <param name="message">日志消息</param>
            <returns></returns>
        </member>
        <member name="M:EdgeGateway.Device.Services.Helpers.DeviceLogHelper.CreatePointCollectionLogMessage(System.String,System.String,System.String,System.Object,System.Object,System.String,System.String,System.Int64,System.Boolean)">
            <summary>
            创建点位采集日志消息
            </summary>
            <param name="deviceId">设备ID</param>
            <param name="pointIdentifier">点位标识</param>
            <param name="pointName">点位名称</param>
            <param name="originalValue">原始值</param>
            <param name="processedValue">处理后的值</param>
            <param name="status">采集状态</param>
            <param name="errorMessage">错误信息</param>
            <param name="elapsedMs">采集耗时</param>
            <param name="hasExpressionProcessing">是否有表达式处理</param>
            <returns></returns>
        </member>
        <member name="T:ImportExcelResultDto">
            <summary>
                Excel导入结果
            </summary>
        </member>
        <member name="P:ImportExcelResultDto.DeviceCount">
            <summary>
                导入的设备数量
            </summary>
        </member>
        <member name="P:ImportExcelResultDto.DeviceLabelCount">
            <summary>
                导入的设备标签数量
            </summary>
        </member>
        <member name="P:ImportExcelResultDto.DeviceEventCount">
            <summary>
                导入的设备事件数量
            </summary>
        </member>
        <member name="P:ImportExcelResultDto.ErrorMessages">
            <summary>
                错误消息列表
            </summary>
        </member>
        <member name="T:ImportExcelDeviceDto">
            <summary>
                导入设备Excel
            </summary>
        </member>
        <member name="P:ImportExcelDeviceDto.File">
            <summary>
                文件
            </summary>
        </member>
    </members>
</doc>
