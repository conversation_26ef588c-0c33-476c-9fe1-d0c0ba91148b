namespace EdgeGateway.Device.Entity;

/// <summary>
/// </summary>
[SugarTable("device", "设备表")]
public class Device : EntityBase
{
    /// <summary>
    ///     设备标识符
    /// </summary>
    [SugarColumn(ColumnDescription = "设备标识符", Length = 128)]
    public string Identifier { get; set; }

    /// <summary>
    ///     设备名称
    /// </summary>
    [SugarColumn(ColumnDescription = "设备名称", Length = 128)]
    public string? Name { get; set; }

    /// <summary>
    ///    基本配置
    /// </summary>
    [SugarColumn(ColumnDescription = "基本配置", IsJson = true)]
    public DeviceInfo DeviceInfo { get; set; } = new();

    /// <summary>
    ///     驱动信息
    /// </summary>
    [SugarColumn(ColumnDescription = "驱动信息", IsJson = true)]
    public DeviceDriver Driver { get; set; }

    /// <summary>
    ///     是否启动
    /// </summary>
    [SugarColumn(ColumnDescription = "是否启动")]
    public bool Enable { get; set; }

    /// <summary>
    ///     设备配置
    /// </summary>
    [SugarColumn(IsJson = true)]
    public List<DeviceConfig> DeviceConfigs { get; set; } = new();

    /// <summary>
    ///     分组标签
    /// </summary>
    [SugarColumn(ColumnDescription = "分组标签", IsJson = true)]
    public List<string> GroupTags { get; set; } = new();

    /// <summary>
    ///     设备位置
    /// </summary>
    [SugarColumn(ColumnDescription = "设备位置", Length = 128)]
    public string? Location { get; set; }

    /// <summary>
    ///     排序
    /// </summary>
    [SugarColumn(ColumnDescription = "排序")]
    public int Index { get; set; }

    /// <summary>
    ///     描述
    /// </summary>
    [SugarColumn(ColumnDescription = "描述", ColumnDataType = "longtext,text,clob")]
    public string? Description { get; set; }

    /// <summary>
    /// 通道Id
    /// </summary>
    [SugarColumn(ColumnDescription = "通道Id")]
    public long? ChannelId { get; set; } = 0;

    /// <summary>
    /// 是否允许写入
    /// </summary>
    [SugarColumn(ColumnDescription = "是否允许写入")]
    public bool AllowWrite { get; set; } = true;

    /// <summary>
    /// 设备日志等级
    /// </summary>
    [SugarColumn(ColumnDescription = "设备日志等级")]
    public DeviceLogLevelEnum LogLevel { get; set; } = DeviceLogLevelEnum.Info;

    #region 忽略字段

    /// <summary>
    ///     协议名称
    /// </summary>
    [SugarColumn(IsIgnore = true)]
    public string DriverName => Driver?.DriverName ?? "";

    /// <summary>
    ///     是否连接
    /// </summary>
    [SugarColumn(IsIgnore = true)]
    public bool IsConnect { get; set; }

    /// <summary>
    ///     是否连接
    /// </summary>
    [SugarColumn(IsIgnore = true)]
    public DeviceStatusTypeEnum Status { get; set; }

    // 最后活动时间
    [SugarColumn(IsIgnore = true)]
    public DateTime? LastActiveTime { get; set; }

    #endregion

    #region 关联表

    /// <summary>
    ///     边缘通道
    /// </summary>
    [Navigate(NavigateType.OneToOne, nameof(ChannelId))]
    [SugarColumn(IsIgnore = true)]
    public EdgeChannel? EdgeChannel { get; set; }

    /// <summary>
    ///     设备属性
    /// </summary>
    [Navigate(NavigateType.OneToMany, nameof(Entity.DeviceLabel.DeviceId))]
    [SugarColumn(IsIgnore = true)]
    public List<DeviceLabel> DeviceLabel { get; set; }

    #endregion 关联表
}

/// <summary>
/// 设备基本配置信息
/// </summary>
public class DeviceInfo
{
    /// <summary>
    /// 
    /// </summary>
    /// <param name="minPeriod">轮询时间</param>
    /// <param name="waitTime">恢复就绪等待时间</param>
    /// <param name="reConnTime">恢复时间</param>
    /// <param name="reportType">数据上报方式</param>
    /// <param name="storeHistoryData">是否存储历史数据</param>
    public DeviceInfo(
        int minPeriod = 1000,
        int waitTime = 0,
        int reConnTime = 1000 * 30,
        DataReportTypeEnum reportType = DataReportTypeEnum.Always,
        bool storeHistoryData = true)
    {
        MinPeriod = minPeriod;
        WaitTime = waitTime;
        ReConnTime = reConnTime;
        ReportType = reportType;
        StoreHistoryData = storeHistoryData;
    }

    /// <summary>
    ///   设备轮询的时间周期
    /// </summary>
    public int MinPeriod { get; }

    /// <summary>
    ///     恢复就绪等待时间
    /// </summary>
    public int WaitTime { get; set; }

    /// <summary>
    ///     恢复时间
    /// </summary>
    public int ReConnTime { get; set; }

    /// <summary>
    /// 数据上报方式
    /// </summary>
    public DataReportTypeEnum ReportType { get; set; }

    /// <summary>
    ///     存储历史数据
    /// </summary>
    public bool StoreHistoryData { get; set; }

}

/// <summary>
///     驱动信息
/// </summary>
public class DeviceDriver
{
    /// <summary>
    ///     协议名称
    /// </summary>
    public string DriverName { get; set; }

    /// <summary>
    ///     协议版本
    /// </summary>
    public string Version { get; set; }

    // /// <summary>
    // /// 协议类型
    // /// </summary>
    // public string DriverType { get; set; }
}

/// <summary>
/// 设备连接配置
/// </summary>
public class DeviceConfig
{
    /// <summary>
    ///     标识
    /// </summary>
    public string Identifier { get; set; }

    /// <summary>
    ///     描述
    /// </summary>
    public string? Description { get; set; }

    /// <summary>
    ///     值
    /// </summary>
    public object? Value { get; set; }

    /// <summary>
    ///     备注
    /// </summary>
    public string? EnumInfo { get; set; }

    /// <summary>
    ///     分组名称
    /// </summary>
    public string? GroupName { get; set; } = "连接配置";

    /// <summary>
    ///     是否是必填
    /// </summary>
    public bool IsRequired { get; set; } = true;

    /// <summary>
    ///     是否显示
    /// </summary>
    public bool Display { get; set; }

    /// <summary>
    ///     字段显示条件表达式
    /// </summary>
    public string DisplayExpress { get; set; }

    /// <summary>
    ///     排序,越小越在前
    /// </summary>
    public short Order { get; set; }

    /// <summary>
    ///     输入框类型
    /// </summary>
    public string Type { get; set; } = "text";
}