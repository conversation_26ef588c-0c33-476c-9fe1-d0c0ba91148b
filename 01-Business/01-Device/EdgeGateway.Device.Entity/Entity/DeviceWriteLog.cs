namespace EdgeGateway.Device.Entity
{
  /// <summary>
  /// 设备写入日志
  /// </summary>
  [SugarTable("device_write_log")]
  public class DeviceWriteLog : EntityBaseId
  {
    /// <summary>
    /// 设备ID
    /// </summary>
    [SugarColumn(ColumnDescription = "设备ID")]
    public long DeviceId { get; set; }

    /// <summary>
    /// 标签标识符
    /// </summary>
    [SugarColumn(ColumnDescription = "标签标识符", Length = 64, IsNullable = true)]
    public string LabelIdentifier { get; set; }

    /// <summary>
    /// 写入地址
    /// </summary>
    [SugarColumn(ColumnDescription = "写入地址", Length = 256)]
    public string Address { get; set; }

    /// <summary>
    /// 写入值
    /// </summary>
    [SugarColumn(ColumnDescription = "写入值", Length = 1024)]
    public string Value { get; set; }

    /// <summary>
    /// 数据类型
    /// </summary>
    [SugarColumn(ColumnDescription = "数据类型", Length = 64, IsNullable = true)]
    public string DataType { get; set; }

    /// <summary>
    /// 数据长度
    /// </summary>
    [SugarColumn(ColumnDescription = "数据长度")]
    public ushort Length { get; set; }

    /// <summary>
    /// 编码方式
    /// </summary>
    [SugarColumn(ColumnDescription = "编码方式", Length = 32, IsNullable = true)]
    public string Encoding { get; set; }

    /// <summary>
    /// 写入来源类型
    /// </summary>
    [SugarColumn(ColumnDescription = "写入来源类型")]
    public WriteSourceType SourceType { get; set; }

    /// <summary>
    /// 写入时间
    /// </summary>
    [SugarColumn(ColumnDescription = "写入时间")]
    public DateTime WriteTime { get; set; }

    /// <summary>
    /// 写入状态
    /// </summary>
    [SugarColumn(ColumnDescription = "写入状态")]
    public bool Success { get; set; }

    /// <summary>
    /// 错误信息
    /// </summary>
    [SugarColumn(ColumnDescription = "错误信息", Length = 1024, IsNullable = true)]
    public string ErrorMessage { get; set; }
  }

  /// <summary>
  /// 写入来源类型
  /// </summary>
  public enum WriteSourceType
  {
    /// <summary>
    /// 手动写入
    /// </summary>
    Manual = 0,

    /// <summary>
    /// 远程API写入
    /// </summary>
    RemoteApi = 1,

    /// <summary>
    /// 本地脚本写入
    /// </summary>
    LocalScript = 2,

    /// <summary>
    /// 定时任务写入
    /// </summary>
    ScheduledTask = 3,

    /// <summary>
    /// 事件触发写入
    /// </summary>
    EventTriggered = 4,

    /// <summary>
    /// 其他来源
    /// </summary>
    Other = 99
  }
}