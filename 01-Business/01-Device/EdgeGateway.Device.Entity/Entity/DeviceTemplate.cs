using SqlSugar;

namespace EdgeGateway.Device.Entity;

/// <summary>
/// 设备模板
/// </summary>
[SugarTable("device_template", "设备模板")]
public class DeviceTemplate : EntityBaseId
{
  /// <summary>
  /// 模板名称
  /// </summary>
  [SugarColumn(ColumnDescription = "模板名称", Length = 128)]
  public string Name { get; set; }

  /// <summary>
  /// 模板描述
  /// </summary>
  [SugarColumn(ColumnDescription = "模板描述", Length = 256, IsNullable = true)]
  public string? Description { get; set; }

  /// <summary>
  /// 驱动名称
  /// </summary>
  [SugarColumn(ColumnDescription = "驱动名称", Length = 128)]
  public string DriverName { get; set; }

  /// <summary>
  /// 创建时间
  /// </summary>
  [SugarColumn(ColumnDescription = "创建时间")]
  public DateTime CreateTime { get; set; } = DateTime.Now;

  /// <summary>
  /// 模板下的点位列表
  /// </summary>
  [Navigate(NavigateType.OneToMany, nameof(DeviceTemplateLabel.TemplateId))]
  public List<DeviceTemplateLabel> Labels { get; set; } = new List<DeviceTemplateLabel>();
}