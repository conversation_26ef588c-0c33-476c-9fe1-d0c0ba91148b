namespace EdgeGateway.Device.Entity.Dto.Label;

/// <summary>
/// 标签数据导入参数
/// </summary>
public class VariableDataImportInput
{
  /// <summary>
  /// 设备ID
  /// </summary>
  public long DeviceId { get; set; }

  /// <summary>
  /// 标签列表
  /// </summary>
  public List<DeviceLabel> Labels { get; set; }

  /// <summary>
  /// 导入模式
  /// </summary>
  public ImportModeEnum ImportMode { get; set; }

  /// <summary>
  /// 忽略的字段列表
  /// </summary>
  public List<string> IgnoreFields { get; set; } = new();
}