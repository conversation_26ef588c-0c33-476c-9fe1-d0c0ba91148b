namespace EdgeGateway.Device.Entity.Dto.Device;

/// <summary>
/// 设备诊断请求输入
/// </summary>
public class DeviceDiagnosticInput
{
  /// <summary>
  /// 设备ID
  /// </summary>
  public long DeviceId { get; set; }

  /// <summary>
  /// 是否包含连接诊断
  /// </summary>
  public bool IncludeConnection { get; set; } = true;

  /// <summary>
  /// 是否包含性能诊断
  /// </summary>
  public bool IncludePerformance { get; set; } = true;

  /// <summary>
  /// 是否包含健康诊断
  /// </summary>
  public bool IncludeHealth { get; set; } = true;

  /// <summary>
  /// 是否包含配置诊断
  /// </summary>
  public bool IncludeConfiguration { get; set; } = true;

  /// <summary>
  /// 是否包含数据诊断
  /// </summary>
  public bool IncludeData { get; set; } = true;

  /// <summary>
  /// 是否包含历史诊断数据
  /// </summary>
  public bool IncludeHistory { get; set; } = false;

  /// <summary>
  /// 历史数据查询天数（默认7天）
  /// </summary>
  public int HistoryDays { get; set; } = 7;

  /// <summary>
  /// 历史数据页码
  /// </summary>
  public int HistoryPage { get; set; } = 1;

  /// <summary>
  /// 历史数据页大小
  /// </summary>
  public int HistoryPageSize { get; set; } = 20;
}