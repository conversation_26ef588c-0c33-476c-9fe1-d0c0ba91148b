namespace EdgeGateway.Device.Entity.Dto.Device;

/// <summary>
/// 设备诊断响应
/// </summary>
public class DeviceDiagnosticDto
{
  /// <summary>
  /// 设备ID
  /// </summary>
  public long DeviceId { get; set; }

  /// <summary>
  /// 设备标识符
  /// </summary>
  public string DeviceIdentifier { get; set; } = string.Empty;

  /// <summary>
  /// 设备名称
  /// </summary>
  public string DeviceName { get; set; } = string.Empty;

  /// <summary>
  /// 诊断时间
  /// </summary>
  public DateTime DiagnosticTime { get; set; } = DateTime.Now;

  /// <summary>
  /// 综合健康评分（0-100）
  /// </summary>
  public double OverallHealthScore { get; set; }

  /// <summary>
  /// 综合状态
  /// </summary>
  public DeviceDiagnosticStatus OverallStatus { get; set; }

  /// <summary>
  /// 连接诊断信息
  /// </summary>
  public ConnectionDiagnostic? Connection { get; set; }

  /// <summary>
  /// 性能诊断信息
  /// </summary>
  public PerformanceDiagnostic? Performance { get; set; }

  /// <summary>
  /// 健康诊断信息
  /// </summary>
  public HealthDiagnostic? Health { get; set; }

  /// <summary>
  /// 配置诊断信息
  /// </summary>
  public ConfigurationDiagnostic? Configuration { get; set; }

  /// <summary>
  /// 数据诊断信息
  /// </summary>
  public DataDiagnostic? Data { get; set; }

  /// <summary>
  /// 诊断建议列表
  /// </summary>
  public List<DiagnosticRecommendation> Recommendations { get; set; } = new();

  /// <summary>
  /// 历史诊断数据
  /// </summary>
  public HistoryDiagnosticData? History { get; set; }
}

/// <summary>
/// 连接诊断信息
/// </summary>
public class ConnectionDiagnostic
{
  /// <summary>
  /// 连接状态
  /// </summary>
  public bool IsConnected { get; set; }

  /// <summary>
  /// 连接质量评分（0-100）
  /// </summary>
  public double ConnectionQuality { get; set; }

  /// <summary>
  /// 网络延迟（毫秒）
  /// </summary>
  public long? NetworkLatency { get; set; }

  /// <summary>
  /// 协议状态
  /// </summary>
  public string ProtocolStatus { get; set; } = string.Empty;

  /// <summary>
  /// 最后连接时间
  /// </summary>
  public DateTime? LastConnectedTime { get; set; }

  /// <summary>
  /// 最后活动时间
  /// </summary>
  public DateTime? LastActiveTime { get; set; }

  /// <summary>
  /// 连接失败次数
  /// </summary>
  public int ConnectionFailureCount { get; set; }

  /// <summary>
  /// 连接稳定性（过去24小时连接成功率）
  /// </summary>
  public double ConnectionStability { get; set; }
}

/// <summary>
/// 性能诊断信息
/// </summary>
public class PerformanceDiagnostic
{
  /// <summary>
  /// 平均响应时间（毫秒）
  /// </summary>
  public long AvgResponseTime { get; set; }

  /// <summary>
  /// 最大响应时间（毫秒）
  /// </summary>
  public long MaxResponseTime { get; set; }

  /// <summary>
  /// CPU使用率（%）
  /// </summary>
  public double CpuUsage { get; set; }

  /// <summary>
  /// 内存使用量（MB）
  /// </summary>
  public long MemoryUsage { get; set; }

  /// <summary>
  /// 数据吞吐量（每秒处理数据点数）
  /// </summary>
  public double DataThroughput { get; set; }

  /// <summary>
  /// 队列延迟（毫秒）
  /// </summary>
  public long QueueDelay { get; set; }

  /// <summary>
  /// 性能趋势（过去24小时）
  /// </summary>
  public PerformanceTrend Trend { get; set; } = PerformanceTrend.Stable;
}

/// <summary>
/// 健康诊断信息
/// </summary>
public class HealthDiagnostic
{
  /// <summary>
  /// 健康状态
  /// </summary>
  public EventHealthStatusEnum HealthStatus { get; set; }

  /// <summary>
  /// 健康评分（0-100）
  /// </summary>
  public double HealthScore { get; set; }

  /// <summary>
  /// 触发成功率（%）
  /// </summary>
  public double TriggerSuccessRate { get; set; }

  /// <summary>
  /// 总触发次数
  /// </summary>
  public long TotalTriggerCount { get; set; }

  /// <summary>
  /// 失败次数
  /// </summary>
  public long FailureCount { get; set; }

  /// <summary>
  /// 最后错误信息
  /// </summary>
  public string? LastErrorMessage { get; set; }

  /// <summary>
  /// 最后错误时间
  /// </summary>
  public DateTime? LastErrorTime { get; set; }

  /// <summary>
  /// 可用性评分（过去7天）
  /// </summary>
  public double AvailabilityScore { get; set; }
}

/// <summary>
/// 配置诊断信息
/// </summary>
public class ConfigurationDiagnostic
{
  /// <summary>
  /// 配置有效性
  /// </summary>
  public bool IsConfigurationValid { get; set; }

  /// <summary>
  /// 配置完整性评分（0-100）
  /// </summary>
  public double ConfigurationCompleteness { get; set; }

  /// <summary>
  /// 必填配置项数量
  /// </summary>
  public int RequiredConfigCount { get; set; }

  /// <summary>
  /// 已配置项数量
  /// </summary>
  public int ConfiguredCount { get; set; }

  /// <summary>
  /// 缺失的配置项
  /// </summary>
  public List<string> MissingConfigurations { get; set; } = new();

  /// <summary>
  /// 无效的配置项
  /// </summary>
  public List<string> InvalidConfigurations { get; set; } = new();

  /// <summary>
  /// 驱动版本信息
  /// </summary>
  public string DriverVersion { get; set; } = string.Empty;

  /// <summary>
  /// 是否为最新版本
  /// </summary>
  public bool IsLatestVersion { get; set; }
}

/// <summary>
/// 数据诊断信息
/// </summary>
public class DataDiagnostic
{
  /// <summary>
  /// 数据采集状态
  /// </summary>
  public bool IsDataCollecting { get; set; }

  /// <summary>
  /// 数据质量评分（0-100）
  /// </summary>
  public double DataQuality { get; set; }

  /// <summary>
  /// 最后数据采集时间
  /// </summary>
  public DateTime? LastDataTime { get; set; }

  /// <summary>
  /// 数据点总数
  /// </summary>
  public int TotalDataPoints { get; set; }

  /// <summary>
  /// 活跃数据点数量
  /// </summary>
  public int ActiveDataPoints { get; set; }

  /// <summary>
  /// 数据采集频率（次/分钟）
  /// </summary>
  public double CollectionFrequency { get; set; }

  /// <summary>
  /// 数据丢失率（%）
  /// </summary>
  public double DataLossRate { get; set; }

  /// <summary>
  /// 异常数据点数量
  /// </summary>
  public int AnomalousDataCount { get; set; }
}

/// <summary>
/// 诊断建议
/// </summary>
public class DiagnosticRecommendation
{
  /// <summary>
  /// 建议级别
  /// </summary>
  public RecommendationLevel Level { get; set; }

  /// <summary>
  /// 建议类型
  /// </summary>
  public string Type { get; set; } = string.Empty;

  /// <summary>
  /// 建议标题
  /// </summary>
  public string Title { get; set; } = string.Empty;

  /// <summary>
  /// 建议描述
  /// </summary>
  public string Description { get; set; } = string.Empty;

  /// <summary>
  /// 操作建议
  /// </summary>
  public string Action { get; set; } = string.Empty;
}

/// <summary>
/// 历史诊断数据
/// </summary>
public class HistoryDiagnosticData
{
  /// <summary>
  /// 诊断历史记录
  /// </summary>
  public List<EventDiagnosticInfo> DiagnosticHistory { get; set; } = new();

  /// <summary>
  /// 性能历史记录
  /// </summary>
  public List<PerformanceHistoryEntry> PerformanceHistory { get; set; } = new();

  /// <summary>
  /// 总记录数
  /// </summary>
  public int TotalCount { get; set; }

  /// <summary>
  /// 当前页码
  /// </summary>
  public int CurrentPage { get; set; }

  /// <summary>
  /// 页大小
  /// </summary>
  public int PageSize { get; set; }
}

/// <summary>
/// 设备诊断状态
/// </summary>
public enum DeviceDiagnosticStatus
{
  /// <summary>
  /// 良好
  /// </summary>
  Excellent = 0,

  /// <summary>
  /// 正常
  /// </summary>
  Good = 1,

  /// <summary>
  /// 警告
  /// </summary>
  Warning = 2,

  /// <summary>
  /// 错误
  /// </summary>
  Error = 3,

  /// <summary>
  /// 严重
  /// </summary>
  Critical = 4
}

/// <summary>
/// 性能趋势
/// </summary>
public enum PerformanceTrend
{
  /// <summary>
  /// 改善中
  /// </summary>
  Improving = 0,

  /// <summary>
  /// 稳定
  /// </summary>
  Stable = 1,

  /// <summary>
  /// 下降中
  /// </summary>
  Declining = 2
}

/// <summary>
/// 建议级别
/// </summary>
public enum RecommendationLevel
{
  /// <summary>
  /// 信息
  /// </summary>
  Info = 0,

  /// <summary>
  /// 建议
  /// </summary>
  Suggestion = 1,

  /// <summary>
  /// 警告
  /// </summary>
  Warning = 2,

  /// <summary>
  /// 严重
  /// </summary>
  Critical = 3
}