namespace EdgeGateway.Device.Entity;

/// <summary>
/// 设备列表请求
/// </summary>
public class DevicePageInput : BasePageInput
{
    /// <summary>
    /// 通道ID
    /// </summary>
    public long? ChannelId { get; set; }

}

/// <summary>
///     新增设备
/// </summary>
public class DeviceAddInput
{
    /// <summary>
    ///     设备Id
    /// </summary>
    [Required(ErrorMessage = "设备Id不能是空")]
    public string Identifier { get; set; }

    /// <summary>
    ///  设备名称
    /// </summary>
    public string Name { get; set; }

    /// <summary>
    ///     描述
    /// </summary>
    public string Description { get; set; }

    /// <summary>
    /// 配置
    /// </summary>
    public Dictionary<string, object> DeviceConfig { get; set; }

    /// <summary>
    /// 基本配置
    /// </summary>
    public DeviceInfo DeviceInfo { get; set; }

    /// <summary>
    ///     采集协议
    /// </summary>
    [Required]
    public DeviceDriver Driver { get; set; }

    /// <summary>
    ///     通道ID
    /// </summary>
    public long ChannelId { get; set; }

    /// <summary>
    /// 分组标签
    /// </summary>
    public List<string> GroupTags { get; set; } = new();

    /// <summary>
    /// 设备位置
    /// </summary>
    public string? Location { get; set; }

    /// <summary>
    /// 设备日志等级
    /// </summary>
    public DeviceLogLevelEnum LogLevel { get; set; } = DeviceLogLevelEnum.Info;
}

/// <summary>
/// 设备修改
/// </summary>
public class DeviceUpdateInput : DeviceAddInput
{
    /// <summary>
    /// 设备ID
    /// </summary>
    [Required(ErrorMessage = "设备ID不能为空")]
    public long Id { get; set; }
}

/// <summary>
/// 手动读取参数
/// </summary>
public class ManualReadInput
{
    /// <summary>
    /// 设备ID
    /// </summary>
    [Required]
    public long DeviceId { get; set; }

    /// <summary>
    /// 地址
    /// </summary>
    [Required]
    public string Address { get; set; }

    /// <summary>
    /// 长度
    /// </summary>
    public ushort Length { get; set; } = 1;

    /// <summary>
    /// 数据类型
    /// </summary>
    [Required]
    public string DataType { get; set; }

    /// <summary>
    /// 字符编码
    /// </summary>
    public string? Encoding { get; set; }
}

/// <summary>
/// 手动写入参数
/// </summary>
public class ManualWriteInput
{
    /// <summary>
    /// 设备ID
    /// </summary>  
    [Required]
    public long DeviceId { get; set; }

    /// <summary>
    /// 地址
    /// </summary>
    [Required]
    public string Address { get; set; }

    /// <summary>
    /// 写入值
    /// </summary>
    [Required]
    public string Value { get; set; }

    /// <summary>
    /// 数据类型
    /// </summary>
    [Required]
    public string DataType { get; set; }

    /// <summary>
    /// 长度
    /// </summary>
    public ushort Length { get; set; } = 1;

    /// <summary>
    /// 字符编码
    /// </summary>
    public string? Encoding { get; set; }
}

/// <summary>
/// 正在采集设备分页查询参数
/// </summary>
public class CollectingDevicePageInput : BasePageInput
{
    /// <summary>
    /// 通道ID
    /// </summary>
    public long? ChannelId { get; set; }

    /// <summary>
    /// 连接状态筛选：true-已连接，false-未连接，null-全部
    /// </summary>
    public bool? IsConnected { get; set; }
}