namespace EdgeGateway.Device.Entity.Dto.Template;

/// <summary>
/// 设备模板输出
/// </summary>
public class DeviceTemplateOutput
{
  /// <summary>
  /// 模板ID
  /// </summary>
  public long Id { get; set; }

  /// <summary>
  /// 模板名称
  /// </summary>
  public string Name { get; set; }

  /// <summary>
  /// 模板描述
  /// </summary>
  public string? Description { get; set; }

  /// <summary>
  /// 驱动名称
  /// </summary>
  public string DriverName { get; set; }

  /// <summary>
  /// 创建时间
  /// </summary>
  public DateTime CreateTime { get; set; }

  /// <summary>
  /// 点位数量
  /// </summary>
  public int LabelCount { get; set; }

  /// <summary>
  /// 模板点位列表（详情时返回）
  /// </summary>
  public List<DeviceTemplateLabelOutput>? Labels { get; set; }
}

/// <summary>
/// 设备模板点位输出
/// </summary>
public class DeviceTemplateLabelOutput
{
  /// <summary>
  /// 点位ID
  /// </summary>
  public long Id { get; set; }

  /// <summary>
  /// 标识符
  /// </summary>
  public string Identifier { get; set; }

  /// <summary>
  /// 变量名
  /// </summary>
  public string Name { get; set; }

  /// <summary>
  /// 转换数据类型
  /// </summary>
  public string TransitionType { get; set; }

  /// <summary>
  /// 数据来源
  /// </summary>
  public ValueSourceEnum ValueSource { get; set; }

  /// <summary>
  /// 执行优先级
  /// </summary>
  public short ActionOrder { get; set; }

  /// <summary>
  /// 默认值/脚本内容/表达式
  /// </summary>
  public string? Content { get; set; }

  /// <summary>
  /// 上送方式
  /// </summary>
  public SendTypeEnum SendType { get; set; }

  /// <summary>
  /// 采集周期
  /// </summary>
  public int Period { get; set; }

  /// <summary>
  /// 是否启用
  /// </summary>
  public bool Enable { get; set; }

  /// <summary>
  /// 读写方式
  /// </summary>
  public ProtectTypeEnum ProtectType { get; set; }

  /// <summary>
  /// 读取数据类型
  /// </summary>
  public string? DataType { get; set; }

  /// <summary>
  /// 读取地址
  /// </summary>
  public string? RegisterAddress { get; set; }

  /// <summary>
  /// 描述
  /// </summary>
  public string? Description { get; set; }
}