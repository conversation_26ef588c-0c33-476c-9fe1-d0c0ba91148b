namespace EdgeGateway.Device.Entity.Model;

/// <summary>
///     通讯管道列表请求
/// </summary>
public class EdgeChannelPageInput : BasePageInput
{
}

/// <summary>
///     通讯管道新增
/// </summary>
public class EdgeChannelAddInput
{
    /// <summary>
    ///     通道名称
    /// </summary>
    public string ChannelName { get; set; }

    /// <summary>
    ///     状态 ：启用；禁用
    /// </summary>
    [Required]
    public bool Enable { get; set; }

    /// <summary>
    ///     串口号
    /// </summary>
    [Required]
    public string Serial { get; set; }

    /// <summary>
    ///     波特率
    /// </summary>
    [Required]
    public int BaudRate { get; set; }

    /// <summary>
    ///     数据位
    /// </summary>
    [Required]
    public int DataBits { get; set; }

    /// <summary>
    ///     停止位
    /// </summary>
    [Required]
    public StopBits Stop { get; set; }

    /// <summary>
    ///     校验位
    /// </summary>
    [Required]
    public Parity Checkout { get; set; }

    /// <summary>
    ///     线程数量
    /// </summary>
    public int ThreadCount { get; set; }

}