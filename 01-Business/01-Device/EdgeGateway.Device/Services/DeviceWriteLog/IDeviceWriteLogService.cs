using EdgeGateway.Device.Entity;
using EdgeGateway.Base.Entity;
using EdgeGateway.SqlSugar.SqlSugar;

namespace EdgeGateway.Device.Services
{
  /// <summary>
  /// 设备写入日志服务接口
  /// </summary>
  public interface IDeviceWriteLogService
  {
    /// <summary>
    /// 分页查询设备写入日志
    /// </summary>
    /// <param name="input">查询参数</param>
    /// <returns>分页结果</returns>
    Task<SqlSugarPagedList<DeviceWriteLog>> GetDeviceWriteLogPage(DeviceWriteLogPageInput input);

    /// <summary>
    /// 获取设备写入日志详情
    /// </summary>
    /// <param name="id">日志ID</param>
    /// <returns>写入日志详情</returns>
    Task<DeviceWriteLog> GetDeviceWriteLogDetail(long id);

    /// <summary>
    /// 删除设备写入日志
    /// </summary>
    /// <param name="id">日志ID</param>
    /// <returns>是否成功</returns>
    Task<bool> DeleteDeviceWriteLog(long id);

    /// <summary>
    /// 清空指定设备的写入日志
    /// </summary>
    /// <param name="deviceId">设备ID</param>
    /// <returns>是否成功</returns>
    Task<bool> ClearDeviceWriteLogs(long deviceId);

    /// <summary>
    /// 清空所有写入日志
    /// </summary>
    /// <returns>是否成功</returns>
    Task<bool> ClearAllDeviceWriteLogs();

    /// <summary>
    /// 获取设备写入统计信息
    /// </summary>
    /// <param name="deviceId">设备ID（可选）</param>
    /// <param name="startTime">开始时间（可选）</param>
    /// <param name="endTime">结束时间（可选）</param>
    /// <returns>写入统计信息</returns>
    Task<DeviceWriteLogStatistics> GetDeviceWriteLogStatistics(long? deviceId = null, DateTime? startTime = null, DateTime? endTime = null);

    /// <summary>
    /// 手动触发清理过期日志
    /// </summary>
    /// <returns>清理的记录数</returns>
    Task<int> ManualCleanup();

    /// <summary>
    /// 获取当前日志清理配置
    /// </summary>
    /// <returns>清理配置信息</returns>
    object GetCleanupConfig();

    /// <summary>
    /// 更新日志清理配置
    /// </summary>
    /// <param name="retentionDays">保留天数</param>
    /// <param name="enableAutoCleanup">是否启用自动清理</param>
    /// <param name="cleanupTime">清理执行时间（格式如"03:00"）</param>
    /// <returns>更新后的配置</returns>
    object UpdateCleanupConfig(int retentionDays, bool enableAutoCleanup, string cleanupTime);
  }

  /// <summary>
  /// 设备写入日志查询参数
  /// </summary>
  public class DeviceWriteLogPageInput : BasePageInput
  {
    /// <summary>
    /// 设备ID
    /// </summary>
    public long? DeviceId { get; set; }

    /// <summary>
    /// 标签标识符
    /// </summary>
    public string LabelIdentifier { get; set; }

    /// <summary>
    /// 写入地址
    /// </summary>
    public string Address { get; set; }

    /// <summary>
    /// 写入值
    /// </summary>
    public string Value { get; set; }

    /// <summary>
    /// 写入来源类型
    /// </summary>
    public WriteSourceType? SourceType { get; set; }

    /// <summary>
    /// 写入状态
    /// </summary>
    public bool? Success { get; set; }

    /// <summary>
    /// 开始时间
    /// </summary>
    public DateTime? StartTime { get; set; }

    /// <summary>
    /// 结束时间
    /// </summary>
    public DateTime? EndTime { get; set; }
  }

  /// <summary>
  /// 设备写入日志统计信息
  /// </summary>
  public class DeviceWriteLogStatistics
  {
    /// <summary>
    /// 总写入次数
    /// </summary>
    public int TotalCount { get; set; }

    /// <summary>
    /// 成功写入次数
    /// </summary>
    public int SuccessCount { get; set; }

    /// <summary>
    /// 失败写入次数
    /// </summary>
    public int FailCount { get; set; }

    /// <summary>
    /// 按来源类型统计
    /// </summary>
    public Dictionary<WriteSourceType, int> SourceTypeCount { get; set; } = new Dictionary<WriteSourceType, int>();

    /// <summary>
    /// 按设备统计（前10）
    /// </summary>
    public List<DeviceWriteCount> TopDevices { get; set; } = new List<DeviceWriteCount>();
  }

  /// <summary>
  /// 设备写入统计
  /// </summary>
  public class DeviceWriteCount
  {
    /// <summary>
    /// 设备ID
    /// </summary>
    public long DeviceId { get; set; }

    /// <summary>
    /// 设备标识符
    /// </summary>
    public string DeviceIdentifier { get; set; }

    /// <summary>
    /// 设备名称
    /// </summary>
    public string DeviceName { get; set; }

    /// <summary>
    /// 写入次数
    /// </summary>
    public int WriteCount { get; set; }
  }

  /// <summary>
  /// 操作用户写入统计
  /// </summary>
  public class OperatorWriteCount
  {
    /// <summary>
    /// 操作用户
    /// </summary>
    public string Operator { get; set; }

    /// <summary>
    /// 写入次数
    /// </summary>
    public int WriteCount { get; set; }
  }
}