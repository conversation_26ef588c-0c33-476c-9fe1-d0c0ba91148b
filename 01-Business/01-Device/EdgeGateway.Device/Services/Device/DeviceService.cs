using EdgeGateway.Base.Entity.Enums;
using EdgeGateway.Base.SystemStatistics;
using EdgeGateway.Device.Entity.Dto;
using EdgeGateway.Driver.Entity.Const;
using EdgeGateway.Driver.Entity.Enums;
using Microsoft.AspNetCore.Http;
using NPOI.SS.UserModel;
using NPOI.SS.Util;
using NPOI.XSSF.UserModel;
using Microsoft.Extensions.Logging;
using Microsoft.Extensions.Caching.Memory;
using EdgeGateway.Device.Entity.Dto.Device;

namespace EdgeGateway.Device.Services.Device;

/// <summary>
///     设备中心
/// </summary>
[ApiDescriptionSettings("设备中心")]
[Route("/api/device/")]
public class DeviceService : ITransient, IDynamicApiController
{
    /// <summary>
    ///     设备仓储
    /// </summary>
    private readonly SqlSugarRepository<Entity.Device> _device;
    /// <summary>
    ///     协议单例
    /// </summary>
    private readonly DriverHostService _driver;
    /// <summary>
    ///     采集授权
    /// </summary>
    private readonly DeviceAuthorizationService _authorization;
    /// <summary>
    ///     采集设备线程单例
    /// </summary>
    private readonly Clients.EdgeGateway _edgeGateway;
    /// <summary>
    /// 系统统计服务
    /// </summary>
    private readonly ISystemStatisticsService _systemStatisticsService;
    /// <summary>
    /// 日志
    /// </summary>
    private readonly ILogger<DeviceService> _logger;
    /// <summary>
    /// 设备标签仓储
    /// </summary>
    private readonly SqlSugarRepository<DeviceLabel> _deviceLabel;
    /// <summary>
    /// 事件健康状态仓储
    /// </summary>
    private readonly SqlSugarRepository<EventHealthStatus> _healthStatus;
    /// <summary>
    /// 事件诊断信息仓储
    /// </summary>
    private readonly SqlSugarRepository<EventDiagnosticInfo> _diagnosticInfo;
    /// <summary>
    /// 内存缓存
    /// </summary>
    private readonly IMemoryCache _memoryCache;


    /// <summary>
    /// </summary>
    /// <param name="device">设备仓储</param>
    /// <param name="driver">协议单例</param>
    /// <param name="authorization">采集授权</param>
    /// <param name="edgeGateway">采集设备线程单例</param>
    /// <param name="statisticsService">系统统计服务</param>
    /// <param name="logger">日志</param>
    /// <param name="deviceLabel">设备标签仓储</param>
    /// <param name="healthStatus">事件健康状态仓储</param>
    /// <param name="diagnosticInfo">事件诊断信息仓储</param>
    /// <param name="memoryCache">内存缓存</param>
    public DeviceService(
        SqlSugarRepository<Entity.Device> device,
        DriverHostService driver,
        DeviceAuthorizationService authorization,
        Clients.EdgeGateway edgeGateway,
        ISystemStatisticsService statisticsService,
        ILogger<DeviceService> logger,
        SqlSugarRepository<DeviceLabel> deviceLabel,
        SqlSugarRepository<EventHealthStatus> healthStatus,
        SqlSugarRepository<EventDiagnosticInfo> diagnosticInfo,
        IMemoryCache memoryCache)
    {
        _device = device;
        _driver = driver;
        _authorization = authorization;
        _edgeGateway = edgeGateway;
        _systemStatisticsService = statisticsService;
        _logger = logger;
        _deviceLabel = deviceLabel;
        _healthStatus = healthStatus;
        _diagnosticInfo = diagnosticInfo;
        _memoryCache = memoryCache;
    }

    /// <summary>
    ///     设备列表
    /// </summary>
    /// <param name="input"></param>
    /// <returns></returns>
    [HttpGet("page")]
    [OperationId(nameof(Page))]
    public async Task<SqlSugarPagedList<Entity.Device>> Page([FromQuery] DevicePageInput input)
    {
        // 获取设备分页数据
        var devicePage = await _device.AsQueryable()
        .WhereIF(input.ChannelId != null && input.ChannelId != 0, w => w.ChannelId == input.ChannelId)
        .WhereIF(input.ChannelId == null || input.ChannelId == 0, w => w.ChannelId == 0)
        .WhereIF(!string.IsNullOrEmpty(input.Keyword), w => w.Identifier.Contains(input.Keyword.Trim())
         || (w.Name != null && w.Name.Contains(input.Keyword.Trim())))
        .ToPagedListAsync(input.Page, input.PageSize);

        // 获取设备连接状态
        foreach (var item in devicePage.Items)
        {
            if (_edgeGateway.DeviceThreads.TryGetValue(item.Id, out var thread))
            {
                // 设备连接状态
                item.IsConnect = thread.Driver.IsConnected;
                // 设备状态
                item.Status = DeviceStatusTypeEnum.Good;
                // 最后活动时间
                item.LastActiveTime = thread.LastActiveTime;
            }
            else
            {
                // 设备未连接
                item.IsConnect = false;
                // 设备状态为异常
                item.Status = DeviceStatusTypeEnum.Bad;
                // 最后活动时间
                item.LastActiveTime = null;
            }
        }

        return devicePage;
    }

    /// <summary>
    ///     获取正在采集的设备列表
    /// </summary>
    /// <param name="input">查询参数</param>
    /// <returns>正在采集的设备分页列表</returns>
    [HttpGet("collecting")]
    [OperationId(nameof(GetCollectingDevices))]
    public async Task<SqlSugarPagedList<CollectingDeviceDto>> GetCollectingDevices([FromQuery] CollectingDevicePageInput input)
    {
        try
        {
            // 获取所有启用的设备，包含相关信息
            var query = _device.AsQueryable()
                .Where(w => w.Enable) // 只查询启用的设备
                .Includes(w => w.EdgeChannel)
                .Includes(w => w.DeviceLabel.Where(l => l.Enable).ToList())
                .WhereIF(input.ChannelId != null && input.ChannelId != 0, w => w.ChannelId == input.ChannelId)
                .WhereIF(!string.IsNullOrEmpty(input.Keyword), w => w.Identifier.Contains(input.Keyword.Trim())
                    || (w.Name != null && w.Name.Contains(input.Keyword.Trim())));

            // 获取分页数据
            var devicePage = await query.ToPagedListAsync(input.Page, input.PageSize);

            // 转换为CollectingDeviceDto并填充采集状态信息
            var collectingDevices = new List<CollectingDeviceDto>();

            foreach (var device in devicePage.Items)
            {
                // 检查设备是否有活跃的采集线程
                var hasActiveThread = _edgeGateway.DeviceThreads.TryGetValue(device.Id, out var thread);

                // 根据连接状态筛选进行过滤 - 只有当设备有活跃线程时才进行此过滤
                if (hasActiveThread && input.IsConnected.HasValue && thread.Driver.IsConnected != input.IsConnected.Value)
                    continue;

                // 如果没有活跃线程且用户指定只要已连接的设备，则跳过
                if (!hasActiveThread && input.IsConnected.HasValue && input.IsConnected.Value)
                    continue;

                var collectingDevice = new CollectingDeviceDto
                {
                    Id = device.Id,
                    Identifier = device.Identifier,
                    Name = device.Name ?? string.Empty,
                    Description = device.Description,
                    ChannelName = device.EdgeChannel?.ChannelName,
                    DriverName = device.Driver?.DriverName,
                    IsConnect = hasActiveThread ? thread.Driver.IsConnected : false,
                    Status = hasActiveThread ?
                        (thread.Driver.IsConnected ? DeviceStatusTypeEnum.Good : DeviceStatusTypeEnum.Bad) :
                        DeviceStatusTypeEnum.Bad, // 使用Bad状态表示未采集
                    RunStatus = GetDeviceRunStatus(device, hasActiveThread, hasActiveThread ? thread.Driver.IsConnected : false),
                    LastActiveTime = hasActiveThread ? thread.LastActiveTime : null,
                    CollectionPeriod = device.DeviceInfo?.MinPeriod,
                    TotalDataPoints = device.DeviceLabel?.Count ?? 0,
                    EnabledDataPoints = device.DeviceLabel?.Count(l => l.Enable) ?? 0,
                    ReportType = device.DeviceInfo?.ReportType,
                    GroupTags = device.GroupTags ?? new List<string>(),
                    Location = device.Location,
                    CollectionStartTime = hasActiveThread ? thread.CollectionStartTime : null, // 没有线程时为null
                    TodayOnlineMinutes = hasActiveThread ? (int)thread.TodayOnlineMinutes : 0 // 没有线程时为0
                };

                collectingDevices.Add(collectingDevice);
            }

            // 创建新的分页结果
            var result = new SqlSugarPagedList<CollectingDeviceDto>
            {
                Page = devicePage.Page,
                PageSize = devicePage.PageSize,
                Total = devicePage.Total, // 使用原始数据库查询的总数
                Items = collectingDevices,
                TotalPages = devicePage.TotalPages, // 使用原始的总页数
                HasPrevPage = devicePage.HasPrevPage, // 使用原始的上一页标志
                HasNextPage = devicePage.HasNextPage // 使用原始的下一页标志
            };

            _logger.LogInformation($"获取设备列表成功，共查询到 {devicePage.Total} 个设备，当前页显示 {collectingDevices.Count} 个设备");
            return result;
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "获取设备列表失败");
            throw Oops.Oh("获取设备列表失败");
        }
    }

    /// <summary>
    ///     获取所有设备位置
    /// </summary>
    /// <returns>位置列表</returns>
    [HttpGet("locations")]
    [OperationId(nameof(GetLocations))]
    public async Task<List<string>> GetLocations()
    {
        // 获取所有设备位置
        var locations = await _device.AsQueryable()
            .Where(w => w.Location != null && w.Location != "")
            .Select(s => s.Location)
            .Distinct()
            .ToListAsync();

        return locations.OrderBy(x => x).ToList();
    }

    /// <summary>
    ///     获取所有协议名称
    /// </summary>
    /// <returns>协议名称列表</returns>
    [HttpGet("driver-names")]
    [OperationId(nameof(GetDriverNames))]
    public async Task<List<string>> GetDriverNames()
    {
        // 获取所有设备使用的协议名称
        var driverNames = await _device.AsQueryable()
            .Select(s => s.Driver.DriverName)
            .Distinct()
            .ToListAsync();

        return driverNames.OrderBy(x => x).ToList();
    }

    /// <summary>
    ///     创建设备
    /// </summary>
    /// <param name="input"></param>
    /// <returns></returns>
    [HttpPost("add")]
    [OperationId(nameof(Add))]
    public async Task<long> Add(DeviceAddInput input)
    {
        // 验证是否需要开启授权
        await _authorization.CheckAuthorization(DeviceAuthorizationTypeEnum.Device);
        if (await _device.AsQueryable().AnyAsync(a => a.Identifier == input.Identifier))
            throw Oops.Oh(ErrorCode.C3000, input.Identifier);

        // 如果通道ID不为0
        if (input.ChannelId != 0)
        {
            // 检查通道是否存在
            _ = await _device.AsSugarClient().Queryable<Entity.EdgeChannel>()
                .FirstAsync(a => a.Id == input.ChannelId) ?? throw Oops.Oh(ErrorCode.C3004);
        }
        // 验证协议是否存在
        var driver = _driver.Drivers.FirstOrDefault(f => f.DriverName == input.Driver.DriverName) ?? throw Oops.Oh(ErrorCode.D4001, input.Driver.DriverName);
        // 
        var device = input.Adapt<Entity.Device>();
        device.Enable = true;
        device.Index = 99;
        // 生成设备连接配置
        _driver.AddConfigs(device, input.DeviceConfig);
        device.DeviceLabel = new List<DeviceLabel>();
        // 设置分组标签
        device.GroupTags = input.GroupTags ?? new List<string>();
        // 设置位置
        device.Location = input.Location;

        // 设备初始化时创建设备默认属性
        if (driver.CreateVariables) CreateVariables(device, driver);

        // 新增设备
        var isAdd = await _device.AsSugarClient().InsertNav(device)
            .Include(w => w.DeviceLabel)
            .ExecuteCommandAsync();
        // 添加成功后直接启动
        if (isAdd)
            await _edgeGateway.CreateDeviceThread(device);
        // 更新设备数量统计 
        UpdateDeviceCount();
        return device.Id;
    }

    /// <summary>
    ///     获取设备详情
    /// </summary>
    /// <param name="id">设备ID</param>
    /// <returns></returns>
    [HttpGet("detail/{id}")]
    [OperationId(nameof(GetDetail))]
    public async Task<Entity.Device> GetDetail(long id)
    {
        var device = await _device.AsQueryable().FirstAsync(a => a.Id == id)
            ?? throw Oops.Oh(ErrorCode.D1000);

        // 获取设备连接状态
        if (_edgeGateway.DeviceThreads.TryGetValue(device.Id, out var thread))
        {
            device.IsConnect = thread.Driver.IsConnected;
            device.Status = DeviceStatusTypeEnum.Good;
        }
        else
        {
            device.IsConnect = false;
            device.Status = DeviceStatusTypeEnum.Bad;
        }

        return device;
    }

    /// <summary>
    ///     获取设备下拉列表
    /// </summary>
    /// <returns></returns>
    [HttpGet("select")]
    [OperationId(nameof(GetSelect))]
    public async Task<List<DeviceSelectDto>> GetSelect()
    {
        var devices = await _device.AsQueryable()
            .Where(w => w.Enable)
            .Select(s => new DeviceSelectDto
            {
                Id = s.Id,
                Name = s.Name,
                Identifier = s.Identifier
            })
            .ToListAsync();

        return devices;
    }

    /// <summary>
    ///     更新设备
    /// </summary>
    /// <param name="input"></param>
    /// <returns></returns> 
    [HttpPost("update")]
    [OperationId(nameof(Update))]
    public async Task Update(DeviceUpdateInput input)
    {
        // 设备信息
        var device = await _device.AsQueryable().Where(w => w.Id == input.Id)
            .Includes(w => w.DeviceLabel)
            .FirstAsync() ?? throw Oops.Oh(ErrorCode.D1000);

        // 验证标识符是否重复
        if (await _device.IsAnyAsync(a => a.Identifier == input.Identifier && a.Id != input.Id))
            throw Oops.Oh(ErrorCode.C3000, input.Identifier);

        // 更新设备信息
        device.Name = input.Name;
        // 更新设备描述
        device.Description = input.Description;
        // 更新设备基本配置
        device.DeviceInfo = input.DeviceInfo;
        // 更新分组标签
        device.GroupTags = input.GroupTags ?? new List<string>();
        // 更新位置
        device.Location = input.Location;
        // 更新日志等级
        device.LogLevel = input.LogLevel;
        // 更新设备连接配置
        foreach (var (key, objValue) in input.DeviceConfig)
        {
            // 获取设备配置
            var deviceConfig = device.DeviceConfigs.FirstOrDefault(f => f.Identifier == key);
            // 如果配置存在
            if (deviceConfig != null)
                // 更新配置值
                deviceConfig.Value = ObjectExtension.GetJsonElementValue(objValue);
        }

        // 更新设备
        await _device.AsSugarClient().Updateable(device).IgnoreColumns(true).ExecuteCommandAsync();
        // 重启设备线程
        await _edgeGateway.RestartDeviceThread(device);
    }

    /// <summary>
    ///     删除设备
    /// </summary>
    /// <param name="id">设备ID</param>
    /// <returns></returns>
    [HttpPost("delete/{id}")]
    [OperationId(nameof(Delete))]
    public async Task Delete(long id)
    {
        // 获取设备
        var device = await _device.AsQueryable().Where(w => w.Id == id)
            .Includes(w => w.DeviceLabel)
            .FirstAsync() ?? throw Oops.Oh(ErrorCode.D1000);

        // 停止设备线程
        await _edgeGateway.StopDeviceThread(device);

        // 删除设备
        await _device.AsSugarClient().DeleteNav(device)
            .Include(w => w.DeviceLabel)
            .ExecuteCommandAsync();

        // 更新设备数量统计 
        UpdateDeviceCount();
    }

    /// <summary>
    ///     导出JSON
    /// </summary>
    [HttpGet("export-json")]
    [OperationId(nameof(ExportJson))]
    public async Task<IActionResult> ExportJson()
    {
        // 获取设备
        var devices = await _device.GetListAsync();
        // 序列化设备
        var json = JsonSerializer.Serialize(devices, new JsonSerializerOptions
        {
            WriteIndented = true
        });
        // 返回文件
        return new FileContentResult(Encoding.UTF8.GetBytes(json), "application/json")
        {
            FileDownloadName = $"设备列表_{DateTime.Now:yyyyMMddHHmmss}.json"
        };
    }

    /// <summary>
    ///     导入JSON
    /// </summary>
    [HttpPost("import-json")]
    [OperationId(nameof(ImportJson))]
    public async Task<List<string>> ImportJson([FromBody] List<Entity.Device> devices)
    {
        var errors = new List<string>();

        try
        {
            // 验证是否需要开启授权
            await _authorization.CheckAuthorization(DeviceAuthorizationTypeEnum.Device, devices.Count);

            // 获取所有已存在的标识符
            var existingIdentifiers = await _device.AsQueryable()
                .Select(s => s.Identifier)
                .ToListAsync();

            // 遍历导入的设备
            foreach (var device in devices)
            {
                try
                {
                    // 验证设备名称和标识符是否为空
                    if (string.IsNullOrEmpty(device.Name) || string.IsNullOrEmpty(device.Identifier))
                    {
                        // 添加错误信息
                        errors.Add($"设备 {device.Name ?? device.Identifier ?? "未知"}: 设备名称或标识符不能为空");
                        continue;
                    }

                    // 验证协议是否存在
                    var driver = _driver.Drivers.FirstOrDefault(f => f.DriverName == device.Driver?.DriverName);
                    if (driver == null)
                    {
                        // 添加错误信息
                        errors.Add($"设备 {device.Name}: 协议 {device.Driver?.DriverName ?? "未知"} 不存在");
                        continue;
                    }

                    // 检查设备是否已存在
                    var existingDevice = await _device.AsQueryable()
                        .Where(w => w.Identifier == device.Identifier)
                        .Includes(w => w.DeviceLabel)
                        .FirstAsync();
                    // 如果设备已存在
                    if (existingDevice != null)
                    {
                        // 停止现有设备的采集
                        await _edgeGateway.StopDeviceThread(existingDevice);

                        // 更新现有设备
                        device.Id = existingDevice.Id;
                        await _device.AsSugarClient().UpdateNav(device)
                            .Include(w => w.DeviceLabel)
                            .ExecuteCommandAsync();
                    }
                    else
                    {
                        // 插入新设备
                        device.Id = YitIdHelper.NextId();
                        await _device.AsSugarClient().InsertNav(device)
                            .Include(w => w.DeviceLabel)
                            .ExecuteCommandAsync();
                    }

                    // 启动设备采集
                    if (device.Enable)
                    {
                        await _edgeGateway.CreateDeviceThread(device);
                    }
                }
                catch (Exception ex)
                {
                    errors.Add($"设备 {device.Name}: {ex.Message}");
                }
            }
        }
        catch (Exception ex)
        {
            errors.Add($"导入失败: {ex.Message}");
        }
        // 更新设备数量统计
        UpdateDeviceCount();
        return errors;
    }

    /// <summary>
    ///     批量启用/禁用设备
    /// </summary>
    [HttpPost("batch-enable")]
    [OperationId(nameof(BatchEnable))]
    public async Task BatchEnable(BatchEnableInput input)
    {
        var devices = new List<Entity.Device>();
        if (input.Enable)
        {
            // 获取设备
            devices = await _device.AsQueryable().Where(w => input.Ids.Contains(w.Id))
                .Includes(c => c.DeviceLabel.Where(w => w.Enable).ToList())
                .ToListAsync();
        }
        else
        {
            // 获取设备
            devices = await _device.GetListAsync(w => input.Ids.Contains(w.Id));
        }

        // 遍历设备
        foreach (var device in devices)
        {
            // 更新设备状态
            device.Enable = input.Enable;
        }
        // 更新设备状态
        await _device.AsSugarClient().Updateable(devices).UpdateColumns(w => w.Enable).ExecuteCommandAsync();

        // 遍历设备
        foreach (var device in devices)
        {
            try
            {
                // 如果启用
                if (input.Enable)
                    // 创建设备线程
                    await _edgeGateway.CreateDeviceThread(device);
                else
                    // 停止设备线程
                    await _edgeGateway.StopDeviceThread(device);
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, $"设备启动{device.Name}失败");
                throw Oops.Oh(ErrorCode.D1000, $"设备启动{device.Name}失败");
            }
        }
    }

    /// <summary>
    ///     批量删除设备
    /// </summary>
    [HttpPost("batch-delete")]
    [OperationId(nameof(BatchDelete))]
    public async Task BatchDelete([FromBody] List<long> ids)
    {
        // 遍历要删除的设备ID
        foreach (var id in ids)
        {
            // 获取设备
            var device = await _device.AsQueryable()
                .Where(w => w.Id == id)
                .Includes(w => w.DeviceLabel)
                .FirstAsync();
            // 如果设备不存在
            if (device == null) continue;
            {
                // 停止设备采集
                await _edgeGateway.StopDeviceThread(device);
                // 删除设备
                await _device.AsSugarClient().DeleteNav(device)
                    .Include(w => w.DeviceLabel)
                    .ExecuteCommandAsync();
            }
        }
        // 更新设备数量统计
        UpdateDeviceCount();
    }

    /// <summary>
    ///     批量重启设备
    /// </summary>
    [HttpPost("batch-restart")]
    [OperationId(nameof(BatchRestart))]
    public async Task BatchRestart([FromBody] List<long> ids)
    {
        // 遍历要重启的设备ID
        foreach (var id in ids)
        {
            try
            {
                // 获取设备
                var device = await _device.AsQueryable()
                    .Where(w => w.Id == id)
                    .Includes(w => w.DeviceLabel)
                    .FirstAsync();

                // 如果设备不存在，跳过
                if (device == null)
                {
                    _logger.LogWarning($"设备ID {id} 不存在，跳过重启操作");
                    continue;
                }

                // 重启设备线程
                await _edgeGateway.RestartDeviceThread(device);

                // 记录成功日志
                _logger.LogInformation($"设备 {device.Name}({device.Identifier}) 重启成功");
            }
            catch (Exception ex)
            {
                // 记录错误但不中断其他设备的重启操作
                _logger.LogError(ex, $"设备ID {id} 重启失败");
                // 可以选择抛出异常，这里选择继续处理其他设备
                // throw Oops.Oh(ErrorCode.D1000, $"设备ID {id} 重启失败: {ex.Message}");
            }
        }
    }

    /// <summary>
    ///     导出设备数据为Excel
    /// </summary>
    /// <param name="ids">要导出的设备ID列表，为空则导出所有设备</param>
    /// <returns>Excel文件流</returns>
    [HttpGet("export-excel")]
    [OperationId(nameof(ExportExcel))]
    public async Task<IActionResult> ExportExcel([FromQuery] List<long>? ids = null)
    {
        try
        {
            // 根据传入的ID列表获取设备数据，包括标签和事件
            var query = _device.AsQueryable()
                .Includes(d => d.EdgeChannel)
                .Includes(d => d.DeviceLabel);

            // 如果传入了设备ID列表，则按ID过滤
            if (ids != null && ids.Count > 0)
            {
                query = query.Where(d => ids.Contains(d.Id));
            }

            var devices = await query.ToListAsync();

            // 创建工作簿
            var workbook = new XSSFWorkbook();

            // 创建设备工作表
            CreateDeviceSheet(workbook, devices);

            // 创建设备标签工作表
            CreateDeviceLabelSheet(workbook, devices);

            // 创建设备事件工作表
            CreateDeviceEventSheet(workbook, devices);

            // 写入到内存流
            var ms = new MemoryStream();
            workbook.Write(ms, true);
            ms.Position = 0;

            // 根据导出类型生成文件名
            var fileName = ids != null && ids.Count > 0
                ? $"设备配置导出_选择性_{DateTime.Now:yyyyMMddHHmmss}.xlsx"
                : $"设备配置导出_{DateTime.Now:yyyyMMddHHmmss}.xlsx";

            // 返回文件
            return new FileStreamResult(ms, "application/vnd.openxmlformats-officedocument.spreadsheetml.sheet")
            {
                FileDownloadName = fileName
            };
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "导出设备Excel失败");
            throw Oops.Oh(ErrorCode.D1000, "导出设备Excel失败");
        }
    }

    /// <summary>
    /// 创建设备工作表
    /// </summary>
    private void CreateDeviceSheet(XSSFWorkbook workbook, List<Entity.Device> devices)
    {
        // 创建设备工作表
        var sheet = workbook.CreateSheet("设备列表");

        // 创建样式
        var titleStyle = CreateTitleStyle(workbook);
        var headerStyle = CreateHeaderStyle(workbook);
        var dataStyle = CreateDataStyle(workbook);
        var boolStyle = CreateBoolStyle(workbook, dataStyle);

        // 创建标题行
        var titleRow = sheet.CreateRow(0);
        titleRow.Height = 30 * 20; // 设置行高
        var titleCell = titleRow.CreateCell(0);
        titleCell.SetCellValue("设备列表");
        titleCell.CellStyle = titleStyle;

        // 定义表头
        var headers = new[]
        {
            "ID", "标识符", "名称", "通道", "驱动名称", "驱动版本", "轮询周期(ms)",
            "恢复等待时间(ms)", "恢复时间(ms)", "数据上报方式", "存储历史数据",
            "是否启用", "是否允许写入", "描述", "分组标签", "位置", "设备配置"
        };

        // 合并标题单元格
        var columnCount = headers.Length; // 总列数
        sheet.AddMergedRegion(new CellRangeAddress(0, 0, 0, columnCount - 1));

        // 为合并后的所有单元格设置样式
        for (int i = 1; i < columnCount; i++)
        {
            var cell = titleRow.CreateCell(i);
            cell.CellStyle = titleStyle;
        }

        // 创建表头行
        var headerRow = sheet.CreateRow(1);
        headerRow.Height = 25 * 20; // 设置行高

        // 设置表头
        for (int i = 0; i < headers.Length; i++)
        {
            var cell = headerRow.CreateCell(i);
            cell.SetCellValue(headers[i]);
            cell.CellStyle = headerStyle;
        }

        // 填充数据
        for (int i = 0; i < devices.Count; i++)
        {
            var device = devices[i];
            var row = sheet.CreateRow(i + 2); // 从第三行开始（标题+表头后）

            // ID
            var idCell = row.CreateCell(0);
            idCell.SetCellValue(device.Id);
            idCell.CellStyle = dataStyle;

            // 标识符
            var identifierCell = row.CreateCell(1);
            identifierCell.SetCellValue(device.Identifier);
            identifierCell.CellStyle = dataStyle;

            // 名称
            var nameCell = row.CreateCell(2);
            nameCell.SetCellValue(device.Name);
            nameCell.CellStyle = dataStyle;

            // 通道
            var channelCell = row.CreateCell(3);
            channelCell.SetCellValue(device.EdgeChannel?.ChannelName ?? "");
            channelCell.CellStyle = dataStyle;

            // 驱动名称
            var driverNameCell = row.CreateCell(4);
            driverNameCell.SetCellValue(device.Driver?.DriverName ?? "");
            driverNameCell.CellStyle = dataStyle;

            // 驱动版本
            var versionCell = row.CreateCell(5);
            versionCell.SetCellValue(device.Driver?.Version ?? "");
            versionCell.CellStyle = dataStyle;

            // 轮询周期
            var minPeriodCell = row.CreateCell(6);
            minPeriodCell.SetCellValue(device.DeviceInfo?.MinPeriod ?? 0);
            minPeriodCell.CellStyle = dataStyle;

            // 恢复等待时间
            var waitTimeCell = row.CreateCell(7);
            waitTimeCell.SetCellValue(device.DeviceInfo?.WaitTime ?? 0);
            waitTimeCell.CellStyle = dataStyle;

            // 恢复时间
            var reConnTimeCell = row.CreateCell(8);
            reConnTimeCell.SetCellValue(device.DeviceInfo?.ReConnTime ?? 0);
            reConnTimeCell.CellStyle = dataStyle;

            // 数据上报方式
            var reportTypeCell = row.CreateCell(9);
            reportTypeCell.SetCellValue(device.DeviceInfo?.ReportType.ToString() ?? "");
            reportTypeCell.CellStyle = dataStyle;

            // 存储历史数据
            var storeHistoryDataCell = row.CreateCell(10);
            storeHistoryDataCell.SetCellValue(device.DeviceInfo?.StoreHistoryData == true ? "是" : "否");
            storeHistoryDataCell.CellStyle = boolStyle;

            // 是否启用
            var enableCell = row.CreateCell(11);
            enableCell.SetCellValue(device.Enable ? "是" : "否");
            enableCell.CellStyle = boolStyle;

            // 是否允许写入
            var allowWriteCell = row.CreateCell(12);
            allowWriteCell.SetCellValue(device.AllowWrite ? "是" : "否");
            allowWriteCell.CellStyle = boolStyle;

            // 描述
            var descCell = row.CreateCell(13);
            descCell.SetCellValue(device.Description ?? "");
            descCell.CellStyle = dataStyle;

            // 分组标签
            var groupTagsCell = row.CreateCell(14);
            groupTagsCell.SetCellValue(device.GroupTags != null && device.GroupTags.Count > 0
                ? string.Join(",", device.GroupTags)
                : "");
            groupTagsCell.CellStyle = dataStyle;

            // 位置
            var locationCell = row.CreateCell(15);
            locationCell.SetCellValue(device.Location ?? "");
            locationCell.CellStyle = dataStyle;

            // 设备配置（JSON格式）
            var configCell = row.CreateCell(16);
            configCell.SetCellValue(System.Text.Json.JsonSerializer.Serialize(device.DeviceConfigs));
            configCell.CellStyle = dataStyle;
        }

        // 自动调整列宽
        for (int i = 0; i < headers.Length; i++)
        {
            sheet.AutoSizeColumn(i);
            // 设置最小列宽
            if (sheet.GetColumnWidth(i) < 15 * 256)
            {
                sheet.SetColumnWidth(i, 15 * 256);
            }
            // 设置最大列宽
            if (sheet.GetColumnWidth(i) > 50 * 256)
            {
                sheet.SetColumnWidth(i, 50 * 256);
            }
        }
    }

    /// <summary>
    /// 创建设备标签工作表
    /// </summary>
    private void CreateDeviceLabelSheet(XSSFWorkbook workbook, List<Entity.Device> devices)
    {
        // 创建设备标签工作表
        var sheet = workbook.CreateSheet("设备标签");

        // 创建样式
        var titleStyle = CreateTitleStyle(workbook);
        var headerStyle = CreateHeaderStyle(workbook);
        var dataStyle = CreateDataStyle(workbook);
        var boolStyle = CreateBoolStyle(workbook, dataStyle);

        // 创建标题行
        var titleRow = sheet.CreateRow(0);
        titleRow.Height = 30 * 20; // 设置行高
        var titleCell = titleRow.CreateCell(0);
        titleCell.SetCellValue("设备标签");
        titleCell.CellStyle = titleStyle;

        // 定义表头
        var headers = new[]
        {
            "ID", "设备ID", "设备名称", "标识符", "变量名", "转换数据类型", "数据来源",
            "执行优先级", "默认值/脚本内容/表达式", "上送方式", "采集周期(ms)",
            "强制归档时间", "长度", "描述", "是否启用", "读写方式", "读取数据类型",
            "字符串编码", "读取方法", "读取地址", "读取长度", "标签", "自定义映射规则"
        };

        // 合并标题单元格
        var columnCount = headers.Length; // 总列数
        sheet.AddMergedRegion(new CellRangeAddress(0, 0, 0, columnCount - 1));

        // 为合并后的所有单元格设置样式
        for (int i = 1; i < columnCount; i++)
        {
            var cell = titleRow.CreateCell(i);
            cell.CellStyle = titleStyle;
        }

        // 创建表头行
        var headerRow = sheet.CreateRow(1);
        headerRow.Height = 25 * 20; // 设置行高

        // 设置表头
        for (int i = 0; i < headers.Length; i++)
        {
            var cell = headerRow.CreateCell(i);
            cell.SetCellValue(headers[i]);
            cell.CellStyle = headerStyle;
        }

        // 填充数据
        int rowIndex = 2; // 从第三行开始（标题+表头后）

        foreach (var device in devices)
        {
            if (device.DeviceLabel == null || device.DeviceLabel.Count == 0)
                continue;

            foreach (var label in device.DeviceLabel)
            {
                var row = sheet.CreateRow(rowIndex++);

                // ID
                var idCell = row.CreateCell(0);
                idCell.SetCellValue(label.Id);
                idCell.CellStyle = dataStyle;

                // 设备ID
                var deviceIdCell = row.CreateCell(1);
                deviceIdCell.SetCellValue(label.DeviceId);
                deviceIdCell.CellStyle = dataStyle;

                // 设备名称
                var deviceNameCell = row.CreateCell(2);
                deviceNameCell.SetCellValue(device.Name ?? "");
                deviceNameCell.CellStyle = dataStyle;

                // 标识符
                var identifierCell = row.CreateCell(3);
                identifierCell.SetCellValue(label.Identifier);
                identifierCell.CellStyle = dataStyle;

                // 变量名
                var nameCell = row.CreateCell(4);
                nameCell.SetCellValue(label.Name);
                nameCell.CellStyle = dataStyle;

                // 转换数据类型
                var transitionTypeCell = row.CreateCell(5);
                transitionTypeCell.SetCellValue(label.TransitionType);
                transitionTypeCell.CellStyle = dataStyle;

                // 数据来源
                var valueSourceCell = row.CreateCell(6);
                valueSourceCell.SetCellValue(label.ValueSource.ToString());
                valueSourceCell.CellStyle = dataStyle;

                // 执行优先级
                var actionOrderCell = row.CreateCell(7);
                actionOrderCell.SetCellValue(label.ActionOrder);
                actionOrderCell.CellStyle = dataStyle;

                // 默认值/脚本内容/表达式
                var contentCell = row.CreateCell(8);
                contentCell.SetCellValue(label.Content ?? "");
                contentCell.CellStyle = dataStyle;

                // 上送方式
                var sendTypeCell = row.CreateCell(9);
                sendTypeCell.SetCellValue(label.SendType.ToString());
                sendTypeCell.CellStyle = dataStyle;

                // 采集周期
                var periodCell = row.CreateCell(10);
                periodCell.SetCellValue(label.Period);
                periodCell.CellStyle = dataStyle;

                // 强制归档时间
                var archiveTimeCell = row.CreateCell(11);
                archiveTimeCell.SetCellValue(label.ArchiveTime?.ToString() ?? "");
                archiveTimeCell.CellStyle = dataStyle;

                // 长度
                var lengthCell = row.CreateCell(12);
                lengthCell.SetCellValue(label.Length?.ToString() ?? "");
                lengthCell.CellStyle = dataStyle;

                // 描述
                var descCell = row.CreateCell(13);
                descCell.SetCellValue(label.Description ?? "");
                descCell.CellStyle = dataStyle;

                // 是否启用
                var enableCell = row.CreateCell(14);
                enableCell.SetCellValue(label.Enable ? "是" : "否");
                enableCell.CellStyle = boolStyle;

                // 读写方式
                var protectTypeCell = row.CreateCell(15);
                protectTypeCell.SetCellValue(label.ProtectType.ToString());
                protectTypeCell.CellStyle = dataStyle;

                // 读取数据类型
                var dataTypeCell = row.CreateCell(16);
                dataTypeCell.SetCellValue(label.DataType ?? "");
                dataTypeCell.CellStyle = dataStyle;

                // 字符串编码
                var encodingCell = row.CreateCell(17);
                encodingCell.SetCellValue(label.Encoding ?? "");
                encodingCell.CellStyle = dataStyle;

                // 读取方法
                var methodCell = row.CreateCell(18);
                methodCell.SetCellValue(label.Method ?? "");
                methodCell.CellStyle = dataStyle;

                // 读取地址
                var registerAddressCell = row.CreateCell(19);
                registerAddressCell.SetCellValue(label.RegisterAddress ?? "");
                registerAddressCell.CellStyle = dataStyle;

                // 读取长度
                var readLengthCell = row.CreateCell(20);
                readLengthCell.SetCellValue(label.ReadLength?.ToString() ?? "");
                readLengthCell.CellStyle = dataStyle;

                // 标签
                var tagsCell = row.CreateCell(21);
                tagsCell.SetCellValue(label.Tags != null ? string.Join(",", label.Tags) : "");
                tagsCell.CellStyle = dataStyle;

                // 自定义映射规则
                var customCell = row.CreateCell(22);
                customCell.SetCellValue(label.Custom != null ? System.Text.Json.JsonSerializer.Serialize(label.Custom) : "");
                customCell.CellStyle = dataStyle;
            }
        }

        // 自动调整列宽
        for (int i = 0; i < headers.Length; i++)
        {
            sheet.AutoSizeColumn(i);
            // 设置最小列宽
            if (sheet.GetColumnWidth(i) < 15 * 256)
            {
                sheet.SetColumnWidth(i, 15 * 256);
            }
            // 设置最大列宽
            if (sheet.GetColumnWidth(i) > 50 * 256)
            {
                sheet.SetColumnWidth(i, 50 * 256);
            }
        }
    }

    /// <summary>
    /// 创建设备事件工作表
    /// </summary>
    private void CreateDeviceEventSheet(XSSFWorkbook workbook, List<Entity.Device> devices)
    {
        // 创建设备事件工作表
        var sheet = workbook.CreateSheet("设备事件");

        // 创建样式
        var titleStyle = CreateTitleStyle(workbook);
        var headerStyle = CreateHeaderStyle(workbook);
        var dataStyle = CreateDataStyle(workbook);
        var boolStyle = CreateBoolStyle(workbook, dataStyle);

        // 创建标题行
        var titleRow = sheet.CreateRow(0);
        titleRow.Height = 30 * 20; // 设置行高
        var titleCell = titleRow.CreateCell(0);
        titleCell.SetCellValue("设备事件");
        titleCell.CellStyle = titleStyle;

        // 定义表头
        var headers = new[]
        {
            "ID", "设备ID", "设备名称", "事件名称", "触发方式", "是否启用", "事件配置", "事件执行条件"
        };

        // 合并标题单元格
        var columnCount = headers.Length; // 总列数
        sheet.AddMergedRegion(new CellRangeAddress(0, 0, 0, columnCount - 1));

        // 为合并后的所有单元格设置样式
        for (int i = 1; i < columnCount; i++)
        {
            var cell = titleRow.CreateCell(i);
            cell.CellStyle = titleStyle;
        }

        // 创建表头行
        var headerRow = sheet.CreateRow(1);
        headerRow.Height = 25 * 20; // 设置行高

        // 设置表头
        for (int i = 0; i < headers.Length; i++)
        {
            var cell = headerRow.CreateCell(i);
            cell.SetCellValue(headers[i]);
            cell.CellStyle = headerStyle;
        }

        // 填充数据
        int rowIndex = 2; // 从第三行开始（标题+表头后）

        // 自动调整列宽
        for (int i = 0; i < headers.Length; i++)
        {
            sheet.AutoSizeColumn(i);
            // 设置最小列宽
            if (sheet.GetColumnWidth(i) < 15 * 256)
            {
                sheet.SetColumnWidth(i, 15 * 256);
            }
            // 设置最大列宽
            if (sheet.GetColumnWidth(i) > 50 * 256)
            {
                sheet.SetColumnWidth(i, 50 * 256);
            }
        }
    }

    /// <summary>
    /// 创建标题样式
    /// </summary>
    private ICellStyle CreateTitleStyle(XSSFWorkbook workbook)
    {
        var titleStyle = workbook.CreateCellStyle();
        var titleFont = workbook.CreateFont();
        titleFont.FontHeightInPoints = 14;
        titleFont.IsBold = true;
        titleStyle.SetFont(titleFont);
        titleStyle.Alignment = NPOI.SS.UserModel.HorizontalAlignment.Center;
        titleStyle.VerticalAlignment = NPOI.SS.UserModel.VerticalAlignment.Center;

        // 设置标题行背景色为浅蓝色
        titleStyle.FillForegroundColor = IndexedColors.PaleBlue.Index;
        titleStyle.FillPattern = FillPattern.SolidForeground;

        // 设置标题行边框
        titleStyle.BorderTop = BorderStyle.Thin;
        titleStyle.BorderBottom = BorderStyle.Thin;
        titleStyle.BorderLeft = BorderStyle.Thin;
        titleStyle.BorderRight = BorderStyle.Thin;

        return titleStyle;
    }

    /// <summary>
    /// 创建表头样式
    /// </summary>
    private ICellStyle CreateHeaderStyle(XSSFWorkbook workbook)
    {
        var headerStyle = workbook.CreateCellStyle();
        var headerFont = workbook.CreateFont();
        headerFont.FontHeightInPoints = 12;
        headerFont.IsBold = true;
        headerStyle.SetFont(headerFont);
        headerStyle.Alignment = NPOI.SS.UserModel.HorizontalAlignment.Center;
        headerStyle.VerticalAlignment = NPOI.SS.UserModel.VerticalAlignment.Center;

        // 设置表头背景色为灰色
        headerStyle.FillForegroundColor = IndexedColors.Grey25Percent.Index;
        headerStyle.FillPattern = FillPattern.SolidForeground;

        // 设置表头边框
        headerStyle.BorderTop = BorderStyle.Thin;
        headerStyle.BorderBottom = BorderStyle.Thin;
        headerStyle.BorderLeft = BorderStyle.Thin;
        headerStyle.BorderRight = BorderStyle.Thin;

        return headerStyle;
    }

    /// <summary>
    /// 创建数据单元格样式
    /// </summary>
    private ICellStyle CreateDataStyle(XSSFWorkbook workbook)
    {
        var dataStyle = workbook.CreateCellStyle();
        dataStyle.Alignment = NPOI.SS.UserModel.HorizontalAlignment.Left;
        dataStyle.VerticalAlignment = NPOI.SS.UserModel.VerticalAlignment.Center;

        // 设置数据单元格边框
        dataStyle.BorderTop = BorderStyle.Thin;
        dataStyle.BorderBottom = BorderStyle.Thin;
        dataStyle.BorderLeft = BorderStyle.Thin;
        dataStyle.BorderRight = BorderStyle.Thin;

        // 设置自动换行
        dataStyle.WrapText = true;

        return dataStyle;
    }

    /// <summary>
    /// 创建布尔值样式
    /// </summary>
    private ICellStyle CreateBoolStyle(XSSFWorkbook workbook, ICellStyle dataStyle)
    {
        var boolStyle = workbook.CreateCellStyle();
        boolStyle.CloneStyleFrom(dataStyle);
        boolStyle.Alignment = NPOI.SS.UserModel.HorizontalAlignment.Center;
        return boolStyle;
    }

    /// <summary>
    ///     导入设备配置Excel
    /// </summary>
    /// <param name="input">Excel文件</param>
    /// <returns>导入结果</returns>
    [HttpPost("import-excel")]
    [OperationId(nameof(ImportExcel))]
    public async Task<ImportExcelResultDto> ImportExcel([FromForm] ImportExcelDeviceDto input)
    {
        if (input == null || input.File == null || input.File.Length == 0)
            throw Oops.Oh(ErrorCode.D1000, "请选择要导入的Excel文件");

        var result = new ImportExcelResultDto
        {
            DeviceCount = 0,
            DeviceLabelCount = 0,
            DeviceEventCount = 0,
            ErrorMessages = new List<string>()
        };

        try
        {
            // 读取Excel文件
            using var stream = input.File.OpenReadStream();
            var workbook = new XSSFWorkbook(stream);

            // 开始事务
            var db = _device.AsSugarClient();
            await db.AsTenant().BeginTranAsync();

            try
            {
                // 导入设备
                var deviceSheet = workbook.GetSheet("设备列表");
                if (deviceSheet != null)
                {
                    var deviceResult = await ImportDevices(deviceSheet, db);
                    result.DeviceCount = deviceResult.Item1;
                    if (deviceResult.Item2.Count > 0)
                        result.ErrorMessages.AddRange(deviceResult.Item2);
                }

                // 导入设备标签
                var labelSheet = workbook.GetSheet("设备标签");
                if (labelSheet != null)
                {
                    var labelResult = await ImportDeviceLabels(labelSheet, db);
                    result.DeviceLabelCount = labelResult.Item1;
                    if (labelResult.Item2.Count > 0)
                        result.ErrorMessages.AddRange(labelResult.Item2);
                }

                // 导入设备事件
                var eventSheet = workbook.GetSheet("设备事件");
                if (eventSheet != null)
                {
                    var eventResult = await ImportDeviceEvents(eventSheet, db);
                    result.DeviceEventCount = eventResult.Item1;
                    if (eventResult.Item2.Count > 0)
                        result.ErrorMessages.AddRange(eventResult.Item2);
                }

                // 提交事务
                await db.AsTenant().CommitTranAsync();
            }
            catch (Exception ex)
            {
                // 回滚事务
                await db.AsTenant().RollbackTranAsync();
                _logger.LogError(ex, "导入设备Excel失败");
                result.ErrorMessages.Add($"导入失败：{ex.Message}");
            }

            return result;
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "导入设备Excel失败");
            throw Oops.Oh(ErrorCode.D1000, $"导入设备Excel失败：{ex.Message}");
        }
    }

    /// <summary>
    /// 获取设备列索引
    /// </summary>
    private Dictionary<string, int> GetDeviceColumnIndexes(IRow headerRow)
    {
        var columnIndexes = new Dictionary<string, int>();
        var requiredColumns = new[]
        {
            "标识符", "名称", "通道", "驱动名称", "驱动版本", "轮询周期(ms)",
            "恢复等待时间(ms)", "恢复时间(ms)", "数据上报方式", "存储历史数据",
            "是否启用", "是否允许写入", "描述", "分组标签", "位置", "设备配置"
        };

        for (int i = 0; i < headerRow.LastCellNum; i++)
        {
            var cell = headerRow.GetCell(i);
            if (cell != null)
            {
                var columnName = cell.StringCellValue;
                columnIndexes[columnName] = i;
            }
        }

        // 检查必需列是否存在
        foreach (var column in requiredColumns)
        {
            if (!columnIndexes.ContainsKey(column))
            {
                columnIndexes[column] = -1; // 设置为-1表示列不存在
            }
        }

        return columnIndexes;
    }

    /// <summary>
    /// 导入设备数据
    /// </summary>
    private async Task<Tuple<int, List<string>>> ImportDevices(ISheet sheet, ISqlSugarClient db)
    {
        var importCount = 0;
        var errorMessages = new List<string>();

        try
        {
            // 获取表头行
            var headerRow = sheet.GetRow(1);
            if (headerRow == null)
            {
                errorMessages.Add("设备列表工作表格式不正确，缺少表头行");
                return Tuple.Create(importCount, errorMessages);
            }

            // 获取列索引
            var columnIndexes = GetDeviceColumnIndexes(headerRow);

            // 读取数据行
            for (int i = 2; i <= sheet.LastRowNum; i++)
            {
                var row = sheet.GetRow(i);
                if (row == null) continue;

                try
                {
                    // 创建设备对象
                    var device = new Entity.Device
                    {
                        Identifier = GetCellStringValue(row, columnIndexes["标识符"]),
                        Name = GetCellStringValue(row, columnIndexes["名称"]),
                        Enable = GetCellBoolValue(row, columnIndexes["是否启用"]),
                        AllowWrite = GetCellBoolValue(row, columnIndexes["是否允许写入"]),
                        Description = GetCellStringValue(row, columnIndexes["描述"]),
                        DeviceInfo = new DeviceInfo(
                            minPeriod: GetCellIntValue(row, columnIndexes["轮询周期(ms)"]),
                            waitTime: GetCellIntValue(row, columnIndexes["恢复等待时间(ms)"]),
                            reConnTime: GetCellIntValue(row, columnIndexes["恢复时间(ms)"]),
                            reportType: ParseEnum<DataReportTypeEnum>(GetCellStringValue(row, columnIndexes["数据上报方式"])),
                            storeHistoryData: GetCellBoolValue(row, columnIndexes["存储历史数据"])
                        ),
                        Driver = new DeviceDriver
                        {
                            DriverName = GetCellStringValue(row, columnIndexes["驱动名称"]),
                            Version = GetCellStringValue(row, columnIndexes["驱动版本"])
                        }
                    };

                    // 设置分组标签
                    if (columnIndexes.ContainsKey("分组标签") && columnIndexes["分组标签"] >= 0)
                    {
                        var groupTagsStr = GetCellStringValue(row, columnIndexes["分组标签"]);
                        if (!string.IsNullOrEmpty(groupTagsStr))
                        {
                            device.GroupTags = groupTagsStr.Split(',').Where(t => !string.IsNullOrEmpty(t)).ToList();
                        }
                    }

                    // 设置位置
                    if (columnIndexes.ContainsKey("位置") && columnIndexes["位置"] >= 0)
                    {
                        device.Location = GetCellStringValue(row, columnIndexes["位置"]);
                    }

                    // 设置通道ID
                    var channelName = GetCellStringValue(row, columnIndexes["通道"]);
                    if (!string.IsNullOrEmpty(channelName))
                    {
                        var channel = await db.Queryable<EdgeChannel>()
                            .Where(c => c.ChannelName == channelName)
                            .FirstAsync();
                        if (channel != null)
                        {
                            device.ChannelId = channel.Id;
                        }
                    }

                    // 设置设备配置
                    var configJson = GetCellStringValue(row, columnIndexes["设备配置"]);
                    if (!string.IsNullOrEmpty(configJson))
                    {
                        try
                        {
                            device.DeviceConfigs = System.Text.Json.JsonSerializer.Deserialize<List<DeviceConfig>>(configJson);
                        }
                        catch
                        {
                            errorMessages.Add($"第{i + 1}行设备配置JSON格式不正确");
                        }
                    }

                    // 检查设备是否已存在
                    var existingDevice = await db.Queryable<Entity.Device>()
                        .Where(d => d.Identifier == device.Identifier)
                        .FirstAsync();

                    if (existingDevice != null)
                    {
                        // 更新现有设备
                        device.Id = existingDevice.Id;
                        await db.Updateable(device).ExecuteCommandAsync();
                    }
                    else
                    {
                        // 插入新设备
                        await db.Insertable(device).ExecuteCommandAsync();
                    }

                    importCount++;
                }
                catch (Exception ex)
                {
                    errorMessages.Add($"导入第{i + 1}行设备数据失败：{ex.Message}");
                }
            }
        }
        catch (Exception ex)
        {
            errorMessages.Add($"导入设备数据失败：{ex.Message}");
        }

        return Tuple.Create(importCount, errorMessages);
    }

    /// <summary>
    /// 导入设备标签数据
    /// </summary>
    private async Task<Tuple<int, List<string>>> ImportDeviceLabels(ISheet sheet, ISqlSugarClient db)
    {
        var importCount = 0;
        var errorMessages = new List<string>();

        try
        {
            // 获取表头行
            var headerRow = sheet.GetRow(1);
            if (headerRow == null)
            {
                errorMessages.Add("设备标签工作表格式不正确，缺少表头行");
                return Tuple.Create(importCount, errorMessages);
            }

            // 获取列索引
            var columnIndexes = GetDeviceLabelColumnIndexes(headerRow);

            // 读取数据行
            for (int i = 2; i <= sheet.LastRowNum; i++)
            {
                var row = sheet.GetRow(i);
                if (row == null) continue;

                try
                {
                    // 获取设备ID
                    var deviceId = GetCellLongValue(row, columnIndexes["设备ID"]);
                    if (deviceId <= 0)
                    {
                        // 尝试通过设备名称查找设备
                        var deviceName = GetCellStringValue(row, columnIndexes["设备名称"]);
                        if (!string.IsNullOrEmpty(deviceName))
                        {
                            var device = await db.Queryable<Entity.Device>()
                                .Where(d => d.Name == deviceName)
                                .FirstAsync();
                            if (device != null)
                            {
                                deviceId = device.Id;
                            }
                            else
                            {
                                errorMessages.Add($"第{i + 1}行标签数据无法找到对应的设备：{deviceName}");
                                continue;
                            }
                        }
                        else
                        {
                            errorMessages.Add($"第{i + 1}行标签数据缺少设备ID或设备名称");
                            continue;
                        }
                    }

                    // 检查设备是否存在
                    var deviceExists = await db.Queryable<Entity.Device>()
                        .AnyAsync(d => d.Id == deviceId);
                    if (!deviceExists)
                    {
                        errorMessages.Add($"第{i + 1}行标签数据对应的设备ID不存在：{deviceId}");
                        continue;
                    }

                    // 创建标签对象
                    var label = new DeviceLabel
                    {
                        DeviceId = deviceId,
                        Identifier = GetCellStringValue(row, columnIndexes["标识符"]),
                        Name = GetCellStringValue(row, columnIndexes["变量名"]),
                        TransitionType = GetCellStringValue(row, columnIndexes["转换数据类型"]),
                        ValueSource = ParseEnum<ValueSourceEnum>(GetCellStringValue(row, columnIndexes["数据来源"])),
                        ActionOrder = (short)GetCellIntValue(row, columnIndexes["执行优先级"]),
                        Content = GetCellStringValue(row, columnIndexes["默认值/脚本内容/表达式"]),
                        SendType = ParseEnum<SendTypeEnum>(GetCellStringValue(row, columnIndexes["上送方式"])),
                        Period = GetCellIntValue(row, columnIndexes["采集周期(ms)"]),
                        Description = GetCellStringValue(row, columnIndexes["描述"]),
                        Enable = GetCellBoolValue(row, columnIndexes["是否启用"]),
                        ProtectType = ParseEnum<ProtectTypeEnum>(GetCellStringValue(row, columnIndexes["读写方式"])),
                        DataType = GetCellStringValue(row, columnIndexes["读取数据类型"]),
                        Encoding = GetCellStringValue(row, columnIndexes["字符串编码"]),
                        Method = GetCellStringValue(row, columnIndexes["读取方法"]),
                        RegisterAddress = GetCellStringValue(row, columnIndexes["读取地址"])
                    };

                    // 设置可空字段
                    var archiveTimeStr = GetCellStringValue(row, columnIndexes["强制归档时间"]);
                    if (!string.IsNullOrEmpty(archiveTimeStr) && int.TryParse(archiveTimeStr, out int archiveTime))
                    {
                        label.ArchiveTime = archiveTime;
                    }

                    var lengthStr = GetCellStringValue(row, columnIndexes["长度"]);
                    if (!string.IsNullOrEmpty(lengthStr) && ushort.TryParse(lengthStr, out ushort length))
                    {
                        label.Length = length;
                    }

                    var readLengthStr = GetCellStringValue(row, columnIndexes["读取长度"]);
                    if (!string.IsNullOrEmpty(readLengthStr) && ushort.TryParse(readLengthStr, out ushort readLength))
                    {
                        label.ReadLength = readLength;
                    }

                    // 设置标签
                    var tagsStr = GetCellStringValue(row, columnIndexes["标签"]);
                    if (!string.IsNullOrEmpty(tagsStr))
                    {
                        label.Tags = tagsStr.Split(',').ToList();
                    }

                    // 设置自定义映射规则
                    var customJson = GetCellStringValue(row, columnIndexes["自定义映射规则"]);
                    if (!string.IsNullOrEmpty(customJson))
                    {
                        try
                        {
                            label.Custom = System.Text.Json.JsonSerializer.Deserialize<Dictionary<string, object>>(customJson);
                        }
                        catch
                        {
                            errorMessages.Add($"第{i + 1}行标签自定义映射规则JSON格式不正确");
                        }
                    }

                    // 检查标签是否已存在
                    var existingLabel = await db.Queryable<DeviceLabel>()
                        .Where(l => l.DeviceId == label.DeviceId && l.Identifier == label.Identifier)
                        .FirstAsync();

                    if (existingLabel != null)
                    {
                        // 更新现有标签
                        label.Id = existingLabel.Id;
                        await db.Updateable(label).ExecuteCommandAsync();
                    }
                    else
                    {
                        // 插入新标签
                        await db.Insertable(label).ExecuteCommandAsync();
                    }

                    importCount++;
                }
                catch (Exception ex)
                {
                    errorMessages.Add($"导入第{i + 1}行标签数据失败：{ex.Message}");
                }
            }
        }
        catch (Exception ex)
        {
            errorMessages.Add($"导入标签数据失败：{ex.Message}");
        }

        return Tuple.Create(importCount, errorMessages);
    }

    /// <summary>
    /// 获取设备标签列索引
    /// </summary>
    private Dictionary<string, int> GetDeviceLabelColumnIndexes(IRow headerRow)
    {
        var columnIndexes = new Dictionary<string, int>();
        var requiredColumns = new[]
        {
            "设备ID", "设备名称", "标识符", "变量名", "转换数据类型", "数据来源",
            "执行优先级", "默认值/脚本内容/表达式", "上送方式", "采集周期(ms)",
            "强制归档时间", "长度", "描述", "是否启用", "读写方式", "读取数据类型",
            "字符串编码", "读取方法", "读取地址", "读取长度", "标签", "自定义映射规则"
        };

        for (int i = 0; i < headerRow.LastCellNum; i++)
        {
            var cell = headerRow.GetCell(i);
            if (cell != null)
            {
                var columnName = cell.StringCellValue;
                columnIndexes[columnName] = i;
            }
        }

        // 检查必需列是否存在
        foreach (var column in requiredColumns)
        {
            if (!columnIndexes.ContainsKey(column))
            {
                columnIndexes[column] = -1; // 设置为-1表示列不存在
            }
        }

        return columnIndexes;
    }

    /// <summary>
    /// 导入设备事件数据
    /// </summary>
    private async Task<Tuple<int, List<string>>> ImportDeviceEvents(ISheet sheet, ISqlSugarClient db)
    {
        var importCount = 0;
        var errorMessages = new List<string>();

        try
        {
            // 获取表头行
            var headerRow = sheet.GetRow(1);
            if (headerRow == null)
            {
                errorMessages.Add("设备事件工作表格式不正确，缺少表头行");
                return Tuple.Create(importCount, errorMessages);
            }

            // 获取列索引
            var columnIndexes = GetDeviceEventColumnIndexes(headerRow);

            // 读取数据行
            for (int i = 2; i <= sheet.LastRowNum; i++)
            {
                var row = sheet.GetRow(i);
                if (row == null) continue;

                try
                {
                    // 获取设备ID
                    var deviceId = GetCellLongValue(row, columnIndexes["设备ID"]);
                    if (deviceId <= 0)
                    {
                        // 尝试通过设备名称查找设备
                        var deviceName = GetCellStringValue(row, columnIndexes["设备名称"]);
                        if (!string.IsNullOrEmpty(deviceName))
                        {
                            var device = await db.Queryable<Entity.Device>()
                                .Where(d => d.Name == deviceName)
                                .FirstAsync();
                            if (device != null)
                            {
                                deviceId = device.Id;
                            }
                            else
                            {
                                errorMessages.Add($"第{i + 1}行事件数据无法找到对应的设备：{deviceName}");
                                continue;
                            }
                        }
                        else
                        {
                            errorMessages.Add($"第{i + 1}行事件数据缺少设备ID或设备名称");
                            continue;
                        }
                    }

                    // 检查设备是否存在
                    var deviceExists = await db.Queryable<Entity.Device>()
                        .AnyAsync(d => d.Id == deviceId);
                    if (!deviceExists)
                    {
                        errorMessages.Add($"第{i + 1}行事件数据对应的设备ID不存在：{deviceId}");
                        continue;
                    }

                    importCount++;
                }
                catch (Exception ex)
                {
                    errorMessages.Add($"导入第{i + 1}行事件数据失败：{ex.Message}");
                }
            }
        }
        catch (Exception ex)
        {
            errorMessages.Add($"导入事件数据失败：{ex.Message}");
        }

        return Tuple.Create(importCount, errorMessages);
    }

    /// <summary>
    /// 获取设备事件列索引
    /// </summary>
    private Dictionary<string, int> GetDeviceEventColumnIndexes(IRow headerRow)
    {
        var columnIndexes = new Dictionary<string, int>();
        var requiredColumns = new[]
        {
            "设备ID", "设备名称", "事件名称", "触发方式", "是否启用", "事件配置", "事件执行条件"
        };

        for (int i = 0; i < headerRow.LastCellNum; i++)
        {
            var cell = headerRow.GetCell(i);
            if (cell != null)
            {
                var columnName = cell.StringCellValue;
                columnIndexes[columnName] = i;
            }
        }

        // 检查必需列是否存在
        foreach (var column in requiredColumns)
        {
            if (!columnIndexes.ContainsKey(column))
            {
                columnIndexes[column] = -1; // 设置为-1表示列不存在
            }
        }

        return columnIndexes;
    }

    /// <summary>
    /// 获取单元格字符串值
    /// </summary>
    private string GetCellStringValue(IRow row, int columnIndex)
    {
        if (columnIndex < 0 || columnIndex >= row.LastCellNum)
            return string.Empty;

        var cell = row.GetCell(columnIndex);
        if (cell == null)
            return string.Empty;

        switch (cell.CellType)
        {
            case CellType.String:
                return cell.StringCellValue;
            case CellType.Numeric:
                return cell.NumericCellValue.ToString();
            case CellType.Boolean:
                return cell.BooleanCellValue.ToString();
            default:
                return string.Empty;
        }
    }

    /// <summary>
    /// 获取单元格整数值
    /// </summary>
    private int GetCellIntValue(IRow row, int columnIndex)
    {
        if (columnIndex < 0 || columnIndex >= row.LastCellNum)
            return 0;

        var cell = row.GetCell(columnIndex);
        if (cell == null)
            return 0;

        switch (cell.CellType)
        {
            case CellType.Numeric:
                return (int)cell.NumericCellValue;
            case CellType.String:
                if (int.TryParse(cell.StringCellValue, out int result))
                    return result;
                return 0;
            default:
                return 0;
        }
    }

    /// <summary>
    /// 获取单元格长整数值
    /// </summary>
    private long GetCellLongValue(IRow row, int columnIndex)
    {
        if (columnIndex < 0 || columnIndex >= row.LastCellNum)
            return 0;

        var cell = row.GetCell(columnIndex);
        if (cell == null)
            return 0;

        switch (cell.CellType)
        {
            case CellType.Numeric:
                return (long)cell.NumericCellValue;
            case CellType.String:
                if (long.TryParse(cell.StringCellValue, out long result))
                    return result;
                return 0;
            default:
                return 0;
        }
    }

    /// <summary>
    /// 获取单元格布尔值
    /// </summary>
    private bool GetCellBoolValue(IRow row, int columnIndex)
    {
        if (columnIndex < 0 || columnIndex >= row.LastCellNum)
            return false;

        var cell = row.GetCell(columnIndex);
        if (cell == null)
            return false;

        switch (cell.CellType)
        {
            case CellType.Boolean:
                return cell.BooleanCellValue;
            case CellType.String:
                var value = cell.StringCellValue.Trim();
                return value == "是" || value == "true" || value == "True" || value == "TRUE" || value == "1";
            case CellType.Numeric:
                return cell.NumericCellValue > 0;
            default:
                return false;
        }
    }

    /// <summary>
    /// 解析枚举值
    /// </summary>
    private T ParseEnum<T>(string value) where T : struct
    {
        if (string.IsNullOrEmpty(value))
            return default;

        if (Enum.TryParse<T>(value, true, out T result))
            return result;

        return default;
    }

    #region 设备调试

    /// <summary>
    /// 手动读取设备数据
    /// </summary>
    /// <param name="input">读取参数</param>
    /// <returns>读取结果</returns>
    [HttpGet("manual-read")]
    [OperationId(nameof(ManualRead))]
    public async Task<List<ReadDataResult>> ManualRead([FromQuery] ManualReadInput input)
    {
        // 获取设备信息
        var device = await _device.GetByIdAsync(input.DeviceId) ?? throw Oops.Oh(ErrorCode.D1000);

        // 调用手动读取方法
        var result = await _edgeGateway.ManualRead(input.DeviceId, input.Address, input.Length, input.DataType, input.Encoding);

        return result;
    }

    /// <summary>
    /// 手动写入设备数据
    /// </summary>
    /// <param name="input"></param>
    /// <returns>写入结果</returns>
    [HttpPost("manual-write")]
    [OperationId(nameof(ManualWrite))]
    public async Task<List<Dictionary<string, object>>> ManualWrite([FromBody] ManualWriteInput input)
    {
        // 获取设备信息
        var device = await _device.GetByIdAsync(input.DeviceId) ?? throw Oops.Oh(ErrorCode.D1000);
        // 如果设备不允许写入
        if (!device.AllowWrite)
            throw Oops.Oh(ErrorCode.D1000, "设备不允许写入");
        // 调用手动写入方法
        var result = await _edgeGateway.ManualWrite(input.DeviceId, input.Address, input.Value,
            input.DataType, input.Length, input.Encoding);

        return result;
    }

    /// <summary>
    ///     拷贝设备
    /// </summary>
    /// <param name="id">设备ID</param>
    /// <returns>新设备ID</returns>
    [HttpPost("copy/{id}")]
    [OperationId(nameof(Copy))]
    public async Task<long> Copy(long id)
    {
        // 获取原始设备及其标签配置
        var sourceDevice = await _device.AsQueryable()
            .Where(w => w.Id == id)
            .Includes(w => w.DeviceLabel)
            .FirstAsync() ?? throw Oops.Oh(ErrorCode.D1000);

        // 使用Adapt映射创建新设备对象
        var newDevice = sourceDevice.Adapt<Entity.Device>();

        // 生成新ID
        newDevice.Id = YitIdHelper.NextId();
        // 修改设备名称和标识符
        newDevice.Name = $"{sourceDevice.Name}-副本";
        newDevice.Identifier = $"{sourceDevice.Identifier}-copy";
        // 确保设备启用
        newDevice.Enable = true;
        // 复制分组标签
        newDevice.GroupTags = sourceDevice.GroupTags != null
            ? new List<string>(sourceDevice.GroupTags)
            : new List<string>();
        // 复制位置信息
        newDevice.Location = sourceDevice.Location;

        // 验证标识符是否重复
        if (await _device.IsAnyAsync(a => a.Identifier == newDevice.Identifier))
            newDevice.Identifier = $"{sourceDevice.Identifier}-copy-{DateTime.Now:yyyyMMddHHmmss}";

        // 处理设备标签配置
        if (sourceDevice.DeviceLabel != null && sourceDevice.DeviceLabel.Count > 0)
        {
            // 创建新的标签列表
            newDevice.DeviceLabel = new List<DeviceLabel>();

            // 复制设备标签配置
            foreach (var label in sourceDevice.DeviceLabel)
            {
                // 使用Adapt映射创建新标签
                var newLabel = label.Adapt<DeviceLabel>();
                // 设置新ID和设备ID
                newLabel.Id = YitIdHelper.NextId();
                newLabel.DeviceId = newDevice.Id;

                // 添加到新设备的标签列表
                newDevice.DeviceLabel.Add(newLabel);
            }
        }

        // 确保设备禁用
        newDevice.Enable = false;

        // 保存新设备及其标签配置
        var isAdd = await _device.AsSugarClient().InsertNav(newDevice)
            .Include(w => w.DeviceLabel)
            .ExecuteCommandAsync();

        // 添加成功后直接启动
        if (isAdd)
            await _edgeGateway.CreateDeviceThread(newDevice);

        // 更新设备数量统计 
        UpdateDeviceCount();

        return newDevice.Id;
    }

    /// <summary>
    ///     启动设备采集
    /// </summary>
    /// <param name="id">设备ID</param>
    /// <returns></returns>
    [HttpPost("start-collection/{id}")]
    [OperationId(nameof(StartCollection))]
    public async Task StartCollection(long id)
    {
        try
        {
            // 获取设备信息
            var device = await _device.AsQueryable()
                .Where(w => w.Id == id)
                .Includes(w => w.DeviceLabel.Where(l => l.Enable).ToList())
                .FirstAsync() ?? throw Oops.Oh(ErrorCode.D1000, $"设备不存在：{id}");

            // 检查设备是否已启用
            if (!device.Enable)
            {
                throw Oops.Oh(ErrorCode.D1000, $"设备 {device.Name} 未启用，无法启动采集");
            }

            // 检查设备是否已在采集中
            if (_edgeGateway.DeviceThreads.ContainsKey(device.Id))
            {
                throw Oops.Oh(ErrorCode.D1000, $"设备 {device.Name} 已在采集中");
            }

            // 启动设备采集
            await _edgeGateway.CreateDeviceThread(device);

            _logger.LogInformation($"设备 {device.Name}({device.Identifier}) 启动采集成功");
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, $"启动设备采集失败: DeviceId={id}");
            throw Oops.Oh($"启动设备采集失败: {ex.Message}");
        }
    }

    /// <summary>
    ///     停止设备采集
    /// </summary>
    /// <param name="id">设备ID</param>
    /// <returns></returns>
    [HttpPost("stop-collection/{id}")]
    [OperationId(nameof(StopCollection))]
    public async Task StopCollection(long id)
    {
        try
        {
            // 获取设备信息
            var device = await _device.AsQueryable()
                .Where(w => w.Id == id)
                .Includes(w => w.DeviceLabel)
                .FirstAsync() ?? throw Oops.Oh(ErrorCode.D1000, $"设备不存在：{id}");

            // 检查设备是否在采集中
            if (!_edgeGateway.DeviceThreads.ContainsKey(device.Id))
            {
                throw Oops.Oh(ErrorCode.D1000, $"设备 {device.Name} 未在采集中");
            }

            // 停止设备采集
            await _edgeGateway.StopDeviceThread(device);

            _logger.LogInformation($"设备 {device.Name}({device.Identifier}) 停止采集成功");

        }
        catch (Exception ex)
        {
            _logger.LogError(ex, $"停止设备采集失败: DeviceId={id}");
            throw Oops.Oh($"停止设备采集失败: {ex.Message}");
        }
    }

    #endregion

    #region 设备诊断

    /// <summary>
    /// 设备综合诊断
    /// </summary>
    /// <param name="input">诊断参数</param>
    /// <returns>诊断结果</returns>
    [HttpPost("diagnostic")]
    [OperationId(nameof(GetDeviceDiagnostic))]
    public async Task<DeviceDiagnosticDto> GetDeviceDiagnostic([FromBody] DeviceDiagnosticInput input)
    {
        try
        {
            // 验证设备是否存在
            var device = await _device.GetByIdAsync(input.DeviceId)
                ?? throw Oops.Oh(ErrorCode.D1000, $"设备不存在：{input.DeviceId}");

            var diagnostic = new DeviceDiagnosticDto
            {
                DeviceId = device.Id,
                DeviceIdentifier = device.Identifier,
                DeviceName = device.Name ?? string.Empty,
                DiagnosticTime = DateTime.Now
            };

            // 并行执行各项诊断
            var tasks = new List<Task>();

            if (input.IncludeConnection)
                tasks.Add(Task.Run(async () => diagnostic.Connection = await GetConnectionDiagnostic(device)));

            if (input.IncludePerformance)
                tasks.Add(Task.Run(async () => diagnostic.Performance = await GetPerformanceDiagnostic(device)));

            if (input.IncludeHealth)
                tasks.Add(Task.Run(async () => diagnostic.Health = await GetHealthDiagnostic(device)));

            if (input.IncludeConfiguration)
                tasks.Add(Task.Run(async () => diagnostic.Configuration = await GetConfigurationDiagnostic(device)));

            if (input.IncludeData)
                tasks.Add(Task.Run(async () => diagnostic.Data = await GetDataDiagnostic(device)));

            if (input.IncludeHistory)
                tasks.Add(Task.Run(async () => diagnostic.History = await GetHistoryDiagnostic(device, input)));

            // 等待所有诊断任务完成
            await Task.WhenAll(tasks);

            // 计算综合健康评分
            diagnostic.OverallHealthScore = CalculateOverallHealthScore(diagnostic);
            diagnostic.OverallStatus = GetOverallStatus(diagnostic.OverallHealthScore);

            // 生成诊断建议
            diagnostic.Recommendations = GenerateRecommendations(diagnostic);

            return diagnostic;
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "设备诊断失败: DeviceId={DeviceId}", input.DeviceId);
            throw Oops.Oh($"设备诊断失败: {ex.Message}");
        }
    }

    /// <summary>
    /// 获取连接诊断信息
    /// </summary>
    private async Task<ConnectionDiagnostic> GetConnectionDiagnostic(Entity.Device device)
    {
        var cacheKey = $"connection_diagnostic_{device.Id}";
        if (_memoryCache.TryGetValue(cacheKey, out ConnectionDiagnostic? cached) && cached != null)
        {
            return cached;
        }

        var diagnostic = new ConnectionDiagnostic();

        // 检查设备线程状态
        if (_edgeGateway.DeviceThreads.TryGetValue(device.Id, out var thread))
        {
            diagnostic.IsConnected = thread.Driver.IsConnected;
            diagnostic.LastActiveTime = thread.LastActiveTime;
            diagnostic.ProtocolStatus = thread.Driver.IsConnected ? "已连接" : "未连接";
            diagnostic.LastConnectedTime = thread.LastActiveTime;
        }
        else
        {
            diagnostic.IsConnected = false;
            diagnostic.ProtocolStatus = "设备线程未运行";
        }

        // 计算连接质量评分
        if (diagnostic.IsConnected)
        {
            diagnostic.ConnectionQuality = 100.0;
        }
        else
        {
            diagnostic.ConnectionQuality = 0.0;
        }

        // 查询历史连接数据来计算稳定性
        var oneDayAgo = DateTime.Now.AddDays(-1);
        var diagnosticHistory = await _diagnosticInfo.AsQueryable()
            .Where(d => d.DeviceId == device.Id && d.CreateTime >= oneDayAgo)
            .Where(d => d.DiagnosticType == DiagnosticTypeEnum.HealthCheck)
            .OrderByDescending(d => d.CreateTime)
            .Take(100)
            .ToListAsync();

        if (diagnosticHistory.Any())
        {
            var successCount = diagnosticHistory.Count(d => d.Level <= DiagnosticLevelEnum.Info);
            diagnostic.ConnectionStability = (double)successCount / diagnosticHistory.Count * 100;
        }
        else
        {
            diagnostic.ConnectionStability = diagnostic.IsConnected ? 100.0 : 0.0;
        }

        // 缓存1分钟
        _memoryCache.Set(cacheKey, diagnostic, TimeSpan.FromMinutes(1));
        return diagnostic;
    }

    /// <summary>
    /// 获取性能诊断信息
    /// </summary>
    private async Task<PerformanceDiagnostic> GetPerformanceDiagnostic(Entity.Device device)
    {
        var cacheKey = $"performance_diagnostic_{device.Id}";
        if (_memoryCache.TryGetValue(cacheKey, out PerformanceDiagnostic? cached) && cached != null)
        {
            return cached;
        }

        var diagnostic = new PerformanceDiagnostic();

        // 获取最近的性能数据
        var recentHealth = await _healthStatus.AsQueryable()
            .Where(h => h.DeviceId == device.Id)
            .OrderByDescending(h => h.LastCheckTime)
            .FirstAsync();

        if (recentHealth != null)
        {
            diagnostic.AvgResponseTime = recentHealth.AvgExecutionTime;
            diagnostic.MaxResponseTime = recentHealth.MaxExecutionTime;

            // 从性能历史中获取CPU和内存数据
            if (recentHealth.PerformanceHistory?.Any() == true)
            {
                var latestPerf = recentHealth.PerformanceHistory
                    .OrderByDescending(p => p.RecordTime)
                    .FirstOrDefault();

                if (latestPerf != null)
                {
                    diagnostic.CpuUsage = latestPerf.CpuUsage;
                    diagnostic.MemoryUsage = latestPerf.MemoryUsage;
                }
            }
        }

        // 计算数据吞吐量
        if (_edgeGateway.DeviceThreads.TryGetValue(device.Id, out var thread))
        {
            // 这里可以从设备线程获取实时吞吐量数据
            diagnostic.DataThroughput = CalculateDataThroughput(device.Id);
        }

        // 分析性能趋势
        diagnostic.Trend = await AnalyzePerformanceTrend(device.Id);

        // 缓存30秒
        _memoryCache.Set(cacheKey, diagnostic, TimeSpan.FromSeconds(30));
        return diagnostic;
    }

    /// <summary>
    /// 获取健康诊断信息
    /// </summary>
    private async Task<HealthDiagnostic> GetHealthDiagnostic(Entity.Device device)
    {
        var diagnostic = new HealthDiagnostic();

        // 获取设备健康状态
        var healthStatus = await _healthStatus.AsQueryable()
            .Where(h => h.DeviceId == device.Id)
            .OrderByDescending(h => h.LastCheckTime)
            .FirstAsync();

        if (healthStatus != null)
        {
            diagnostic.HealthStatus = healthStatus.Status;
            diagnostic.TotalTriggerCount = healthStatus.TriggerCount;
            diagnostic.FailureCount = healthStatus.FailureCount;
            diagnostic.LastErrorMessage = healthStatus.LastErrorMessage;

            // 计算成功率
            if (healthStatus.TriggerCount > 0)
            {
                diagnostic.TriggerSuccessRate = ((double)(healthStatus.TriggerCount - healthStatus.FailureCount)
                    / healthStatus.TriggerCount) * 100;
            }
            else
            {
                diagnostic.TriggerSuccessRate = 100.0;
            }

            diagnostic.HealthScore = diagnostic.TriggerSuccessRate;
        }
        else
        {
            diagnostic.HealthStatus = EventHealthStatusEnum.Unknown;
            diagnostic.HealthScore = 50.0; // 默认评分
            diagnostic.TriggerSuccessRate = 100.0;
        }

        // 计算可用性评分（过去7天）
        diagnostic.AvailabilityScore = await CalculateAvailabilityScore(device.Id, 7);

        return diagnostic;
    }

    /// <summary>
    /// 获取配置诊断信息
    /// </summary>
    private async Task<ConfigurationDiagnostic> GetConfigurationDiagnostic(Entity.Device device)
    {
        var diagnostic = new ConfigurationDiagnostic
        {
            DriverVersion = device.Driver?.Version ?? "未知",
            IsLatestVersion = true // 这里可以实现版本检查逻辑
        };

        // 检查配置完整性
        var requiredConfigs = device.DeviceConfigs?.Where(c => c.IsRequired).ToList() ?? new List<DeviceConfig>();
        var configuredItems = device.DeviceConfigs?.Where(c => c.Value != null && !string.IsNullOrEmpty(c.Value.ToString())).ToList() ?? new List<DeviceConfig>();

        diagnostic.RequiredConfigCount = requiredConfigs.Count;
        diagnostic.ConfiguredCount = configuredItems.Count;

        // 查找缺失的配置
        diagnostic.MissingConfigurations = requiredConfigs
            .Where(c => c.Value == null || string.IsNullOrEmpty(c.Value.ToString()))
            .Select(c => c.Identifier)
            .ToList();

        // 计算配置完整性
        if (diagnostic.RequiredConfigCount > 0)
        {
            var validConfigCount = diagnostic.RequiredConfigCount - diagnostic.MissingConfigurations.Count;
            diagnostic.ConfigurationCompleteness = (double)validConfigCount / diagnostic.RequiredConfigCount * 100;
        }
        else
        {
            diagnostic.ConfigurationCompleteness = 100.0;
        }

        diagnostic.IsConfigurationValid = diagnostic.MissingConfigurations.Count == 0;

        return diagnostic;
    }

    /// <summary>
    /// 获取数据诊断信息
    /// </summary>
    private async Task<DataDiagnostic> GetDataDiagnostic(Entity.Device device)
    {
        var diagnostic = new DataDiagnostic();

        // 获取设备标签数据
        var deviceLabels = await _deviceLabel.AsQueryable()
            .Where(l => l.DeviceId == device.Id)
            .ToListAsync();

        diagnostic.TotalDataPoints = deviceLabels.Count;
        diagnostic.ActiveDataPoints = deviceLabels.Count(l => l.Enable);

        // 检查数据采集状态
        if (_edgeGateway.DeviceThreads.TryGetValue(device.Id, out var thread))
        {
            diagnostic.IsDataCollecting = thread.Driver.IsConnected;
            diagnostic.LastDataTime = thread.LastActiveTime;
        }
        else
        {
            diagnostic.IsDataCollecting = false;
        }

        // 计算数据质量评分
        if (diagnostic.TotalDataPoints > 0)
        {
            var qualityScore = (double)diagnostic.ActiveDataPoints / diagnostic.TotalDataPoints * 100;
            diagnostic.DataQuality = qualityScore;
        }
        else
        {
            diagnostic.DataQuality = 0.0;
        }

        // 计算采集频率（基于设备配置的最小周期）
        if (device.DeviceInfo?.MinPeriod > 0)
        {
            diagnostic.CollectionFrequency = 60000.0 / device.DeviceInfo.MinPeriod; // 每分钟次数
        }

        return diagnostic;
    }

    /// <summary>
    /// 获取历史诊断数据
    /// </summary>
    private async Task<HistoryDiagnosticData> GetHistoryDiagnostic(Entity.Device device, DeviceDiagnosticInput input)
    {
        var history = new HistoryDiagnosticData
        {
            CurrentPage = input.HistoryPage,
            PageSize = input.HistoryPageSize
        };

        var startDate = DateTime.Now.AddDays(-input.HistoryDays);

        // 分页查询诊断历史
        var diagnosticQuery = _diagnosticInfo.AsQueryable()
            .Where(d => d.DeviceId == device.Id && d.CreateTime >= startDate)
            .OrderByDescending(d => d.CreateTime);

        history.TotalCount = await diagnosticQuery.CountAsync();
        history.DiagnosticHistory = await diagnosticQuery
            .Skip((input.HistoryPage - 1) * input.HistoryPageSize)
            .Take(input.HistoryPageSize)
            .ToListAsync();

        // 获取性能历史
        var healthStatus = await _healthStatus.AsQueryable()
            .Where(h => h.DeviceId == device.Id)
            .FirstAsync();

        if (healthStatus?.PerformanceHistory != null)
        {
            history.PerformanceHistory = healthStatus.PerformanceHistory
                .Where(p => p.RecordTime >= startDate)
                .OrderByDescending(p => p.RecordTime)
                .Take(input.HistoryPageSize)
                .ToList();
        }

        return history;
    }

    /// <summary>
    /// 计算综合健康评分
    /// </summary>
    private double CalculateOverallHealthScore(DeviceDiagnosticDto diagnostic)
    {
        var scores = new List<double>();

        if (diagnostic.Connection != null)
            scores.Add(diagnostic.Connection.ConnectionQuality * 0.3); // 连接权重30%

        if (diagnostic.Health != null)
            scores.Add(diagnostic.Health.HealthScore * 0.3); // 健康权重30%

        if (diagnostic.Configuration != null)
            scores.Add(diagnostic.Configuration.ConfigurationCompleteness * 0.2); // 配置权重20%

        if (diagnostic.Data != null)
            scores.Add(diagnostic.Data.DataQuality * 0.2); // 数据权重20%

        return scores.Any() ? scores.Sum() : 0.0;
    }

    /// <summary>
    /// 获取综合状态
    /// </summary>
    private DeviceDiagnosticStatus GetOverallStatus(double healthScore)
    {
        return healthScore switch
        {
            >= 95 => DeviceDiagnosticStatus.Excellent,
            >= 80 => DeviceDiagnosticStatus.Good,
            >= 60 => DeviceDiagnosticStatus.Warning,
            >= 30 => DeviceDiagnosticStatus.Error,
            _ => DeviceDiagnosticStatus.Critical
        };
    }

    /// <summary>
    /// 生成诊断建议
    /// </summary>
    private List<DiagnosticRecommendation> GenerateRecommendations(DeviceDiagnosticDto diagnostic)
    {
        var recommendations = new List<DiagnosticRecommendation>();

        // 连接问题建议
        if (diagnostic.Connection?.IsConnected == false)
        {
            recommendations.Add(new DiagnosticRecommendation
            {
                Level = RecommendationLevel.Critical,
                Type = "连接",
                Title = "设备连接中断",
                Description = "设备当前处于离线状态，无法进行数据采集",
                Action = "检查设备网络连接、电源状态和通信配置"
            });
        }

        // 配置问题建议
        if (diagnostic.Configuration?.IsConfigurationValid == false)
        {
            recommendations.Add(new DiagnosticRecommendation
            {
                Level = RecommendationLevel.Warning,
                Type = "配置",
                Title = "配置项不完整",
                Description = $"存在 {diagnostic.Configuration.MissingConfigurations.Count} 个缺失的必填配置项",
                Action = "完善设备配置信息，确保所有必填项都已正确配置"
            });
        }

        // 性能问题建议
        if (diagnostic.Performance?.AvgResponseTime > 5000)
        {
            recommendations.Add(new DiagnosticRecommendation
            {
                Level = RecommendationLevel.Suggestion,
                Type = "性能",
                Title = "响应时间较慢",
                Description = "设备平均响应时间超过5秒，可能影响数据采集效率",
                Action = "检查网络延迟，考虑优化采集频率或协议配置"
            });
        }

        // 健康状态建议
        if (diagnostic.Health?.TriggerSuccessRate < 90)
        {
            recommendations.Add(new DiagnosticRecommendation
            {
                Level = RecommendationLevel.Warning,
                Type = "健康",
                Title = "触发成功率偏低",
                Description = $"设备触发成功率为 {diagnostic.Health.TriggerSuccessRate:F1}%，低于推荐标准",
                Action = "检查设备日志，排查导致失败的具体原因"
            });
        }

        return recommendations;
    }

    /// <summary>
    /// 计算数据吞吐量
    /// </summary>
    private double CalculateDataThroughput(long deviceId)
    {
        // 这里可以实现实际的吞吐量计算逻辑
        // 暂时返回模拟值
        return 0.0;
    }

    /// <summary>
    /// 分析性能趋势
    /// </summary>
    private async Task<PerformanceTrend> AnalyzePerformanceTrend(long deviceId)
    {
        // 获取最近的性能数据进行趋势分析
        var recentPerformance = await _healthStatus.AsQueryable()
            .Where(h => h.DeviceId == deviceId)
            .Where(h => h.PerformanceHistory != null && h.PerformanceHistory.Count > 0)
            .OrderByDescending(h => h.LastCheckTime)
            .FirstAsync();

        if (recentPerformance?.PerformanceHistory?.Count >= 2)
        {
            var sortedHistory = recentPerformance.PerformanceHistory
                .OrderBy(p => p.RecordTime)
                .ToList();

            var recent = sortedHistory.TakeLast(5).Average(p => p.ExecutionTime);
            var previous = sortedHistory.Take(5).Average(p => p.ExecutionTime);

            if (recent < previous * 0.9) return PerformanceTrend.Improving;
            if (recent > previous * 1.1) return PerformanceTrend.Declining;
        }

        return PerformanceTrend.Stable;
    }

    /// <summary>
    /// 计算可用性评分
    /// </summary>
    private async Task<double> CalculateAvailabilityScore(long deviceId, int days)
    {
        var startDate = DateTime.Now.AddDays(-days);

        var healthRecords = await _healthStatus.AsQueryable()
            .Where(h => h.DeviceId == deviceId && h.LastCheckTime >= startDate)
            .ToListAsync();

        if (!healthRecords.Any())
            return 100.0;

        var goodRecords = healthRecords.Count(h => h.Status == EventHealthStatusEnum.Normal);
        return (double)goodRecords / healthRecords.Count * 100;
    }

    #endregion

    #region 内部方法

    /// <summary>
    ///     创建默认的属性
    /// </summary>
    /// <param name="device"></param>
    /// <param name="driver"></param>
    private void CreateVariables(Entity.Device device, Entity.Models.Driver driver)
    {
        var constructor = driver.Type.GetConstructor([typeof(DriverInfoDto)]) ?? throw Oops.Oh("该协议版本暂不支持！");
        if (constructor.Invoke([new DriverInfoDto { DeviceId = 0, Identifier = "" }]) is not IDriver iDriver)
            throw Oops.Oh($"[驱动协议]-{driver.DriverName}，未找到");

        if (iDriver.Variables != null)
            foreach (var method in iDriver.Variables)
                device.DeviceLabel.Add(new DeviceLabel
                {
                    Id = YitIdHelper.NextId(),
                    Description = method.Description,
                    Identifier = method.Identifier,
                    Name = method.Name,
                    Enable = true,
                    Period = 0,
                    SendType = SendTypeEnum.Always,
                    TransitionType = method.TransitionType,
                    ValueSource = ValueSourceEnum.Read,
                    Method = method.Identifier,
                    Custom = method.Custom,
                    ProtectType = ProtectTypeEnum.OnlyRead,
                    Length = method.Length,
                    Encoding = method.TransitionType == VariableStatus.TransitionString ? "utf8" : null
                });
    }

    /// <summary>
    /// 更新设备数量统计
    /// </summary>
    private void UpdateDeviceCount()
    {
        var count = _device.Count(w => w.Id > 0);
        _systemStatisticsService.UpdateDeviceCount(count);
    }

    /// <summary>
    /// 获取设备运行状态
    /// </summary>
    /// <param name="device">设备实体</param>
    /// <param name="hasActiveThread">是否有活跃线程</param>
    /// <param name="isConnected">是否连接</param>
    /// <returns>运行状态字符串</returns>
    private string GetDeviceRunStatus(Entity.Device device, bool hasActiveThread, bool isConnected)
    {
        // 如果设备未启用，返回停止状态
        if (!device.Enable)
        {
            return "stopped";
        }

        // 如果没有活跃线程，根据设备启用状态返回
        if (!hasActiveThread)
        {
            return device.Enable ? "paused" : "stopped";
        }

        // 如果有活跃线程且连接正常，返回活跃状态
        if (isConnected)
        {
            return "active";
        }

        // 如果有活跃线程但连接异常，返回错误状态
        return "error";
    }

    #endregion
}