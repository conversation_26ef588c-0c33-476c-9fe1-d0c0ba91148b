using System.ComponentModel.DataAnnotations;

namespace EdgeGateway.Device.Services.AI.Models;

/// <summary>
/// AI标签生成请求模型
/// </summary>
public class AILabelGenerationRequest
{
    /// <summary>
    /// 设备ID
    /// </summary>
    [Required(ErrorMessage = "设备ID不能为空")]
    public long DeviceId { get; set; }

    /// <summary>
    /// 协议类型
    /// </summary>
    [Required(ErrorMessage = "协议类型不能为空")]
    public string ProtocolType { get; set; }

    /// <summary>
    /// 用户输入的点位描述信息
    /// 例如："创建10个温度传感器点位，地址从40001开始，数据类型为Float"
    /// </summary>
    [Required(ErrorMessage = "用户输入不能为空")]
    public string UserInput { get; set; }

    /// <summary>
    /// 额外的协议配置信息
    /// </summary>
    public Dictionary<string, object>? AdditionalConfig { get; set; }

    /// <summary>
    /// 是否包含字段元数据
    /// 用于前端动态表单生成
    /// </summary>
    public bool IncludeMetadata { get; set; } = true;

    /// <summary>
    /// AI生成的最大点位数量限制
    /// </summary>
    public int MaxLabelCount { get; set; } = 50;

    /// <summary>
    /// 语言偏好（中文/英文）
    /// </summary>
    public string Language { get; set; } = "zh-CN";
}
