namespace EdgeGateway.Device.Services.Driver;

/// <summary>
///     驱动实例池，用于管理和复用驱动实例
/// </summary>
public class DriverPool
{
    private readonly ConcurrentDictionary<string, ConcurrentBag<IDriver>> _driverPool = new();
    private readonly IDriverFactory _driverFactory;
    private readonly ILogger<DriverPool> _logger;
    private readonly int _maxPoolSize;

    /// <summary>
    ///     构造函数
    /// </summary>
    /// <param name="driverFactory">驱动工厂</param>
    /// <param name="logger">日志记录器</param>
    /// <param name="maxPoolSize">每种驱动的最大池大小</param>
    public DriverPool(IDriverFactory driverFactory, ILogger<DriverPool> logger, int maxPoolSize = 10)
    {
        _driverFactory = driverFactory;
        _logger = logger;
        _maxPoolSize = maxPoolSize;
    }

    /// <summary>
    ///     获取驱动实例，优先从池中获取，池中没有则创建新实例
    /// </summary>
    /// <param name="device">设备信息</param>
    /// <returns>驱动实例</returns>
    public IDriver GetDriver(Entity.Device device)
    {
        var key = GetDriverKey(device);

        // 尝试从池中获取实例
        if (_driverPool.TryGetValue(key, out var pool) && pool.TryTake(out var driver))
        {
            _logger.LogDebug($"从池中获取驱动实例: {key}");
            return driver;
        }

        // 创建新实例
        _logger.LogDebug($"创建新的驱动实例: {key}");
        return _driverFactory.CreateDriver(device);
    }

    /// <summary>
    ///     归还驱动实例到池中
    /// </summary>
    /// <param name="device">设备信息</param>
    /// <param name="driver">驱动实例</param>
    public void ReturnDriver(Entity.Device device, IDriver driver)
    {
        if (driver == null)
            return;

        var key = GetDriverKey(device);

        // 获取或创建池
        var pool = _driverPool.GetOrAdd(key, _ => new ConcurrentBag<IDriver>());

        // 如果池未满，则添加到池中
        if (pool.Count < _maxPoolSize)
        {
            _logger.LogDebug($"归还驱动实例到池中: {key}");
            pool.Add(driver);
        }
        else
        {
            // 池已满，直接释放
            _logger.LogDebug($"池已满，直接释放驱动实例: {key}");
            driver.Dispose();
        }
    }

    /// <summary>
    ///     清空指定类型的驱动池
    /// </summary>
    /// <param name="driverName">驱动名称</param>
    public void ClearPool(string driverName)
    {
        // 查找所有匹配的键
        var keysToRemove = _driverPool.Keys
            .Where(k => k.StartsWith(driverName + "_"))
            .ToList();

        foreach (var key in keysToRemove)
            if (_driverPool.TryRemove(key, out var pool))
            {
                // 释放池中的所有驱动
                var count = 0;
                while (pool.TryTake(out var driver))
                {
                    driver.Dispose();
                    count++;
                }

                _logger.LogInformation($"已清空驱动池: {key}, 释放实例数: {count}");
            }
    }

    /// <summary>
    ///     清空所有驱动池
    /// </summary>
    public void ClearAllPools()
    {
        foreach (var key in _driverPool.Keys)
            if (_driverPool.TryRemove(key, out var pool))
            {
                // 释放池中的所有驱动
                var count = 0;
                while (pool.TryTake(out var driver))
                {
                    driver.Dispose();
                    count++;
                }

                _logger.LogInformation($"已清空驱动池: {key}, 释放实例数: {count}");
            }
    }

    /// <summary>
    ///     获取驱动键
    /// </summary>
    /// <param name="device">设备信息</param>
    /// <returns>驱动键</returns>
    private string GetDriverKey(Entity.Device device)
    {
        return $"{device.Driver?.DriverName}_{device.Driver?.Version}";
    }
}