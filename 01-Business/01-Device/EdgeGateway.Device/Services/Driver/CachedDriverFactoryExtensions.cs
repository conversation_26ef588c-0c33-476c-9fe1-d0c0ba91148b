using EdgeGateway.Device.Services.Clients;
using System.Collections.Concurrent;

namespace EdgeGateway.Device.Services.Driver;

/// <summary>
/// CachedDriverFactory扩展方法
/// </summary>
public static class CachedDriverFactoryExtensions
{
  // 存储最后创建的缓存驱动包装器实例
  private static readonly ConcurrentDictionary<string, CachedDriverWrapper> _cachedWrappers = new();

  /// <summary>
  /// 注册缓存驱动包装器实例
  /// </summary>
  /// <param name="wrapper">缓存驱动包装器实例</param>
  /// <param name="driverName">驱动名称</param>
  public static void RegisterCachedWrapper(this CachedDriverWrapper wrapper, string driverName)
  {
    if (wrapper != null && !string.IsNullOrEmpty(driverName))
    {
      _cachedWrappers[driverName] = wrapper;
    }
  }

  /// <summary>
  /// 获取指定驱动的缓存驱动包装器实例
  /// </summary>
  /// <param name="factory">缓存驱动工厂</param>
  /// <param name="driverName">驱动名称</param>
  /// <returns>缓存驱动包装器实例，如果没有则返回null</returns>
  public static CachedDriverWrapper GetCachedDriverWrapper(this CachedDriverFactory factory, string driverName)
  {
    if (string.IsNullOrEmpty(driverName))
      return null;

    _cachedWrappers.TryGetValue(driverName, out var wrapper);
    return wrapper;
  }

  /// <summary>
  /// 获取所有缓存驱动包装器的缓存统计信息
  /// </summary>
  /// <param name="factory">缓存驱动工厂</param>
  /// <returns>所有缓存驱动的统计信息</returns>
  public static Dictionary<string, Dictionary<string, object>> GetAllCacheStatistics(this CachedDriverFactory factory)
  {
    var result = new Dictionary<string, Dictionary<string, object>>();

    foreach (var kvp in _cachedWrappers)
    {
      try
      {
        var stats = kvp.Value.GetCacheStatistics();
        result[kvp.Key] = stats;
      }
      catch
      {
        // 忽略获取统计信息时的错误
      }
    }

    return result;
  }

  /// <summary>
  /// 清除所有缓存
  /// </summary>
  /// <param name="factory">缓存驱动工厂</param>
  public static void ClearAllCaches(this CachedDriverFactory factory)
  {
    foreach (var wrapper in _cachedWrappers.Values)
    {
      try
      {
        wrapper.ClearCache();
      }
      catch
      {
        // 忽略清除缓存时的错误
      }
    }
  }
}