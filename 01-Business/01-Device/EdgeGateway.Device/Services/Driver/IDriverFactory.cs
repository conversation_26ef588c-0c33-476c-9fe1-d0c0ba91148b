using EdgeGateway.Driver.Interface;

namespace EdgeGateway.Device.Services.Driver;

/// <summary>
/// 驱动工厂接口
/// </summary>
public interface IDriverFactory
{
  /// <summary>
  /// 创建驱动实例
  /// </summary>
  /// <param name="device">设备信息</param>
  /// <returns>驱动实例</returns>
  IDriver CreateDriver(Entity.Device device);

  /// <summary>
  /// 尝试更新驱动
  /// </summary>
  /// <param name="driverName">驱动名称</param>
  /// <returns>是否更新成功</returns>
  Task<bool> TryUpdateDriverAsync(string driverName);
}