using EdgeGateway.Device.Services.Driver;

namespace EdgeGateway.Device.Services;

/// <summary>
///     驱动库
/// </summary>
[ApiDescriptionSettings("设备中心")]
[Route("/api/driver/")]
public class DriverService : ITransient, IDynamicApiController
{
    /// <summary>
    ///     驱动服务
    /// </summary>
    private readonly DriverHostService _driverHostService;

    /// <summary>
    /// </summary>
    /// <param name="driver"></param>
    public DriverService(DriverHostService driver)
    {
        _driverHostService = driver;
    }

    /// <summary>
    ///     驱动集合
    /// </summary>
    /// <returns></returns>
    [HttpGet("list")]
    [OperationId(nameof(GetList))]
    public Task<dynamic> GetList()
    {
        return Task.FromResult<dynamic>(_driverHostService.Drivers.Select(s => new
        {
            s.DriverName,
            s.Manufacturer,
            s.Version,
            s.IsNet,
            s.Description
        }));
    }

    /// <summary>
    ///     驱动详情
    /// </summary>
    /// <returns></returns>
    [HttpGet("{name}/info")]
    [OperationId(nameof(GetDetail))]
    public Task<Entity.Models.Driver?> GetDetail(string name)
    {
        var driver = _driverHostService.Drivers.FirstOrDefault(f => f.DriverName == name);
        return Task.FromResult(driver);
    }
   

    /// <summary>
    ///     获取驱动协议更新日志
    /// </summary>
    /// <param name="name">驱动名称</param>
    /// <param name="format">日志格式，默认为Markdown</param>
    /// <returns>驱动协议的更新日志</returns>
    [HttpGet("{name}/versionlogs")]
    [OperationId(nameof(GetVersionLogs))]
    public Task<dynamic> GetVersionLogs(string name, LogFormat format = LogFormat.Markdown)
    {
        var driver = _driverHostService.Drivers.FirstOrDefault(f => f.DriverName == name);
        if (driver == null)
            return Task.FromResult<dynamic>(new { success = false, message = $"未找到名为 {name} 的驱动" });

        try
        {
            // 使用Driver的Type属性创建IDriver实例
            var constructor = driver.Type.GetConstructor([typeof(DriverInfoDto)]);
            if (constructor == null)
                return Task.FromResult<dynamic>(new { success = false, message = "该协议版本暂不支持！" });

            var driverInstance = constructor.Invoke([new DriverInfoDto { DeviceId = 0, Identifier = "" }]) as IDriver;
            if (driverInstance == null)
                return Task.FromResult<dynamic>(new { success = false, message = $"驱动 {name} 创建失败" });

            var logs = driverInstance.GetVersionLogs(format);
            return Task.FromResult<dynamic>(new { success = true, data = logs });
        }
        catch (Exception ex)
        {
            return Task.FromResult<dynamic>(new { success = false, message = $"获取驱动日志失败：{ex.Message}" });
        }
    }
}