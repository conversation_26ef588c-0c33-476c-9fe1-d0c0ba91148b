using Microsoft.Extensions.Hosting;

namespace EdgeGateway.Device.Services;

/// <summary>
///     延迟服务管理器
/// </summary>
public class DelayedHostedServiceManager : IHostedService
{
  /// <summary>
  ///     驱动服务
  /// </summary>
  private readonly DriverHostService _driverHostService;

  /// <summary>
  ///     采集服务
  /// </summary>
  private readonly EdgeHostService _edgeHostService;

  /// <summary>
  ///     日志
  /// </summary>
  private readonly ILogger<DelayedHostedServiceManager> _logger;

  /// <summary>
  ///     应用程序生命周期
  /// </summary>
  private readonly IHostApplicationLifetime _applicationLifetime;

  /// <summary>
  ///     构造函数
  /// </summary>
  /// <param name="driverHostService"> 驱动服务 </param>
  /// <param name="edgeHostService"> 采集服务 </param>
  /// <param name="logger"> 日志 </param>
  /// <param name="applicationLifetime"> 应用程序生命周期 </param>
  public DelayedHostedServiceManager(
      DriverHostService driverHostService,
      EdgeHostService edgeHostService,
      
      ILogger<DelayedHostedServiceManager> logger,
      IHostApplicationLifetime applicationLifetime)
  {
    _driverHostService = driverHostService;
    _edgeHostService = edgeHostService;
    _logger = logger;
    _applicationLifetime = applicationLifetime;
  }

  /// <summary>
  ///     启动服务
  /// </summary>
  /// <param name="cancellationToken"> 取消令牌 </param>
  /// <returns> 任务 </returns>
  public async Task StartAsync(CancellationToken cancellationToken)
  {
    await Task.Run(async () =>
    {
      _applicationLifetime.ApplicationStarted.Register(async () =>
      {
        try
        {
          _logger.LogInformation("开始启动延迟加载的服务...");
          await _driverHostService.StartAsync(cancellationToken);
          _logger.LogInformation("驱动服务启动完成");
          await _edgeHostService.StartAsync(cancellationToken);
          _logger.LogInformation("采集服务启动完成");
          _logger.LogInformation("数据转发服务启动完成");
          _logger.LogInformation("所有延迟加载服务启动完成");
        }
        catch (Exception ex)
        {
          _logger.LogError(ex, "延迟服务启动过程中发生错误");
        }
      });

      return Task.CompletedTask;
    }, cancellationToken);
  }

  /// <summary>
  ///     停止服务
  /// </summary>
  /// <param name="cancellationToken"> 取消令牌 </param>
  /// <returns> 任务 </returns>
  public async Task StopAsync(CancellationToken cancellationToken)
  {
    _logger.LogInformation("开始停止延迟加载的服务...");
    await _edgeHostService.StopAsync(cancellationToken);
    _logger.LogInformation("采集服务停止完成");
    await _driverHostService.StopAsync(cancellationToken);
    _logger.LogInformation("驱动服务停止完成");
    _logger.LogInformation("所有延迟加载服务停止完成");
  }
}