using EdgeGateway.Device.Entity.Models.Events;

namespace EdgeGateway.Device.Services.Events
{
  /// <summary>
  /// 事件调度任务控制器
  /// </summary>
  [ApiDescriptionSettings("Device", Name = "EventSchedule")]
  public class EventScheduleController : IDynamicApiController
  {
    private readonly ILogger<EventScheduleController> _logger;
    private readonly SqlSugarRepository<EventScheduleTask> _scheduleTaskRepository;
    private readonly SqlSugarRepository<EventTaskLog> _taskLogRepository;
    private readonly SqlSugarRepository<DeviceEvent> _eventRepository;

    /// <summary>
    /// 构造函数
    /// </summary>
    public EventScheduleController(
        ILogger<EventScheduleController> logger,
        SqlSugarRepository<EventScheduleTask> scheduleTaskRepository,
        SqlSugarRepository<EventTaskLog> taskLogRepository,
        SqlSugarRepository<DeviceEvent> eventRepository)
    {
      _logger = logger;
      _scheduleTaskRepository = scheduleTaskRepository;
      _taskLogRepository = taskLogRepository;
      _eventRepository = eventRepository;
    }

    /// <summary>
    /// 获取事件的调度任务
    /// </summary>
    /// <param name="eventId">事件ID</param>
    /// <returns>调度任务列表</returns>
    [HttpGet("events/{eventId}/schedule-tasks")]
    public async Task<ActionResult<List<EventScheduleTaskResponse>>> GetEventScheduleTasks(long eventId)
    {
      try
      {
        // 获取事件
        var eventEntity = await _eventRepository.GetFirstAsync(e => e.Id == eventId);
        if (eventEntity == null)
        {
          throw Oops.Oh($"事件不存在: {eventId}");
        }

        // 获取调度任务
        var scheduleTasks = await _scheduleTaskRepository.GetListAsync(t => t.DeviceEventId == eventId);

        var result = scheduleTasks.Select(t => new EventScheduleTaskResponse
        {
          Id = t.Id,
          DeviceEventId = t.DeviceEventId,
          DeviceId = t.DeviceId,
          TaskName = t.TaskName,
          TaskIdentifier = t.TaskIdentifier,
          TaskType = t.TaskType,
          Status = t.Status,
          ExpressionType = t.ExpressionType,
          Expression = t.Expression,
          LastExecuteTime = t.LastExecuteTime,
          NextExecuteTime = t.NextExecuteTime,
          StartTime = t.StartTime,
          EndTime = t.EndTime,
          ExecuteCount = t.ExecuteCount,
          MaxExecuteCount = t.MaxExecuteCount,
          MissedCount = t.MissedCount,
          SuccessCount = t.SuccessCount,
          FailureCount = t.FailureCount,
          LastExecuteResult = t.LastExecuteResult
        }).ToList();

        return result;
      }
      catch (Exception ex)
      {
        _logger.LogError(ex, "获取事件调度任务失败: {EventId}", eventId);
        throw Oops.Oh($"获取事件调度任务失败: {ex.Message}");
      }
    }

    /// <summary>
    /// 获取调度任务详情
    /// </summary>
    /// <param name="taskId">任务ID</param>
    /// <returns>调度任务详情</returns>
    [HttpGet("schedule-tasks/{taskId}")]
    public async Task<ActionResult<EventScheduleTaskResponse>> GetScheduleTask(long taskId)
    {
      try
      {
        // 获取调度任务
        var scheduleTask = await _scheduleTaskRepository.GetFirstAsync(t => t.Id == taskId);
        if (scheduleTask == null)
        {
          throw Oops.Oh($"调度任务不存在: {taskId}");
        }

        return new EventScheduleTaskResponse
        {
          Id = scheduleTask.Id,
          DeviceEventId = scheduleTask.DeviceEventId,
          DeviceId = scheduleTask.DeviceId,
          TaskName = scheduleTask.TaskName,
          TaskIdentifier = scheduleTask.TaskIdentifier,
          TaskType = scheduleTask.TaskType,
          Status = scheduleTask.Status,
          ExpressionType = scheduleTask.ExpressionType,
          Expression = scheduleTask.Expression,
          LastExecuteTime = scheduleTask.LastExecuteTime,
          NextExecuteTime = scheduleTask.NextExecuteTime,
          StartTime = scheduleTask.StartTime,
          EndTime = scheduleTask.EndTime,
          ExecuteCount = scheduleTask.ExecuteCount,
          MaxExecuteCount = scheduleTask.MaxExecuteCount,
          MissedCount = scheduleTask.MissedCount,
          SuccessCount = scheduleTask.SuccessCount,
          FailureCount = scheduleTask.FailureCount,
          LastExecuteResult = scheduleTask.LastExecuteResult
        };
      }
      catch (Exception ex)
      {
        _logger.LogError(ex, "获取调度任务详情失败: {TaskId}", taskId);
        throw Oops.Oh($"获取调度任务详情失败: {ex.Message}");
      }
    }

    /// <summary>
    /// 暂停调度任务
    /// </summary>
    /// <param name="taskId">任务ID</param>
    /// <returns>操作结果</returns>
    [HttpPost("schedule-tasks/{taskId}/pause")]
    public async Task PauseScheduleTask(long taskId)
    {
      try
      {
        // 获取调度任务
        var scheduleTask = await _scheduleTaskRepository.GetFirstAsync(t => t.Id == taskId);
        if (scheduleTask == null)
        {
          throw Oops.Oh($"调度任务不存在: {taskId}");
        }

        // 更新状态
        scheduleTask.Status = ScheduleTaskStatusEnum.Paused;
        scheduleTask.MemoryStatus.IsPaused = true;
        scheduleTask.MemoryStatus.PauseTime = DateTime.Now;

        await _scheduleTaskRepository.UpdateAsync(scheduleTask);

        // TODO: 调用调度服务暂停任务
      }
      catch (Exception ex)
      {
        _logger.LogError(ex, "暂停调度任务失败: {TaskId}", taskId);
        throw Oops.Oh($"暂停调度任务失败: {ex.Message}");
      }
    }

    /// <summary>
    /// 恢复调度任务
    /// </summary>
    /// <param name="taskId">任务ID</param>
    /// <returns>操作结果</returns>
    [HttpPost("schedule-tasks/{taskId}/resume")]
    public async Task ResumeScheduleTask(long taskId)
    {
      try
      {
        // 获取调度任务
        var scheduleTask = await _scheduleTaskRepository.GetFirstAsync(t => t.Id == taskId);
        if (scheduleTask == null)
        {
          throw Oops.Oh($"调度任务不存在: {taskId}");
        }

        // 检查状态
        if (scheduleTask.Status != ScheduleTaskStatusEnum.Paused)
        {
          throw Oops.Oh($"调度任务状态不是暂停状态: {scheduleTask.Status}");
        }

        // 更新状态
        scheduleTask.Status = ScheduleTaskStatusEnum.Scheduled;
        scheduleTask.MemoryStatus.IsPaused = false;
        scheduleTask.MemoryStatus.PauseTime = null;

        await _scheduleTaskRepository.UpdateAsync(scheduleTask);

        // TODO: 调用调度服务恢复任务

      }
      catch (Exception ex)
      {
        _logger.LogError(ex, "恢复调度任务失败: {TaskId}", taskId);
        throw Oops.Oh($"恢复调度任务失败: {ex.Message}");
      }
    }

    /// <summary>
    /// 取消调度任务
    /// </summary>
    /// <param name="taskId">任务ID</param>
    /// <returns>操作结果</returns>
    [HttpPost("schedule-tasks/{taskId}/cancel")]
    public async Task CancelScheduleTask(long taskId)
    {
      try
      {
        // 获取调度任务
        var scheduleTask = await _scheduleTaskRepository.GetFirstAsync(t => t.Id == taskId);
        if (scheduleTask == null)
        {
          throw Oops.Oh($"调度任务不存在: {taskId}");
        }

        // 更新状态
        scheduleTask.Status = ScheduleTaskStatusEnum.Canceled;

        await _scheduleTaskRepository.UpdateAsync(scheduleTask);

        // TODO: 调用调度服务取消任务

      }
      catch (Exception ex)
      {
        _logger.LogError(ex, "取消调度任务失败: {TaskId}", taskId);
        throw Oops.Oh($"取消调度任务失败: {ex.Message}");
      }
    }

    /// <summary>
    /// 获取任务执行日志
    /// </summary>
    /// <param name="taskId">任务ID</param>
    /// <param name="pageIndex">页码</param>
    /// <param name="pageSize">每页大小</param>
    /// <returns>任务日志列表</returns>
    [HttpGet("schedule-tasks/{taskId}/logs")]
    public async Task<ActionResult<List<EventTaskLog>>> GetTaskLogs(
        long taskId, [FromQuery] int pageIndex = 1, [FromQuery] int pageSize = 10)
    {
      try
      {
        // 获取调度任务
        var scheduleTask = await _scheduleTaskRepository.GetFirstAsync(t => t.Id == taskId);
        if (scheduleTask == null)
        {
          throw Oops.Oh($"调度任务不存在: {taskId}");
        }

        // 获取任务日志
        var logs = await _taskLogRepository.AsQueryable()
            .Where(l => l.TaskId == taskId)
            .OrderByDescending(l => l.ExecuteTime)
            .Skip((pageIndex - 1) * pageSize)
            .Take(pageSize)
            .ToListAsync();

        return logs;
      }
      catch (Exception ex)
      {
        _logger.LogError(ex, "获取任务执行日志失败: {TaskId}", taskId);
        throw Oops.Oh($"获取任务执行日志失败: {ex.Message}");
      }
    }

    /// <summary>
    /// 清理过期任务日志
    /// </summary>
    /// <param name="days">保留天数</param>
    /// <returns>操作结果</returns>
    [HttpPost("schedule-tasks/clean-logs")]
    public async Task CleanTaskLogs([FromQuery] int days = 30)
    {
      try
      {
        // 计算过期时间
        var expireTime = DateTime.Now.AddDays(-days);

        // 删除过期日志
        await _taskLogRepository.DeleteAsync(l => l.ExecuteTime < expireTime);

      }
      catch (Exception ex)
      {
        _logger.LogError(ex, "清理过期任务日志失败");
        throw Oops.Oh($"清理过期任务日志失败: {ex.Message}");
      }
    }
  }
}