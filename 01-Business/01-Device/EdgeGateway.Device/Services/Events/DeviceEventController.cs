using System;
using System.Collections.Generic;
using System.Threading.Tasks;
using EdgeGateway.Device.Entity;
using EdgeGateway.Device.Entity.Models.Events;
using EdgeGateway.Device.Services.Events;
using Furion.DatabaseAccessor;
using Furion.DynamicApiController;
using Microsoft.AspNetCore.Authorization;
using Microsoft.AspNetCore.Mvc;
using Microsoft.Extensions.Logging;
using SqlSugar;

namespace EdgeGateway.Device.API.Controllers
{
  /// <summary>
  /// 设备事件控制器
  /// </summary>
  [ApiDescriptionSettings("Device", Name = "DeviceEvent")]
  public class DeviceEventController : IDynamicApiController
  {
    private readonly ILogger<DeviceEventController> _logger;
    private readonly IDeviceEventConfigService _eventConfigService;
    private readonly SqlSugarRepository<DeviceEvent> _eventRepository;
    private readonly SqlSugarRepository<EventHealthStatus> _healthStatusRepository;
    private readonly SqlSugarRepository<EventDiagnosticInfo> _diagnosticRepository;
    private readonly SqlSugarRepository<EventScheduleTask> _scheduleTaskRepository;

    /// <summary>
    /// 构造函数
    /// </summary>
    public DeviceEventController(
        ILogger<DeviceEventController> logger,
        IDeviceEventConfigService eventConfigService,
        SqlSugarRepository<DeviceEvent> eventRepository,
        SqlSugarRepository<EventHealthStatus> healthStatusRepository,
        SqlSugarRepository<EventDiagnosticInfo> diagnosticRepository,
        SqlSugarRepository<EventScheduleTask> scheduleTaskRepository)
    {
      _logger = logger;
      _eventConfigService = eventConfigService;
      _eventRepository = eventRepository;
      _healthStatusRepository = healthStatusRepository;
      _diagnosticRepository = diagnosticRepository;
      _scheduleTaskRepository = scheduleTaskRepository;
    }

    /// <summary>
    /// 获取设备的所有事件
    /// </summary>
    /// <param name="deviceId">设备ID</param>
    /// <returns>事件列表</returns>
    [HttpGet("devices/{deviceId}/events")]
    public async Task<ActionResult<List<EventResponse>>> GetDeviceEvents(long deviceId)
    {
      try
      {
        var events = await _eventConfigService.GetDeviceEventsAsync(deviceId);
        var result = new List<EventResponse>();

        foreach (var eventInfo in events)
        {
          // 获取健康状态
          var healthStatus = await _healthStatusRepository.GetFirstAsync(h => h.DeviceEventId == eventInfo.Id)
            ?? new EventHealthStatus { Status = EventHealthStatusEnum.Unknown };

          result.Add(new EventResponse
          {
            Id = eventInfo.Id,
            DeviceId = eventInfo.DeviceId,
            DeviceIdentifier = "", // 需要查询设备获取标识符
            EventName = eventInfo.Name,
            Description = "", // 需要从DeviceEvent实体获取
            Enabled = eventInfo.Enabled,
            TriggerEventType = TriggerEventTypeEnum.DataChange, // 从实体获取
            Priority = 0, // 从实体获取
            EventGroup = "", // 从实体获取
            LastTriggerTime = eventInfo.LastTriggerTime,
            TriggerCount = eventInfo.TriggerCount,
            CreateTime = eventInfo.CreatedTime,
            UpdateTime = eventInfo.UpdatedTime,
            HealthStatus = healthStatus.Status
          });
        }

        return result;
      }
      catch (Exception ex)
      {
        _logger.LogError(ex, "获取设备事件列表失败: {DeviceId}", deviceId);
        throw Oops.Oh($"获取设备事件列表失败: {ex.Message}");
      }
    }

    /// <summary>
    /// 获取所有事件
    /// </summary>
    /// <returns>事件列表</returns>
    [HttpGet("events")]
    public async Task<ActionResult<List<EventResponse>>> GetAllEvents()
    {
      try
      {
        var events = await _eventConfigService.GetAllEventsAsync();
        var result = new List<EventResponse>();

        foreach (var eventInfo in events)
        {
          // 获取健康状态
          var healthStatus = await _healthStatusRepository.GetFirstAsync(h => h.DeviceEventId == eventInfo.Id)
            ?? new EventHealthStatus { Status = EventHealthStatusEnum.Unknown };

          result.Add(new EventResponse
          {
            Id = eventInfo.Id,
            DeviceId = eventInfo.DeviceId,
            DeviceIdentifier = "", // 需要查询设备获取标识符
            EventName = eventInfo.Name,
            Description = "", // 需要从DeviceEvent实体获取
            Enabled = eventInfo.Enabled,
            TriggerEventType = TriggerEventTypeEnum.DataChange, // 从实体获取
            Priority = 0, // 从实体获取
            EventGroup = "", // 从实体获取
            LastTriggerTime = eventInfo.LastTriggerTime,
            TriggerCount = eventInfo.TriggerCount,
            CreateTime = eventInfo.CreatedTime,
            UpdateTime = eventInfo.UpdatedTime,
            HealthStatus = healthStatus.Status
          });
        }

        return result;
      }
      catch (Exception ex)
      {
        _logger.LogError(ex, "获取所有事件列表失败");
        throw Oops.Oh($"获取所有事件列表失败: {ex.Message}");
      }
    }

    /// <summary>
    /// 获取事件详情
    /// </summary>
    /// <param name="eventId">事件ID</param>
    /// <returns>事件详情</returns>
    [HttpGet("events/{eventId}")]
    public async Task<ActionResult<EventDetailResponse>> GetEvent(long eventId)
    {
      try
      {
        // 获取事件基本信息
        var eventInfo = await _eventConfigService.GetEventAsync(eventId);
        if (eventInfo == null)
        {
          throw Oops.Oh($"事件不存在: {eventId}");
        }

        // 获取事件实体
        var eventEntity = await _eventRepository.GetFirstAsync(e => e.Id == eventId);
        if (eventEntity == null)
        {
          throw Oops.Oh($"事件不存在: {eventId}");
        }

        // 获取健康状态
        var healthStatus = await _healthStatusRepository.GetFirstAsync(h => h.DeviceEventId == eventId)
          ?? new EventHealthStatus { Status = EventHealthStatusEnum.Unknown };

        return new EventDetailResponse
        {
          Id = eventInfo.Id,
          DeviceId = eventInfo.DeviceId,
          DeviceIdentifier = "", // 需要查询设备获取标识符
          EventName = eventInfo.Name,
          Description = eventEntity.Description,
          Enabled = eventInfo.Enabled,
          TriggerEventType = eventEntity.TriggerEventType,
          Condition = eventInfo.Condition,
          TimerConfig = ConvertTimerConfig(eventEntity.TimerConfig),
          Actions = eventEntity.Actions,
          Priority = eventEntity.Priority,
          EventGroup = eventEntity.EventGroup,
          LastTriggerTime = eventInfo.LastTriggerTime,
          TriggerCount = eventInfo.TriggerCount,
          StatusBufferTime = eventEntity.StatusBufferTime,
          SaveTriggerHistory = eventEntity.SaveTriggerHistory,
          HistoryRetentionDays = eventEntity.HistoryRetentionDays,
          CreateTime = eventInfo.CreatedTime,
          UpdateTime = eventInfo.UpdatedTime,
          HealthStatus = healthStatus.Status
        };
      }
      catch (Exception ex)
      {
        _logger.LogError(ex, "获取事件详情失败: {EventId}", eventId);
        throw Oops.Oh($"获取事件详情失败: {ex.Message}");
      }
    }

    /// <summary>
    /// 创建设备事件
    /// </summary>
    /// <param name="request">创建事件请求</param>
    /// <returns>事件ID</returns>
    [HttpPost("events")]
    public async Task<ActionResult<long>> CreateEvent(CreateEventRequest request)
    {
      try
      {
        // 调用配置服务创建事件
        var eventId = await _eventConfigService.CreateEventAsync(
            request.DeviceId,
            request.EventName,
            request.Condition,
            request.Description);

        // 更新其他字段
        var eventEntity = await _eventRepository.GetFirstAsync(e => e.Id == eventId);
        if (eventEntity != null)
        {
          eventEntity.TriggerEventType = request.TriggerEventType;
          eventEntity.TimerConfig = ConvertToEntityTimerConfig(request.TimerConfig);
          eventEntity.Actions = request.Actions;
          eventEntity.Priority = request.Priority;
          eventEntity.EventGroup = request.EventGroup;

          await _eventRepository.UpdateAsync(eventEntity);
        }

        return eventId;
      }
      catch (Exception ex)
      {
        _logger.LogError(ex, "创建设备事件失败: {RequestData}", request);
        throw Oops.Oh($"创建设备事件失败: {ex.Message}");
      }
    }

    /// <summary>
    /// 更新设备事件
    /// </summary>
    /// <param name="eventId">事件ID</param>
    /// <param name="request">更新事件请求</param>
    /// <returns>操作结果</returns>
    [HttpPut("events/{eventId}")]
    public async Task UpdateEvent(long eventId, UpdateEventRequest request)
    {
      try
      {
        // 获取事件实体
        var eventEntity = await _eventRepository.GetFirstAsync(e => e.Id == eventId);
        if (eventEntity == null)
        {
          throw Oops.Oh($"事件不存在: {eventId}");
        }

        // 调用配置服务更新事件
        await _eventConfigService.UpdateEventAsync(
            eventId,
            request.EventName,
            request.Condition,
            request.Description);

        // 更新其他字段
        eventEntity.TriggerEventType = request.TriggerEventType;
        eventEntity.TimerConfig = ConvertToEntityTimerConfig(request.TimerConfig);
        eventEntity.Actions = request.Actions;
        eventEntity.Priority = request.Priority;
        eventEntity.EventGroup = request.EventGroup;

        await _eventRepository.UpdateAsync(eventEntity);
      }
      catch (Exception ex)
      {
        _logger.LogError(ex, "更新设备事件失败: {EventId}, {RequestData}", eventId, request);
        throw Oops.Oh($"更新设备事件失败: {ex.Message}");
      }
    }

    /// <summary>
    /// 删除设备事件
    /// </summary>
    /// <param name="eventId">事件ID</param>
    /// <returns>操作结果</returns>
    [HttpDelete("events/{eventId}")]
    public async Task DeleteEvent(long eventId)
    {
      try
      {
        await _eventConfigService.DeleteEventAsync(eventId);
      }
      catch (Exception ex)
      {
        _logger.LogError(ex, "删除设备事件失败: {EventId}", eventId);
        throw Oops.Oh($"删除设备事件失败: {ex.Message}");
      }
    }

    /// <summary>
    /// 启用设备事件
    /// </summary>
    /// <param name="eventId">事件ID</param>
    /// <returns>操作结果</returns>
    [HttpPost("events/{eventId}/enable")]
    public async Task EnableEvent(long eventId)
    {
      try
      {
        await _eventConfigService.EnableEventAsync(eventId);
      }
      catch (Exception ex)
      {
        _logger.LogError(ex, "启用设备事件失败: {EventId}", eventId);
        throw Oops.Oh($"启用设备事件失败: {ex.Message}");
      }
    }

    /// <summary>
    /// 禁用设备事件
    /// </summary>
    /// <param name="eventId">事件ID</param>
    /// <returns>操作结果</returns>
    [HttpPost("events/{eventId}/disable")]
    public async Task DisableEvent(long eventId)
    {
      try
      {
        await _eventConfigService.DisableEventAsync(eventId);
      }
      catch (Exception ex)
      {
        _logger.LogError(ex, "禁用设备事件失败: {EventId}", eventId);
        throw Oops.Oh($"禁用设备事件失败: {ex.Message}");
      }
    }

    /// <summary>
    /// 检查事件名称是否存在
    /// </summary>
    /// <param name="deviceId">设备ID</param>
    /// <param name="eventName">事件名称</param>
    /// <param name="excludeEventId">排除的事件ID</param>
    /// <returns>是否存在</returns>
    [HttpGet("devices/{deviceId}/events/check-name")]
    public async Task<ActionResult<bool>> CheckEventNameExists(long deviceId, [FromQuery] string eventName, [FromQuery] long? excludeEventId = null)
    {
      try
      {
        var exists = await _eventConfigService.IsEventNameExistsAsync(deviceId, eventName, excludeEventId);
        return exists;
      }
      catch (Exception ex)
      {
        _logger.LogError(ex, "检查事件名称是否存在失败: {DeviceId}, {EventName}", deviceId, eventName);
        throw Oops.Oh($"检查事件名称是否存在失败: {ex.Message}");
      }
    }

    /// <summary>
    /// 将实体的 TimerTriggerConfig 转换为模型的 TimerTriggerConfig
    /// </summary>
    private Entity.Models.Events.TimerTriggerConfig ConvertTimerConfig(Entity.EntityTimerTriggerConfig config)
    {
      // 如果为null则返回null
      if (config == null) return null;

      // 创建并返回新的 TimerTriggerConfig
      return new Entity.Models.Events.TimerTriggerConfig
      {
        // 根据需要设置属性
        // 注意：根据这两个类的实际结构设置适当的属性
      };
    }

    /// <summary>
    /// 将模型的 TimerTriggerConfig 转换为实体的 TimerTriggerConfig
    /// </summary>
    private Entity.EntityTimerTriggerConfig ConvertToEntityTimerConfig(Entity.Models.Events.TimerTriggerConfig config)
    {
      // 如果为null则返回null
      if (config == null) return null;

      // 创建并返回新的 TimerTriggerConfig
      return new Entity.EntityTimerTriggerConfig
      {
        // 根据需要设置属性
        // 注意：根据这两个类的实际结构设置适当的属性
      };
    }
  }
}