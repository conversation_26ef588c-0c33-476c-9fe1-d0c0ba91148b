using System;
using System.Collections.Generic;
using System.Linq;
using System.Threading.Tasks;
using EdgeGateway.Device.Entity.Models.Events;
using Furion.DatabaseAccessor;
using Microsoft.Extensions.Logging;
using SqlSugar;

namespace EdgeGateway.Device.Services.Events
{
  /// <summary>
  /// 设备事件配置服务实现
  /// </summary>
  public class DeviceEventConfigService : IDeviceEventConfigService
  {
    private readonly ILogger<DeviceEventConfigService> _logger;
    private readonly SqlSugarRepository<Entity.DeviceEvent> _eventRepository;
    private readonly IDeviceEventService _deviceEventService;

    /// <summary>
    /// 构造函数
    /// </summary>
    public DeviceEventConfigService(
        ILogger<DeviceEventConfigService> logger,
        SqlSugarRepository<Entity.DeviceEvent> eventRepository,
        IDeviceEventService deviceEventService)
    {
      _logger = logger;
      _eventRepository = eventRepository;
      _deviceEventService = deviceEventService;
    }

    /// <summary>
    /// 创建设备事件
    /// </summary>
    public async Task<long> CreateEventAsync(long deviceId, string eventName, EventCondition condition, string description = null)
    {
      try
      {
        // 检查事件名称是否存在
        if (await IsEventNameExistsAsync(deviceId, eventName))
        {
          throw new Exception($"设备ID {deviceId} 下已存在同名事件: {eventName}");
        }

        // 创建事件实体
        var eventEntity = new Entity.DeviceEvent
        {
          DeviceId = deviceId,
          EventName = eventName,
          Enable = true,
          TriggerEventType = Entity.TriggerEventTypeEnum.属性触发,
          // TODO: 将条件转换为CustomEventWhereList
          Description = description,
          CreateTime = DateTime.Now,
          UpdateTime = DateTime.Now
        };

        // 保存到数据库
        await _eventRepository.InsertAsync(eventEntity);

        // 在事件服务中注册事件
        await _deviceEventService.RegisterEventAsync(deviceId, eventName, condition);

        _logger.LogInformation("创建设备事件成功: DeviceId={DeviceId}, EventName={EventName}",
            deviceId, eventName);

        return eventEntity.Id;
      }
      catch (Exception ex)
      {
        _logger.LogError(ex, "创建设备事件失败: DeviceId={DeviceId}, EventName={EventName}",
            deviceId, eventName);
        throw;
      }
    }

    /// <summary>
    /// 更新设备事件
    /// </summary>
    public async Task UpdateEventAsync(long eventId, string eventName, EventCondition condition, string description = null)
    {
      try
      {
        // 获取事件实体
        var eventEntity = await _eventRepository.GetFirstAsync(e => e.Id == eventId);
        if (eventEntity == null)
        {
          throw new Exception($"事件不存在: {eventId}");
        }

        // 检查事件名称是否存在
        if (await IsEventNameExistsAsync(eventEntity.DeviceId, eventName, eventId))
        {
          throw new Exception($"设备ID {eventEntity.DeviceId} 下已存在同名事件: {eventName}");
        }

        // 更新事件实体
        eventEntity.EventName = eventName;
        eventEntity.Description = description;
        eventEntity.UpdateTime = DateTime.Now;
        // TODO: 更新CustomEventWhereList

        // 保存到数据库
        await _eventRepository.UpdateAsync(eventEntity);

        // 在事件服务中更新事件
        await _deviceEventService.UpdateEventAsync(eventId, eventName, condition);

        _logger.LogInformation("更新设备事件成功: EventId={EventId}, DeviceId={DeviceId}, EventName={EventName}",
            eventId, eventEntity.DeviceId, eventName);
      }
      catch (Exception ex)
      {
        _logger.LogError(ex, "更新设备事件失败: EventId={EventId}, EventName={EventName}",
            eventId, eventName);
        throw;
      }
    }

    /// <summary>
    /// 删除设备事件
    /// </summary>
    public async Task DeleteEventAsync(long eventId)
    {
      try
      {
        // 获取事件实体
        var eventEntity = await _eventRepository.GetFirstAsync(e => e.Id == eventId);
        if (eventEntity == null)
        {
          throw new Exception($"事件不存在: {eventId}");
        }

        // 删除事件实体
        await _eventRepository.DeleteAsync(eventEntity);

        // 在事件服务中删除事件
        await _deviceEventService.DeleteEventAsync(eventId);

        _logger.LogInformation("删除设备事件成功: EventId={EventId}, DeviceId={DeviceId}",
            eventId, eventEntity.DeviceId);
      }
      catch (Exception ex)
      {
        _logger.LogError(ex, "删除设备事件失败: EventId={EventId}", eventId);
        throw;
      }
    }

    /// <summary>
    /// 启用设备事件
    /// </summary>
    public async Task EnableEventAsync(long eventId)
    {
      try
      {
        // 获取事件实体
        var eventEntity = await _eventRepository.GetFirstAsync(e => e.Id == eventId);
        if (eventEntity == null)
        {
          throw new Exception($"事件不存在: {eventId}");
        }

        // 启用事件实体
        eventEntity.Enable = true;
        eventEntity.UpdateTime = DateTime.Now;

        // 保存到数据库
        await _eventRepository.UpdateAsync(eventEntity);

        // 在事件服务中启用事件
        await _deviceEventService.EnableEventAsync(eventId);

        _logger.LogInformation("启用设备事件成功: EventId={EventId}, DeviceId={DeviceId}",
            eventId, eventEntity.DeviceId);
      }
      catch (Exception ex)
      {
        _logger.LogError(ex, "启用设备事件失败: EventId={EventId}", eventId);
        throw;
      }
    }

    /// <summary>
    /// 禁用设备事件
    /// </summary>
    public async Task DisableEventAsync(long eventId)
    {
      try
      {
        // 获取事件实体
        var eventEntity = await _eventRepository.GetFirstAsync(e => e.Id == eventId);
        if (eventEntity == null)
        {
          throw new Exception($"事件不存在: {eventId}");
        }

        // 禁用事件实体
        eventEntity.Enable = false;
        eventEntity.UpdateTime = DateTime.Now;

        // 保存到数据库
        await _eventRepository.UpdateAsync(eventEntity);

        // 在事件服务中禁用事件
        await _deviceEventService.DisableEventAsync(eventId);

        _logger.LogInformation("禁用设备事件成功: EventId={EventId}, DeviceId={DeviceId}",
            eventId, eventEntity.DeviceId);
      }
      catch (Exception ex)
      {
        _logger.LogError(ex, "禁用设备事件失败: EventId={EventId}", eventId);
        throw;
      }
    }

    /// <summary>
    /// 获取设备事件列表
    /// </summary>
    public async Task<List<DeviceEventInfo>> GetDeviceEventsAsync(long deviceId)
    {
      try
      {
        // 从事件服务获取设备事件列表
        return await _deviceEventService.GetDeviceEventsAsync(deviceId);
      }
      catch (Exception ex)
      {
        _logger.LogError(ex, "获取设备事件列表失败: DeviceId={DeviceId}", deviceId);
        throw;
      }
    }

    /// <summary>
    /// 获取事件详情
    /// </summary>
    public async Task<DeviceEventInfo> GetEventAsync(long eventId)
    {
      try
      {
        // 从事件服务获取事件详情
        return await _deviceEventService.GetEventAsync(eventId);
      }
      catch (Exception ex)
      {
        _logger.LogError(ex, "获取事件详情失败: EventId={EventId}", eventId);
        throw;
      }
    }

    /// <summary>
    /// 获取所有事件
    /// </summary>
    public async Task<List<DeviceEventInfo>> GetAllEventsAsync()
    {
      try
      {
        // 获取所有设备ID
        var deviceIds = await _eventRepository.GetListAsync();
        var distinctDeviceIds = deviceIds.Select(e => e.DeviceId).Distinct().ToList();

        // 获取所有设备的事件列表
        var allEvents = new List<DeviceEventInfo>();
        foreach (var deviceId in distinctDeviceIds)
        {
          var deviceEvents = await GetDeviceEventsAsync(deviceId);
          allEvents.AddRange(deviceEvents);
        }

        return allEvents;
      }
      catch (Exception ex)
      {
        _logger.LogError(ex, "获取所有事件列表失败");
        throw;
      }
    }

    /// <summary>
    /// 检查事件名称是否存在
    /// </summary>
    public async Task<bool> IsEventNameExistsAsync(long deviceId, string eventName, long? excludeEventId = null)
    {
      try
      {
        // 构建查询
        var query = _eventRepository.AsQueryable()
                   .Where(e => e.DeviceId == deviceId && e.EventName == eventName);

        // 如果指定了排除的事件ID，则排除该事件
        if (excludeEventId.HasValue)
        {
          query = query.Where(e => e.Id != excludeEventId.Value);
        }

        // 执行查询
        return await query.AnyAsync();
      }
      catch (Exception ex)
      {
        _logger.LogError(ex, "检查事件名称是否存在失败: DeviceId={DeviceId}, EventName={EventName}",
            deviceId, eventName);
        throw;
      }
    }
  }
}