using EdgeGateway.Device.Entity.Models.Events;

namespace EdgeGateway.Device.Services.Events;

/// <summary>
///     设备事件服务接口，负责接收设备数据并触发事件
/// </summary>
public interface IDeviceEventService
{
  /// <summary>
  ///     初始化服务
  /// </summary>
  /// <returns>初始化任务</returns>
  Task InitializeAsync();

  /// <summary>
  ///     接收设备数据并处理
  /// </summary>
  /// <param name="deviceId">设备ID</param>
  /// <param name="deviceIdentifier">设备标识符</param>
  /// <param name="payLoad">设备数据</param>
  /// <returns>处理任务</returns>
  Task ProcessDeviceDataAsync(long deviceId, string deviceIdentifier, PayLoad payLoad);

  /// <summary>
  ///     处理设备状态变更
  /// </summary>
  /// <param name="deviceId">设备ID</param>
  /// <param name="deviceIdentifier">设备标识符</param>
  /// <param name="status">设备状态</param>
  /// <returns>处理任务</returns>
  Task ProcessDeviceStatusChangeAsync(long deviceId, string deviceIdentifier, bool status);

  /// <summary>
  ///     注册事件
  /// </summary>
  /// <param name="deviceId">设备ID</param>
  /// <param name="eventName">事件名称</param>
  /// <param name="condition">事件条件</param>
  /// <returns>事件ID</returns>
  Task<long> RegisterEventAsync(long deviceId, string eventName, EventCondition condition);

  /// <summary>
  ///     更新事件
  /// </summary>
  /// <param name="eventId">事件ID</param>
  /// <param name="eventName">事件名称</param>
  /// <param name="condition">事件条件</param>
  /// <returns>更新任务</returns>
  Task UpdateEventAsync(long eventId, string eventName, EventCondition condition);

  /// <summary>
  ///     删除事件
  /// </summary>
  /// <param name="eventId">事件ID</param>
  /// <returns>删除任务</returns>
  Task DeleteEventAsync(long eventId);

  /// <summary>
  ///     启用事件
  /// </summary>
  /// <param name="eventId">事件ID</param>
  /// <returns>启用任务</returns>
  Task EnableEventAsync(long eventId);

  /// <summary>
  ///     禁用事件
  /// </summary>
  /// <param name="eventId">事件ID</param>
  /// <returns>禁用任务</returns>
  Task DisableEventAsync(long eventId);

  /// <summary>
  ///     获取设备的所有事件
  /// </summary>
  /// <param name="deviceId">设备ID</param>
  /// <returns>事件列表</returns>
  Task<List<DeviceEventInfo>> GetDeviceEventsAsync(long deviceId);

  /// <summary>
  ///     获取事件详情
  /// </summary>
  /// <param name="eventId">事件ID</param>
  /// <returns>事件详情</returns>
  Task<DeviceEventInfo> GetEventAsync(long eventId);
}

/// <summary>
///     设备事件信息
/// </summary>
public class DeviceEventInfo
{
  /// <summary>
  ///     事件ID
  /// </summary>
  public long Id { get; set; }

  /// <summary>
  ///     设备ID
  /// </summary>
  public long DeviceId { get; set; }

  /// <summary>
  ///     事件名称
  /// </summary>
  public string Name { get; set; }

  /// <summary>
  ///     事件条件
  /// </summary>
  public EventCondition Condition { get; set; }

  /// <summary>
  ///     事件触发器列表
  /// </summary>
  public List<EventTrigger> Triggers { get; set; } = new();

  /// <summary>
  ///     是否启用
  /// </summary>
  public bool Enabled { get; set; }

  /// <summary>
  ///     创建时间
  /// </summary>
  public DateTime CreatedTime { get; set; }

  /// <summary>
  ///     更新时间
  /// </summary>
  public DateTime UpdatedTime { get; set; }

  /// <summary>
  ///     上次触发时间
  /// </summary>
  public DateTime? LastTriggerTime { get; set; }

  /// <summary>
  ///     触发次数
  /// </summary>
  public int TriggerCount { get; set; }
}