using EdgeGateway.Device.Services.Events;

namespace EdgeGateway.Device.Extensions;

/// <summary>
///     服务集合扩展类
/// </summary>
public static class ServiceCollectionExtensions
{
  /// <summary>
  ///     添加设备事件服务
  /// </summary>
  /// <param name="services">服务集合</param>
  /// <returns>服务集合</returns>
  public static IServiceCollection AddDeviceEventServices(this IServiceCollection services)
    {
        if (services == null) throw new ArgumentNullException(nameof(services));

        // 注册设备事件服务
        services.AddSingleton<IDeviceEventService, DeviceEventService>();

        // 注册设备事件配置服务
        services.AddScoped<IDeviceEventConfigService, DeviceEventConfigService>();

        // 初始化设备事件服务
        var serviceProvider = services.BuildServiceProvider();
        var deviceEventService = serviceProvider.GetService<IDeviceEventService>();
        deviceEventService?.InitializeAsync().GetAwaiter().GetResult();

        return services;
    }
}