global using Furion.Logging;
global using System.Collections.Generic;
global using System;
global using System.Collections.Concurrent;
global using System.Threading.Tasks;
global using Furion.JsonSerialization;
global using System.Linq;
global using System.Threading;
global using System.Reflection;
global using Microsoft.Extensions.DependencyInjection;
global using System.ComponentModel;
global using Microsoft.Extensions.Hosting;
global using EdgeGateway.Driver.Entity.Model;
global using EdgeGateway.Driver.Interface;
global using EdgeGateway.SqlSugar.SqlSugar;
global using Furion.DependencyInjection;
global using Furion.FriendlyException;
global using SqlSugar;
global using EdgeGateway.Core.Extension;
global using EdgeGateway.Device.Entity;
global using EdgeGateway.Device.Entity.Model;
global using EdgeGateway.Device.HostServices;
global using EdgeGateway.Driver.Entity.Attributes;
global using Furion.DynamicApiController;
global using Mapster;
global using Microsoft.AspNetCore.Mvc;
global using EdgeGateway.Core.Const;
global using EdgeGateway.Core;
global using Microsoft.Extensions.Logging;
global using EdgeGateway.Core.Queue;
global using EdgeGateway.Device.Services;
global using EdgeGateway.Device.Services.Clients;
global using Furion;
global using System.Security.AccessControl;
global using System.Security.Principal;
global using System.Text.RegularExpressions;
global using EdgeGateway.Driver.Interface.Utils;
global using Spectre.Console;
global using System.Text;
global using System.Text.Json;
global using System.Threading.Channels;
global using Yitter.IdGenerator;
global using Furion.SpecificationDocument;
global using EdgeGateway.TimeSeriesStorage.Abstractions.Interfaces;
global using EdgeGateway.WebSocket;
global using Furion.EventBus;
global using EdgeGateway.Engine.Abstractions;
global using EdgeGateway.Driver.Entity.Const;
global using EdgeGateway.Base.Entity.Enums;
global using EdgeGateway.Pipeline.Entity.Model;
global using System.Diagnostics;
global using EdgeGateway.Device.Entity.Const;
global using EdgeGateway.Device.Services.Helpers;
global using EdgeGateway.Shared.Storage;
global using EdgeGateway.TimeSeriesStorage.Abstractions.Models;
global using Furion.Templates;