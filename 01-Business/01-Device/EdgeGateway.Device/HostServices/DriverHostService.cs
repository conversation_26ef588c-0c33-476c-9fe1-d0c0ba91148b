using System.Diagnostics;
using EdgeGateway.Device.Entity.Models;
using EdgeGateway.Driver.Entity.Model;
using System.Text.RegularExpressions;
using System.Reflection;

namespace EdgeGateway.Device.HostServices;

/// <summary>
///     驱动服务类，负责加载和管理所有协议驱动
/// </summary>
public class DriverHostService : IHostedService, IDisposable
{
    /// <summary>
    ///     加密驱动文件的存放路径，默认为程序根目录下的drivers文件夹
    /// </summary>
    private readonly string _driverPath = Path.Combine(AppDomain.CurrentDomain.BaseDirectory, @"drivers/");

    /// <summary>
    ///     解密后的DLL临时存放路径，使用系统临时目录下的随机子目录
    /// </summary>
    private readonly string _tempPath;

    /// <summary>
    ///     日志
    /// </summary>
    private readonly ILogger<DriverHostService> _logger;

    /// <summary>
    ///     存储解密后的驱动程序文件路径列表
    /// </summary>
    private readonly List<string> _driverFiles = new();

    /// <summary>
    ///     存储所有已加载的驱动程序信息
    /// </summary>
    public List<Entity.Models.Driver> Drivers = new();

    /// <summary>
    /// </summary>
    /// <param name="logger"></param>
    public DriverHostService(ILogger<DriverHostService> logger)
    {
        _logger = logger;
        // 在系统临时目录下创建随机名称的子目录
        _tempPath = Path.Combine(
            Path.GetTempPath(),
            "EdgeGW_" +
            DateTime.Now.Ticks.ToString("x") + "_" +
            Guid.NewGuid().ToString("N").Substring(0, 8)
        );
    }

    /// <summary>
    ///     服务启动时执行的方法，负责加载所有驱动
    /// </summary>
    public Task StartAsync(CancellationToken cancellationToken)
    {
        try
        {
            _logger.LogInformation("驱动加载中...");

            // 清理可能存在的旧临时文件
            CleanupOldTempDirectories();

            // 确保临时目录安全性
            EnsureTempDirectorySecurity();

            // 检查驱动目录是否存在
            if (Directory.Exists(_driverPath))
            {
                // 查找所有加密的驱动文件（.epdll后缀）
                // 使用文件名分组来避免重复加载同一个驱动的不同版本
                var encryptedFiles = Directory.GetFiles(_driverPath, "*.epdll", SearchOption.AllDirectories)
                    .Select(x => new FileInfo(x))
                    .GroupBy(x =>
                    {
                        // 获取文件名
                        var fileName = Path.GetFileNameWithoutExtension(x.Name);
                        // 获取版本号
                        var index = fileName.LastIndexOf("_");
                        // 如果文件名包含版本号（使用_分隔），则按版本号前的部分分组
                        return index >= 0 ? fileName.Substring(index) : fileName;
                    })
                    .Select(group => group.First()) // 每组只取第一个文件
                    .Select(x => x.FullName)
                    .ToList();

                // 处理每个加密的驱动文件
                foreach (var encryptedFile in encryptedFiles)
                    try
                    {
                        // 读取加密文件内容
                        var encryptedBytes = File.ReadAllBytes(encryptedFile);
                        // 使用自定义解密算法解密DLL文件
                        var dllBytes = CustomEncryption.Decrypt(encryptedBytes);
                        // 构建解密后的临时文件路径，保持原有的目录结构
                        var relativePath = Path.GetRelativePath(_driverPath, encryptedFile);
                        var tempFilePath = Path.Combine(
                            _tempPath,
                            Path.GetDirectoryName(relativePath)!,
                            Path.ChangeExtension(Path.GetFileName(relativePath), ".dll")
                        );

                        // 确保临时文件的目录结构存在
                        Directory.CreateDirectory(Path.GetDirectoryName(tempFilePath)!);
                        // 将解密后的DLL保存到临时目录
                        WriteDecryptedDll(tempFilePath, dllBytes);
                    }
                    catch (Exception ex)
                    {
                        _logger.LogError($"解密失败 {encryptedFile}: {ex.Message}");
                    }
            }

            // 加载所有解密后的驱动
            LoadAllDrivers();
        }
        catch (Exception ex)
        {
            _logger.LogError($"[驱动加载] 故障：{ex.Message}");
        }

        return Task.CompletedTask;
    }

    /// <summary>
    ///     清理旧的临时目录
    /// </summary>
    private void CleanupOldTempDirectories()
    {
        try
        {
            // 清理系统临时目录中的旧文件
            var tempRoot = Path.GetTempPath();
            var oldTempDirs = Directory.GetDirectories(tempRoot, "EdgeGW_*");
            foreach (var dir in oldTempDirs)
                try
                {
                    if (Directory.Exists(dir))
                    {
                        // 尝试清理目录中的所有文件
                        foreach (var file in Directory.GetFiles(dir, "*.*", SearchOption.AllDirectories))
                            try
                            {
                                File.SetAttributes(file, FileAttributes.Normal);
                                File.Delete(file);
                            }
                            catch (Exception ex)
                            {
                                _logger.LogWarning($"清理旧文件失败 {Path.GetFileName(file)}: {ex.Message}");
                            }

                        Directory.Delete(dir, true);
                        _logger.LogInformation($"清理到旧的临时目录: {dir}");
                    }
                }
                catch (Exception ex)
                {
                    _logger.LogWarning($"清理旧临时目录失败 {dir}: {ex.Message}");
                }

            // 检查并清理之前记录的未能清理的路径
            var failedPathsFile = Path.Combine(AppDomain.CurrentDomain.BaseDirectory, "cleanup_failed_paths.txt");
            if (File.Exists(failedPathsFile))
                try
                {
                    var failedPaths = File.ReadAllLines(failedPathsFile);
                    var newFailedPaths = new List<string>();

                    foreach (var line in failedPaths)
                        try
                        {
                            // 解析记录的路径
                            var match = Regex.Match(line, @"Failed to delete (.+)$");
                            if (match.Success)
                            {
                                var path = match.Groups[1].Value.Trim();
                                if (Directory.Exists(path))
                                {
                                    // 尝试清理目录中的所有文件
                                    foreach (var file in Directory.GetFiles(path, "*.*", SearchOption.AllDirectories))
                                        try
                                        {
                                            File.SetAttributes(file, FileAttributes.Normal);
                                            File.Delete(file);
                                        }
                                        catch (Exception ex)
                                        {
                                            _logger.LogWarning($"清理历史遗留文件失败 {Path.GetFileName(file)}: {ex.Message}");
                                        }

                                    Directory.Delete(path, true);
                                    _logger.LogInformation($"成功清理历史遗留目录: {path}");
                                }
                            }
                        }
                        catch (Exception ex)
                        {
                            newFailedPaths.Add(line);
                            _logger.LogWarning($"清理历史遗留目录失败: {ex.Message}");
                        }

                    // 如果还有未能清理的路径，重新写入文件
                    if (newFailedPaths.Any())
                        File.WriteAllLines(failedPathsFile, newFailedPaths);
                    else
                        // 如果所有路径都清理成功，删除记录文件
                        File.Delete(failedPathsFile);
                }
                catch (Exception ex)
                {
                    _logger.LogError($"处理历史清理记录文件失败: {ex.Message}");
                }
        }
        catch (Exception ex)
        {
            _logger.LogError($"清理旧临时文件过程中发生错误: {ex.Message}");
        }
    }

    /// <summary>
    ///     确保临时目录的安全性
    /// </summary>
    private void EnsureTempDirectorySecurity()
    {
        if (Directory.Exists(_tempPath))
        {
            Directory.Delete(_tempPath, true);
        }

        // 创建临时目录
        var dirInfo = Directory.CreateDirectory(_tempPath);

        // 根据操作系统设置不同的安全策略
        if (OperatingSystem.IsWindows())
        {
            try
            {
                var security = dirInfo.GetAccessControl();
                security.SetAccessRuleProtection(true, false); // 禁用继承

                // 仅允许当前用户完全控制
                var currentUser = WindowsIdentity.GetCurrent();
                var userRule = new FileSystemAccessRule(
                    currentUser.Name,
                    FileSystemRights.FullControl,
                    AccessControlType.Allow);

                security.AddAccessRule(userRule);
                dirInfo.SetAccessControl(security);
            }
            catch (PlatformNotSupportedException ex)
            {
                _logger.LogWarning($"设置目录ACL权限失败(仅Windows支持): {ex.Message}");
            }
        }
        else
        {
            try
            {
                // 在Unix系统上尝试使用chmod设置权限
                var chmodProcess = new Process
                {
                    StartInfo = new ProcessStartInfo
                    {
                        FileName = "chmod",
                        Arguments = $"700 {_tempPath}",
                        RedirectStandardOutput = true,
                        RedirectStandardError = true,
                        UseShellExecute = false,
                        CreateNoWindow = true
                    }
                };
                chmodProcess.Start();
                chmodProcess.WaitForExit();
            }
            catch (Exception ex)
            {
                _logger.LogWarning($"设置目录权限失败: {ex.Message}");
            }
        }

        try
        {
            // 设置隐藏属性 (仅Windows支持)
            if (OperatingSystem.IsWindows())
            {
                File.SetAttributes(_tempPath, FileAttributes.Hidden | FileAttributes.System);
            }
        }
        catch (PlatformNotSupportedException ex)
        {
            _logger.LogWarning($"设置目录隐藏属性失败(仅Windows支持): {ex.Message}");
        }
    }

    /// <summary>
    ///     安全地写入解密后的DLL文件
    /// </summary>
    private void WriteDecryptedDll(string tempFilePath, byte[] dllBytes)
    {
        // 使用随机文件名
        var randomFileName = Path.GetRandomFileName() + ".dll";
        var finalPath = Path.Combine(Path.GetDirectoryName(tempFilePath)!, randomFileName);

        // 使用FileStream确保文件被正确关闭
        using (var fs = new FileStream(finalPath, FileMode.Create, FileAccess.Write, FileShare.None))
        {
            fs.Write(dllBytes, 0, dllBytes.Length);
        }

        // 设置文件属性
        File.SetAttributes(finalPath, FileAttributes.Hidden | FileAttributes.System);

        // 将实际路径添加到列表
        _driverFiles.Add(finalPath);
    }

    /// <summary>
    ///     服务停止时执行的方法，负责清理临时文件
    /// </summary>
    public async Task StopAsync(CancellationToken cancellationToken)
    {
        try
        {
            _logger.LogInformation("驱动服务正在停止，清理临时文件...");

            // 安全清理临时文件
            await SecureCleanupAsync();

            _logger.LogWarning("[驱动停止] 服务已停用");
        }
        catch (Exception ex)
        {
            _logger.LogError($"清理临时文件失败: {ex.Message}");
        }
    }

    /// <summary>
    ///     安全清理临时文件
    /// </summary>
    private async Task SecureCleanupAsync()
    {
        if (!Directory.Exists(_tempPath)) return;

        try
        {
            _logger.LogInformation("开始清理临时文件...");

            // 先卸载所有已加载的程序集
            foreach (var driver in Drivers)
                try
                {
                    // 获取程序集
                    var assembly = driver.Type.Assembly;
                    // 清理驱动实例
                    driver.Type = null;
                }
                catch (Exception ex)
                {
                    _logger.LogError($"卸载驱动失败: {ex.Message}");
                }

            // 清空驱动列表
            Drivers.Clear();

            // 强制GC回收
            GC.Collect();
            GC.WaitForPendingFinalizers();
            GC.Collect(); // 二次回收

            // 等待文件句柄释放
            await Task.Delay(500);

            foreach (var file in _driverFiles.ToList()) // 使用 ToList() 创建副本
            {
                var retryCount = 3;
                while (retryCount > 0)
                    try
                    {
                        if (File.Exists(file))
                        {
                            // 尝试清除文件的只读属性
                            File.SetAttributes(file, FileAttributes.Normal);

                            // 删除文件
                            File.Delete(file);
                            _logger.LogDebug($"成功删除文件: {Path.GetFileName(file)}");
                            break;
                        }
                    }
                    catch (Exception ex)
                    {
                        retryCount--;
                        if (retryCount == 0)
                        {
                            _logger.LogError($"删除文件 {Path.GetFileName(file)} 失败: {ex.Message}");
                        }
                        else
                        {
                            await Task.Delay(200); // 短暂等待后重试
                            // 再次尝试GC
                            GC.Collect();
                            GC.WaitForPendingFinalizers();
                        }
                    }
            }

            // 清空文件列表
            _driverFiles.Clear();

            // 等待所有文件操作完成
            await Task.Delay(500);

            // 尝试删除临时目录
            if (Directory.Exists(_tempPath))
            {
                var retryCount = 3; // 增加重试次数
                while (retryCount > 0)
                    try
                    {
                        // 确保目录中的所有文件都可以访问
                        foreach (var file in Directory.GetFiles(_tempPath, "*.*", SearchOption.AllDirectories))
                            try
                            {
                                File.SetAttributes(file, FileAttributes.Normal);
                                // 尝试打开文件以确保它未被锁定
                                using var fs = new FileStream(file, FileMode.Open, FileAccess.ReadWrite, FileShare.None);
                            }
                            catch (Exception ex)
                            {
                                _logger.LogWarning($"文件 {Path.GetFileName(file)} 可能被锁定: {ex.Message}");
                                // 如果文件被锁定，等待后继续
                                await Task.Delay(200);
                            }

                        Directory.Delete(_tempPath, true);
                        _logger.LogInformation($"成功删除临时目录: {_tempPath}");
                        break;
                    }
                    catch (Exception ex)
                    {
                        retryCount--;
                        if (retryCount == 0)
                        {
                            _logger.LogError($"删除临时目录失败，已重试3次: {ex.Message}");
                            // 如果实在删除不了，至少记录下来
                            await File.WriteAllTextAsync(
                                Path.Combine(AppDomain.CurrentDomain.BaseDirectory, "cleanup_failed_paths.txt"),
                                $"{DateTime.Now}: Failed to delete {_tempPath}\n"
                            );
                            break;
                        }

                        _logger.LogWarning($"删除临时目录失败，将重试: {ex.Message}");
                        await Task.Delay(1000); // 增加等待时间到1秒

                        // 再次尝试GC
                        GC.Collect();
                        GC.WaitForPendingFinalizers();
                    }
            }
        }
        catch (Exception ex)
        {
            _logger.LogError($"清理过程中发生错误: {ex.Message}");
            throw;
        }
    }

    /// <summary>
    ///     释放资源
    /// </summary>
    public void Dispose()
    {
        try
        {
            // 确保在程序意外退出时也能清理临时文件
            SecureCleanupAsync().Wait(TimeSpan.FromSeconds(10)); // 设置超时时间
        }
        catch (Exception ex)
        {
            _logger.LogError($"在 Dispose 中清理临时文件失败: {ex.Message}");
        }
    }

    /// <summary>
    ///     初始化全部驱动加载
    ///     This method initializes all driver loads.
    /// </summary>
    /// <exception cref="AppFriendlyException"></exception>
    [NonAction]
    private void LoadAllDrivers()
    {
        // 检查驱动文件
        if (_driverFiles.Count == 0) throw Oops.Oh("驱动加载失败，一般是驱动项目的dll没有复制到驱动文件夹");
        // 创建表格
        var table = new Table();
        // 添加表格列
        table.AddColumns("[red]协议名称[/]", "[olive]协议厂商[/]", "[green]协议类型[/]", "[blue]协议版本[/]");
        // 设置表格边框
        table.Border = TableBorder.HeavyHead;
        // 创建进度条
        AnsiConsole.Progress()
            // 添加进度条列
            .Columns(new TaskDescriptionColumn(), new ProgressBarColumn(), new PercentageColumn(), new RemainingTimeColumn(), new SpinnerColumn())
            .Start(ctx =>
            {
                // 初始化已处理文件数和总文件数
                var filesProcessed = 0;
                var totalFiles = _driverFiles.Count;
                // 创建进度条任务
                var task1 = ctx.AddTask("[bold][green]协议扫描进度：[/][/]");
                // 加载驱动
                foreach (var file in _driverFiles)
                    try
                    {
                        // 更新进度
                        filesProcessed++;
                        var progress = (double)filesProcessed / totalFiles * 100;
                        // 加载驱动
                        var dll = Assembly.LoadFrom(file);
                        // 遍历驱动类型
                        foreach (var type in dll.GetTypes().Where(x => typeof(IDriver).IsAssignableFrom(x) && x.IsClass))
                            try
                            {
                                // 创建驱动对象
                                var driver = new Entity.Models.Driver
                                {
                                    FileName = Path.GetFileName(file),
                                    Type = type
                                };
                                // 获取驱动属性
                                var attribute = type.CustomAttributes.FirstOrDefault()?.ConstructorArguments;
                                if (attribute != null)
                                {
                                    // 设置驱动名称
                                    driver.DriverName = attribute[0].ToString().Replace("\"", "");
                                    // 设置驱动版本
                                    driver.Version = attribute[1].ToString().Replace("\"", "");
                                    // 设置驱动厂商
                                    driver.Manufacturer = attribute[2].ToString().Replace("\"", "");
                                    // 设置驱动类型
                                    driver.DriverType = attribute[3].ToString().Replace("\"", "");
                                    // 设置是否创建变量
                                    driver.CreateVariables = Convert.ToBoolean(attribute[4].Value);
                                    // 设置是否为网口设备
                                    driver.IsNet = Convert.ToBoolean(attribute[5].Value);
                                    // 设置协议描述 - 检查是否有第7个参数
                                    driver.Description = attribute.Count > 6
                                        ? attribute[6].ToString().Replace("\"", "")
                                        : $"{driver.DriverName} {driver.Version}"; // 如果没有描述参数，使用驱动名+版本作为默认描述
                                }

                                // 获取驱动构造函数
                                var constructor = driver.Type.GetConstructor([typeof(DriverInfoDto)]) ?? throw Oops.Oh("该协议版本暂不支持！");
                                // 创建驱动对象
                                if (constructor.Invoke([new DriverInfoDto { DeviceId = 0, Identifier = "" }]) is not IDriver iDriver)
                                    throw Oops.Oh($"[驱动协议]-{driver.DriverName}，未找到");
                                // 属性配置
                                driver.PropertyConfiguration = iDriver.PropertyConfiguration;
                                // 遍历协议属性
                                foreach (var property in driver.Type.GetProperties())
                                {
                                    // 获取配置属性
                                    var config = property.GetCustomAttribute<ConfigParameterAttribute>();
                                    // 检查配置属性是否存在
                                    if (config == null || (!config.Display && config.DisplayExpress.IsNullOrEmpty())) continue;
                                    // 协议字段配置
                                    var driverConfig = new DriverConfigs
                                    {
                                        // 设置配置标识
                                        Identifier = property.Name,
                                        // 设置配置名称
                                        ConfigName = config.ConfigName,
                                        // 设置配置组名称
                                        ConfigGroupName = config.GroupName,
                                        // 设置是否必填
                                        Required = config.Required,
                                        // 设置配置值
                                        Value = property.GetValue(iDriver),
                                        // 设置是否显示
                                        Display = config.Display,
                                        // 设置显示表达式
                                        DisplayExpress = config.DisplayExpress,
                                        // 设置配置类型
                                        Type = config.Type,
                                        // 设置配置顺序
                                        Order = config.Order,
                                        // 设置配置描述
                                        Description = config.Description
                                    };
                                    // 处理枚举
                                    if (property.PropertyType.BaseType == typeof(Enum))
                                    {
                                        // 获取枚举字段 
                                        var fields = property.PropertyType.GetFields(BindingFlags.Static | BindingFlags.Public);
                                        // 获取枚举信息
                                        var enumInfos = fields.ToDictionary(f => f.GetCustomAttribute<DescriptionAttribute>()?.Description ?? f.Name, f => (int)f.GetValue(null)!);
                                        // 枚举使用数字
                                        driverConfig.Value = Convert.ToInt32(property.GetValue(iDriver));
                                        // 设置枚举信息
                                        driverConfig.EnumInfo = JSON.Serialize(enumInfos);
                                    }

                                    // 添加到驱动连接配置
                                    driver.ConnectionConfig.Add(driverConfig);
                                }

                                // 打印协议清单
                                table.AddRow(driver.DriverName, driver.Manufacturer, driver.DriverType, driver.Version);
                                // 添加到驱动文件列表中，供后续加载使用
                                Drivers.Add(driver);
                            }
                            catch (Exception e)
                            {
                                // 打印错误信息
                                _logger.LogError("[驱动加载] 故障：" + e.Message);
                            }

                        // 更新进度 
                        task1.Increment(progress);
                    }
                    catch (Exception e)
                    {
                        // 打印错误信息 
                        _logger.LogError("[驱动加载] 故障：" + e.Message);
                    }
            });

        // 打印协议清单
        AnsiConsole.Write(table);
        // 打印协议数量
        AnsiConsole.Write(new BreakdownChart().Width(80)
            //  .Label("[green bold underline]协议数量[/]")
            //  .CenterLabel()
            .AddItem("CNC", Drivers.Count(w => w.DriverType == "CNC"), Color.Yellow)
            .AddItem("PLC", Drivers.Count(w => w.DriverType == "PLC"), Color.Green)
            .AddItem("其他", Drivers.Count(w => w.DriverType != "PLC" && w.DriverType != "CNC"), Color.Blue));
    }

    /// <summary>
    ///     添加设备增加默认点位值
    ///     This method adds default point values when a device is added
    /// </summary>
    /// <param name="device"></param>
    /// <param name="deviceConfigs"></param>
    [NonAction]
    public void AddConfigs(Entity.Device device, Dictionary<string, object> deviceConfigs = null)
    {
        // 检查设备配置是否为空
        device.DeviceConfigs ??= new List<DeviceConfig>();
        // 获取驱动信息
        var driverInfo = Drivers.FirstOrDefault(x => x.DriverName == device.DriverName) ?? throw Oops.Oh("暂时不支持该协议！");
        // 生成设备连接配置
        CreateDeviceConfig(device, driverInfo, deviceConfigs);
    }

    /// <summary>
    ///     生成设备连接配置
    ///     This method creates device connection configurations.
    /// </summary>
    public void CreateDeviceConfig(Entity.Device device, Entity.Models.Driver driverInfo, Dictionary<string, object> deviceConfigs = null)
    {
        // 遍历驱动连接配置
        foreach (var config in driverInfo.ConnectionConfig)
        {
            // 过滤 标记为不显示的配置
            if (!config.Display && config.DisplayExpress.IsNullOrEmpty()) continue;
            // 连接配置
            var deviceConfig = device.DeviceConfigs.FirstOrDefault(x => x.Identifier == config.Identifier);
            // 检查连接配置是否存在
            if (deviceConfig == null)
            {
                // 创建连接配置
                var devConfig = new DeviceConfig
                {
                    // 设置配置标识
                    Identifier = config.Identifier,
                    // 设置配置描述
                    Description = config.Description,
                    // 设置配置组名称
                    GroupName = config.ConfigGroupName,
                    // 设置是否必填
                    IsRequired = config.Required,
                    // 设置配置值
                    Value = deviceConfigs != null && deviceConfigs.TryGetValue(config.Identifier, out var config1) ? config1 : ObjectExtension.GetJsonElementValue(config.Value),
                    // 设置是否显示
                    Display = config.Display,
                    // 设置显示表达式
                    DisplayExpress = config.DisplayExpress,
                    // 设置配置类型
                    Type = config.Type,
                    // 设置配置顺序
                    Order = config.Order,
                    // 设置枚举信息
                    EnumInfo = config.EnumInfo
                };
                // 添加到设备连接配置
                device.DeviceConfigs.Add(devConfig);
            }
            else
            {
                // 调整配置
                deviceConfig.Description = config.Description;
                // 设置配置组名称
                deviceConfig.GroupName = config.ConfigGroupName;
                // 设置是否必填
                deviceConfig.IsRequired = config.Required;
                // 设置是否显示
                deviceConfig.Display = config.Display;
                // 设置显示表达式
                deviceConfig.DisplayExpress = config.DisplayExpress;
                // 设置枚举信息
                deviceConfig.EnumInfo = config.EnumInfo;
                // 查找设备连接配置
                var index = device.DeviceConfigs.FindIndex(x => x.Identifier == deviceConfig.Identifier);
                // 检查设备连接配置是否存在
                if (index != -1)
                    // 设置设备连接配置
                    device.DeviceConfigs[index] = deviceConfig;
            }
        }
    }

    /// <summary>
    /// 热加载驱动
    /// </summary>
    /// <param name="driverName">驱动名称</param>
    /// <returns>加载结果</returns>
    public async Task<bool> ReloadDriverAsync(string driverName)
    {
        _logger.LogInformation($"开始热加载驱动: {driverName}");

        try
        {
            // 查找对应的加密驱动文件
            var encryptedFile = Directory.GetFiles(_driverPath, $"{driverName}*.epdll", SearchOption.AllDirectories)
                .OrderByDescending(f => f) // 按文件名排序，取最新版本
                .FirstOrDefault();

            if (encryptedFile == null)
            {
                _logger.LogWarning($"未找到驱动文件: {driverName}");
                return false;
            }

            // 解密驱动文件
            var encryptedBytes = await File.ReadAllBytesAsync(encryptedFile);
            var dllBytes = CustomEncryption.Decrypt(encryptedBytes);

            // 构建临时文件路径
            var tempFilePath = Path.Combine(
                _tempPath,
                Path.GetFileNameWithoutExtension(encryptedFile) + "_" + Guid.NewGuid().ToString("N") + ".dll"
            );

            // 保存解密后的DLL
            WriteDecryptedDll(tempFilePath, dllBytes);

            // 添加到驱动文件列表
            _driverFiles.Add(tempFilePath);

            // 加载程序集
            var assembly = Assembly.LoadFrom(tempFilePath);

            // 查找实现IDriver的类型
            var driverTypes = assembly.GetTypes()
                .Where(t => typeof(IDriver).IsAssignableFrom(t) && t.IsClass)
                .ToList();

            if (!driverTypes.Any())
            {
                _logger.LogWarning($"驱动文件中未找到实现IDriver接口的类: {driverName}");
                return false;
            }

            // 移除旧版本驱动
            var oldDrivers = Drivers.Where(d => d.DriverName == driverName).ToList();
            foreach (var oldDriver in oldDrivers)
            {
                Drivers.Remove(oldDriver);
            }

            // 加载新驱动
            foreach (var type in driverTypes)
            {
                try
                {
                    var driver = new Entity.Models.Driver
                    {
                        FileName = Path.GetFileName(tempFilePath),
                        Type = type
                    };

                    // 获取驱动属性
                    var attribute = type.CustomAttributes.FirstOrDefault()?.ConstructorArguments;
                    if (attribute != null)
                    {
                        // 设置驱动属性
                        driver.DriverName = attribute[0].ToString().Replace("\"", "");
                        // 设置驱动版本
                        driver.Version = attribute[1].ToString().Replace("\"", "");
                        // 设置驱动厂商
                        driver.Manufacturer = attribute[2].ToString().Replace("\"", "");
                        // 设置驱动类型
                        driver.DriverType = attribute[3].ToString().Replace("\"", "");
                        // 设置是否创建变量
                        driver.CreateVariables = Convert.ToBoolean(attribute[4].Value);
                        // 设置是否为网口设备
                        driver.IsNet = Convert.ToBoolean(attribute[5].Value);
                        // 设置协议描述 - 检查是否有第7个参数
                        driver.Description = attribute.Count > 6
                            ? attribute[6].ToString().Replace("\"", "")
                            : $"{driver.DriverName} {driver.Version}"; // 如果没有描述参数，使用驱动名+版本作为默认描述
                    }

                    // 获取驱动构造函数
                    var constructor = driver.Type.GetConstructor([typeof(DriverInfoDto)]);
                    if (constructor == null)
                    {
                        _logger.LogWarning($"驱动类型不支持标准构造函数: {type.FullName}");
                        continue;
                    }

                    // 创建驱动实例
                    var driverInstance = constructor.Invoke([new DriverInfoDto { DeviceId = 0, Identifier = "" }]) as IDriver;
                    if (driverInstance == null)
                    {
                        _logger.LogWarning($"无法创建驱动实例: {type.FullName}");
                        continue;
                    }

                    // 设置属性配置
                    driver.PropertyConfiguration = driverInstance.PropertyConfiguration;

                    // 遍历协议属性，与LoadAllDrivers方法中的逻辑保持一致
                    foreach (var property in driver.Type.GetProperties())
                    {
                        // 获取配置属性
                        var config = property.GetCustomAttribute<ConfigParameterAttribute>();
                        // 检查配置属性是否存在
                        if (config == null || (!config.Display && config.DisplayExpress.IsNullOrEmpty())) continue;
                        // 协议字段配置
                        var driverConfig = new DriverConfigs
                        {
                            // 设置配置标识
                            Identifier = property.Name,
                            // 设置配置名称
                            ConfigName = config.ConfigName,
                            // 设置配置组名称
                            ConfigGroupName = config.GroupName,
                            // 设置是否必填
                            Required = config.Required,
                            // 设置配置值
                            Value = property.GetValue(driverInstance),
                            // 设置是否显示
                            Display = config.Display,
                            // 设置显示表达式
                            DisplayExpress = config.DisplayExpress,
                            // 设置配置类型
                            Type = config.Type,
                            // 设置配置顺序
                            Order = config.Order,
                            // 设置配置描述
                            Description = config.Description
                        };
                        // 处理枚举
                        if (property.PropertyType.BaseType == typeof(Enum))
                        {
                            // 获取枚举字段 
                            var fields = property.PropertyType.GetFields(BindingFlags.Static | BindingFlags.Public);
                            // 获取枚举信息
                            var enumInfos = fields.ToDictionary(f => f.GetCustomAttribute<DescriptionAttribute>()?.Description ?? f.Name, f => (int)f.GetValue(null)!);
                            // 枚举使用数字
                            driverConfig.Value = Convert.ToInt32(property.GetValue(driverInstance));
                            // 设置枚举信息
                            driverConfig.EnumInfo = JSON.Serialize(enumInfos);
                        }

                        // 添加到驱动连接配置
                        driver.ConnectionConfig.Add(driverConfig);
                    }

                    // 添加到驱动列表
                    Drivers.Add(driver);

                    _logger.LogInformation($"成功加载驱动: {driver.DriverName} {driver.Version}");
                }
                catch (Exception ex)
                {
                    _logger.LogError(ex, $"加载驱动类型失败: {type.FullName}");
                }
            }

            return true;
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, $"热加载驱动失败: {driverName}");
            return false;
        }
    }
}