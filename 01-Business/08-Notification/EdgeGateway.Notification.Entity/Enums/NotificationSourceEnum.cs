namespace EdgeGateway.Notification.Entity.Enums;

/// <summary>
/// 通知来源枚举
/// </summary>
public enum NotificationSourceEnum
{
  /// <summary>
  /// 设备管理
  /// </summary>
  [Description("设备管理")]
  DeviceManagement = 1,

  /// <summary>
  /// 数据转发
  /// </summary>
  [Description("数据转发")]
  DataForwarding = 2,

  /// <summary>
  /// 系统管理
  /// </summary>
  [Description("系统管理")]
  SystemManagement = 3,

  /// <summary>
  /// 数据采集
  /// </summary>
  [Description("数据采集")]
  DataCollection = 4,

  /// <summary>
  /// 工作流编排
  /// </summary>
  [Description("工作流编排")]
  WorkflowEngine = 5,

  /// <summary>
  /// 安全监控
  /// </summary>
  [Description("安全监控")]
  SecurityMonitoring = 6,

  /// <summary>
  /// 设备监控
  /// </summary>
  [Description("设备监控")]
  DeviceMonitoring = 7
}