namespace EdgeGateway.Notification.Services;

/// <summary>
///     通知服务接口
/// </summary>
public interface INotificationService
{
  /// <summary>
  ///     创建通知
  /// </summary>
  /// <param name="dto">创建通知DTO</param>
  /// <returns>通知ID</returns>
  Task<long> CreateNotificationAsync(CreateNotificationDto dto);

  /// <summary>
  ///     分页查询通知
  /// </summary>
  /// <param name="query">查询条件</param>
  /// <returns>分页结果</returns>
  Task<SqlSugarPagedList<NotificationResponseDto>> GetNotificationsAsync(NotificationQueryDto query);

  /// <summary>
  ///     获取通知详情
  /// </summary>
  /// <param name="id">通知ID</param>
  /// <returns>通知详情</returns>
  Task<NotificationResponseDto?> GetNotificationByIdAsync(long id);

  /// <summary>
  ///     标记通知为已读
  /// </summary>
  /// <param name="id">通知ID</param>
  /// <returns>是否成功</returns>
  Task<bool> MarkAsReadAsync(long id);

  /// <summary>
  ///     标记所有通知为已读
  /// </summary>
  /// <returns>影响的行数</returns>
  Task<int> MarkAllAsReadAsync();

  /// <summary>
  ///     删除通知
  /// </summary>
  /// <param name="id">通知ID</param>
  /// <returns>是否成功</returns>
  Task<bool> DeleteNotificationAsync(long id);

  /// <summary>
  ///     清除已读通知
  /// </summary>
  /// <returns>删除的数量</returns>
  Task<int> ClearReadNotificationsAsync();

  /// <summary>
  ///     获取未读通知数量
  /// </summary>
  /// <returns>未读数量</returns>
  Task<int> GetUnreadCountAsync();

  /// <summary>
  ///     推送通知（包含持久化和实时推送）
  /// </summary>
  /// <param name="dto">创建通知DTO</param>
  /// <returns>通知ID</returns>
  Task<long> PushNotificationAsync(CreateNotificationDto dto);
}