using EdgeGateway.Forwarding.Entity;

namespace EdgeGateway.Notification.Events;

/// <summary>
///     通知事件处理器
/// </summary>
public class NotificationEventHandler : IEventSubscriber, ITransient
{
    /// <summary>
    ///     服务提供者，用于手动创建作用域
    /// </summary>
    private readonly IServiceProvider _serviceProvider;

    /// <summary>
    ///     日志服务
    /// </summary>
    private readonly ILogger<NotificationEventHandler> _logger;

    /// <summary>
    ///     构造函数
    /// </summary>
    /// <param name="serviceProvider">服务提供者</param>
    /// <param name="logger">日志服务</param>
    public NotificationEventHandler(
        IServiceProvider serviceProvider,
        ILogger<NotificationEventHandler> logger)
    {
        _serviceProvider = serviceProvider;
        _logger = logger;
    }


    /// <summary>
    ///     处理现有设备状态变化事件 - 适配原有事件格式
    /// </summary>
    /// <param name="context">事件上下文</param>
    /// <returns></returns>
    [EventSubscribe("device_status_changed")]
    public async Task HandleLegacyDeviceStatusChangedAsync(EventHandlerExecutingContext context)
    {
        // 创建作用域来访问Scoped服务
        using var scope = _serviceProvider.CreateScope();
        var notificationService = scope.ServiceProvider.GetRequiredService<INotificationService>();

        try
        {
            // 从事件上下文中获取强类型的负载数据
            var eventData = context.GetPayload<DeviceStatusChangedEvent>();
            if (eventData == null)
            {
                _logger.LogWarning("设备状态变化事件的负载为空");
                return;
            }

            // 添加调试日志
            _logger.LogDebug("收到设备状态变化事件: DeviceId={DeviceId}, DeviceIdentifier={DeviceIdentifier}, IsOnline={IsOnline}",
                eventData.DeviceId, eventData.DeviceIdentifier, eventData.IsOnline);

            // 直接使用强类型事件数据
            var deviceId = eventData.DeviceId;
            var deviceIdentifier = eventData.DeviceIdentifier;
            var deviceName = eventData.DeviceName;
            var isOnline = eventData.IsOnline;
            var errorMessage = eventData.ErrorMessage;
            var driverName = eventData.DriverName;

            _logger.LogInformation("收到设备状态变化事件（兼容格式）：设备{DeviceIdentifier}({DeviceName}) 状态变为 {IsOnline}",
                deviceIdentifier, deviceName, isOnline ? "在线" : "离线");

            // 转换为标准的 DeviceStatusChangedEvent 格式
            var standardEventData = new DeviceStatusChangedEvent
            {
                DeviceId = deviceId,
                DeviceIdentifier = deviceIdentifier,
                DeviceName = deviceName,
                IsOnline = isOnline,
                Status = isOnline ? DeviceStatusTypeEnum.Good : DeviceStatusTypeEnum.Bad,
                EventTime = DateTime.Now,
                ErrorMessage = errorMessage,
                DriverName = driverName
            };

            // 创建设备状态变化通知
            var notificationDto = new CreateNotificationDto
            {
                Title = GetDeviceStatusTitle(standardEventData),
                Message = GetDeviceStatusMessage(standardEventData),
                Type = GetDeviceStatusNotificationType(standardEventData),
                Source = NotificationSourceEnum.DeviceManagement,
                ActionUrl = $"/device-management/{deviceId}",
                Details = GetDeviceStatusDetails(standardEventData),
                RelatedEntityId = deviceId.ToString(),
                RelatedEntityType = "Device"
            };

            await notificationService.PushNotificationAsync(notificationDto);

            _logger.LogInformation("设备状态变化通知推送成功：{Title}", notificationDto.Title);
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "处理设备状态变化事件时发生错误");
        }
    }

    /// <summary>
    ///     处理转发连接状态变化事件
    /// </summary>
    /// <param name="context">事件上下文</param>
    /// <returns></returns>
    [EventSubscribe("forward_connection_changed")]
    public async Task HandleForwardConnectionChangedAsync(EventHandlerExecutingContext context)
    {
        // 创建作用域来访问Scoped服务
        using var scope = _serviceProvider.CreateScope();
        var notificationService = scope.ServiceProvider.GetRequiredService<INotificationService>();

        try
        {
            // 从事件上下文中获取负载数据
            var eventData = context.GetPayload<ForwardConnectionChangedEvent>();
            if (eventData == null)
            {
                _logger.LogWarning("转发连接状态变化事件的负载为空");
                return;
            }

            _logger.LogInformation("收到转发连接状态变化事件：转发{ForwardId}({ForwardName}) 连接状态变为 {IsConnected}",
                eventData.ForwardId, eventData.ForwardName, eventData.IsConnected ? "已连接" : "已断开");

            var notificationDto = new CreateNotificationDto
            {
                Title = GetForwardStatusTitle(eventData),
                Message = GetForwardStatusMessage(eventData),
                Type = GetForwardStatusNotificationType(eventData),
                Source = NotificationSourceEnum.DataForwarding,
                ActionUrl = $"/data-forwarding/{eventData.ForwardId}",
                Details = GetForwardStatusDetails(eventData),
                RelatedEntityId = eventData.ForwardId.ToString(),
                RelatedEntityType = "ForwardConfig"
            };

            await notificationService.PushNotificationAsync(notificationDto);

            _logger.LogInformation("转发连接变化通知推送成功：{Title}", notificationDto.Title);
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "处理转发连接变化事件时发生错误");
        }
    }

    #region 设备状态事件处理辅助方法

    /// <summary>
    ///     获取设备状态通知标题
    /// </summary>
    /// <param name="eventData">事件数据</param>
    /// <returns>通知标题</returns>
    private static string GetDeviceStatusTitle(DeviceStatusChangedEvent eventData)
    {
        if (eventData.IsOnline) return "设备已上线";

        return string.IsNullOrEmpty(eventData.ErrorMessage) ? "设备已离线" : "设备连接异常";
    }

    /// <summary>
    ///     获取设备状态通知消息
    /// </summary>
    /// <param name="eventData">事件数据</param>
    /// <returns>通知消息</returns>
    private static string GetDeviceStatusMessage(DeviceStatusChangedEvent eventData)
    {
        var deviceDisplayName = !string.IsNullOrEmpty(eventData.DeviceName)
            ? $"{eventData.DeviceName}({eventData.DeviceIdentifier})"
            : eventData.DeviceIdentifier;

        if (eventData.IsOnline) return $"设备 {deviceDisplayName} 已成功上线";

        return string.IsNullOrEmpty(eventData.ErrorMessage)
            ? $"设备 {deviceDisplayName} 已离线"
            : $"设备 {deviceDisplayName} 连接异常";
    }

    /// <summary>
    ///     获取设备状态通知类型
    /// </summary>
    /// <param name="eventData">事件数据</param>
    /// <returns>通知类型</returns>
    private static NotificationTypeEnum GetDeviceStatusNotificationType(DeviceStatusChangedEvent eventData)
    {
        if (eventData.IsOnline) return NotificationTypeEnum.Success;

        return string.IsNullOrEmpty(eventData.ErrorMessage)
            ? NotificationTypeEnum.Warning
            : NotificationTypeEnum.Critical;
    }

    /// <summary>
    ///     获取设备状态详细信息
    /// </summary>
    /// <param name="eventData">事件数据</param>
    /// <returns>详细信息</returns>
    private static string GetDeviceStatusDetails(DeviceStatusChangedEvent eventData)
    {
        var details = new List<string>
        {
            $"设备标识: {eventData.DeviceIdentifier}",
            $"事件时间: {eventData.EventTime:yyyy-MM-dd HH:mm:ss}",
            $"设备状态: {(eventData.Status == DeviceStatusTypeEnum.Good ? "正常" : "异常")}"
        };

        if (!string.IsNullOrEmpty(eventData.Location)) details.Add($"设备位置: {eventData.Location}");

        if (!string.IsNullOrEmpty(eventData.DriverName)) details.Add($"驱动程序: {eventData.DriverName}");

        if (!string.IsNullOrEmpty(eventData.ErrorMessage)) details.Add($"错误信息: {eventData.ErrorMessage}");

        return string.Join("; ", details);
    }

    #endregion

    #region 转发连接事件处理辅助方法

    /// <summary>
    ///     获取转发状态通知标题
    /// </summary>
    /// <param name="eventData">事件数据</param>
    /// <returns>通知标题</returns>
    private static string GetForwardStatusTitle(ForwardConnectionChangedEvent eventData)
    {
        if (eventData.IsConnected) return "数据转发连接成功";

        return string.IsNullOrEmpty(eventData.ErrorMessage) ? "数据转发连接断开" : "数据转发连接失败";
    }

    /// <summary>
    ///     获取转发状态通知消息
    /// </summary>
    /// <param name="eventData">事件数据</param>
    /// <returns>通知消息</returns>
    private static string GetForwardStatusMessage(ForwardConnectionChangedEvent eventData)
    {
        var forwardTypeName = GetForwardTypeDisplayName(eventData.ForwardType);

        if (eventData.IsConnected) return $"{forwardTypeName}转发规则 {eventData.ForwardName} 连接成功";

        return string.IsNullOrEmpty(eventData.ErrorMessage)
            ? $"{forwardTypeName}转发规则 {eventData.ForwardName} 连接已断开"
            : $"{forwardTypeName}转发规则 {eventData.ForwardName} 连接失败";
    }

    /// <summary>
    ///     获取转发状态通知类型
    /// </summary>
    /// <param name="eventData">事件数据</param>
    /// <returns>通知类型</returns>
    private static NotificationTypeEnum GetForwardStatusNotificationType(ForwardConnectionChangedEvent eventData)
    {
        if (eventData.IsConnected) return NotificationTypeEnum.Success;

        return string.IsNullOrEmpty(eventData.ErrorMessage)
            ? NotificationTypeEnum.Warning
            : NotificationTypeEnum.Critical;
    }

    /// <summary>
    ///     获取转发状态详细信息
    /// </summary>
    /// <param name="eventData">事件数据</param>
    /// <returns>详细信息</returns>
    private static string GetForwardStatusDetails(ForwardConnectionChangedEvent eventData)
    {
        var details = new List<string>
        {
            $"转发规则: {eventData.ForwardName}",
            $"转发类型: {GetForwardTypeDisplayName(eventData.ForwardType)}",
            $"事件时间: {eventData.EventTime:yyyy-MM-dd HH:mm:ss}",
            $"连接状态: {(eventData.IsConnected ? "已连接" : "已断开")}"
        };

        if (!string.IsNullOrEmpty(eventData.TargetUrl)) details.Add($"目标地址: {eventData.TargetUrl}");

        if (eventData.RetryCount.HasValue) details.Add($"重试次数: {eventData.RetryCount.Value}");

        if (!string.IsNullOrEmpty(eventData.ErrorMessage)) details.Add($"错误信息: {eventData.ErrorMessage}");

        return string.Join("; ", details);
    }

    /// <summary>
    ///     获取转发类型显示名称
    /// </summary>
    /// <param name="forwardType">转发类型</param>
    /// <returns>显示名称</returns>
    private static string GetForwardTypeDisplayName(ForwardTypeEnum forwardType)
    {
        // 注意：这里需要根据实际的转发类型枚举进行调整
        return forwardType.ToString();
    }

    #endregion
}