using System.Diagnostics;
using EdgeGateway.AI.Entity;
using EdgeGateway.AI.Services;
using EdgeGateway.Core.Queue;
using Furion;
using Microsoft.Extensions.DependencyInjection;
using Microsoft.Extensions.Logging;

namespace EdgeGateway.AI;

/// <summary>
///     自定义启动类
/// </summary>
public class Startup : AppStartup
{
    /// <summary>
    /// 配置服务
    /// </summary>
    /// <param name="services">服务集合</param>
    public void ConfigureServices(IServiceCollection services)
    {
        var sw = Stopwatch.StartNew();

        // 注册 DeepSeek 配置选项
        services.AddConfigurableOptions<DeepSeekOptions>();

        // 注册 DeepSeek 配置持久化服务
        services.AddSingleton<DeepSeekConfigPersistenceService>();

        // 注册 DeepSeek 服务
        services.AddSingleton<IDeepSeekService, DeepSeekService>();
        services.AddSingleton<DeepSeekDocumentManager>();

        // 注册 DocumentGenerator 服务
        services.AddSingleton<DocumentGenerator>();

        sw.Stop();
        var logger = services.BuildServiceProvider().GetRequiredService<ILogger<Startup>>();
        logger.LogInformation("╔════════════════════════════════════════════════════════════════");
        logger.LogInformation("║ AI 服务注入完成，耗时: {ElapsedMilliseconds}ms", sw.ElapsedMilliseconds);
        logger.LogInformation("╚════════════════════════════════════════════════════════════════");
    }
}