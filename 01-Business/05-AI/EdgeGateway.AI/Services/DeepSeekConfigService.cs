using Microsoft.AspNetCore.Mvc;
using Furion.DynamicApiController;
using Furion.SpecificationDocument;
using Furion.DependencyInjection;
using Microsoft.Extensions.Logging;
using Microsoft.Extensions.Options;

namespace EdgeGateway.AI.Services
{
  /// <summary>
  /// DeepSeek 配置管理服务
  /// </summary>
  [ApiDescriptionSettings("AI 中心")]
  [Route("/api/ai/config/")]
  public class DeepSeekConfigService : ITransient, IDynamicApiController
  {
    /// <summary>
    /// 配置选项
    /// </summary>
    private readonly IOptionsMonitor<DeepSeekOptions> _optionsMonitor;

    /// <summary>
    /// 配置持久化服务
    /// </summary>
    private readonly DeepSeekConfigPersistenceService _persistenceService;

    /// <summary>
    /// 日志记录器
    /// </summary>
    private readonly ILogger<DeepSeekConfigService> _logger;

    /// <summary>
    /// 构造函数
    /// </summary>
    /// <param name="optionsMonitor">DeepSeek 配置选项监视器</param>
    /// <param name="persistenceService">配置持久化服务</param>
    /// <param name="logger">日志记录器</param>
    public DeepSeekConfigService(
        IOptionsMonitor<DeepSeekOptions> optionsMonitor,
        DeepSeekConfigPersistenceService persistenceService,
        ILogger<DeepSeekConfigService> logger)
    {
      _optionsMonitor = optionsMonitor;
      _persistenceService = persistenceService;
      _logger = logger;
    }

    /// <summary>
    /// 获取 DeepSeek 配置信息
    /// </summary>
    /// <returns>配置信息</returns>
    [HttpGet("info")]
    [OperationId(nameof(GetConfig))]
    public async Task<object> GetConfig()
    {
      try
      {
        // 从持久化存储加载最新配置
        var config = await _persistenceService.LoadConfigAsync();

        return new
        {
          apiKey = config.ApiKey,
          baseUrl = config.BaseUrl,
          model = config.Model,
          temperature = config.Temperature,
          maxTokens = config.MaxTokens,
          topP = config.TopP,
          frequencyPenalty = config.FrequencyPenalty,
          presencePenalty = config.PresencePenalty,
          streamResponse = config.StreamResponse,
          maxContextMessages = config.MaxContextMessages,
          contextExpirationMinutes = config.ContextExpirationMinutes,
          defaultSystemPrompt = config.DefaultSystemPrompt
        };
      }
      catch (Exception ex)
      {
        _logger.LogError(ex, "获取配置失败");
        throw;
      }
    }

    /// <summary>
    /// 更新 DeepSeek 配置信息
    /// </summary>
    /// <param name="config">配置信息</param>
    /// <returns>更新结果</returns>
    [HttpPut("update")]
    [OperationId(nameof(UpdateConfig))]
    public async Task<object> UpdateConfig([FromBody] DeepSeekOptions config)
    {
      try
      {
        // 保存配置到持久化存储
        var success = await _persistenceService.SaveConfigAsync(config);

        if (success)
        {
          _logger.LogInformation("DeepSeek 配置更新成功");
          return new { success = true, message = "配置更新成功" };
        }
        else
        {
          _logger.LogError("DeepSeek 配置保存失败");
          return new { success = false, message = "配置保存失败" };
        }
      }
      catch (Exception ex)
      {
        _logger.LogError(ex, "更新配置失败");
        return new { success = false, message = $"配置更新失败: {ex.Message}" };
      }
    }

    /// <summary>
    /// 重置 DeepSeek 配置为默认值
    /// </summary>
    /// <returns>重置结果</returns>
    [HttpPost("reset")]
    [OperationId(nameof(ResetConfig))]
    public async Task<object> ResetConfig()
    {
      try
      {
        var success = await _persistenceService.ResetToDefaultAsync();

        if (success)
        {
          _logger.LogInformation("DeepSeek 配置已重置为默认值");
          return new { success = true, message = "配置已重置为默认值" };
        }
        else
        {
          _logger.LogError("DeepSeek 配置重置失败");
          return new { success = false, message = "配置重置失败" };
        }
      }
      catch (Exception ex)
      {
        _logger.LogError(ex, "重置配置失败");
        return new { success = false, message = $"配置重置失败: {ex.Message}" };
      }
    }

    /// <summary>
    /// 验证 DeepSeek 配置
    /// </summary>
    /// <param name="config">配置信息</param>
    /// <returns>验证结果</returns>
    [HttpPost("validate")]
    [OperationId(nameof(ValidateConfig))]
    public async Task<object> ValidateConfig([FromBody] DeepSeekOptions config)
    {
      try
      {
        var validationErrors = new List<string>();

        // 验证 API Key
        if (string.IsNullOrWhiteSpace(config.ApiKey))
        {
          validationErrors.Add("API Key 不能为空");
        }

        // 验证 Base URL
        if (string.IsNullOrWhiteSpace(config.BaseUrl))
        {
          validationErrors.Add("Base URL 不能为空");
        }
        else if (!Uri.TryCreate(config.BaseUrl, UriKind.Absolute, out _))
        {
          validationErrors.Add("Base URL 格式无效");
        }

        // 验证模型名称
        var supportedModels = new[] { "deepseek-chat", "deepseek-reasoner" };
        if (!supportedModels.Contains(config.Model))
        {
          validationErrors.Add($"不支持的模型: {config.Model}");
        }

        // 验证参数范围
        if (config.Temperature < 0 || config.Temperature > 2)
        {
          validationErrors.Add("Temperature 必须在 0-2 范围内");
        }

        if (config.MaxTokens < 1 || config.MaxTokens > 8192)
        {
          validationErrors.Add("MaxTokens 必须在 1-8192 范围内");
        }

        if (config.TopP < 0 || config.TopP > 1)
        {
          validationErrors.Add("TopP 必须在 0-1 范围内");
        }

        if (config.FrequencyPenalty < -2 || config.FrequencyPenalty > 2)
        {
          validationErrors.Add("FrequencyPenalty 必须在 -2 到 2 范围内");
        }

        if (config.PresencePenalty < -2 || config.PresencePenalty > 2)
        {
          validationErrors.Add("PresencePenalty 必须在 -2 到 2 范围内");
        }

        var isValid = validationErrors.Count == 0;
        return new
        {
          success = isValid,
          message = isValid ? "配置验证通过" : "配置验证失败",
          errors = validationErrors
        };
      }
      catch (Exception ex)
      {
        _logger.LogError(ex, "验证配置失败");
        return new { success = false, message = $"配置验证失败: {ex.Message}" };
      }
    }
  }
}