using System.Text.Json;
using Microsoft.Extensions.Logging;
using Furion.DependencyInjection;

namespace EdgeGateway.AI.Services
{
  /// <summary>
  /// DeepSeek 配置持久化服务
  /// 负责配置的读取、保存和管理
  /// </summary>
  public class DeepSeekConfigPersistenceService : ISingleton
  {
    private readonly ILogger<DeepSeekConfigPersistenceService> _logger;
    private readonly string _configFilePath;
    private readonly SemaphoreSlim _fileLock = new(1, 1);

    /// <summary>
    /// 构造函数
    /// </summary>
    /// <param name="logger">日志记录器</param>
    public DeepSeekConfigPersistenceService(ILogger<DeepSeekConfigPersistenceService> logger)
    {
      _logger = logger;
      _configFilePath = Path.Combine(AppContext.BaseDirectory, "Configuration", "DeepSeek.json");
      
      // 确保配置目录存在
      var configDir = Path.GetDirectoryName(_configFilePath);
      if (!Directory.Exists(configDir))
      {
        Directory.CreateDirectory(configDir!);
      }

      // 如果配置文件不存在，创建默认配置
      if (!File.Exists(_configFilePath))
      {
        CreateDefaultConfigFile();
      }
    }

    /// <summary>
    /// 加载配置
    /// </summary>
    /// <returns>DeepSeek 配置选项</returns>
    public async Task<DeepSeekOptions> LoadConfigAsync()
    {
      await _fileLock.WaitAsync();
      try
      {
        if (!File.Exists(_configFilePath))
        {
          _logger.LogWarning("配置文件不存在，返回默认配置");
          return new DeepSeekOptions();
        }

        var jsonContent = await File.ReadAllTextAsync(_configFilePath);
        var configWrapper = JsonSerializer.Deserialize<DeepSeekConfigWrapper>(jsonContent, new JsonSerializerOptions
        {
          PropertyNameCaseInsensitive = true
        });

        return configWrapper?.DeepSeek ?? new DeepSeekOptions();
      }
      catch (Exception ex)
      {
        _logger.LogError(ex, "加载配置文件失败，返回默认配置");
        return new DeepSeekOptions();
      }
      finally
      {
        _fileLock.Release();
      }
    }

    /// <summary>
    /// 保存配置
    /// </summary>
    /// <param name="options">配置选项</param>
    /// <returns>是否保存成功</returns>
    public async Task<bool> SaveConfigAsync(DeepSeekOptions options)
    {
      await _fileLock.WaitAsync();
      try
      {
        // 验证配置
        if (!ValidateConfig(options))
        {
          _logger.LogError("配置验证失败，无法保存");
          return false;
        }

        // 创建备份
        await CreateBackupAsync();

        // 包装配置
        var configWrapper = new DeepSeekConfigWrapper
        {
          DeepSeek = options
        };

        // 序列化并保存
        var jsonContent = JsonSerializer.Serialize(configWrapper, new JsonSerializerOptions
        {
          WriteIndented = true,
          PropertyNamingPolicy = JsonNamingPolicy.CamelCase
        });

        await File.WriteAllTextAsync(_configFilePath, jsonContent);
        _logger.LogInformation("DeepSeek 配置已保存到文件");
        return true;
      }
      catch (Exception ex)
      {
        _logger.LogError(ex, "保存配置文件失败");
        return false;
      }
      finally
      {
        _fileLock.Release();
      }
    }

    /// <summary>
    /// 重置为默认配置
    /// </summary>
    /// <returns>是否重置成功</returns>
    public async Task<bool> ResetToDefaultAsync()
    {
      try
      {
        var defaultOptions = new DeepSeekOptions();
        return await SaveConfigAsync(defaultOptions);
      }
      catch (Exception ex)
      {
        _logger.LogError(ex, "重置配置失败");
        return false;
      }
    }

    /// <summary>
    /// 创建默认配置文件
    /// </summary>
    private void CreateDefaultConfigFile()
    {
      try
      {
        var defaultOptions = new DeepSeekOptions();
        var configWrapper = new DeepSeekConfigWrapper
        {
          DeepSeek = defaultOptions
        };

        var jsonContent = JsonSerializer.Serialize(configWrapper, new JsonSerializerOptions
        {
          WriteIndented = true,
          PropertyNamingPolicy = JsonNamingPolicy.CamelCase
        });

        File.WriteAllText(_configFilePath, jsonContent);
        _logger.LogInformation("已创建默认 DeepSeek 配置文件");
      }
      catch (Exception ex)
      {
        _logger.LogError(ex, "创建默认配置文件失败");
      }
    }

    /// <summary>
    /// 创建配置备份
    /// </summary>
    private async Task CreateBackupAsync()
    {
      try
      {
        if (File.Exists(_configFilePath))
        {
          var backupPath = $"{_configFilePath}.backup.{DateTime.Now:yyyyMMddHHmmss}";
          await File.WriteAllTextAsync(backupPath, await File.ReadAllTextAsync(_configFilePath));
          
          // 只保留最近5个备份文件
          var backupDir = Path.GetDirectoryName(_configFilePath);
          var backupFiles = Directory.GetFiles(backupDir!, "DeepSeek.json.backup.*")
            .OrderByDescending(f => f)
            .Skip(5);
          
          foreach (var oldBackup in backupFiles)
          {
            File.Delete(oldBackup);
          }
        }
      }
      catch (Exception ex)
      {
        _logger.LogWarning(ex, "创建配置备份失败");
      }
    }

    /// <summary>
    /// 验证配置
    /// </summary>
    /// <param name="options">配置选项</param>
    /// <returns>是否有效</returns>
    private bool ValidateConfig(DeepSeekOptions options)
    {
      if (options == null)
      {
        _logger.LogError("配置对象为空");
        return false;
      }

      if (string.IsNullOrWhiteSpace(options.ApiKey))
      {
        _logger.LogError("API Key 不能为空");
        return false;
      }

      if (string.IsNullOrWhiteSpace(options.BaseUrl))
      {
        _logger.LogError("Base URL 不能为空");
        return false;
      }

      if (options.Temperature < 0 || options.Temperature > 2)
      {
        _logger.LogError("Temperature 必须在 0-2 范围内");
        return false;
      }

      if (options.MaxTokens < 1 || options.MaxTokens > 8192)
      {
        _logger.LogError("MaxTokens 必须在 1-8192 范围内");
        return false;
      }

      if (options.TopP < 0 || options.TopP > 1)
      {
        _logger.LogError("TopP 必须在 0-1 范围内");
        return false;
      }

      if (options.FrequencyPenalty < -2 || options.FrequencyPenalty > 2)
      {
        _logger.LogError("FrequencyPenalty 必须在 -2 到 2 范围内");
        return false;
      }

      if (options.PresencePenalty < -2 || options.PresencePenalty > 2)
      {
        _logger.LogError("PresencePenalty 必须在 -2 到 2 范围内");
        return false;
      }

      return true;
    }
  }

  /// <summary>
  /// 配置文件包装器
  /// </summary>
  internal class DeepSeekConfigWrapper
  {
    public DeepSeekOptions DeepSeek { get; set; } = new();
  }
}
