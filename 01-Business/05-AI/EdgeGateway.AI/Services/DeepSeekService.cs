using System.Threading.Channels;
using Microsoft.AspNetCore.Mvc;
using Furion.DynamicApiController;
using Furion.SpecificationDocument;
using Furion.DependencyInjection;
using EdgeGateway.AI.Entity;

namespace EdgeGateway.AI.Services
{
  /// <summary>
  /// DeepSeek AI 服务
  /// </summary>
  [ApiDescriptionSettings("AI 中心")]
  [Route("/api/ai/")]
  public class DeepSeekService : ITransient, IDynamicApiController
  {
    /// <summary>
    /// DeepSeek 服务
    /// </summary>
    private readonly IDeepSeekService _deepSeekService;

    /// <summary>
    /// 构造函数
    /// </summary>
    /// <param name="deepSeekService">DeepSeek 服务</param>
    public DeepSeekService(IDeepSeekService deepSeekService)
    {
      _deepSeekService = deepSeekService;
    }

    /// <summary>
    /// 点位脚本Ai生成
    /// </summary>
    /// <param name="request">聊天请求</param>
    /// <returns>聊天响应</returns>
    [HttpPost("chat")]
    [OperationId(nameof(Chat))]
    public async Task<object> Chat([FromBody] ChatRequest request)
    {
      try
      {
        var response = await _deepSeekService.ChatAsync(
               request.UserId,
               request.Message);

        return new
        {
          success = true,
          data = response
          //data = "```javascript\\n// 初始化累计产量变量\\nvar totalProduction = store.Get('totalProduction') || 0;\\n\\n// 获取当前产量值\\nvar currentProduction = device.Get('deviceId', 'production');\\n\\n// 处理读取失败的情况\\nif (currentProduction === null || currentProduction === undefined) {\\n    currentProduction = 0;\\n}\\n\\n// 判断是否发生产量复位\\nvar lastProduction = store.Get('lastProduction') || 0;\\nif (currentProduction < lastProduction) {\\n    // 发生复位，将上次产量累加到总产量\\n    totalProduction += lastProduction;\\n}\\n\\n// 更新累计产量\\ntotalProduction += currentProduction;\\n\\n// 保存当前状态\\nstore.Set('totalProduction', totalProduction);\\nstore.Set('lastProduction', currentProduction);\\n\\n// 返回累计产量\\nreturn totalProduction;\\n```\\n\\n### 代码说明：\\n1. **变量初始化**：\\n   - `totalProduction`：从存储中获取累计产量，如果不存在则初始化为0\\n   - `currentProduction`：从设备获取当前产量值\\n\\n2. **错误处理**：\\n   - 如果读取当前产量失败（null或undefined），将其设为0\\n\\n3. **复位检测**：\\n   - 比较当前产量和上次记录产量，如果当前产量小于上次产量，说明发生了复位\\n   - 发生复位时，将上次产量累加到总产量中\\n\\n4. **累计计算**：\\n   - 将当前产量累加到总产量中\\n\\n5. **状态保存**：\\n   - 将更新后的累计产量和当前产量保存到存储中\\n\\n6. **返回值**：\\n   - 返回最新的累计产量值\\n\\n### 注意事项：\\n- 该脚本假设设备ID为'deviceId'，产量属性为'production'\\n- 使用store进行持久化存储，确保数据在脚本重启后不会丢失\\n- 考虑了产量复位的情况，确保累计产量的准确性\\n- 处理了读取失败的情况，增强了脚本的健壮性\\n\\n你可以根据实际设备ID和属性名称调整脚本中的参数。这个脚本可以定期执行（如每分钟），以保持累计产量的实时更新。"
        };
      }
      catch (Exception ex)
      {
        return new
        {
          success = false,
          message = ex.Message
        };
      }
    }
    
    /// <summary>
    /// 发送流式聊天消息
    /// </summary>
    /// <param name="request">聊天请求</param>
    /// <returns>流式响应</returns>
    [HttpPost("chat/stream")]
    [OperationId(nameof(ChatStream))]
    public async IAsyncEnumerable<object> ChatStream([FromBody] ChatRequest request)
    {
      var channel = Channel.CreateUnbounded<object>();
      var writer = channel.Writer;
      var reader = channel.Reader;

      _ = Task.Run(async () =>
      {
        try
        {
          await _deepSeekService.StreamChatAsync(
              request.UserId,
              request.Message,
              async message => await writer.WriteAsync(new { success = true, data = message }));
          await writer.WriteAsync(new { success = true, done = true });
        }
        catch (Exception ex)
        {
          await writer.WriteAsync(new { success = false, message = ex.Message });
        }
        finally
        {
          writer.Complete();
        }
      });

      await foreach (var item in reader.ReadAllAsync())
      {
        yield return item;
      }
    }

    /// <summary>
    /// 清除用户会话
    /// </summary>
    /// <param name="input">清除会话请求实体</param>
    /// <returns>操作结果</returns>
    [HttpPost("chat/clear")]
    [OperationId(nameof(ClearSession))]
    public async Task<object> ClearSession([FromBody] ClearSessionInput input)
    {
      try
      {
        await _deepSeekService.ClearSessionAsync(input.UserId);
        return new { success = true, message = "会话已清除" };
      }
      catch (Exception ex)
      {
        return new { success = false, message = ex.Message };
      }
    }

    /// <summary>
    /// PayLoad 数据转换代码生成
    /// </summary>
    /// <param name="request">数据转换请求</param>
    /// <returns>生成的转换代码</returns>
    [HttpPost("payload-transform")]
    [OperationId(nameof(PayLoadTransform))]
    public async Task<object> PayLoadTransform([FromBody] PayLoadTransformRequest request)
    {
      try
      {
        // 构建专用的 PayLoad 数据转换提示词
        var systemPrompt = BuildPayLoadTransformPrompt();

        // 直接使用用户的描述作为消息
        var userMessage = request.Description;

        var response = await _deepSeekService.ChatAsync(
               $"payload_transform_{request.UserId}",
               userMessage,
               systemPrompt);

        return new
        {
          success = true,
          data = response
        };
      }
      catch (Exception ex)
      {
        return new
        {
          success = false,
          message = ex.Message
        };
      }
    }

    /// <summary>
    /// 构建 PayLoad 数据转换专用提示词
    /// </summary>
    /// <returns>系统提示词</returns>
    private string BuildPayLoadTransformPrompt()
    {
      return @"
您是一个专业的工业数据转换代码生成专家。请根据用户的自然语言描述，生成纯 JavaScript 代码来转换 PayLoad 数据。

**严格要求：**
1. 只返回可直接执行的 JavaScript 代码，不包含任何解释文字或 markdown 格式
2. 代码必须兼容 Jint JavaScript 引擎（.NET 环境）
3. 使用标准 JavaScript ES5/ES6 语法，避免 Node.js 特有功能
4. 必须包含详细的中文注释说明每个转换步骤
5. 代码应该是完整的函数，接受 data 和 config 参数并返回转换后的数据
6. 确保代码具有良好的错误处理机制
7. 返回的数据结构必须符合 PayLoad 格式

**可用变量：**
- data: 输入的 PayLoad 数据对象
- config: 转换配置信息对象（可选，可能为空）

**输入数据结构 PayLoad (data)：**
- data.id: 设备标识（字符串）
- data.name: 设备名称（字符串）
- data.ts: 时间戳（长整型，毫秒）
- data.status: 采集状态（枚举：1=全部成功，2=部分成功，3=全部失败）
- data.statusSummary: 状态统计信息对象
  - totalCount: 总标签数量
  - successCount: 成功读取的标签数量
  - failedCount: 失败读取的标签数量
  - errorCount: 错误读取的标签数量
  - successRate: 成功率（百分比）
- data.labels: 标签数据字典对象，key为标签名，value为标签对象：
  - name: 标签名称（字符串）
  - value: 标签值（任意类型）
  - time: 时间戳（长整型，毫秒）
  - transitionType: 数据类型（字符串）
  - status: 读取状态（字符串：Good/Error/Bad）
  - errorMessage: 错误信息（字符串，可选）

**代码模板：**
```javascript
function transform(data, config) {
    // 创建转换后的数据结构
    var transformed = {
        id: data.id,
        name: data.name,
        ts: data.ts,
        status: data.status,
        statusSummary: data.statusSummary,
        labels: {}
    };

    // 在这里实现具体的转换逻辑
    // 遍历处理 data.labels 中的数据
    // 可以使用 config 中的配置参数

    return transformed;
}

// 执行转换
transform(data, config);
```

**注意事项：**
- 确保代码可以直接在 Jint 引擎中执行
- 处理可能的空值和异常情况
- 保持原有数据结构的完整性
- 添加必要的数据验证和错误处理
- 使用中文注释详细说明转换逻辑
- 如果需要配置参数，可以通过 config 对象获取

请严格按照以上要求生成代码，确保代码质量和可执行性。";
    }



  }
}