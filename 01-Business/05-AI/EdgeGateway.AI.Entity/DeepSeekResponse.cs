using System.Text.Json.Serialization;

namespace EdgeGateway.AI.Entity
{
  /// <summary>
  /// DeepSeek 响应实体
  /// </summary>
  public class DeepSeekResponse
  {
    /// <summary>
    /// 选择列表
    /// </summary>
    [JsonPropertyName("choices")]
    public List<Choice> Choices { get; set; }

    /// <summary>
    /// 创建时间
    /// </summary>
    [JsonPropertyName("created")]
    public long Created { get; set; }

    /// <summary>
    /// 模型ID
    /// </summary>
    [JsonPropertyName("model")]
    public string Model { get; set; }

    /// <summary>
    /// 使用情况
    /// </summary>
    [JsonPropertyName("usage")]
    public Usage Usage { get; set; }
  }

  /// <summary>
  /// 选择项
  /// </summary>
  public class Choice
  {
    /// <summary>
    /// 完成原因
    /// </summary>
    [JsonPropertyName("finish_reason")]
    public string FinishReason { get; set; }

    /// <summary>
    /// 索引
    /// </summary>
    [JsonPropertyName("index")]
    public int Index { get; set; }

    /// <summary>
    /// 消息
    /// </summary>
    [JsonPropertyName("message")]
    public Message Message { get; set; }

    /// <summary>
    /// Delta
    /// </summary>
    [JsonPropertyName("delta")]
    public Message Delta { get; set; }
  }

  /// <summary>
  /// 消息
  /// </summary>
  public class Message
  {
    /// <summary>
    /// 内容
    /// </summary>
    [JsonPropertyName("content")]
    public string Content { get; set; }

    /// <summary>
    /// 角色
    /// </summary>
    [JsonPropertyName("role")]
    public string Role { get; set; }
  }

  /// <summary>
  /// 使用情况
  /// </summary>
  public class Usage
  {
    /// <summary>
    /// 完成令牌数
    /// </summary>
    [JsonPropertyName("completion_tokens")]
    public int CompletionTokens { get; set; }

    /// <summary>
    /// 提示令牌数
    /// </summary>
    [JsonPropertyName("prompt_tokens")]
    public int PromptTokens { get; set; }

    /// <summary>
    /// 总令牌数
    /// </summary>
    [JsonPropertyName("total_tokens")]
    public int TotalTokens { get; set; }
  }
}