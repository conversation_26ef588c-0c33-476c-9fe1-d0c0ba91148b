namespace EdgeGateway.AI.Entity
{
  /// <summary>
  /// DeepSeek 会话实体
  /// </summary>
  public class DeepSeekSession
  {
    /// <summary>
    /// 会话ID
    /// </summary>
    public string Id { get; set; } = Guid.NewGuid().ToString();

    /// <summary>
    /// 系统提示词
    /// </summary>
    public string SystemPrompt { get; set; }

    /// <summary>
    /// 上下文
    /// </summary>
    public DeepSeekContext Context { get; set; }

    /// <summary>
    /// 创建时间
    /// </summary>
    public DateTime CreatedAt { get; set; } = DateTime.Now;

    /// <summary>
    /// 最后活动时间
    /// </summary>
    public DateTime LastActivityAt { get; set; } = DateTime.Now;

    /// <summary>
    /// 用户标识
    /// </summary>
    public string UserId { get; set; }

    /// <summary>
    /// 文档列表
    /// </summary>
    public List<DeepSeekDocument> Documents { get; set; } = new();

    /// <summary>
    /// 更新最后活动时间
    /// </summary>
    public void UpdateActivity()
    {
      LastActivityAt = DateTime.Now;
    }

    /// <summary>
    /// 添加文档
    /// </summary>
    /// <param name="document">文档</param>
    public void AddDocument(DeepSeekDocument document)
    {
      Documents.Add(document);
      UpdateActivity();
    }

    /// <summary>
    /// 清除文档
    /// </summary>
    public void ClearDocuments()
    {
      Documents.Clear();
      UpdateActivity();
    }

    /// <summary>
    /// 获取所有文档内容
    /// </summary>
    /// <returns>文档内容</returns>
    public string GetAllDocumentsContent()
    {
      return string.Join("\n\n", Documents.Select(d => d.Content));
    }
  }
}