using System.Text.Json.Serialization;

namespace EdgeGateway.AI.Entity
{
  /// <summary>
  /// DeepSeek 消息实体
  /// </summary>
  public class DeepSeekMessage
  {
    /// <summary>
    /// 角色
    /// </summary>
    [JsonPropertyName("role")]
    public string Role { get; set; }

    /// <summary>
    /// 内容
    /// </summary>
    [JsonPropertyName("content")]
    public string Content { get; set; }

    /// <summary>
    /// 创建时间
    /// </summary>
    public DateTime CreatedAt { get; set; } = DateTime.Now;
  }
}