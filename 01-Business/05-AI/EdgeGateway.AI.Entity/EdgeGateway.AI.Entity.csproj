<Project Sdk="Microsoft.NET.Sdk">

  <PropertyGroup>
    <TargetFramework>net9.0</TargetFramework>
    <ImplicitUsings>enable</ImplicitUsings>
    <Nullable>enable</Nullable>
  </PropertyGroup>

  <PropertyGroup Condition=" '$(Configuration)' == 'Debug' ">
    <DocumentationFile>bin\Debug\EdgeGateway.AI.Entity.xml</DocumentationFile>
  </PropertyGroup>

  <ItemGroup>
    <ProjectReference Include="..\..\00-System\EdgeGateway.Base.Entity\EdgeGateway.Base.Entity.csproj" />
    <ProjectReference Include="..\..\02-Engine\EdgeGateway.Engine\EdgeGateway.Engine.csproj" />
  </ItemGroup>

</Project>
