namespace EdgeGateway.AI.Entity
{
  /// <summary>
  /// DeepSeek 上下文实体
  /// </summary>
  public class DeepSeekContext
  {
    /// <summary>
    /// 上下文ID
    /// </summary>
    public string Id { get; set; } = Guid.NewGuid().ToString();

    /// <summary>
    /// 消息历史
    /// </summary>
    public List<DeepSeekMessage> Messages { get; set; } = new();

    /// <summary>
    /// 创建时间
    /// </summary>
    public DateTime CreatedAt { get; set; } = DateTime.Now;

    /// <summary>
    /// 最后更新时间
    /// </summary>
    public DateTime LastUpdatedAt { get; set; } = DateTime.Now;

    /// <summary>
    /// 添加消息
    /// </summary>
    /// <param name="role">角色</param>
    /// <param name="content">内容</param>
    public void AddMessage(string role, string content)
    {
      Messages.Add(new DeepSeekMessage
      {
        Role = role,
        Content = content
      });
      LastUpdatedAt = DateTime.Now;
    }

    /// <summary>
    /// 清除历史消息
    /// </summary>
    public void Clear()
    {
      Messages.Clear();
      LastUpdatedAt = DateTime.Now;
    }

    /// <summary>
    /// 获取最近的N条消息
    /// </summary>
    /// <param name="count">消息数量</param>
    /// <returns>消息列表</returns>
    public List<DeepSeekMessage> GetRecentMessages(int count)
    {
      return Messages.Skip(Math.Max(0, Messages.Count - count)).ToList();
    }
  }
}