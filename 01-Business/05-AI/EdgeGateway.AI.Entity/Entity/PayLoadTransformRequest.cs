using System.ComponentModel.DataAnnotations;

namespace EdgeGateway.AI.Entity
{
    /// <summary>
    /// PayLoad 数据转换请求实体
    /// </summary>
    public class PayLoadTransformRequest
    {
        /// <summary>
        /// 用户ID
        /// </summary>
        [Required(ErrorMessage = "用户ID不能为空")]
        public string UserId { get; set; }

        /// <summary>
        /// 转换需求描述（自然语言描述完整的转换需求）
        /// </summary>
        [Required(ErrorMessage = "转换需求描述不能为空")]
        [StringLength(3000, ErrorMessage = "转换需求描述不能超过3000个字符")]
        public string Description { get; set; }
    }
}
