namespace EdgeGateway.AI.Entity
{
  /// <summary>
  /// 聊天请求实体
  /// </summary>
  public class ChatRequest
  {
    /// <summary>
    /// 用户ID
    /// </summary>
    public string UserId { get; set; }

    /// <summary>
    /// 消息内容
    /// </summary>
    public string Message { get; set; }
  }
}

/// <summary>
/// 清除会话请求实体
/// </summary>
public class ClearSessionInput
{
  /// <summary>
  /// 用户ID
  /// </summary>
  public string UserId { get; set; }
}