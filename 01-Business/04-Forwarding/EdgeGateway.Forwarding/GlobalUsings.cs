global using System.Collections.Generic;
global using System;
global using System.ComponentModel.DataAnnotations;
global using SqlSugar;
global using EdgeGateway.Core.Util;
global using System.Collections.Concurrent;
global using EdgeGateway.Core.Queue;
global using EdgeGateway.Device.Services.Forward;
global using Furion.EventBus;
global using Microsoft.Extensions.Hosting;
global using Microsoft.Extensions.Logging;
global using SqlSugar;
global using System.Text;
global using EdgeGateway.Core.Extension;
global using EdgeGateway.Forwarding.HostServices;
global using EdgeGateway.SqlSugar.SqlSugar;
global using Furion.DependencyInjection;
global using Furion.DynamicApiController;
global using Furion.FriendlyException;
global using Furion.SpecificationDocument;
global using Microsoft.AspNetCore.Http;
global using Microsoft.AspNetCore.Mvc;
global using NPOI.XSSF.UserModel;
global using EdgeGateway.Forwarding.Entity;
global using System.Net.Http.Headers;
global using System.Buffers;
global using System.Net.Sockets;
global using System.Security.Authentication;
global using System.Security.Cryptography.X509Certificates;
global using EdgeGateway.WebSocket;
global using MQTTnet;
global using MQTTnet.Exceptions;
global using MQTTnet.Protocol;
global using EdgeGateway.Base.SystemStatistics;
global using EdgeGateway.Forwarding.Clients;
global using System.Text.Json;
global using EdgeGateway.Forwarding.Entity.Dto;
global using EdgeGateway.Forwarding.Entity.Dto.CustomConfig;
global using Mapster;
global using EdgeGateway.Forwarding.Storage;
global using Microsoft.Extensions.DependencyInjection;
global using EdgeGateway.Pipeline.Entity.Model;
