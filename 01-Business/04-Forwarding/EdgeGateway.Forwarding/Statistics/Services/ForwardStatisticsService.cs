using EdgeGateway.WebSocket;
using EdgeGateway.Forwarding.Statistics.Models;
using Microsoft.Extensions.Hosting;
using Microsoft.Extensions.Logging;
using Microsoft.Extensions.Options;
using System.Text.Json;

namespace EdgeGateway.Forwarding.Statistics.Services;

/// <summary>
/// 转发统计服务
/// 负责统计数据的收集、聚合和推送
/// </summary>
public class ForwardStatisticsService : BackgroundService, IForwardStatisticsService
{
  /// <summary>
  /// 日志
  /// </summary>
  private readonly ILogger<ForwardStatisticsService> _logger;
  /// <summary>
  /// 选项
  /// </summary>
  private readonly ForwardStatisticsOptions _options;
  /// <summary>
  /// 收集器
  /// </summary>
  private readonly ForwardStatisticsCollector _collector;
  /// <summary>
  /// 聚合器
  /// </summary>
  private readonly ForwardStatisticsAggregator _aggregator;
  /// <summary>
  /// 消息推送服务
  /// </summary>
  private readonly IMessagePushService _messagePushService;

  /// <summary>
  /// 统计推送定时器
  /// </summary>
  private Timer? _pushTimer;

  /// <summary>
  /// 数据清理定时器
  /// </summary>
  private Timer? _cleanupTimer;

  /// <summary>
  /// 服务启动状态
  /// </summary>
  private bool _isRunning;

  /// <summary>
  /// 构造函数
  /// </summary>
  public ForwardStatisticsService(
      ILogger<ForwardStatisticsService> logger,
      IOptions<ForwardStatisticsOptions> options,
      ForwardStatisticsCollector collector,
      ForwardStatisticsAggregator aggregator,
      IMessagePushService messagePushService)
  {
    _logger = logger;
    _options = options.Value;
    _collector = collector;
    _aggregator = aggregator;
    _messagePushService = messagePushService;
  }

  #region IForwardStatisticsService 实现

  /// <summary>
  /// 记录转发成功
  /// </summary>
  /// <param name="configId">配置ID</param>
  /// <param name="dataSize">数据大小</param>
  /// <param name="latencyMs">延迟时间</param>
  public void RecordSuccess(long configId, long dataSize, double latencyMs)
  {
    if (!_options.Enabled) return;

    try
    {
      _collector.RecordSuccess(configId, dataSize, latencyMs);
      // 同时记录全局统计
      _collector.RecordSuccess(0, dataSize, latencyMs);

      _logger.LogDebug("记录转发成功: ConfigId={ConfigId}, Size={Size}, Latency={Latency}ms",
          configId, dataSize, latencyMs);
    }
    catch (Exception ex)
    {
      _logger.LogError(ex, "记录转发成功时发生错误: ConfigId={ConfigId}", configId);
    }
  }

  /// <summary>
  /// 记录转发失败
  /// </summary>
  /// <param name="configId">配置ID</param>
  /// <param name="dataSize">数据大小</param>
  /// <param name="errorType">错误类型</param>
  /// <param name="latencyMs">延迟时间</param>
  public void RecordFailure(long configId, long dataSize, ForwardErrorType errorType, double latencyMs = 0)
  {
    if (!_options.Enabled) return;

    try
    {
      _collector.RecordFailure(configId, dataSize, errorType, latencyMs);
      // 同时记录全局统计
      _collector.RecordFailure(0, dataSize, errorType, latencyMs);

      _logger.LogDebug("记录转发失败: ConfigId={ConfigId}, ErrorType={ErrorType}, Size={Size}",
          configId, errorType, dataSize);
    }
    catch (Exception ex)
    {
      _logger.LogError(ex, "记录转发失败时发生错误: ConfigId={ConfigId}", configId);
    }
  }

  /// <summary>
  /// 记录重试消息
  /// </summary>
  /// <param name="configId">配置ID</param>
  /// <param name="dataSize">数据大小</param>
  public void RecordRetry(long configId, long dataSize)
  {
    if (!_options.Enabled) return;

    try
    {
      _collector.RecordRetry(configId, dataSize);
      // 同时记录全局统计
      _collector.RecordRetry(0, dataSize);

      _logger.LogDebug("记录重试消息: ConfigId={ConfigId}, Size={Size}", configId, dataSize);
    }
    catch (Exception ex)
    {
      _logger.LogError(ex, "记录重试消息时发生错误: ConfigId={ConfigId}", configId);
    }
  }

  /// <summary>
  /// 记录完成的转发（成功后删除记录）
  /// </summary>
  /// <param name="configId">配置ID</param>
  /// <param name="dataSize">数据大小</param>
  public void RecordCompletion(long configId, long dataSize)
  {
    if (!_options.Enabled) return;

    try
    {
      _collector.RecordCompletion(configId, dataSize);
      // 同时记录全局统计
      _collector.RecordCompletion(0, dataSize);

      _logger.LogDebug("记录完成的转发: ConfigId={ConfigId}, Size={Size}", configId, dataSize);
    }
    catch (Exception ex)
    {
      _logger.LogError(ex, "记录完成的转发时发生错误: ConfigId={ConfigId}", configId);
    }
  }

  /// <summary>
  /// 更新离线消息数
  /// </summary>
  /// <param name="configId">配置ID</param>
  /// <param name="count">离线消息数量</param>
  public void UpdateOfflineMessageCount(long configId, long count)
  {
    if (!_options.Enabled) return;

    try
    {
      _collector.RecordOfflineMessages(configId, count);

      _logger.LogDebug("更新离线消息数: ConfigId={ConfigId}, Count={Count}", configId, count);
    }
    catch (Exception ex)
    {
      _logger.LogError(ex, "更新离线消息数时发生错误: ConfigId={ConfigId}", configId);
    }
  }

  /// <summary>
  /// 注册转发配置
  /// </summary>
  /// <param name="configId">配置ID</param>
  /// <param name="configName">配置名称</param>
  /// <param name="forwardType">转发类型</param>
  public void RegisterForwardConfig(long configId, string configName, string forwardType)
  {
    if (!_options.Enabled) return;

    try
    {
      _aggregator.UpdateConfigInfo(configId, configName, forwardType);

      _logger.LogInformation("注册转发配置: ConfigId={ConfigId}, Name={Name}, Type={Type}",
          configId, configName, forwardType);
    }
    catch (Exception ex)
    {
      _logger.LogError(ex, "注册转发配置时发生错误: ConfigId={ConfigId}", configId);
    }
  }

  /// <summary>
  /// 注销转发配置
  /// </summary>
  /// <param name="configId">配置ID</param>
  public void UnregisterForwardConfig(long configId)
  {
    if (!_options.Enabled) return;

    try
    {
      _aggregator.RemoveConfigInfo(configId);

      _logger.LogInformation("注销转发配置: ConfigId={ConfigId}", configId);
    }
    catch (Exception ex)
    {
      _logger.LogError(ex, "注销转发配置时发生错误: ConfigId={ConfigId}", configId);
    }
  }

  /// <summary>
  /// 获取配置统计数据
  /// </summary>
  /// <param name="configId">配置ID</param>
  /// <returns>统计数据</returns>
  public ForwardStatistics GetConfigStatistics(long configId)
  {
    try
    {
      return _aggregator.GetConfigStatistics(configId);
    }
    catch (Exception ex)
    {
      _logger.LogError(ex, "获取配置统计数据时发生错误: ConfigId={ConfigId}", configId);
      return new ForwardStatistics { ConfigId = configId };
    }
  }

  /// <summary>
  /// 获取全局统计数据
  /// </summary>
  /// <returns>全局统计数据</returns>
  public ForwardStatistics GetGlobalStatistics()
  {
    try
    {
      return _aggregator.GetGlobalStatistics();
    }
    catch (Exception ex)
    {
      _logger.LogError(ex, "获取全局统计数据时发生错误");
      return new ForwardStatistics { ConfigId = 0, ConfigName = "全局统计" };
    }
  }

  /// <summary>
  /// 获取性能报告
  /// </summary>
  /// <returns>性能报告</returns>
  public ForwardPerformanceReport GetPerformanceReport()
  {
    try
    {
      return _aggregator.GetPerformanceReport();
    }
    catch (Exception ex)
    {
      _logger.LogError(ex, "获取性能报告时发生错误");
      return new ForwardPerformanceReport { GeneratedAt = DateTime.Now };
    }
  }

  #endregion

  #region BackgroundService 实现

  /// <summary>
  /// 启动后台服务
  /// </summary>
  protected override async Task ExecuteAsync(CancellationToken stoppingToken)
  {
    if (!_options.Enabled)
    {
      _logger.LogInformation("转发统计服务已禁用");
      return;
    }

    _logger.LogInformation("转发统计服务启动，推送间隔: {Interval}秒", _options.PushInterval);
    _isRunning = true;

    try
    {
      // 启动统计推送定时器
      _pushTimer = new Timer(OnPushTimerElapsed, null,
          TimeSpan.FromSeconds(_options.PushInterval),
          TimeSpan.FromSeconds(_options.PushInterval));

      // 启动数据清理定时器（每小时清理一次）
      _cleanupTimer = new Timer(OnCleanupTimerElapsed, null,
          TimeSpan.FromHours(1),
          TimeSpan.FromHours(1));

      // 等待取消信号
      await Task.Delay(Timeout.Infinite, stoppingToken);
    }
    catch (OperationCanceledException)
    {
      _logger.LogInformation("转发统计服务正在停止");
    }
    catch (Exception ex)
    {
      _logger.LogError(ex, "转发统计服务运行时发生错误");
    }
    finally
    {
      _isRunning = false;
      _pushTimer?.Dispose();
      _cleanupTimer?.Dispose();
      _logger.LogInformation("转发统计服务已停止");
    }
  }

  #endregion

  #region 私有方法

  /// <summary>
  /// 统计推送定时器回调
  /// </summary>
  private void OnPushTimerElapsed(object? state)
  {
    if (!_isRunning || !_options.Enabled) return;

    _ = Task.Run(async () =>
    {
      try
      {
        await PushStatisticsAsync();
      }
      catch (Exception ex)
      {
        _logger.LogError(ex, "推送统计数据时发生错误");
      }
    });
  }

  /// <summary>
  /// 数据清理定时器回调
  /// </summary>
  private void OnCleanupTimerElapsed(object? state)
  {
    if (!_isRunning || !_options.Enabled) return;

    _ = Task.Run(() =>
    {
      try
      {
        _aggregator.CleanupExpiredData();
        _logger.LogDebug("定期数据清理完成");
      }
      catch (Exception ex)
      {
        _logger.LogError(ex, "数据清理时发生错误");
      }
    });
  }

  /// <summary>
  /// 推送统计数据到WebSocket
  /// </summary>
  private async Task PushStatisticsAsync()
  {
    try
    {
      // 获取全局统计数据
      var globalStats = GetGlobalStatistics();

      // 推送全局统计
      await PushStatisticsToWebSocket("global", globalStats);

      // 推送分组统计（按类型）
      var groupedStats = _aggregator.GetGroupedStatistics("type");
      foreach (var group in groupedStats)
      {
        await PushStatisticsToWebSocket($"type_{group.Key.ToLower()}", group.Value);
      }

      // 推送性能报告（每分钟推送一次）
      var currentMinute = DateTime.Now.Minute;
      if (currentMinute % 1 == 0) // 每分钟推送
      {
        var performanceReport = GetPerformanceReport();
        await PushPerformanceReportToWebSocket(performanceReport);
      }

      _logger.LogDebug("统计数据推送完成");
    }
    catch (Exception ex)
    {
      _logger.LogError(ex, "推送统计数据时发生错误");
    }
  }

  /// <summary>
  /// 推送统计数据到WebSocket
  /// </summary>
  private async Task PushStatisticsToWebSocket(string type, ForwardStatistics statistics)
  {
    try
    {
      var topic = $"{_options.PushTopicPrefix}_{type}";
      var message = JsonSerializer.Serialize(statistics, new JsonSerializerOptions
      {
        PropertyNamingPolicy = JsonNamingPolicy.CamelCase,
        WriteIndented = false
      });

      await _messagePushService.PushMessageAsync(topic, message);

      _logger.LogDebug("推送统计数据: Topic={Topic}, ConfigId={ConfigId}", topic, statistics.ConfigId);
    }
    catch (Exception ex)
    {
      _logger.LogError(ex, "推送统计数据到WebSocket时发生错误: Type={Type}", type);
    }
  }

  /// <summary>
  /// 推送性能报告到WebSocket
  /// </summary>
  private async Task PushPerformanceReportToWebSocket(ForwardPerformanceReport report)
  {
    try
    {
      var topic = $"{_options.PushTopicPrefix}_performance_report";
      var message = JsonSerializer.Serialize(report, new JsonSerializerOptions
      {
        PropertyNamingPolicy = JsonNamingPolicy.CamelCase,
        WriteIndented = false
      });

      await _messagePushService.PushMessageAsync(topic, message);

      _logger.LogDebug("推送性能报告: Topic={Topic}", topic);
    }
    catch (Exception ex)
    {
      _logger.LogError(ex, "推送性能报告到WebSocket时发生错误");
    }
  }

  #endregion

  #region IDisposable 实现

  /// <summary>
  /// 释放资源
  /// </summary>
  public override void Dispose()
  {
    _isRunning = false;
    _pushTimer?.Dispose();
    _cleanupTimer?.Dispose();
    base.Dispose();
  }

  #endregion
}

/// <summary>
/// 转发统计服务接口
/// </summary>
public interface IForwardStatisticsService
{
  /// <summary>
  /// 记录转发成功
  /// </summary>
  /// <param name="configId">配置ID</param>
  /// <param name="dataSize">数据大小</param>
  /// <param name="latencyMs">延迟时间</param>
  void RecordSuccess(long configId, long dataSize, double latencyMs);

  /// <summary>
  /// 记录转发失败
  /// </summary>
  /// <param name="configId">配置ID</param>
  /// <param name="dataSize">数据大小</param>
  /// <param name="errorType">错误类型</param>
  /// <param name="latencyMs">延迟时间</param>
  void RecordFailure(long configId, long dataSize, ForwardErrorType errorType, double latencyMs = 0);

  /// <summary>
  /// 记录重试消息
  /// </summary>
  /// <param name="configId">配置ID</param>
  /// <param name="dataSize">数据大小</param>
  void RecordRetry(long configId, long dataSize);

  /// <summary>
  /// 记录完成的转发（成功后删除记录）
  /// </summary>
  /// <param name="configId">配置ID</param>
  /// <param name="dataSize">数据大小</param>
  void RecordCompletion(long configId, long dataSize);

  /// <summary>
  /// 更新离线消息数
  /// </summary>
  /// <param name="configId">配置ID</param>
  /// <param name="count">离线消息数量</param>
  void UpdateOfflineMessageCount(long configId, long count);

  /// <summary>
  /// 注册转发配置
  /// </summary>
  /// <param name="configId">配置ID</param>
  /// <param name="configName">配置名称</param>
  /// <param name="forwardType">转发类型</param>
  void RegisterForwardConfig(long configId, string configName, string forwardType);

  /// <summary>
  /// 注销转发配置
  /// </summary>
  /// <param name="configId">配置ID</param>
  void UnregisterForwardConfig(long configId);

  /// <summary>
  /// 获取配置统计数据
  /// </summary>
  /// <param name="configId">配置ID</param>
  /// <returns>统计数据</returns>
  ForwardStatistics GetConfigStatistics(long configId);

  /// <summary>
  /// 获取全局统计数据
  /// </summary>
  /// <returns>全局统计数据</returns>
  ForwardStatistics GetGlobalStatistics();

  /// <summary>
  /// 获取性能报告
  /// </summary>
  /// <returns>性能报告</returns>
  ForwardPerformanceReport GetPerformanceReport();
}