using EdgeGateway.Forwarding.Statistics.Models;
using Microsoft.Extensions.Logging;
using Microsoft.Extensions.Options;

namespace EdgeGateway.Forwarding.Statistics.Services;

/// <summary>
/// 转发统计聚合器
/// 负责聚合多个转发客户端的统计数据，计算全局指标
/// </summary>
public class ForwardStatisticsAggregator
{
  private readonly ILogger<ForwardStatisticsAggregator> _logger;
  private readonly ForwardStatisticsOptions _options;
  private readonly ForwardStatisticsCollector _collector;

  /// <summary>
  /// 转发配置信息缓存
  /// Key: ConfigId, Value: 配置信息
  /// </summary>
  private readonly Dictionary<long, ForwardConfigInfo> _configInfoCache = new();

  /// <summary>
  /// 构造函数
  /// </summary>
  public ForwardStatisticsAggregator(
      ILogger<ForwardStatisticsAggregator> logger,
      IOptions<ForwardStatisticsOptions> options,
      ForwardStatisticsCollector collector)
  {
    _logger = logger;
    _options = options.Value;
    _collector = collector;
  }

  /// <summary>
  /// 更新转发配置信息
  /// </summary>
  /// <param name="configId">配置ID</param>
  /// <param name="configName">配置名称</param>
  /// <param name="forwardType">转发类型</param>
  public void UpdateConfigInfo(long configId, string configName, string forwardType)
  {
    try
    {
      _configInfoCache[configId] = new ForwardConfigInfo
      {
        ConfigId = configId,
        ConfigName = configName,
        ForwardType = forwardType,
        LastUpdateTime = DateTime.Now
      };

      _logger.LogDebug("更新转发配置信息: ConfigId={ConfigId}, Name={Name}, Type={Type}",
          configId, configName, forwardType);
    }
    catch (Exception ex)
    {
      _logger.LogError(ex, "更新配置信息时发生错误: ConfigId={ConfigId}", configId);
    }
  }

  /// <summary>
  /// 移除转发配置信息
  /// </summary>
  /// <param name="configId">配置ID</param>
  public void RemoveConfigInfo(long configId)
  {
    try
    {
      _configInfoCache.Remove(configId);
      _logger.LogDebug("移除转发配置信息: ConfigId={ConfigId}", configId);
    }
    catch (Exception ex)
    {
      _logger.LogError(ex, "移除配置信息时发生错误: ConfigId={ConfigId}", configId);
    }
  }

  /// <summary>
  /// 获取指定配置的统计数据
  /// </summary>
  /// <param name="configId">配置ID</param>
  /// <returns>统计数据</returns>
  public ForwardStatistics GetConfigStatistics(long configId)
  {
    try
    {
      var statistics = _collector.GetStatistics(configId);

      // 补充配置信息
      if (_configInfoCache.TryGetValue(configId, out var configInfo))
      {
        statistics.ConfigName = configInfo.ConfigName;
        statistics.ForwardType = configInfo.ForwardType;
      }

      return statistics;
    }
    catch (Exception ex)
    {
      _logger.LogError(ex, "获取配置统计数据时发生错误: ConfigId={ConfigId}", configId);
      return new ForwardStatistics { ConfigId = configId };
    }
  }

  /// <summary>
  /// 获取全局统计数据（所有配置聚合）
  /// </summary>
  /// <returns>全局统计数据</returns>
  public ForwardStatistics GetGlobalStatistics()
  {
    try
    {
      var allConfigs = _configInfoCache.Keys.ToList();

      // 如果配置信息缓存为空，直接从收集器获取ConfigId=0的全局统计数据
      if (!allConfigs.Any())
      {
        _logger.LogDebug("配置信息缓存为空，直接获取全局统计数据");
        var directGlobalStats = _collector.GetStatistics(0);
        directGlobalStats.ConfigId = 0;
        directGlobalStats.ConfigName = "全局统计";
        directGlobalStats.ForwardType = "All";
        directGlobalStats.Timestamp = DateTime.Now;
        return directGlobalStats;
      }

      var globalStats = AggregateStatistics(allConfigs);

      globalStats.ConfigId = 0;
      globalStats.ConfigName = "全局统计";
      globalStats.ForwardType = "All";
      globalStats.Timestamp = DateTime.Now;

      return globalStats;
    }
    catch (Exception ex)
    {
      _logger.LogError(ex, "获取全局统计数据时发生错误");
      // 发生异常时，返回ConfigId=0的基础统计数据
      var fallbackStats = _collector.GetStatistics(0);
      fallbackStats.ConfigId = 0;
      fallbackStats.ConfigName = "全局统计";
      fallbackStats.ForwardType = "All";
      fallbackStats.Timestamp = DateTime.Now;
      return fallbackStats;
    }
  }

  /// <summary>
  /// 获取分组统计数据
  /// </summary>
  /// <param name="groupBy">分组方式：type（按转发类型）</param>
  /// <returns>分组统计数据</returns>
  public Dictionary<string, ForwardStatistics> GetGroupedStatistics(string groupBy = "type")
  {
    try
    {
      var result = new Dictionary<string, ForwardStatistics>();

      switch (groupBy.ToLower())
      {
        case "type":
          var typeGroups = _configInfoCache.Values
              .GroupBy(c => c.ForwardType)
              .ToList();

          foreach (var group in typeGroups)
          {
            var configIds = group.Select(c => c.ConfigId).ToList();
            var aggregated = AggregateStatistics(configIds);
            aggregated.ConfigId = 0;
            aggregated.ConfigName = $"{group.Key}类型统计";
            aggregated.ForwardType = group.Key;
            result[group.Key] = aggregated;
          }
          break;

        default:
          _logger.LogWarning("不支持的分组方式: {GroupBy}", groupBy);
          break;
      }

      return result;
    }
    catch (Exception ex)
    {
      _logger.LogError(ex, "获取分组统计数据时发生错误: GroupBy={GroupBy}", groupBy);
      return new Dictionary<string, ForwardStatistics>();
    }
  }

  /// <summary>
  /// 获取Top N配置统计
  /// </summary>
  /// <param name="topN">Top数量</param>
  /// <param name="orderBy">排序字段：messages（消息数）、success_rate（成功率）、latency（延迟）</param>
  /// <returns>Top N统计数据</returns>
  public List<ForwardStatistics> GetTopConfigurations(int topN = 10, string orderBy = "messages")
  {
    try
    {
      var allConfigs = _configInfoCache.Keys.ToList();
      var configStats = allConfigs.Select(id => GetConfigStatistics(id)).ToList();

      switch (orderBy.ToLower())
      {
        case "messages":
          configStats = configStats
              .OrderByDescending(s => s.Metrics.TotalMessages)
              .Take(topN)
              .ToList();
          break;

        case "success_rate":
          configStats = configStats
              .OrderByDescending(s => s.Metrics.SuccessRate)
              .Take(topN)
              .ToList();
          break;

        case "latency":
          configStats = configStats
              .OrderBy(s => s.LatencyMetrics.AverageLatency)
              .Take(topN)
              .ToList();
          break;

        default:
          _logger.LogWarning("不支持的排序字段: {OrderBy}", orderBy);
          configStats = configStats.Take(topN).ToList();
          break;
      }

      return configStats;
    }
    catch (Exception ex)
    {
      _logger.LogError(ex, "获取Top配置统计时发生错误: TopN={TopN}, OrderBy={OrderBy}", topN, orderBy);
      return new List<ForwardStatistics>();
    }
  }

  /// <summary>
  /// 获取性能报告
  /// </summary>
  /// <returns>性能报告</returns>
  public ForwardPerformanceReport GetPerformanceReport()
  {
    try
    {
      var globalStats = GetGlobalStatistics();
      var configStats = _configInfoCache.Keys.Select(id => GetConfigStatistics(id)).ToList();

      // 计算性能指标
      var avgSuccessRate = configStats.Any() ? configStats.Average(s => s.Metrics.SuccessRate) : 0;
      var avgLatency = configStats.Any() ? configStats.Average(s => s.LatencyMetrics.AverageLatency) : 0;
      var totalThroughput = configStats.Sum(s => s.RealTimeMetrics.MessagesPerSecond);

      // 识别问题配置
      var problemConfigs = configStats
          .Where(s => s.Metrics.SuccessRate < 90 || s.LatencyMetrics.AverageLatency > 1000)
          .ToList();

      // 统计错误分布
      var errorDistribution = new Dictionary<ForwardErrorType, long>();
      foreach (var stat in configStats)
      {
        foreach (var error in stat.ErrorCounts)
        {
          errorDistribution[error.Key] = errorDistribution.GetValueOrDefault(error.Key, 0) + error.Value;
        }
      }

      return new ForwardPerformanceReport
      {
        GeneratedAt = DateTime.Now,
        TotalConfigurations = configStats.Count,
        ActiveConfigurations = configStats.Count(s => s.RealTimeMetrics.ActiveConnections > 0),
        AverageSuccessRate = avgSuccessRate,
        AverageLatency = avgLatency,
        TotalThroughput = totalThroughput,
        ProblemConfigurations = problemConfigs.Count,
        ErrorDistribution = errorDistribution,
        ProblemConfigDetails = problemConfigs.Take(5).ToList(), // 只包含前5个问题配置
        Recommendations = GenerateRecommendations(globalStats, configStats)
      };
    }
    catch (Exception ex)
    {
      _logger.LogError(ex, "生成性能报告时发生错误");
      return new ForwardPerformanceReport { GeneratedAt = DateTime.Now };
    }
  }

  /// <summary>
  /// 清理过期数据
  /// </summary>
  public void CleanupExpiredData()
  {
    try
    {
      // 清理过期的配置信息
      var expiredConfigs = _configInfoCache
          .Where(kvp => DateTime.Now - kvp.Value.LastUpdateTime > TimeSpan.FromHours(24))
          .Select(kvp => kvp.Key)
          .ToList();

      foreach (var configId in expiredConfigs)
      {
        _configInfoCache.Remove(configId);
      }

      // 清理统计收集器中的过期数据
      _collector.CleanupExpiredData();

      _logger.LogDebug("清理过期数据完成，清理配置数量: {Count}", expiredConfigs.Count);
    }
    catch (Exception ex)
    {
      _logger.LogError(ex, "清理过期数据时发生错误");
    }
  }

  #region 私有方法

  /// <summary>
  /// 聚合统计数据
  /// </summary>
  private ForwardStatistics AggregateStatistics(List<long> configIds)
  {
    try
    {
      // 防止空集合异常
      if (configIds?.Any() != true)
      {
        return CreateEmptyStatistics();
      }

      var stats = configIds.Select(id => _collector.GetStatistics(id))
                           .Where(s => s != null) // 过滤掉null统计数据
                           .ToList();

      // 如果没有有效的统计数据，返回空统计
      if (!stats.Any())
      {
        return CreateEmptyStatistics();
      }

      var aggregated = new ForwardStatistics
      {
        Timestamp = DateTime.Now,
        Metrics = new ForwardMetrics
        {
          TotalMessages = stats.Sum(s => s.Metrics?.TotalMessages ?? 0),
          SuccessMessages = stats.Sum(s => s.Metrics?.SuccessMessages ?? 0),
          FailedMessages = stats.Sum(s => s.Metrics?.FailedMessages ?? 0),
          RetryMessages = stats.Sum(s => s.Metrics?.RetryMessages ?? 0),
          OfflineMessages = stats.Sum(s => s.Metrics?.OfflineMessages ?? 0),
          TotalBytes = stats.Sum(s => s.Metrics?.TotalBytes ?? 0),
          TodayGrowth = stats.Sum(s => s.Metrics?.TodayGrowth ?? 0),
          GrowthRate = stats.Any() ? stats.Where(s => s.Metrics != null).Average(s => s.Metrics.GrowthRate) : 0
        },
        RealTimeMetrics = new ForwardRealTimeMetrics
        {
          MessagesPerSecond = stats.Sum(s => s.RealTimeMetrics?.MessagesPerSecond ?? 0),
          BytesPerSecond = stats.Sum(s => s.RealTimeMetrics?.BytesPerSecond ?? 0),
          ActiveConnections = stats.Sum(s => s.RealTimeMetrics?.ActiveConnections ?? 0),
          QueueLength = stats.Sum(s => s.RealTimeMetrics?.QueueLength ?? 0)
        },
        LatencyMetrics = AggregateLatencyMetrics(stats),
        ErrorCounts = AggregateErrorCounts(stats),
        TimeSeries = AggregateTimeSeries(stats)
      };

      return aggregated;
    }
    catch (Exception ex)
    {
      _logger.LogError(ex, "聚合统计数据时发生错误");
      return CreateEmptyStatistics();
    }
  }

  /// <summary>
  /// 创建空的统计数据
  /// </summary>
  private ForwardStatistics CreateEmptyStatistics()
  {
    return new ForwardStatistics
    {
      Timestamp = DateTime.Now,
      Metrics = new ForwardMetrics(),
      RealTimeMetrics = new ForwardRealTimeMetrics(),
      LatencyMetrics = new ForwardLatencyMetrics(),
      ErrorCounts = new Dictionary<ForwardErrorType, long>(),
      TimeSeries = new List<ForwardTimeSeriesPoint>()
    };
  }

  /// <summary>
  /// 聚合延迟指标
  /// </summary>
  private ForwardLatencyMetrics AggregateLatencyMetrics(List<ForwardStatistics> stats)
  {
    try
    {
      // 防止空集合异常
      if (stats?.Any() != true)
      {
        return new ForwardLatencyMetrics();
      }

      var validStats = stats.Where(s => s.LatencyMetrics?.AverageLatency > 0).ToList();
      if (!validStats.Any())
      {
        return new ForwardLatencyMetrics();
      }

      return new ForwardLatencyMetrics
      {
        AverageLatency = validStats.Average(s => s.LatencyMetrics.AverageLatency),
        MinLatency = validStats.Min(s => s.LatencyMetrics.MinLatency),
        MaxLatency = validStats.Max(s => s.LatencyMetrics.MaxLatency),
        P50Latency = validStats.Average(s => s.LatencyMetrics.P50Latency),
        P90Latency = validStats.Average(s => s.LatencyMetrics.P90Latency),
        P99Latency = validStats.Average(s => s.LatencyMetrics.P99Latency)
      };
    }
    catch (Exception ex)
    {
      _logger.LogError(ex, "聚合延迟指标时发生错误");
      return new ForwardLatencyMetrics();
    }
  }

  /// <summary>
  /// 聚合错误统计
  /// </summary>
  private Dictionary<ForwardErrorType, long> AggregateErrorCounts(List<ForwardStatistics> stats)
  {
    var aggregated = new Dictionary<ForwardErrorType, long>();

    try
    {
      // 防止空集合异常
      if (stats?.Any() != true)
      {
        return aggregated;
      }

      foreach (var stat in stats)
      {
        // 防止ErrorCounts为null
        if (stat.ErrorCounts?.Any() == true)
        {
          foreach (var error in stat.ErrorCounts)
          {
            aggregated[error.Key] = aggregated.GetValueOrDefault(error.Key, 0) + error.Value;
          }
        }
      }
    }
    catch (Exception ex)
    {
      _logger.LogError(ex, "聚合错误统计时发生错误");
    }

    return aggregated;
  }

  /// <summary>
  /// 聚合时间序列数据
  /// </summary>
  private List<ForwardTimeSeriesPoint> AggregateTimeSeries(List<ForwardStatistics> stats)
  {
    try
    {
      // 防止空集合异常
      if (stats?.Any() != true)
      {
        return new List<ForwardTimeSeriesPoint>();
      }

      // 按时间戳分组并聚合
      var timeGroups = stats
          .Where(s => s.TimeSeries?.Any() == true) // 过滤掉没有时间序列数据的统计
          .SelectMany(s => s.TimeSeries)
          .GroupBy(ts => ts.Timestamp)
          .OrderBy(g => g.Key)
          .Select(g => new ForwardTimeSeriesPoint
          {
            Timestamp = g.Key,
            MessageCount = g.Sum(ts => ts.MessageCount),
            SuccessCount = g.Sum(ts => ts.SuccessCount),
            FailCount = g.Sum(ts => ts.FailCount),
            AvgLatency = g.Where(ts => ts.AvgLatency > 0).Any() ?
                  g.Where(ts => ts.AvgLatency > 0).Average(ts => ts.AvgLatency) : 0,
            DataBytes = g.Sum(ts => ts.DataBytes)
          })
          .ToList();

      return timeGroups;
    }
    catch (Exception ex)
    {
      _logger.LogError(ex, "聚合时间序列数据时发生错误");
      return new List<ForwardTimeSeriesPoint>();
    }
  }

  /// <summary>
  /// 生成性能建议
  /// </summary>
  private List<string> GenerateRecommendations(ForwardStatistics globalStats, List<ForwardStatistics> configStats)
  {
    var recommendations = new List<string>();

    try
    {
      // 成功率建议
      if (globalStats.Metrics.SuccessRate < 95)
      {
        recommendations.Add($"整体成功率偏低({globalStats.Metrics.SuccessRate:F1}%)，建议检查网络连接和配置参数");
      }

      // 延迟建议
      if (globalStats.LatencyMetrics.AverageLatency > 500)
      {
        recommendations.Add($"平均延迟较高({globalStats.LatencyMetrics.AverageLatency:F0}ms)，建议优化网络或增加连接池");
      }

      // 错误分析建议 - 防止空集合异常
      if (globalStats.ErrorCounts?.Any() == true)
      {
        var topError = globalStats.ErrorCounts.OrderByDescending(e => e.Value).FirstOrDefault();
        if (topError.Key != ForwardErrorType.Other && topError.Value > 0)
        {
          recommendations.Add($"主要错误类型为{topError.Key}({topError.Value}次)，建议针对性优化");
        }
      }

      // 负载建议 - 防止空集合异常
      if (configStats?.Any() == true)
      {
        var maxThroughput = configStats.Max(s => s.RealTimeMetrics.MessagesPerSecond);
        if (maxThroughput > 100)
        {
          recommendations.Add("检测到高负载场景，建议考虑水平扩展或负载均衡");
        }
      }

      // 如果没有配置统计数据，给出提示
      if (configStats?.Any() != true)
      {
        recommendations.Add("当前没有活跃的转发配置，请检查配置状态");
      }
    }
    catch (Exception ex)
    {
      _logger.LogError(ex, "生成性能建议时发生错误");
      recommendations.Add("生成性能建议时遇到问题，请稍后重试");
    }

    return recommendations;
  }

  #endregion
}

/// <summary>
/// 转发配置信息
/// </summary>
internal class ForwardConfigInfo
{
  public long ConfigId { get; set; }
  public string ConfigName { get; set; } = string.Empty;
  public string ForwardType { get; set; } = string.Empty;
  public DateTime LastUpdateTime { get; set; }
}

/// <summary>
/// 转发性能报告
/// </summary>
public class ForwardPerformanceReport
{
  /// <summary>
  /// 报告生成时间
  /// </summary>
  public DateTime GeneratedAt { get; set; }

  /// <summary>
  /// 配置总数
  /// </summary>
  public int TotalConfigurations { get; set; }

  /// <summary>
  /// 活跃配置数
  /// </summary>
  public int ActiveConfigurations { get; set; }

  /// <summary>
  /// 平均成功率
  /// </summary>
  public double AverageSuccessRate { get; set; }

  /// <summary>
  /// 平均延迟
  /// </summary>
  public double AverageLatency { get; set; }

  /// <summary>
  /// 总吞吐量
  /// </summary>
  public double TotalThroughput { get; set; }

  /// <summary>
  /// 问题配置数量
  /// </summary>
  public int ProblemConfigurations { get; set; }

  /// <summary>
  /// 错误分布
  /// </summary>
  public Dictionary<ForwardErrorType, long> ErrorDistribution { get; set; } = new();

  /// <summary>
  /// 问题配置详情
  /// </summary>
  public List<ForwardStatistics> ProblemConfigDetails { get; set; } = new();

  /// <summary>
  /// 性能建议
  /// </summary>
  public List<string> Recommendations { get; set; } = new();
}