using EdgeGateway.Forwarding.Retry;

namespace EdgeGateway.Forwarding.HostServices;

/// <summary>
/// 重试后台服务
/// </summary>
public class RetryBackgroundService : BackgroundService
{
  /// <summary>
  /// 日志记录器  
  /// </summary>
  private readonly ILogger<RetryBackgroundService> _logger;

  /// <summary>
  /// 服务范围工厂
  /// </summary>
  private readonly IServiceScopeFactory _serviceScopeFactory;

  /// <summary>
  /// 周期计时器
  /// </summary>
  private readonly PeriodicTimer _timer;

  /// <summary>
  /// 构造函数
  /// </summary>
  /// <param name="logger">日志记录器</param>
  /// <param name="serviceScopeFactory">服务范围工厂</param>
  public RetryBackgroundService(
      ILogger<RetryBackgroundService> logger,
      IServiceScopeFactory serviceScopeFactory)
  {
    _logger = logger;
    _serviceScopeFactory = serviceScopeFactory;
    _timer = new PeriodicTimer(TimeSpan.FromSeconds(10)); // 每10秒检查一次
  }

  /// <summary>
  /// 执行任务
  /// </summary>
  protected override async Task ExecuteAsync(CancellationToken stoppingToken)
  {
    _logger.LogInformation("重试后台服务启动");

    try
    {
      // 周期性执行任务
      while (await _timer.WaitForNextTickAsync(stoppingToken))
      {
        // 处理待重试的记录
        await ProcessPendingRetries(stoppingToken);
      }
    }
    catch (OperationCanceledException)
    {
      _logger.LogInformation("重试后台服务停止");
    }
  }

  /// <summary>
  /// 处理待重试的记录
  /// </summary>
  private async Task ProcessPendingRetries(CancellationToken stoppingToken)
  {
    try
    {
      using var scope = _serviceScopeFactory.CreateScope();
      var failureRecordRepo = scope.ServiceProvider.GetRequiredService<SqlSugarRepository<ForwardFailureRecord>>();
      var retryService = scope.ServiceProvider.GetRequiredService<RetryService>();

      // 获取所有待重试且到达重试时间的记录
      var records = await failureRecordRepo.AsQueryable()
          .Where(x => x.Status == 0 && x.NextRetryTime <= DateTime.Now)
          .ToListAsync();

      // 遍历所有待重试的记录
      foreach (var record in records)
      {
        if (stoppingToken.IsCancellationRequested)
          break;

        try
        {
          // 处理重试
          await retryService.HandleRetryAsync(record);
        }
        catch (Exception ex)
        {
          _logger.LogError(ex, "处理重试记录失败: RecordId={RecordId}", record.Id);
        }
      }
    }
    catch (Exception ex)
    {
      _logger.LogError(ex, "处理待重试记录时发生错误");
    }
  }

  /// <summary>
  /// 停止服务
  /// </summary>
  public override async Task StopAsync(CancellationToken cancellationToken)
  {
    _logger.LogInformation("重试后台服务正在停止");
    await base.StopAsync(cancellationToken);
  }
}