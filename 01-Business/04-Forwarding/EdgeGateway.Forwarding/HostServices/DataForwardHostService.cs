using Furion.JsonSerialization;
using EdgeGateway.Engine.EngineActions;
using Microsoft.Extensions.DependencyInjection;
using Microsoft.Extensions.Hosting;
using Microsoft.Extensions.Logging;
using SqlSugar;
using System;
using System.Collections.Concurrent;
using System.Collections.Generic;
using System.Linq;
using System.Threading;
using System.Threading.Tasks;
using System.Net.Sockets;
using System.Net.Http;
using System.Security;
using EdgeGateway.Forwarding.Utils;
using EdgeGateway.Forwarding.Statistics.Services;
using EdgeGateway.Forwarding.Statistics.Models;
using EdgeGateway.Notification.Events;

namespace EdgeGateway.Forwarding.HostServices;

/// <summary>
///     数据转发服务
/// </summary>
public class DataForwardHostService : IHostedService
{
    /// <summary>
    ///     日志
    /// </summary>
    private readonly ILogger<DataForwardHostService> _logger;

    /// <summary>
    ///     日志工厂
    /// </summary>
    private readonly ILoggerFactory _loggerFactory;

    /// <summary>
    ///     队列服务
    /// </summary>
    private readonly QueueService<PayLoad> _queueService;

    /// <summary>
    ///     取消令牌源
    /// </summary>
    private readonly CancellationTokenSource _cancellationTokenSource = new();

    /// <summary>
    ///     存储所有转发客户端
    /// </summary>
    private readonly ConcurrentDictionary<long, IForwardClient> _forwardClients = new();

    /// <summary>
    /// 直接使用sqlsugarclient 
    /// </summary>
    private readonly ISqlSugarClient _sqlSugarClient;

    /// <summary>
    ///     重试任务
    /// </summary>
    private Task _retryTask;

    /// <summary>
    ///     处理任务
    /// </summary>
    private Task _processTask;

    /// <summary>
    ///     每次处理的批量大小
    /// </summary>
    private const int BatchSize = 100;

    /// <summary>
    ///     离线消息存储服务
    /// </summary>
    private readonly IOfflineStorageService _offlineStorage;

    /// <summary>
    ///     事件发布器
    /// </summary>
    private readonly IEventPublisher _eventPublisher;

    /// <summary>
    ///     消息推送服务
    /// </summary>
    private readonly IMessagePushService _messagePushService;

    /// <summary>
    ///     重试定时器
    /// </summary>
    private readonly PeriodicTimer _retryTimer;

    /// <summary>
    ///     系统统计服务
    /// </summary>
    private readonly ISystemStatisticsService _systemStatisticsService;

    /// <summary>
    ///     添加一个字段来跟踪统计任务
    /// </summary>
    private Task _statisticsTask;

    /// <summary>
    ///     内存缓冲区，用于存储失败记录，减少数据库写入频率
    /// </summary>
    private readonly ConcurrentQueue<ForwardFailureRecord> _failureRecordBuffer = new();

    /// <summary>
    ///     配置缓存字典，用于存储转发配置，减少数据库查询操作
    /// </summary>
    private readonly ConcurrentDictionary<long, ForwardConfig> _forwardConfigCache = new();

    /// <summary>
    ///     缓冲区大小阈值，当缓冲区中的记录数量达到此值时，触发批量写入
    /// </summary>
    private const int BufferThreshold = 50;

    /// <summary>
    ///     缓冲区刷新定时器
    /// </summary>
    private readonly PeriodicTimer _bufferFlushTimer;

    /// <summary>
    ///     缓冲区刷新任务
    /// </summary>
    private Task _bufferFlushTask;

    /// <summary>
    ///     缓冲区锁对象
    /// </summary>
    private readonly SemaphoreSlim _bufferLock = new(1, 1);

    /// <summary>
    /// 脚本引擎池
    /// </summary>
    private readonly JsScriptEnginePool _scriptEnginePool;

    /// <summary>
    /// 统计服务
    /// </summary>
    private readonly IForwardStatisticsService _statisticsService;

    /// <summary>
    /// 服务提供者
    /// </summary>
    private readonly IServiceProvider _serviceProvider;

    /// <summary>
    ///     数据转发主机服务类
    /// </summary>
    /// <remarks>
    ///     该类负责管理数据转发配置、创建和管理转发客户端，以及处理数据转发的启动和停止操作。
    /// </remarks>
    public DataForwardHostService(
        ILogger<DataForwardHostService> logger,
        ILoggerFactory loggerFactory,
        QueueService<PayLoad> queueService,
        IOfflineStorageService offlineStorage,
        IEventPublisher eventPublisher,
        IMessagePushService messagePushService,
        ISqlSugarClient sqlSugarClient,
        ISystemStatisticsService systemStatisticsService,
        JsScriptEnginePool scriptEnginePool,
        IForwardStatisticsService statisticsService,
        IServiceProvider serviceProvider)
    {
        _logger = logger;
        _loggerFactory = loggerFactory;
        _queueService = queueService;
        _offlineStorage = offlineStorage;
        _eventPublisher = eventPublisher;
        _messagePushService = messagePushService;
        _sqlSugarClient = sqlSugarClient;
        _systemStatisticsService = systemStatisticsService;
        _scriptEnginePool = scriptEnginePool;
        _retryTimer = new PeriodicTimer(TimeSpan.FromSeconds(10)); // 每10秒检查一次重试
        _bufferFlushTimer = new PeriodicTimer(TimeSpan.FromSeconds(5)); // 每5秒刷新一次缓冲区
        _statisticsService = statisticsService;
        _serviceProvider = serviceProvider;
    }

    /// <summary>
    ///     启动服务
    /// </summary>
    /// <param name="cancellationToken">取消令牌</param>
    public async Task StartAsync(CancellationToken cancellationToken)
    {
        try
        {
            // 优化SQLite配置
            OptimizeSqliteConfiguration();

            // 启动统计信息更新任务
            _statisticsTask = UpdateForwardStatisticsAsync(_cancellationTokenSource.Token);

            // 启动缓冲区刷新任务
            _bufferFlushTask = FlushBufferPeriodicAsync(_cancellationTokenSource.Token);

            // 异步启动初始化过程，不等待完成
            _ = Task.Run(async () =>
            {
                try
                {
                    // 从数据库加载转发配置
                    var configs = await LoadForwardConfigs();

                    // 初始化所有转发客户端
                    foreach (var config in configs.Where(c => c.Enable))
                    {
                        var client = CreateForwardClient(config);
                        _forwardClients.TryAdd(config.Id, client);
                        await client.ConnectAsync(cancellationToken);

                        // 注册配置信息到统计服务
                        _statisticsService.RegisterForwardConfig(config.Id, config.Name, config.Type.ToString());
                        _logger.LogDebug("已注册配置到统计服务: ConfigId={ConfigId}, Name={Name}, Type={Type}",
                            config.Id, config.Name, config.Type);
                    }

                    // 启动处理任务
                    _processTask = ProcessQueueAsync(_cancellationTokenSource.Token);
                    // 启动重试任务
                    _retryTask = ProcessRetryAsync(_cancellationTokenSource.Token);

                    _logger.LogInformation("数据转发服务初始化完成");
                }
                catch (Exception ex)
                {
                    _logger.LogError(ex, "数据转发服务初始化过程中发生错误");
                }
            }, cancellationToken);

            _logger.LogInformation("数据转发服务已启动，后台初始化进行中...");
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "启动数据转发服务时发生错误");
            throw;
        }
    }

    /// <summary>
    ///     停止服务
    /// </summary>
    /// <param name="cancellationToken">取消令牌</param>
    public async Task StopAsync(CancellationToken cancellationToken)
    {
        try
        {
            // 取消处理任务
            _cancellationTokenSource.Cancel();

            // 在停止服务前，确保缓冲区中的数据被写入数据库
            await FlushBufferAsync();

            if (_processTask != null)
                await _processTask;
            if (_retryTask != null)
                await _retryTask;
            if (_statisticsTask != null)
                await _statisticsTask;
            if (_bufferFlushTask != null)
                await _bufferFlushTask;

            // 断开所有客户端连接并释放资源
            foreach (var client in _forwardClients.Values)
            {
                await client.DisconnectAsync();
                client.Dispose();
            }

            // 释放MQTT客户端资源
            _forwardClients.Clear();

            // 释放定时器资源
            _retryTimer?.Dispose();
            _bufferFlushTimer?.Dispose();
            _bufferLock?.Dispose();
            _logger.LogInformation("数据转发服务已停止");
        }
        catch (Exception ex)
        {
            // 如果是任务取消异常,则不需要记录错误日志
            if (ex is TaskCanceledException)
                return;
            _logger.LogError(ex, "停止数据转发服务时发生错误");
        }
    }

    /// <summary>
    ///     处理队列数据
    /// </summary>
    /// <param name="cancellationToken">取消令牌</param>
    private async Task ProcessQueueAsync(CancellationToken cancellationToken)
    {
        while (!cancellationToken.IsCancellationRequested)
            try
            {
                // 批量获取数据
                var items = await _queueService.DequeueRangeAsync(BatchSize);
                // 如果获取的数据为空,则延迟100毫秒
                if (!items.Any())
                {
                    // 延迟100毫秒
                    await Task.Delay(100, cancellationToken);
                    continue;
                }

                // 遍历所有转发客户端,考虑并行发送
                Parallel.ForEach(_forwardClients, async clientKvp =>
                {
                    try
                    {
                        // 数据已在处理层处理过，直接使用处理后的数据
                        // 每个处理后的负载可能对应多个设备的数据，已经聚合和清洗过
                        // 转换数据 todo 暂时不实现
                        var transformedData = await TransformData(items, clientKvp.Value.Config);
                        // 创建取消令牌源
                        using var cts = new CancellationTokenSource(clientKvp.Value.Config.Timeout);
                        // 创建取消令牌源
                        using var linkedCts = CancellationTokenSource.CreateLinkedTokenSource(cts.Token, cancellationToken);
                        // 转发数据
                        await ForwardWithRetryAsync(clientKvp.Key, transformedData, linkedCts.Token);
                    }
                    catch (OperationCanceledException) when (!cancellationToken.IsCancellationRequested)
                    {
                        // 记录超时
                        _logger.LogWarning("转发超时 - 客户端ID: {ClientId}", clientKvp.Key);
                    }
                    catch (Exception ex)
                    {
                        // 记录错误
                        _logger.LogError(ex, "转发失败 - 客户端ID: {ClientId}", clientKvp.Key);
                    }
                });
            }
            catch (Exception ex)
            {
                // 如果是任务取消异常,则不需要记录错误日志
                if (!(ex is TaskCanceledException))
                    _logger.LogError(ex, "处理队列数据时发生错误");
                // 延迟1秒
                await Task.Delay(1000, cancellationToken);
            }
    }

    /// <summary>
    /// 处理重试任务
    /// </summary>
    /// <param name="cancellationToken">取消令牌</param>
    /// <returns></returns>
    private async Task ProcessRetryAsync(CancellationToken cancellationToken)
    {
        try
        {
            using var scope = _serviceProvider.CreateScope();
            // 上次查询时间
            DateTime lastQueryTime = DateTime.MinValue;
            // 最小查询间隔（毫秒）
            const int minQueryInterval = 5000;

            while (!cancellationToken.IsCancellationRequested)
                try
                {
                    // 等待下一次重试
                    await _retryTimer.WaitForNextTickAsync(cancellationToken);

                    // 检查是否达到最小查询间隔
                    var now = DateTime.Now;
                    if ((now - lastQueryTime).TotalMilliseconds < minQueryInterval)
                    {
                        _logger.LogDebug("重试查询间隔过短，跳过本次查询");
                        continue;
                    }

                    // 更新上次查询时间
                    lastQueryTime = now;

                    // 使用事务包装所有数据库操作，包括查询操作
                    await _sqlSugarClient.Ado.UseTranAsync(async () =>
                    {
                        // 获取所有待重试且到达重试时间的记录
                        var records = await _sqlSugarClient.Queryable<ForwardFailureRecord>()
                            .Where(x => x.Status == ForwardFailureRecordStatus.Pending && x.NextRetryTime <= now)
                            .Take(200) // 限制每次处理的记录数量
                            .ToListAsync(cancellationToken);

                        if (!records.Any())
                            return;

                        _logger.LogInformation("获取到待重试记录: Count={Count}", records.Count);

                        // 批量更新状态为重试中
                        var recordIds = records.Select(x => x.Id).ToList();
                        await _sqlSugarClient.Updateable<ForwardFailureRecord>()
                            .SetColumns(x => new ForwardFailureRecord { Status = ForwardFailureRecordStatus.Retrying })
                            .Where(x => recordIds.Contains(x.Id))
                            .ExecuteCommandAsync(cancellationToken);

                        // 按配置ID分组处理
                        var recordGroups = records.GroupBy(x => x.ForwardConfigId);
                        foreach (var group in recordGroups)
                        {
                            // 如果取消令牌被取消,则跳出循环
                            if (cancellationToken.IsCancellationRequested)
                                break;

                            if (!_forwardClients.TryGetValue(group.Key, out var client))
                            {
                                _logger.LogWarning("未找到转发客户端: {ConfigId}", group.Key);
                                continue;
                            }

                            // 成功记录
                            var successRecords = new List<ForwardFailureRecord>();
                            // 失败记录
                            var failedRecords = new List<ForwardFailureRecord>();

                            // 处理每个分组中的记录
                            foreach (var record in group)
                                try
                                {
                                    var startTime = DateTime.Now;
                                    var dataSize = System.Text.Encoding.UTF8.GetByteCount(record.Data);

                                    // 转发数据
                                    await client.ForwardAsync(record.Data, cancellationToken);

                                    // 计算延迟并记录统计
                                    var latency = (DateTime.Now - startTime).TotalMilliseconds;
                                    _statisticsService.RecordSuccess(group.Key, dataSize, latency);
                                    _statisticsService.RecordRetry(group.Key, dataSize);

                                    // 添加成功记录
                                    successRecords.Add(record);

                                    // 推送重试成功消息到WebSocket，让用户知道缓存数据发送成功
                                    try
                                    {
                                        var additionalInfo = $"重试第{record.RetryCount + 1}次成功";
                                        await client.PushRetrySuccessMessageAsync(record.Data, additionalInfo);
                                    }
                                    catch (Exception pushEx)
                                    {
                                        // 推送失败不影响主要流程，只记录警告日志
                                        _logger.LogWarning(pushEx, "推送重试成功消息失败: ConfigId={ConfigId}", group.Key);
                                    }
                                }
                                catch (Exception ex)
                                {
                                    var dataSize = System.Text.Encoding.UTF8.GetByteCount(record.Data);
                                    var errorType = ClassifyError(ex);

                                    // 记录重试失败统计
                                    _statisticsService.RecordFailure(group.Key, dataSize, errorType);

                                    // 增加重试次数
                                    record.RetryCount++;
                                    // 记录失败原因
                                    record.FailureReason = ExceptionHelper.FormatFailureReason(ex, "批量重试");
                                    // 添加失败记录
                                    failedRecords.Add(record);
                                }

                            // 批量处理所有记录（成功和失败）
                            var successRecordIds = new List<long>();
                            var allUpdates = new List<ForwardFailureRecord>();

                            // 收集成功记录的ID用于删除
                            if (successRecords.Any())
                            {
                                successRecordIds.AddRange(successRecords.Select(r => r.Id));

                                _logger.LogInformation("批量重试成功，准备删除记录: ConfigId={ConfigId}, Count={Count}",
                                    group.Key, successRecords.Count);
                            }

                            // 处理失败记录
                            if (failedRecords.Any())
                            {
                                // 获取重试策略
                                var policy = client.Config.RetryConfig;
                                if (policy == null)
                                {
                                    _logger.LogWarning("转发配置 {ConfigId} 未配置重试策略，使用默认策略", group.Key);
                                    continue;
                                }

                                var pendingRecords = new List<ForwardFailureRecord>();
                                // 处理每个失败记录
                                foreach (var record in failedRecords)
                                {
                                    // 计算下次重试时间（指数退避）
                                    var delay = Math.Min(
                                        policy.InitialDelay * Math.Pow(policy.BackoffMultiplier, record.RetryCount),
                                        policy.MaxDelay
                                    );
                                    // 设置记录状态为待重试
                                    record.Status = ForwardFailureRecordStatus.Pending;
                                    record.NextRetryTime = DateTime.Now.AddMilliseconds(delay);
                                    pendingRecords.Add(record);
                                }

                                // 添加待重试的记录
                                allUpdates.AddRange(pendingRecords.Select(r => new ForwardFailureRecord
                                {
                                    Id = r.Id,
                                    Status = ForwardFailureRecordStatus.Pending,
                                    RetryCount = r.RetryCount,
                                    NextRetryTime = r.NextRetryTime
                                }));

                                // 记录重试日志
                                if (pendingRecords.Any())
                                {
                                    _logger.LogInformation("批量更新重试记录: ConfigId={ConfigId}, Count={Count}",
                                        group.Key, pendingRecords.Count);
                                }
                            }

                            // 先删除成功记录
                            if (successRecordIds.Any())
                            {
                                // 记录内存统计
                                foreach (var record in successRecords)
                                {
                                    var dataSize = System.Text.Encoding.UTF8.GetByteCount(record.Data);
                                    _statisticsService.RecordCompletion(group.Key, dataSize);
                                }

                                await _sqlSugarClient.Deleteable<ForwardFailureRecord>()
                                    .Where(x => successRecordIds.Contains(x.Id))
                                    .ExecuteCommandAsync();
                                _logger.LogInformation("批量删除成功记录完成: ConfigId={ConfigId}, Count={Count}",
                                    group.Key, successRecordIds.Count);
                            }

                            // 然后更新失败记录
                            if (allUpdates.Any())
                            {
                                await _sqlSugarClient.Updateable(allUpdates).ExecuteCommandAsync();
                                _logger.LogInformation("批量更新失败记录完成: ConfigId={ConfigId}, Count={Count}",
                                    group.Key, allUpdates.Count);
                            }
                        }
                    });
                }
                catch (Exception ex)
                {
                    // 只有不是任务取消异常,才记录错误日志
                    if (!(ex is TaskCanceledException))
                        _logger.LogError(ex, "处理重试任务时发生错误");
                    await Task.Delay(1000, cancellationToken);
                }
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "重试处理任务异常");
        }
    }

    /// <summary>
    /// 带重试的转发
    /// </summary>
    /// <param name="configId">转发配置ID</param>
    /// <param name="dataList">原始数据列表</param>
    /// <param name="cancellationToken">取消令牌</param>
    /// <returns></returns>
    private async Task ForwardWithRetryAsync(long configId, List<string> dataList, CancellationToken cancellationToken)
    {
        // 获取转发客户端
        if (!_forwardClients.TryGetValue(configId, out var client))
        {
            _logger.LogWarning("未找到转发客户端: {ConfigId}", configId);
            return;
        }

        // 依次处理每个数据项
        foreach (var data in dataList)
        {
            try
            {
                var startTime = DateTime.Now;
                var dataSize = System.Text.Encoding.UTF8.GetByteCount(data);

                // 转发数据
                await client.ForwardAsync(data, cancellationToken);

                // 计算延迟并记录成功统计
                var latency = (DateTime.Now - startTime).TotalMilliseconds;
                _statisticsService.RecordSuccess(configId, dataSize, latency);

                _logger.LogDebug("转发成功: ConfigId={ConfigId}, Size={Size}, Latency={Latency}ms",
                    configId, dataSize, latency);
            }
            catch (Exception ex)
            {
                var dataSize = System.Text.Encoding.UTF8.GetByteCount(data);
                var errorType = ClassifyError(ex);

                // 记录转发失败统计
                _statisticsService.RecordFailure(configId, dataSize, errorType);

                // 获取重试策略
                var retryConfig = client.Config.RetryConfig;
                if (retryConfig == null)
                {
                    _logger.LogWarning("转发配置 {ConfigId} 未配置重试策略，使用默认策略", configId);
                    continue;
                }

                // 创建失败记录
                var record = new ForwardFailureRecord
                {
                    ForwardConfigId = configId, // 转发配置ID
                    Data = data, // 原始数据
                    FailureReason = ExceptionHelper.FormatFailureReasonWithTarget(ex, client.Config.Name, "数据转发"), // 失败原因
                    RetryCount = 0, // 重试次数
                    Status = ForwardFailureRecordStatus.Pending, // 状态
                    NextRetryTime = DateTime.Now.AddMilliseconds(retryConfig.InitialDelay) // 使用配置的初始延迟
                };

                // 添加到内存缓冲区，而不是直接写入数据库
                await AddToBufferAsync(record);

                // 记录失败
                _logger.LogWarning("转发失败，已加入重试队列: ConfigId={ConfigId}, Reason={Reason}, NextRetry={NextRetry}",
                    configId, ex.Message, record.NextRetryTime);
            }
        }
    }

    /// <summary>
    /// 添加记录到内存缓冲区
    /// </summary>
    /// <param name="record">失败记录</param>
    private async Task AddToBufferAsync(ForwardFailureRecord record)
    {
        await _bufferLock.WaitAsync();
        try
        {
            // 添加到缓冲区
            _failureRecordBuffer.Enqueue(record);

            // 如果缓冲区大小超过阈值，立即刷新
            if (_failureRecordBuffer.Count >= BufferThreshold)
            {
                await FlushBufferAsync();
            }
        }
        finally
        {
            _bufferLock.Release();
        }
    }

    /// <summary>
    /// 定期刷新缓冲区
    /// </summary>
    /// <param name="cancellationToken">取消令牌</param>
    /// <returns></returns>
    private async Task FlushBufferPeriodicAsync(CancellationToken cancellationToken)
    {
        while (!cancellationToken.IsCancellationRequested)
        {
            try
            {
                // 等待下一次刷新
                await _bufferFlushTimer.WaitForNextTickAsync(cancellationToken);

                // 刷新缓冲区
                await FlushBufferAsync();
            }
            catch (Exception ex)
            {
                // 只有不是任务取消异常，才记录错误日志
                if (!(ex is TaskCanceledException))
                {
                    _logger.LogError(ex, "刷新缓冲区时发生错误");
                }

                await Task.Delay(1000, cancellationToken);
            }
        }
    }

    /// <summary>
    /// 刷新缓冲区，将内存中的记录批量写入数据库
    /// </summary>
    private async Task FlushBufferAsync()
    {
        // 如果缓冲区为空，直接返回
        if (_failureRecordBuffer.IsEmpty)
        {
            return;
        }

        await _bufferLock.WaitAsync();
        try
        {
            // 从缓冲区中取出所有记录
            var records = new List<ForwardFailureRecord>();
            while (_failureRecordBuffer.TryDequeue(out var record))
            {
                records.Add(record);
            }

            // 如果没有记录，直接返回
            if (!records.Any())
            {
                return;
            }

            // todo 考虑后续表容量限制，例如限制100万条，超过后删除最早的记录

            // 批量插入记录
            await _sqlSugarClient.Insertable(records).ExecuteCommandAsync();

            _logger.LogInformation("批量写入失败记录: Count={Count}", records.Count);
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "批量写入失败记录时发生错误");
        }
        finally
        {
            _bufferLock.Release();
        }
    }

    /// <summary>
    ///     创建转发客户端
    /// </summary>
    /// <param name="config">转发配置</param>
    /// <returns>转发客户端</returns>
    private IForwardClient CreateForwardClient(ForwardConfig config)
    {
        try
        {
            return config.Type switch
            {
                ForwardTypeEnum.MQTT => new MqttForwardClient(config, _loggerFactory, _offlineStorage, _eventPublisher, _messagePushService),
                ForwardTypeEnum.HTTP => new HttpForwardClient(config),
                ForwardTypeEnum.WebSocket => new WebSocketForwardClient(config),
                _ => throw new ArgumentException($"不支持的转发类型: {config.Type}")
            };
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "创建转发客户端时发生错误: ConfigId={ConfigId}", config.Id);
            throw;
        }
    }

    /// <summary>
    ///     转换数据格式
    ///     通过数据处理管道对原始数据进行清洗、聚合等处理
    /// </summary>
    /// <param name="data">原始数据列表</param>
    /// <param name="config">转发配置信息</param>
    /// <returns>处理后的数据字符串</returns>
    /// <remarks>
    ///     处理流程：
    ///     1. 创建处理上下文，包含原始数据和配置信息
    ///     2. 通过数据处理管道执行所有注册的处理器
    ///     3. 如果处理失败，返回原始数据的JSON字符串作为降级处理
    /// </remarks>
    private async Task<List<string>> TransformData(List<PayLoad> data, ForwardConfig config)
    {
        try
        {
            // 默认结果列表
            List<string> result = new();
            switch (config.Type)
            {
                case ForwardTypeEnum.MQTT:
                    {
                        // 查找发布主题中用于实时数据的配置
                        var topicConfig = config.MqttCustomConfig.Topics.FirstOrDefault(t =>
                            t.Direction == TopicDirectionEnum.Publish &&
                            t.UseType == UseTypeEnum.RealTimeData);

                        if (topicConfig == null)
                        {
                            _logger.LogWarning("未找到实时数据处理脚本: ConfigId={ConfigId}", config.Id);
                            return data.Select(d => JSON.Serialize(d)).ToList();
                        }

                        // 获取脚本模板
                        string scriptTemplate = topicConfig.PayloadTemplate;
                        // 如果没有设置脚本模板,直接序列化返回
                        if (string.IsNullOrEmpty(scriptTemplate))
                        {
                            return data.Select(d => JSON.Serialize(d)).ToList();
                        }

                        // 对每个数据项应用脚本处理
                        foreach (var item in data)
                        {
                            try
                            {
                                // 使用脚本引擎处理数据
                                var scriptResult = await _scriptEnginePool.ExecuteScriptAsync(
                                    scriptKey: $"forward-transform-{config.Id}-{topicConfig.Topic}",
                                    script: scriptTemplate,
                                    variables: new Dictionary<string, object>
                                    {
                                        { "data", item },
                                        { "config", config }
                                    });

                                // 如果结果是字符串,直接添加
                                if (scriptResult is string stringResult)
                                {
                                    result.Add(stringResult);
                                }
                                // 否则序列化为JSON
                                else
                                {
                                    result.Add(JSON.Serialize(scriptResult));
                                }
                            }
                            catch (Exception ex)
                            {
                                _logger.LogError(ex, "执行脚本处理失败: ConfigId={ConfigId}, Topic={Topic}",
                                    config.Id, topicConfig.Topic);
                                // 脚本执行失败时，添加原始序列化数据作为降级处理
                                result.Add(JSON.Serialize(item));
                            }
                        }

                        return result;
                    }
                case ForwardTypeEnum.HTTP:
                    {
                        // HTTP转发处理逻辑...
                        // 暂时没有针对HTTP的脚本处理,直接序列化返回
                        return data.Select(d => JSON.Serialize(d)).ToList();
                    }
                case ForwardTypeEnum.WebSocket:
                    {
                        // WebSocket转发处理逻辑...
                        // 暂时没有针对WebSocket的脚本处理,直接序列化返回
                        return data.Select(d => JSON.Serialize(d)).ToList();
                    }
                default:
                    {
                        _logger.LogWarning("不支持的转发类型: {Type}", config.Type);
                        return data.Select(d => JSON.Serialize(d)).ToList();
                    }
            }
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "转换数据格式时发生错误");
            // 发生异常时,返回原始数据
            return data.Select(d => JSON.Serialize(d)).ToList();
        }
    }

    /// <summary>
    /// 添加或更新转发配置
    /// </summary>
    /// <param name="config"></param>
    /// <returns></returns>
    /// <exception cref="Exception"></exception>
    public async Task UpdateForwardConfigAsync(ForwardConfig config)
    {
        try
        {
            var threadId = Thread.CurrentThread.ManagedThreadId;
            _logger.LogInformation("[线程ID:{ThreadId}] 开始更新转发配置: ConfigId={ConfigId}, Type={Type}, Enable={Enable}",
                threadId, config.Id, config.Type, config.Enable);

            // 使用锁确保线程安全
            lock (_forwardClients)
            {
                _logger.LogInformation("[线程ID:{ThreadId}] 获取到锁，准备处理现有客户端: ConfigId={ConfigId}", threadId, config.Id);

                // 如果已存在该配置的客户端，先断开连接并移除
                if (_forwardClients.TryRemove(config.Id, out var existingClient))
                {
                    _logger.LogInformation("[线程ID:{ThreadId}] 找到现有客户端，准备断开连接: ConfigId={ConfigId}, 客户端类型={ClientType}, 当前连接状态={IsConnected}",
                        threadId, config.Id, existingClient.GetType().Name, existingClient.IsConnected);

                    try
                    {
                        // 记录断开连接前的状态
                        _logger.LogInformation("[线程ID:{ThreadId}] 断开连接前状态: ConfigId={ConfigId}, IsConnected={IsConnected}, LastActivityTime={LastActivityTime}",
                            threadId, config.Id, existingClient.IsConnected, existingClient.LastActivityTime);

                        // 确保同步等待断开连接完成
                        var disconnectTask = existingClient.DisconnectAsync();
                        _logger.LogInformation("[线程ID:{ThreadId}] 开始等待断开连接任务完成: ConfigId={ConfigId}", threadId, config.Id);

                        // 设置超时，避免无限等待
                        var timeoutTask = Task.Delay(5000); // 5秒超时
                        var completedTask = Task.WhenAny(disconnectTask, timeoutTask);

                        if (completedTask == timeoutTask)
                        {
                            _logger.LogWarning("[线程ID:{ThreadId}] 断开连接任务超时: ConfigId={ConfigId}", threadId, config.Id);
                        }
                        else
                        {
                            _logger.LogInformation("[线程ID:{ThreadId}] 断开连接任务已完成: ConfigId={ConfigId}", threadId, config.Id);
                        }

                        // 等待一小段时间确保所有资源都被正确释放
                        _logger.LogInformation("[线程ID:{ThreadId}] 等待资源释放: ConfigId={ConfigId}", threadId, config.Id);
                        Thread.Sleep(100);

                        // 释放资源前记录状态
                        _logger.LogInformation("[线程ID:{ThreadId}] 准备释放资源: ConfigId={ConfigId}, 客户端引用状态={ClientState}",
                            threadId, config.Id, existingClient == null ? "null" : "有效");

                        // 释放资源
                        existingClient?.Dispose();
                        _logger.LogInformation("[线程ID:{ThreadId}] 已调用Dispose方法: ConfigId={ConfigId}", threadId, config.Id);

                        // 强制GC回收
                        _logger.LogInformation("[线程ID:{ThreadId}] 开始强制GC回收: ConfigId={ConfigId}", threadId, config.Id);
                        GC.Collect();
                        GC.WaitForPendingFinalizers();
                        _logger.LogInformation("[线程ID:{ThreadId}] 完成GC回收: ConfigId={ConfigId}", threadId, config.Id);

                        _logger.LogInformation("[线程ID:{ThreadId}] 已断开并释放转发配置: ConfigId={ConfigId}", threadId, config.Id);
                    }
                    catch (Exception ex)
                    {
                        _logger.LogError(ex, "[线程ID:{ThreadId}] 断开连接时发生错误: ConfigId={ConfigId}, 错误类型={ExceptionType}",
                            threadId, config.Id, ex.GetType().Name);
                    }
                }
                else
                {
                    _logger.LogInformation("[线程ID:{ThreadId}] 未找到现有客户端: ConfigId={ConfigId}", threadId, config.Id);
                }
            }

            _logger.LogInformation("[线程ID:{ThreadId}] 锁已释放，准备处理新客户端: ConfigId={ConfigId}, Enable={Enable}",
                threadId, config.Id, config.Enable);

            // 更新配置缓存
            _forwardConfigCache[config.Id] = config;
            _logger.LogInformation("[线程ID:{ThreadId}] 已更新配置缓存: ConfigId={ConfigId}", threadId, config.Id);

            // 如果配置已启用，创建并启动新的客户端
            if (config.Enable)
            {
                _logger.LogInformation("[线程ID:{ThreadId}] 配置已启用，准备创建新客户端: ConfigId={ConfigId}", threadId, config.Id);

                // 创建转发客户端
                var client = CreateForwardClient(config);
                _logger.LogInformation("[线程ID:{ThreadId}] 已创建新客户端: ConfigId={ConfigId}, 客户端类型={ClientType}",
                    threadId, config.Id, client.GetType().Name);

                // 添加到转发客户端列表前确保连接成功
                _logger.LogInformation("[线程ID:{ThreadId}] 开始连接: ConfigId={ConfigId}", threadId, config.Id);
                try
                {
                    await client.ConnectAsync();
                    _logger.LogInformation("[线程ID:{ThreadId}] 连接成功: ConfigId={ConfigId}, IsConnected={IsConnected}",
                        threadId, config.Id, client.IsConnected);
                }
                catch (Exception ex)
                {
                    _logger.LogError(ex, "[线程ID:{ThreadId}] 连接失败: ConfigId={ConfigId}, 错误类型={ExceptionType}",
                        threadId, config.Id, ex.GetType().Name);

                    // 确保释放资源
                    try
                    {
                        client.Dispose();
                        _logger.LogInformation("[线程ID:{ThreadId}] 连接失败后已释放资源: ConfigId={ConfigId}", threadId, config.Id);
                    }
                    catch (Exception disposeEx)
                    {
                        _logger.LogError(disposeEx, "[线程ID:{ThreadId}] 连接失败后释放资源时出错: ConfigId={ConfigId}",
                            threadId, config.Id);
                    }
                    throw;
                }

                // 连接成功后再添加到字典
                _logger.LogInformation("[线程ID:{ThreadId}] 尝试添加客户端到字典: ConfigId={ConfigId}", threadId, config.Id);
                if (!_forwardClients.TryAdd(config.Id, client))
                {
                    _logger.LogWarning("[线程ID:{ThreadId}] 添加客户端到字典失败: ConfigId={ConfigId}", threadId, config.Id);

                    // 如果添加失败，确保释放资源
                    try
                    {
                        await client.DisconnectAsync();
                        client.Dispose();
                        _logger.LogInformation("[线程ID:{ThreadId}] 添加失败后已释放资源: ConfigId={ConfigId}", threadId, config.Id);
                    }
                    catch (Exception disposeEx)
                    {
                        _logger.LogError(disposeEx, "[线程ID:{ThreadId}] 添加失败后释放资源时出错: ConfigId={ConfigId}",
                            threadId, config.Id);
                    }
                    throw new Exception($"无法添加转发客户端到集合中: ConfigId={config.Id}");
                }

                // 发布转发连接状态变化事件
                try
                {
                    var connectionEvent = new ForwardConnectionChangedEvent
                    {
                        ForwardId = config.Id,
                        ForwardName = config.Name,
                        IsConnected = client.IsConnected,
                        EventTime = DateTime.Now,
                        ForwardType = ForwardTypeEnum.MQTT,
                        ErrorMessage = $"转发配置 [{config.Name}] 连接状态变更为 {(client.IsConnected ? "已连接" : "已断开")}",
                    };
                    await _eventPublisher.PublishAsync("forward_connection_changed", connectionEvent);
                    _logger.LogDebug("已发布转发连接状态变化事件: ConfigId={ConfigId}, Status={Status}",
                        config.Id, client.IsConnected);
                }
                catch (Exception eventEx)
                {
                    _logger.LogWarning(eventEx, "发布转发连接状态变化事件失败: ConfigId={ConfigId}", config.Id);
                }

                // 注册配置信息到统计服务
                _statisticsService.RegisterForwardConfig(config.Id, config.Name, config.Type.ToString());
                _logger.LogInformation("[线程ID:{ThreadId}] 已注册配置到统计服务: ConfigId={ConfigId}, Name={Name}, Type={Type}",
                    threadId, config.Id, config.Name, config.Type);

                _logger.LogInformation("[线程ID:{ThreadId}] 已更新转发配置并重新连接: ConfigId={ConfigId}, Type={Type}",
                    threadId, config.Id, config.Type);
            }
            else
            {
                _logger.LogInformation("[线程ID:{ThreadId}] 转发配置已禁用: ConfigId={ConfigId}", threadId, config.Id);
            }
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "[线程ID:{0}] 更新转发配置时发生错误: ConfigId={1}, 错误类型={2}",
                Thread.CurrentThread.ManagedThreadId, config.Id, ex.GetType().Name);
            throw;
        }
    }

    /// <summary>
    /// 删除转发配置
    /// </summary>
    /// <param name="configId">配置ID</param>
    /// <returns></returns>
    public async Task RemoveForwardConfigAsync(long configId)
    {
        try
        {
            // 从缓存中移除配置
            _forwardConfigCache.TryRemove(configId, out _);
            _logger.LogInformation("已从缓存中移除转发配置: ConfigId={ConfigId}", configId);

            // 从统计服务注销配置信息
            _statisticsService.UnregisterForwardConfig(configId);
            _logger.LogInformation("已从统计服务注销配置: ConfigId={ConfigId}", configId);

            // 如果存在该配置的客户端，断开连接并移除
            if (_forwardClients.TryRemove(configId, out var client))
                try
                {
                    // 断开连接
                    await client.DisconnectAsync();

                    // 发布转发连接状态变化事件
                    try
                    {
                        var connectionEvent = new ForwardConnectionChangedEvent
                        {
                            ForwardId = configId,
                            ForwardName = client.Config.Name,
                            IsConnected = false,
                            EventTime = DateTime.Now,
                            ErrorMessage = $"转发配置 [{client.Config.Name}] 已断开连接"
                        };
                        await _eventPublisher.PublishAsync("forward_connection_changed", connectionEvent);
                        _logger.LogDebug("已发布转发连接断开事件: ConfigId={ConfigId}", configId);
                    }
                    catch (Exception eventEx)
                    {
                        _logger.LogWarning(eventEx, "发布转发连接断开事件失败: ConfigId={ConfigId}", configId);
                    }

                    // 释放资源
                    client.Dispose();
                    _logger.LogInformation("已移除并释放转发配置: ConfigId={ConfigId}", configId);
                }
                catch (Exception ex)
                {
                    _logger.LogError(ex, "断开连接时发生错误: ConfigId={ConfigId}", configId);
                }
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "移除转发配置时发生错误: ConfigId={ConfigId}", configId);
        }
    }

    /// <summary>
    ///     获取转发客户端状态
    /// </summary>
    /// <param name="configId">配置ID</param>
    /// <returns></returns>
    public bool GetForwardClientStatus(long configId)
    {
        return _forwardClients.TryGetValue(configId, out var client) && client.IsConnected;
    }

    /// <summary>
    ///     获取转发客户端最后活动时间
    /// </summary>
    /// <param name="configId">配置ID</param>
    /// <returns></returns>
    public DateTime GetForwardClientLastActivityTime(long configId)
    {
        return _forwardClients.TryGetValue(configId, out var client) ? client.LastActivityTime : DateTime.MinValue;
    }

    /// <summary>
    ///     重新加载所有转发配置
    /// </summary>
    /// <returns></returns>
    public async Task ReloadForwardConfigsAsync()
    {
        try
        {
            // 断开所有现有连接并释放资源
            foreach (var client in _forwardClients.Values)
                try
                {
                    await client.DisconnectAsync();
                    client.Dispose();
                }
                catch (Exception ex)
                {
                    _logger.LogError(ex, "断开连接时发生错误");
                }

            _forwardClients.Clear();

            // 重新加载配置
            var configs = await LoadForwardConfigs();

            // 初始化所有转发客户端
            foreach (var config in configs.Where(c => c.Enable))
            {
                var client = CreateForwardClient(config);
                _forwardClients.TryAdd(config.Id, client);
                await client.ConnectAsync();

                // 注册配置信息到统计服务
                _statisticsService.RegisterForwardConfig(config.Id, config.Name, config.Type.ToString());
                _logger.LogDebug("已注册配置到统计服务: ConfigId={ConfigId}, Name={Name}, Type={Type}",
                    config.Id, config.Name, config.Type);
            }

            _logger.LogInformation("已重新加载所有转发配置");
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "重新加载转发配置时发生错误");
            throw;
        }
    }

    /// <summary>
    ///     从数据库加载转发配置
    /// </summary>
    /// <returns></returns>
    private async Task<List<ForwardConfig>> LoadForwardConfigs()
    {
        try
        {
            // 从数据库查询所有启用的转发配置
            var configs = await _sqlSugarClient.Queryable<ForwardConfig>()
                .Where(x => x.Enable)
                .ToListAsync();

            // 更新配置缓存
            _forwardConfigCache.Clear(); // 清除现有缓存
            foreach (var config in configs)
            {
                _forwardConfigCache[config.Id] = config;
            }

            _logger.LogInformation("已加载转发配置并更新缓存: ConfigCount={ConfigCount}", configs.Count);
            return configs;
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "加载转发配置时发生错误");
            throw;
        }
    }

    /// <summary>
    ///     获取转发客户端连接记录
    /// </summary>
    /// <param name="configId">配置ID</param>
    /// <returns>连接记录列表</returns>
    public IEnumerable<ConnectionRecord> GetForwardClientConnectionRecords(long configId)
    {
        if (_forwardClients.TryGetValue(configId, out var client)) return client.GetConnectionRecords();
        return Enumerable.Empty<ConnectionRecord>();
    }

    /// <summary>
    ///     定时器，用于定时更新转发统计信息
    /// </summary>
    private readonly PeriodicTimer _statisticsTimer;

    /// <summary>
    ///     定时更新转发统计信息
    /// </summary>
    private async Task UpdateForwardStatisticsAsync(CancellationToken cancellationToken)
    {
        try
        {
            while (await _statisticsTimer.WaitForNextTickAsync(cancellationToken))
                await GetForwardStatisticsSummaryAsync();
        }
        catch (Exception ex)
        {
            // 只有不是任务取消异常，才记录错误日志
            if (!(ex is TaskCanceledException))
                _logger.LogError(ex, "更新转发统计信息失败");
        }
    }

    /// <summary>
    ///     获取所有转发客户端的统计汇总
    /// </summary>
    /// <returns>统计汇总信息</returns>
    private async Task GetForwardStatisticsSummaryAsync()
    {
        foreach (var clientKvp in _forwardClients)
        {
            try
            {
                // 优先从缓存中获取配置，不存在时才从数据库读取
                ForwardConfig config;
                if (!_forwardConfigCache.TryGetValue(clientKvp.Key, out config))
                {
                    // 缓存未命中，从数据库查询
                    config = await _sqlSugarClient.Queryable<ForwardConfig>()
                        .Where(x => x.Id == clientKvp.Key)
                        .FirstAsync();

                    // 如果查询到配置，则更新缓存
                    if (config != null)
                    {
                        _forwardConfigCache[clientKvp.Key] = config;
                        _logger.LogDebug("缓存未命中，从数据库获取配置并更新缓存: ConfigId={ConfigId}", clientKvp.Key);
                    }
                }

                if (config == null)
                {
                    _logger.LogWarning("未找到转发配置: ConfigId={ConfigId}", clientKvp.Key);
                    continue;
                }

                // 从客户端获取统计数据，这不需要数据库操作
                var (offlineCount, sendCount, failCount, receiveCount) = clientKvp.Value.GetOfflineAndSendCount();

                // 更新系统统计数据，这也不需要数据库操作
                _systemStatisticsService.UpdateStatistics(offlineCount, sendCount, receiveCount, failCount);

                _logger.LogDebug("已更新统计信息: ConfigId={ConfigId}, OfflineCount={OfflineCount}, SendCount={SendCount}, FailCount={FailCount}, ReceiveCount={ReceiveCount}",
                    clientKvp.Key, offlineCount, sendCount, failCount, receiveCount);
            }
            catch (Exception ex)
            {
                // 更详细的错误日志
                _logger.LogError(ex, "获取客户端统计信息失败: ConfigId={ConfigId}, ErrorType={ErrorType}",
                    clientKvp.Key, ex.GetType().Name);
                continue;
            }
        }
    }

    /// <summary>
    /// 优化SQLite配置，提高低配硬件上的性能
    /// </summary>
    private void OptimizeSqliteConfiguration()
    {
        try
        {
            // 获取SQLite连接
            var connection = _sqlSugarClient.Ado.Connection;
            if (connection.State != System.Data.ConnectionState.Open)
            {
                connection.Open();
            }

            // 创建命令
            using var command = connection.CreateCommand();

            // 设置日志模式为WAL (Write-Ahead Logging)，提高写入性能
            command.CommandText = "PRAGMA journal_mode=WAL;";
            command.ExecuteNonQuery();

            // 设置同步模式为NORMAL，在保证数据安全的同时提高性能
            command.CommandText = "PRAGMA synchronous=NORMAL;";
            command.ExecuteNonQuery();

            // 设置缓存大小（单位：KB），根据硬件情况调整
            command.CommandText = "PRAGMA cache_size=-4096;"; // 4MB缓存
            command.ExecuteNonQuery();

            // 设置临时存储位置为内存，减少磁盘I/O
            command.CommandText = "PRAGMA temp_store=MEMORY;";
            command.ExecuteNonQuery();

            // 设置页大小，通常4KB是较好的选择
            command.CommandText = "PRAGMA page_size=4096;";
            command.ExecuteNonQuery();

            // 启用内存映射，提高读取性能
            command.CommandText = "PRAGMA mmap_size=67108864;"; // 64MB
            command.ExecuteNonQuery();

            _logger.LogInformation("SQLite配置已优化，提高低配硬件上的性能");
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "优化SQLite配置时发生错误");
        }
    }

    /// <summary>
    /// 分类错误类型
    /// </summary>
    /// <param name="exception">异常对象</param>
    /// <returns>错误类型</returns>
    private ForwardErrorType ClassifyError(Exception exception)
    {
        return exception switch
        {
            TimeoutException => ForwardErrorType.ConnectionTimeout,
            SocketException => ForwardErrorType.NetworkError,
            HttpRequestException => ForwardErrorType.NetworkError,
            UnauthorizedAccessException => ForwardErrorType.AuthenticationFailed,
            SecurityException => ForwardErrorType.CertificateError,
            ArgumentException => ForwardErrorType.DataFormatError,
            InvalidOperationException => ForwardErrorType.ConfigurationError,
            NotSupportedException => ForwardErrorType.ConfigurationError,
            _ => ForwardErrorType.Other
        };
    }
}