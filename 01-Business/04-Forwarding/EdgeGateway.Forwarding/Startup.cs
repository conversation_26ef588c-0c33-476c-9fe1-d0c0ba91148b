using System.Diagnostics;
using Furion;
using EdgeGateway.Engine.EngineActions;
using Microsoft.Extensions.DependencyInjection.Extensions;
using EdgeGateway.Forwarding.Statistics.Services;
using EdgeGateway.Forwarding.Statistics.Models;

namespace EdgeGateway.Forwarding;

/// <summary>
///     自定义启动类
/// </summary>
public class Startup : AppStartup
{
    /// <summary>
    /// </summary>
    /// <param name="services"></param>
    public void ConfigureServices(IServiceCollection services)
    {
        var sw = Stopwatch.StartNew();

        // // 注册处理后数据队列
        // services.AddSingleton<QueueService<ProcessedPayLoad>>();

        // 注册统计配置选项
        services.Configure<ForwardStatisticsOptions>(options =>
        {
            options.Enabled = true;
            options.PushInterval = 1; // 1秒推送间隔
            options.TimeWindowMinutes = 60; // 60分钟时间窗口
            options.RetentionDays = 7; // 保留7天数据
            options.LatencySampleSize = 1000; // 延迟样本数量
            options.EnableDetailedErrorStats = true; // 启用详细错误统计
            options.PushTopicPrefix = "forward_statistics"; // WebSocket推送主题前缀
        });

        // 注册统计服务组件
        services.AddSingleton<ForwardStatisticsCollector>();
        services.AddSingleton<ForwardStatisticsAggregator>();
        services.AddSingleton<IForwardStatisticsService, ForwardStatisticsService>();

        // 将统计服务注册为后台服务
        services.AddHostedService<ForwardStatisticsService>();

        // 注册数据转发服务
        services.AddSingleton<DataForwardHostService>();
        services.AddHostedService(provider => provider.GetRequiredService<DataForwardHostService>());

        // 注册转发客户端工厂
        services.AddTransient<IForwardClientFactory, ForwardClientFactory>();

        // 注册离线存储服务
        services.AddSingleton<IOfflineStorageService, SqliteOfflineStorage>();

        sw.Stop();
        var logger = services.BuildServiceProvider().GetRequiredService<ILogger<Startup>>();
        logger.LogInformation("╔════════════════════════════════════════════════════════════════");
        logger.LogInformation("║ 转发服务注入完成，耗时: {ElapsedMilliseconds}ms", sw.ElapsedMilliseconds);
        logger.LogInformation("╚════════════════════════════════════════════════════════════════");
    }
}