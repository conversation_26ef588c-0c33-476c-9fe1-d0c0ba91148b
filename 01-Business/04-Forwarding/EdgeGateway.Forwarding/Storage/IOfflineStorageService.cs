namespace EdgeGateway.Forwarding.Storage;

/// <summary>
///     离线消息存储服务
/// </summary>
public interface IOfflineStorageService
{
    /// <summary>
    ///     批量保存离线消息
    /// </summary>
    /// <param name="messages">离线消息</param>
    /// <returns></returns>
    Task SaveMessagesAsync(IEnumerable<ForwardFailureRecord> messages);

    /// <summary>
    ///     获取待处理的消息
    /// </summary>
    /// <param name="batchSize">批量大小</param>
    /// <returns></returns>
    Task<List<ForwardFailureRecord>> GetPendingMessagesAsync(int batchSize);

    /// <summary>
    ///     更新离线消息状态
    /// </summary>
    /// <param name="messages">离线消息</param>
    /// <returns></returns>
    Task UpdateMessagesAsync(IEnumerable<ForwardFailureRecord> messages);

    /// <summary>
    ///     获取待处理的消息数量
    /// </summary>
    /// <returns></returns>
    Task<long> GetPendingMessageCountAsync();

    /// <summary>
    ///     判断是否有待处理的消息
    /// </summary>
    /// <returns></returns>
    Task<bool> HasPendingMessagesAsync();

    /// <summary>
    ///     删除离线消息
    /// </summary>
    /// <param name="messageIds">消息Id</param>
    /// <returns></returns>
    Task DeleteMessagesAsync(IEnumerable<long> messageIds);

    /// <summary>
    ///     按转发配置ID获取待处理的消息数量
    /// </summary>
    /// <param name="forwardConfigId">转发配置ID</param>
    /// <returns>待处理消息数量</returns>
    Task<long> GetPendingMessageCountByConfigIdAsync(long forwardConfigId);
}