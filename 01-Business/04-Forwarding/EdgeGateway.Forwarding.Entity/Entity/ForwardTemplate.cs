namespace EdgeGateway.Forwarding.Entity;

/// <summary>
/// 转发模板
/// </summary>
[SugarTable("forward_template", "转发模板")]
public class ForwardTemplate : EntityBaseId
{
  /// <summary>
  /// 模板名称
  /// </summary>
  [SugarColumn(ColumnDescription = "模板名称", Length = 64)]
  public string Name { get; set; }

  /// <summary>
  /// 模板描述
  /// </summary>
  [SugarColumn(ColumnDescription = "模板描述", Length = 256)]
  public string Description { get; set; }

  /// <summary>
  /// 模板类型
  /// </summary>
  [SugarColumn(ColumnDescription = "模板类型")]
  public TemplateTypeEnum Type { get; set; }

  /// <summary>
  /// 转发类型
  /// </summary>
  [SugarColumn(ColumnDescription = "转发类型")]
  public ForwardTypeEnum ForwardType { get; set; }

  /// <summary>
  /// 模板配置
  /// </summary>
  [SugarColumn(ColumnDescription = "模板配置", ColumnDataType = "longtext,text,clob", IsJson = true)]
  public ForwardConfig Config { get; set; }

  /// <summary>
  /// 是否内置
  /// </summary>
  [SugarColumn(ColumnDescription = "是否内置")]
  public bool IsBuiltIn { get; set; }

  /// <summary>
  /// 排序
  /// </summary>
  [SugarColumn(ColumnDescription = "排序")]
  public int Sort { get; set; }
}

/// <summary>
/// 模板类型
/// </summary>
public enum TemplateTypeEnum
{
  /// <summary>
  /// 阿里云IoT
  /// </summary>
  AliIoT,

  /// <summary>
  /// 华为云IoT
  /// </summary>
  HuaweiIoT,

  /// <summary>
  /// 腾讯云IoT
  /// </summary>
  TencentIoT,

  /// <summary>
  /// AWS IoT
  /// </summary>
  AwsIoT,

  /// <summary>
  /// Azure IoT
  /// </summary>
  AzureIoT,

  /// <summary>
  /// EMQX
  /// </summary>
  EMQX,

  /// <summary>
  /// ThingsBoard
  /// </summary>
  ThingsBoard,

  /// <summary>
  /// 自定义
  /// </summary>
  Custom
}