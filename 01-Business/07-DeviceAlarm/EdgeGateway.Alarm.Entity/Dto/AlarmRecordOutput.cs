namespace EdgeGateway.Alarm.Entity.Dto;

/// <summary>
///     报警记录输出DTO
/// </summary>
public class AlarmRecordOutput
{
  /// <summary>
  ///     ID
  /// </summary>
  public long Id { get; set; }

  /// <summary>
  ///     报警规则名称
  /// </summary>
  public string AlarmRuleName { get; set; }

  /// <summary>
  ///     设备ID
  /// </summary>
  public string DeviceId { get; set; }

  /// <summary>
  ///     设备名称
  /// </summary>
  public string DeviceName { get; set; }

  /// <summary>
  ///     触发时间
  /// </summary>
  public DateTime TriggerTime { get; set; }

  /// <summary>
  ///     解除时间
  /// </summary>
  public DateTime? ResolveTime { get; set; }

  /// <summary>
  ///     确认时间
  /// </summary>
  public DateTime? AcknowledgeTime { get; set; }

  /// <summary>
  ///     确认用户
  /// </summary>
  public string AcknowledgeUser { get; set; }

  /// <summary>
  ///     报警级别
  /// </summary>
  public int Severity { get; set; }

  /// <summary>
  ///     报警消息
  /// </summary>
  public string Message { get; set; }

  /// <summary>
  ///     当前状态
  /// </summary>
  public string State { get; set; }

  /// <summary>
  ///     触发值
  /// </summary>
  public Dictionary<string, object> TriggerValues { get; set; }

  /// <summary>
  ///     持续时间（分钟）
  /// </summary>
  public double DurationMinutes => (ResolveTime.HasValue ? ResolveTime.Value : DateTime.Now) > TriggerTime
      ? (ResolveTime.HasValue ? (ResolveTime.Value - TriggerTime).TotalMinutes : (DateTime.Now - TriggerTime).TotalMinutes)
      : 0;
}