namespace EdgeGateway.Alarm.Entity.Dto;

/// <summary>
///     报警规则统计输出
/// </summary>
public class AlarmRuleStatisticsOutput
{
  /// <summary>
  ///     按规则ID分组的报警数量
  /// </summary>
  public Dictionary<string, int> AlarmsByRule { get; set; } = new();

  /// <summary>
  ///     按规则名称分组的报警数量
  /// </summary>
  public Dictionary<string, int> AlarmsByRuleName { get; set; } = new();

  /// <summary>
  ///     按设备ID分组的报警数量
  /// </summary>
  public Dictionary<string, int> AlarmsByDevice { get; set; } = new();

  /// <summary>
  ///     按严重程度分组的报警数量
  /// </summary>
  public Dictionary<int, int> AlarmsBySeverity { get; set; } = new();

  /// <summary>
  ///     开始时间
  /// </summary>
  public DateTime StartTime { get; set; }

  /// <summary>
  ///     结束时间
  /// </summary>
  public DateTime EndTime { get; set; }
}