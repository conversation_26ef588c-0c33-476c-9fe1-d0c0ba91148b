using System.ComponentModel;

namespace EdgeGateway.Alarm.Entity.Enum;

/// <summary>
///     报警严重性枚举
/// </summary>
public enum AlarmSeverityEnum
{
  /// <summary>
  ///     信息
  /// </summary>
  [Description("信息")]
  Information = 1,

  /// <summary>
  ///     警告
  /// </summary>
  [Description("警告")]
  Warning = 2,

  /// <summary>
  ///     一般
  /// </summary>
  [Description("一般")]
  Average = 3,

  /// <summary>
  ///     严重
  /// </summary>
  [Description("严重")]
  High = 4,

  /// <summary>
  ///     灾难
  /// </summary>
  [Description("灾难")]
  Disaster = 5
}