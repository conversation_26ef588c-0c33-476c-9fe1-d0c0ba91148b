namespace EdgeGateway.Alarm.Entity;

/// <summary>
///     报警记录实体
/// </summary>
[SugarTable("alarm_record_{year}{month}{day}", "报警记录")]
[SplitTable(SplitType.Day)] // 按天分表
public class AlarmRecord : EntityBaseId
{
  /// <summary>
  ///     报警规则ID
  /// </summary>
  [SugarColumn(ColumnDescription = "报警规则ID", Length = 64)]
    public long AlarmRuleId { get; set; }

  /// <summary>
  ///     报警规则名称
  /// </summary>
  [SugarColumn(ColumnDescription = "报警规则名称", Length = 128)]
    public string AlarmRuleName { get; set; }

  /// <summary>
  ///     设备ID
  /// </summary>
  [SugarColumn(ColumnDescription = "设备ID", Length = 64)]
    public string DeviceId { get; set; }

  /// <summary>
  ///     设备名称
  /// </summary>
  [SugarColumn(ColumnDescription = "设备名称", Length = 128)]
    public string DeviceName { get; set; }

  /// <summary>
  ///     触发时间
  /// </summary>
  [SugarColumn(ColumnDescription = "触发时间")]
    [SplitField]
    public DateTime TriggerTime { get; set; }

  /// <summary>
  ///     解除时间
  /// </summary>
  [SugarColumn(ColumnDescription = "解除时间", IsNullable = true)]
    public DateTime? ResolveTime { get; set; }

  /// <summary>
  ///     确认时间
  /// </summary>
  [SugarColumn(ColumnDescription = "确认时间", IsNullable = true)]
    public DateTime? AcknowledgeTime { get; set; }

  /// <summary>
  ///     确认用户
  /// </summary>
  [SugarColumn(ColumnDescription = "确认用户", Length = 64, IsNullable = true)]
    public string AcknowledgeUser { get; set; }

  /// <summary>
  ///     报警级别
  /// </summary>
  [SugarColumn(ColumnDescription = "报警级别", Length = 4)]
    public int Severity { get; set; }

  /// <summary>
  ///     报警消息
  /// </summary>
  [SugarColumn(ColumnDescription = "报警消息", ColumnDataType = "longtext,text,clob")]
    public string Message { get; set; }

  /// <summary>
  ///     触发值
  /// </summary>
  [SugarColumn(ColumnDescription = "触发值", IsJson = true, ColumnDataType = "longtext,text,clob")]
    public Dictionary<string, object> TriggerValues { get; set; } = new();

  /// <summary>
  ///     当前状态
  /// </summary>
  [SugarColumn(ColumnDescription = "当前状态", Length = 20)]
    public string State { get; set; } = "Active"; // Active, Resolved, Acknowledged
}