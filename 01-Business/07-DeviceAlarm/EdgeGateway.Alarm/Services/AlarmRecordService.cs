using System.Linq.Expressions;
using EdgeGateway.Alarm.Entity;
using EdgeGateway.Alarm.Entity.Dto;
using EdgeGateway.Alarm.Entity.Enum;
using EdgeGateway.SqlSugar.SqlSugar;
using Furion.DependencyInjection;
using Furion.FriendlyException;
using Microsoft.Extensions.Logging;

namespace EdgeGateway.Alarm.Services;

/// <summary>
///     报警记录服务实现
/// </summary>
public class AlarmRecordService : IAlarmRecordService, ITransient
{
    private readonly SqlSugarRepository<AlarmRecord> _repository;
    private readonly ILogger<AlarmRecordService> _logger;

    /// <summary>
    ///     构造函数
    /// </summary>
    /// <param name="repository">报警记录仓储</param>
    /// <param name="logger">日志记录器</param>
    public AlarmRecordService(SqlSugarRepository<AlarmRecord> repository, ILogger<AlarmRecordService> logger)
    {
        _repository = repository;
        _logger = logger;
    }

    /// <summary>
    ///     创建报警记录
    /// </summary>
    /// <param name="record">报警记录</param>
    /// <returns>报警记录ID</returns>
    public async Task<long> CreateAsync(AlarmRecord record)
    {
        // 使用分表功能插入数据
        await _repository.SplitTableInsertAsync(record);

        _logger.LogInformation("创建报警记录成功: {AlarmRuleName}, 设备: {DeviceId}, ID: {RecordId}",
            record.AlarmRuleName, record.DeviceId, record.Id);

        return record.Id;
    }

    /// <summary>
    ///     更新报警记录状态
    /// </summary>
    /// <param name="id">记录ID</param>
    /// <param name="state">状态</param>
    /// <param name="user">用户，可选</param>
    /// <returns>是否成功</returns>
    public async Task<bool> UpdateStateAsync(long id, string state, string user = null)
    {
        var record = await _repository.SplitTableGetFirstAsync(r => r.Id == id);
        if (record == null)
            throw Oops.Oh("报警记录不存在");

        record.State = state;

        if (state == AlarmStateEnum.Acknowledged.ToString())
        {
            record.AcknowledgeTime = DateTime.Now;
            record.AcknowledgeUser = user;
        }
        else if (state == AlarmStateEnum.Resolved.ToString())
        {
            record.ResolveTime = DateTime.Now;
        }

        var result = await _repository.SplitTableUpdateAsync(record);

        _logger.LogInformation("更新报警记录状态: ID: {RecordId}, 状态: {State}, 用户: {User}, 结果: {Result}",
            id, state, user, result ? "成功" : "失败");

        return result;
    }

    /// <summary>
    ///     获取报警记录
    /// </summary>
    /// <param name="id">记录ID</param>
    /// <returns>报警记录</returns>
    public async Task<AlarmRecord> GetAsync(long id)
    {
        return await _repository.SplitTableGetFirstAsync(r => r.Id == id);
    }

    /// <summary>
    ///     获取报警记录分页列表
    /// </summary>
    /// <param name="page">页码</param>
    /// <param name="pageSize">每页大小</param>
    /// <param name="deviceId">设备ID，可选</param>
    /// <param name="severity">严重程度，可选</param>
    /// <param name="state">状态，可选</param>
    /// <param name="startTime">开始时间，可选</param>
    /// <param name="endTime">结束时间，可选</param>
    /// <returns>报警记录分页列表</returns>
    public async Task<SqlSugarPagedList<AlarmRecord>> GetPagedListAsync(
        int page,
        int pageSize,
        string deviceId = null,
        int? severity = null,
        string state = null,
        DateTime? startTime = null,
        DateTime? endTime = null)
    {
        // 创建分页查询
        var query = _repository.AsQueryable().SplitTable();

        // 应用过滤条件
        if (!string.IsNullOrEmpty(deviceId))
            query = query.Where(r => r.DeviceId == deviceId);

        if (severity.HasValue)
            query = query.Where(r => r.Severity == severity.Value);

        if (!string.IsNullOrEmpty(state))
            query = query.Where(r => r.State == state);

        if (startTime.HasValue)
            query = query.Where(r => r.TriggerTime >= startTime.Value);

        if (endTime.HasValue)
            query = query.Where(r => r.TriggerTime <= endTime.Value);

        // 按触发时间降序排序
        query = query.OrderByDescending(r => r.TriggerTime);

        // 执行分页查询
        return await query.ToPagedListAsync(page, pageSize);
    }

    /// <summary>
    ///     批量确认报警
    /// </summary>
    /// <param name="ids">记录ID列表</param>
    /// <param name="user">用户</param>
    /// <returns>是否成功</returns>
    public async Task<bool> AcknowledgeBatchAsync(List<long> ids, string user)
    {
        try
        {
            foreach (var id in ids) await UpdateStateAsync(id, AlarmStateEnum.Acknowledged.ToString(), user);
            return true;
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "批量确认报警失败");
            return false;
        }
    }

    /// <summary>
    ///     获取报警统计
    /// </summary>
    /// <param name="deviceId">设备ID，可选</param>
    /// <returns>报警统计</returns>
    public async Task<AlarmStatisticsOutput> GetStatisticsAsync(string deviceId = null)
    {
        var result = new AlarmStatisticsOutput();

        try
        {
            // 创建查询基础
            Expression<Func<AlarmRecord, bool>> baseExpression = record => true;
            if (!string.IsNullOrEmpty(deviceId)) baseExpression = record => record.DeviceId == deviceId;

            // 获取总数
            result.TotalAlarms = await _repository.CountAsync(baseExpression);

            // 获取各状态数量
            result.ActiveAlarms = await _repository.CountAsync(r =>
                r.State == AlarmStateEnum.Active.ToString() &&
                (string.IsNullOrEmpty(deviceId) || r.DeviceId == deviceId));

            result.AcknowledgedAlarms = await _repository.CountAsync(r =>
                r.State == AlarmStateEnum.Acknowledged.ToString() &&
                (string.IsNullOrEmpty(deviceId) || r.DeviceId == deviceId));

            result.ResolvedAlarms = await _repository.CountAsync(r =>
                r.State == AlarmStateEnum.Resolved.ToString() &&
                (string.IsNullOrEmpty(deviceId) || r.DeviceId == deviceId));

            // 获取各严重程度的报警数量
            for (var severity = 1; severity <= 5; severity++)
            {
                var count = await _repository.CountAsync(r =>
                    r.Severity == severity &&
                    (string.IsNullOrEmpty(deviceId) || r.DeviceId == deviceId));

                result.AlarmsBySeverity[severity] = count;
            }

            // 获取各设备的报警数量
            if (string.IsNullOrEmpty(deviceId))
            {
                // 如果未指定设备ID，则获取所有设备的统计
                var devices = await _repository.AsQueryable()
                    .Select(r => r.DeviceId)
                    .Distinct()
                    .ToListAsync();

                foreach (var device in devices)
                {
                    var count = await _repository.CountAsync(r => r.DeviceId == device);
                    result.AlarmsByDevice[device] = count;
                }
            }
            else
            {
                // 如果指定了设备ID，则只获取该设备的统计
                result.AlarmsByDevice[deviceId] = result.TotalAlarms;
            }
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "获取报警统计失败");
        }

        return result;
    }
}