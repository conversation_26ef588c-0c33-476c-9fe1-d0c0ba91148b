using EdgeGateway.Alarm.Entity.Model;
using System;
using System.Threading.Tasks;

namespace EdgeGateway.Alarm.Services;

/// <summary>
///     报警配置服务接口
/// </summary>
public interface IAlarmConfigurationService
{
  /// <summary>
  ///     配置变更事件
  /// </summary>
  event EventHandler<ConfigurationChangedEventArgs> ConfigurationChanged;

  /// <summary>
  ///     获取当前配置
  /// </summary>
  /// <returns>报警配置</returns>
  AlarmConfiguration GetConfiguration();

  /// <summary>
  ///     获取报警配置
  /// </summary>
  /// <returns>报警配置</returns>
  Task<AlarmConfiguration> GetConfigurationAsync();

  /// <summary>
  ///     更新报警配置
  /// </summary>
  /// <param name="configuration">报警配置</param>
  /// <returns>更新结果</returns>
  Task<bool> UpdateConfigurationAsync(AlarmConfiguration configuration);

  /// <summary>
  ///     重置报警配置
  /// </summary>
  /// <returns>重置结果</returns>
  Task<bool> ResetConfigurationAsync();

  /// <summary>
  ///     保存配置到文件
  /// </summary>
  /// <returns>异步任务</returns>
  Task SaveConfigurationAsync();

  /// <summary>
  ///     从文件加载配置
  /// </summary>
  /// <returns>异步任务</returns>
  Task LoadConfigurationAsync();
}