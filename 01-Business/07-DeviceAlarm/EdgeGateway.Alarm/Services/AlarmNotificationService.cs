using System.Collections.Concurrent;
using System.Net;
using System.Net.Mail;
using System.Text.Json;
using EdgeGateway.Alarm.Entity;
using EdgeGateway.Alarm.Entity.Model;
using EdgeGateway.WebSocket;
using Furion.DependencyInjection;
using Microsoft.Extensions.Logging;
using Polly;
using Polly.Retry;

namespace EdgeGateway.Alarm.Services;

/// <summary>
///     报警通知服务实现
/// </summary>
public class AlarmNotificationService : IAlarmNotificationService, ITransient
{
    private readonly IMessagePushService _messagePushService;
    private readonly ILogger<AlarmNotificationService> _logger;

    /// <summary>
    ///     WebSocket主题名
    /// </summary>
    private const string ALARM_TOPIC = "device_alarm";

    /// <summary>
    ///     通知重试策略
    /// </summary>
    private readonly AsyncRetryPolicy _retryPolicy;

    /// <summary>
    ///     通知模板缓存
    /// </summary>
    private readonly ConcurrentDictionary<string, string> _templateCache = new();

    /// <summary>
    ///     构造函数
    /// </summary>
    /// <param name="messagePushService">消息推送服务</param>
    /// <param name="logger">日志记录器</param>
    public AlarmNotificationService(IMessagePushService messagePushService, ILogger<AlarmNotificationService> logger)
    {
        _messagePushService = messagePushService;
        _logger = logger;

        // 初始化重试策略
        _retryPolicy = Policy
            .Handle<Exception>()
            .WaitAndRetryAsync(3, retryAttempt =>
                    TimeSpan.FromSeconds(Math.Pow(2, retryAttempt)),
                (exception, timeSpan, retryCount, context) =>
                {
                    _logger.LogWarning(exception,
                        "发送通知失败,将在 {RetryIn}s 后进行第 {RetryCount} 次重试",
                        timeSpan.TotalSeconds,
                        retryCount);
                });

        // 初始化通知模板
        InitializeTemplates();
    }

    /// <summary>
    ///     初始化通知模板
    /// </summary>
    private void InitializeTemplates()
    {
        _templateCache["websocket"] = @"
{
    ""type"": ""alarm"",
    ""data"": {
        ""id"": ""{id}"",
        ""deviceId"": ""{deviceId}"",
        ""deviceName"": ""{deviceName}"",
        ""ruleName"": ""{ruleName}"",
        ""message"": ""{message}"",
        ""severity"": {severity},
        ""triggerTime"": ""{triggerTime}"",
        ""state"": ""{state}"",
        ""values"": {values}
    }
}";

        _templateCache["email"] = @"
报警通知

设备: {deviceName} ({deviceId})
规则: {ruleName}
级别: {severityText}
时间: {triggerTime}
状态: {stateText}
消息: {message}

触发值:
{valuesList}

请及时处理!";

        _templateCache["dingtalk"] = @"
## 报警通知
### {severityText}级别报警

**设备**: {deviceName} ({deviceId})  
**规则**: {ruleName}  
**时间**: {triggerTime}  
**状态**: {stateText}  
**消息**: {message}  

**触发值**:  
{valuesList}";
    }

    /// <summary>
    ///     发送报警通知
    /// </summary>
    /// <param name="alarm">报警记录</param>
    /// <param name="action">报警动作</param>
    /// <returns>是否成功</returns>
    public async Task<bool> SendNotificationAsync(AlarmRecord alarm, AlarmAction action)
    {
        try
        {
            // 使用重试策略包装通知发送
            return await _retryPolicy.ExecuteAsync(async () =>
            {
                // 根据动作类型选择通知方式
                switch (action.Type.ToLower())
                {
                    case "websocket":
                        return await SendWebSocketNotificationAsync(alarm);
                    case "email":
                        return await SendEmailNotificationAsync(alarm, action.Parameters);
                    case "dingtalk":
                        return await SendDingTalkNotificationAsync(alarm, action.Parameters);
                    default:
                        _logger.LogWarning("不支持的通知类型: {NotificationType}", action.Type);
                        return false;
                }
            });
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "发送报警通知失败: {AlarmId}, {ActionType}", alarm.Id, action.Type);
            return false;
        }
    }

    /// <summary>
    ///     获取支持的通知类型列表
    /// </summary>
    /// <returns>通知类型列表</returns>
    public List<string> GetSupportedNotificationTypes()
    {
        return new List<string> { "WebSocket", "Email", "DingTalk" };
    }

    /// <summary>
    ///     发送WebSocket通知
    /// </summary>
    /// <param name="alarm">报警记录</param>
    /// <returns>是否成功</returns>
    private async Task<bool> SendWebSocketNotificationAsync(AlarmRecord alarm)
    {
        try
        {
            var template = _templateCache["websocket"];
            var message = FormatTemplate(template, alarm);

            // 推送到WebSocket
            await _messagePushService.PushMessageAsync(ALARM_TOPIC, message);

            // 同时推送到设备特定的主题
            await _messagePushService.PushMessageAsync($"device_{alarm.DeviceId}_alarm", message);

            _logger.LogInformation("WebSocket报警通知已发送: {AlarmId}", alarm.Id);

            return true;
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "发送WebSocket报警通知失败: {AlarmId}", alarm.Id);
            throw; // 重新抛出异常以触发重试
        }
    }

    /// <summary>
    ///     发送邮件通知
    /// </summary>
    /// <param name="alarm">报警记录</param>
    /// <param name="parameters">邮件参数</param>
    /// <returns>是否成功</returns>
    private async Task<bool> SendEmailNotificationAsync(AlarmRecord alarm, Dictionary<string, string> parameters)
    {
        try
        {
            // 检查必要的邮件参数
            if (!parameters.TryGetValue("to", out var to))
            {
                _logger.LogError("邮件接收者未指定: {AlarmId}", alarm.Id);
                return false;
            }

            if (!parameters.TryGetValue("smtpServer", out var smtpServer))
            {
                _logger.LogError("SMTP服务器未指定: {AlarmId}", alarm.Id);
                return false;
            }

            if (!parameters.TryGetValue("smtpPort", out var smtpPortStr) || !int.TryParse(smtpPortStr, out var smtpPort))
            {
                _logger.LogError("SMTP端口未指定或格式错误: {AlarmId}", alarm.Id);
                return false;
            }

            if (!parameters.TryGetValue("username", out var username))
            {
                _logger.LogError("SMTP用户名未指定: {AlarmId}", alarm.Id);
                return false;
            }

            if (!parameters.TryGetValue("password", out var password))
            {
                _logger.LogError("SMTP密码未指定: {AlarmId}", alarm.Id);
                return false;
            }

            // 获取发件人地址，如果未指定则使用用户名
            parameters.TryGetValue("from", out var from);
            from ??= username;

            // 获取邮件主题，如果未指定则使用默认主题
            parameters.TryGetValue("subject", out var subject);
            subject ??= $"设备报警通知 - {alarm.DeviceName}";

            // 获取邮件内容
            var template = _templateCache["email"];
            var body = FormatTemplate(template, alarm);

            // 创建邮件消息
            using var mailMessage = new MailMessage
            {
                From = new MailAddress(from),
                Subject = subject,
                Body = body,
                IsBodyHtml = false
            };

            // 添加收件人
            foreach (var recipient in to.Split(new[] { ',', ';' }, StringSplitOptions.RemoveEmptyEntries)) mailMessage.To.Add(recipient.Trim());

            // 创建SMTP客户端
            using var smtpClient = new SmtpClient(smtpServer, smtpPort)
            {
                EnableSsl = parameters.TryGetValue("enableSsl", out var enableSslStr) &&
                            bool.TryParse(enableSslStr, out var enableSsl) && enableSsl,
                Credentials = new NetworkCredential(username, password)
            };

            // 发送邮件
            await smtpClient.SendMailAsync(mailMessage);

            _logger.LogInformation("邮件报警通知已发送: {AlarmId}, 接收者: {Recipients}", alarm.Id, to);
            return true;
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "发送邮件报警通知失败: {AlarmId}", alarm.Id);
            throw; // 重新抛出异常以触发重试
        }
    }

    /// <summary>
    ///     发送钉钉通知
    /// </summary>
    /// <param name="alarm">报警记录</param>
    /// <param name="parameters">钉钉参数</param>
    /// <returns>是否成功</returns>
    private async Task<bool> SendDingTalkNotificationAsync(AlarmRecord alarm, Dictionary<string, string> parameters)
    {
        try
        {
            if (!parameters.TryGetValue("webhook", out var webhook))
            {
                _logger.LogError("钉钉Webhook未指定: {AlarmId}", alarm.Id);
                return false;
            }

            var template = _templateCache["dingtalk"];
            var message = FormatTemplate(template, alarm);

            // TODO: 实现钉钉通知功能
            // 这里可以使用钉钉机器人API发送通知

            _logger.LogInformation("钉钉报警通知功能尚未实现: {AlarmId}", alarm.Id);
            return false;
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "发送钉钉报警通知失败: {AlarmId}", alarm.Id);
            throw; // 重新抛出异常以触发重试
        }
    }

    /// <summary>
    ///     格式化模板
    /// </summary>
    /// <param name="template">模板</param>
    /// <param name="alarm">报警记录</param>
    /// <returns>格式化后的内容</returns>
    private string FormatTemplate(string template, AlarmRecord alarm)
    {
        var severityText = GetSeverityText(alarm.Severity);
        var stateText = GetStateText(alarm.State);
        var valuesList = FormatTriggerValues(alarm.TriggerValues);

        return template
            .Replace("{id}", alarm.Id.ToString())
            .Replace("{deviceId}", alarm.DeviceId)
            .Replace("{deviceName}", alarm.DeviceName)
            .Replace("{ruleName}", alarm.AlarmRuleName)
            .Replace("{message}", alarm.Message)
            .Replace("{severity}", alarm.Severity.ToString())
            .Replace("{severityText}", severityText)
            .Replace("{triggerTime}", alarm.TriggerTime.ToString("yyyy-MM-dd HH:mm:ss"))
            .Replace("{state}", alarm.State)
            .Replace("{stateText}", stateText)
            .Replace("{values}", JsonSerializer.Serialize(alarm.TriggerValues))
            .Replace("{valuesList}", valuesList);
    }

    /// <summary>
    ///     获取报警级别文本
    /// </summary>
    private string GetSeverityText(int severity)
    {
        return severity switch
        {
            1 => "信息",
            2 => "警告",
            3 => "一般",
            4 => "严重",
            5 => "灾难",
            _ => "未知"
        };
    }

    /// <summary>
    ///     获取报警状态文本
    /// </summary>
    private string GetStateText(string state)
    {
        return state switch
        {
            "Active" => "活动",
            "Acknowledged" => "已确认",
            "Resolved" => "已解决",
            _ => "未知"
        };
    }

    /// <summary>
    ///     格式化触发值
    /// </summary>
    private string FormatTriggerValues(Dictionary<string, object> values)
    {
        if (values == null || values.Count == 0)
            return "无";

        return string.Join("\n", values.Select(v => $"{v.Key}: {v.Value}"));
    }
}