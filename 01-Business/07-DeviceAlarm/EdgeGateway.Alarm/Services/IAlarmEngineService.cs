using EdgeGateway.Pipeline.Entity.Model;

namespace EdgeGateway.Alarm.Services;

/// <summary>
///     报警引擎服务接口
/// </summary>
public interface IAlarmEngineService
{
  /// <summary>
  ///     刷新报警规则
  /// </summary>
  /// <returns>是否成功</returns>
  Task<bool> ReloadRulesAsync();

  /// <summary>
  ///     测试报警表达式
  /// </summary>
  /// <param name="expression">表达式</param>
  /// <param name="context">上下文变量</param>
  /// <returns>表达式结果</returns>
  Task<bool> TestExpressionAsync(string expression, Dictionary<string, object> context);

  /// <summary>
  ///     处理接收到的数据
  /// </summary>
  /// <param name="payload">数据负载</param>
  /// <returns>处理任务</returns>
  Task OnPayloadReceived(PayLoad payload);
}