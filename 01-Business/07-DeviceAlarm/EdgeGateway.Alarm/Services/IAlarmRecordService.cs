using EdgeGateway.Alarm.Entity;
using EdgeGateway.Alarm.Entity.Dto;
using EdgeGateway.SqlSugar.SqlSugar;

namespace EdgeGateway.Alarm.Services;

/// <summary>
///     报警记录服务接口
/// </summary>
public interface IAlarmRecordService
{
    /// <summary>
    ///     创建报警记录
    /// </summary>
    /// <param name="record">报警记录</param>
    /// <returns>报警记录ID</returns>
    Task<long> CreateAsync(AlarmRecord record);

    /// <summary>
    ///     更新报警记录状态
    /// </summary>
    /// <param name="id">记录ID</param>
    /// <param name="state">状态</param>
    /// <param name="user">用户，可选</param>
    /// <returns>是否成功</returns>
    Task<bool> UpdateStateAsync(long id, string state, string user = null);

    /// <summary>
    ///     获取报警记录
    /// </summary>
    /// <param name="id">记录ID</param>
    /// <returns>报警记录</returns>
    Task<AlarmRecord> GetAsync(long id);

    /// <summary>
    ///     获取报警记录分页列表
    /// </summary>
    /// <param name="page">页码</param>
    /// <param name="pageSize">每页大小</param>
    /// <param name="deviceId">设备ID，可选</param>
    /// <param name="severity">严重程度，可选</param>
    /// <param name="state">状态，可选</param>
    /// <param name="startTime">开始时间，可选</param>
    /// <param name="endTime">结束时间，可选</param>
    /// <returns>报警记录分页列表</returns>
    Task<SqlSugarPagedList<AlarmRecord>> GetPagedListAsync(int page, int pageSize,
        string deviceId = null, int? severity = null, string state = null,
        DateTime? startTime = null, DateTime? endTime = null);

    /// <summary>
    ///     批量确认报警
    /// </summary>
    /// <param name="ids">记录ID列表</param>
    /// <param name="user">用户</param>
    /// <returns>是否成功</returns>
    Task<bool> AcknowledgeBatchAsync(List<long> ids, string user);

    /// <summary>
    ///     获取报警统计
    /// </summary>
    /// <param name="deviceId">设备ID，可选</param>
    /// <returns>报警统计</returns>
    Task<AlarmStatisticsOutput> GetStatisticsAsync(string deviceId = null);
}