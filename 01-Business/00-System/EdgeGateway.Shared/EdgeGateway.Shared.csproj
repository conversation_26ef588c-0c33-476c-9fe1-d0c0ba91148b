<Project Sdk="Microsoft.NET.Sdk">

  <PropertyGroup>
    <TargetFramework>net9.0</TargetFramework>
    <ImplicitUsings>enable</ImplicitUsings>
    <Nullable>enable</Nullable>
  </PropertyGroup>

  <ItemGroup>
    <PackageReference Include="Newtonsoft.Json" Version="13.0.3" />
  </ItemGroup>

  <ItemGroup>
    <ProjectReference Include="..\..\..\03-CoreDependencies\EdgeGateway.Core\EdgeGateway.Core.csproj" />
    <ProjectReference Include="..\..\..\06-Protocols\EdgeGateway.Driver.Entity\EdgeGateway.Driver.Entity.csproj" />
    <ProjectReference Include="..\EdgeGateway.Base.Entity\EdgeGateway.Base.Entity.csproj" />
  </ItemGroup>

</Project> 