using System;
using System.Collections.Generic;
using System.Dynamic;
using System.Linq;

namespace EdgeGateway.Shared.Utils;

/// <summary>
/// 标签结构处理工具类，用于创建支持点操作符访问的嵌套标签结构
/// </summary>
public static class TagsHelper
{
  /// <summary>
  /// 创建嵌套标签对象结构，支持tags.product.name这样的点操作符访问语法
  /// 此方法用于处理通用对象，需要提供值提取委托
  /// </summary>
  /// <typeparam name="T">标签值类型</typeparam>
  /// <param name="labels">标签键值对字典</param>
  /// <param name="getNameFunc">获取名称的委托</param>
  /// <param name="getValueFunc">获取值的委托</param>
  /// <param name="getTimeFunc">获取时间的委托（可选）</param>
  /// <param name="getTypeFunc">获取类型的委托（可选）</param>
  /// <param name="getDescriptionFunc">获取描述的委托（可选）</param>
  /// <returns>嵌套的动态对象结构</returns>
  public static dynamic CreateNestedTagsObject<T>(
      IDictionary<string, T> labels,
      Func<T, string> getNameFunc,
      Func<T, object> getValueFunc,
      Func<T, long?> getTimeFunc = null,
      Func<T, string> getTypeFunc = null,
      Func<T, string> getDescriptionFunc = null)
  {
    // 创建动态对象作为根节点，ExpandoObject支持动态添加属性
    dynamic tagsObject = new ExpandoObject();

    // 遍历所有标签值，构建嵌套结构
    foreach (var pair in labels)
    {
      // 跳过标识符为空的标签，避免处理异常
      if (string.IsNullOrEmpty(pair.Key))
        continue;

      // 将标识符按点分隔，例如"dayProduct.name"会被分割为["dayProduct", "name"]
      var segments = pair.Key.Split('.');
      // current指针用于跟踪当前处理的嵌套层级对象
      dynamic current = tagsObject;

      // 处理除最后一个段外的所有路径段（例如对于"a.b.c"，此循环处理"a"和"b"）
      for (int i = 0; i < segments.Length - 1; i++)
      {
        // 获取当前段名称（如"dayProduct"）
        string segment = segments[i];
        // 将dynamic转换为IDictionary以便进行键值操作
        var currentDict = (IDictionary<string, object>)current;

        // 如果当前路径段不存在，则创建一个新的嵌套ExpandoObject
        if (!currentDict.ContainsKey(segment))
        {
          currentDict[segment] = new ExpandoObject();
        }
        // 移动current指针到下一个嵌套层级
        current = currentDict[segment];
      }

      // 处理最后一个路径段，设置最终的属性值
      string lastSegment = segments.Last();

      // 动态创建对象，根据提供的委托添加属性
      var propertyObject = new ExpandoObject() as IDictionary<string, object>;

      // 添加id属性，代表标签本身的key
      propertyObject["id"] = lastSegment;

      // 添加fullId属性，代表标签的完整路径
      propertyObject["fullId"] = pair.Key;

      // 添加名称属性（必需）
      propertyObject["name"] = getNameFunc(pair.Value);

      // 添加值属性（必需）
      propertyObject["value"] = getValueFunc(pair.Value) ?? string.Empty;

      // 添加可选属性
      if (getTimeFunc != null)
      {
        var time = getTimeFunc(pair.Value);
        if (time.HasValue)
          propertyObject["time"] = time.Value;
      }

      if (getTypeFunc != null)
      {
        var type = getTypeFunc(pair.Value);
        if (!string.IsNullOrEmpty(type))
          propertyObject["type"] = type;
      }

      if (getDescriptionFunc != null)
      {
        var description = getDescriptionFunc(pair.Value);
        if (!string.IsNullOrEmpty(description))
          propertyObject["description"] = description;
      }

        // 在最终位置设置标签属性对象
        ((IDictionary<string, object>)current)[lastSegment] = propertyObject;
    }

    // 返回构建好的嵌套标签对象结构
    return tagsObject;
  }
}