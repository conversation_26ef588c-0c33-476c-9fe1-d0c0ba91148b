using System;
using System.Collections.Generic;
using System.Linq;
using System.Text;
using System.Text.RegularExpressions;
using System.Threading.Tasks;
using EdgeGateway.Base.OpenApi.Entity;
using EdgeGateway.Base.OpenApi.Models;
using EdgeGateway.SqlSugar.SqlSugar;
using Furion.DataEncryption;
using Furion.LinqBuilder;
using Microsoft.Extensions.Caching.Memory;
using Microsoft.Extensions.Logging;

namespace EdgeGateway.Base.OpenApi.Services;

/// <summary>
/// API密钥服务实现
/// </summary>
public class ApiKeyService : IApiKeyService, IScoped
{
  private readonly SqlSugarRepository<ApiKey> _apiKeyRepo;
  private readonly SqlSugarRepository<ApiPermission> _apiPermissionRepo;
  private readonly SqlSugarRepository<ApiCallLog> _apiCallLogRepo;
  private readonly IMemoryCache _cache;
  private readonly ILogger<ApiKeyService> _logger;

  /// <summary>
  /// 构造函数
  /// </summary>
  /// <param name="apiKeyRepo">API密钥仓储</param>
  /// <param name="apiPermissionRepo">API权限仓储</param>
  /// <param name="apiCallLogRepo">API调用日志仓储</param>
  /// <param name="cache">缓存</param>
  /// <param name="logger">日志</param>
  public ApiKeyService(
      SqlSugarRepository<ApiKey> apiKeyRepo,
      SqlSugarRepository<ApiPermission> apiPermissionRepo,
      SqlSugarRepository<ApiCallLog> apiCallLogRepo,
      IMemoryCache cache,
      ILogger<ApiKeyService> logger)
  {
    _apiKeyRepo = apiKeyRepo;
    _apiPermissionRepo = apiPermissionRepo;
    _apiCallLogRepo = apiCallLogRepo;
    _cache = cache;
    _logger = logger;
  }

  /// <summary>
  /// 创建API密钥
  /// </summary>
  /// <param name="model">API密钥创建模型</param>
  /// <returns>创建后的API密钥详细信息</returns>
  public async Task<ApiKeyDetailModel> CreateApiKeyAsync(ApiKeyCreateModel model)
  {
    // 生成API密钥和密钥密文
    var apiKey = Guid.NewGuid().ToString("N");
    var apiSecret = Guid.NewGuid().ToString("N");

    // 创建API密钥实体
    var apiKeyEntity = new ApiKey
    {
      Name = model.Name,
      Key = apiKey,
      Secret = MD5Encryption.Encrypt(apiSecret), // 存储密钥的哈希值
      Creator = model.Creator,
      Status = model.Status,
      AllowedIps = model.AllowedIps,
      ExpireTime = model.ExpireTime,
      Remark = model.Remark,
      CreateTime = DateTime.Now
    };

    // 保存API密钥
    await _apiKeyRepo.InsertAsync(apiKeyEntity);

    // 返回详细信息
    return new ApiKeyDetailModel
    {
      Id = apiKeyEntity.Id,
      Name = apiKeyEntity.Name,
      Key = apiKeyEntity.Key,
      Secret = apiSecret, // 返回原始密钥（仅在创建时返回）
      Creator = apiKeyEntity.Creator,
      Status = apiKeyEntity.Status,
      AllowedIps = apiKeyEntity.AllowedIps,
      ExpireTime = apiKeyEntity.ExpireTime,
      Remark = apiKeyEntity.Remark,
      CreateTime = apiKeyEntity.CreateTime,
      LastAccessTime = apiKeyEntity.LastAccessTime,
      CallCount = apiKeyEntity.CallCount,
      Permissions = new List<ApiPermissionModel>()
    };
  }

  /// <summary>
  /// 更新API密钥
  /// </summary>
  /// <param name="id">API密钥ID</param>
  /// <param name="model">API密钥更新模型</param>
  /// <returns>更新结果</returns>
  public async Task<bool> UpdateApiKeyAsync(long id, ApiKeyUpdateModel model)
  {
    // 获取API密钥
    var apiKey = await _apiKeyRepo.GetByIdAsync(id);
    if (apiKey == null)
    {
      return false;
    }

    // 更新API密钥
    if (!string.IsNullOrEmpty(model.Name))
    {
      apiKey.Name = model.Name;
    }

    if (model.Status.HasValue)
    {
      apiKey.Status = model.Status.Value;
    }

    if (model.AllowedIps != null)
    {
      apiKey.AllowedIps = model.AllowedIps;
    }

    if (model.ExpireTime.HasValue)
    {
      apiKey.ExpireTime = model.ExpireTime;
    }

    if (model.Remark != null)
    {
      apiKey.Remark = model.Remark;
    }

    apiKey.UpdateTime = DateTime.Now;

    // 保存更新
    await _apiKeyRepo.UpdateAsync(apiKey);

    // 清除缓存
    _cache.Remove($"ApiKey:{apiKey.Key}");

    return true;
  }

  /// <summary>
  /// 删除API密钥
  /// </summary>
  /// <param name="id">API密钥ID</param>
  /// <returns>删除结果</returns>
  public async Task<bool> DeleteApiKeyAsync(long id)
  {
    // 获取API密钥
    var apiKey = await _apiKeyRepo.GetByIdAsync(id);
    if (apiKey == null)
    {
      return false;
    }

    // 删除API密钥
    await _apiKeyRepo.DeleteAsync(apiKey);

    // 删除相关的API权限
    await _apiPermissionRepo.DeleteAsync(p => p.ApiKeyId == id);

    // 清除缓存
    _cache.Remove($"ApiKey:{apiKey.Key}");

    return true;
  }

  /// <summary>
  /// 获取API密钥
  /// </summary>
  /// <param name="id">API密钥ID</param>
  /// <returns>API密钥详细信息</returns>
  public async Task<ApiKeyDetailModel> GetApiKeyAsync(long id)
  {
    // 获取API密钥
    var apiKey = await _apiKeyRepo.GetByIdAsync(id);
    if (apiKey == null)
    {
      return null;
    }

    // 获取API权限
    var permissions = await _apiPermissionRepo.AsQueryable()
        .Where(p => p.ApiKeyId == id)
        .ToListAsync();

    // 返回详细信息
    return new ApiKeyDetailModel
    {
      Id = apiKey.Id,
      Name = apiKey.Name,
      Key = apiKey.Key,
      Secret = null, // 不返回密钥密文
      Creator = apiKey.Creator,
      Status = apiKey.Status,
      AllowedIps = apiKey.AllowedIps,
      ExpireTime = apiKey.ExpireTime,
      Remark = apiKey.Remark,
      CreateTime = apiKey.CreateTime,
      LastAccessTime = apiKey.LastAccessTime,
      CallCount = apiKey.CallCount,
      Permissions = permissions.Select(p => new ApiPermissionModel
      {
        Id = p.Id,
        ApiKeyId = p.ApiKeyId,
        ApiPath = p.ApiPath,
        HttpMethods = p.HttpMethods,
        GroupName = p.GroupName,
        Status = p.Status,
        RateLimit = p.RateLimit,
        CreateTime = p.CreateTime,
        UpdateTime = p.UpdateTime,
        Remark = p.Remark
      }).ToList()
    };
  }

  /// <summary>
  /// 获取API密钥列表
  /// </summary>
  /// <param name="pageIndex">页码</param>
  /// <param name="pageSize">每页大小</param>
  /// <param name="keyword">关键字</param>
  /// <returns>API密钥列表</returns>
  public async Task<PagedList<ApiKeyModel>> GetApiKeyListAsync(int pageIndex, int pageSize, string keyword = null)
  {
    // 构建查询条件
    var query = _apiKeyRepo.AsQueryable();
    if (!string.IsNullOrEmpty(keyword))
    {
      query = query.Where(k => k.Name.Contains(keyword) || k.Key.Contains(keyword) || k.Creator.Contains(keyword));
    }

    // 获取总记录数
    var totalCount = await query.CountAsync();

    // 获取分页数据
    var items = await query.OrderByDescending(k => k.CreateTime)
        .Skip((pageIndex - 1) * pageSize)
        .Take(pageSize)
        .ToListAsync();

    // 返回分页列表
    return new PagedList<ApiKeyModel>
    {
      PageIndex = pageIndex,
      PageSize = pageSize,
      TotalCount = totalCount,
      TotalPages = (int)Math.Ceiling(totalCount / (double)pageSize),
      Items = items.Select(k => new ApiKeyModel
      {
        Id = k.Id,
        Name = k.Name,
        Key = k.Key,
        Creator = k.Creator,
        Status = k.Status,
        ExpireTime = k.ExpireTime,
        CreateTime = k.CreateTime,
        LastAccessTime = k.LastAccessTime,
        CallCount = k.CallCount
      }).ToList()
    };
  }

  /// <summary>
  /// 验证API密钥
  /// </summary>
  /// <param name="apiKey">API密钥</param>
  /// <param name="apiSecret">API密钥密文</param>
  /// <returns>有效的API密钥实体，无效则返回null</returns>
  public async Task<ApiKey> ValidateApiKeyAsync(string apiKey, string apiSecret)
  {
    // 尝试从缓存获取
    if (!_cache.TryGetValue($"ApiKey:{apiKey}", out ApiKey apiKeyEntity))
    {
      // 从数据库获取
      apiKeyEntity = await _apiKeyRepo.AsQueryable()
          .FirstAsync(k => k.Key == apiKey);

      if (apiKeyEntity != null)
      {
        // 缓存API密钥，有效期5分钟
        _cache.Set($"ApiKey:{apiKey}", apiKeyEntity, TimeSpan.FromMinutes(5));
      }
    }

    if (apiKeyEntity == null)
    {
      _logger.LogWarning("API密钥不存在: {ApiKey}", apiKey);
      return null;
    }

    // 检查API密钥状态
    if (apiKeyEntity.Status != 1)
    {
      _logger.LogWarning("API密钥已禁用: {ApiKey}", apiKey);
      return null;
    }

    // 检查API密钥过期时间
    if (apiKeyEntity.ExpireTime.HasValue && apiKeyEntity.ExpireTime.Value < DateTime.Now)
    {
      _logger.LogWarning("API密钥已过期: {ApiKey}", apiKey);
      return null;
    }

    // 验证密钥密文
    var encryptedSecret = MD5Encryption.Encrypt(apiSecret);
    if (apiKeyEntity.Secret != encryptedSecret)
    {
      _logger.LogWarning("API密钥密文不正确: {ApiKey}", apiKey);
      return null;
    }

    return apiKeyEntity;
  }

  /// <summary>
  /// 检查IP白名单
  /// </summary>
  /// <param name="apiKey">API密钥实体</param>
  /// <param name="clientIp">客户端IP</param>
  /// <returns>是否在白名单内</returns>
  public Task<bool> CheckIpWhitelistAsync(ApiKey apiKey, string clientIp)
  {
    // 如果未设置IP白名单，则允许所有IP
    if (string.IsNullOrEmpty(apiKey.AllowedIps))
    {
      return Task.FromResult(true);
    }

    // 分割IP白名单
    var allowedIps = apiKey.AllowedIps.Split(',', StringSplitOptions.RemoveEmptyEntries);

    // 检查IP是否在白名单内
    var result = allowedIps.Any(ip => ip.Trim() == clientIp || ip.Trim() == "*");

    return Task.FromResult(result);
  }

  /// <summary>
  /// 检查API权限
  /// </summary>
  /// <param name="apiKeyId">API密钥ID</param>
  /// <param name="path">API路径</param>
  /// <param name="method">HTTP方法</param>
  /// <returns>是否有权限</returns>
  public async Task<bool> CheckApiPermissionAsync(long apiKeyId, string path, string method)
  {
    // 获取所有启用的权限
    var permissions = await _apiPermissionRepo.AsQueryable()
        .Where(p => p.ApiKeyId == apiKeyId && p.Status == 1)
        .ToListAsync();

    if (permissions.Count == 0)
    {
      _logger.LogWarning("API密钥未设置任何权限: {ApiKeyId}", apiKeyId);
      return false;
    }

    // 检查是否有匹配的权限
    foreach (var permission in permissions)
    {
      // 检查路径
      if (IsPathMatch(permission.ApiPath, path))
      {
        // 检查HTTP方法
        var methods = permission.HttpMethods.Split(',', StringSplitOptions.RemoveEmptyEntries);
        if (methods.Any(m => m.Trim().Equals("*", StringComparison.OrdinalIgnoreCase) ||
                            m.Trim().Equals(method, StringComparison.OrdinalIgnoreCase)))
        {
          return true;
        }
      }
    }

    _logger.LogWarning("API密钥没有访问权限: {ApiKeyId}, {Path}, {Method}", apiKeyId, path, method);
    return false;
  }

  /// <summary>
  /// 检查访问频率
  /// </summary>
  /// <param name="apiKeyId">API密钥ID</param>
  /// <param name="path">API路径</param>
  /// <param name="method">HTTP方法</param>
  /// <returns>是否允许访问</returns>
  public async Task<bool> CheckRateLimitAsync(long apiKeyId, string path, string method)
  {
    // 获取API权限
    var permission = await _apiPermissionRepo.AsQueryable()
        .Where(p => p.ApiKeyId == apiKeyId && p.Status == 1)
        .FirstAsync(p => IsPathMatch(p.ApiPath, path));

    if (permission == null || permission.RateLimit <= 0)
    {
      // 未设置频率限制
      return true;
    }

    // 获取缓存键
    var cacheKey = $"RateLimit:{apiKeyId}:{path}";

    // 获取当前分钟内的调用次数
    if (!_cache.TryGetValue(cacheKey, out int callCount))
    {
      callCount = 0;
    }

    // 检查是否超过限制
    if (callCount >= permission.RateLimit)
    {
      _logger.LogWarning("API访问频率超限: {ApiKeyId}, {Path}, {CallCount}/{RateLimit}",
          apiKeyId, path, callCount, permission.RateLimit);
      return false;
    }

    // 增加调用次数
    _cache.Set(cacheKey, callCount + 1, new MemoryCacheEntryOptions
    {
      AbsoluteExpirationRelativeToNow = TimeSpan.FromMinutes(1)
    });

    return true;
  }

  /// <summary>
  /// 更新API密钥访问信息
  /// </summary>
  /// <param name="apiKeyId">API密钥ID</param>
  /// <returns>更新结果</returns>
  public async Task<bool> UpdateApiKeyAccessAsync(long apiKeyId)
  {
    try
    {
      // 更新最后访问时间和调用次数
      await _apiKeyRepo.AsUpdateable()
          .SetColumns(k => new ApiKey
          {
            LastAccessTime = DateTime.Now,
            CallCount = k.CallCount + 1
          })
          .Where(k => k.Id == apiKeyId)
          .ExecuteCommandAsync();

      return true;
    }
    catch (Exception ex)
    {
      _logger.LogError(ex, "更新API密钥访问信息失败: {ApiKeyId}", apiKeyId);
      return false;
    }
  }

  /// <summary>
  /// 获取API密钥权限列表
  /// </summary>
  /// <param name="apiKeyId">API密钥ID</param>
  /// <returns>API权限列表</returns>
  public async Task<List<ApiPermissionModel>> GetApiPermissionsAsync(long apiKeyId)
  {
    // 获取API权限
    var permissions = await _apiPermissionRepo.AsQueryable()
        .Where(p => p.ApiKeyId == apiKeyId)
        .ToListAsync();

    // 返回权限列表
    return permissions.Select(p => new ApiPermissionModel
    {
      Id = p.Id,
      ApiKeyId = p.ApiKeyId,
      ApiPath = p.ApiPath,
      HttpMethods = p.HttpMethods,
      GroupName = p.GroupName,
      Status = p.Status,
      RateLimit = p.RateLimit,
      CreateTime = p.CreateTime,
      UpdateTime = p.UpdateTime,
      Remark = p.Remark
    }).ToList();
  }

  /// <summary>
  /// 更新API密钥权限
  /// </summary>
  /// <param name="apiKeyId">API密钥ID</param>
  /// <param name="permissions">API权限列表</param>
  /// <returns>更新结果</returns>
  public async Task<bool> UpdateApiPermissionsAsync(long apiKeyId, List<ApiPermissionModel> permissions)
  {
    try
    {
      // 删除现有权限
      await _apiPermissionRepo.DeleteAsync(p => p.ApiKeyId == apiKeyId);

      // 添加新权限
      var entities = permissions.Select(p => new ApiPermission
      {
        ApiKeyId = apiKeyId,
        ApiPath = p.ApiPath,
        HttpMethods = p.HttpMethods,
        GroupName = p.GroupName,
        Status = p.Status,
        RateLimit = p.RateLimit,
        CreateTime = DateTime.Now,
        Remark = p.Remark
      }).ToList();

      await _apiPermissionRepo.InsertRangeAsync(entities);

      return true;
    }
    catch (Exception ex)
    {
      _logger.LogError(ex, "更新API密钥权限失败: {ApiKeyId}", apiKeyId);
      return false;
    }
  }

  /// <summary>
  /// 记录API调用日志
  /// </summary>
  /// <param name="log">API调用日志</param>
  /// <returns>记录结果</returns>
  public async Task<bool> LogApiCallAsync(ApiCallLog log)
  {
    try
    {
      await _apiCallLogRepo.InsertAsync(log);
      return true;
    }
    catch (Exception ex)
    {
      _logger.LogError(ex, "记录API调用日志失败");
      return false;
    }
  }

  /// <summary>
  /// 获取API调用日志列表
  /// </summary>
  /// <param name="pageIndex">页码</param>
  /// <param name="pageSize">每页大小</param>
  /// <param name="apiKeyId">API密钥ID</param>
  /// <param name="startTime">开始时间</param>
  /// <param name="endTime">结束时间</param>
  /// <param name="isSuccess">是否成功</param>
  /// <returns>API调用日志列表</returns>
  public async Task<PagedList<ApiCallLogModel>> GetApiCallLogsAsync(int pageIndex, int pageSize, long? apiKeyId = null,
      DateTime? startTime = null, DateTime? endTime = null, bool? isSuccess = null)
  {
    // 构建查询条件
    var query = _apiCallLogRepo.AsQueryable()
        .LeftJoin<ApiKey>((l, k) => l.ApiKeyId == k.Id)
        .Select((l, k) => new ApiCallLogModel
        {
          Id = l.Id,
          ApiKeyId = l.ApiKeyId,
          ApiKeyName = k.Name,
          CallerIp = l.CallerIp,
          RequestPath = l.RequestPath,
          HttpMethod = l.HttpMethod,
          StatusCode = l.StatusCode,
          ExecutionTime = l.ExecutionTime,
          CallTime = l.CallTime,
          IsSuccess = l.IsSuccess,
          ErrorMessage = l.ErrorMessage,
          RequestParams = l.RequestParams,
          RequestHeaders = l.RequestHeaders,
          ResponseContent = l.ResponseContent
        });

    // 添加过滤条件
    if (apiKeyId.HasValue)
    {
      query = query.Where(l => l.ApiKeyId == apiKeyId.Value);
    }

    if (startTime.HasValue)
    {
      query = query.Where(l => l.CallTime >= startTime.Value);
    }

    if (endTime.HasValue)
    {
      query = query.Where(l => l.CallTime <= endTime.Value);
    }

    if (isSuccess.HasValue)
    {
      query = query.Where(l => l.IsSuccess == isSuccess.Value);
    }

    // 获取总记录数
    var totalCount = await query.CountAsync();

    // 获取分页数据
    var items = await query.OrderByDescending(l => l.CallTime)
        .Skip((pageIndex - 1) * pageSize)
        .Take(pageSize)
        .ToListAsync();

    // 返回分页列表
    return new PagedList<ApiCallLogModel>
    {
      PageIndex = pageIndex,
      PageSize = pageSize,
      TotalCount = totalCount,
      TotalPages = (int)Math.Ceiling(totalCount / (double)pageSize),
      Items = items
    };
  }

  /// <summary>
  /// 清理过期的API调用日志
  /// </summary>
  /// <param name="days">保留天数</param>
  /// <returns>清理结果</returns>
  public async Task<bool> CleanupApiCallLogsAsync(int days)
  {
    try
    {
      // 计算截止日期
      var cutoffDate = DateTime.Now.AddDays(-days);

      // 删除截止日期之前的日志
      await _apiCallLogRepo.DeleteAsync(l => l.CallTime < cutoffDate);

      return true;
    }
    catch (Exception ex)
    {
      _logger.LogError(ex, "清理过期的API调用日志失败");
      return false;
    }
  }

  /// <summary>
  /// 检查路径是否匹配
  /// </summary>
  /// <param name="pattern">路径模式</param>
  /// <param name="path">实际路径</param>
  /// <returns>是否匹配</returns>
  private bool IsPathMatch(string pattern, string path)
  {
    // 转换通配符模式为正则表达式
    var regex = "^" + Regex.Escape(pattern)
        .Replace("\\*", ".*")
        .Replace("\\?", ".")
        + "$";

    return Regex.IsMatch(path, regex, RegexOptions.IgnoreCase);
  }
}