using System.Diagnostics;
using EdgeGateway.Base.OpenApi.Authentication;
using EdgeGateway.Base.OpenApi.Constants;
using EdgeGateway.Base.OpenApi.Services;
using Furion;
using Microsoft.AspNetCore.Authentication;
using Microsoft.Extensions.DependencyInjection;
using Microsoft.Extensions.Logging;

namespace EdgeGateway.Base.OpenApi;

/// <summary>
/// 开放API功能启动类
/// </summary>
public class Startup : AppStartup
{
  /// <summary>
  /// 配置服务
  /// </summary>
  /// <param name="services">服务集合</param>
  public void ConfigureServices(IServiceCollection services)
  {
    var sw = Stopwatch.StartNew();

    // 注册API密钥服务
    services.AddScoped<IApiKeyService, ApiKeyService>();

    // 注册API密钥认证方案
    services.AddAuthentication()
        .AddScheme<ApiKeyAuthenticationOptions, ApiKeyAuthenticationHandler>(
            OpenApiConstants.ApiKeyAuthenticationScheme, options => { });

    sw.Stop();
    var logger = services.BuildServiceProvider().GetRequiredService<ILogger<Startup>>();
    logger.LogInformation("╔════════════════════════════════════════════════════════════════");
    logger.LogInformation("║ 开放API服务注入完成，耗时: {ElapsedMilliseconds}ms", sw.ElapsedMilliseconds);
    logger.LogInformation("╚════════════════════════════════════════════════════════════════");
  }
}