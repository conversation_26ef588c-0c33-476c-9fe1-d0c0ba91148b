using System.Collections.Generic;
using System.Threading.Tasks;
using EdgeGateway.Base.OpenApi.Attributes;
using EdgeGateway.Base.OpenApi.Constants;
using EdgeGateway.Device.Entity;
using Furion.DynamicApiController;
using Furion.SpecificationDocument;
using Microsoft.AspNetCore.Authorization;
using Microsoft.AspNetCore.Mvc;

namespace EdgeGateway.Base.OpenApi.Controllers;

/// <summary>
/// 示例开放API控制器
/// 通过在控制器上添加OpenApiAttribute，使整个控制器的所有方法都成为开放API
/// </summary>
[ApiDescriptionSettings("开放API示例")]
[Route($"{OpenApiConstants.OpenApiRoutePrefix}/sample")]
[OpenApi("示例API", "用于演示开放API功能的示例控制器")]
public class SampleOpenApiController : IDynamicApiController
{
  /// <summary>
  /// 获取系统信息
  /// 不需要认证的开放API示例
  /// </summary>
  /// <returns>系统信息</returns>
  [HttpGet("system-info")]
  [OpenApi("系统信息", "获取系统基本信息，不需要认证")]
  [OperationId(nameof(GetSystemInfo))]
  [DisplayName("获取系统信息")]
  public dynamic GetSystemInfo()
  {
    return new
    {
      Name = "EdgeGateway",
      Version = "1.0.0",
      Environment = "Production",
      Status = "Running"
    };
  }

  /// <summary>
  /// 获取设备列表
  /// 需要认证的开放API示例
  /// </summary>
  /// <returns>设备列表</returns>
  [HttpGet("devices")]
  [OpenApi("设备列表", "获取系统中的设备列表，需要认证")]
  [OperationId(nameof(GetDevices))]
  [DisplayName("获取设备列表")]
  public List<object> GetDevices()
  {
    // 这里只是一个示例，实际应该从数据库或其他服务获取设备列表
    return new List<object>
        {
            new { Id = 1, Name = "Device1", Type = "Sensor", Status = "Online" },
            new { Id = 2, Name = "Device2", Type = "Controller", Status = "Offline" },
            new { Id = 3, Name = "Device3", Type = "Gateway", Status = "Online" }
        };
  }

  /// <summary>
  /// 获取设备详情
  /// 需要认证的开放API示例，同时演示路径参数
  /// </summary>
  /// <param name="id">设备ID</param>
  /// <returns>设备详情</returns>
  [HttpGet("devices/{id}")]
  [OpenApi("设备详情", "获取指定设备的详细信息，需要认证")]
  [OperationId(nameof(GetDeviceById))]
  [DisplayName("获取设备详情")]
  public object GetDeviceById([FromRoute] int id)
  {
    // 这里只是一个示例，实际应该从数据库或其他服务获取设备详情
    return new
    {
      Id = id,
      Name = $"Device{id}",
      Type = "Sensor",
      Status = "Online",
      Properties = new
      {
        Temperature = 25.5,
        Humidity = 60.2,
        Battery = 85
      }
    };
  }

  /// <summary>
  /// 创建设备
  /// 需要认证的开放API示例，同时演示POST请求和请求体参数
  /// </summary>
  /// <param name="device">设备信息</param>
  /// <returns>创建结果</returns>
  [HttpPost("devices")]
  [OpenApi("创建设备", "创建新设备，需要认证")]
  [OperationId(nameof(CreateDevice))]
  [DisplayName("创建设备")]
  public object CreateDevice([FromBody] DeviceInfo device)
  {
    // 这里只是一个示例，实际应该将设备信息保存到数据库
    return new
    {
      Success = true,
      Message = "设备创建成功",
      Data = new
      {
        Id = 4,
        Status = "Online"
      }
    };
  }
}