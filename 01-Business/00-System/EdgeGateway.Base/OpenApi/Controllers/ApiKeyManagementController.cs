using System.Collections.Generic;
using System.Threading.Tasks;
using EdgeGateway.Base.OpenApi.Models;
using EdgeGateway.Base.OpenApi.Services;
using Furion.DataValidation;
using Furion.DynamicApiController;
using Furion.SpecificationDocument;
using Microsoft.AspNetCore.Authorization;
using Microsoft.AspNetCore.Mvc;

namespace EdgeGateway.Base.OpenApi.Controllers;

/// <summary>
/// API密钥管理控制器
/// </summary>
[ApiDescriptionSettings("开放API管理")]
[Route("/api/openapi/keys")]
[Authorize]
public class ApiKeyManagementController : IDynamicApiController
{
  private readonly IApiKeyService _apiKeyService;

  /// <summary>
  /// 构造函数
  /// </summary>
  /// <param name="apiKeyService">API密钥服务</param>
  public ApiKeyManagementController(IApiKeyService apiKeyService)
  {
    _apiKeyService = apiKeyService;
  }

  /// <summary>
  /// 创建API密钥
  /// </summary>
  /// <param name="model">API密钥创建模型</param>
  /// <returns>创建后的API密钥详细信息</returns>
  [HttpPost]
  [OperationId(nameof(CreateApiKey))]
  [DisplayName("创建API密钥")]
  public async Task<ApiKeyDetailModel> CreateApiKey([FromBody, Required] ApiKeyCreateModel model)
  {
    return await _apiKeyService.CreateApiKeyAsync(model);
  }

  /// <summary>
  /// 更新API密钥
  /// </summary>
  /// <param name="id">API密钥ID</param>
  /// <param name="model">API密钥更新模型</param>
  /// <returns>更新结果</returns>
  [HttpPut("{id}")]
  [OperationId(nameof(UpdateApiKey))]
  [DisplayName("更新API密钥")]
  public async Task<bool> UpdateApiKey([FromRoute, Required] long id, [FromBody, Required] ApiKeyUpdateModel model)
  {
    return await _apiKeyService.UpdateApiKeyAsync(id, model);
  }

  /// <summary>
  /// 删除API密钥
  /// </summary>
  /// <param name="id">API密钥ID</param>
  /// <returns>删除结果</returns>
  [HttpDelete("{id}")]
  [OperationId(nameof(DeleteApiKey))]
  [DisplayName("删除API密钥")]
  public async Task<bool> DeleteApiKey([FromRoute, Required] long id)
  {
    return await _apiKeyService.DeleteApiKeyAsync(id);
  }

  /// <summary>
  /// 获取API密钥详情
  /// </summary>
  /// <param name="id">API密钥ID</param>
  /// <returns>API密钥详细信息</returns>
  [HttpGet("{id}")]
  [OperationId(nameof(GetApiKey))]
  [DisplayName("获取API密钥详情")]
  public async Task<ApiKeyDetailModel> GetApiKey([FromRoute, Required] long id)
  {
    return await _apiKeyService.GetApiKeyAsync(id);
  }

  /// <summary>
  /// 获取API密钥列表
  /// </summary>
  /// <param name="pageIndex">页码</param>
  /// <param name="pageSize">每页大小</param>
  /// <param name="keyword">关键字</param>
  /// <returns>API密钥列表</returns>
  [HttpGet]
  [OperationId(nameof(GetApiKeys))]
  [DisplayName("获取API密钥列表")]
  public async Task<PagedList<ApiKeyModel>> GetApiKeys([FromQuery] int pageIndex = 1, [FromQuery] int pageSize = 20, [FromQuery] string keyword = null)
  {
    return await _apiKeyService.GetApiKeyListAsync(pageIndex, pageSize, keyword);
  }

  /// <summary>
  /// 获取API密钥权限列表
  /// </summary>
  /// <param name="id">API密钥ID</param>
  /// <returns>API权限列表</returns>
  [HttpGet("{id}/permissions")]
  [OperationId(nameof(GetApiKeyPermissions))]
  [DisplayName("获取API密钥权限列表")]
  public async Task<List<ApiPermissionModel>> GetApiKeyPermissions([FromRoute, Required] long id)
  {
    return await _apiKeyService.GetApiPermissionsAsync(id);
  }

  /// <summary>
  /// 更新API密钥权限
  /// </summary>
  /// <param name="id">API密钥ID</param>
  /// <param name="permissions">API权限列表</param>
  /// <returns>更新结果</returns>
  [HttpPut("{id}/permissions")]
  [OperationId(nameof(UpdateApiKeyPermissions))]
  [DisplayName("更新API密钥权限")]
  public async Task<bool> UpdateApiKeyPermissions([FromRoute, Required] long id, [FromBody, Required] List<ApiPermissionModel> permissions)
  {
    return await _apiKeyService.UpdateApiPermissionsAsync(id, permissions);
  }

  /// <summary>
  /// 获取API调用日志列表
  /// </summary>
  /// <param name="pageIndex">页码</param>
  /// <param name="pageSize">每页大小</param>
  /// <param name="apiKeyId">API密钥ID</param>
  /// <param name="isSuccess">是否成功</param>
  /// <returns>API调用日志列表</returns>
  [HttpGet("logs")]
  [OperationId(nameof(GetApiCallLogs))]
  [DisplayName("获取API调用日志列表")]
  public async Task<PagedList<ApiCallLogModel>> GetApiCallLogs(
      [FromQuery] int pageIndex = 1,
      [FromQuery] int pageSize = 20,
      [FromQuery] long? apiKeyId = null,
      [FromQuery] bool? isSuccess = null)
  {
    return await _apiKeyService.GetApiCallLogsAsync(pageIndex, pageSize, apiKeyId, null, null, isSuccess);
  }

  /// <summary>
  /// 清理过期的API调用日志
  /// </summary>
  /// <param name="days">保留天数</param>
  /// <returns>清理结果</returns>
  [HttpPost("logs/cleanup")]
  [OperationId(nameof(CleanupApiCallLogs))]
  [DisplayName("清理过期的API调用日志")]
  public async Task<bool> CleanupApiCallLogs([FromQuery, Required] int days)
  {
    return await _apiKeyService.CleanupApiCallLogsAsync(days);
  }
}
