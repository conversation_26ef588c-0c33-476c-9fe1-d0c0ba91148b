using System;

namespace EdgeGateway.Base.OpenApi.Models;

/// <summary>
/// API调用日志模型
/// </summary>
public class ApiCallLogModel
{
  /// <summary>
  /// 日志ID
  /// </summary>
  public long Id { get; set; }

  /// <summary>
  /// API密钥ID
  /// </summary>
  public long ApiKeyId { get; set; }

  /// <summary>
  /// API密钥名称
  /// </summary>
  public string ApiKeyName { get; set; }

  /// <summary>
  /// 调用者IP
  /// </summary>
  public string CallerIp { get; set; }

  /// <summary>
  /// 请求路径
  /// </summary>
  public string RequestPath { get; set; }

  /// <summary>
  /// 请求方法
  /// </summary>
  public string HttpMethod { get; set; }

  /// <summary>
  /// 响应状态码
  /// </summary>
  public int StatusCode { get; set; }

  /// <summary>
  /// 执行时间（毫秒）
  /// </summary>
  public long ExecutionTime { get; set; }

  /// <summary>
  /// 调用时间
  /// </summary>
  public DateTime CallTime { get; set; }

  /// <summary>
  /// 是否成功
  /// </summary>
  public bool IsSuccess { get; set; }

  /// <summary>
  /// 错误消息
  /// </summary>
  public string ErrorMessage { get; set; }

  /// <summary>
  /// 请求参数（可能会很长，仅在详情中显示）
  /// </summary>
  public string RequestParams { get; set; }

  /// <summary>
  /// 请求头信息（可能会很长，仅在详情中显示）
  /// </summary>
  public string RequestHeaders { get; set; }

  /// <summary>
  /// 响应内容（可能会很长，仅在详情中显示）
  /// </summary>
  public string ResponseContent { get; set; }
}