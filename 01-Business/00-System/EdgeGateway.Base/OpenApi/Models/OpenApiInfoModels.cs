using System.Collections.Generic;

namespace EdgeGateway.Base.OpenApi.Models;

/// <summary>
/// 开放API信息模型
/// </summary>
public class OpenApiInfoModel
{
  /// <summary>
  /// API路径
  /// </summary>
  public string Path { get; set; }

  /// <summary>
  /// HTTP方法
  /// </summary>
  public string HttpMethod { get; set; }

  /// <summary>
  /// API名称
  /// </summary>
  public string Name { get; set; }

  /// <summary>
  /// API描述
  /// </summary>
  public string Description { get; set; }

  /// <summary>
  /// API分组名
  /// </summary>
  public string GroupName { get; set; }

  /// <summary>
  /// 是否需要认证
  /// </summary>
  public bool RequireAuthentication { get; set; }

  /// <summary>
  /// 访问频率限制（次数/分钟）
  /// </summary>
  public int RateLimit { get; set; }

  /// <summary>
  /// 是否允许缓存结果
  /// </summary>
  public bool AllowCache { get; set; }

  /// <summary>
  /// 缓存有效期（秒）
  /// </summary>
  public int CacheExpiration { get; set; }

  /// <summary>
  /// 参数列表
  /// </summary>
  public List<OpenApiParameterModel> Parameters { get; set; } = new List<OpenApiParameterModel>();
}

/// <summary>
/// 开放API参数模型
/// </summary>
public class OpenApiParameterModel
{
  /// <summary>
  /// 参数名称
  /// </summary>
  public string Name { get; set; }

  /// <summary>
  /// 参数类型
  /// </summary>
  public string Type { get; set; }

  /// <summary>
  /// 参数来源（FromQuery, FromRoute, FromBody, FromForm, FromHeader）
  /// </summary>
  public string Source { get; set; }

  /// <summary>
  /// 是否必需
  /// </summary>
  public bool IsRequired { get; set; }
}