using System;
using System.Collections.Generic;
using System.Diagnostics;
using System.IO;
using System.Linq;
using System.Reflection;
using System.Text;
using System.Text.Json;
using System.Threading.Tasks;
using EdgeGateway.Base.OpenApi.Attributes;
using EdgeGateway.Base.OpenApi.Constants;
using EdgeGateway.Base.OpenApi.Entity;
using EdgeGateway.Base.OpenApi.Services;
using Microsoft.AspNetCore.Http;
using Microsoft.AspNetCore.Mvc.Controllers;
using Microsoft.AspNetCore.Routing;
using Microsoft.Extensions.Caching.Memory;
using Microsoft.Extensions.DependencyInjection;
using Microsoft.Extensions.Logging;

namespace EdgeGateway.Base.OpenApi.Middleware;

/// <summary>
/// 开放API中间件
/// </summary>
public class OpenApiMiddleware
{
  private readonly RequestDelegate _next;
  private readonly ILogger<OpenApiMiddleware> _logger;
  private readonly IMemoryCache _cache;

  /// <summary>
  /// 构造函数
  /// </summary>
  /// <param name="next">请求委托</param>
  /// <param name="logger">日志</param>
  /// <param name="cache">缓存</param>
  public OpenApiMiddleware(RequestDelegate next, ILogger<OpenApiMiddleware> logger, IMemoryCache cache)
  {
    _next = next;
    _logger = logger;
    _cache = cache;
  }

  /// <summary>
  /// 处理请求
  /// </summary>
  /// <param name="context">HTTP上下文</param>
  /// <returns></returns>
  public async Task InvokeAsync(HttpContext context)
  {
    // 检查是否是开放API请求
    if (!IsOpenApiRequest(context))
    {
      await _next(context);
      return;
    }

    // 记录原始请求体和响应体
    var originalRequestBody = context.Request.Body;
    var originalResponseBody = context.Response.Body;

    string requestBody = null;
    string responseBody = null;
    ApiCallLog apiCallLog = null;
    var stopwatch = new Stopwatch();
    long? apiKeyId = null;
    bool isSuccess = false;

    try
    {
      // 读取请求体
      if (context.Request.ContentLength > 0)
      {
        context.Request.EnableBuffering();
        using var requestReader = new StreamReader(originalRequestBody, Encoding.UTF8, leaveOpen: true);
        requestBody = await requestReader.ReadToEndAsync();
        context.Request.Body.Position = 0;
      }

      // 检查是否存在OpenApi特性
      if (!HasOpenApiAttribute(context, out var openApiAttr))
      {
        await _next(context);
        return;
      }

      // 如果需要认证，则进行API密钥验证
      if (openApiAttr.RequireAuthentication)
      {
        var apiKeyService = context.RequestServices.GetRequiredService<IApiKeyService>();

        // 尝试获取API密钥和密钥
        if (!TryGetApiKeyAndSecret(context, out var apiKey, out var apiSecret))
        {
          context.Response.StatusCode = StatusCodes.Status401Unauthorized;
          await WriteResponseAsync(context, "未提供有效的API密钥");
          return;
        }

        // 验证API密钥
        var apiKeyEntity = await apiKeyService.ValidateApiKeyAsync(apiKey, apiSecret);
        if (apiKeyEntity == null)
        {
          context.Response.StatusCode = StatusCodes.Status401Unauthorized;
          await WriteResponseAsync(context, "API密钥无效或已过期");
          return;
        }

        apiKeyId = apiKeyEntity.Id;

        // 获取客户端IP地址
        var clientIp = context.Connection.RemoteIpAddress?.ToString();

        // 检查IP白名单
        if (!await apiKeyService.CheckIpWhitelistAsync(apiKeyEntity, clientIp))
        {
          context.Response.StatusCode = StatusCodes.Status403Forbidden;
          await WriteResponseAsync(context, "IP地址未授权");
          return;
        }

        // 检查API权限
        var path = context.Request.Path.Value;
        var method = context.Request.Method;
        if (!await apiKeyService.CheckApiPermissionAsync(apiKeyEntity.Id, path, method))
        {
          context.Response.StatusCode = StatusCodes.Status403Forbidden;
          await WriteResponseAsync(context, "无权访问该API");
          return;
        }

        // 检查访问频率
        if (!await apiKeyService.CheckRateLimitAsync(apiKeyEntity.Id, path, method))
        {
          context.Response.StatusCode = StatusCodes.Status429TooManyRequests;
          await WriteResponseAsync(context, "API访问频率超限");
          return;
        }

        // 更新API密钥访问信息
        await apiKeyService.UpdateApiKeyAccessAsync(apiKeyEntity.Id);
      }

      // 替换响应流，以便可以读取响应内容
      using var responseMemoryStream = new MemoryStream();
      context.Response.Body = responseMemoryStream;

      // 开始计时
      stopwatch.Start();

      // 处理请求
      await _next(context);

      // 停止计时
      stopwatch.Stop();

      // 读取响应体
      responseMemoryStream.Position = 0;
      using var responseReader = new StreamReader(responseMemoryStream, Encoding.UTF8, leaveOpen: true);
      responseBody = await responseReader.ReadToEndAsync();

      // 将响应内容复制回原始响应流
      responseMemoryStream.Position = 0;
      await responseMemoryStream.CopyToAsync(originalResponseBody);

      isSuccess = context.Response.StatusCode >= 200 && context.Response.StatusCode < 400;
    }
    catch (Exception ex)
    {
      stopwatch.Stop();
      _logger.LogError(ex, "处理开放API请求时发生异常");

      context.Response.StatusCode = StatusCodes.Status500InternalServerError;
      await WriteResponseAsync(context, "处理请求时发生服务器错误");

      isSuccess = false;
      responseBody = $"处理请求时发生服务器错误: {ex.Message}";
    }
    finally
    {
      // 恢复原始响应流
      context.Response.Body = originalResponseBody;

      // 记录API调用日志（如果有API密钥ID）
      if (apiKeyId.HasValue)
      {
        try
        {
          var apiKeyService = context.RequestServices.GetRequiredService<IApiKeyService>();

          apiCallLog = new ApiCallLog
          {
            ApiKeyId = apiKeyId.Value,
            CallerIp = context.Connection.RemoteIpAddress?.ToString(),
            RequestPath = context.Request.Path.Value,
            HttpMethod = context.Request.Method,
            RequestParams = requestBody,
            RequestHeaders = GetRequestHeaders(context),
            StatusCode = context.Response.StatusCode,
            ResponseContent = responseBody,
            ExecutionTime = stopwatch.ElapsedMilliseconds,
            CallTime = DateTime.Now,
            IsSuccess = isSuccess,
            ErrorMessage = isSuccess ? null : responseBody
          };

          await apiKeyService.LogApiCallAsync(apiCallLog);
        }
        catch (Exception ex)
        {
          _logger.LogError(ex, "记录API调用日志时发生异常");
        }
      }

      // 如果响应体尚未写入，则写入
      if (context.Response.Body == originalResponseBody && !string.IsNullOrEmpty(responseBody))
      {
        await context.Response.WriteAsync(responseBody);
      }
    }
  }

  /// <summary>
  /// 检查是否是开放API请求
  /// </summary>
  /// <param name="context">HTTP上下文</param>
  /// <returns>是否是开放API请求</returns>
  private bool IsOpenApiRequest(HttpContext context)
  {
    // 检查是否以开放API前缀开头
    return context.Request.Path.Value.StartsWith(OpenApiConstants.OpenApiRoutePrefix, StringComparison.OrdinalIgnoreCase);
  }

  /// <summary>
  /// 检查是否存在OpenApi特性
  /// </summary>
  /// <param name="context">HTTP上下文</param>
  /// <param name="openApiAttr">OpenApi特性</param>
  /// <returns>是否存在OpenApi特性</returns>
  private bool HasOpenApiAttribute(HttpContext context, out OpenApiAttribute openApiAttr)
  {
    openApiAttr = null;

    var endpoint = context.GetEndpoint();
    if (endpoint == null)
    {
      return false;
    }

    var controllerActionDescriptor = endpoint.Metadata.GetMetadata<ControllerActionDescriptor>();
    if (controllerActionDescriptor == null)
    {
      return false;
    }

    // 检查方法上的特性
    openApiAttr = controllerActionDescriptor.MethodInfo.GetCustomAttribute<OpenApiAttribute>();

    // 如果方法上没有特性，则检查控制器上的特性
    if (openApiAttr == null)
    {
      openApiAttr = controllerActionDescriptor.ControllerTypeInfo.GetCustomAttribute<OpenApiAttribute>();
    }

    return openApiAttr != null;
  }

  /// <summary>
  /// 尝试获取API密钥和密钥
  /// </summary>
  /// <param name="context">HTTP上下文</param>
  /// <param name="apiKey">API密钥</param>
  /// <param name="apiSecret">API密钥密文</param>
  /// <returns>是否获取成功</returns>
  private bool TryGetApiKeyAndSecret(HttpContext context, out string apiKey, out string apiSecret)
  {
    apiKey = null;
    apiSecret = null;

    // 从请求头中获取
    if (context.Request.Headers.TryGetValue(OpenApiConstants.ApiKeyHeaderName, out var headerApiKey) &&
        context.Request.Headers.TryGetValue(OpenApiConstants.ApiSecretHeaderName, out var headerApiSecret))
    {
      apiKey = headerApiKey;
      apiSecret = headerApiSecret;
      return !string.IsNullOrEmpty(apiKey) && !string.IsNullOrEmpty(apiSecret);
    }

    // 从查询字符串中获取
    if (context.Request.Query.TryGetValue(OpenApiConstants.ApiKeyQueryName, out var queryApiKey) &&
        context.Request.Query.TryGetValue(OpenApiConstants.ApiSecretQueryName, out var queryApiSecret))
    {
      apiKey = queryApiKey;
      apiSecret = queryApiSecret;
      return !string.IsNullOrEmpty(apiKey) && !string.IsNullOrEmpty(apiSecret);
    }

    // 从Authorization头中获取（Basic认证）
    if (context.Request.Headers.TryGetValue("Authorization", out var authHeader))
    {
      var authHeaderValue = authHeader.ToString();
      if (authHeaderValue.StartsWith("Basic ", StringComparison.OrdinalIgnoreCase))
      {
        var encodedCredentials = authHeaderValue.Substring("Basic ".Length).Trim();
        var decodedCredentials = Encoding.UTF8.GetString(Convert.FromBase64String(encodedCredentials));
        var credentials = decodedCredentials.Split(':', 2);
        if (credentials.Length == 2)
        {
          apiKey = credentials[0];
          apiSecret = credentials[1];
          return !string.IsNullOrEmpty(apiKey) && !string.IsNullOrEmpty(apiSecret);
        }
      }
    }

    return false;
  }

  /// <summary>
  /// 获取请求头信息
  /// </summary>
  /// <param name="context">HTTP上下文</param>
  /// <returns>请求头信息</returns>
  private string GetRequestHeaders(HttpContext context)
  {
    var headers = new Dictionary<string, string>();
    foreach (var header in context.Request.Headers)
    {
      headers[header.Key] = header.Value;
    }
    return JsonSerializer.Serialize(headers);
  }

  /// <summary>
  /// 写入响应
  /// </summary>
  /// <param name="context">HTTP上下文</param>
  /// <param name="message">消息</param>
  /// <returns></returns>
  private async Task WriteResponseAsync(HttpContext context, string message)
  {
    context.Response.ContentType = "application/json";
    var response = new
    {
      success = false,
      message
    };
    await context.Response.WriteAsync(JsonSerializer.Serialize(response));
  }
}