using EdgeGateway.SqlSugar.Entity;

namespace EdgeGateway.Base.OpenApi.Entity;

/// <summary>
///     API密钥实体
///     用于存储外部调用者的API密钥信息
/// </summary>
[SugarTable("sys_api_key")]
public class ApiKey : EntityBaseId
{
  /// <summary>
  ///     密钥名称
  /// </summary>
  [SugarColumn(Length = 100)]
    public string Name { get; set; }

  /// <summary>
  ///     密钥
  /// </summary>
  [SugarColumn(Length = 64)]
    public string Key { get; set; }

  /// <summary>
  ///     密钥密文
  /// </summary>
  [SugarColumn(Length = 64)]
    public string Secret { get; set; }

  /// <summary>
  ///     创建者
  /// </summary>
  [SugarColumn(Length = 50)]
    public string Creator { get; set; }

  /// <summary>
  ///     密钥状态（0-禁用，1-启用）
  /// </summary>
  public int Status { get; set; } = 1;

  /// <summary>
  ///     允许的IP白名单，多个IP用逗号分隔
  /// </summary>
  [SugarColumn(Length = 500)]
    public string AllowedIps { get; set; }

  /// <summary>
  ///     过期时间
  /// </summary>
  public DateTime? ExpireTime { get; set; }

  /// <summary>
  ///     备注
  /// </summary>
  [SugarColumn(Length = 500)]
    public string Remark { get; set; }

  /// <summary>
  ///     创建时间
  /// </summary>
  public DateTime CreateTime { get; set; } = DateTime.Now;

  /// <summary>
  ///     更新时间
  /// </summary>
  public DateTime? UpdateTime { get; set; }

  /// <summary>
  ///     最后访问时间
  /// </summary>
  public DateTime? LastAccessTime { get; set; }

  /// <summary>
  ///     调用次数
  /// </summary>
  public long CallCount { get; set; } = 0;
}