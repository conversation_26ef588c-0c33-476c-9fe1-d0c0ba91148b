global using Furion.DependencyInjection;
global using Furion.DynamicApiController;
global using Furion.FriendlyException;
global using Mapster;
global using Microsoft.AspNetCore.Authorization;
global using Microsoft.AspNetCore.Mvc;
global using System.ComponentModel;
global using EdgeGateway.Core.Const;
global using EdgeGateway.SqlSugar.SqlSugar;
global using EdgeGateway.Base.Entity.Entity;
global using EdgeGateway.Core;
global using System.ComponentModel.DataAnnotations;
global using EdgeGateway.Base.Auth.Dto;
global using EdgeGateway.Base.User;
global using Furion.DataEncryption;
global using Furion.SpecificationDocument;
global using Microsoft.AspNetCore.Http;
global using EdgeGateway.Core.Configuration;
global using EdgeGateway.Base.Entity.Dto;
global using System.IO.Ports;
global using System.Text;
global using EdgeGateway.Base.Entity.Model;
global using EdgeGateway.WebSocket;
global using Microsoft.Extensions.Logging;
global using SharpPcap;
global using PacketDotNet;
global using System.Collections.Concurrent;
global using System.Net.NetworkInformation;
global using System.Net;
global using SqlSugar;
global using System.Diagnostics;
global using System.Net.Sockets;
global using System.Text.Json;
global using System.Security.Cryptography;
