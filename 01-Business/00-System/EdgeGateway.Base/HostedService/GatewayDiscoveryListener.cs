using EdgeGateway.Base.Service.Config;
using Microsoft.Extensions.Hosting;

/// <summary>
///     网关发现监听服务
///     用于处理网关设备的自动发现和注册
/// </summary>
public class GatewayDiscoveryListener : IHostedService
{
    /// <summary>
    ///     UDP监听端口号
    /// </summary>
    private const int UDP_PORT = 9999;

    /// <summary>
    /// 日志记录器
    /// </summary>
    private readonly ILogger<GatewayDiscoveryListener> _logger;
    /// <summary>
    /// 网关配置服务
    /// </summary>
    private readonly GatewayConfigurationService _configService;

    /// <summary>
    ///     用于数据签名的密钥
    /// </summary>
    private readonly string _secretKey = "YOUR_SECRET_KEY";

    /// <summary>
    ///     UDP客户端实例
    /// </summary>
    private readonly UdpClient _udpClient;

    /// <summary>
    ///     取消令牌源
    /// </summary>
    private CancellationTokenSource _cts;

    /// <summary>
    ///     监听任务
    /// </summary>
    private Task _listeningTask;

    /// <summary>
    ///     构造函数
    /// </summary>
    /// <param name="logger">日志记录器</param>
    /// <param name="configService">网关配置服务</param>
    public GatewayDiscoveryListener(
        ILogger<GatewayDiscoveryListener> logger,
        GatewayConfigurationService configService)
    {
        _logger = logger;
        _configService = configService;
        _udpClient = new UdpClient(UDP_PORT);
    }

    /// <summary>
    ///     启动监听服务
    /// </summary>
    /// <param name="cancellationToken">取消令牌</param>
    /// <returns>任务</returns>
    public Task StartAsync(CancellationToken cancellationToken)
    {
        // 创建取消令牌源
        _cts = CancellationTokenSource.CreateLinkedTokenSource(cancellationToken);
        // 启动监听任务
        _listeningTask = StartListeningAsync(_cts.Token);
        return Task.CompletedTask;
    }

    /// <summary>
    ///     停止监听服务
    /// </summary>
    /// <param name="cancellationToken">取消令牌</param>
    /// <returns>任务</returns>
    public Task StopAsync(CancellationToken cancellationToken)
    {
        _cts?.Cancel();
        _listeningTask?.Wait();
        _listeningTask?.Dispose();
        _udpClient?.Dispose();
        return Task.CompletedTask;
    }

    /// <summary>
    ///     启动监听服务
    /// </summary>
    /// <param name="cancellationToken">取消令牌</param>
    private async Task StartListeningAsync(CancellationToken cancellationToken)
    {
        try
        {
            // 循环处理接收到的数据
            while (!cancellationToken.IsCancellationRequested)
            {
                // 接收数据
                var result = await _udpClient.ReceiveAsync(cancellationToken);
                var message = Encoding.UTF8.GetString(result.Buffer);

                try
                {
                    // 反序列化请求数据
                    var discoveryRequest = JsonSerializer.Deserialize<DiscoveryRequest>(message);
                    // 验证请求数据
                    if (IsValidRequest(discoveryRequest))
                    {
                        // 构建网关信息
                        var gatewayInfo = await BuildGatewayInfoAsync();
                        // 序列化网关信息
                        var response = JsonSerializer.Serialize(gatewayInfo);
                        // 发送响应数据
                        var responseBytes = Encoding.UTF8.GetBytes(response);
                        await _udpClient.SendAsync(responseBytes, responseBytes.Length, result.RemoteEndPoint);
                    }
                }
                catch (JsonException)
                {
                    _logger.LogWarning("收到无效的发现请求");
                }
            }
        }
        catch (Exception ex) when (ex is not OperationCanceledException)
        {
            _logger.LogError(ex, "网关发现监听服务发生错误");
        }
    }

    /// <summary>
    ///     构建网关信息响应
    /// </summary>
    /// <returns>包含网关详细信息的对象</returns>
    private async Task<GatewayInfo> BuildGatewayInfoAsync()
    {
        // 获取持久化的配置信息
        var config = await _configService.GetConfigurationAsync();

        // 构建网关信息
        var info = new GatewayInfo
        {
            Id = config.Id, // 网关唯一标识符
            Name = config.Name, // 网关名称
            IP = GetLocalIPAddress(), // 获取本机IPv4地址
            Version = config.Version, // 网关软件版本
            MAC = GetMACAddress(), // 获取本机MAC地址
            LastActiveTime = DateTime.UtcNow, // 最后活动时间
            Properties = GetGatewayProperties() // 获取网关的系统属性信息
        };

        // 添加签名
        var dataToSign = $"{info.Id}{info.IP}{info.Version}";
        // 计算签名
        info.Signature = ComputeHMAC(dataToSign, _secretKey);

        // 记录日志
        _logger.LogInformation("网关发现响应: {GatewayInfo}", JsonSerializer.Serialize(info));

        return info;
    }

    /// <summary>
    ///     获取网关的系统属性信息
    /// </summary>
    /// <returns>包含系统属性的字典</returns>
    private Dictionary<string, string> GetGatewayProperties()
    {
        // 构建系统属性字典
        return new Dictionary<string, string>
        {
            { "CPU", Environment.ProcessorCount.ToString() }, // 获取CPU核心数
            { "OS", Environment.OSVersion.ToString() }, // 获取操作系统版本
            { "Memory", GetAvailableMemory() } // 获取当前可用内存信息
        };
    }

    /// <summary>
    ///     获取本机IPv4地址
    /// </summary>
    /// <returns>本机IPv4地址字符串</returns>
    private string GetLocalIPAddress()
    {
        // 获取所有网络接口
        var interfaces = NetworkInterface.GetAllNetworkInterfaces();
        // 获取第一个非回环的网络接口
        return interfaces
            .FirstOrDefault(n =>
                n.OperationalStatus == OperationalStatus.Up && // 接口状态为Up
                n.NetworkInterfaceType != NetworkInterfaceType.Loopback) // 接口类型不为Loopback
            ?.GetIPProperties() // 获取接口的IP属性
            ?.UnicastAddresses // 获取接口的单播地址
            ?.FirstOrDefault(a => a.Address.AddressFamily == AddressFamily.InterNetwork) // 获取第一个IPv4地址
            ?.Address.ToString(); // 返回IPv4地址
    }

    /// <summary>
    ///     获取本机MAC地址
    /// </summary>
    /// <returns>MAC地址字符串</returns>
    private string GetMACAddress()
    {
        // 获取所有网络接口
        var interfaces = NetworkInterface.GetAllNetworkInterfaces();
        // 获取第一个非回环的网络接口
        return interfaces
            .FirstOrDefault(n =>
                n.OperationalStatus == OperationalStatus.Up &&
                n.NetworkInterfaceType != NetworkInterfaceType.Loopback)
            ?.GetPhysicalAddress()
            ?.ToString();
    }

    /// <summary>
    ///     计算HMAC签名
    /// </summary>
    /// <param name="data">要签名的数据</param>
    /// <param name="key">签名密钥</param>
    /// <returns>Base64编码的签名字符串</returns>
    private string ComputeHMAC(string data, string key)
    {
        // 使用HMACSHA256算法计算签名
        using var hmac = new HMACSHA256(Encoding.UTF8.GetBytes(key));
        var hash = hmac.ComputeHash(Encoding.UTF8.GetBytes(data));
        // 返回Base64编码的签名字符串
        return Convert.ToBase64String(hash);
    }

    /// <summary>
    ///     获取当前可用内存信息
    /// </summary>
    /// <returns>格式化的内存使用情况字符串</returns>
    private string GetAvailableMemory()
    {
        // 获取当前可用内存信息
        var totalMemory = GC.GetTotalMemory(false);
        var availableMemory = GC.GetTotalMemory(true);
        // 返回格式化的内存使用情况字符串
        return $"{availableMemory} / {totalMemory} bytes";
    }

    /// <summary>
    ///     验证发现请求的合法性
    /// </summary>
    /// <param name="request">发现请求对象</param>
    /// <returns>如果请求有效则返回true，否则返回false</returns>
    private bool IsValidRequest(DiscoveryRequest request)
    {
        // 实现签名验证逻辑
        var dataToSign = $"{request.Id}{request.IP}{request.Version}";
        var expectedSignature = ComputeHMAC(dataToSign, _secretKey);
        return request.Signature == expectedSignature;
    }
}

/// <summary>
///     网关信息模型
///     用于描述网关的基本信息和状态
/// </summary>
public class GatewayInfo
{
    /// <summary>
    ///     网关唯一标识符
    /// </summary>
    public string Id { get; set; }

    /// <summary>
    ///     网关名称
    /// </summary>
    public string Name { get; set; }

    /// <summary>
    ///     网关IP地址
    /// </summary>
    public string IP { get; set; }

    /// <summary>
    ///     网关软件版本
    /// </summary>
    public string Version { get; set; }

    /// <summary>
    ///     网关MAC地址
    /// </summary>
    public string MAC { get; set; }

    /// <summary>
    ///     最后活动时间
    /// </summary>
    public DateTime LastActiveTime { get; set; }

    /// <summary>
    ///     数据签名，用于验证数据完整性
    /// </summary>
    public string Signature { get; set; }

    /// <summary>
    ///     网关附加属性集合
    /// </summary>
    public Dictionary<string, string> Properties { get; set; }
}

/// <summary>
///     网关发现请求模型
///     用于客户端发送网关发现请求时传递必要的信息
/// </summary>
public class DiscoveryRequest
{
    /// <summary>
    ///     请求唯一标识符
    /// </summary>
    public string Id { get; set; }

    /// <summary>
    ///     请求方IP地址
    /// </summary>
    public string IP { get; set; }

    /// <summary>
    ///     请求方版本号
    /// </summary>
    public string Version { get; set; }

    /// <summary>
    ///     请求数据签名，用于验证请求的合法性
    /// </summary>
    public string Signature { get; set; }
}