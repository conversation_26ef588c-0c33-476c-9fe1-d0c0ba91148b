using EdgeGateway.Base.Entity.Dto.Database;

namespace EdgeGateway.Base.Database;

/// <summary>
/// 数据库管理服务
/// </summary>
public interface IDatabaseManagerService
{
  /// <summary>
  /// 获取数据库列表
  /// </summary>
  Task<List<DatabaseDto>> GetDatabasesAsync();

  /// <summary>
  /// 获取数据表列表
  /// </summary>
  /// <param name="database">数据库名</param>
  Task<List<TableDto>> GetTablesAsync(string database);

  /// <summary>
  /// 获取表字段信息
  /// </summary>
  /// <param name="database">数据库名</param>
  /// <param name="table">表名</param>
  Task<List<ColumnDto>> GetColumnsAsync(string database, string table);

  /// <summary>
  /// 查询表数据
  /// </summary>
  /// <param name="input">查询条件</param>
  Task<SqlSugarPagedList<dynamic>> GetTableDataAsync(TableDataInput input);

  /// <summary>
  /// 执行SQL语句
  /// </summary>
  /// <param name="input">SQL语句</param>
  Task<dynamic> ExecuteSqlAsync(ExecuteSqlInput input);

  /// <summary>
  /// 备份数据库
  /// </summary>
  /// <param name="input">备份参数</param>
  Task BackupDatabaseAsync(BackupDatabaseInput input);

  /// <summary>
  /// 获取数据库备份记录
  /// </summary>
  Task<SqlSugarPagedList<DatabaseBackup>> GetDatabaseBackupsAsync(DatabaseBackupInput input);

  /// <summary>
  /// 下载备份文件
  /// </summary>
  Task<(byte[] FileContent, string FileName)> DownloadBackupAsync(long backupId);

  /// <summary>
  /// 还原数据库
  /// </summary>
  Task RestoreDatabaseAsync(long backupId);

  /// <summary>
  /// 获取备份配置
  /// </summary>
  Task<DatabaseBackupConfig> GetBackupConfigAsync();

  /// <summary>
  /// 更新备份配置
  /// </summary>
  Task UpdateBackupConfigAsync(DatabaseBackupConfig config);
}