using System.Net.Http.Json;
using System.Security.Cryptography;
using System.Text.Json;
using ICSharpCode.SharpZipLib.Zip;
using Microsoft.Extensions.Configuration;
using Microsoft.Extensions.Logging;

/// <summary>
///     系统更新服务，负责处理系统更新包的上传、验证、安装和回滚
/// </summary>
public class SystemUpdateService : ISystemUpdateService
{
    /// <summary>
    /// 日志服务
    /// </summary>
    private readonly ILogger<SystemUpdateService> _logger;

    /// <summary>
    /// 更新包加密服务
    /// </summary>
    private readonly IUpdatePackageEncryption _encryption;

    /// <summary>
    /// 备份路径
    /// </summary>
    private readonly string _backupPath;

    /// <summary>
    /// 配置服务
    /// </summary>
    private readonly IConfiguration _configuration;

    /// <summary>
    /// 当前版本
    /// </summary>
    private readonly string _currentVersion;

    /// <summary>
    /// 更新路径
    /// </summary>
    private readonly string _updatePath;

    /// <summary>
    ///     构造函数
    /// </summary>
    /// <param name="logger">日志服务</param>
    /// <param name="encryption">更新包加密服务</param>
    /// <param name="configuration">配置服务</param>
    public SystemUpdateService(
        ILogger<SystemUpdateService> logger,
        IUpdatePackageEncryption encryption,
        IConfiguration configuration)
    {
        _logger = logger;
        _encryption = encryption;
        _backupPath = Path.Combine(AppContext.BaseDirectory, "Backups");
        _updatePath = Path.Combine(AppContext.BaseDirectory, "Updates");
        _currentVersion = configuration["App:Version"] ?? "1.0.0";
        _configuration = configuration;

        // 确保备份和更新目录存在
        Directory.CreateDirectory(_backupPath);
        Directory.CreateDirectory(_updatePath);
    }

    /// <summary>
    ///     检查是否有新版本可用
    /// </summary>
    /// <returns>更新检查结果，包含是否有更新和最新版本信息</returns>
    public async Task<CheckUpdateOutput> CheckUpdateAsync()
    {
        try
        {
            // 1. 从配置获取更新服务器地址
            var updateApiUrl = _configuration["Update:RemoteApiUrl"]
                               ?? throw new ArgumentNullException("未配置更新服务器地址");

            // 2. 调用远程API获取最新版本信息
            using var httpClient = new HttpClient();
            var response = await httpClient.GetAsync($"{updateApiUrl}/latest");
            response.EnsureSuccessStatusCode();

            var latestVersion = await response.Content.ReadFromJsonAsync<UpdatePackage>();
            if (latestVersion == null) throw new Exception("无法获取最新版本信息");

            // 3. 比对版本号
            var currentVersion = Version.Parse(_currentVersion);
            var newVersion = Version.Parse(latestVersion.Version);

            return new CheckUpdateOutput
            {
                HasUpdate = newVersion > currentVersion,
                LatestVersion = latestVersion
            };
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "检查更新失败");
            throw;
        }
    }

    /// <summary>
    ///     上传并安装更新包
    /// </summary>
    /// <param name="file">更新包文件</param>
    /// <returns>更新是否成功</returns>
    public async Task<bool> UploadUpdatePackageAsync(IFormFile file)
    {
        try
        {
            // 1. 验证文件格式和大小
            if (!ValidateUpdatePackage(file)) throw Oops.Oh("无效的更新包格式");

            // 2. 解密更新包
            var decryptedPath = await DecryptUpdatePackageAsync(file);

            // 3. 验证更新包完整性
            if (!ValidatePackageIntegrity(decryptedPath)) throw Oops.Oh("更新包完整性验证失败");

            // 4. 备份当前系统
            await BackupCurrentSystemAsync();

            // 5. 执行更新
            await ExecuteUpdateAsync(decryptedPath);

            return true;
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "更新失败");
            // 6. 如果更新失败，执行回滚
            await RollbackAsync();
            throw;
        }
    }

    /// <summary>
    ///     从远程URL下载并安装更新包
    /// </summary>
    /// <param name="downloadUrl">更新包下载地址</param>
    /// <returns>更新是否成功</returns>
    public async Task<bool> ExecuteRemoteUpdateAsync(string downloadUrl)
    {
        var tempFile = Path.Combine(_updatePath, $"download_{Guid.NewGuid():N}.upkg");
        try
        {
            // 1. 下载更新包
            using (var httpClient = new HttpClient())
            using (var response = await httpClient.GetAsync(downloadUrl))
            {
                if (!response.IsSuccessStatusCode) throw Oops.Oh($"下载更新包失败: {response.StatusCode}");

                using var fileStream = File.Create(tempFile);
                await response.Content.CopyToAsync(fileStream);
            }

            // 2. 将下载的文件转换为IFormFile
            using var fileContent = new FileStream(tempFile, FileMode.Open);
            var formFile = new FormFile(fileContent, 0, fileContent.Length, "update", Path.GetFileName(tempFile));

            // 3. 使用现有的上传更新逻辑处理
            return await UploadUpdatePackageAsync(formFile);
        }
        finally
        {
            if (File.Exists(tempFile)) File.Delete(tempFile);
        }
    }

    /// <summary>
    ///     验证更新包的基本格式和大小
    /// </summary>
    /// <param name="file">更新包文件</param>
    /// <returns>验证是否通过</returns>
    private bool ValidateUpdatePackage(IFormFile file)
    {
        // 验证文件大小（限制100MB）
        if (file.Length > 100 * 1024 * 1024) return false;

        // 验证文件扩展名（必须是.upkg）
        var extension = Path.GetExtension(file.FileName).ToLower();
        if (extension != ".upkg") return false;

        return true;
    }

    /// <summary>
    ///     解密更新包并保存到临时文件
    /// </summary>
    /// <param name="file">加密的更新包文件</param>
    /// <returns>解密后的文件路径</returns>
    private async Task<string> DecryptUpdatePackageAsync(IFormFile file)
    {
        var tempPath = Path.Combine(_updatePath, $"temp_{Guid.NewGuid():N}.zip");

        try
        {
            // 1. 读取加密文件到内存
            using (var ms = new MemoryStream())
            {
                await file.CopyToAsync(ms);
                var encryptedData = ms.ToArray();

                // 2. 解密数据
                var decryptedData = _encryption.Decrypt(encryptedData);

                // 3. 保存解密后的文件
                await File.WriteAllBytesAsync(tempPath, decryptedData);
            }

            return tempPath;
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "解密更新包失败");
            if (File.Exists(tempPath)) File.Delete(tempPath);
            throw Oops.Oh("解密更新包失败");
        }
    }

    /// <summary>
    ///     验证更新包的完整性，包括文件结构和MD5校验
    /// </summary>
    /// <param name="packagePath">更新包路径</param>
    /// <returns>验证是否通过</returns>
    private bool ValidatePackageIntegrity(string packagePath)
    {
        try
        {
            using var zip = new ZipFile(packagePath);

            // 1. 检查必要文件是否存在
            if (zip.FindEntry("manifest.json", true) == -1 || zip.FindEntry("version", true) == -1) return false;

            // 2. 读取并验证 manifest.json
            var manifestEntry = zip.GetEntry("manifest.json");
            using var manifestStream = zip.GetInputStream(manifestEntry);
            using var reader = new StreamReader(manifestStream);
            var manifest = JsonSerializer.Deserialize<UpdatePackage>(reader.ReadToEnd());

            if (manifest == null) return false;

            // 3. 验证整个包的MD5
            using (var fs = File.OpenRead(packagePath))
            {
                var actualMd5 = CalculateMD5(fs);
                if (actualMd5 != manifest.Md5)
                {
                    _logger.LogWarning($"更新包MD5校验失败: 期望={manifest.Md5}, 实际={actualMd5}");
                    return false;
                }
            }

            return true;
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "验证更新包完整性失败");
            return false;
        }
    }

    /// <summary>
    ///     备份当前系统，用于更新失败时的回滚
    /// </summary>
    private async Task BackupCurrentSystemAsync()
    {
        var backupName = $"backup_{DateTime.Now:yyyyMMddHHmmss}";
        var backupDir = Path.Combine(_backupPath, backupName);

        try
        {
            // 1. 创建备份目录
            Directory.CreateDirectory(backupDir);

            // 2. 复制当前系统文件
            var sourceDir = AppContext.BaseDirectory;
            await CopyDirectoryAsync(sourceDir, backupDir);

            // 3. 保存版本信息
            await File.WriteAllTextAsync(
                Path.Combine(backupDir, "version"),
                _currentVersion
            );
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "备份系统失败");
            throw Oops.Oh("备份系统失败");
        }
    }

    /// <summary>
    ///     执行系统回滚，恢复到最近的备份版本
    /// </summary>
    private async Task RollbackAsync()
    {
        try
        {
            // 1. 获取最新的备份
            var latestBackup = Directory
                .GetDirectories(_backupPath)
                .OrderByDescending(d => d)
                .FirstOrDefault();

            if (latestBackup == null) throw new Exception("未找到可用的备份");

            // 2. 恢复文件
            var sourceDir = AppContext.BaseDirectory;
            await CopyDirectoryAsync(latestBackup, sourceDir);

            _logger.LogInformation("系统已回滚到备份版本");
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "系统回滚失败");
            throw Oops.Oh("系统回滚失败");
        }
    }

    /// <summary>
    ///     递归复制目录及其内容
    /// </summary>
    /// <param name="sourceDir">源目录</param>
    /// <param name="destDir">目标目录</param>
    private async Task CopyDirectoryAsync(string sourceDir, string destDir)
    {
        foreach (var file in Directory.GetFiles(sourceDir, "*", SearchOption.AllDirectories))
        {
            var relativePath = Path.GetRelativePath(sourceDir, file);
            var destFile = Path.Combine(destDir, relativePath);

            Directory.CreateDirectory(Path.GetDirectoryName(destFile)!);
            File.Copy(file, destFile, true);
        }
    }

    /// <summary>
    ///     计算文件的MD5值
    /// </summary>
    /// <param name="stream">文件流</param>
    /// <returns>MD5哈希值的十六进制字符串</returns>
    private string CalculateMD5(Stream stream)
    {
        using var md5 = MD5.Create();
        var hash = md5.ComputeHash(stream);
        return BitConverter.ToString(hash).Replace("-", "").ToLowerInvariant();
    }

    /// <summary>
    ///     执行更新包的安装
    /// </summary>
    /// <param name="packagePath">更新包路径</param>
    private async Task ExecuteUpdateAsync(string packagePath)
    {
        try
        {
            using var zip = new ZipFile(packagePath);

            // 1. 读取manifest获取更新类型
            var manifestEntry = zip.GetEntry("manifest.json");
            using var manifestStream = zip.GetInputStream(manifestEntry);
            using var reader = new StreamReader(manifestStream);
            var manifest = JsonSerializer.Deserialize<UpdatePackage>(reader.ReadToEnd());

            // 2. 执行更新前脚本
            if (zip.FindEntry("scripts/pre-update.sh", true) != -1)
                await ExecuteUpdateScript(zip, "scripts/pre-update.sh");

            // 3. 根据更新类型执行更新
            switch (manifest.Type)
            {
                case UpdatePackageType.Frontend:
                    await UpdateFrontend(zip);
                    break;
                case UpdatePackageType.Backend:
                    await UpdateBackend(zip);
                    break;
                case UpdatePackageType.Full:
                    await UpdateFrontend(zip);
                    await UpdateBackend(zip);
                    break;
            }

            // 4. 执行更新后脚本
            if (zip.FindEntry("scripts/post-update.sh", true) != -1)
                await ExecuteUpdateScript(zip, "scripts/post-update.sh");

            // 5. 更新版本信息
            await File.WriteAllTextAsync(
                Path.Combine(AppContext.BaseDirectory, "version"),
                manifest.Version
            );

            _logger.LogInformation($"系统已更新到版本 {manifest.Version}");
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "执行更新失败");
            throw;
        }
        finally
        {
            // 清理临时文件
            if (File.Exists(packagePath)) File.Delete(packagePath);
        }
    }

    /// <summary>
    ///     更新前端文件
    /// </summary>
    /// <param name="zip">更新包zip文件</param>
    private async Task UpdateFrontend(ZipFile zip)
    {
        var frontendPath = Path.Combine(AppContext.BaseDirectory, "Configs");
        await ExtractFilesToDirectory(zip, "frontend/", frontendPath);
    }

    /// <summary>
    ///     更新后端文件
    /// </summary>
    /// <param name="zip">更新包zip文件</param>
    private async Task UpdateBackend(ZipFile zip)
    {
        var backendPath = AppContext.BaseDirectory;
        await ExtractFilesToDirectory(zip, "backend/", backendPath);
    }

    /// <summary>
    ///     从zip文件中提取指定前缀的文件到目标目录
    /// </summary>
    /// <param name="zip">zip文件</param>
    /// <param name="prefix">文件前缀</param>
    /// <param name="targetPath">目标目录</param>
    private async Task ExtractFilesToDirectory(ZipFile zip, string prefix, string targetPath)
    {
        foreach (var entry in zip)
            if (entry is ZipEntry zipEntry &&
                zipEntry.Name.StartsWith(prefix, StringComparison.OrdinalIgnoreCase))
            {
                var relativePath = zipEntry.Name[prefix.Length..];
                var targetFile = Path.Combine(targetPath, relativePath);

                Directory.CreateDirectory(Path.GetDirectoryName(targetFile)!);

                using var zipStream = zip.GetInputStream(zipEntry);
                using var fileStream = File.Create(targetFile);
                await zipStream.CopyToAsync(fileStream);
            }
    }

    /// <summary>
    ///     执行更新脚本
    /// </summary>
    /// <param name="zip">zip文件</param>
    /// <param name="scriptPath">脚本路径</param>
    private async Task ExecuteUpdateScript(ZipFile zip, string scriptPath)
    {
        var entry = zip.GetEntry(scriptPath);
        if (entry == null) return;

        var tempScript = Path.Combine(Path.GetTempPath(), Path.GetFileName(scriptPath));
        try
        {
            // 提取脚本到临时文件
            using (var zipStream = zip.GetInputStream(entry))
            using (var fileStream = File.Create(tempScript))
            {
                await zipStream.CopyToAsync(fileStream);
            }

            // 设置执行权限（仅在Linux/MacOS上）
            if (OperatingSystem.IsLinux() || OperatingSystem.IsMacOS())
                await ProcessExtensions.ExecuteAsync("chmod", $"+x {tempScript}");

            // 执行脚本
            await ProcessExtensions.ExecuteAsync(tempScript, "");
        }
        finally
        {
            if (File.Exists(tempScript)) File.Delete(tempScript);
        }
    }
}