using System.Net.Http.Json;

/// <summary>
/// 默认 IP 定位提供者
/// </summary>
public class DefaultIPLocatorProvider : IIPLocatorProvider, ITransient
{
  /// <summary>
  /// Http 客户端工厂
  /// </summary>
  private readonly IHttpClientFactory _httpClientFactory;

  /// <summary>
  /// 构造函数
  /// </summary>
  /// <param name="httpClientFactory">Http 客户端工厂</param>
  public DefaultIPLocatorProvider(IHttpClientFactory httpClientFactory)
  {
    _httpClientFactory = httpClientFactory;
  }

  /// <summary>
  /// 获取 IP 定位信息
  /// </summary>
  /// <param name="ip">IP 地址</param>
  /// <returns>IP 定位信息</returns>
  public async Task<IPLocation> GetIPLocationAsync(string ip)
  {
    try
    {
      // 使用免费的 IP 查询 API
      using var client = _httpClientFactory.CreateClient();
      var response = await client.GetFromJsonAsync<IPLocation>($"http://ip-api.com/json/{ip}");
      return response;
    }
    catch
    {
      return new IPLocation { Country = "未知", Region = "", City = "" };
    }
  }
}