namespace EdgeGateway.Base.Network;

/// <summary>
/// 串口服务
/// </summary>
[ApiDescriptionSettings("网络工具")]
[Route("/api/system/network/serial")]
public class SerialPortService : ID<PERSON><PERSON><PERSON><PERSON>ontroller, ISingleton, IDisposable
{
  /// <summary>
  /// 日志
  /// </summary>
  private readonly ILogger<SerialPortService> _logger;
  /// <summary>
  /// 消息推送服务
  /// </summary>
  private readonly IMessagePushService _messagePushService;
  /// <summary>
  /// 当前串口
  /// </summary>
  private SerialPort _serialPort;
  /// <summary>
  /// 消息日志
  /// </summary>
  private readonly List<SerialMessageLog> _messageLogs;
  /// <summary>
  /// 锁对象
  /// </summary>
  private readonly object _lockObj = new();
  /// <summary>
  /// 是否打开
  /// </summary>
  private bool _isOpen;
  /// <summary>
  /// 当前配置
  /// </summary>
  private SerialPortConfig _currentConfig;

  /// <summary>
  /// 构造函数
  /// </summary>
  /// <param name="logger">日志</param>
  /// <param name="messagePushService">消息推送服务</param>
  public SerialPortService(
      ILogger<SerialPortService> logger,
      IMessagePushService messagePushService)
  {
    _logger = logger;
    _messagePushService = messagePushService;
    _messageLogs = new List<SerialMessageLog>();
  }

  /// <summary>
  /// 获取可用串口
  /// </summary>
  [HttpGet("ports")]
  [OperationId(nameof(GetPorts))]
  [DisplayName("获取可用串口")]
  public List<string> GetPorts()
  {
    return SerialPort.GetPortNames().ToList();
  }

  /// <summary>
  /// 打开串口
  /// </summary>
  [HttpPost("open")]
  [OperationId(nameof(OpenPort))]
  [DisplayName("打开串口")]
  public bool OpenPort([FromBody] SerialPortConfig config)
  {
    try
    {
      // 如果已打开且配置相同，直接返回
      if (_isOpen && ConfigEquals(config))
      {
        return true;
      }

      // 关闭现有连接
      ClosePort();

      _currentConfig = config;
      _serialPort = new SerialPort
      {
        PortName = config.PortName,
        BaudRate = config.BaudRate,
        DataBits = config.DataBits,
        StopBits = config.StopBits,
        Parity = config.Parity,
        Handshake = config.Handshake
      };

      // 注册数据接收事件
      _serialPort.DataReceived += SerialPort_DataReceived;
      _serialPort.Open();
      _isOpen = true;

      _logger.LogInformation($"已打开串口 {config.PortName}");
      return true;
    }
    catch (Exception ex)
    {
      _logger.LogError(ex, $"打开串口 {config.PortName} 失败");
      return false;
    }
  }

  /// <summary>
  /// 关闭串口
  /// </summary>
  [HttpPost("close")]
  [OperationId(nameof(ClosePort))]
  [DisplayName("关闭串口")]
  public void ClosePort()
  {
    if (!_isOpen)
    {
      return;
    }

    try
    {
      _serialPort?.Close();
      _serialPort?.Dispose();
      _serialPort = null;
      _isOpen = false;
      _currentConfig = null;
    }
    catch (Exception ex)
    {
      _logger.LogError(ex, "关闭串口时出错");
    }
  }

  /// <summary>
  /// 发送消息
  /// </summary>
  [HttpPost("send")]
  [OperationId(nameof(SendMessage))]
  [DisplayName("发送消息")]
  public bool SendMessage([FromBody] SerialMessage message)
  {
    if (!_isOpen)
    {
      throw new Exception("串口未打开");
    }

    try
    {
      // 转换为字节数组
      byte[] data;
      // 判断格式 
      if (message.Format.ToUpper() == "HEX")
      {
        // 十六进制转换
        data = HexToBytes(message.Content);
      }
      else
      {
        // 转换为ASCII
        data = Encoding.ASCII.GetBytes(message.Content);
      }

      // 追加结束符
      switch (message.Append.ToUpper())
      {
        case "CR":
          data = data.Concat(new byte[] { 0x0D }).ToArray();
          break;
        case "LF":
          data = data.Concat(new byte[] { 0x0A }).ToArray();
          break;
        case "CRLF":
          data = data.Concat(new byte[] { 0x0D, 0x0A }).ToArray();
          break;
      }

      // 发送数据
      _serialPort.Write(data, 0, data.Length);

      // 记录发送的消息
      LogMessage(MessageType.Send, message.Content, message.Format);
      return true;
    }
    catch (Exception ex)
    {
      _logger.LogError(ex, "发送消息失败");
      return false;
    }
  }

  /// <summary>
  /// 获取消息历史
  /// </summary>
  [HttpGet("history")]
  [OperationId(nameof(GetMessageHistory))]
  [DisplayName("获取消息历史")]
  public List<SerialMessageLog> GetMessageHistory()
  {
    return _messageLogs;
  }

  /// <summary>
  /// 清空消息历史
  /// </summary>
  [HttpPost("clear-history")]
  [OperationId(nameof(ClearMessageHistory))]
  [DisplayName("清空消息历史")]
  public void ClearMessageHistory()
  {
    _messageLogs.Clear();
  }

  /// <summary>
  /// 获取串口状态
  /// </summary>
  [HttpGet("status")]
  [OperationId(nameof(GetStatus))]
  [DisplayName("获取串口状态")]
  public object GetStatus()
  {
    return new
    {
      IsOpen = _isOpen,
      Config = _currentConfig
    };
  }

  /// <summary>
  /// 串口数据接收事件
  /// </summary>
  /// <param name="sender">发送者</param>
  /// <param name="e">事件参数</param>
  private void SerialPort_DataReceived(object sender, SerialDataReceivedEventArgs e)
  {
    try
    {
      // 读取接收缓冲区中的所有字节
      byte[] buffer = new byte[_serialPort.BytesToRead];
      _serialPort.Read(buffer, 0, buffer.Length);

      // 转换为ASCII和HEX格式
      var asciiContent = Encoding.ASCII.GetString(buffer);
      var hexContent = BitConverter.ToString(buffer);

      // 记录接收的消息
      LogMessage(MessageType.Receive, asciiContent, "ASCII");

      // 推送到前端
      _ = _messagePushService.PushMessageAsync("serial", new
      {
        type = "receive",
        ascii = asciiContent,
        hex = hexContent,
        timestamp = DateTime.Now
      });
    }
    catch (Exception ex)
    {
      _logger.LogError(ex, "接收数据时出错");
    }
  }

  /// <summary>
  /// 记录消息
  /// </summary>
  /// <param name="type">消息类型</param>
  /// <param name="content">消息内容</param>
  /// <param name="format">消息格式</param>
  private void LogMessage(MessageType type, string content, string format)
  {
    _messageLogs.Add(new SerialMessageLog
    {
      Type = type,
      Content = content,
      Format = format,
      Timestamp = DateTime.Now
    });
  }

  /// <summary>
  /// 配置是否相同
  /// </summary>
  /// <param name="config">配置</param>
  /// <returns>是否相同</returns>
  private bool ConfigEquals(SerialPortConfig config)
  {
    return _currentConfig != null &&
           _currentConfig.PortName == config.PortName &&
           _currentConfig.BaudRate == config.BaudRate &&
           _currentConfig.DataBits == config.DataBits &&
           _currentConfig.StopBits == config.StopBits &&
           _currentConfig.Parity == config.Parity &&
           _currentConfig.Handshake == config.Handshake;
  }

  /// <summary>
  /// 十六进制转换为字节数组
  /// </summary>
  /// <param name="hex">十六进制字符串</param>
  /// <returns>字节数组</returns>
  private static byte[] HexToBytes(string hex)
  {
    hex = hex.Replace(" ", "");
    return Enumerable.Range(0, hex.Length / 2)
        .Select(x => Convert.ToByte(hex.Substring(x * 2, 2), 16))
        .ToArray();
  }

  /// <summary>
  /// 释放资源
  /// </summary>
  public void Dispose()
  {
    ClosePort();
  }
}