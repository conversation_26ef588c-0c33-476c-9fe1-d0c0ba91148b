using System.Net.NetworkInformation;
using System.Runtime.InteropServices;
using System.Text.Json;
using Microsoft.Extensions.Logging;

/// <summary>
/// 网络配置服务
/// </summary>
[ApiDescriptionSettings("网络配置")]
[Route("/api/system/network")]
public class NetworkConfigService : ITransient, IDynamicApiController
{
  /// <summary>
  /// 日志
  /// </summary>
  private readonly ILogger<NetworkConfigService> _logger;
  /// <summary>
  /// 网络配置路径
  /// </summary>
  private readonly string _networkConfigPath;

  /// <summary>
  /// 构造函数
  /// </summary>
  /// <param name="logger"></param>
  public NetworkConfigService(ILogger<NetworkConfigService> logger)
  {
    _logger = logger;
    _networkConfigPath = Path.Combine(ConfigPath.ConfigRoot, "network.json");
  }

  /// <summary>
  /// 获取网络接口配置
  /// </summary>
  [DisplayName("获取网络接口配置")]
  [HttpGet("interfaces")]
  [OperationId(nameof(GetNetworkInterfacesAsync))]
  public async Task<List<NetworkInterfaceConfig>> GetNetworkInterfacesAsync()
  {
    var configs = new List<NetworkInterfaceConfig>();

    // 获取所有网络接口
    var interfaces = NetworkInterface.GetAllNetworkInterfaces()
        .Where(ni =>
            // 排除回环接口
            ni.NetworkInterfaceType != NetworkInterfaceType.Loopback &&
            // 排除虚拟网卡
            ni.NetworkInterfaceType != NetworkInterfaceType.Tunnel &&
            // 排除没有 IPv4 地址的接口
            ni.GetIPProperties().UnicastAddresses
                .Any(addr => addr.Address.AddressFamily == System.Net.Sockets.AddressFamily.InterNetwork) &&
            // 排除没有网关的接口
            ni.GetIPProperties().GatewayAddresses.Count > 0 &&
            // 排除特定描述的网卡
            !ni.Description.Contains("VirtualBox", StringComparison.OrdinalIgnoreCase) &&
            !ni.Description.Contains("VMware", StringComparison.OrdinalIgnoreCase) &&
            !ni.Description.Contains("Hyper-V", StringComparison.OrdinalIgnoreCase) &&
            // 保留以太网、无线网卡和移动网络
            (ni.NetworkInterfaceType == NetworkInterfaceType.Ethernet ||
             ni.NetworkInterfaceType == NetworkInterfaceType.Wireless80211 ||
             ni.NetworkInterfaceType == NetworkInterfaceType.Wwanpp ||
             ni.NetworkInterfaceType == NetworkInterfaceType.Wwanpp2));

    // 获取IPv4配置
    foreach (var ni in interfaces)
    {
      // 获取IPv4配置
      var ipProps = ni.GetIPProperties();
      var config = new NetworkInterfaceConfig
      {
        Name = ni.Name,// 接口名称
        Description = ni.Description,// 接口描述
        Type = ni.NetworkInterfaceType.ToString(),// 接口类型
        Status = ni.OperationalStatus.ToString(),// 接口状态
        MacAddress = ni.GetPhysicalAddress().ToString(),// 接口MAC地址
        IpConfigs = new List<IpConfig>(),// IPv4配置
        DnsServers = ipProps.DnsAddresses
            .Select(ip => ip.ToString())
            .ToList(),// DNS服务器
        Enabled = ni.OperationalStatus == OperationalStatus.Up// 接口是否启用
      };

      // 获取IPv4配置
      foreach (var ip in ipProps.UnicastAddresses)
      {
        // 排除IPv6地址
        if (ip.Address.AddressFamily == System.Net.Sockets.AddressFamily.InterNetwork)
        {
          // 获取网关地址
          var gateway = ipProps.GatewayAddresses.FirstOrDefault()?.Address.ToString();
          // 默认值
          var metric = 100;

          // 获取当前路由的metric
          if (!string.IsNullOrEmpty(gateway))
          {
            // 根据操作系统获取路由metric
            if (RuntimeInformation.IsOSPlatform(OSPlatform.Linux))
            {
              // 获取路由
              var (_, output) = await ProcessExtensions.ExecuteAsync("ip", "route show");
              // 分割路由
              var routes = output.Split('\n');
              // 遍历路由
              foreach (var route in routes)
              {
                // 检查路由是否包含网关和metric
                if (route.Contains(gateway) && route.Contains("metric"))
                {
                  // 分割路由
                  var parts = route.Split(' ');
                  // 获取metric索引
                  var metricIndex = Array.IndexOf(parts, "metric");
                  // 检查metric索引是否有效
                  if (metricIndex >= 0 && metricIndex + 1 < parts.Length)
                  {
                    // 转换metric
                    int.TryParse(parts[metricIndex + 1], out metric);
                  }
                }
              }
            }
          }

          // 添加IPv4配置
          config.IpConfigs.Add(new IpConfig
          {
            IpAddress = ip.Address.ToString(),// IPv4地址
            SubnetMask = ip.IPv4Mask.ToString(),// 子网掩码
            Gateway = gateway,// 网关地址
            Metric = metric// 路由metric
          });
        }
      }

      configs.Add(config);
    }

    return configs;
  }

  /// <summary>
  /// 更新网络接口配置
  /// </summary>
  [DisplayName("更新网络接口配置")]
  [HttpPost("interface")]
  [OperationId(nameof(UpdateNetworkConfigAsync))]
  public async Task UpdateNetworkConfigAsync(NetworkInterfaceConfig config)
  {
    try
    {
      // 读取现有配置
      var configs = await LoadNetworkConfigsAsync();

      // 更新或添加配置
      var existingConfig = configs.FirstOrDefault(c => c.Name == config.Name);
      if (existingConfig != null)
      {
        existingConfig.IpConfigs = config.IpConfigs;
      }
      else
      {
        configs.Add(config);
      }

      // 保存配置
      await SaveNetworkConfigsAsync(configs);
    }
    catch (Exception ex)
    {
      _logger.LogError(ex, "更新网络配置失败");
      throw Oops.Oh("更新网络配置失败");
    }
  }

  /// <summary>
  /// 应用网络配置
  /// </summary>
  [DisplayName("应用网络配置")]
  [HttpPost("apply/{interfaceName}")]
  [OperationId(nameof(ApplyNetworkConfigAsync))]
  public async Task ApplyNetworkConfigAsync(string interfaceName)
  {
    try
    {
      // 加载网络配置
      var configs = await LoadNetworkConfigsAsync();
      // 获取指定接口的配置
      var config = configs.FirstOrDefault(c => c.Name == interfaceName);
      if (config == null)
        throw Oops.Oh("未找到指定接口的配置");

      // 根据操作系统应用网络配置
      if (RuntimeInformation.IsOSPlatform(OSPlatform.Linux))
      {
        // 应用Linux网络配置
        await ApplyLinuxNetworkConfigAsync(config);
      }
      // 应用Windows网络配置
      else if (RuntimeInformation.IsOSPlatform(OSPlatform.Windows))
      {
        // 应用Windows网络配置
        await ApplyWindowsNetworkConfigAsync(config);
      }
      else
      {
        // 不支持的操作系统
        throw Oops.Oh("不支持的操作系统");
      }
    }
    catch (Exception ex)
    {
      _logger.LogError(ex, "应用网络配置失败");
      throw Oops.Oh("应用网络配置失败");
    }
  }

  /// <summary>
  /// 加载网络配置
  /// </summary>
  /// <returns></returns>
  private async Task<List<NetworkInterfaceConfig>> LoadNetworkConfigsAsync()
  {
    // 检查文件是否存在
    if (!File.Exists(_networkConfigPath))
      return new List<NetworkInterfaceConfig>();

    // 读取文件
    var json = await File.ReadAllTextAsync(_networkConfigPath);
    // 反序列化
    return JsonSerializer.Deserialize<List<NetworkInterfaceConfig>>(json)
           ?? new List<NetworkInterfaceConfig>();
  }

  /// <summary>
  /// 保存网络配置
  /// </summary>
  /// <param name="configs"></param>
  /// <returns></returns>
  private async Task SaveNetworkConfigsAsync(List<NetworkInterfaceConfig> configs)
  {
    var json = JsonSerializer.Serialize(configs, new JsonSerializerOptions
    {
      WriteIndented = true
    });
    await File.WriteAllTextAsync(_networkConfigPath, json);
  }

  /// <summary>
  /// 应用Linux网络配置 
  /// </summary>
  /// <param name="config"></param>
  /// <returns></returns>
  private async Task ApplyLinuxNetworkConfigAsync(NetworkInterfaceConfig config)
  {
    // 先设置接口状态
    var stateCommand = config.Enabled ? "up" : "down";
    await ProcessExtensions.ExecuteAsync("ip", $"link set {config.Name} {stateCommand}");

    if (config.Enabled)  // 只有在启用状态才配置IP和路由
    {
      // 先删除现有配置
      await ProcessExtensions.ExecuteAsync("ip", $"addr flush dev {config.Name}");
      await ProcessExtensions.ExecuteAsync("ip", $"route flush dev {config.Name}");

      foreach (var ip in config.IpConfigs)
      {
        // 配置IP地址
        await ProcessExtensions.ExecuteAsync("ip",
            $"addr add {ip.IpAddress}/{ip.SubnetMask} dev {config.Name}");

        if (!string.IsNullOrEmpty(ip.Gateway))
        {
          // 添加路由时设置metric
          await ProcessExtensions.ExecuteAsync("ip",
              $"route add default via {ip.Gateway} dev {config.Name} metric {ip.Metric}");
        }
      }

      // 应用DNS配置
      if (config.DnsServers?.Any() == true)
      {
        var content = string.Join("\n", config.DnsServers.Select(dns => $"nameserver {dns}"));
        await File.WriteAllTextAsync("/etc/resolv.conf", content);
      }
    }
  }

  /// <summary>
  /// 应用Windows网络配置
  /// </summary>
  /// <param name="config"></param>
  /// <returns></returns>
  private async Task ApplyWindowsNetworkConfigAsync(NetworkInterfaceConfig config)
  {
    // 先设置接口状态
    var stateCommand = config.Enabled ? "enable" : "disable";
    await ProcessExtensions.ExecuteAsync("netsh",
        $"interface set interface \"{config.Name}\" {stateCommand}");

    if (config.Enabled)  // 只有在启用状态才配置IP和路由
    {
      foreach (var ip in config.IpConfigs)
      {
        // 先禁用DHCP
        await ProcessExtensions.ExecuteAsync("netsh",
            $"interface ipv4 set address \"{config.Name}\" static {ip.IpAddress} {ip.SubnetMask}");

        await Task.Delay(1000);

        // 如果有网关，设置网关
        if (!string.IsNullOrEmpty(ip.Gateway))
        {
          await ProcessExtensions.ExecuteAsync("netsh",
              $"interface ipv4 add address \"{config.Name}\" gateway={ip.Gateway} gwmetric={ip.Metric}");
        }

        await Task.Delay(1000);
      }

      // 应用DNS配置
      if (config.DnsServers?.Any() == true)
      {
        // 先清除现有DNS配置
        await ProcessExtensions.ExecuteAsync("netsh",
            $"interface ipv4 set dnsservers \"{config.Name}\" static none");

        // 等待DNS配置清除
        await Task.Delay(1000);

        // 设置主DNS
        var primaryDns = config.DnsServers.FirstOrDefault();
        if (!string.IsNullOrEmpty(primaryDns))
        {
          await ProcessExtensions.ExecuteAsync("netsh",
              $"interface ipv4 set dnsservers \"{config.Name}\" static {primaryDns} primary");

          // 添加其他DNS服务器
          foreach (var dns in config.DnsServers.Skip(1))
          {
            await ProcessExtensions.ExecuteAsync("netsh",
                $"interface ipv4 add dnsservers \"{config.Name}\" {dns} index=2");
          }
        }
      }
    }
  }

  /// <summary>
  /// 设置网络接口状态（启用/禁用）
  /// </summary>
  [HttpPost("interface/{interfaceName}/state")]
  [OperationId(nameof(SetInterfaceStateAsync))]
  public async Task SetInterfaceStateAsync(string interfaceName, bool enabled)
  {
    try
    {
      if (RuntimeInformation.IsOSPlatform(OSPlatform.Linux))
      {
        var command = enabled ? "up" : "down";
        await ProcessExtensions.ExecuteAsync("ip", $"link set {interfaceName} {command}");
      }
      else if (RuntimeInformation.IsOSPlatform(OSPlatform.Windows))
      {
        var command = enabled ? "enable" : "disable";
        await ProcessExtensions.ExecuteAsync("netsh",
            $"interface set interface \"{interfaceName}\" {command}");
      }

      // 更新配置文件中的状态
      var configs = await LoadNetworkConfigsAsync();
      var config = configs.FirstOrDefault(c => c.Name == interfaceName);
      if (config != null)
      {
        config.Enabled = enabled;
        await SaveNetworkConfigsAsync(configs);
      }
    }
    catch (Exception ex)
    {
      _logger.LogError(ex, "设置网络接口状态失败");
      throw Oops.Oh("设置网络接口状态失败");
    }
  }
}