using Furion.HttpRemote;

namespace EdgeGateway.Base.Service.Debug.HttpClient
{
  /// <summary>
  /// HTTP客户端服务
  /// 用于处理HTTP请求的核心业务逻辑
  /// </summary>
  public class HttpClientService : ISingleton
  {
    /// <summary>
    /// HTTP远程服务
    /// </summary>
    private readonly IHttpRemoteService _httpRemoteService;

    /// <summary>
    /// 日志服务
    /// </summary>
    private readonly ILogger<HttpClientService> _log;

    /// <summary>
    /// 请求历史记录，最多保留50条
    /// </summary>
    private readonly List<HttpRequestLog> _requestLogs = new List<HttpRequestLog>();

    /// <summary>
    /// 构造函数
    /// </summary>
    /// <param name="httpRemoteService">HTTP远程服务</param>
    /// <param name="log">日志服务</param>
    public HttpClientService(
        IHttpRemoteService httpRemoteService,
        ILogger<HttpClientService> log)
    {
      _httpRemoteService = httpRemoteService;
      _log = log;
    }

    /// <summary>
    /// 发送HTTP请求
    /// </summary>
    /// <param name="config">HTTP请求配置</param>
    /// <returns>HTTP响应信息</returns>
    public async Task<HttpResponseInfo> SendRequestAsync(HttpRequestConfig config)
    {
      var stopwatch = Stopwatch.StartNew();
      var response = new HttpResponseInfo();

      try
      {
        switch (config.Method)
        {
          case Entity.Model.HttpMethod.GET:
            await ExecuteGetRequest(config, response);
            break;
          case Entity.Model.HttpMethod.POST:
            await ExecutePostRequest(config, response);
            break;
          case Entity.Model.HttpMethod.PUT:
            await ExecutePutRequest(config, response);
            break;
          case Entity.Model.HttpMethod.DELETE:
            await ExecuteDeleteRequest(config, response);
            break;
          default:
            throw new NotSupportedException($"不支持的HTTP方法: {config.Method}");
        }
      }
      catch (Exception ex)
      {
        _log.LogError(ex, "执行HTTP请求失败");
        response.Error = ex.Message;
      }
      finally
      {
        stopwatch.Stop();
        response.ResponseTime = stopwatch.ElapsedMilliseconds;
      }

      // 记录请求日志
      var requestLog = new HttpRequestLog
      {
        Request = config,
        Response = response,
        Timestamp = DateTime.Now
      };
      // 如果请求历史记录超过50条，则删除最早的一条
      if (_requestLogs.Count >= 50)
      {
        _requestLogs.RemoveAt(0);
      }
      _requestLogs.Add(requestLog);

      return response;
    }

    /// <summary>
    /// 获取请求历史记录
    /// </summary>
    /// <returns>请求历史记录列表</returns>
    public List<HttpRequestLog> GetRequestHistory()
    {
      // 排序
      return _requestLogs.OrderByDescending(x => x.Timestamp).ToList();
    }

    /// <summary>
    /// 清空请求历史记录
    /// </summary>
    public void ClearRequestHistory()
    {
      _requestLogs.Clear();
    }

    #region 私有方法

    /// <summary>
    /// 执行GET请求
    /// </summary>
    private async Task ExecuteGetRequest(HttpRequestConfig config, HttpResponseInfo response)
    {
      var statusCode = HttpStatusCode.OK;
      try
      {
        var result = await _httpRemoteService.GetAsStringAsync(config.Url,
          builder => builder
            .SetTimeout(TimeSpan.FromMilliseconds(config.Timeout))
            .WithHeaders(config.Headers)
            .WithQueryParameters(config.Parameters)
            .SetOnRequestFailed((exception, responseMessage) =>
            {
              statusCode = responseMessage?.StatusCode ?? HttpStatusCode.BadGateway;
              response.StatusCode = (int)statusCode;
              if (responseMessage != null)
              {
                response.Headers = responseMessage.Headers
                  .ToDictionary(h => h.Key, h => string.Join(", ", h.Value));
              }
              throw new Exception(exception?.Message ?? "请求失败");
            }));

        response.StatusCode = (int)statusCode;
        response.Body = result;
      }
      catch (Exception ex)
      {
        if (response.StatusCode == 0)
        {
          response.StatusCode = (int)statusCode;
        }
        throw Oops.Oh(ex.Message);
      }
    }

    /// <summary>
    /// 执行POST请求
    /// </summary>
    private async Task ExecutePostRequest(HttpRequestConfig config, HttpResponseInfo response)
    {
      var statusCode = HttpStatusCode.OK;
      try
      {
        var contentType = config.ContentType ?? "application/json";

        var result = await _httpRemoteService.PostAsStringAsync(config.Url,
          builder => builder
            .SetTimeout(TimeSpan.FromMilliseconds(config.Timeout))
            .WithHeaders(config.Headers)
            .WithQueryParameters(config.Parameters)
            .SetContentType(contentType)
            .SetContent(config.Body)
            .SetOnRequestFailed((exception, responseMessage) =>
            {
              statusCode = responseMessage?.StatusCode ?? HttpStatusCode.BadGateway;
              response.StatusCode = (int)statusCode;
              if (responseMessage != null)
              {
                response.Headers = responseMessage.Headers
                  .ToDictionary(h => h.Key, h => string.Join(", ", h.Value));
              }
              throw new Exception(exception?.Message ?? "请求失败");
            }));

        response.StatusCode = (int)statusCode;
        response.Body = result;
      }
      catch (Exception ex)
      {
        if (response.StatusCode == 0)
        {
          response.StatusCode = (int)statusCode;
        }
        throw Oops.Oh(ex.Message);
      }
    }

    /// <summary>
    /// 执行PUT请求
    /// </summary>
    private async Task ExecutePutRequest(HttpRequestConfig config, HttpResponseInfo response)
    {
      var statusCode = HttpStatusCode.OK;
      try
      {
        var contentType = config.ContentType ?? "application/json";

        var result = await _httpRemoteService.PutAsStringAsync(config.Url,
          builder => builder
            .SetTimeout(TimeSpan.FromMilliseconds(config.Timeout))
            .WithHeaders(config.Headers)
            .WithQueryParameters(config.Parameters)
            .SetContentType(contentType)
            .SetContent(config.Body)
            .SetOnRequestFailed((exception, responseMessage) =>
            {
              statusCode = responseMessage?.StatusCode ?? HttpStatusCode.BadGateway;
              response.StatusCode = (int)statusCode;
              if (responseMessage != null)
              {
                response.Headers = responseMessage.Headers
                  .ToDictionary(h => h.Key, h => string.Join(", ", h.Value));
              }
              throw new Exception(exception?.Message ?? "请求失败");
            }));

        response.StatusCode = (int)statusCode;
        response.Body = result;
      }
      catch (Exception ex)
      {
        if (response.StatusCode == 0)
        {
          response.StatusCode = (int)statusCode;
        }
        throw Oops.Oh(ex.Message);
      }
    }

    /// <summary>
    /// 执行DELETE请求
    /// </summary>
    private async Task ExecuteDeleteRequest(HttpRequestConfig config, HttpResponseInfo response)
    {
      var statusCode = HttpStatusCode.OK;
      try
      {
        var result = await _httpRemoteService.DeleteAsStringAsync(config.Url,
          builder => builder
            .SetTimeout(TimeSpan.FromMilliseconds(config.Timeout))
            .WithHeaders(config.Headers)
            .WithQueryParameters(config.Parameters)
            .SetOnRequestFailed((exception, responseMessage) =>
            {
              statusCode = responseMessage?.StatusCode ?? HttpStatusCode.BadGateway;
              response.StatusCode = (int)statusCode;
              if (responseMessage != null)
              {
                response.Headers = responseMessage.Headers
                  .ToDictionary(h => h.Key, h => string.Join(", ", h.Value));
              }
              throw new Exception(exception?.Message ?? "请求失败");
            }));

        response.StatusCode = (int)statusCode;
        response.Body = result;
      }
      catch (Exception ex)
      {
        if (response.StatusCode == 0)
        {
          response.StatusCode = (int)statusCode;
        }
        throw Oops.Oh(ex.Message);
      }
    }

    #endregion
  }
}