using System.Text.RegularExpressions;

namespace EdgeGateway.Base.Network;

/// <summary>
///     网络拓扑服务
/// </summary>
[ApiDescriptionSettings("网络拓扑")]
[Route("/api/system/network/topology")]
public class NetworkTopologyService : <PERSON><PERSON><PERSON>, IDynamicApiController, IDisposable
{
  /// <summary>
  ///     网络拓扑服务
  /// </summary>
  private readonly ILogger<NetworkTopologyService> _logger;

  /// <summary>
  ///     网络节点
  /// </summary>
  private static ConcurrentDictionary<string, NetworkNodeInfo> _networkNodes;

  /// <summary>
  ///     网络链接
  /// </summary>
  private static readonly ConcurrentDictionary<string, NetworkLinkInfo> _networkLinks = new();

  /// <summary>
  ///     监控定时器
  /// </summary>
  private Timer _monitorTimer;

  /// <summary>
  ///     监控间隔
  /// </summary>
  private const int MonitorInterval = 5000; // 5秒检测一次

  /// <summary>
  ///     历史记录仓储
  /// </summary>
  private readonly ISqlSugarClient _db;

  /// <summary>
  ///     历史记录缓冲区
  /// </summary>
  private readonly ConcurrentQueue<NetworkNodeHistory> _historyBuffer = new ConcurrentQueue<NetworkNodeHistory>();

  /// <summary>
  ///     历史记录缓冲区大小
  /// </summary>
  private readonly int _historyBufferSize = 1;

  /// <summary>
  ///     发现定时器
  /// </summary>
  private Timer _discoveryTimer;

  /// <summary>
  ///     发现间隔
  /// </summary>
  private const int DiscoveryInterval = 300000; // 5分钟执行一次自动发现

  /// <summary>
  ///     最大扫描主机数
  /// </summary>
  private const int MaxHostsToScan = 50; // 最大扫描主机数

  /// <summary>
  ///     最小响应时间阈值(ms)
  /// </summary>
  private const int MinPingResponse = 2000; // 最小响应时间阈值(ms)

  public NetworkTopologyService(
      ILogger<NetworkTopologyService> logger,
      ISqlSugarClient db)
  {
    _logger = logger;
    _db = db;

    // 只在第一次初始化时执行网络发现
    if (_networkNodes == null)
    {
      _networkNodes = new();
      Task.Run(async () =>
      {
        try
        {
          await PerformNetworkDiscovery();
        }
        catch (Exception ex)
        {
          _logger.LogError(ex, "初始化网络拓扑服务时出错");
        }
      }).Wait();
      // 初始化监控和发现定时器
      InitializeMonitoring();
      InitializeDiscovery();
      
    }
  }

  #region 网络拓扑

  /// <summary>
  ///     获取网络拓扑信息
  /// </summary>
  [DisplayName("获取网络拓扑信息")]
  [HttpGet]
  [OperationId(nameof(GetTopology))]
  public NetworkTopology GetTopology()
  {
    // 获取网络拓扑
    return new NetworkTopology
    {
      Nodes = _networkNodes.Values.ToList(),
      Links = _networkLinks.Values.ToList()
    };
  }

  /// <summary>
  ///     获取指定节点的详细信息
  /// </summary>
  [DisplayName("获取指定节点的详细信息")]
  [HttpGet("node/{nodeId}")]
  [OperationId(nameof(GetNodeDetails))]
  public NetworkNodeInfo GetNodeDetails(string nodeId)
  {
    // 获取节点
    if (_networkNodes.TryGetValue(nodeId, out var node))
      return node;
    // 节点不存在
    throw Oops.Oh("节点不存在");
  }

  /// <summary>
  ///     手动添加网络节点
  /// </summary>
  [HttpPost("node")]
  [OperationId(nameof(AddNode))]
  [DisplayName("手动添加网络节点")]
  public async Task AddNode(NetworkNodeInfo node)
  {
    // 标记为手动添加
    node.IsManual = true;

    // 添加节点
    if (_networkNodes.TryAdd(node.Id, node))
      // 检测节点连通性
      await DetectNodeConnectivity(node);
    else
      // 节点已存在
      throw Oops.Oh("节点已存在");
  }

  /// <summary>
  ///     删除网络节点
  /// </summary>
  [HttpDelete("node/{nodeId}")]
  [OperationId(nameof(RemoveNode))]
  [DisplayName("手动删除网络节点")]
  public void RemoveNode(string nodeId)
  {
    // 删除节点
    if (_networkNodes.TryRemove(nodeId, out _))
    {
      // 删除相关的链接
      var linksToRemove = _networkLinks.Values
          .Where(l => l.SourceId == nodeId || l.TargetId == nodeId)
          .Select(l => l.Id);

      // 删除相关的链接
      foreach (var linkId in linksToRemove) _networkLinks.TryRemove(linkId, out _);
    }
  }

  /// <summary>
  ///     获取网络分析数据
  /// </summary>
  [HttpGet("analytics")]
  [OperationId(nameof(GetNetworkAnalytics))]
  [DisplayName("获取网络分析数据")]
  public async Task<NetworkAnalytics> GetNetworkAnalytics([FromQuery] DateTime? startTime = null, [FromQuery] DateTime? endTime = null)
  {
    // 获取历史记录 
    startTime ??= DateTime.Now.AddDays(-1);
    endTime ??= DateTime.Now;

    var history = await _db.Queryable<NetworkNodeHistory>()
        .Where(h => h.RecordTime >= startTime && h.RecordTime <= endTime)
        .ToListAsync();

    // 创建网络分析数据
    var analytics = new NetworkAnalytics
    {
      StartTime = startTime.Value,
      EndTime = endTime.Value
    };

    // 遍历历史记录 
    foreach (var nodeGroup in history.GroupBy(h => h.NodeId))
    {
      // 获取节点ID
      var nodeId = nodeGroup.Key;
      // 获取节点记录
      var records = nodeGroup.ToList();

      // 计算可用性
      var totalRecords = records.Count;
      if (totalRecords > 0)
      {
        // 获取在线记录
        var onlineRecords = records.Count(r => r.Status == NodeStatus.Online);
        // 计算可用性
        analytics.NodeAvailability[nodeId] = (double)onlineRecords / totalRecords * 100;

        // 计算平均延迟
        var onlineLatencies = records
            .Where(r => r.Status == NodeStatus.Online)
            .Select(r => r.Latency)
            .ToList();

        analytics.AverageLatency[nodeId] = onlineLatencies.Any()
            ? onlineLatencies.Average()
            : 1000; // 如果没有在线记录，则平均延迟为0

        // 计算故障次数
        analytics.FailureCount[nodeId] = records
            .Count(r => r.Status == NodeStatus.Offline);
      }
      else
      {
        // 如果没有记录，设置默认值
        analytics.NodeAvailability[nodeId] = 0;
        analytics.AverageLatency[nodeId] = 0;
        analytics.FailureCount[nodeId] = 0;
      }
    }

    return analytics;
  }

  /// <summary>
  ///     手动触发网络发现
  /// </summary>
  [HttpPost("discover")]
  [OperationId(nameof(DiscoverNetwork))]
  [DisplayName("手动触发网络发现")]
  public async Task<NetworkDiscoveryResult> DiscoverNetwork()
  {
    // 执行网络发现
    return await PerformNetworkDiscovery();
  }

  #region 内部方法

  /// <summary>
  ///     初始化监控
  /// </summary>
  private void InitializeMonitoring()
  {
    _monitorTimer = new Timer(async _ => await MonitorNetwork(), null, 0, MonitorInterval);
  }

  /// <summary>
  ///     监控网络
  /// </summary>
  private async Task MonitorNetwork()
  {
    try
    {
      if (_networkNodes.IsEmpty)
      {
        // 如果没有节点，重新执行发现
        await PerformNetworkDiscovery();
        return;
      }

      foreach (var node in _networkNodes.Values.Where(w => w.Type != NodeType.Local))
      {
        // 检测节点连通性 
        await DetectNodeConnectivity(node);

        // 添加历史记录
        var history = new NetworkNodeHistory
        {
          NodeId = node.Id,
          Status = node.Status,
          Latency = node.Latency,
          RecordTime = DateTime.Now
        };

        // 添加历史记录 
        _historyBuffer.Enqueue(history);

        // 当缓冲区满时批量保存
        if (_historyBuffer.Count >= _historyBufferSize)
          // 保存历史记录 
          await SaveHistoryBuffer();
      }

      // 更新连接状态
      UpdateLinkStatus();
    }
    catch (Exception ex)
    {
      _logger.LogError(ex, "网络监控出错");
    }
  }

  /// <summary>
  ///     保存历史记录
  /// </summary>
  private async Task SaveHistoryBuffer()
  {
    // 创建记录列表
    var records = new List<NetworkNodeHistory>();
    // 从缓冲区中取出记录
    while (_historyBuffer.TryDequeue(out var history)) records.Add(history);

    // 如果记录不为空
    if (records.Any())
      // 批量保存记录
      await _db.Insertable(records).ExecuteCommandAsync();
  }

  /// <summary>
  ///     检测节点连通性
  /// </summary>
  private async Task DetectNodeConnectivity(NetworkNodeInfo node)
  {
    try
    {
      var isReachable = false;

      // 尝试Ping
      using (var ping = new Ping())
      {
        try
        {
          // 发送Ping请求
          var reply = await ping.SendPingAsync(node.IpAddress, 1000);
          // 判断是否可达
          isReachable = reply.Status == IPStatus.Success;

          if (isReachable) node.Latency = reply.RoundtripTime;
        }
        catch
        {
          // 不可达
          isReachable = false;
        }
      }

      // 更新节点状态
      node.Status = isReachable ? NodeStatus.Online : NodeStatus.Offline;
      // 更新节点状态
      node.LastChecked = DateTime.Now;

      // 如果状态发生变化，记录日志
      if (node.LastStatus != node.Status)
      {
        _logger.LogInformation($"节点 {node.Name} ({node.IpAddress}) 状态变更: {node.LastStatus} -> {node.Status}");
        node.LastStatus = node.Status;
      }
    }
    catch (Exception ex)
    {
      _logger.LogError(ex, $"检测节点 {node.Name} ({node.IpAddress}) 连通性时出错");
      node.Status = NodeStatus.Unknown;
    }
  }

  /// <summary>
  ///     初始化发现
  /// </summary>
  private void InitializeDiscovery()
  {
    _discoveryTimer = new Timer(async _ => await PerformNetworkDiscovery(), null, 0, DiscoveryInterval);
  }

  /// <summary>
  ///     执行网络发现
  /// </summary>
  private async Task<NetworkDiscoveryResult> PerformNetworkDiscovery()
  {
    var result = new NetworkDiscoveryResult
    {
      DiscoveryTime = DateTime.Now
    };

    try
    {
      // 获取本地网络接口
      var interfaces = NetworkInterface.GetAllNetworkInterfaces()
          .Where(i => i.OperationalStatus == OperationalStatus.Up &&
                      i.NetworkInterfaceType != NetworkInterfaceType.Loopback);

      foreach (var netInterface in interfaces)
      {
        var ipProps = netInterface.GetIPProperties();

        // 添加本地接口作为节点
        if (ipProps.UnicastAddresses.Any(a =>
                a.Address.AddressFamily == AddressFamily.InterNetwork &&
                !a.Address.ToString().StartsWith("127.")))
        {
          var localAddress = ipProps.UnicastAddresses
              .First(a => a.Address.AddressFamily == AddressFamily.InterNetwork &&
                          !a.Address.ToString().StartsWith("127."));

          var localNode = new NetworkNodeInfo
          {
            Id = $"{localAddress.Address}",
            Name = $"本机 ({netInterface.Name})",
            IpAddress = localAddress.Address.ToString(),
            Type = NodeType.Local,
            MacAddress = netInterface.GetPhysicalAddress().ToString()
          };

          result.DiscoveredNodes.Add(localNode);
          await AddNodeIfNotExists(localNode);
        }

        // 获取网关
        foreach (var gateway in ipProps.GatewayAddresses)
          // 判断是否为IPv4地址
          if (gateway.Address.AddressFamily == AddressFamily.InterNetwork)
          {
            // 创建节点
            var node = new NetworkNodeInfo
            {
              Id = $"{gateway.Address}",
              Name = $"{gateway.Address}",
              IpAddress = gateway.Address.ToString(),
              Type = NodeType.Gateway,
              MacAddress = await GetMacAddress(gateway.Address.ToString())
            };

            // 添加到发现结果
            result.DiscoveredNodes.Add(node);
            // 添加到网络节点
            await AddNodeIfNotExists(node);
          }

        // 扫描本地网段
        var unicastAddresses = ipProps.UnicastAddresses
            .Where(a => a.Address.AddressFamily == AddressFamily.InterNetwork
                        && !a.Address.ToString().StartsWith("127.")); // 过滤掉127网段

        // 遍历本地网段 
        foreach (var addr in unicastAddresses)
        {
          // 获取网络地址
          var network = GetNetworkAddress(addr.Address, addr.IPv4Mask);
          // 扫描网络范围
          await ScanNetworkRange(network, addr.IPv4Mask, result);
        }
      }

      // 发现连接关系
      await DiscoverLinks(result);

      // 清理不再存在的节点
      var discoveredNodeIds = result.DiscoveredNodes.Select(n => n.Id).ToHashSet();
      var nodesToRemove = _networkNodes.Keys
          .Where(id => !discoveredNodeIds.Contains(id) &&
                       _networkNodes.TryGetValue(id, out var node) &&
                       node.Type != NodeType.Local &&
                       node.Type != NodeType.Gateway &&
                       !node.IsManual) // 不清理手动添加的节点
          .ToList();

      foreach (var nodeId in nodesToRemove) RemoveNode(nodeId);
    }
    catch (Exception ex)
    {
      _logger.LogError(ex, "网络发现过程出错");
    }

    return result;
  }

  /// <summary>
  ///     扫描网络范围
  /// </summary>
  private async Task ScanNetworkRange(IPAddress network, IPAddress subnetMask, NetworkDiscoveryResult result)
  {
    try
    {
      // 获取主机地址
      var hosts = GetHostAddresses(network, subnetMask)
          .Take(MaxHostsToScan) // 限制最大扫描数量
          .ToList();

      // 创建任务列表
      var tasks = new List<Task>();
      var semaphore = new SemaphoreSlim(10); // 限制并发数

      // 遍历主机地址
      foreach (var host in hosts)
      {
        await semaphore.WaitAsync();
        tasks.Add(Task.Run(async () =>
        {
          try
          {
            // 快速Ping测试
            using var ping = new Ping();
            var reply = await ping.SendPingAsync(host, 1000);

            // 只处理响应时间在合理范围内的主机
            if (reply.Status == IPStatus.Success && reply.RoundtripTime < MinPingResponse)
            {
              // 尝试解析主机名
              var hostName = string.Empty;
              try
              {
                var hostEntry = await Dns.GetHostEntryAsync(host.ToString());
                hostName = hostEntry.HostName;
              }
              catch
              {
                hostName = host.ToString();
              }

              // 创建节点
              var node = new NetworkNodeInfo
              {
                Id = $"{host}",
                Name = !string.IsNullOrEmpty(hostName) ? hostName : $"{host}",
                IpAddress = host.ToString(),
                Type = NodeType.Host,
                MacAddress = await GetMacAddress(host.ToString()),
                Latency = reply.RoundtripTime
              };

              // 添加到发现结果
              result.DiscoveredNodes.Add(node);
              // 添加到网络节点
              await AddNodeIfNotExists(node);
            }
          }
          catch (Exception ex)
          {
            _logger.LogDebug(ex, $"扫描主机 {host} 时出错");
          }
          finally
          {
            semaphore.Release();
          }
        }));
      }

      // 等待所有任务完成
      await Task.WhenAll(tasks);
    }
    catch (Exception ex)
    {
      _logger.LogError(ex, "扫描网络范围时出错");
    }
  }

  /// <summary>
  ///     发现连接关系
  /// </summary>
  private async Task DiscoverLinks(NetworkDiscoveryResult result)
  {
    // 遍历发现节点
    foreach (var node in result.DiscoveredNodes)
    {
      // 使用traceroute发现路径
      var hops = await TraceRoute(node.IpAddress);

      // 遍历路径
      for (var i = 0; i < hops.Count - 1; i++)
      {
        // 创建链接
        var link = new NetworkLinkInfo
        {
          Id = $"link_{hops[i]}_{hops[i + 1]}",
          SourceId = $"{hops[i]}",
          TargetId = $"{hops[i + 1]}",
          Type = LinkType.Ethernet
        };

        // 添加到发现结果
        result.DiscoveredLinks.Add(link);
        // 添加到网络链接
        _networkLinks.TryAdd(link.Id, link);
      }
    }
  }

  /// <summary>
  ///     获取MAC地址
  /// </summary>
  private async Task<string> GetMacAddress(string ipAddress)
  {
    try
    {
      // 创建进程
      var process = new Process
      {
        // 设置进程启动信息
        StartInfo = new ProcessStartInfo
        {
          FileName = "arp", // 设置进程启动信息
          Arguments = $"-a {ipAddress}", // 设置进程参数
          UseShellExecute = false, // 不使用Shell执行
          RedirectStandardOutput = true, // 重定向标准输出
          CreateNoWindow = true // 不创建窗口
        }
      };

      // 启动进程
      process.Start();
      // 读取进程输出
      var output = await process.StandardOutput.ReadToEndAsync();
      // 等待进程退出
      await process.WaitForExitAsync();

      // 解析ARP输出获取MAC地址
      var match = Regex.Match(output, @"([0-9A-Fa-f]{2}[:-]){5}([0-9A-Fa-f]{2})");
      // 返回MAC地址
      return match.Success ? match.Value : string.Empty;
    }
    catch
    {
      return string.Empty;
    }
  }

  /// <summary>
  ///     执行traceroute
  /// </summary>
  private async Task<List<string>> TraceRoute(string destination)
  {
    // 创建路径列表
    var hops = new List<string>();
    try
    {
      // 遍历TTL
      for (var ttl = 1; ttl <= 30; ttl++)
      {
        // 创建Ping对象
        using var ping = new Ping();
        // 创建Ping选项
        var options = new PingOptions(ttl, true);
        // 发送Ping请求
        var reply = await ping.SendPingAsync(destination, 1000, new byte[32], options);
        // 判断是否可达
        if (reply.Status == IPStatus.TtlExpired)
        {
          // 添加到路径
          hops.Add(reply.Address.ToString());
        }
        else if (reply.Status == IPStatus.Success)
        {
          // 添加到路径
          hops.Add(reply.Address.ToString());
          // 跳出循环
          break;
        }
      }
    }
    catch (Exception ex)
    {
      _logger.LogError(ex, $"执行traceroute到 {destination} 时出错");
    }

    return hops;
  }

  /// <summary>
  ///     获取网络地址
  /// </summary>
  /// <param name="address"></param>
  /// <param name="subnetMask"></param>
  /// <returns></returns>
  private IPAddress GetNetworkAddress(IPAddress address, IPAddress subnetMask)
  {
    // 获取IP地址字节数组
    var ipBytes = address.GetAddressBytes();
    // 获取子网掩码字节数组
    var maskBytes = subnetMask.GetAddressBytes();
    // 创建网络地址字节数组
    var networkBytes = new byte[4];

    // 计算网络地址
    for (var i = 0; i < 4; i++) networkBytes[i] = (byte)(ipBytes[i] & maskBytes[i]);

    return new IPAddress(networkBytes);
  }

  /// <summary>
  ///     获取主机地址
  /// </summary>
  /// <param name="network"></param>
  /// <param name="subnetMask"></param>
  /// <returns></returns>
  private IEnumerable<IPAddress> GetHostAddresses(IPAddress network, IPAddress subnetMask)
  {
    var networkBytes = network.GetAddressBytes();
    var maskBytes = subnetMask.GetAddressBytes();

    // 如果是127网段则直接返回空结果
    if (networkBytes[0] == 127) yield break;

    // 计算有效主机数量
    var hostCount = 0;
    for (var i = 0; i < 4; i++) hostCount = (hostCount << 8) + (byte)~maskBytes[i];

    // 优先扫描常用IP段
    var priorityHosts = new List<int> { 1, 2, 254, 253 }; // 网关、服务器等常用IP结尾

    foreach (var lastOctet in priorityHosts)
    {
      var hostBytes = (byte[])networkBytes.Clone();
      hostBytes[3] = (byte)lastOctet;
      var ip = new IPAddress(hostBytes);
      // 确保不扫描127网段
      if (!ip.ToString().StartsWith("127.")) yield return ip;
    }

    // 然后扫描其他IP,但限制数量
    var random = new Random();
    var remainingHosts = Enumerable.Range(3, 250)
        .Where(i => !priorityHosts.Contains(i))
        .OrderBy(x => random.Next()) // 随机排序
        .Take(MaxHostsToScan - priorityHosts.Count);

    foreach (var lastOctet in remainingHosts)
    {
      var hostBytes = (byte[])networkBytes.Clone();
      hostBytes[3] = (byte)lastOctet;
      var ip = new IPAddress(hostBytes);
      // 确保不扫描127网段
      if (!ip.ToString().StartsWith("127.")) yield return ip;
    }
  }

  /// <summary>
  ///     添加节点
  /// </summary>
  /// <param name="node"></param>
  /// <returns></returns>
  private async Task AddNodeIfNotExists(NetworkNodeInfo node)
  {
    if (_networkNodes.TryGetValue(node.Id, out var existingNode))
    {
      // 更新现有节点的信息
      if (existingNode.Type != NodeType.Local) existingNode.Name = node.Name;
      existingNode.IpAddress = node.IpAddress;
      existingNode.Type = node.Type;
      existingNode.MacAddress = node.MacAddress;
      existingNode.LastChecked = DateTime.Now;
    }
    else
    {
      // 添加新节点
      await AddNode(node);
    }
  }

  /// <summary>
  ///     更新连接状态
  /// </summary>
  private void UpdateLinkStatus()
  {
    try
    {
      // 遍历所有连接
      foreach (var link in _networkLinks.Values)
        // 获取源节点和目标节点
        if (_networkNodes.TryGetValue(link.SourceId, out var sourceNode) &&
            _networkNodes.TryGetValue(link.TargetId, out var targetNode))
        {
          // 更新连接状态
          var newStatus = DetermineLinkStatus(sourceNode, targetNode);

          // 如果状态发生变化，记录日志
          if (link.Status != newStatus)
          {
            _logger.LogInformation($"连接 {link.Id} 状态从 {link.Status} 变更为 {newStatus}");
            link.Status = newStatus;
          }
        }
        else
        {
          // 如果源节点或目标节点不存在，设置为未知状态
          link.Status = LinkStatus.Unknown;
        }
    }
    catch (Exception ex)
    {
      _logger.LogError(ex, "更新连接状态时出错");
    }
  }

  /// <summary>
  ///     确定连接状态
  /// </summary>
  /// <param name="sourceNode">源节点</param>
  /// <param name="targetNode">目标节点</param>
  /// <returns>连接状态</returns>
  private LinkStatus DetermineLinkStatus(NetworkNodeInfo sourceNode, NetworkNodeInfo targetNode)
  {
    // 如果源节点和目标节点都在线，则连接为活动状态
    if (sourceNode.Status == NodeStatus.Online && targetNode.Status == NodeStatus.Online)
    {
      // 如果延迟超过阈值，可能表示连接质量不佳
      var averageLatency = (sourceNode.Latency + targetNode.Latency) / 2;
      if (averageLatency > 1000) // 1秒以上的延迟可能表示连接不稳定
        return LinkStatus.Unstable;
      return LinkStatus.Active;
    }
    // 如果任一节点离线，则连接为非活动状态

    if (sourceNode.Status == NodeStatus.Offline || targetNode.Status == NodeStatus.Offline) return LinkStatus.Inactive;
    // 其他情况为未知状态
    return LinkStatus.Unknown;
  }

  #endregion

  #endregion

  #region 网络工具

  /// <summary>
  ///     批量Ping
  /// </summary>
  /// <param name="request">Ping请求参数</param>
  /// <returns>Ping结果列表</returns>
  [HttpPost("ping")]
  [OperationId(nameof(BatchPing))]
  [DisplayName("批量Ping")]
  public async Task<List<PingResult>> BatchPing([FromBody] PingRequest request)
  {
    var results = new List<PingResult>();
    var tasks = new List<Task<PingResult>>();

    // 创建取消令牌,用于超时控制
    using var cts = new CancellationTokenSource(request.Timeout + 1000);

    try
    {
      // 并行执行Ping
      foreach (var target in request.Targets) tasks.Add(PingHostAsync(target, request.Timeout, request.Ttl, cts.Token));

      // 等待所有Ping完成
      results = (await Task.WhenAll(tasks)).ToList();
    }
    catch (OperationCanceledException)
    {
      _logger.LogWarning("批量Ping操作超时");
      // 将未完成的任务标记为失败
      foreach (var target in request.Targets.Where(t =>
                   !results.Any(r => r.Target == t)))
        results.Add(new PingResult
        {
          Target = target,
          Status = PingStatus.Failed,
          ResponseTime = request.Timeout,
          Ttl = 0
        });
    }
    catch (Exception ex)
    {
      _logger.LogError(ex, "批量Ping操作出错");
      throw;
    }

    return results;
  }

  /// <summary>
  ///     端口扫描
  /// </summary>
  /// <param name="request">扫描请求参数</param>
  /// <returns>扫描结果列表</returns>
  [HttpPost("port-scan")]
  [OperationId(nameof(ScanPorts))]
  [DisplayName("端口扫描")]
  public async Task<List<PortScanResult>> ScanPorts([FromBody] PortScanRequest request)
  {
    var results = new List<PortScanResult>();
    var tasks = new List<Task<PortScanResult>>();
    var semaphore = new SemaphoreSlim(request.ConcurrentScans);

    try
    {
      // 创建取消令牌
      using var cts = new CancellationTokenSource();
      cts.CancelAfter(TimeSpan.FromMilliseconds(request.Timeout * 2));

      // 扫描指定范围的端口
      for (var port = request.StartPort; port <= request.EndPort; port++)
      {
        await semaphore.WaitAsync();
        var portNumber = port;
        tasks.Add(Task.Run(async () =>
        {
          try
          {
            return await CheckPortAsync(request.Target, portNumber, request.Timeout);
          }
          finally
          {
            semaphore.Release();
          }
        }));
      }

      // 等待所有扫描完成
      results = (await Task.WhenAll(tasks)).ToList();

      // 识别可能的服务
      foreach (var result in results.Where(r => r.Status == PortStatus.Open)) result.Service = IdentifyService(result.Port);
    }
    catch (OperationCanceledException)
    {
      _logger.LogWarning("端口扫描操作超时");
    }
    catch (Exception ex)
    {
      _logger.LogError(ex, "端口扫描操作出错");
      throw;
    }

    return results;
  }

  /// <summary>
  ///     检查单个端口
  /// </summary>
  /// <param name="target">目标主机</param>
  /// <param name="port">端口号</param>
  /// <param name="timeout">超时时间(ms)</param>
  /// <returns>端口检测结果</returns>
  [HttpGet("check-port")]
  [OperationId(nameof(CheckPort))]
  [DisplayName("检查端口")]
  public async Task<PortScanResult> CheckPort([FromQuery] string target, [FromQuery] int port, [FromQuery] int timeout = 1000)
  {
    var result = await CheckPortAsync(target, port, timeout);
    if (result.Status == PortStatus.Open) result.Service = IdentifyService(port);
    return result;
  }

  #region 内部方法

  /// <summary>
  ///     对单个主机执行Ping
  /// </summary>
  private async Task<PingResult> PingHostAsync(string target, int timeout, int ttl, CancellationToken cancellationToken)
  {
    var result = new PingResult
    {
      Target = target,
      Status = PingStatus.Failed,
      ResponseTime = timeout,
      Ttl = 0
    };

    try
    {
      using var ping = new Ping();
      var options = new PingOptions
      {
        DontFragment = true,
        Ttl = ttl
      };

      var reply = await ping.SendPingAsync(
          target,
          timeout,
          new byte[32],
          options
      );

      result.Status = reply.Status == IPStatus.Success ? PingStatus.Success : PingStatus.Failed;
      result.ResponseTime = result.Status == PingStatus.Failed ? timeout : reply.RoundtripTime;
      result.Ttl = reply.Options?.Ttl ?? 0;
    }
    catch (PingException)
    {
      // Ping异常,保持默认失败状态
    }
    catch (Exception ex)
    {
      _logger.LogError(ex, $"Ping {target} 时发生错误");
    }

    return result;
  }

  /// <summary>
  ///     检查端口是否开放
  /// </summary>
  private async Task<PortScanResult> CheckPortAsync(string target, int port, int timeout)
  {
    var result = new PortScanResult
    {
      Target = target,
      Port = port,
      Status = PortStatus.Closed,
      ResponseTime = -1
    };

    try
    {
      using var tcpClient = new TcpClient();
      var stopwatch = Stopwatch.StartNew();

      // 尝试连接端口
      var connectTask = tcpClient.ConnectAsync(target, port);
      if (await Task.WhenAny(connectTask, Task.Delay(timeout)) == connectTask)
      {
        stopwatch.Stop();
        result.Status = PortStatus.Open;
        result.ResponseTime = stopwatch.ElapsedMilliseconds;
      }
    }
    catch (SocketException)
    {
      // 端口关闭或连接被拒绝
    }
    catch (Exception ex)
    {
      _logger.LogError(ex, $"检查端口 {target}:{port} 时发生错误");
    }

    return result;
  }

  /// <summary>
  ///     识别常见服务
  /// </summary>
  private string IdentifyService(int port)
  {
    return port switch
    {
      21 => "FTP",
      22 => "SSH",
      23 => "Telnet",
      25 => "SMTP",
      53 => "DNS",
      80 => "HTTP",
      110 => "POP3",
      143 => "IMAP",
      443 => "HTTPS",
      445 => "SMB",
      3306 => "MySQL",
      3389 => "RDP",
      5432 => "PostgreSQL",
      8080 => "HTTP-Proxy",
      _ => "Unknown"
    };
  }

  #endregion

  #endregion


  public void Dispose()
  {
    _monitorTimer?.Dispose();
    _db?.Dispose();
    _discoveryTimer?.Dispose();
  }
}