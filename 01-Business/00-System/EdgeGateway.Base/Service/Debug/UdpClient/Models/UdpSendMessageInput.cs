namespace EdgeGateway.Base.Service.Debug.UdpClient.Models
{
  /// <summary>
  /// UDP发送消息输入模型
  /// 用于指定发送消息的目标地址和内容
  /// </summary>
  public class UdpSendMessageInput
  {
    /// <summary>
    /// 远程IP地址
    /// 指定消息发送的目标IP地址
    /// </summary>
    /// <example>*************</example>
    public string RemoteIp { get; set; }

    /// <summary>
    /// 远程端口
    /// 指定消息发送的目标端口号
    /// </summary>
    /// <example>8080</example>
    public int RemotePort { get; set; }

    /// <summary>
    /// 消息内容
    /// 支持多行消息，使用\n分隔
    /// </summary>
    /// <example>Hello\nWorld</example>
    public string Message { get; set; }

    /// <summary>
    /// 结束符
    /// </summary>
    /// <example>none/crc16/r/n/rn</example>
    /// <remarks>
    /// - none: 不添加结束符
    /// - crc16: 添加CRC16校验
    /// - r: 添加回车符 (0x0D)
    /// - n: 添加换行符 (0x0A)
    /// - rn: 添加回车换行符 (0x0D 0x0A)
    /// </remarks>
    public string EndWith { get; set; } = "none";
  }
}