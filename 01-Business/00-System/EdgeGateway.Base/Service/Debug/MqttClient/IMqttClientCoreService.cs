namespace EdgeGateway.Base.Network;

/// <summary>
/// MQTT客户端核心服务接口
/// </summary>
public interface IMqttClientCoreService
{
    /// <summary>
    /// 连接到MQTT服务器
    /// </summary>
    /// <param name="config">连接配置</param>
    /// <returns>连接是否成功</returns>
    Task<bool> ConnectAsync(MqttClientConfig config);

    /// <summary>
    /// 断开连接
    /// </summary>
    Task DisconnectAsync();

    /// <summary>
    /// 发布消息
    /// </summary>
    /// <param name="message">消息</param>
    /// <returns>发布是否成功</returns>
    Task<bool> PublishMessageAsync(MqttMessage message);

    /// <summary>
    /// 订阅主题
    /// </summary>
    /// <param name="message">订阅信息</param>
    /// <returns>订阅是否成功</returns>
    Task<bool> SubscribeAsync(MqttMessage message);

    /// <summary>
    /// 取消订阅
    /// </summary>
    /// <param name="topic">主题</param>
    /// <returns>取消订阅是否成功</returns>
    Task<bool> UnsubscribeAsync(string topic);

    /// <summary>
    /// 获取已订阅的主题
    /// </summary>
    /// <returns>订阅主题列表</returns>
    List<string> GetSubscriptions();

    /// <summary>
    /// 获取消息历史
    /// </summary>
    /// <returns>消息历史列表</returns>
    List<MqttMessageLog> GetMessageHistory();

    /// <summary>
    /// 清空消息历史
    /// </summary>
    void ClearMessageHistory();

    /// <summary>
    /// 获取连接状态
    /// </summary>
    /// <returns>连接状态信息</returns>
    object GetStatus();

    /// <summary>
    /// 心跳接口 - 更新最后活动时间
    /// </summary>
    void Heartbeat();

    /// <summary>
    /// 获取最后活动时间
    /// </summary>
    /// <returns>最后活动时间</returns>
    DateTime GetLastActivityTime();

    /// <summary>
    /// 检查是否超时
    /// </summary>
    /// <param name="timeoutMinutes">超时时间（分钟）</param>
    /// <returns>是否超时</returns>
    bool IsTimeout(int timeoutMinutes = 30);
}
