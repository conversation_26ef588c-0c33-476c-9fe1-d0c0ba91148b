using EdgeGateway.Base.Service.Debug.TcpServer.Models;
using HslCommunication.BasicFramework;

namespace EdgeGateway.Base.Service.Debug.TcpClient
{
  /// <summary>
  /// TCP客户端服务
  /// 用于处理TCP客户端的核心业务逻辑
  /// </summary>
  public class TcpClientService : ISingleton
  {
    /// <summary>
    /// TCP客户端实例
    /// </summary>
    private System.Net.Sockets.TcpClient _client;

    /// <summary>
    /// 连接状态标志
    /// </summary>
    private bool _isConnected;

    /// <summary>
    /// 消息推送服务
    /// </summary>
    private readonly IMessagePushService _messagePushService;

    /// <summary>
    /// 日志服务
    /// </summary>
    private readonly ILogger<TcpClientService> _log;

    /// <summary>
    /// 构造函数
    /// </summary>
    public TcpClientService(IMessagePushService messagePushService, ILogger<TcpClientService> log)
    {
      _messagePushService = messagePushService;
      _log = log;
    }

    /// <summary>
    /// 连接到TCP服务器
    /// </summary>
    /// <param name="ip">服务器IP地址</param>
    /// <param name="port">服务器端口号</param>
    /// <exception cref="Exception">连接失败时抛出异常</exception>
    public async Task Connect(string ip, int port)
    {
      if (_isConnected)
        return;

      try
      {
        _client = new System.Net.Sockets.TcpClient();
        await _client.ConnectAsync(ip, port);
        _isConnected = true;

        // 开始异步接收数据
        _ = Task.Run(ReceiveDataAsync);
      }
      catch (Exception ex)
      {
        _log.LogError(ex, "连接服务器失败");
        throw new Exception($"连接服务器失败: {ex.Message}");
      }
    }

    /// <summary>
    /// 断开与服务器的连接
    /// </summary>
    public async Task Disconnect()
    {
      if (!_isConnected)
        return;

      try
      {
        _isConnected = false;
        _client?.Close();
        _client = null;
      }
      catch (Exception ex)
      {
        _log.LogError(ex, "断开连接时发生错误");
        throw;
      }
    }

    /// <summary>
    /// 异步接收数据
    /// </summary>
    private async Task ReceiveDataAsync()
    {
      try
      {
        using var stream = _client.GetStream();
        var buffer = new byte[4096];

        while (_isConnected)
        {
          var bytesRead = await stream.ReadAsync(buffer, 0, buffer.Length);
          if (bytesRead == 0)
            break;

          var receivedData = new byte[bytesRead];
          Array.Copy(buffer, receivedData, bytesRead);

          await ProcessReceivedDataAsync(receivedData);
        }
      }
      catch (Exception ex)
      {
        _log.LogError(ex, "接收数据时发生错误");
      }
      finally
      {
        _isConnected = false;
        _client?.Close();
      }
    }

    /// <summary>
    /// 处理接收到的数据
    /// </summary>
    /// <param name="data">接收到的原始字节数据</param>
    private async Task ProcessReceivedDataAsync(byte[] data)
    {
      try
      {
        var message = Encoding.UTF8.GetString(data).Trim();
        if (string.IsNullOrEmpty(message))
        {
          return;
        }

        var messageModel = new MessageModel
        {
          Content = FormatMessage(message),
          Timestamp = DateTime.Now,
          ClientIP = _client?.Client?.RemoteEndPoint?.ToString()
        };

        await _messagePushService.PushMessageAsync("TcpClient_Debugger", messageModel);
      }
      catch (Exception ex)
      {
        _log.LogError(ex, "处理数据时发生错误");
      }
    }

    /// <summary>
    /// 格式化消息内容
    /// </summary>
    /// <param name="message">原始消息内容</param>
    /// <returns>格式化后的消息</returns>
    private string FormatMessage(string message)
    {
      try
      {
        if (message.All(c => "0123456789ABCDEFabcdef ".Contains(c)))
        {
          var hexString = message.Replace(" ", "").ToUpper();
          var formatted = string.Join(" ", Enumerable.Range(0, hexString.Length / 2)
            .Select(i => hexString.Substring(i * 2, 2)));
          return $"HEX: {formatted}";
        }

        return $"TEXT: {message}";
      }
      catch
      {
        return $"RAW: {message}";
      }
    }

    /// <summary>
    /// 发送数据到服务器
    /// </summary>
    private async Task SendDataAsync(byte[] data)
    {
      try
      {
        if (_isConnected && _client?.GetStream() != null)
        {
          await _client.GetStream().WriteAsync(data, 0, data.Length);
        }
      }
      catch (Exception ex)
      {
        _log.LogError(ex, "发送数据时发生错误");
        throw;
      }
    }

    /// <summary>
    /// 发送消息到服务器
    /// </summary>
    /// <param name="message">消息内容和配置</param>
    public async Task SendMessage(SendMessageInput message)
    {
      if (!_isConnected)
      {
        throw new Exception("未连接到服务器");
      }

      if (string.IsNullOrEmpty(message.Message))
      {
        throw new Exception("消息内容不能为空");
      }

      var messages = message.Message.Split('\n');
      foreach (var msg in messages)
      {
        var data = Encoding.UTF8.GetBytes(msg);

        switch (message.EndWith)
        {
          case "crc16":
            data = HslCommunication.Serial.SoftCRC16.CRC16(data);
            break;
          case "r":
            data = SoftBasic.SpliceArray(data, new byte[] { 0x0D });
            break;
          case "n":
            data = SoftBasic.SpliceArray(data, new byte[] { 0x0A });
            break;
          case "rn":
            data = SoftBasic.SpliceArray(data, new byte[] { 0x0D, 0x0A });
            break;
        }

        await SendDataAsync(data);
        await Task.Delay(100); // 模拟真实环境的发送延迟
      }
    }

    /// <summary>
    /// 获取连接状态
    /// </summary>
    public (bool IsConnected, string RemoteEndPoint) GetStatus()
    {
      return (_isConnected, _client?.Client?.RemoteEndPoint?.ToString());
    }
  }

  /// <summary>
  /// 发送消息输入模型
  /// </summary>
  public class SendMessageInput
  {
    /// <summary>
    /// 消息内容
    /// 支持多行消息，使用\n分隔
    /// </summary>
    /// <example>Hello\nWorld</example>
    public string Message { get; set; }

    /// <summary>
    /// 结束符
    /// </summary>
    /// <example>none/crc16/r/n/rn</example>
    /// <remarks>
    /// - none: 不添加结束符
    /// - crc16: 添加CRC16校验
    /// - r: 添加回车符 (0x0D)
    /// - n: 添加换行符 (0x0A)
    /// - rn: 添加回车换行符 (0x0D 0x0A)
    /// </remarks>
    public string EndWith { get; set; } = "none";
  }
}