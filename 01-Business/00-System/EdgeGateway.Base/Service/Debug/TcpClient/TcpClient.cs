namespace EdgeGateway.Base.Service.Debug.TcpClient
{
  /// <summary>
  /// TCP客户端控制器
  /// 提供TCP客户端相关的API接口
  /// </summary>
  [ApiDescriptionSettings("调试助手")]
  [Route("/api/debug/tcpClient")]
  public class TcpDebugClient : IDynamicApiController
  {
    private readonly TcpClientService _tcpClientService;
    private readonly ILogger<TcpDebugClient> _log;

    /// <summary>
    /// 构造函数
    /// </summary>
    public TcpDebugClient(TcpClientService tcpClientService, ILogger<TcpDebugClient> log)
    {
      _tcpClientService = tcpClientService;
      _log = log;
    }

    /// <summary>
    /// 连接到服务器
    /// </summary>
    [HttpPost("connect")]
    [OperationId(nameof(ConnectToServer))]
    [DisplayName("连接到服务器")]
    public async Task ConnectToServer([FromQuery] string ip, [FromQuery] int port)
    {
      try
      {
        await _tcpClientService.Connect(ip, port);
      }
      catch (Exception ex)
      {
        _log.LogError(ex, "连接服务器失败");
        throw Oops.Oh(ex.Message);
      }
    }

    /// <summary>
    /// 断开与服务器的连接
    /// </summary>
    [HttpPost("disconnect")]
    [OperationId(nameof(DisconnectFromServer))]
    [DisplayName("断开连接")]
    public async Task DisconnectFromServer()
    {
      try
      {
        await _tcpClientService.Disconnect();
      }
      catch (Exception ex)
      {
        _log.LogError(ex, "断开连接失败");
        throw Oops.Oh(ex.Message);
      }
    }

    /// <summary>
    /// 获取客户端连接状态
    /// </summary>
    [HttpGet("status")]
    [OperationId(nameof(GetStatus))]
    [DisplayName("获取连接状态")]
    public async Task<dynamic> GetStatus()
    {
      var (isConnected, remoteEndPoint) = _tcpClientService.GetStatus();
      return new
      {
        IsConnected = isConnected,
        RemoteEndPoint = remoteEndPoint
      };
    }

    /// <summary>
    /// 发送消息到服务器
    /// </summary>
    [HttpPost("send")]
    [OperationId(nameof(SendMessage))]
    [DisplayName("发送消息")]
    public async Task SendMessage([FromBody] SendMessageInput message)
    {
      try
      {
        await _tcpClientService.SendMessage(message);
      }
      catch (Exception ex)
      {
        _log.LogError(ex, "发送消息失败");
        throw Oops.Oh(ex.Message);
      }
    }
  }
}
