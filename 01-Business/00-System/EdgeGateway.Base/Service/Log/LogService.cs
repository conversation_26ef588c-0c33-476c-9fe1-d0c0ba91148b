using System.Runtime.CompilerServices;
using System.Threading.Channels;
using System.Text;
using System.IO.MemoryMappedFiles;
using EdgeGateway.Base.Entity.Dto.Log;

namespace EdgeGateway.Base.Service.Log;

/// <summary>
///     日志服务
/// </summary>
[ApiDescriptionSettings("日志服务")]
[Route("/api/log")]
public class LogService : ITransient, IDynamicApiController
{
  /// <summary>
  ///     日志文件路径
  /// </summary>
  private readonly string _logPath;

  /// <summary>
  ///     日志记录器
  /// </summary>
  private readonly ILogger<LogService> _logger;

  /// <summary>
  ///     构造函数
  /// </summary>
  public LogService(ILogger<LogService> logger)
    {
        _logPath = Path.Combine(AppContext.BaseDirectory, "logs");
        _logger = logger;
    }

  /// <summary>
  ///     获取日志文件列表(按级别分组)
  /// </summary>
  [HttpGet("files")]
    [OperationId(nameof(GetLogFiles))]
    public List<LogFileGroupDto> GetLogFiles()
    {
        try
        {
            if (!Directory.Exists(_logPath))
                return new List<LogFileGroupDto>();

            // 获取所有日志目录
            var logDirs = Directory.GetDirectories(_logPath)
                .Select(Path.GetFileName)
                .Where(dir => !string.IsNullOrEmpty(dir))
                .ToList();

            var result = new List<LogFileGroupDto>();

            // 处理根目录下的日志文件
            var rootFiles = GetLogFilesFromDirectory(_logPath, "General");
            if (rootFiles.Any())
                result.Add(new LogFileGroupDto
                {
                    LogLevel = "General",
                    Files = rootFiles
                });

            // 处理各个级别目录下的日志文件
            foreach (var dir in logDirs)
            {
                var dirPath = Path.Combine(_logPath, dir);
                var files = GetLogFilesFromDirectory(dirPath, dir);

                if (files.Any())
                    result.Add(new LogFileGroupDto
                    {
                        LogLevel = dir, // 日志级别
                        Files = files // 该级别下的日志文件列表
                    });
            }

            return result;
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "获取日志文件列表失败");
            throw Oops.Oh(ErrorCode.D3000);
        }
    }

  /// <summary>
  ///     从指定目录获取日志文件信息
  /// </summary>
  private List<LogFileDto> GetLogFilesFromDirectory(string directory, string logLevel)
    {
        // 获取指定目录下的所有日志文件
        return Directory.GetFiles(directory, "*.log")
            .Select(f => new FileInfo(f))
            .Select(f => new LogFileDto
            {
                FileName = f.Name, // 文件名
                FileSize = Math.Round(f.Length / 1024.0, 2), // 文件大小
                LastWriteTime = f.LastWriteTime, // 最后修改时间
                LogLevel = logLevel, // 日志级别
                LineCount = 0 // 暂不计算行数，提高列表加载性能
            })
            .OrderByDescending(x => x.LastWriteTime)
            .ToList();
    }

  /// <summary>
  ///     读取日志文件内容
  /// </summary>
  [HttpPost("content")]
    [OperationId(nameof(GetLogContentAsync))]
    public async Task<LogContentDto> GetLogContentAsync([FromBody] LogContentInput input)
    {
        try
        {
            // 根据日志级别确定目录路径
            var directoryPath = input.LogLevel == "General" ? _logPath : Path.Combine(_logPath, input.LogLevel);
            var filePath = Path.Combine(directoryPath, input.FileName);

            if (!File.Exists(filePath))
                return new LogContentDto
                {
                    Content = string.Empty,
                    LogLevel = input.LogLevel,
                    FileInfo = null,
                    TotalLines = 0,
                    StartLine = 0,
                    EndLine = 0,
                    HasMore = false
                };

            // 获取文件信息（包含总行数）
            var fileInfo = await GetFileInfoWithLineCountAsync(filePath, input.LogLevel);

            // 如果只需要文件信息，直接返回
            if (input.InfoOnly)
                return new LogContentDto
                {
                    Content = string.Empty,
                    LogLevel = input.LogLevel,
                    FileInfo = fileInfo,
                    TotalLines = fileInfo.LineCount,
                    StartLine = 0,
                    EndLine = 0,
                    HasMore = false
                };

            // 读取指定范围的内容
            var result = await ReadLogContentByRangeAsync(filePath, input, fileInfo.LineCount);
            result.LogLevel = input.LogLevel;
            result.FileInfo = fileInfo;
            result.TotalLines = fileInfo.LineCount;

            return result;
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "读取日志文件失败");
            throw Oops.Oh(ErrorCode.D3001);
        }
    }

    /// <summary>
    /// 高性能流式获取完整日志文件内容
    /// </summary>
    [HttpPost("stream-full")]
    [OperationId(nameof(GetLogStreamFullAsync))]
    public async Task<LogContentDto> GetLogStreamFullAsync([FromBody] LogContentInput input)
    {
        try
        {
            // 根据日志级别确定目录路径
            var directoryPath = input.LogLevel == "General" ? _logPath : Path.Combine(_logPath, input.LogLevel);
            var filePath = Path.Combine(directoryPath, input.FileName);

            if (!File.Exists(filePath))
                return new LogContentDto
                {
                    Content = string.Empty,
                    LogLevel = input.LogLevel,
                    FileInfo = null,
                    TotalLines = 0,
                    StartLine = 0,
                    EndLine = 0,
                    HasMore = false
                };

            // 使用高性能流式读取整个文件
            var result = await ReadLogFileStreamAsync(filePath, input);
            result.LogLevel = input.LogLevel;
            result.FileInfo = await GetFileInfoWithLineCountAsync(filePath, input.LogLevel);

            return result;
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "流式读取日志文件失败");
            throw Oops.Oh(ErrorCode.D3001);
        }
    }

    /// <summary>
    /// 高性能分块流式获取日志内容（用于大文件）
    /// </summary>
    [HttpPost("stream-chunked")]
    [OperationId(nameof(GetLogStreamChunkedAsync))]
    public async IAsyncEnumerable<LogContentDto> GetLogStreamChunkedAsync(
        [FromBody] LogContentInput input,
        [EnumeratorCancellation] CancellationToken cancellationToken = default)
    {
        // 根据日志级别确定目录路径
        var directoryPath = input.LogLevel == "General" ? _logPath : Path.Combine(_logPath, input.LogLevel);
        var filePath = Path.Combine(directoryPath, input.FileName);

        if (!File.Exists(filePath))
        {
            yield return new LogContentDto
            {
                Content = string.Empty,
                LogLevel = input.LogLevel,
                FileInfo = null,
                TotalLines = 0,
                StartLine = 0,
                EndLine = 0,
                HasMore = false
            };
            yield break;
        }

        var chunkSize = Math.Max(input.Lines, 1000); // 最小1000行每块
        var currentLine = 1;
        var totalLines = await GetFileLineCountAsync(filePath);

        await foreach (var chunk in ReadLogFileChunksAsync(filePath, chunkSize, cancellationToken))
        {
            var result = new LogContentDto
            {
                Content = chunk.Content,
                LogLevel = input.LogLevel,
                FileInfo = await GetFileInfoAsync(filePath, input.LogLevel),
                TotalLines = totalLines,
                StartLine = chunk.StartLine,
                EndLine = chunk.EndLine,
                HasMore = chunk.EndLine < totalLines
            };

            yield return result;

            if (chunk.EndLine >= totalLines)
                break;
        }
    }

  /// <summary>
  ///     获取文件信息
  /// </summary>
  private async Task<LogFileDto> GetFileInfoAsync(string filePath, string logLevel)
    {
        var fileInfo = new FileInfo(filePath);
        return await Task.FromResult(new LogFileDto
        {
            FileName = fileInfo.Name,
            FileSize = Math.Round(fileInfo.Length / 1024.0, 2),
            LastWriteTime = fileInfo.LastWriteTime,
            LogLevel = logLevel,
            LineCount = 0 // 不计算行数，提高性能
        });
    }

  /// <summary>
  ///     获取文件信息（包含行数）
  /// </summary>
  private async Task<LogFileDto> GetFileInfoWithLineCountAsync(string filePath, string logLevel)
    {
        var fileInfo = new FileInfo(filePath);
        var lineCount = await CountLinesAsync(filePath);

        return new LogFileDto
        {
            FileName = fileInfo.Name,
            FileSize = Math.Round(fileInfo.Length / 1024.0, 2),
            LastWriteTime = fileInfo.LastWriteTime,
            LogLevel = logLevel,
            LineCount = lineCount
        };
    }

  /// <summary>
  ///     高效计算文件行数
  /// </summary>
  private async Task<int> CountLinesAsync(string filePath)
    {
        try
        {
            var lineCount = 0;
            using (var reader = new StreamReader(filePath))
            {
                while (await reader.ReadLineAsync() != null) lineCount++;
            }

            return lineCount;
        }
        catch (Exception ex)
        {
            _logger.LogWarning(ex, "计算文件行数失败: {FilePath}", filePath);
            return 0;
        }
    }

  /// <summary>
  ///     按范围读取日志内容
  /// </summary>
  private async Task<LogContentDto> ReadLogContentByRangeAsync(string filePath, LogContentInput input, int totalLines)
    {
        var content = new StringBuilder();
        var startLine = Math.Max(1, input.StartLine);
        var linesToRead = input.Lines;
        var currentLine = 1;
        var linesReturned = 0;
        var actualStartLine = 0;
        var actualEndLine = 0;

        using (var reader = new StreamReader(filePath))
        {
            string line;
            while ((line = await reader.ReadLineAsync()) != null)
            {
                // 如果还没到起始行，继续跳过
                if (currentLine < startLine)
                {
                    currentLine++;
                    continue;
                }

                currentLine++;
            }
        }

        // 计算是否还有更多数据
        var hasMore = false;
        // 对于普通分页，检查是否还有更多行
        hasMore = actualEndLine < totalLines;

        return new LogContentDto
        {
            Content = content.ToString().TrimEnd('\r', '\n'),
            StartLine = actualStartLine,
            EndLine = actualEndLine,
            HasMore = hasMore,
            TotalLines = totalLines
        };
    }


  /// <summary>
  ///     优化的分页读取方法（不计算总行数，提高性能）
  /// </summary>
  private async Task<LogContentDto> ReadLogContentByRangeOptimizedAsync(string filePath, LogContentInput input)
    {
        var content = new StringBuilder();
        var startLine = Math.Max(1, input.StartLine);
        var linesToRead = input.Lines;
        var currentLine = 1;
        var linesReturned = 0;
        var actualStartLine = 0;
        var actualEndLine = 0;
        var hasMore = false;

        using (var reader = new StreamReader(filePath))
        {
            string line;
            while ((line = await reader.ReadLineAsync()) != null)
            {
                // 如果还没到起始行，继续跳过
                if (currentLine < startLine)
                {
                    currentLine++;
                    continue;
                }

                if (actualStartLine == 0)
                    actualStartLine = currentLine;

                content.AppendLine(line);
                linesReturned++;
                actualEndLine = currentLine;

                // 如果已经读取了足够的行数，检查是否还有更多数据
                if (linesReturned >= linesToRead)
                {
                    // 尝试再读一行来判断是否还有更多数据
                    var nextLine = await reader.ReadLineAsync();
                    hasMore = nextLine != null;
                    break;
                }

                currentLine++;
            }
        }

        // 计算总行数
        var totalLines = await GetFileLineCountAsync(filePath);

        return new LogContentDto
        {
            Content = content.ToString().TrimEnd('\r', '\n'),
            StartLine = actualStartLine,
            EndLine = actualEndLine,
            HasMore = hasMore,
            TotalLines = totalLines
        };
    }

  /// <summary>
  ///     获取日志文件详细信息（包含行数）
  /// </summary>
  [HttpGet("file-info")]
    [OperationId(nameof(GetLogFileInfoAsync))]
    public async Task<LogFileDto> GetLogFileInfoAsync([FromQuery] string logLevel, [FromQuery] string fileName)
    {
        try
        {
            var directoryPath = logLevel == "General" ? _logPath : Path.Combine(_logPath, logLevel);
            var filePath = Path.Combine(directoryPath, fileName);

            if (!File.Exists(filePath))
                throw Oops.Oh(ErrorCode.D3002);

            return await GetFileInfoWithLineCountAsync(filePath, logLevel);
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "获取日志文件信息失败");
            throw Oops.Oh(ErrorCode.D3002);
        }
    }

  /// <summary>
  ///     下载日志文件
  /// </summary>
  [HttpGet("download")]
    [OperationId(nameof(DownloadLogFile))]
    public IActionResult DownloadLogFile([FromQuery] string logLevel, [FromQuery] string fileName)
    {
        try
        {
            var directoryPath = Path.Combine(_logPath, logLevel);

            var filePath = Path.Combine(directoryPath, fileName);

            if (!File.Exists(filePath))
                throw Oops.Oh(ErrorCode.D3002);

            var fileStream = new FileStream(filePath, FileMode.Open, FileAccess.Read);
            return new FileStreamResult(fileStream, "application/octet-stream")
            {
                FileDownloadName = fileName
            };
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "下载日志文件失败");
            throw Oops.Oh(ErrorCode.D3002);
        }
    }

  /// <summary>
  ///     删除日志文件
  /// </summary>
  [HttpDelete("delete")]
    [OperationId(nameof(DeleteLogFile))]
    public IActionResult DeleteLogFile([FromQuery] string logLevel, [FromQuery] string fileName)
    {
        try
        {
            // 拼接地址
            var directoryPath = Path.Combine(_logPath, logLevel);
            // 
            var filePath = Path.Combine(directoryPath, fileName);

            if (!File.Exists(filePath))
                throw Oops.Oh(ErrorCode.D3003);

            File.Delete(filePath);

            return new JsonResult(new { success = true, message = "文件删除成功" });
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "删除日志文件失败");
            throw Oops.Oh(ErrorCode.D3003);
        }
    }

  /// <summary>
  ///     批量删除日志文件
  /// </summary>
  [HttpPost("batch-delete")]
    [OperationId(nameof(BatchDeleteLogFiles))]
    public IActionResult BatchDeleteLogFiles([FromBody] List<LogFileDeleteInput> files)
    {
        try
        {
            foreach (var file in files)
            {
                var directoryPath = Path.Combine(_logPath, file.LogLevel);

                var filePath = Path.Combine(directoryPath, file.FileName);

                if (File.Exists(filePath)) File.Delete(filePath);
            }

            return new JsonResult(new { success = true, message = "文件批量删除成功" });
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "批量删除日志文件失败");
            throw Oops.Oh(ErrorCode.D3003);
        }
    }

  /// <summary>
  ///     分页预览日志内容（高性能）
  /// </summary>
  [HttpPost("preview")]
    [OperationId(nameof(GetLogPreviewAsync))]
    public async Task<LogContentDto> GetLogPreviewAsync([FromBody] LogContentInput input)
    {
        try
        {
            // 设置合理的默认值
            input.Lines = Math.Min(input.Lines, 10000); // 提高最大行数限制到10000
            input.StartLine = Math.Max(input.StartLine, 1); // 确保起始行号有效

            // 根据日志级别确定目录路径
            var directoryPath = input.LogLevel == "General" ? _logPath : Path.Combine(_logPath, input.LogLevel);
            var filePath = Path.Combine(directoryPath, input.FileName);

            if (!File.Exists(filePath))
                return new LogContentDto
                {
                    Content = string.Empty,
                    LogLevel = input.LogLevel,
                    FileInfo = null,
                    TotalLines = 0,
                    StartLine = 0,
                    EndLine = 0,
                    HasMore = false
                };

            // 获取文件基本信息（不计算行数，提高性能）
            var fileInfo = await GetFileInfoAsync(filePath, input.LogLevel);

            // 使用高效的分页读取
            var result = await ReadLogContentByRangeOptimizedAsync(filePath, input);
            result.LogLevel = input.LogLevel;
            result.FileInfo = fileInfo;

            return result;
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "预览日志文件失败");
            throw Oops.Oh(ErrorCode.D3001);
        }
    }

  /// <summary>
  ///     高性能批量日志获取（支持大数据集）
  /// </summary>
  [HttpPost("bulk")]
    [OperationId(nameof(GetLogBulkAsync))]
    public async Task<LogContentDto> GetLogBulkAsync([FromBody] LogContentInput input)
    {
        try
        {
            // 设置更高的性能限制
            input.Lines = Math.Min(input.Lines, 50000); // 支持最大50000行
            input.StartLine = Math.Max(input.StartLine, 1);

            // 根据日志级别确定目录路径
            var directoryPath = input.LogLevel == "General" ? _logPath : Path.Combine(_logPath, input.LogLevel);
            var filePath = Path.Combine(directoryPath, input.FileName);

            if (!File.Exists(filePath))
                return new LogContentDto
                {
                    Content = string.Empty,
                    LogLevel = input.LogLevel,
                    FileInfo = null,
                    TotalLines = 0,
                    StartLine = 0,
                    EndLine = 0,
                    HasMore = false
                };

            // 使用高效的批量读取
            var result = await ReadLogContentBulkAsync(filePath, input);
            result.LogLevel = input.LogLevel;
            result.FileInfo = await GetFileInfoAsync(filePath, input.LogLevel);

            return result;
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "批量获取日志内容失败");
            throw Oops.Oh(ErrorCode.D3001);
        }
    }

  /// <summary>
  ///     流式读取日志文件内容
  /// </summary>
  /// <param name="input">日志内容查询参数</param>
  /// <returns>日志内容流</returns>
  [HttpPost("stream-content")]
    [OperationId(nameof(StreamLogContentAsync))]
    public async IAsyncEnumerable<StreamLogContentDto> StreamLogContentAsync(
        [FromBody] LogContentInput input,
        [EnumeratorCancellation] CancellationToken cancellationToken = default)
    {
        // 创建无界通道
        var channel = Channel.CreateUnbounded<StreamLogContentDto>();
        var writer = channel.Writer;

        // 启动后台任务处理日志读取
        _ = Task.Run(async () =>
        {
            try
            {
                // 根据日志级别确定目录路径
                var directoryPath = input.LogLevel == "General" ? _logPath : Path.Combine(_logPath, input.LogLevel);
                var filePath = Path.Combine(directoryPath, input.FileName);

                if (!File.Exists(filePath))
                {
                    await writer.WriteAsync(new StreamLogContentDto
                    {
                        Content = string.Empty,
                        LogLevel = input.LogLevel,
                        FileInfo = null,
                        IsCompleted = true,
                        LineNumber = 0,
                        TotalLinesRead = 0
                    }, cancellationToken);
                    writer.Complete();
                    return;
                }

                // 获取文件信息
                var fileInfo = await GetFileInfoAsync(filePath, input.LogLevel);

                // 使用StreamReader读取文件内容
                var linesRead = 0;
                var linesReturned = 0;
                var batchSize = 100; // 每批返回的行数
                var currentBatch = new List<string>(batchSize);

                using (var reader = new StreamReader(filePath))
                {
                    string line;
                    while ((line = await reader.ReadLineAsync()) != null &&
                           linesReturned < input.Lines &&
                           !cancellationToken.IsCancellationRequested)
                    {
                        linesRead++;

                        // 定期检查是否取消
                        if (linesRead % 1000 == 0 && cancellationToken.IsCancellationRequested) break;
                    }

                    // 处理剩余的行
                    if (currentBatch.Count > 0)
                    {
                        var content = string.Join(Environment.NewLine, currentBatch);
                        await writer.WriteAsync(new StreamLogContentDto
                        {
                            Content = content,
                            LogLevel = input.LogLevel,
                            FileInfo = fileInfo,
                            IsCompleted = true,
                            LineNumber = linesRead,
                            TotalLinesRead = linesRead,
                            LinesReturned = linesReturned
                        }, cancellationToken);
                    }
                    // 如果没有剩余行但需要发送完成信号
                    else if (linesReturned > 0)
                    {
                        await writer.WriteAsync(new StreamLogContentDto
                        {
                            Content = string.Empty,
                            LogLevel = input.LogLevel,
                            FileInfo = fileInfo,
                            IsCompleted = true,
                            LineNumber = linesRead,
                            TotalLinesRead = linesRead,
                            LinesReturned = linesReturned
                        }, cancellationToken);
                    }
                }
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "流式读取日志文件失败");
                try
                {
                    await writer.WriteAsync(new StreamLogContentDto
                    {
                        Content = string.Empty,
                        Error = ex.Message,
                        IsCompleted = true
                    }, cancellationToken);
                }
                catch
                {
                    // 忽略写入错误
                }
            }
            finally
            {
                writer.Complete();
            }
        }, cancellationToken);

        // 从通道读取并返回结果
        await foreach (var item in channel.Reader.ReadAllAsync(cancellationToken)) yield return item;
    }

  /// <summary>
  ///     高性能批量读取日志内容
  /// </summary>
  private async Task<LogContentDto> ReadLogContentBulkAsync(string filePath, LogContentInput input)
    {
        var content = new StringBuilder();
        var startLine = Math.Max(1, input.StartLine);
        var linesToRead = input.Lines;
        var currentLine = 1;
        var linesReturned = 0;
        var actualStartLine = 0;
        var actualEndLine = 0;

        using (var reader = new StreamReader(filePath))
        {
            string line;
            while ((line = await reader.ReadLineAsync()) != null)
            {
                // 如果还没到起始行，继续跳过
                if (currentLine < startLine)
                {
                    currentLine++;
                    continue;
                }

                if (actualStartLine == 0)
                    actualStartLine = currentLine;

                content.AppendLine(line);
                linesReturned++;
                actualEndLine = currentLine;

                // 如果已经读取了足够的行数，停止读取
                if (linesReturned >= linesToRead)
                    break;

                currentLine++;
            }
        }

        // 检查是否还有更多数据
        var hasMore = await HasMoreDataAsync(filePath, input, actualEndLine + 1);

        // 计算总行数
        var totalLines = await GetFileLineCountAsync(filePath);

        return new LogContentDto
        {
            Content = content.ToString().TrimEnd('\r', '\n'),
            StartLine = actualStartLine,
            EndLine = actualEndLine,
            HasMore = hasMore,
            TotalLines = totalLines
        };
    }

  /// <summary>
  ///     检查是否还有更多数据
  /// </summary>
  private async Task<bool> HasMoreDataAsync(string filePath, LogContentInput input, int afterLine)
    {
        try
        {
            // 简单检查是否还有更多行
            var currentLine = 1;
            using (var reader = new StreamReader(filePath))
            {
                string line;
                while ((line = await reader.ReadLineAsync()) != null)
                {
                    if (currentLine > afterLine)
                        return true;
                    currentLine++;
                }
            }

            return false;
        }
        catch
        {
            return false;
        }
    }

  /// <summary>
  ///     快速计算文件总行数
  /// </summary>
  private async Task<int> GetFileLineCountAsync(string filePath)
    {
        try
        {
            var lineCount = 0;
            using (var reader = new StreamReader(filePath, Encoding.UTF8, true, 65536)) // 64KB buffer
            {
                while (await reader.ReadLineAsync() != null) lineCount++;
            }

            return lineCount;
        }
        catch
        {
            return 0;
        }
    }

    /// <summary>
    /// 高性能流式读取整个日志文件
    /// </summary>
    private async Task<LogContentDto> ReadLogFileStreamAsync(string filePath, LogContentInput input)
    {
        var fileInfo = new FileInfo(filePath);
        var fileSize = fileInfo.Length;

        // 对于小文件（<10MB），直接读取全部内容
        if (fileSize < 10 * 1024 * 1024)
        {
            return await ReadSmallFileAsync(filePath);
        }
        // 对于大文件，使用内存映射文件
        else if (fileSize < 100 * 1024 * 1024)
        {
            return await ReadMediumFileAsync(filePath);
        }
        // 对于超大文件（>100MB），使用分块读取
        else
        {
            return await ReadLargeFileAsync(filePath, input);
        }
    }

    /// <summary>
    /// 读取小文件（<10MB）
    /// </summary>
    private async Task<LogContentDto> ReadSmallFileAsync(string filePath)
    {
        try
        {
            var content = await File.ReadAllTextAsync(filePath, Encoding.UTF8);
            var lines = content.Split('\n', StringSplitOptions.None);
            var totalLines = lines.Length;

            return new LogContentDto
            {
                Content = content.TrimEnd('\r', '\n'),
                StartLine = 1,
                EndLine = totalLines,
                HasMore = false,
                TotalLines = totalLines
            };
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "读取小文件失败: {FilePath}", filePath);
            throw;
        }
    }

    /// <summary>
    /// 读取中等文件（10MB-100MB）使用内存映射
    /// </summary>
    private async Task<LogContentDto> ReadMediumFileAsync(string filePath)
    {
        try
        {
            using var mmf = MemoryMappedFile.CreateFromFile(filePath, FileMode.Open, "logfile", 0, MemoryMappedFileAccess.Read);
            using var accessor = mmf.CreateViewAccessor(0, 0, MemoryMappedFileAccess.Read);

            var fileInfo = new FileInfo(filePath);
            var fileSize = fileInfo.Length;

            var buffer = new byte[fileSize];
            accessor.ReadArray(0, buffer, 0, (int)fileSize);

            var content = Encoding.UTF8.GetString(buffer);
            var lines = content.Split('\n', StringSplitOptions.None);
            var totalLines = lines.Length;

            return await Task.FromResult(new LogContentDto
            {
                Content = content.TrimEnd('\r', '\n'),
                StartLine = 1,
                EndLine = totalLines,
                HasMore = false,
                TotalLines = totalLines
            });
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "读取中等文件失败: {FilePath}", filePath);
            // 回退到普通读取方式
            return await ReadSmallFileAsync(filePath);
        }
    }

    /// <summary>
    /// 读取大文件（>100MB）使用分块策略
    /// </summary>
    private async Task<LogContentDto> ReadLargeFileAsync(string filePath, LogContentInput input)
    {
        try
        {
            var maxLines = Math.Min(input.Lines, 50000); // 大文件最多返回50000行
            var content = new StringBuilder();
            var linesRead = 0;
            var totalLines = await GetFileLineCountAsync(filePath);

            using var reader = new StreamReader(filePath, Encoding.UTF8, true, 131072); // 128KB buffer
            string line;
            var currentLine = 1;

            while ((line = await reader.ReadLineAsync()) != null && linesRead < maxLines)
            {
                content.AppendLine(line);
                linesRead++;
                currentLine++;
            }

            return new LogContentDto
            {
                Content = content.ToString().TrimEnd('\r', '\n'),
                StartLine = 1,
                EndLine = linesRead,
                HasMore = linesRead < totalLines,
                TotalLines = totalLines
            };
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "读取大文件失败: {FilePath}", filePath);
            throw;
        }
    }

    /// <summary>
    /// 高性能分块读取日志文件
    /// </summary>
    private async IAsyncEnumerable<LogChunk> ReadLogFileChunksAsync(
        string filePath,
        int chunkSize,
        [EnumeratorCancellation] CancellationToken cancellationToken = default)
    {
        using var reader = new StreamReader(filePath, Encoding.UTF8, true, 131072); // 128KB buffer
        var currentLine = 1;
        var content = new StringBuilder();
        var linesInChunk = 0;
        var chunkStartLine = 1;

        string line;
        while ((line = await reader.ReadLineAsync()) != null && !cancellationToken.IsCancellationRequested)
        {
            if (linesInChunk == 0)
                chunkStartLine = currentLine;

            content.AppendLine(line);
            linesInChunk++;
            currentLine++;

            if (linesInChunk >= chunkSize)
            {
                yield return new LogChunk
                {
                    Content = content.ToString().TrimEnd('\r', '\n'),
                    StartLine = chunkStartLine,
                    EndLine = currentLine - 1
                };

                content.Clear();
                linesInChunk = 0;
            }
        }

        // 返回最后一个不完整的块
        if (linesInChunk > 0)
        {
            yield return new LogChunk
            {
                Content = content.ToString().TrimEnd('\r', '\n'),
                StartLine = chunkStartLine,
                EndLine = currentLine - 1
            };
        }
    }

    /// <summary>
    /// 日志块数据结构
    /// </summary>
    private class LogChunk
    {
        public string Content { get; set; } = string.Empty;
        public int StartLine { get; set; }
        public int EndLine { get; set; }
    }
}