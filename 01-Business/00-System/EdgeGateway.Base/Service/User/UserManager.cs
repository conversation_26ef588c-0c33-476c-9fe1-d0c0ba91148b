namespace EdgeGateway.Base.User;

/// <summary>
///     当前登录用户
/// </summary>
public class UserManager : IScoped
{
    /// <summary>
    ///     Http上下文访问器
    /// </summary>
    private readonly IHttpContextAccessor _httpContextAccessor;

    /// <summary>
    ///     构造函数
    /// </summary>
    /// <param name="httpContextAccessor">Http上下文访问器</param>
    public UserManager(IHttpContextAccessor httpContextAccessor)
    {
        _httpContextAccessor = httpContextAccessor;
    }

    /// <summary>
    ///     用户ID
    /// </summary>
    public long UserId => Convert.ToInt64(_httpContextAccessor.HttpContext?.User.FindFirst(ClaimConst.UserId)?.Value);

    /// <summary>
    ///     用户账号
    /// </summary>
    public string Account => _httpContextAccessor.HttpContext?.User.FindFirst(ClaimConst.Account)?.Value;

    /// <summary>
    ///     真实姓名
    /// </summary>
    public string RealName => _httpContextAccessor.HttpContext?.User.FindFirst(ClaimConst.RealName)?.Value;

    /// <summary>
    ///     是否超级管理员
    /// </summary>
    public bool SuperAdmin => _httpContextAccessor.HttpContext?.User.FindFirst(ClaimConst.AccountType)?.Value == ((int)AccountTypeEnum.SuperAdmin).ToString();

    /// <summary>
    ///     是否系统管理员
    /// </summary>
    public bool SysAdmin => _httpContextAccessor.HttpContext?.User.FindFirst(ClaimConst.AccountType)?.Value == ((int)AccountTypeEnum.SysAdmin).ToString();
}