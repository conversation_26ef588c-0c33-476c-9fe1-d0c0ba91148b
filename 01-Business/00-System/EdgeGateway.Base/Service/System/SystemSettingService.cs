using EdgeGateway.Base.Entity;
using Furion;
using Microsoft.AspNetCore.Hosting;
using Microsoft.Extensions.Configuration;
using Newtonsoft.Json;
using Newtonsoft.Json.Linq;

namespace EdgeGateway.Base;

/// <summary>
///     系统设置服务
/// </summary>
[ApiDescriptionSettings("系统设置")]
[Route("/api/system/setting")]
public class SystemSettingService : IDynamicApiController, ISingleton
{
  private readonly ILogger<SystemSettingService> _logger;
  private readonly string _settingPath;
  private SystemSetting _currentSetting;
  private readonly object _lockObj = new();

  public SystemSettingService(ILogger<SystemSettingService> logger, IWebHostEnvironment env)
  {
    _logger = logger;
    _settingPath = Path.Combine(env.ContentRootPath, "appsettings.json");
    LoadSetting();
  }

  /// <summary>
  ///     获取系统设置
  /// </summary>
  [HttpGet("get")]
  [DisplayName("获取系统设置")]
  [OperationId(nameof(GetSetting))]
  public SystemSetting GetSetting()
  {
    return _currentSetting;
  }

  /// <summary>
  ///     更新系统设置
  /// </summary>
  [HttpPost("update")]
  [DisplayName("更新系统设置")]
  [OperationId(nameof(UpdateSetting))]
  public async Task<bool> UpdateSetting([FromBody] SystemSetting setting)
  {
    try
    {
      lock (_lockObj)
      {
        _currentSetting = setting;
      }

      await SaveSettingAsync();
      return true;
    }
    catch (Exception ex)
    {
      _logger.LogError(ex, "更新系统设置失败");
      return false;
    }
  }

  /// <summary>
  ///     加载设置
  /// </summary>
  private void LoadSetting()
  {
    try
    {
      var configuration = new ConfigurationBuilder()
          .SetBasePath(Path.GetDirectoryName(_settingPath))
          .AddJsonFile(Path.GetFileName(_settingPath))
          .Build();

      _currentSetting = configuration.GetSection("SystemSetting").Get<SystemSetting>() ?? new SystemSetting();
    }
    catch (Exception ex)
    {
      _logger.LogError(ex, "加载系统设置失败");
      _currentSetting = new SystemSetting();
    }
  }

  /// <summary>
  ///     保存设置
  /// </summary>
  private async Task SaveSettingAsync()
  {
    try
    {
      var configuration = new ConfigurationBuilder()
          .SetBasePath(Path.GetDirectoryName(_settingPath))
          .AddJsonFile(Path.GetFileName(_settingPath))
          .Build();

      var root = JObject.Parse(File.ReadAllText(_settingPath));
      root["SystemSetting"] = JObject.FromObject(_currentSetting);

      await File.WriteAllTextAsync(_settingPath, root.ToString(Formatting.Indented));
    }
    catch (Exception ex)
    {
      _logger.LogError(ex, "保存系统设置失败");
      throw;
    }
  }

  /// <summary>
  ///     上传图片
  /// </summary>
  [HttpPost("uploadImage")]
  [OperationId(nameof(UploadImage))]
  [DisplayName("上传图片")]
  public async Task<string> UploadImage([FromForm] UploadImageRequest request)
  {
    if (request.File == null || request.File.Length == 0)
      throw Oops.Oh("请选择文件");

    // 检查文件类型
    var extension = Path.GetExtension(request.File.FileName).ToLowerInvariant();
    if (!new[] { ".jpg", ".jpeg", ".png", ".ico" }.Contains(extension))
      throw Oops.Oh("只允许上传jpg、png、ico格式的图片");

    try
    {
      // 生成文件名
      var fileName = $"{request.Type}_{Guid.NewGuid()}{extension}";
      var uploadPath = Path.Combine(App.WebHostEnvironment.WebRootPath, "uploads");

      // 确保目录存在
      if (!Directory.Exists(uploadPath))
        Directory.CreateDirectory(uploadPath);

      var filePath = Path.Combine(uploadPath, fileName);

      // 保存文件
      using (var stream = new FileStream(filePath, FileMode.Create))
      {
        await request.File.CopyToAsync(stream);
      }

      // 更新设置
      var fileUrl = $"/uploads/{fileName}";
      lock (_lockObj)
      {
        if (request.Type == "logo")
          _currentSetting.LogoUrl = fileUrl;
        else if (request.Type == "favicon")
          _currentSetting.FaviconUrl = fileUrl;
      }

      await SaveSettingAsync();

      return fileUrl;
    }
    catch (Exception ex)
    {
      _logger.LogError(ex, "上传图片失败");
      throw Oops.Oh("上传图片失败");
    }
  }

  /// <summary>
  ///     删除图片
  /// </summary>
  [HttpDelete("deleteImage")]
  [OperationId(nameof(DeleteImage))]
  [DisplayName("删除图片")]
  public async Task<bool> DeleteImage([FromQuery] string type)
  {
    try
    {
      var fileUrl = type == "logo" ? _currentSetting.LogoUrl : _currentSetting.FaviconUrl;
      if (string.IsNullOrEmpty(fileUrl)) return true;

      var filePath = Path.Combine(App.WebHostEnvironment.WebRootPath, fileUrl.TrimStart('/'));
      if (File.Exists(filePath)) File.Delete(filePath);

      // 更新设置
      lock (_lockObj)
      {
        if (type == "logo")
          _currentSetting.LogoUrl = null;
        else if (type == "favicon") _currentSetting.FaviconUrl = null;
      }

      await SaveSettingAsync();

      return true;
    }
    catch (Exception ex)
    {
      _logger.LogError(ex, "删除图片失败");
      return false;
    }
  }
}