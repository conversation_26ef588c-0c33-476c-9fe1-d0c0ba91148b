using EdgeGateway.Base.SystemStatistics;
using EdgeGateway.Shared.Storage;

namespace EdgeGateway.Base.License;

/// <summary>
/// 许可证服务
/// 负责处理系统许可证的验证、更新和管理
/// </summary>
[ApiDescriptionSettings("授权管理")]
[Route("/api/system/license")]
public class LicenseService : ITransient, IDynamicApiController
{
    /// <summary>
    /// 许可证文件路径
    /// </summary>
    private static readonly string LicenseFilePath = Path.Combine(ConfigPath.ConfigRoot, "license.dat");

    /// <summary>
    /// 数据库上下文
    /// </summary>
    private readonly ISqlSugarClient _db;

    /// <summary>
    /// 配置服务
    /// </summary>
    private readonly IniConfiguration _config;

    /// <summary>
    /// 机器码
    /// </summary>
    private readonly string _machineCode;

    /// <summary>
    /// 构造函数
    /// </summary>
    /// <param name="systemStatistics">系统统计服务</param>
    /// <param name="config">配置服务</param>
    /// <param name="db">数据库上下文</param>
    public LicenseService(ISystemStatisticsService systemStatistics, IniConfiguration config, ISqlSugarClient db)
    {
        _config = config;
        _machineCode = systemStatistics.GetMachineCode();
        _db = db;
    }

    /// <summary>
    /// 获取授权信息
    /// </summary>
    [HttpGet("info")]
    [OperationId(nameof(GetLicenseInfo))]
    [DisplayName("获取授权信息")]
    public async Task<LicenseInfo> GetLicenseInfo()
    {
        var info = await LoadLicenseInfo();
        if (info != null)
        {
            // 验证授权是否有效
            if (!ValidateLicense(info))
            {
                info.IsActivated = false;
                await SaveLicenseInfo(info);
            }
        }
        else
        {
            info = new LicenseInfo
            {
                MachineCode = _machineCode,
                IsActivated = false,
                DeviceLimit = 1,
                TagLimit = 10,
            };
        }

        return info;
    }

    /// <summary>
    /// 激活授权
    /// </summary>
    [HttpPost("activate")]
    [OperationId(nameof(Activate))]
    [DisplayName("激活授权")]
    public async Task<LicenseInfo> Activate(ActivateInput input)
    {
        try
        {
            // 解密激活码
            var decrypted = DESEncryption.Decrypt(input.ActivationCode, "EdgeGateway");
            var parts = decrypted.Split('|');

            if (parts.Length != 7)
                throw Oops.Oh("无效的激活码");

            // 验证机器码
            if (parts[0] != _machineCode)
                throw Oops.Oh("激活码与当前设备不匹配");

            // 解析授权信息
            var info = new LicenseInfo
            {
                MachineCode = _machineCode,
                ActivationCode = input.ActivationCode,
                IsActivated = true,
                DeviceLimit = int.Parse(parts[1]),
                TagLimit = int.Parse(parts[2]),
                StartTime = DateTime.Parse(parts[3]),
                ExpireTime = DateTime.Parse(parts[4]),
                Edition = parts[5],
                Customer = parts[6]
            };

            // 验证授权是否过期
            if (info.ExpireTime < DateTime.Now)
                throw Oops.Oh("授权已过期");

            // 保存授权信息
            await SaveLicenseInfo(info);

            // 同时更新到DataStorage中
            DataStorage.Instance.SetLicenseInfo(info);

            // 更新系统配置
            _config.SetValue("System", ConfigConst.SysDeviceAuthorization, "true");

            // 添加授权记录
            await AddLicenseRecord(info);

            return info;
        }
        catch (Exception ex)
        {
            throw Oops.Oh($"激活失败: {ex.Message}");
        }
    }

    /// <summary>
    /// 加载授权信息
    /// </summary>
    private async Task<LicenseInfo> LoadLicenseInfo()
    {
        try
        {
            if (!File.Exists(LicenseFilePath))
                return null;

            var encrypted = await File.ReadAllTextAsync(LicenseFilePath);
            var json = DESEncryption.Decrypt(encrypted, "EdgeGateway");
            var info = JsonSerializer.Deserialize<LicenseInfo>(json);

            // 加载时同时更新到DataStorage中
            if (info != null)
            {
                DataStorage.Instance.SetLicenseInfo(info);
            }

            return info;
        }
        catch
        {
            return null;
        }
    }

    /// <summary>
    /// 保存授权信息
    /// </summary>
    private async Task SaveLicenseInfo(LicenseInfo info)
    {
        var json = JsonSerializer.Serialize(info);
        var encrypted = DESEncryption.Encrypt(json, "EdgeGateway");
        await File.WriteAllTextAsync(LicenseFilePath, encrypted);
    }

    /// <summary>
    /// 验证授权是否有效
    /// </summary>
    private bool ValidateLicense(LicenseInfo info)
    {
        if (!info.IsActivated)
            return false;

        // 验证机器码
        if (info.MachineCode != _machineCode)
            return false;

        // 验证是否过期
        if (info.ExpireTime < DateTime.Now)
            return false;

        try
        {
            // 验证激活码
            var decrypted = DESEncryption.Decrypt(info.ActivationCode, "EdgeGateway");
            var parts = decrypted.Split('|');

            if (parts.Length != 7 || parts[0] != _machineCode)
                return false;
        }
        catch
        {
            return false;
        }

        return true;
    }

    /// <summary>
    /// 获取授权记录列表
    /// </summary>
    [HttpGet("records")]
    [OperationId(nameof(GetLicenseRecords))]
    [DisplayName("获取授权记录列表")]
    public async Task<List<LicenseRecord>> GetLicenseRecords([FromQuery] LicenseRecordPageInput input)
    {
        var query = _db.Queryable<LicenseRecord>();

        // 条件过滤
        if (!string.IsNullOrEmpty(input.MachineCode))
            query = query.Where(x => x.MachineCode.Contains(input.MachineCode));

        if (input.IsActivated.HasValue)
            query = query.Where(x => x.IsActivated == input.IsActivated.Value);

        if (!string.IsNullOrEmpty(input.Edition))
            query = query.Where(x => x.Edition.Contains(input.Edition));

        if (!string.IsNullOrEmpty(input.Customer))
            query = query.Where(x => x.Customer.Contains(input.Customer));

        if (input.ActivateStartTime.HasValue)
            query = query.Where(x => x.ActivateTime >= input.ActivateStartTime.Value);

        if (input.ActivateEndTime.HasValue)
            query = query.Where(x => x.ActivateTime <= input.ActivateEndTime.Value);

        // 关键字搜索
        if (!string.IsNullOrEmpty(input.Keyword))
        {
            query = query.Where(x => x.MachineCode.Contains(input.Keyword) ||
                                     x.Edition.Contains(input.Keyword) ||
                                     x.Customer.Contains(input.Keyword) ||
                                     x.Operator.Contains(input.Keyword) ||
                                     x.Remark.Contains(input.Keyword));
        }

        // 排序
        if (!string.IsNullOrEmpty(input.Field))
        {
            var isAsc = input.Order != input.DescStr;
            query = query.OrderByIF(isAsc, input.Field);
            query = query.OrderByIF(!isAsc, input.Field + " desc");
        }
        else
        {
            query = query.OrderByDescending(x => x.ActivateTime);
        }

        // 分页查询
        var total = await query.CountAsync();
        var items = await query.Skip((input.Page - 1) * input.PageSize)
                              .Take(input.PageSize)
                              .ToListAsync();

        return items;
    }

    /// <summary>
    /// 添加授权记录
    /// </summary>
    /// <param name="info">授权信息</param>
    /// <param name="remark">备注</param>
    /// <returns></returns>
    private async Task AddLicenseRecord(LicenseInfo info, string remark = "")
    {
        var record = new LicenseRecord
        {
            Id = Yitter.IdGenerator.YitIdHelper.NextId(), // 使用雪花算法生成ID 
            MachineCode = info.MachineCode, // 机器码
            ActivationCode = info.ActivationCode, // 激活码
            IsActivated = info.IsActivated, // 授权状态
            DeviceLimit = info.DeviceLimit, // 授权设备数量
            TagLimit = info.TagLimit, // 授权采集标签数量
            StartTime = info.StartTime, // 授权开始时间
            ExpireTime = info.ExpireTime, // 授权结束时间
            Edition = info.Edition, // 授权版本
            Customer = info.Customer, // 客户信息
            ActivateTime = DateTime.Now, // 激活时间
            Operator = "System", // 操作人
            Remark = remark // 备注
        };

        await _db.Insertable(record).ExecuteCommandAsync();
    }

    /// <summary>
    /// 生成激活码（仅用于测试）
    /// </summary>
    [HttpPost("generate-code")]
    [OperationId(nameof(GenerateActivationCode))]
    [DisplayName("生成激活码（仅用于测试）")]
    public string GenerateActivationCode(GenerateActivationCodeInput input)
    {
        try
        {
            // 使用当前时间作为默认起始时间
            var startTime = input.StartTime ?? DateTime.Now;
            // 默认过期时间为一年后
            var expireTime = input.ExpireTime ?? startTime.AddYears(1);

            // 构建激活码内容
            var codeContent = new StringBuilder();
            codeContent.Append(input.MachineCode).Append("|");
            codeContent.Append(input.DeviceLimit).Append("|");
            codeContent.Append(input.TagLimit).Append("|");
            codeContent.Append(startTime.ToString("yyyy-MM-dd HH:mm:ss")).Append("|");
            codeContent.Append(expireTime.ToString("yyyy-MM-dd HH:mm:ss")).Append("|");
            codeContent.Append(input.Edition).Append("|");
            codeContent.Append(input.Customer);

            // 加密激活码
            var activationCode = DESEncryption.Encrypt(codeContent.ToString(), "EdgeGateway");

            return activationCode;
        }
        catch (Exception ex)
        {
            throw Oops.Oh($"生成激活码失败: {ex.Message}");
        }
    }
}