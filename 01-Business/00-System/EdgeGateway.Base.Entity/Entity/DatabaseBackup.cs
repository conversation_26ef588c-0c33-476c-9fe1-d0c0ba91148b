namespace EdgeGateway.Base.Entity.Entity;

/// <summary>
/// 数据库备份记录
/// </summary>
public class DatabaseBackup : EntityCreateBase
{
  /// <summary>
  /// 数据库名
  /// </summary>
  public string Database { get; set; }

  /// <summary>
  /// 备份文件路径
  /// </summary>
  public string FilePath { get; set; }

  /// <summary>
  /// 备份文件大小(MB)
  /// </summary> 
  public double FileSize { get; set; }

  /// <summary>
  /// 备份类型(Manual/Auto)
  /// </summary>
  public string BackupType { get; set; }

  /// <summary>
  /// 备份状态(Success/Failed)
  /// </summary>
  public string Status { get; set; }

  /// <summary>
  /// 错误信息
  /// </summary>
  public string Error { get; set; }

}

/// <summary>
/// 数据库备份配置
/// </summary>
public class DatabaseBackupConfig
{
  /// <summary>
  /// 是否启用自动备份
  /// </summary>
  public bool EnableAutoBackup { get; set; }

  /// <summary>
  /// 备份周期(天)
  /// </summary>
  public int BackupInterval { get; set; }

  /// <summary>
  /// 备份时间(HH:mm)
  /// </summary>
  public string BackupTime { get; set; }

  /// <summary>
  /// 保留备份数量
  /// </summary>
  public int KeepBackupCount { get; set; }

  /// <summary>
  /// 备份路径
  /// </summary>
  public string BackupPath { get; set; }
}

/// <summary>
/// 数据库备份查询输入
/// </summary>
public class DatabaseBackupInput : BasePageInput
{
  /// <summary>
  /// 数据库名
  /// </summary>
  public string Database { get; set; }

  /// <summary>
  /// 备份类型
  /// </summary>
  public string BackupType { get; set; }

  /// <summary>
  /// 备份状态
  /// </summary>
  public string Status { get; set; }

  /// <summary>
  /// 开始时间
  /// </summary>
  public DateTime? StartTime { get; set; }

  /// <summary>
  /// 结束时间
  /// </summary>
  public DateTime? EndTime { get; set; }
}