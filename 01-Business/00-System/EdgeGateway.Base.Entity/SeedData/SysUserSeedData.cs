namespace EdgeGateway.Base.Entity.SeedData;

/// <summary>
///     系统用户表种子数据
/// </summary>
[IgnoreUpdateSeed]
public class SysUserSeedData : ISqlSugarEntitySeedData<SysUser>
{
    /// <summary>
    ///     种子数据
    /// </summary>
    /// <returns></returns>
    public IEnumerable<SysUser> HasData()
    {
        var encryptPassword = MD5Encryption.Encrypt("fusion@123");

        return new[]
        {
            new SysUser { Id = *************, Account = "superAdmin", Password = "9ede17f8949f04a33bc27e1fa3af14bd", Name = "超级管理员", AccountType = AccountTypeEnum.SuperAdmin },
            new SysUser { Id = *************, Account = "admin", Password = encryptPassword, Name = "系统管理员", AccountType = AccountTypeEnum.SysAdmin }
        };
    }
}