namespace EdgeGateway.Base.Entity.Model;

/// <summary>
/// TCP消息类型
/// </summary>
public enum TcpMessageType
{
  /// <summary>
  /// 发送
  /// </summary>
  Send,

  /// <summary>
  /// 接收
  /// </summary>
  Receive
}

/// <summary>
/// TCP消息记录
/// </summary>
public class TcpMessageLog
{
  /// <summary>
  /// 消息类型
  /// </summary>
  public TcpMessageType Type { get; set; }

  /// <summary>
  /// 消息内容
  /// </summary>
  public string Content { get; set; }

  /// <summary>
  /// 消息格式
  /// </summary>
  public string Format { get; set; }

  /// <summary>
  /// 时间戳
  /// </summary>
  public DateTime Timestamp { get; set; }
}

/// <summary>
/// TCP连接配置
/// </summary>
public class TcpClientConfig
{
  /// <summary>
  /// 目标主机
  /// </summary>
  [Required]
  public string Host { get; set; }

  /// <summary>
  /// 目标端口
  /// </summary>
  [Required]
  [Range(1, 65535)]
  public int Port { get; set; }

  /// <summary>
  /// 连接超时时间(ms)
  /// </summary>
  public int ConnectTimeout { get; set; } = 5000;

  /// <summary>
  /// 接收超时时间(ms)
  /// </summary>
  public int ReceiveTimeout { get; set; } = 5000;

  /// <summary>
  /// 发送超时时间(ms)
  /// </summary>
  public int SendTimeout { get; set; } = 5000;
}