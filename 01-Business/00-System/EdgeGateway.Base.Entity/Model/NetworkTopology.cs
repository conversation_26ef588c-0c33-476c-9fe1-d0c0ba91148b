namespace EdgeGateway.Base.Entity.Model;

/// <summary>
/// 网络拓扑信息
/// </summary>
public class NetworkTopology
{
  /// <summary>
  /// 网络节点列表
  /// </summary>
  public List<NetworkNodeInfo> Nodes { get; set; } = new();

  /// <summary>
  /// 网络连接列表
  /// </summary>
  public List<NetworkLinkInfo> Links { get; set; } = new();
}

/// <summary>
/// 网络节点信息
/// </summary>
public class NetworkNodeInfo
{
  /// <summary>
  /// 节点ID
  /// </summary>
  public string Id { get; set; }

  /// <summary>
  /// 节点名称
  /// </summary>
  public string Name { get; set; }

  /// <summary>
  /// IP地址
  /// </summary>
  public string IpAddress { get; set; }

  /// <summary>
  /// MAC地址
  /// </summary>
  public string MacAddress { get; set; }

  /// <summary>
  /// 节点类型
  /// </summary>
  public NodeType Type { get; set; }

  /// <summary>
  /// 节点状态
  /// </summary>
  public NodeStatus Status { get; set; }

  /// <summary>
  /// 上一次状态
  /// </summary>
  public NodeStatus LastStatus { get; set; }

  /// <summary>
  /// 延迟(ms)
  /// </summary>
  public long Latency { get; set; }

  /// <summary>
  /// 最后检查时间
  /// </summary>
  public DateTime LastChecked { get; set; }

  /// <summary>
  /// 手动添加标记
  /// </summary>
  public bool IsManual { get; set; }

  /// <summary>
  /// 附加属性
  /// </summary>
  public Dictionary<string, string> Properties { get; set; } = new();
}

/// <summary>
/// 网络连接信息
/// </summary>
public class NetworkLinkInfo
{
  /// <summary>
  /// 连接ID
  /// </summary>
  public string Id { get; set; }

  /// <summary>
  /// 源节点ID
  /// </summary>
  public string SourceId { get; set; }

  /// <summary>
  /// 目标节点ID
  /// </summary>
  public string TargetId { get; set; }

  /// <summary>
  /// 连接类型
  /// </summary>
  public LinkType Type { get; set; }

  /// <summary>
  /// 连接状态
  /// </summary>
  public LinkStatus Status { get; set; }

  /// <summary>
  /// 带宽(Mbps)
  /// </summary>
  public int Bandwidth { get; set; }
}

/// <summary>
/// 节点类型
/// </summary>
public enum NodeType
{
  /// <summary>
  /// 网关
  /// </summary>
  Gateway,

  /// <summary>
  /// 路由器
  /// </summary>
  Router,

  /// <summary>
  /// 交换机
  /// </summary>
  Switch,
  /// <summary>
  /// 主机
  /// </summary>
  Host,
  /// <summary>
  /// 其他
  /// </summary>
  Other,
  /// <summary>
  ///     本地
  /// </summary>
  Local
}

/// <summary>
/// 节点状态
/// </summary>
public enum NodeStatus
{
  /// <summary>
  /// 在线
  /// </summary>
  Online,
  /// <summary>
  /// 离线
  /// </summary>
  Offline,
  /// <summary>
  /// 未知
  /// </summary>
  Unknown
}

/// <summary>
/// 连接类型
/// </summary>
public enum LinkType
{
  /// <summary>
  /// 以太网
  /// </summary>
  Ethernet,
  /// <summary>
  /// 无线
  /// </summary>
  Wifi,
  /// <summary>
  /// 其他
  /// </summary>
  Other
}

/// <summary>
/// 连接状态
/// </summary>
public enum LinkStatus
{
  /// <summary>
  /// 活动
  /// 表示连接正常且性能良好
  /// </summary>
  Active,

  /// <summary>
  /// 不稳定
  /// 表示连接存在但性能不佳
  /// </summary>
  Unstable,

  /// <summary>
  /// 非活动
  /// 表示连接断开
  /// </summary>
  Inactive,

  /// <summary>
  /// 未知
  /// 表示无法确定连接状态
  /// </summary>
  Unknown
}