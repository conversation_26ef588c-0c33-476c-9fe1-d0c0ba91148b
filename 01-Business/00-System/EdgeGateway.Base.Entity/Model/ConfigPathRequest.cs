namespace EdgeGateway.Base.Entity.Model;

/// <summary>
/// 配置路径请求类，用于POST请求获取配置
/// </summary>
public class ConfigPathRequest
{
  /// <summary>
  /// 配置文件名（如app.json或system.ini）
  /// </summary>
  public string FileName { get; set; }

  /// <summary>
  /// 是否包含Schema信息
  /// </summary>
  /// <remarks>
  /// 当设置为true时，返回结果将包含配置文件的Schema定义（如果存在）
  /// </remarks>
  public bool IncludeSchema { get; set; } = false;
}