namespace EdgeGateway.Base.Entity.Model;

/// <summary>
///     授权信息
/// </summary>
public class LicenseInfo
{
    /// <summary>
    ///     机器码
    /// </summary>
    public string MachineCode { get; set; }
    
    /// <summary>
    ///     激活码
    /// </summary>
    public string ActivationCode { get; set; }
    
    /// <summary>
    ///     授权状态
    /// </summary>
    public bool IsActivated { get; set; }
    
    /// <summary>
    ///     授权设备数量
    /// </summary>
    public int DeviceLimit { get; set; }
    
    /// <summary>
    ///     授权采集标签数量
    /// </summary>
    public int TagLimit { get; set; }
    
    /// <summary>
    ///     授权开始时间
    /// </summary>
    public DateTime StartTime { get; set; }
    
    /// <summary>
    ///     授权结束时间
    /// </summary>
    public DateTime ExpireTime { get; set; }
    
    /// <summary>
    ///     授权版本
    /// </summary>
    public string Edition { get; set; }
    
    /// <summary>
    ///     客户信息
    /// </summary>
    public string Customer { get; set; }
} 