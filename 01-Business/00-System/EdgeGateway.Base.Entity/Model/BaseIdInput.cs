namespace EdgeGateway.Base.Entity.Model;

/// <summary>
///     主键Id输入参数
/// </summary>
public class BaseIdInput
{
    /// <summary>
    ///     主键Id
    /// </summary>
    [Required(ErrorMessage = "Id不能为空")]
    [DataValidation(ValidationTypes.Numeric)]
    public virtual long Id { get; set; }
}

/// <summary>
/// 主键Id输入参数
/// </summary>
/// <typeparam name="T"></typeparam>
public class BaseIdInput<T>
{
    /// <summary>
    ///     主键Id
    /// </summary>
    [Required(ErrorMessage = "Id不能为空")]
    public  T Id { get; set; }
}