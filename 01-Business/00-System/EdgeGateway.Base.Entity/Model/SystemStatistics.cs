namespace EdgeGateway.Base.Entity.Model;

/// <summary>
///     系统统计数据
/// </summary>
public class SystemStatistics
{
    /// <summary>
    /// 表示一个带时间戳的数据点
    /// </summary>
    public class TimeSeriesDataPoint
    {
        /// <summary>
        /// 数据值
        /// </summary>
        public double Value { get; set; }

        /// <summary>
        /// 时间戳
        /// </summary>
        public DateTime Timestamp { get; set; }
    }

    /// <summary>
    /// 总离线消息数量
    /// </summary>
    public long TotalOfflineCount { get; set; }

    /// <summary>
    /// 总发送消息数量
    /// </summary>
    public long TotalSendCount { get; set; }

    /// <summary>
    /// 总接收消息数量
    /// </summary>
    public long TotalReceiveCount { get; set; }

    /// <summary>
    /// 总失败消息数量
    /// </summary>
    public long TotalFailCount { get; set; }

    /// <summary>
    ///     系统开机时间
    /// </summary>
    public string SystemUptime { get; set; }

    /// <summary>
    ///     程序运行时间
    /// </summary>
    public string ApplicationUptime { get; set; }

    /// <summary>
    ///     当前时间
    /// </summary>
    public string DateTime { get; set; }

    /// <summary>
    ///     CPU使用率
    /// </summary>
    public double CpuUsage { get; set; }

    /// <summary>
    ///     内存使用率
    /// </summary>
    public double MemoryUsage { get; set; }

    /// <summary>
    ///     网络上传速率（KB/s）
    /// </summary>
    public double UploadSpeed { get; set; }

    /// <summary>
    ///     网络下载速率（KB/s）
    /// </summary>
    public double DownloadSpeed { get; set; }

    /// <summary>
    ///     机器唯一标识码
    /// </summary>
    public string MachineCode { get; set; }

    /// <summary>
    ///     系统版本号
    /// </summary>
    public string SystemVersion { get; set; }

    /// <summary>
    /// 上传带宽利用率(%)
    /// </summary>
    public double UploadUtilization { get; set; }

    /// <summary>
    /// 下载带宽利用率(%)
    /// </summary>
    public double DownloadUtilization { get; set; }

    /// <summary>
    /// 设备总数
    /// </summary>
    public int DeviceCount { get; set; }

    /// <summary>
    /// 标签总数
    /// </summary>
    public int LabelCount { get; set; }
    /// <summary>
    ///     发送消息总数
    /// </summary>
    public long MessageSendCount { get; set; }

    /// <summary>
    ///     失败消息总数
    /// </summary>
    public long MessageFailCount { get; set; }

    /// <summary>
    ///     接收消息总数
    /// </summary>
    public long MessageReceiveCount { get; set; }

    /// <summary>
    ///     队列消息总数
    /// </summary>
    public int MessageQueuedCount { get; set; }

    /// <summary>
    /// 在线设备数量
    /// </summary>
    public int OnlineDeviceCount { get; set; }

    /// <summary>
    /// 离线设备数量
    /// </summary>
    public int OfflineDeviceCount { get; set; }

    /// <summary>
    /// 禁用设备数量
    /// </summary>
    public int DisabledDeviceCount { get; set; }

    /// <summary>
    ///     上传历史数据队列
    /// </summary>
    public List<TimeSeriesDataPoint> UploadSpeedHistory { get; set; }

    /// <summary>
    ///     下载历史数据队列
    /// </summary>
    public List<TimeSeriesDataPoint> DownloadSpeedHistory { get; set; }

    /// <summary>
    ///     Cpu历史数据队列
    /// </summary>
    public List<TimeSeriesDataPoint> CpuUsageHistory { get; set; }

    /// <summary>
    ///     内存历史数据队列
    /// </summary>
    public List<TimeSeriesDataPoint> MemoryUsageHistory { get; set; }

    /// <summary>
    ///     磁盘历史数据队列
    /// </summary>
    public List<TimeSeriesDataPoint> DiskUsageHistory { get; set; }

    /// <summary>
    ///     累计发送数据量(MB)
    /// </summary>
    public double TotalSentMB { get; set; }

    /// <summary>
    ///     累计接收数据量(GB)
    /// </summary>
    public double TotalReceivedGB { get; set; }
}