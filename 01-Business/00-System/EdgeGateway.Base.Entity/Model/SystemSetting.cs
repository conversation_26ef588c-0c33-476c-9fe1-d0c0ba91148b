namespace EdgeGateway.Base.Entity.Model;

/// <summary>
/// 系统设置
/// </summary>
public class SystemSetting
{
  /// <summary>
  /// 系统名称
  /// </summary>
  [Required]
  public string SystemName { get; set; }

  /// <summary>
  /// 系统描述
  /// </summary>
  public string Description { get; set; }

  /// <summary>
  /// 系统Logo
  /// </summary>
  public string LogoUrl { get; set; }

  /// <summary>
  /// 网站图标
  /// </summary>
  public string FaviconUrl { get; set; }

  /// <summary>
  /// 版权信息
  /// </summary>
  public string Copyright { get; set; }

  /// <summary>
  /// ICP备案号
  /// </summary>
  public string IcpNumber { get; set; }

  /// <summary>
  /// 是否自动更新
  /// </summary>
  public bool AutoUpdate { get; set; }
}