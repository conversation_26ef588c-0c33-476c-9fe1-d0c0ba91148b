namespace EdgeGateway.Base.Entity.Dto.Database;

/// <summary>
/// 数据库信息
/// </summary>
public class DatabaseDto
{
  /// <summary>
  /// 数据库名称
  /// </summary>
  public string Name { get; set; }

  /// <summary>
  /// 数据库大小(MB)
  /// </summary>
  public double Size { get; set; }

  /// <summary>
  /// 表数量
  /// </summary>
  public int TableCount { get; set; }
}

/// <summary>
/// 数据表信息
/// </summary>
public class TableDto
{
  /// <summary>
  /// 表名
  /// </summary>
  public string Name { get; set; }

  /// <summary>
  /// 表注释
  /// </summary>
  public string Comment { get; set; }

  /// <summary>
  /// 记录数
  /// </summary>
  public int RowCount { get; set; }

  /// <summary>
  /// 数据大小(MB)
  /// </summary>
  public double DataSize { get; set; }

  /// <summary>
  /// 索引大小(MB)
  /// </summary>
  public double IndexSize { get; set; }
}

/// <summary>
/// 表字段信息
/// </summary>
public class ColumnDto
{
  /// <summary>
  /// 字段名
  /// </summary>
  public string Name { get; set; }

  /// <summary>
  /// 数据类型
  /// </summary>
  public string DataType { get; set; }

  /// <summary>
  /// 长度
  /// </summary>
  public int? Length { get; set; }

  /// <summary>
  /// 是否可空
  /// </summary>
  public bool IsNullable { get; set; }

  /// <summary>
  /// 默认值
  /// </summary>
  public string DefaultValue { get; set; }

  /// <summary>
  /// 字段注释
  /// </summary>
  public string Comment { get; set; }

  /// <summary>
  /// 是否主键
  /// </summary>
  public bool IsPrimaryKey { get; set; }
}

/// <summary>
/// 表数据查询输入
/// </summary>
public class TableDataInput : BasePageInput
{
  /// <summary>
  /// 数据库名
  /// </summary>
  public string Database { get; set; }

  /// <summary>
  /// 表名
  /// </summary>
  public string Table { get; set; }

  /// <summary>
  /// 查询条件
  /// </summary>
  public string Where { get; set; }

  /// <summary>
  /// 排序
  /// </summary>
  public string OrderBy { get; set; }
}

/// <summary>
/// SQL执行输入
/// </summary>
public class ExecuteSqlInput
{
  /// <summary>
  /// 数据库名
  /// </summary>
  public string Database { get; set; }

  /// <summary>
  /// SQL语句
  /// </summary>
  public string Sql { get; set; }
}

/// <summary>
/// 数据库备份输入
/// </summary>
public class BackupDatabaseInput
{
  /// <summary>
  /// 数据库名
  /// </summary>
  public string Database { get; set; }

  /// <summary>
  /// 备份文件路径
  /// </summary>
  public string BackupPath { get; set; }
}