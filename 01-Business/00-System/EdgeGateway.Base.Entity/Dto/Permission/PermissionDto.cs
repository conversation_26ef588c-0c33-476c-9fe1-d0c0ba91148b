namespace EdgeGateway.Base.Entity.Dto.Permission;

/// <summary>
/// 角色分页查询输入
/// </summary>
public class RolePageInput
{
    /// <summary>
    /// 页码
    /// </summary>
    public int Page { get; set; } = 1;

    /// <summary>
    /// 页大小
    /// </summary>
    public int PageSize { get; set; } = 10;

    /// <summary>
    /// 角色名称
    /// </summary>
    public string? Name { get; set; }

    /// <summary>
    /// 角色编码
    /// </summary>
    public string? Code { get; set; }

    /// <summary>
    /// 状态
    /// </summary>
    public bool? Status { get; set; }

    /// <summary>
    /// 账户类型
    /// </summary>
    public AccountTypeEnum? AccountType { get; set; }
}

/// <summary>
/// 角色创建输入
/// </summary>
public class RoleCreateInput
{
    /// <summary>
    /// 角色名称
    /// </summary>
    [Required(ErrorMessage = "角色名称不能为空")]
    [MaxLength(50, ErrorMessage = "角色名称长度不能超过50个字符")]
    public string Name { get; set; }

    /// <summary>
    /// 角色编码
    /// </summary>
    [Required(ErrorMessage = "角色编码不能为空")]
    [MaxLength(50, ErrorMessage = "角色编码长度不能超过50个字符")]
    public string Code { get; set; }

    /// <summary>
    /// 角色描述
    /// </summary>
    [MaxLength(200, ErrorMessage = "角色描述长度不能超过200个字符")]
    public string? Description { get; set; }

    /// <summary>
    /// 状态
    /// </summary>
    public bool Status { get; set; } = true;

    /// <summary>
    /// 适用账户类型
    /// </summary>
    public AccountTypeEnum? AccountType { get; set; }

    /// <summary>
    /// 排序
    /// </summary>
    public int Sort { get; set; } = 0;
}

/// <summary>
/// 角色更新输入
/// </summary>
public class RoleUpdateInput
{
    /// <summary>
    /// 角色ID
    /// </summary>
    [Required(ErrorMessage = "角色ID不能为空")]
    public long Id { get; set; }

    /// <summary>
    /// 角色名称
    /// </summary>
    [Required(ErrorMessage = "角色名称不能为空")]
    [MaxLength(50, ErrorMessage = "角色名称长度不能超过50个字符")]
    public string Name { get; set; }

    /// <summary>
    /// 角色编码
    /// </summary>
    [Required(ErrorMessage = "角色编码不能为空")]
    [MaxLength(50, ErrorMessage = "角色编码长度不能超过50个字符")]
    public string Code { get; set; }

    /// <summary>
    /// 角色描述
    /// </summary>
    [MaxLength(200, ErrorMessage = "角色描述长度不能超过200个字符")]
    public string? Description { get; set; }

    /// <summary>
    /// 状态
    /// </summary>
    public bool Status { get; set; } = true;

    /// <summary>
    /// 适用账户类型
    /// </summary>
    public AccountTypeEnum? AccountType { get; set; }

    /// <summary>
    /// 排序
    /// </summary>
    public int Sort { get; set; } = 0;
}

/// <summary>
/// 菜单分页查询输入
/// </summary>
public class MenuPageInput
{
    /// <summary>
    /// 页码
    /// </summary>
    public int Page { get; set; } = 1;

    /// <summary>
    /// 页大小
    /// </summary>
    public int PageSize { get; set; } = 10;

    /// <summary>
    /// 菜单名称
    /// </summary>
    public string? Name { get; set; }

    /// <summary>
    /// 菜单编码
    /// </summary>
    public string? Code { get; set; }

    /// <summary>
    /// 父级菜单ID
    /// </summary>
    public long? ParentId { get; set; }

    /// <summary>
    /// 菜单类型
    /// </summary>
    public MenuTypeEnum? MenuType { get; set; }

    /// <summary>
    /// 状态
    /// </summary>
    public bool? Status { get; set; }
}

/// <summary>
/// 菜单创建输入
/// </summary>
public class MenuCreateInput
{
    /// <summary>
    /// 菜单名称
    /// </summary>
    [Required(ErrorMessage = "菜单名称不能为空")]
    [MaxLength(50, ErrorMessage = "菜单名称长度不能超过50个字符")]
    public string Name { get; set; }

    /// <summary>
    /// 菜单编码
    /// </summary>
    [Required(ErrorMessage = "菜单编码不能为空")]
    [MaxLength(50, ErrorMessage = "菜单编码长度不能超过50个字符")]
    public string Code { get; set; }

    /// <summary>
    /// 路由路径
    /// </summary>
    [MaxLength(200, ErrorMessage = "路由路径长度不能超过200个字符")]
    public string? Path { get; set; }

    /// <summary>
    /// 图标
    /// </summary>
    [MaxLength(50, ErrorMessage = "图标长度不能超过50个字符")]
    public string? Icon { get; set; }

    /// <summary>
    /// 父级菜单ID
    /// </summary>
    public long? ParentId { get; set; }

    /// <summary>
    /// 菜单类型
    /// </summary>
    public MenuTypeEnum MenuType { get; set; }

    /// <summary>
    /// 排序
    /// </summary>
    public int Sort { get; set; } = 0;

    /// <summary>
    /// 状态
    /// </summary>
    public bool Status { get; set; } = true;

    /// <summary>
    /// 是否隐藏
    /// </summary>
    public bool Hidden { get; set; } = false;
}

/// <summary>
/// 菜单更新输入
/// </summary>
public class MenuUpdateInput
{
    /// <summary>
    /// 菜单ID
    /// </summary>
    [Required(ErrorMessage = "菜单ID不能为空")]
    public long Id { get; set; }

    /// <summary>
    /// 菜单名称
    /// </summary>
    [Required(ErrorMessage = "菜单名称不能为空")]
    [MaxLength(50, ErrorMessage = "菜单名称长度不能超过50个字符")]
    public string Name { get; set; }

    /// <summary>
    /// 菜单编码
    /// </summary>
    [Required(ErrorMessage = "菜单编码不能为空")]
    [MaxLength(50, ErrorMessage = "菜单编码长度不能超过50个字符")]
    public string Code { get; set; }

    /// <summary>
    /// 路由路径
    /// </summary>
    [MaxLength(200, ErrorMessage = "路由路径长度不能超过200个字符")]
    public string? Path { get; set; }

    /// <summary>
    /// 图标
    /// </summary>
    [MaxLength(50, ErrorMessage = "图标长度不能超过50个字符")]
    public string? Icon { get; set; }

    /// <summary>
    /// 父级菜单ID
    /// </summary>
    public long? ParentId { get; set; }

    /// <summary>
    /// 菜单类型
    /// </summary>
    public MenuTypeEnum MenuType { get; set; }

    /// <summary>
    /// 排序
    /// </summary>
    public int Sort { get; set; } = 0;

    /// <summary>
    /// 状态
    /// </summary>
    public bool Status { get; set; } = true;

    /// <summary>
    /// 是否隐藏
    /// </summary>
    public bool Hidden { get; set; } = false;
}

/// <summary>
/// 角色菜单分配输入
/// </summary>
public class RoleMenuAssignInput
{
    /// <summary>
    /// 角色ID
    /// </summary>
    [Required(ErrorMessage = "角色ID不能为空")]
    public long RoleId { get; set; }

    /// <summary>
    /// 菜单ID列表
    /// </summary>
    [Required(ErrorMessage = "菜单ID列表不能为空")]
    public List<long> MenuIds { get; set; } = new();
}

/// <summary>
/// 用户分页查询输入
/// </summary>
public class UserPageInput
{
    /// <summary>
    /// 页码
    /// </summary>
    public int Page { get; set; } = 1;

    /// <summary>
    /// 页大小
    /// </summary>
    public int PageSize { get; set; } = 10;

    /// <summary>
    /// 账号
    /// </summary>
    public string? Account { get; set; }

    /// <summary>
    /// 姓名
    /// </summary>
    public string? Name { get; set; }

    /// <summary>
    /// 账号类型
    /// </summary>
    public AccountTypeEnum? AccountType { get; set; }

    /// <summary>
    /// 状态
    /// </summary>
    public bool? Status { get; set; }
}

/// <summary>
/// 用户创建输入
/// </summary>
public class UserCreateInput
{
    /// <summary>
    /// 账号
    /// </summary>
    [Required(ErrorMessage = "账号不能为空")]
    [MaxLength(50, ErrorMessage = "账号长度不能超过50个字符")]
    public string Account { get; set; }

    /// <summary>
    /// 姓名
    /// </summary>
    [Required(ErrorMessage = "姓名不能为空")]
    [MaxLength(50, ErrorMessage = "姓名长度不能超过50个字符")]
    public string Name { get; set; }

    /// <summary>
    /// 密码
    /// </summary>
    [Required(ErrorMessage = "密码不能为空")]
    [MinLength(6, ErrorMessage = "密码长度不能少于6个字符")]
    [MaxLength(100, ErrorMessage = "密码长度不能超过100个字符")]
    public string Password { get; set; }

    /// <summary>
    /// 账号类型
    /// </summary>
    [Required(ErrorMessage = "账号类型不能为空")]
    public AccountTypeEnum AccountType { get; set; }

    /// <summary>
    /// 状态
    /// </summary>
    public bool Status { get; set; } = true;
}

/// <summary>
/// 用户更新输入
/// </summary>
public class UserUpdateInput
{
    /// <summary>
    /// 用户ID
    /// </summary>
    [Required(ErrorMessage = "用户ID不能为空")]
    public long Id { get; set; }

    /// <summary>
    /// 账号
    /// </summary>
    [Required(ErrorMessage = "账号不能为空")]
    [MaxLength(50, ErrorMessage = "账号长度不能超过50个字符")]
    public string Account { get; set; }

    /// <summary>
    /// 姓名
    /// </summary>
    [Required(ErrorMessage = "姓名不能为空")]
    [MaxLength(50, ErrorMessage = "姓名长度不能超过50个字符")]
    public string Name { get; set; }

    /// <summary>
    /// 账号类型
    /// </summary>
    [Required(ErrorMessage = "账号类型不能为空")]
    public AccountTypeEnum AccountType { get; set; }

    /// <summary>
    /// 状态
    /// </summary>
    public bool Status { get; set; }
}

/// <summary>
/// 用户状态输入
/// </summary>
public class UserStatusInput
{
    /// <summary>
    /// 状态
    /// </summary>
    public bool Status { get; set; }
}

/// <summary>
/// 用户密码输入
/// </summary>
public class UserPasswordInput
{
    /// <summary>
    /// 新密码
    /// </summary>
    [Required(ErrorMessage = "密码不能为空")]
    [MinLength(6, ErrorMessage = "密码长度不能少于6个字符")]
    [MaxLength(100, ErrorMessage = "密码长度不能超过100个字符")]
    public string Password { get; set; }
}

/// <summary>
/// 用户角色分配输入
/// </summary>
public class UserRoleAssignInput
{
    /// <summary>
    /// 用户ID
    /// </summary>
    [Required(ErrorMessage = "用户ID不能为空")]
    public long UserId { get; set; }

    /// <summary>
    /// 角色ID列表
    /// </summary>
    [Required(ErrorMessage = "角色ID列表不能为空")]
    public List<long> RoleIds { get; set; } = new();
}

/// <summary>
/// 权限分页查询输入
/// </summary>
public class PermissionPageInput
{
    /// <summary>
    /// 页码
    /// </summary>
    public int Page { get; set; } = 1;

    /// <summary>
    /// 页大小
    /// </summary>
    public int PageSize { get; set; } = 10;

    /// <summary>
    /// 权限名称
    /// </summary>
    public string? Name { get; set; }

    /// <summary>
    /// 权限编码
    /// </summary>
    public string? Code { get; set; }

    /// <summary>
    /// 权限类型
    /// </summary>
    public string? Type { get; set; }

    /// <summary>
    /// 父级权限ID
    /// </summary>
    public long? ParentId { get; set; }

    /// <summary>
    /// 是否启用
    /// </summary>
    public bool? Enabled { get; set; }
}

/// <summary>
/// 权限创建输入
/// </summary>
public class PermissionCreateInput
{
    /// <summary>
    /// 权限名称
    /// </summary>
    [Required(ErrorMessage = "权限名称不能为空")]
    [MaxLength(50, ErrorMessage = "权限名称长度不能超过50个字符")]
    public string Name { get; set; }

    /// <summary>
    /// 权限编码
    /// </summary>
    [Required(ErrorMessage = "权限编码不能为空")]
    [MaxLength(50, ErrorMessage = "权限编码长度不能超过50个字符")]
    public string Code { get; set; }

    /// <summary>
    /// 权限类型
    /// </summary>
    [Required(ErrorMessage = "权限类型不能为空")]
    public string Type { get; set; }

    /// <summary>
    /// 权限描述
    /// </summary>
    [MaxLength(200, ErrorMessage = "权限描述长度不能超过200个字符")]
    public string? Description { get; set; }

    /// <summary>
    /// 父级权限ID
    /// </summary>
    public long? ParentId { get; set; }

    /// <summary>
    /// 是否启用
    /// </summary>
    public bool? Enabled { get; set; } = true;

    /// <summary>
    /// 是否可见
    /// </summary>
    public bool? Visible { get; set; } = true;

    /// <summary>
    /// 排序
    /// </summary>
    public int? Sort { get; set; } = 0;
}

/// <summary>
/// 权限更新输入
/// </summary>
public class PermissionUpdateInput
{
    /// <summary>
    /// 权限名称
    /// </summary>
    [Required(ErrorMessage = "权限名称不能为空")]
    [MaxLength(50, ErrorMessage = "权限名称长度不能超过50个字符")]
    public string Name { get; set; }

    /// <summary>
    /// 权限编码
    /// </summary>
    [Required(ErrorMessage = "权限编码不能为空")]
    [MaxLength(50, ErrorMessage = "权限编码长度不能超过50个字符")]
    public string Code { get; set; }

    /// <summary>
    /// 权限类型
    /// </summary>
    [Required(ErrorMessage = "权限类型不能为空")]
    public string Type { get; set; }

    /// <summary>
    /// 权限描述
    /// </summary>
    [MaxLength(200, ErrorMessage = "权限描述长度不能超过200个字符")]
    public string? Description { get; set; }

    /// <summary>
    /// 父级权限ID
    /// </summary>
    public long? ParentId { get; set; }

    /// <summary>
    /// 是否启用
    /// </summary>
    public bool? Enabled { get; set; } = true;

    /// <summary>
    /// 是否可见
    /// </summary>
    public bool? Visible { get; set; } = true;

    /// <summary>
    /// 排序
    /// </summary>
    public int? Sort { get; set; } = 0;
}

/// <summary>
/// 权限模板创建输入
/// </summary>
public class PermissionTemplateCreateInput
{
    /// <summary>
    /// 模板名称
    /// </summary>
    [Required(ErrorMessage = "模板名称不能为空")]
    [MaxLength(50, ErrorMessage = "模板名称长度不能超过50个字符")]
    public string Name { get; set; }

    /// <summary>
    /// 模板描述
    /// </summary>
    [Required(ErrorMessage = "模板描述不能为空")]
    [MaxLength(200, ErrorMessage = "模板描述长度不能超过200个字符")]
    public string Description { get; set; }

    /// <summary>
    /// 权限ID列表
    /// </summary>
    [Required(ErrorMessage = "权限列表不能为空")]
    public List<long> Permissions { get; set; } = new();

    /// <summary>
    /// 模板类型
    /// </summary>
    public string Type { get; set; } = "custom";
}

/// <summary>
/// 权限模板更新输入
/// </summary>
public class PermissionTemplateUpdateInput
{
    /// <summary>
    /// 模板名称
    /// </summary>
    [Required(ErrorMessage = "模板名称不能为空")]
    [MaxLength(50, ErrorMessage = "模板名称长度不能超过50个字符")]
    public string Name { get; set; }

    /// <summary>
    /// 模板描述
    /// </summary>
    [Required(ErrorMessage = "模板描述不能为空")]
    [MaxLength(200, ErrorMessage = "模板描述长度不能超过200个字符")]
    public string Description { get; set; }

    /// <summary>
    /// 权限ID列表
    /// </summary>
    [Required(ErrorMessage = "权限列表不能为空")]
    public List<long> Permissions { get; set; } = new();

    /// <summary>
    /// 模板类型
    /// </summary>
    public string Type { get; set; } = "custom";
}
