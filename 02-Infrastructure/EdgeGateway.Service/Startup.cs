using EdgeGateway.Service.Abstractions;
using EdgeGateway.Service.AutoStart;
using Microsoft.Extensions.DependencyInjection;
using ModBusTcpServer;
using System.Diagnostics;

namespace EdgeGateway.Service;

/// <summary>
///     自定义启动类
/// </summary>
public class Startup : AppStartup
{
    /// <summary>
    /// </summary>
    /// <param name="services"></param>
    public void ConfigureServices(IServiceCollection services)
    {
        var sw = Stopwatch.StartNew();

        // 注册自启动服务
        services.AddHostedService<AutoStartService>();
        // 注册modbus服务
        services.AddSingleton<ModbusTcpServerService>();
        services.AddSingleton<IBaseService>(sp => sp.GetRequiredService<ModbusTcpServerService>());

        sw.Stop();
        var logger = services.BuildServiceProvider().GetRequiredService<ILogger<Startup>>();
        logger.LogInformation("╔════════════════════════════════════════════════════════════════");
        logger.LogInformation("║ Service 服务注入完成，耗时: {ElapsedMilliseconds}ms", sw.ElapsedMilliseconds);
        logger.LogInformation("╚════════════════════════════════════════════════════════════════");
    }
}