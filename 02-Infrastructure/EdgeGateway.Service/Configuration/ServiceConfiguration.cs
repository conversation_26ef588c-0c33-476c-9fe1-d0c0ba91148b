using System.Text.Json;
using EdgeGateway.Service.Abstractions;
using EdgeGateway.Service.Abstractions.Models;

namespace EdgeGateway.Service.Configuration;

/// <summary>
///     服务配置管理实现
/// </summary>
public class ServiceConfiguration : ISingleton
{
  /// <summary>
  ///     服务管理器
  /// </summary>
  private readonly ServiceManager _serviceManager;

  /// <summary>
  ///     配置服务
  /// </summary>
  private readonly ConfigurationService _configService;

  /// <summary>
  ///     日志记录器
  /// </summary>
  private readonly ILogger<ServiceConfiguration> _logger;

  /// <summary>
  ///     构造函数
  /// </summary>
  /// <param name="serviceManager">服务管理器</param>
  /// <param name="configService">配置服务</param>
  /// <param name="logger">日志记录器</param>
  public ServiceConfiguration(
      ServiceManager serviceManager,
      ConfigurationService configService,
      ILogger<ServiceConfiguration> logger)
  {
    _serviceManager = serviceManager;
    _configService = configService;
    _logger = logger;
  }

  /// <summary>
  ///     获取所有服务配置描述
  /// </summary>
  public Dictionary<string, ServiceConfigDescription> GetAllServiceDescriptions()
  {
    try
    {
      // 获取所有服务
      var services = _serviceManager.GetAllServices();
      var result = new Dictionary<string, ServiceConfigDescription>();

      // 遍历所有服务，获取配置描述
      foreach (var service in services)
      {
        if (service.Value is IConfigurableService configurableService)
        {
          // 获取服务配置描述
          result[service.Key] = configurableService.GetConfigDescription();
        }
      }

      return result;
    }
    catch (Exception ex)
    {
      _logger.LogError(ex, "获取服务配置描述失败");
      throw;
    }
  }

  /// <summary>
  ///     获取服务配置
  /// </summary>
  public async Task<object> GetServiceConfig(string serviceId)
  {
    try
    {
      // 获取服务配置
      var config = await _configService.GetServiceConfigAsync(serviceId);
      if (config.Count == 0)
        // 如果配置为空，则抛出异常
        throw new KeyNotFoundException($"未找到服务 {serviceId} 的配置");
      return config;
    }
    catch (Exception ex)
    {
      _logger.LogError(ex, "获取服务 {ServiceId} 配置失败", serviceId);
      throw;
    }
  }

  /// <summary>
  ///     更新服务配置
  /// </summary>
  public async Task UpdateServiceConfig(string serviceId, object config)
  {
    try
    {
      // 获取所有服务
      var services = _serviceManager.GetAllServices();
      if (!services.TryGetValue(serviceId, out var service))
        // 如果服务不存在，则抛出异常
        throw new KeyNotFoundException($"服务 {serviceId} 不存在");

      // 将对象转换为配置字典
      var configDict = new Dictionary<string, string>();

      // 将JsonElement转换为字典
      if (config is JsonElement jsonElement)
      {
        foreach (var property in jsonElement.EnumerateObject())
        {
          // 排除ServiceId和IsEnabled
          if (property.Name != "IsEnabled" && property.Name != "ServiceId" && property.Name != "Version")
          {
            var value = property.Value.ToString();
            if (!string.IsNullOrEmpty(value))
            {
              configDict[property.Name] = value;
            }
          }
          else
          {
            // 忽略更新字段
          }
        }
      }

      // 更新指定服务的配置
      await _configService.UpdateServiceConfigAsync(serviceId, configDict);
      _logger.LogInformation("服务 {ServiceId} 配置已更新", serviceId);

      // 如果服务正在运行，重启服务以应用新配置
      if (service.IsRunning)
      {
        // 重新加载配置
        await service.InitializeAsync();
        _logger.LogInformation("重启服务 {ServiceId} 以应用新配置", serviceId);
        await StopService(serviceId);
        await StartService(serviceId);
      }
    }
    catch (Exception ex)
    {
      _logger.LogError(ex, "更新服务 {ServiceId} 配置失败", serviceId);
      throw;
    }
  }

  /// <summary>
  ///     检查服务是否启用
  /// </summary>
  public async Task<bool> IsServiceEnabled(string serviceId)
  {
    try
    {
      // 获取服务配置
      var config = await _configService.GetServiceConfigAsync(serviceId);
      // 检查配置中是否包含 IsEnabled 键，并且其值为 true
      return config.TryGetValue("IsEnabled", out var enabled) &&
             enabled.Equals("true", StringComparison.OrdinalIgnoreCase);
    }
    catch (Exception ex)
    {
      _logger.LogError(ex, "检查服务 {ServiceId} 状态失败", serviceId);
      return false;
    }
  }

  /// <summary>
  ///     设置服务启用状态
  /// </summary>
  public async Task SetServiceEnabled(string serviceId, bool enabled)
  {
    try
    {
      // 获取服务配置
      var config = await _configService.GetServiceConfigAsync(serviceId);
      if (config.Count == 0) throw new KeyNotFoundException($"服务 {serviceId} 不存在");

      // 设置服务启用状态
      config["IsEnabled"] = enabled.ToString().ToLower();
      // 更新服务配置
      await _configService.UpdateServiceConfigAsync(serviceId, config);
      _logger.LogInformation("服务 {ServiceId} 已{Status}", serviceId, enabled ? "启用" : "禁用");
    }
    catch (Exception ex)
    {
      _logger.LogError(ex, "{Action}服务 {ServiceId} 失败", enabled ? "启用" : "禁用", serviceId);
      throw;
    }
  }

  /// <summary>
  ///     启动服务
  /// </summary>
  public async Task StartService(string serviceId)
  {
    try
    {
      // 启动服务
      await _serviceManager.StartServiceAsync(serviceId);
      _logger.LogInformation("服务 {ServiceId} 已启动", serviceId);
    }
    catch (Exception ex)
    {
      _logger.LogError(ex, "启动服务 {ServiceId} 失败", serviceId);
      throw;
    }
  }

  /// <summary>
  ///     停止服务
  /// </summary>
  public async Task StopService(string serviceId)
  {
    try
    {
      // 停止服务
      await _serviceManager.StopServiceAsync(serviceId);
      _logger.LogInformation("服务 {ServiceId} 已停止", serviceId);
    }
    catch (Exception ex)
    {
      _logger.LogError(ex, "停止服务 {ServiceId} 失败", serviceId);
      throw;
    }
  }

}