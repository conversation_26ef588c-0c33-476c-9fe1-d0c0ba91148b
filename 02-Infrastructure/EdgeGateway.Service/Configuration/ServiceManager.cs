using EdgeGateway.Service.Abstractions;
using Microsoft.Extensions.DependencyInjection;

namespace EdgeGateway.Service.Configuration;

/// <summary>
///     服务管理器实现
/// </summary>
public class ServiceManager : ISingleton
{
    /// <summary>
    ///     服务字典
    /// </summary>
    private readonly Dictionary<string, IBaseService> _services;

    /// <summary>
    ///     日志记录器
    /// </summary>
    private readonly ILogger<ServiceManager> _logger;

    /// <summary>
    /// 
    /// </summary>
    private readonly IConfigurationService _configurationService;
    /// <summary>
    /// 
    /// </summary>
    private readonly IServiceProvider _serviceProvider;

    /// <summary>
    /// 
    /// </summary>
    /// <param name="logger"></param>
    /// <param name="configurationService"></param>
    /// <param name="serviceProvider"></param>
    public ServiceManager(
        ILogger<ServiceManager> logger,
        IConfigurationService configurationService,
        IServiceProvider serviceProvider)
    {
        _services = new Dictionary<string, IBaseService>();
        _logger = logger;
        _configurationService = configurationService;
        _serviceProvider = serviceProvider;
    }

    /// <summary>
    ///     启动所有服务
    /// </summary>
    /// <returns></returns>
    public async Task StartAllServicesAsync()
    {
        // 获取所有启用的服务配置
        var enabledServices = await _configurationService.GetEnabledServicesAsync();
        // 遍历所有服务并启动
        foreach (var service in _services.Values)
            try
            {
                // 如果服务在启用的服务列表中，则启动
                if (enabledServices.ContainsKey(service.GetServiceConfig().ServiceId))
                {
                    // 重启前加载当前配置
                    await service.InitializeAsync();
                    // 启动服务
                    await service.StartAsync();
                }
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "启动服务 {ServiceName} 时发生错误", service.GetType().Name);
            }
    }

    /// <summary>
    ///     停止所有服务
    /// </summary>
    /// <returns></returns>
    public async Task StopAllServicesAsync()
    {
        foreach (var service in _services.Values)
            try
            {
                await service.StopAsync();
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "停止服务 {ServiceName} 时发生错误", service.GetType().Name);
            }
    }

    /// <summary>
    ///     获取所有服务状态
    /// </summary>
    /// <returns></returns>
    public Dictionary<string, bool> GetServicesStatus()
    {
        return _services.ToDictionary(
            s => s.Key,
            s => s.Value.IsRunning);
    }

    /// <summary>
    ///     启动指定服务
    /// </summary>
    /// <param name="serviceId">服务ID</param>
    /// <returns></returns>
    public async Task StartServiceAsync(string serviceId)
    {
        if (_services.TryGetValue(serviceId, out var service))
            await service.StartAsync();
        else
            throw new KeyNotFoundException($"未找到服务: {serviceId}");
    }

    /// <summary>
    ///     停止指定服务
    /// </summary>
    /// <param name="serviceId">服务ID</param>
    /// <returns></returns>
    public async Task StopServiceAsync(string serviceId)
    {
        if (_services.TryGetValue(serviceId, out var service))
            await service.StopAsync();
        else
            throw new KeyNotFoundException($"未找到服务: {serviceId}");
    }

    /// <summary>
    ///     获取所有服务
    /// </summary>
    /// <returns></returns>
    public Dictionary<string, IBaseService> GetAllServices()
    {
        return new Dictionary<string, IBaseService>(_services);
    }

    /// <summary>
    ///     注册服务
    /// </summary>
    /// <param name="serviceId">服务ID</param>
    /// <param name="service">服务</param>
    private void RegisterService(string serviceId, IBaseService service)
    {
        _services[serviceId] = service;
    }

    /// <summary>
    /// 初始化服务管理器
    /// </summary>
    public async Task InitializeAsync()
    {
        try
        {
            // 获取所有启用的服务配置
            var allServices = await _configurationService.GetAllServicesAsync();

            foreach (var serviceConfig in allServices)
            {
                var serviceId = serviceConfig.Key;
                try
                {
                    // 从DI容器中获取对应的服务实例
                    var service = _serviceProvider.GetService<IBaseService>();
                    if (service != null)
                    {
                        // 注册服务
                        RegisterService(serviceId, service);
                        _logger.LogInformation("服务 {ServiceId} 已注册", serviceId);
                    }
                    else
                    {
                        _logger.LogWarning("未找到服务 {ServiceId} 的实现", serviceId);
                    }
                }
                catch (Exception ex)
                {
                    _logger.LogError(ex, "初始化服务 {ServiceId} 时发生错误", serviceId);
                }
            }
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "初始化服务管理器时发生错误");
            throw;
        }
    }
}