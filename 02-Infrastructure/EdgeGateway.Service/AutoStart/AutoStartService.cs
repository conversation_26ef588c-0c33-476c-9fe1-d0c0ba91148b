using EdgeGateway.Service.Configuration;
using Microsoft.Extensions.Hosting;

namespace EdgeGateway.Service.AutoStart;

/// <summary>
///     自动启动服务实现
///     负责在系统启动时自动启动已配置的服务
/// </summary>
public class AutoStartService : IHostedService
{
    /// <summary>
    ///     日志记录器
    /// </summary>
    private readonly ILogger<AutoStartService> _logger;

    /// <summary>
    ///     服务配置
    /// </summary>
    private readonly ServiceManager _serviceManager;

    /// <summary>
    ///     构造函数
    /// </summary>
    /// <param name="logger">日志记录器</param>
    /// <param name="serviceManager"></param>
    public AutoStartService(
        ILogger<AutoStartService> logger, ServiceManager serviceManager)
    {
        _logger = logger;
        _serviceManager = serviceManager;
    }

    /// <summary>
    ///     启动服务
    /// </summary>
    /// <param name="cancellationToken">取消令牌</param>
    /// <returns>任务</returns>
    public async Task StartAsync(CancellationToken cancellationToken)
    {
        try
        {
            await _serviceManager.InitializeAsync();
            await _serviceManager.StartAllServicesAsync();
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "自动启动服务时发生错误");
        }
    }

    /// <summary>
    ///     停止服务
    /// </summary>
    /// <param name="cancellationToken">取消令牌</param>
    /// <returns>任务</returns>
    public async Task StopAsync(CancellationToken cancellationToken)
    {
        await _serviceManager.StopAllServicesAsync();
    }
}