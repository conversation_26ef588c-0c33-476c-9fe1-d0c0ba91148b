namespace EdgeGateway.Service.Abstractions
{
  /// <summary>
  /// 服务状态信息
  /// </summary>
  public class ServiceStatus
  {
    /// <summary>
    /// 是否运行
    /// </summary>
    public bool IsRunning { get; set; }
    /// <summary>
    /// 启动时间
    /// </summary>
    public DateTime StartTime { get; set; }
    /// <summary>
    /// 运行时间
    /// </summary>
    public TimeSpan Uptime { get; set; }
    /// <summary>
    /// 指标
    /// </summary>
    public Dictionary<string, object> Metrics { get; set; } = new();
  }

  /// <summary>
  /// 服务监控接口
  /// </summary>
  public interface IServiceMonitor
  {
    /// <summary>
    /// 获取服务状态
    /// </summary>
    Task<ServiceStatus> GetStatusAsync();

    /// <summary>
    /// 获取服务健康状态
    /// </summary>
    Task<bool> IsHealthyAsync();
  }
}