namespace EdgeGateway.Service.Abstractions
{
  /// <summary>
  /// 服务生命周期接口
  /// </summary>
  public interface IServiceLifecycle
  {
    /// <summary>
    /// 服务启动前
    /// </summary>
    Task OnStartingAsync();

    /// <summary>
    /// 服务启动后
    /// </summary>
    Task OnStartedAsync();

    /// <summary>
    /// 服务停止前
    /// </summary>
    Task OnStoppingAsync();

    /// <summary>
    /// 服务停止后
    /// </summary>
    Task OnStoppedAsync();
  }
}