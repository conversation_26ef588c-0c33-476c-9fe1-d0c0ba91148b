import { Label } from '@/components/ui/label'
import { Input } from '@/components/ui/input'
import {
  Select,
  SelectContent,
  SelectItem,
  SelectTrigger,
  SelectValue,
} from '@/components/ui/select'
import { NodeConfigProps } from '../types'

export default function ScheduleTriggerConfig({
  selectedNode,
  onPropertyChange,
}: NodeConfigProps) {
  return (
    <>
      <div>
        <Label htmlFor="schedule-type">计划类型</Label>
        <Select
          value={selectedNode.data?.scheduleType || 'interval'}
          onValueChange={(value) => onPropertyChange('scheduleType', value)}>
          <SelectTrigger id="schedule-type">
            <SelectValue placeholder="选择计划类型" />
          </SelectTrigger>
          <SelectContent>
            <SelectItem value="interval">间隔</SelectItem>
            <SelectItem value="daily">每日</SelectItem>
            <SelectItem value="weekly">每周</SelectItem>
            <SelectItem value="monthly">每月</SelectItem>
            <SelectItem value="cron">Cron 表达式</SelectItem>
          </SelectContent>
        </Select>
      </div>

      {selectedNode.data?.scheduleType === 'interval' && (
        <div>
          <Label htmlFor="interval">间隔（分钟）</Label>
          <Input
            id="interval"
            type="number"
            min="1"
            value={selectedNode.data?.interval || '60'}
            onChange={(e) => onPropertyChange('interval', e.target.value)}
          />
        </div>
      )}

      {selectedNode.data?.scheduleType === 'cron' && (
        <div>
          <Label htmlFor="cron">Cron 表达式</Label>
          <Input
            id="cron"
            value={selectedNode.data?.cronExpression || '0 0 * * *'}
            onChange={(e) => onPropertyChange('cronExpression', e.target.value)}
            placeholder="0 0 * * *"
          />
          <p className="text-xs text-gray-500 mt-1">
            例如：0 0 * * * （每天午夜执行）
          </p>
        </div>
      )}
    </>
  )
}
