import { Label } from '@/components/ui/label'
import { Input } from '@/components/ui/input'
import {
  Select,
  SelectContent,
  SelectItem,
  SelectTrigger,
  SelectValue,
} from '@/components/ui/select'
import { NodeConfigProps } from '../types'

export default function WriteTagActionConfig({
  selectedNode,
  onPropertyChange,
}: NodeConfigProps) {
  return (
    <>
      <div>
        <Label htmlFor="device-name">设备选择</Label>
        <Select
          value={selectedNode.data?.deviceName || ''}
          onValueChange={(value) => onPropertyChange('deviceName', value)}>
          <SelectTrigger id="device-name">
            <SelectValue placeholder="选择设备" />
          </SelectTrigger>
          <SelectContent>
            <SelectItem value="dev-001">温度传感器 A1</SelectItem>
            <SelectItem value="dev-002">压力传感器 B2</SelectItem>
            <SelectItem value="dev-003">流量计 C3</SelectItem>
          </SelectContent>
        </Select>
      </div>

      <div>
        <Label htmlFor="tag-name">点位选择</Label>
        <Select
          value={selectedNode.data?.tagName || ''}
          onValueChange={(value) => onPropertyChange('tagName', value)}>
          <SelectTrigger id="tag-name">
            <SelectValue placeholder="选择点位" />
          </SelectTrigger>
          <SelectContent>
            <SelectItem value="tag-001">温度传感器</SelectItem>
            <SelectItem value="tag-002">压力传感器</SelectItem>
            <SelectItem value="tag-003">运行状态</SelectItem>
          </SelectContent>
        </Select>
      </div>

      <div>
        <Label htmlFor="tag-value">写入值</Label>
        <Input
          id="tag-value"
          placeholder="输入要写入的值"
          value={selectedNode.data?.tagValue || ''}
          onChange={(e) => onPropertyChange('tagValue', e.target.value)}
        />
        <p className="text-xs text-gray-500 mt-1">
          可以使用变量，例如: {'{'}previousNode.temperature{'}'}
        </p>
      </div>
    </>
  )
}
