import { Button } from '@/components/ui/button'
import { Input } from '@/components/ui/input'
import { Label } from '@/components/ui/label'
import { Badge } from '@/components/ui/badge'
import {
  Select,
  SelectContent,
  SelectItem,
  SelectTrigger,
  SelectValue,
} from '@/components/ui/select'
import { X } from 'lucide-react'

export interface Variable {
  name: string
  type: string
  value: string
}

interface VariablesPanelProps {
  selectedNode: any
  variables: Variable[]
  onAddVariable: (variable: Omit<Variable, 'id'>) => void
  onRemoveVariable: (index: number) => void
  onNewVariableChange: (field: string, value: string) => void
  newVariableName: string
  newVariableType: string
  newVariableValue: string
}

export default function VariablesPanel({
  selectedNode,
  variables,
  onAddVariable,
  onRemoveVariable,
  onNewVariableChange,
  newVariableName,
  newVariableType,
  newVariableValue,
}: VariablesPanelProps) {
  if (!selectedNode) {
    return (
      <div className="rounded-md border p-4 text-center">
        <h3 className="mb-2 text-sm font-medium">未选择节点</h3>
        <p className="text-xs text-gray-500">
          在画布上选择一个节点以管理其变量
        </p>
      </div>
    )
  }

  return (
    <div className="space-y-4">
      <div>
        <h3 className="text-sm font-medium mb-2">节点变量</h3>
        <p className="text-xs text-gray-500 mb-4">
          在此节点中定义的变量可以在工作流的后续步骤中使用
        </p>

        {variables.length > 0 ? (
          <div className="space-y-2">
            {variables.map((variable, index) => (
              <div
                key={index}
                className="flex items-center justify-between p-2 border rounded-md">
                <div>
                  <span className="font-medium text-sm">{variable.name}</span>
                  <Badge className="ml-2" variant="outline">
                    {variable.type}
                  </Badge>
                  <p className="text-xs text-gray-500 mt-1">
                    {variable.value || '(无默认值)'}
                  </p>
                </div>
                <Button
                  variant="ghost"
                  size="sm"
                  onClick={() => onRemoveVariable(index)}>
                  <X className="h-4 w-4" />
                </Button>
              </div>
            ))}
          </div>
        ) : (
          <div className="text-center p-4 border rounded-md">
            <p className="text-sm text-gray-500">此节点尚未定义变量</p>
          </div>
        )}
      </div>

      <div className="border-t pt-4">
        <h3 className="text-sm font-medium mb-2">添加新变量</h3>
        <div className="space-y-3">
          <div>
            <Label htmlFor="variable-name">变量名称</Label>
            <Input
              id="variable-name"
              placeholder="输入变量名称"
              value={newVariableName}
              onChange={(e) =>
                onNewVariableChange('newVariableName', e.target.value)
              }
            />
          </div>
          <div>
            <Label htmlFor="variable-type">变量类型</Label>
            <Select
              value={newVariableType}
              onValueChange={(value) =>
                onNewVariableChange('newVariableType', value)
              }>
              <SelectTrigger id="variable-type">
                <SelectValue placeholder="选择变量类型" />
              </SelectTrigger>
              <SelectContent>
                <SelectItem value="string">字符串</SelectItem>
                <SelectItem value="number">数字</SelectItem>
                <SelectItem value="boolean">布尔值</SelectItem>
                <SelectItem value="object">对象</SelectItem>
                <SelectItem value="array">数组</SelectItem>
              </SelectContent>
            </Select>
          </div>
          <div>
            <Label htmlFor="variable-value">默认值 (可选)</Label>
            <Input
              id="variable-value"
              placeholder="输入默认值"
              value={newVariableValue}
              onChange={(e) =>
                onNewVariableChange('newVariableValue', e.target.value)
              }
            />
          </div>
          <Button
            onClick={() =>
              onAddVariable({
                name: newVariableName,
                type: newVariableType,
                value: newVariableValue,
              })
            }
            className="w-full">
            添加变量
          </Button>
        </div>
      </div>
    </div>
  )
}
