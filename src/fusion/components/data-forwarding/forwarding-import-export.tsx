import { useState } from 'react'
import { Button } from '@/components/ui/button'
import { ExcelImport } from '@/components/common/excel-import'
import { toast } from '@/components/ui/use-toast'
import {
  exportForwardingToExcel,
  createForwardingImportTemplate,
  validateForwardingImportData,
  convertImportDataToForwardings,
} from '@/lib/utils/forwarding-export-import'
import type { ForwardingConfig } from '@/components/data-forwarding/types'

interface ForwardingImportExportProps {
  forwardings: ForwardingConfig[]
  onImport: (newForwardings: ForwardingConfig[]) => void
  className?: string
}

export function ForwardingImportExport({
  forwardings,
  onImport,
  className = '',
}: ForwardingImportExportProps) {
  const [isImportDialogOpen, setIsImportDialogOpen] = useState(false)

  // 导出数据
  const handleExportData = () => {
    try {
      exportForwardingToExcel(forwardings)
      toast({
        title: '导出成功',
        description: `成功导出 ${forwardings.length} 条转发配置`,
      })
    } catch (error) {
      console.error('导出数据失败:', error)
      toast({
        title: '导出失败',
        description: '导出数据时发生错误',
        variant: 'destructive',
      })
    }
  }

  // 下载导入模板
  const handleDownloadTemplate = async () => {
    try {
      createForwardingImportTemplate()
      return Promise.resolve()
    } catch (error) {
      console.error('下载模板失败:', error)
      toast({
        title: '下载模板失败',
        description: '无法下载导入模板',
        variant: 'destructive',
      })
      return Promise.reject(error)
    }
  }

  // 验证导入数据
  const validateImportData = (data: any[]) => {
    return validateForwardingImportData(data)
  }

  // 处理导入数据
  const handleImportData = async (data: any[], config: any) => {
    try {
      // 转换导入数据为转发配置
      const importedForwardings = convertImportDataToForwardings(
        data,
        forwardings
      )

      // 调用回调函数更新父组件状态
      onImport(importedForwardings)

      toast({
        title: '导入成功',
        description: `成功导入 ${importedForwardings.length} 条转发配置`,
      })
    } catch (error) {
      console.error('导入数据失败:', error)
      toast({
        title: '导入失败',
        description: '导入数据时发生错误',
        variant: 'destructive',
      })
      throw error
    }
  }

  return (
    <div className={`flex items-center gap-2 ${className}`}>
      <Button variant="outline" onClick={handleExportData}>
        <svg
          xmlns="http://www.w3.org/2000/svg"
          viewBox="0 0 24 24"
          fill="none"
          stroke="currentColor"
          strokeWidth="2"
          strokeLinecap="round"
          strokeLinejoin="round"
          className="h-4 w-4 mr-2">
          <path d="M21 15v4a2 2 0 0 1-2 2H5a2 2 0 0 1-2-2v-4" />
          <polyline points="7 10 12 15 17 10" />
          <line x1="12" y1="15" x2="12" y2="3" />
        </svg>
        导出
      </Button>
      <Button variant="outline" onClick={() => setIsImportDialogOpen(true)}>
        <svg
          xmlns="http://www.w3.org/2000/svg"
          viewBox="0 0 24 24"
          fill="none"
          stroke="currentColor"
          strokeWidth="2"
          strokeLinecap="round"
          strokeLinejoin="round"
          className="h-4 w-4 mr-2">
          <path d="M21 15v4a2 2 0 0 1-2 2H5a2 2 0 0 1-2-2v-4" />
          <polyline points="17 8 12 3 7 8" />
          <line x1="12" y1="3" x2="12" y2="15" />
        </svg>
        导入
      </Button>

      <ExcelImport
        open={isImportDialogOpen}
        onOpenChange={setIsImportDialogOpen}
        title="导入转发配置"
        description="导入Excel文件中的转发配置"
        systemFields={[
          { id: 'name', label: '名称', required: true },
          { id: 'type', label: '协议类型', required: true },
          { id: 'url', label: '服务器地址', required: true },
          { id: 'forwardMode', label: '转发模式', required: true },
          { id: 'enable', label: '是否启用', required: false },
          { id: 'username', label: '用户名', required: false },
          { id: 'password', label: '密码', required: false },
          { id: 'description', label: '描述', required: false },
        ]}
        onImport={handleImportData}
        templateUrl="#"
        onTemplateDownload={handleDownloadTemplate}
        validateData={validateImportData}
      />
    </div>
  )
}
