/**
 * 类型适配器 - 处理API类型和组件类型之间的转换
 */

import type {
  ForwardingConfig,
  ForwardingProtocol,
  HttpCustomConfig,
  WebSocketCustomConfig,
  MqttCustomConfig,
  ForwardMode,
} from './types'
import type {
  ForwardConfig as ApiForwardConfig,
  ForwardConfigInput,
  ForwardConfigUpdateInput,
  ForwardTypeEnum,
} from '@/lib/api-services/models'
import type {
  HttpConfig,
  HttpEndpoint,
} from './protocol-configs/http-config-form'
import type { WebSocketConfig } from './protocol-configs/websocket-config-form'

/**
 * 协议类型转换：字符串 -> 数字枚举
 */
export function protocolToApiType(
  protocol: ForwardingProtocol
): ForwardTypeEnum {
  switch (protocol) {
    case 'MQTT':
      return 1 as ForwardTypeEnum
    case 'HTTP':
      return 2 as ForwardTypeEnum
    case 'WebSocket':
      return 3 as ForwardTypeEnum
    default:
      return 1 as ForwardTypeEnum
  }
}

/**
 * 协议类型转换：数字枚举 -> 字符串
 */
export function apiTypeToProtocol(
  type: ForwardTypeEnum | number
): ForwardingProtocol {
  const typeValue = Number(type)
  switch (typeValue) {
    case 1:
      return 'MQTT'
    case 2:
      return 'HTTP'
    case 3:
      return 'WebSocket'
    default:
      return 'MQTT'
  }
}

/**
 * HttpCustomConfig -> HttpConfig 适配器
 */
export function httpCustomConfigToHttpConfig(
  customConfig: HttpCustomConfig,
  baseUrl: string
): HttpConfig {
  return {
    baseUrl,
    authType: customConfig.authType as any,
    username: customConfig.username,
    password: customConfig.password,
    apiKey: customConfig.apiKey,
    apiKeyHeader: customConfig.apiKeyHeader,
    bearerToken: customConfig.bearerToken,
    timeout: customConfig.timeout,
    endpoints: (customConfig.endpoints || []).map(
      (endpoint) =>
        ({
          id:
            endpoint.id || `ep_${Math.random().toString(36).substring(2, 11)}`,
          name: endpoint.name || '',
          path: endpoint.path || '',
          method: endpoint.method as any,
          endpointType: endpoint.endpointType as any,
          template: endpoint.template,
          transformScript: endpoint.transformScript,
          headers: endpoint.headers || {},
          enable: Boolean(endpoint.enable),
          description: endpoint.description,
        } as HttpEndpoint)
    ),
  }
}

/**
 * HttpConfig -> HttpCustomConfig 适配器
 */
export function httpConfigToHttpCustomConfig(
  config: HttpConfig
): HttpCustomConfig {
  return {
    endpoints: config.endpoints || [],
    timeout: config.timeout,
    authType: config.authType,
    username: config.username,
    password: config.password,
    apiKey: config.apiKey,
    apiKeyHeader: config.apiKeyHeader,
    bearerToken: config.bearerToken,
  }
}

/**
 * WebSocketCustomConfig -> WebSocketConfig 适配器
 */
export function wsCustomConfigToWsConfig(
  customConfig: WebSocketCustomConfig,
  serverUrl: string
): WebSocketConfig {
  return {
    serverUrl,
    autoReconnect: customConfig.autoReconnect,
    reconnectInterval: customConfig.reconnectInterval,
    maxReconnectAttempts: customConfig.maxReconnectAttempts,
    channels: customConfig.channels || [],
    protocols: customConfig.protocols,
    headers: customConfig.headers,
  }
}

/**
 * WebSocketConfig -> WebSocketCustomConfig 适配器
 */
export function wsConfigToWsCustomConfig(
  config: WebSocketConfig
): WebSocketCustomConfig {
  return {
    channels: config.channels || [],
    protocols: config.protocols,
    headers: config.headers,
    autoReconnect: config.autoReconnect,
    reconnectInterval: config.reconnectInterval,
    maxReconnectAttempts: config.maxReconnectAttempts,
  }
}

/**
 * ForwardingConfig -> ForwardConfigInput 适配器
 */
export function forwardingConfigToApiInput(
  config: ForwardingConfig
): ForwardConfigInput {
  return {
    name: config.name,
    enable: config.enable,
    type: protocolToApiType(config.type),
    url: config.url,
    username: config.username || null,
    password: config.password || null,
    timeout: config.timeout,
    forwardMode: config.forwardMode,
    connectionTimeout: config.connectionTimeout,
    reconnectInterval: config.reconnectInterval,
    customConfig: config.customConfig,
    offlineStorageConfig: config.offlineStorageConfig,
    retryConfig: config.retryConfig,
  }
}

/**
 * ForwardingConfig -> ForwardConfigUpdateInput 适配器
 */
export function forwardingConfigToApiUpdateInput(
  config: ForwardingConfig
): ForwardConfigUpdateInput {
  return {
    id: Number(config.id),
    name: config.name,
    enable: config.enable,
    type: protocolToApiType(config.type),
    url: config.url,
    username: config.username || null,
    password: config.password || null,
    timeout: config.timeout,
    forwardMode: config.forwardMode,
    connectionTimeout: config.connectionTimeout,
    reconnectInterval: config.reconnectInterval,
    customConfig: config.customConfig,
    offlineStorageConfig: config.offlineStorageConfig,
    retryConfig: config.retryConfig,
  }
}

/**
 * ApiForwardConfig -> ForwardingConfig 适配器
 */
export function apiConfigToForwardingConfig(
  apiConfig: ApiForwardConfig
): ForwardingConfig {
  const protocolType = apiTypeToProtocol(apiConfig.type || 1)

  // 确保forwardMode符合类型要求
  let forwardMode: ForwardMode = 'realtime'
  if (
    apiConfig.forwardMode === 'batch' ||
    apiConfig.forwardMode === 'scheduled' ||
    apiConfig.forwardMode === 'event'
  ) {
    forwardMode = apiConfig.forwardMode as ForwardMode
  }

  return {
    id: apiConfig.id as number,
    name: apiConfig.name || '',
    enable: Boolean(apiConfig.enable),
    type: protocolType,
    forwardMode,
    url: apiConfig.url || '',
    username: apiConfig.username as string | undefined,
    password: apiConfig.password as string | undefined,
    timeout: apiConfig.timeout,
    connectionTimeout: apiConfig.connectionTimeout,
    reconnectInterval: apiConfig.reconnectInterval,
    customConfig: apiConfig.customConfig || {},
    offlineStorageConfig: {
      offlineStorage: Boolean(
        apiConfig.offlineStorageConfig?.offlineStorage ?? true
      ),
      batchSize: apiConfig.offlineStorageConfig?.batchSize ?? 100,
      storageLimit: apiConfig.offlineStorageConfig?.storageLimit ?? 10000,
      storageMode:
        (apiConfig.offlineStorageConfig?.storageMode as any) || 'file',
      expireDays: apiConfig.offlineStorageConfig?.expireDays ?? 7,
      cleanExpireHours: apiConfig.offlineStorageConfig?.cleanExpireHours ?? 1,
    },
    retryConfig: {
      enable: Boolean(apiConfig.retryConfig?.enable ?? true),
      maxRetries: apiConfig.retryConfig?.maxRetries ?? 3,
      retryInterval: apiConfig.retryConfig?.retryInterval ?? 5000,
      initialDelay: apiConfig.retryConfig?.initialDelay ?? 1000,
      maxDelay: apiConfig.retryConfig?.maxDelay ?? 60000,
      backoffMultiplier: apiConfig.retryConfig?.backoffMultiplier ?? 2,
      enableDeadLetter: Boolean(
        apiConfig.retryConfig?.enableDeadLetter ?? false
      ),
      enableAutoRetry: Boolean(apiConfig.retryConfig?.enableAutoRetry ?? true),
    },
    isConnected: Boolean(apiConfig.isConnected),
    lastActivityTime: apiConfig.lastActivityTime?.toString() || undefined,
    createTime: apiConfig.createTime?.toString() || undefined,
    updateTime: apiConfig.updateTime?.toString() || undefined,
    createUserName: apiConfig.createUserName as string | undefined,
    updateUserName: apiConfig.updateUserName as string | undefined,
  }
}

/**
 * 安全的日期转换函数
 */
export function safeDateToString(
  date: Date | string | undefined
): string | undefined {
  if (!date) return undefined
  if (typeof date === 'string') return date
  return date.toISOString()
}

/**
 * 安全的字符串转日期函数
 */
export function safeStringToDate(
  dateStr: string | undefined
): Date | undefined {
  if (!dateStr) return undefined
  try {
    return new Date(dateStr)
  } catch {
    return undefined
  }
}
