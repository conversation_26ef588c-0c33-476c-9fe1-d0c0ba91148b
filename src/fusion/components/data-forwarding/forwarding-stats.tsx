import { useState, useEffect, useMemo, useCallback } from 'react'
import { Card, CardContent, CardHeader, CardTitle } from '@/components/ui/card'
import { Badge } from '@/components/ui/badge'
import { Button } from '@/components/ui/button'
import {
  Select,
  SelectContent,
  SelectItem,
  SelectTrigger,
  SelectValue,
} from '@/components/ui/select'
import {
  BarChart3,
  TrendingUp,
  TrendingDown,
  Activity,
  Clock,
  Database,
  Zap,
  AlertTriangle,
  CheckCircle2,
  RefreshCw,
  Download,
  Wifi,
  WifiOff,
  AlertCircle,
  Info,
  Server,
  Network,
} from 'lucide-react'
import { ForwardingConfig } from '@/components/data-forwarding/types'
import { toast } from '@/components/ui/use-toast'
import * as XLSX from 'xlsx'
import {
  useSignalR,
  useSignalREvent,
  HubConnectionState,
  mapHubConnectionState,
} from '@/lib/signalr/signalr-context'

interface ForwardingStatsProps {
  configId: number
  config: ForwardingConfig
}

// 基于后端WebSocket统计系统的数据接口

// 错误类型枚举
export enum ForwardErrorType {
  ConnectionTimeout = 'ConnectionTimeout',
  AuthenticationFailed = 'AuthenticationFailed',
  DataFormatError = 'DataFormatError',
  NetworkError = 'NetworkError',
  ServerError = 'ServerError',
  ConfigurationError = 'ConfigurationError',
  CertificateError = 'CertificateError',
  PermissionDenied = 'PermissionDenied',
  ResourceLimited = 'ResourceLimited',
  Other = 'Other',
}

// 时间序列数据点
interface TimeSeriesPoint {
  timestamp: string
  messageCount: number
  successCount: number
  failCount: number
  avgLatency: number
  dataBytes: number
}

// 基础指标
interface Metrics {
  totalMessages: number
  successMessages: number
  failedMessages: number
  retryMessages: number
  offlineMessages: number
  successRate: number
  failureRate: number
  totalBytes: number
  todayGrowth: number
  growthRate: number
}

// 实时指标
interface RealTimeMetrics {
  messagesPerSecond: number
  bytesPerSecond: number
  activeConnections: number
  queueLength: number
  cpuUsage: number
  memoryUsage: number
}

// 延迟指标
interface LatencyMetrics {
  averageLatency: number
  minLatency: number
  maxLatency: number
  p50Latency: number
  p90Latency: number
  p99Latency: number
  latencyRange: string
}

// 转发统计数据
interface ForwardStatistics {
  timestamp: string
  configId: number
  configName: string
  forwardType: string
  metrics: Metrics
  realTimeMetrics: RealTimeMetrics
  latencyMetrics: LatencyMetrics
  errorCounts: Record<ForwardErrorType, number>
  timeSeries: TimeSeriesPoint[]
}

// 性能报告数据
interface PerformanceReport {
  generatedAt: string
  totalConfigurations: number
  activeConfigurations: number
  averageSuccessRate: number
  averageLatency: number
  totalThroughput: number
  problemConfigurations: number
  errorDistribution: Record<ForwardErrorType, number>
  problemConfigDetails: ForwardStatistics[]
  recommendations: string[]
}

// 错误类型中文映射
const ERROR_TYPE_LABELS: Record<ForwardErrorType, string> = {
  [ForwardErrorType.ConnectionTimeout]: '连接超时',
  [ForwardErrorType.AuthenticationFailed]: '认证失败',
  [ForwardErrorType.DataFormatError]: '数据格式错误',
  [ForwardErrorType.NetworkError]: '网络错误',
  [ForwardErrorType.ServerError]: '服务器错误',
  [ForwardErrorType.ConfigurationError]: '配置错误',
  [ForwardErrorType.CertificateError]: '证书错误',
  [ForwardErrorType.PermissionDenied]: '权限不足',
  [ForwardErrorType.ResourceLimited]: '资源限制',
  [ForwardErrorType.Other]: '其他错误',
}

export function ForwardingStats({ configId, config }: ForwardingStatsProps) {
  const [stats, setStats] = useState<ForwardStatistics | null>(null)
  const [globalStats, setGlobalStats] = useState<ForwardStatistics | null>(null)
  const [performanceReport, setPerformanceReport] =
    useState<PerformanceReport | null>(null)
  const [timeRange, setTimeRange] = useState<string>('24h')
  const [loading, setLoading] = useState(true)
  const [viewMode, setViewMode] = useState<'specific' | 'global' | 'type'>(
    'global'
  )

  // 使用SignalR连接
  const { connectionState, reconnect, sendMessage, connection } = useSignalR()

  // 安全数值转换函数
  const safeNumber = (value: any, defaultValue: number = 0): number => {
    if (typeof value === 'number' && !isNaN(value)) {
      return value
    }
    if (typeof value === 'string') {
      const parsed = parseFloat(value)
      return !isNaN(parsed) ? parsed : defaultValue
    }
    return defaultValue
  }

  // 安全的toFixed函数
  const safeToFixed = (value: any, digits: number = 2): string => {
    const num = safeNumber(value, 0)
    return num.toFixed(digits)
  }

  // 数据安全性检查和默认值函数 - 增强版
  const getSafeStats = (
    statsData: ForwardStatistics | null
  ): ForwardStatistics | null => {
    if (!statsData) return null

    // 确保所有必需的属性存在，并对数组进行严格验证
    const safeStats: ForwardStatistics = {
      ...statsData,
      // 确保timeSeries始终是有效数组
      timeSeries: Array.isArray(statsData.timeSeries)
        ? statsData.timeSeries
        : [],
      metrics: {
        totalMessages: safeNumber(statsData.metrics?.totalMessages, 0),
        successMessages: safeNumber(statsData.metrics?.successMessages, 0),
        failedMessages: safeNumber(statsData.metrics?.failedMessages, 0),
        retryMessages: safeNumber(statsData.metrics?.retryMessages, 0),
        offlineMessages: safeNumber(statsData.metrics?.offlineMessages, 0),
        successRate: safeNumber(statsData.metrics?.successRate, 0),
        failureRate: safeNumber(statsData.metrics?.failureRate, 0),
        totalBytes: safeNumber(statsData.metrics?.totalBytes, 0),
        todayGrowth: safeNumber(statsData.metrics?.todayGrowth, 0),
        growthRate: safeNumber(statsData.metrics?.growthRate, 0),
      },
      realTimeMetrics: {
        messagesPerSecond: safeNumber(
          statsData.realTimeMetrics?.messagesPerSecond,
          0
        ),
        bytesPerSecond: safeNumber(
          statsData.realTimeMetrics?.bytesPerSecond,
          0
        ),
        activeConnections: safeNumber(
          statsData.realTimeMetrics?.activeConnections,
          0
        ),
        queueLength: safeNumber(statsData.realTimeMetrics?.queueLength, 0),
        cpuUsage: safeNumber(statsData.realTimeMetrics?.cpuUsage, 0),
        memoryUsage: safeNumber(statsData.realTimeMetrics?.memoryUsage, 0),
      },
      latencyMetrics: {
        averageLatency: safeNumber(statsData.latencyMetrics?.averageLatency, 0),
        minLatency: safeNumber(statsData.latencyMetrics?.minLatency, 0),
        maxLatency: safeNumber(statsData.latencyMetrics?.maxLatency, 0),
        p50Latency: safeNumber(statsData.latencyMetrics?.p50Latency, 0),
        p90Latency: safeNumber(statsData.latencyMetrics?.p90Latency, 0),
        p99Latency: safeNumber(statsData.latencyMetrics?.p99Latency, 0),
        latencyRange: statsData.latencyMetrics?.latencyRange || '0-0ms',
      },
      errorCounts: statsData.errorCounts || {},
    }

    // 确保timeSeries中的数据点也是安全的，过滤无效项
    if (Array.isArray(safeStats.timeSeries)) {
      safeStats.timeSeries = safeStats.timeSeries
        .filter((point) => point && typeof point === 'object') // 过滤无效的数据点
        .map((point) => ({
          timestamp: point?.timestamp || '',
          messageCount: safeNumber(point?.messageCount, 0),
          successCount: safeNumber(point?.successCount, 0),
          failCount: safeNumber(point?.failCount, 0),
          avgLatency: safeNumber(point?.avgLatency, 0),
          dataBytes: safeNumber(point?.dataBytes, 0),
        }))
    } else {
      // 如果timeSeries不是数组，设为空数组
      safeStats.timeSeries = []
    }

    return safeStats
  }

  // 获取安全的统计数据
  const safeStats = getSafeStats(stats)

  // 统计数据订阅主题
  const specificTopic = `${configId}_forward_statistics`
  const globalTopic = 'forward_statistics_global'
  const typeTopic = `forward_statistics_type_${config.type.toLowerCase()}`
  const performanceTopic = 'forward_statistics_performance_report'

  // 添加调试日志
  useEffect(() => {
    console.log('ForwardingStats组件状态:', {
      configId,
      viewMode,
      loading,
      hasStats: !!stats,
      hasGlobalStats: !!globalStats,
      connectionState,
      globalTopic,
      typeTopic,
    })
  }, [configId, viewMode, loading, stats, globalStats, connectionState])

  // 安全的数据解析函数
  const parseSignalRData = <T,>(data: T | string): T | null => {
    try {
      // 如果数据是字符串，尝试解析
      if (typeof data === 'string') {
        return JSON.parse(data) as T
      }
      // 如果已经是对象，直接返回
      return data
    } catch (error) {
      console.error('SignalR数据解析失败:', error, '原始数据:', data)
      return null
    }
  }

  // 订阅全局统计事件
  useSignalREvent<ForwardStatistics>(
    globalTopic,
    useCallback(
      (rawData: ForwardStatistics | string) => {
        const data = parseSignalRData<ForwardStatistics>(rawData)
        if (!data) {
          console.error('无法解析全局统计数据:', rawData)
          return
        }

        console.log('收到全局统计数据:', { data, viewMode })
        console.log('数据结构检查:', {
          hasTimeSeries: !!data.timeSeries,
          timeSeriesLength: data.timeSeries?.length || 0,
          hasMetrics: !!data.metrics,
          hasRealTimeMetrics: !!data.realTimeMetrics,
          hasLatencyMetrics: !!data.latencyMetrics,
        })
        setGlobalStats(data)
        if (viewMode === 'global') {
          setStats(data)
          setLoading(false) // 收到数据时停止loading
        }
        // 如果是首次收到数据且loading状态为true，停止loading
        if (loading) {
          setLoading(false)
        }
      },
      [viewMode, loading]
    ),
    [viewMode, loading]
  )

  // 订阅类型统计事件
  useSignalREvent<ForwardStatistics>(
    typeTopic,
    useCallback(
      (rawData: ForwardStatistics | string) => {
        const data = parseSignalRData<ForwardStatistics>(rawData)
        if (!data) {
          console.error('无法解析类型统计数据:', rawData)
          return
        }

        console.log('收到类型统计数据:', { data, viewMode })
        console.log('数据结构检查:', {
          hasTimeSeries: !!data.timeSeries,
          timeSeriesLength: data.timeSeries?.length || 0,
          hasMetrics: !!data.metrics,
          hasRealTimeMetrics: !!data.realTimeMetrics,
          hasLatencyMetrics: !!data.latencyMetrics,
        })
        if (viewMode === 'type') {
          setStats(data)
          setLoading(false) // 收到数据时停止loading
        }
        // 如果是首次收到数据且loading状态为true，停止loading
        if (loading) {
          setLoading(false)
        }
      },
      [viewMode, loading]
    ),
    [viewMode, loading]
  )

  // 订阅性能报告事件
  useSignalREvent<PerformanceReport>(
    performanceTopic,
    useCallback((rawData: PerformanceReport | string) => {
      const data = parseSignalRData<PerformanceReport>(rawData)
      if (!data) {
        console.error('无法解析性能报告数据:', rawData)
        return
      }

      console.log('收到性能报告数据:', data)
      setPerformanceReport(data)
    }, []),
    []
  )

  // 切换视图模式时更新显示数据
  useEffect(() => {
    console.log('视图模式切换:', { viewMode, hasGlobalStats: !!globalStats })
    if (viewMode === 'global' && globalStats) {
      setStats(globalStats)
      setLoading(false) // 确保有数据时停止loading
    }
    // 注意：specific 和 type 模式的数据会在对应的 useSignalREvent 中处理
  }, [viewMode, globalStats])

  // 刷新数据
  const handleRefresh = async () => {
    try {
      // 重新订阅主题以触发数据刷新
      if (connection && connectionState === 'connected') {
        await sendMessage('SubscribeTopic', globalTopic)
        await sendMessage('SubscribeTopic', typeTopic)
        await sendMessage('SubscribeTopic', performanceTopic)
      }
    } catch (error) {
      console.error('刷新数据失败:', error)
    }
    toast({
      title: '数据已刷新',
      description: '统计数据已更新',
    })
  }

  // 导出统计报告
  const exportReport = () => {
    if (!safeStats) return

    const reportData = [
      ['指标', '数值'],
      ['总消息数', safeStats.metrics.totalMessages],
      ['成功消息数', safeStats.metrics.successMessages],
      ['失败消息数', safeStats.metrics.failedMessages],
      ['成功率', `${safeToFixed(safeStats.metrics.successRate, 2)}%`],
      ['平均响应时间', `${safeStats.latencyMetrics.averageLatency}ms`],
      ['最大响应时间', `${safeStats.latencyMetrics.maxLatency}ms`],
      ['最小响应时间', `${safeStats.latencyMetrics.minLatency}ms`],
      ['吞吐量', `${safeStats.realTimeMetrics.messagesPerSecond} msg/s`],
      [],
      ['每日统计', ''],
      ['日期', '总数', '成功', '失败', '平均响应时间'],
      ...(safeStats?.timeSeries?.map((point) => [
        point?.timestamp || '',
        point?.messageCount || 0,
        point?.successCount || 0,
        point?.failCount || 0,
        `${point?.avgLatency || 0}ms`,
      ]) || []),
    ]

    const ws = XLSX.utils.aoa_to_sheet(reportData)
    const wb = XLSX.utils.book_new()
    XLSX.utils.book_append_sheet(wb, ws, '转发统计报告')
    XLSX.writeFile(
      wb,
      `forwarding-stats-${configId}-${new Date()
        .toISOString()
        .slice(0, 10)}.xlsx`
    )

    toast({
      title: '导出成功',
      description: '统计报告已导出',
    })
  }

  // 计算趋势 - 增强参数验证
  const calculateTrend = (data: number[] | undefined | null) => {
    // 严格的输入验证
    if (!data || !Array.isArray(data) || data.length < 2) return 0

    // 确保数组中的元素都是有效数字
    const validData = data.filter(
      (value) => typeof value === 'number' && !isNaN(value)
    )
    if (validData.length < 2) return 0

    const recent =
      validData.slice(-10).reduce((a, b) => a + b, 0) /
      Math.min(validData.length, 10)
    const previous =
      validData.slice(-20, -10).reduce((a, b) => a + b, 0) /
      Math.min(validData.slice(-20, -10).length, 10)
    if (previous === 0) return 0
    return ((recent - previous) / previous) * 100
  }

  // 渲染趋势指示器
  const renderTrend = (value: number) => {
    if (Math.abs(value) < 1) return null

    const isPositive = value > 0
    return (
      <div
        className={`flex items-center text-xs ${
          isPositive ? 'text-green-600' : 'text-red-600'
        }`}>
        {isPositive ? (
          <TrendingUp className="h-3 w-3 mr-1" />
        ) : (
          <TrendingDown className="h-3 w-3 mr-1" />
        )}
        {safeToFixed(Math.abs(value), 1)}%
      </div>
    )
  }

  // 渲染简化的图表 - 增强数组安全性
  const renderMiniChart = (
    data: number[] | undefined | null,
    color: string = 'bg-blue-500'
  ) => {
    // 严格的输入验证
    if (!data || !Array.isArray(data) || data.length === 0) {
      return (
        <div className="flex items-center justify-center h-12 text-xs text-muted-foreground">
          无数据
        </div>
      )
    }

    // 过滤有效数字
    const validData = data.filter(
      (value) => typeof value === 'number' && !isNaN(value)
    )
    if (validData.length === 0) {
      return (
        <div className="flex items-center justify-center h-12 text-xs text-muted-foreground">
          无有效数据
        </div>
      )
    }

    const max = Math.max(...validData)
    const min = Math.min(...validData)
    const range = max - min || 1

    return (
      <div className="flex items-end space-x-1 h-12">
        {validData.slice(-20).map((value, index) => {
          const height = ((value - min) / range) * 100
          return (
            <div
              key={index}
              className={`${color} rounded-sm opacity-70 hover:opacity-100 transition-opacity`}
              style={{
                height: `${Math.max(height, 5)}%`,
                width: '3px',
              }}
              title={`${value}`}
            />
          )
        })}
      </div>
    )
  }

  // 获取连接状态显示信息
  const getConnectionStatusInfo = (state: HubConnectionState) => {
    switch (state) {
      case HubConnectionState.Connected:
        return {
          icon: <Wifi className="h-4 w-4" />,
          text: '已连接',
          className: 'bg-green-100 text-green-800 border-green-300',
        }
      case HubConnectionState.Connecting:
      case HubConnectionState.Reconnecting:
        return {
          icon: <RefreshCw className="h-4 w-4 animate-spin" />,
          text: state === HubConnectionState.Connecting ? '连接中' : '重连中',
          className: 'bg-yellow-100 text-yellow-800 border-yellow-300',
        }
      case HubConnectionState.Disconnected:
      case HubConnectionState.Disconnecting:
        return {
          icon: <WifiOff className="h-4 w-4" />,
          text: '未连接',
          className: 'bg-gray-100 text-gray-800 border-gray-300',
        }
      default:
        return {
          icon: <WifiOff className="h-4 w-4" />,
          text: '未知状态',
          className: 'bg-gray-100 text-gray-800 border-gray-300',
        }
    }
  }

  // 渲染连接状态
  const renderConnectionStatus = () => {
    // 将connectionState字符串转换为HubConnectionState
    const hubState =
      connectionState === 'connected'
        ? HubConnectionState.Connected
        : connectionState === 'connecting'
        ? HubConnectionState.Connecting
        : connectionState === 'reconnecting'
        ? HubConnectionState.Reconnecting
        : HubConnectionState.Disconnected

    const { icon, text, className } = getConnectionStatusInfo(hubState)
    return (
      <Badge variant="outline" className={className}>
        {icon}
        <span className="ml-1">{text}</span>
      </Badge>
    )
  }

  // 显式订阅统计主题
  useEffect(() => {
    const subscribeToTopics = async () => {
      // 检查连接状态
      if (connection && connection.state === 'Connected') {
        try {
          console.log('开始订阅统计主题...', {
            globalTopic,
            typeTopic,
            performanceTopic,
          })
          // 订阅全局统计
          await sendMessage('SubscribeTopic', globalTopic)
          // 订阅类型统计
          await sendMessage('SubscribeTopic', typeTopic)
          // 订阅性能报告
          await sendMessage('SubscribeTopic', performanceTopic)
          console.log(
            `已订阅统计主题: ${globalTopic}, ${typeTopic}, ${performanceTopic}`
          )
        } catch (error) {
          console.error('订阅统计主题失败:', error)
          setLoading(false) // 订阅失败时停止loading
        }
      } else {
        console.warn('SignalR连接未就绪，无法订阅主题:', connection?.state)
        setLoading(false) // 连接未就绪时停止loading
      }
    }

    subscribeToTopics()

    // 组件卸载时取消订阅
    return () => {
      if (connection && connection.state === 'Connected') {
        Promise.all([
          sendMessage('UnsubscribeTopic', globalTopic),
          sendMessage('UnsubscribeTopic', typeTopic),
          sendMessage('UnsubscribeTopic', performanceTopic),
        ])
          .then(() => console.log('已取消订阅统计主题'))
          .catch((err) => console.error('取消订阅统计主题失败:', err))
      }
    }
  }, [
    connection,
    connectionState,
    sendMessage,
    globalTopic,
    typeTopic,
    performanceTopic,
  ])

  if (loading || !safeStats) {
    console.log('显示加载页面:', {
      loading,
      hasStats: !!stats,
      hasSafeStats: !!safeStats,
      connectionState,
    })
    return (
      <div className="space-y-6">
        <div className="flex items-center justify-between">
          <div className="flex items-center space-x-4">
            <h3 className="text-lg font-medium flex items-center">
              <BarChart3 className="h-5 w-5 mr-2" />
              转发统计分析
            </h3>
            {renderConnectionStatus()}
          </div>
        </div>

        <Card>
          <CardContent className="flex flex-col items-center justify-center py-12">
            <RefreshCw className="h-8 w-8 animate-spin mr-2 mb-4" />
            <p className="text-lg">正在加载统计数据...</p>
            <p className="text-sm text-muted-foreground mt-2">
              {connectionState === 'connected'
                ? '等待WebSocket数据推送...'
                : '正在建立WebSocket连接...'}
            </p>
            <p className="text-xs text-muted-foreground mt-2">
              调试: loading={loading.toString()}, hasStats={(!stats).toString()}
              , hasSafeStats={(!safeStats).toString()}, viewMode={viewMode}
            </p>
          </CardContent>
        </Card>
      </div>
    )
  }

  // 安全计算hourlyTrend，避免数组访问错误
  const hourlyTrend = calculateTrend(
    safeStats?.timeSeries?.map((point) => point?.avgLatency) || []
  )

  return (
    <div className="space-y-6">
      {/* 控制栏 */}
      <div className="flex items-center justify-between">
        <div className="flex items-center space-x-4">
          <h3 className="text-lg font-medium flex items-center">
            <BarChart3 className="h-5 w-5 mr-2" />
            转发统计分析
          </h3>

          {/* 视图模式选择 */}
          <Select
            value={viewMode}
            onValueChange={(value: 'specific' | 'global' | 'type') =>
              setViewMode(value)
            }>
            <SelectTrigger className="w-40">
              <SelectValue />
            </SelectTrigger>
            <SelectContent>
              <SelectItem value="global">全局统计</SelectItem>
              <SelectItem value="type">{config.type}类型统计</SelectItem>
            </SelectContent>
          </Select>

          <Select value={timeRange} onValueChange={setTimeRange}>
            <SelectTrigger className="w-32">
              <SelectValue />
            </SelectTrigger>
            <SelectContent>
              <SelectItem value="1h">最近1小时</SelectItem>
              <SelectItem value="24h">最近24小时</SelectItem>
              <SelectItem value="7d">最近7天</SelectItem>
              <SelectItem value="30d">最近30天</SelectItem>
            </SelectContent>
          </Select>

          {/* 连接状态显示 */}
          {renderConnectionStatus()}
        </div>

        <div className="flex space-x-2">
          <Button variant="outline" size="sm" onClick={handleRefresh}>
            <RefreshCw className="h-4 w-4 mr-2" />
            刷新
          </Button>
          <Button variant="outline" size="sm" onClick={exportReport}>
            <Download className="h-4 w-4 mr-2" />
            导出报告
          </Button>
        </div>
      </div>

      {/* 关键指标卡片 */}
      <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-6 gap-6">
        {/* 总消息数 */}
        <Card>
          <CardContent className="p-6">
            <div className="flex items-center justify-between">
              <div>
                <p className="text-sm font-medium text-muted-foreground">
                  总消息数
                </p>
                <p className="text-2xl font-bold">
                  {safeStats.metrics.totalMessages.toLocaleString()}
                </p>
                {renderTrend(hourlyTrend)}
              </div>
              <div className="h-12 w-12 bg-blue-100 rounded-full flex items-center justify-center">
                <Activity className="h-6 w-6 text-blue-600" />
              </div>
            </div>
            <div className="mt-4">
              {renderMiniChart(
                safeStats?.timeSeries?.map((point) => point?.messageCount) || []
              )}
            </div>
          </CardContent>
        </Card>

        {/* 成功率 */}
        <Card>
          <CardContent className="p-6">
            <div className="flex items-center justify-between">
              <div>
                <p className="text-sm font-medium text-muted-foreground">
                  成功率
                </p>
                <p className="text-2xl font-bold">
                  {safeToFixed(safeStats.metrics.successRate, 1)}%
                </p>
                <div className="flex items-center mt-1">
                  <CheckCircle2 className="h-3 w-3 text-green-500 mr-1" />
                  <span className="text-xs text-muted-foreground">
                    {safeStats.metrics.successMessages.toLocaleString()} 成功
                  </span>
                </div>
              </div>
              <div className="h-12 w-12 bg-green-100 rounded-full flex items-center justify-center">
                <CheckCircle2 className="h-6 w-6 text-green-600" />
              </div>
            </div>
          </CardContent>
        </Card>

        {/* 平均响应时间 */}
        <Card>
          <CardContent className="p-6">
            <div className="flex items-center justify-between">
              <div>
                <p className="text-sm font-medium text-muted-foreground">
                  P50延迟
                </p>
                <p className="text-2xl font-bold">
                  {safeToFixed(safeStats.latencyMetrics.averageLatency, 2)}ms
                </p>
                <div className="text-xs text-muted-foreground mt-1">
                  范围: {safeStats.latencyMetrics.minLatency}-
                  {safeStats.latencyMetrics.maxLatency}ms
                </div>
              </div>
              <div className="h-12 w-12 bg-amber-100 rounded-full flex items-center justify-center">
                <Clock className="h-6 w-6 text-amber-600" />
              </div>
            </div>
          </CardContent>
        </Card>

        {/* 实时吞吐量 */}
        <Card>
          <CardContent className="p-6">
            <div className="flex items-center justify-between">
              <div>
                <p className="text-sm font-medium text-muted-foreground">
                  实时吞吐量
                </p>
                <p className="text-2xl font-bold">
                  {safeStats.realTimeMetrics.messagesPerSecond.toFixed(2)}
                </p>
                <p className="text-xs text-muted-foreground">消息/秒</p>
              </div>
              <div className="h-12 w-12 bg-purple-100 rounded-full flex items-center justify-center">
                <Zap className="h-6 w-6 text-purple-600" />
              </div>
            </div>
          </CardContent>
        </Card>

        {/* 活跃连接数 */}
        <Card>
          <CardContent className="p-6">
            <div className="flex items-center justify-between">
              <div>
                <p className="text-sm font-medium text-muted-foreground">
                  活跃连接
                </p>
                <p className="text-2xl font-bold">
                  {safeStats.realTimeMetrics.activeConnections}
                </p>
                <div className="text-xs text-muted-foreground mt-1">
                  队列: {safeStats.realTimeMetrics.queueLength}
                </div>
              </div>
              <div className="h-12 w-12 bg-cyan-100 rounded-full flex items-center justify-center">
                <Network className="h-6 w-6 text-cyan-600" />
              </div>
            </div>
          </CardContent>
        </Card>

        {/* 数据传输速率 */}
        <Card>
          <CardContent className="p-6">
            <div className="flex items-center justify-between">
              <div>
                <p className="text-sm font-medium text-muted-foreground">
                  传输速率
                </p>
                <p className="text-2xl font-bold">
                  {safeToFixed(
                    safeStats.realTimeMetrics.bytesPerSecond / 1024,
                    1
                  )}
                </p>
                <p className="text-xs text-muted-foreground">KB/s</p>
              </div>
              <div className="h-12 w-12 bg-indigo-100 rounded-full flex items-center justify-center">
                <Database className="h-6 w-6 text-indigo-600" />
              </div>
            </div>
          </CardContent>
        </Card>
      </div>

      {/* 延迟性能指标 */}
      <div className="grid grid-cols-1 md:grid-cols-4 gap-6">
        <Card>
          <CardHeader className="pb-2">
            <CardTitle className="text-base flex items-center">
              <Clock className="h-4 w-4 mr-2" />
              延迟分析
            </CardTitle>
          </CardHeader>
          <CardContent>
            <div className="grid grid-cols-2 gap-4 text-sm">
              <div>
                <span className="text-muted-foreground block">P50延迟</span>
                <span className="font-medium">
                  {safeToFixed(safeStats.latencyMetrics.p50Latency, 2)}ms
                </span>
              </div>
              <div>
                <span className="text-muted-foreground block">P90延迟</span>
                <span className="font-medium">
                  {safeToFixed(safeStats.latencyMetrics.p90Latency, 2)}ms
                </span>
              </div>
              <div>
                <span className="text-muted-foreground block">P99延迟</span>
                <span className="font-medium">
                  {safeToFixed(safeStats.latencyMetrics.p99Latency, 2)}ms
                </span>
              </div>
              <div>
                <span className="text-muted-foreground block">最大延迟</span>
                <span className="font-medium">
                  {safeToFixed(safeStats.latencyMetrics.maxLatency, 2)}ms
                </span>
              </div>
            </div>
          </CardContent>
        </Card>

        <Card>
          <CardHeader className="pb-2">
            <CardTitle className="text-base flex items-center">
              <Server className="h-4 w-4 mr-2" />
              系统负载
            </CardTitle>
          </CardHeader>
          <CardContent>
            <div className="space-y-3">
              <div>
                <div className="flex justify-between text-sm mb-1">
                  <span>CPU使用率</span>
                  <span>
                    {safeToFixed(safeStats.realTimeMetrics.cpuUsage, 1)}%
                  </span>
                </div>
                <div className="w-full bg-gray-200 rounded-full h-2">
                  <div
                    className="bg-blue-500 h-2 rounded-full"
                    style={{
                      width: `${Math.min(
                        safeStats.realTimeMetrics.cpuUsage,
                        100
                      )}%`,
                    }}
                  />
                </div>
              </div>
              <div>
                <div className="flex justify-between text-sm mb-1">
                  <span>内存使用率</span>
                  <span>
                    {safeToFixed(safeStats.realTimeMetrics.memoryUsage, 1)}%
                  </span>
                </div>
                <div className="w-full bg-gray-200 rounded-full h-2">
                  <div
                    className="bg-green-500 h-2 rounded-full"
                    style={{
                      width: `${Math.min(
                        safeStats.realTimeMetrics.memoryUsage,
                        100
                      )}%`,
                    }}
                  />
                </div>
              </div>
            </div>
          </CardContent>
        </Card>

        <Card>
          <CardHeader className="pb-2">
            <CardTitle className="text-base flex items-center">
              <Activity className="h-4 w-4 mr-2" />
              增长趋势
            </CardTitle>
          </CardHeader>
          <CardContent>
            <div className="space-y-2 text-sm">
              <div className="flex justify-between">
                <span className="text-muted-foreground">今日增长</span>
                <span className="font-medium">
                  +{safeStats.metrics.todayGrowth.toLocaleString()}
                </span>
              </div>
              <div className="flex justify-between">
                <span className="text-muted-foreground">增长率</span>
                <span
                  className={`font-medium ${
                    safeStats.metrics.growthRate >= 0
                      ? 'text-green-600'
                      : 'text-red-600'
                  }`}>
                  {safeStats.metrics.growthRate >= 0 ? '+' : ''}
                  {safeToFixed(safeStats.metrics.growthRate, 1)}%
                </span>
              </div>
              <div className="flex justify-between">
                <span className="text-muted-foreground">重试消息</span>
                <span className="font-medium">
                  {safeStats.metrics.retryMessages.toLocaleString()}
                </span>
              </div>
              <div className="flex justify-between">
                <span className="text-muted-foreground">离线消息</span>
                <span className="font-medium">
                  {safeStats.metrics.offlineMessages.toLocaleString()}
                </span>
              </div>
            </div>
          </CardContent>
        </Card>

        <Card>
          <CardHeader className="pb-2">
            <CardTitle className="text-base flex items-center">
              <Info className="h-4 w-4 mr-2" />
              配置信息
            </CardTitle>
          </CardHeader>
          <CardContent>
            <div className="space-y-2 text-sm">
              <div className="flex justify-between">
                <span className="text-muted-foreground">配置名称</span>
                <span className="font-medium truncate">
                  {safeStats.configName}
                </span>
              </div>
              <div className="flex justify-between">
                <span className="text-muted-foreground">转发类型</span>
                <Badge variant="secondary">{safeStats.forwardType}</Badge>
              </div>
              <div className="flex justify-between">
                <span className="text-muted-foreground">最后更新</span>
                <span className="font-medium">
                  {new Date(safeStats.timestamp).toLocaleTimeString()}
                </span>
              </div>
            </div>
          </CardContent>
        </Card>
      </div>

      {/* 详细图表和统计 */}
      <div className="grid grid-cols-1 lg:grid-cols-2 gap-6">
        {/* 每日统计 */}
        <Card>
          <CardHeader>
            <CardTitle className="text-base">时间序列数据</CardTitle>
          </CardHeader>
          <CardContent>
            <div className="space-y-4">
              {(safeStats?.timeSeries || []).slice(-7).map((point, index) => (
                <div
                  key={point?.timestamp || index}
                  className="flex items-center justify-between p-3 bg-gray-50 rounded-lg">
                  <div className="flex-1">
                    <div className="flex items-center justify-between mb-1">
                      <span className="text-sm font-medium">
                        {point?.timestamp
                          ? new Date(point.timestamp).toLocaleString()
                          : '无时间戳'}
                      </span>
                      <span className="text-sm text-muted-foreground">
                        {point?.messageCount || 0} 条
                      </span>
                    </div>

                    {/* 成功率进度条 */}
                    <div className="w-full bg-gray-200 rounded-full h-2">
                      <div
                        className="bg-green-500 h-2 rounded-full"
                        style={{
                          width: `${
                            (point?.messageCount || 0) > 0
                              ? ((point?.successCount || 0) /
                                  (point?.messageCount || 1)) *
                                100
                              : 0
                          }%`,
                        }}
                      />
                    </div>

                    <div className="flex justify-between mt-1 text-xs text-muted-foreground">
                      <span>成功: {point?.successCount || 0}</span>
                      <span>失败: {point?.failCount || 0}</span>
                      <span>
                        响应: {safeToFixed(point?.avgLatency || 0, 1)}ms
                      </span>
                    </div>
                  </div>
                </div>
              ))}
            </div>
          </CardContent>
        </Card>

        {/* 错误类型分析 */}
        <Card>
          <CardHeader>
            <CardTitle className="text-base">错误类型分析</CardTitle>
          </CardHeader>
          <CardContent>
            <div className="space-y-4">
              {Object.entries(safeStats.errorCounts)
                .filter(([_, count]) => count > 0)
                .sort(([, a], [, b]) => b - a)
                .map(([type, count]) => {
                  const errorType = type as ForwardErrorType
                  const label = ERROR_TYPE_LABELS[errorType] || type
                  const percentage =
                    safeStats.metrics.failedMessages > 0
                      ? (count / safeStats.metrics.failedMessages) * 100
                      : 0
                  return (
                    <div
                      key={type}
                      className="flex items-center justify-between">
                      <div className="flex items-center flex-1">
                        <AlertTriangle className="h-4 w-4 text-amber-500 mr-2" />
                        <span className="text-sm">{label}</span>
                      </div>
                      <div className="flex items-center space-x-2">
                        <div className="w-24 bg-gray-200 rounded-full h-2">
                          <div
                            className={`h-2 rounded-full ${
                              errorType === ForwardErrorType.ConnectionTimeout
                                ? 'bg-red-500'
                                : errorType === ForwardErrorType.NetworkError
                                ? 'bg-orange-500'
                                : errorType ===
                                  ForwardErrorType.AuthenticationFailed
                                ? 'bg-purple-500'
                                : errorType === ForwardErrorType.DataFormatError
                                ? 'bg-yellow-500'
                                : 'bg-gray-500'
                            }`}
                            style={{ width: `${percentage}%` }}
                          />
                        </div>
                        <span className="text-sm font-medium w-12 text-right">
                          {count}
                        </span>
                        <span className="text-xs text-muted-foreground w-12 text-right">
                          {safeToFixed(percentage, 1)}%
                        </span>
                      </div>
                    </div>
                  )
                })}

              {Object.values(safeStats.errorCounts).every(
                (count) => count === 0
              ) && (
                <div className="text-center py-8 text-muted-foreground">
                  <CheckCircle2 className="h-8 w-8 mx-auto mb-2 text-green-500" />
                  <p>暂无错误记录</p>
                </div>
              )}
            </div>
          </CardContent>
        </Card>
      </div>

      {/* 性能报告部分 */}
      {performanceReport && (
        <Card>
          <CardHeader>
            <CardTitle className="text-base flex items-center">
              <BarChart3 className="h-4 w-4 mr-2" />
              性能报告
              <Badge variant="outline" className="ml-2">
                {new Date(performanceReport.generatedAt).toLocaleTimeString()}
              </Badge>
            </CardTitle>
          </CardHeader>
          <CardContent>
            <div className="grid grid-cols-1 md:grid-cols-3 gap-6">
              {/* 总体统计 */}
              <div className="space-y-3">
                <h4 className="font-medium text-sm text-muted-foreground">
                  总体统计
                </h4>
                <div className="space-y-2 text-sm">
                  <div className="flex justify-between">
                    <span>总配置数</span>
                    <span className="font-medium">
                      {performanceReport.totalConfigurations}
                    </span>
                  </div>
                  <div className="flex justify-between">
                    <span>活跃配置</span>
                    <span className="font-medium">
                      {performanceReport.activeConfigurations}
                    </span>
                  </div>
                  <div className="flex justify-between">
                    <span>平均成功率</span>
                    <span className="font-medium">
                      {safeToFixed(performanceReport.averageSuccessRate, 1)}%
                    </span>
                  </div>
                  <div className="flex justify-between">
                    <span>平均延迟</span>
                    <span className="font-medium">
                      {safeToFixed(performanceReport.averageLatency, 1)}ms
                    </span>
                  </div>
                  <div className="flex justify-between">
                    <span>总吞吐量</span>
                    <span className="font-medium">
                      {safeToFixed(performanceReport.totalThroughput, 1)} msg/s
                    </span>
                  </div>
                </div>
              </div>

              {/* 问题配置 */}
              <div className="space-y-3">
                <h4 className="font-medium text-sm text-muted-foreground">
                  问题配置
                </h4>
                {performanceReport.problemConfigurations > 0 ? (
                  <div className="space-y-2">
                    <div className="flex items-center">
                      <AlertTriangle className="h-4 w-4 text-amber-500 mr-2" />
                      <span className="text-sm">
                        发现 {performanceReport.problemConfigurations}{' '}
                        个问题配置
                      </span>
                    </div>
                    {(performanceReport.problemConfigDetails || [])
                      .slice(0, 3)
                      .map((config) => (
                        <div
                          key={config.configId}
                          className="p-2 bg-amber-50 rounded text-sm">
                          <div className="font-medium">{config.configName}</div>
                          <div className="text-xs text-muted-foreground">
                            成功率: {safeToFixed(config.metrics.successRate, 1)}
                            % | 延迟:{' '}
                            {safeToFixed(
                              config.latencyMetrics.averageLatency,
                              1
                            )}
                            ms
                          </div>
                        </div>
                      ))}
                  </div>
                ) : (
                  <div className="flex items-center text-sm text-green-600">
                    <CheckCircle2 className="h-4 w-4 mr-2" />
                    <span>所有配置运行正常</span>
                  </div>
                )}
              </div>

              {/* 改进建议 */}
              <div className="space-y-3">
                <h4 className="font-medium text-sm text-muted-foreground">
                  改进建议
                </h4>
                <div className="space-y-2">
                  {(performanceReport.recommendations || [])
                    .slice(0, 3)
                    .map((recommendation, index) => (
                      <div key={index} className="flex items-start text-sm">
                        <Info className="h-4 w-4 text-blue-500 mr-2 mt-0.5 flex-shrink-0" />
                        <span>{recommendation}</span>
                      </div>
                    ))}
                </div>
              </div>
            </div>
          </CardContent>
        </Card>
      )}

      {/* 实时监控图表 */}
      <Card>
        <CardHeader>
          <CardTitle className="text-base">实时监控</CardTitle>
        </CardHeader>
        <CardContent>
          <div className="h-32 flex items-end space-x-1">
            {(safeStats?.timeSeries || []).length > 0 ? (
              (safeStats?.timeSeries || []).slice(-20).map((point, index) => {
                const validTimeSeries = safeStats?.timeSeries || []
                const validLatencies = validTimeSeries
                  .map((p) => p?.avgLatency || 0)
                  .filter((lat) => typeof lat === 'number' && !isNaN(lat))
                const maxLatency =
                  validLatencies.length > 0 ? Math.max(...validLatencies) : 1
                const pointLatency = point?.avgLatency || 0
                const height =
                  maxLatency > 0 ? (pointLatency / maxLatency) * 100 : 0

                return (
                  <div
                    key={point?.timestamp || index}
                    className="bg-gradient-to-t from-blue-500 to-blue-300 rounded-sm opacity-80 hover:opacity-100 transition-opacity flex-1 min-w-0"
                    style={{ height: `${Math.max(height, 5)}%` }}
                    title={`${
                      point?.timestamp
                        ? new Date(point.timestamp).toLocaleString()
                        : '无时间戳'
                    }: ${safeToFixed(pointLatency, 2)}ms`}
                  />
                )
              })
            ) : (
              <div className="flex items-center justify-center w-full h-full text-muted-foreground">
                <p className="text-sm">等待数据...</p>
              </div>
            )}
          </div>

          {/* 图表说明 */}
          <div className="mt-4 flex items-center justify-between text-xs text-muted-foreground">
            <span>延迟趋势 (最近20个数据点)</span>
            {(safeStats?.timeSeries || []).length > 0 && (
              <span>
                当前:{' '}
                {(() => {
                  const lastPoint = (safeStats?.timeSeries || [])[
                    (safeStats?.timeSeries || []).length - 1
                  ]
                  return lastPoint?.avgLatency !== undefined
                    ? safeToFixed(lastPoint.avgLatency, 2)
                    : '0.00'
                })()}
                ms
              </span>
            )}
          </div>
        </CardContent>
      </Card>
    </div>
  )
}
