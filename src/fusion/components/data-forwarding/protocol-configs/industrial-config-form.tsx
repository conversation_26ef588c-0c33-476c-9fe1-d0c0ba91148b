import { useState, useEffect } from 'react'
import { Button } from '@/components/ui/button'
import { Input } from '@/components/ui/input'
import { Label } from '@/components/ui/label'
import { Switch } from '@/components/ui/switch'
import {
  Select,
  SelectContent,
  SelectItem,
  SelectTrigger,
  SelectValue,
} from '@/components/ui/select'
import { Tabs, TabsContent, TabsList, TabsTrigger } from '@/components/ui/tabs'
import { Card, CardContent, CardHeader, CardTitle } from '@/components/ui/card'
import { Badge } from '@/components/ui/badge'
import { Plus, Trash2, AlertCircle, Info, Settings, Zap } from 'lucide-react'
import { Alert, AlertDescription } from '@/components/ui/alert'
import type { IndustrialConfig, IndustrialProtocol } from '../types'

interface IndustrialConfigFormProps {
  config: IndustrialConfig
  protocolType: IndustrialProtocol
  onChange: (config: IndustrialConfig) => void
}

export function IndustrialConfigForm({
  config,
  protocolType,
  onChange,
}: IndustrialConfigFormProps) {
  const [activeTab, setActiveTab] = useState('connection')
  const [testConnection, setTestConnection] = useState<
    'idle' | 'testing' | 'success' | 'error'
  >('idle')

  // OPC UA订阅管理
  const [subscriptions, setSubscriptions] = useState(config.subscriptions || [])

  useEffect(() => {
    if (protocolType === 'OPCUA') {
      onChange({ ...config, subscriptions })
    }
  }, [subscriptions, protocolType])

  const addSubscription = () => {
    setSubscriptions([
      ...subscriptions,
      {
        nodeId: '',
        displayName: '',
        dataType: 'Double',
        samplingInterval: 1000,
        enable: true,
      },
    ])
  }

  const updateSubscription = (index: number, field: string, value: any) => {
    const newSubscriptions = [...subscriptions]
    newSubscriptions[index] = { ...newSubscriptions[index], [field]: value }
    setSubscriptions(newSubscriptions)
  }

  const deleteSubscription = (index: number) => {
    setSubscriptions(subscriptions.filter((_, i) => i !== index))
  }

  const handleTestConnection = async () => {
    setTestConnection('testing')
    // 模拟连接测试
    setTimeout(() => {
      setTestConnection(Math.random() > 0.3 ? 'success' : 'error')
    }, 2000)
  }

  // 渲染Modbus配置
  const renderModbusConfig = () => (
    <div className="space-y-4">
      <div className="grid grid-cols-1 md:grid-cols-3 gap-4">
        <div className="space-y-2">
          <Label htmlFor="slaveId">从站地址 *</Label>
          <Input
            id="slaveId"
            type="number"
            value={config.slaveId || ''}
            onChange={(e) =>
              onChange({ ...config, slaveId: Number(e.target.value) })
            }
            placeholder="1"
            min="1"
            max="247"
          />
          <p className="text-xs text-muted-foreground">
            Modbus从站地址 (1-247)
          </p>
        </div>
        <div className="space-y-2">
          <Label htmlFor="functionCode">功能码</Label>
          <Select
            value={config.functionCode?.toString() || '3'}
            onValueChange={(value) =>
              onChange({ ...config, functionCode: Number(value) })
            }>
            <SelectTrigger>
              <SelectValue />
            </SelectTrigger>
            <SelectContent>
              <SelectItem value="1">01 - 读取线圈状态</SelectItem>
              <SelectItem value="2">02 - 读取离散输入</SelectItem>
              <SelectItem value="3">03 - 读取保持寄存器</SelectItem>
              <SelectItem value="4">04 - 读取输入寄存器</SelectItem>
              <SelectItem value="5">05 - 写单个线圈</SelectItem>
              <SelectItem value="6">06 - 写单个寄存器</SelectItem>
              <SelectItem value="15">15 - 写多个线圈</SelectItem>
              <SelectItem value="16">16 - 写多个寄存器</SelectItem>
            </SelectContent>
          </Select>
        </div>
        <div className="space-y-2">
          <Label htmlFor="dataType">数据类型</Label>
          <Select
            value={config.dataType || 'holding_registers'}
            onValueChange={(value) =>
              onChange({ ...config, dataType: value as any })
            }>
            <SelectTrigger>
              <SelectValue />
            </SelectTrigger>
            <SelectContent>
              <SelectItem value="coils">线圈 (Coils)</SelectItem>
              <SelectItem value="discrete_inputs">
                离散输入 (Discrete Inputs)
              </SelectItem>
              <SelectItem value="holding_registers">
                保持寄存器 (Holding Registers)
              </SelectItem>
              <SelectItem value="input_registers">
                输入寄存器 (Input Registers)
              </SelectItem>
            </SelectContent>
          </Select>
        </div>
      </div>

      <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
        <div className="space-y-2">
          <Label htmlFor="startAddress">起始地址</Label>
          <Input
            id="startAddress"
            type="number"
            value={config.startAddress || ''}
            onChange={(e) =>
              onChange({ ...config, startAddress: Number(e.target.value) })
            }
            placeholder="0"
            min="0"
            max="65535"
          />
          <p className="text-xs text-muted-foreground">
            寄存器起始地址 (0-65535)
          </p>
        </div>
        <div className="space-y-2">
          <Label htmlFor="quantity">数量</Label>
          <Input
            id="quantity"
            type="number"
            value={config.quantity || ''}
            onChange={(e) =>
              onChange({ ...config, quantity: Number(e.target.value) })
            }
            placeholder="1"
            min="1"
            max="125"
          />
          <p className="text-xs text-muted-foreground">
            读取寄存器数量 (1-125)
          </p>
        </div>
      </div>

      <Alert>
        <Info className="h-4 w-4" />
        <AlertDescription>
          Modbus服务器模式将创建虚拟寄存器，允许其他设备读取转发的数据。请确保配置的地址范围不与现有设备冲突。
        </AlertDescription>
      </Alert>
    </div>
  )

  // 渲染OPC UA配置
  const renderOPCUAConfig = () => (
    <div className="space-y-6">
      {/* 服务器配置 */}
      <div className="space-y-4">
        <h4 className="text-sm font-medium">服务器配置</h4>
        <div className="space-y-2">
          <Label htmlFor="endpoint">端点URL *</Label>
          <Input
            id="endpoint"
            value={config.endpoint || ''}
            onChange={(e) => onChange({ ...config, endpoint: e.target.value })}
            placeholder="opc.tcp://localhost:4840"
          />
        </div>

        <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
          <div className="space-y-2">
            <Label htmlFor="securityMode">安全模式</Label>
            <Select
              value={config.securityMode || 'None'}
              onValueChange={(value) =>
                onChange({ ...config, securityMode: value as any })
              }>
              <SelectTrigger>
                <SelectValue />
              </SelectTrigger>
              <SelectContent>
                <SelectItem value="None">无安全 (None)</SelectItem>
                <SelectItem value="Sign">签名 (Sign)</SelectItem>
                <SelectItem value="SignAndEncrypt">
                  签名和加密 (SignAndEncrypt)
                </SelectItem>
              </SelectContent>
            </Select>
          </div>
          <div className="space-y-2">
            <Label htmlFor="securityPolicy">安全策略</Label>
            <Select
              value={config.securityPolicy || 'None'}
              onValueChange={(value) =>
                onChange({ ...config, securityPolicy: value as any })
              }>
              <SelectTrigger>
                <SelectValue />
              </SelectTrigger>
              <SelectContent>
                <SelectItem value="None">无策略 (None)</SelectItem>
                <SelectItem value="Basic128Rsa15">Basic128Rsa15</SelectItem>
                <SelectItem value="Basic256">Basic256</SelectItem>
                <SelectItem value="Basic256Sha256">Basic256Sha256</SelectItem>
              </SelectContent>
            </Select>
          </div>
        </div>
      </div>

      {/* 用户认证 */}
      <div className="space-y-4">
        <h4 className="text-sm font-medium">用户认证</h4>
        <div className="space-y-2">
          <Label htmlFor="userIdentityType">认证类型</Label>
          <Select
            value={config.userIdentity?.type || 'anonymous'}
            onValueChange={(value) =>
              onChange({
                ...config,
                userIdentity: { ...config.userIdentity, type: value as any },
              })
            }>
            <SelectTrigger>
              <SelectValue />
            </SelectTrigger>
            <SelectContent>
              <SelectItem value="anonymous">匿名访问</SelectItem>
              <SelectItem value="username">用户名密码</SelectItem>
              <SelectItem value="certificate">证书认证</SelectItem>
            </SelectContent>
          </Select>
        </div>

        {config.userIdentity?.type === 'username' && (
          <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
            <div className="space-y-2">
              <Label htmlFor="opcUsername">用户名</Label>
              <Input
                id="opcUsername"
                value={config.userIdentity?.username || ''}
                onChange={(e) =>
                  onChange({
                    ...config,
                    userIdentity: {
                      type: 'username',
                      ...config.userIdentity,
                      username: e.target.value,
                    },
                  })
                }
                placeholder="OPC UA用户名"
              />
            </div>
            <div className="space-y-2">
              <Label htmlFor="opcPassword">密码</Label>
              <Input
                id="opcPassword"
                type="password"
                value={config.userIdentity?.password || ''}
                onChange={(e) =>
                  onChange({
                    ...config,
                    userIdentity: {
                      type: 'username',
                      ...config.userIdentity,
                      password: e.target.value,
                    },
                  })
                }
                placeholder="OPC UA密码"
              />
            </div>
          </div>
        )}

        {config.userIdentity?.type === 'certificate' && (
          <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
            <div className="space-y-2">
              <Label htmlFor="certificatePath">证书路径</Label>
              <Input
                id="certificatePath"
                value={config.userIdentity?.certificatePath || ''}
                onChange={(e) =>
                  onChange({
                    ...config,
                    userIdentity: {
                      type: 'certificate',
                      ...config.userIdentity,
                      certificatePath: e.target.value,
                    },
                  })
                }
                placeholder="/path/to/certificate.pem"
              />
            </div>
            <div className="space-y-2">
              <Label htmlFor="privateKeyPath">私钥路径</Label>
              <Input
                id="privateKeyPath"
                value={config.userIdentity?.privateKeyPath || ''}
                onChange={(e) =>
                  onChange({
                    ...config,
                    userIdentity: {
                      type: 'certificate',
                      ...config.userIdentity,
                      privateKeyPath: e.target.value,
                    },
                  })
                }
                placeholder="/path/to/private-key.pem"
              />
            </div>
          </div>
        )}
      </div>
    </div>
  )

  // 渲染Siemens S7配置
  const renderS7Config = () => (
    <div className="space-y-4">
      <div className="grid grid-cols-1 md:grid-cols-3 gap-4">
        <div className="space-y-2">
          <Label htmlFor="rack">机架号</Label>
          <Input
            id="rack"
            type="number"
            value={config.rack || ''}
            onChange={(e) =>
              onChange({ ...config, rack: Number(e.target.value) })
            }
            placeholder="0"
            min="0"
            max="7"
          />
          <p className="text-xs text-muted-foreground">PLC机架号 (0-7)</p>
        </div>
        <div className="space-y-2">
          <Label htmlFor="slot">插槽号</Label>
          <Input
            id="slot"
            type="number"
            value={config.slot || ''}
            onChange={(e) =>
              onChange({ ...config, slot: Number(e.target.value) })
            }
            placeholder="1"
            min="0"
            max="31"
          />
          <p className="text-xs text-muted-foreground">CPU插槽号 (0-31)</p>
        </div>
        <div className="space-y-2">
          <Label htmlFor="plcType">PLC类型</Label>
          <Select
            value={config.plcType || 'S7-1200'}
            onValueChange={(value) =>
              onChange({ ...config, plcType: value as any })
            }>
            <SelectTrigger>
              <SelectValue />
            </SelectTrigger>
            <SelectContent>
              <SelectItem value="S7-300">S7-300</SelectItem>
              <SelectItem value="S7-400">S7-400</SelectItem>
              <SelectItem value="S7-1200">S7-1200</SelectItem>
              <SelectItem value="S7-1500">S7-1500</SelectItem>
            </SelectContent>
          </Select>
        </div>
      </div>

      <Alert>
        <Info className="h-4 w-4" />
        <AlertDescription>
          Siemens
          S7协议配置需要与PLC硬件配置匹配。机架号和插槽号必须与实际硬件配置一致。
        </AlertDescription>
      </Alert>
    </div>
  )

  // 渲染EtherNet/IP配置
  const renderEtherNetIPConfig = () => (
    <div className="space-y-4">
      <div className="grid grid-cols-1 md:grid-cols-3 gap-4">
        <div className="space-y-2">
          <Label htmlFor="path">路径</Label>
          <Input
            id="path"
            value={config.path || ''}
            onChange={(e) => onChange({ ...config, path: e.target.value })}
            placeholder="1,0"
          />
          <p className="text-xs text-muted-foreground">设备路径，例如: 1,0</p>
        </div>
        <div className="space-y-2">
          <Label htmlFor="vendorId">厂商ID</Label>
          <Input
            id="vendorId"
            type="number"
            value={config.vendorId || ''}
            onChange={(e) =>
              onChange({ ...config, vendorId: Number(e.target.value) })
            }
            placeholder="1"
          />
        </div>
        <div className="space-y-2">
          <Label htmlFor="deviceType">设备类型</Label>
          <Input
            id="deviceType"
            type="number"
            value={config.deviceType || ''}
            onChange={(e) =>
              onChange({ ...config, deviceType: Number(e.target.value) })
            }
            placeholder="12"
          />
        </div>
      </div>

      <Alert>
        <Info className="h-4 w-4" />
        <AlertDescription>
          EtherNet/IP配置用于连接支持CIP协议的工业设备。路径格式通常为"端口,节点"，如"1,0"表示端口1节点0。
        </AlertDescription>
      </Alert>
    </div>
  )

  // 渲染OPC UA订阅配置
  const renderOPCUASubscriptions = () => (
    <div className="space-y-4">
      <div className="flex justify-between items-center">
        <div>
          <h4 className="text-sm font-medium">节点订阅</h4>
          <p className="text-xs text-muted-foreground">
            配置要监控的OPC UA节点
          </p>
        </div>
        <Button size="sm" onClick={addSubscription}>
          <Plus className="h-4 w-4 mr-1" />
          添加节点
        </Button>
      </div>

      {subscriptions.length === 0 ? (
        <div className="text-center py-8 text-muted-foreground border border-dashed rounded-lg">
          <p>暂无节点订阅配置</p>
          <Button
            variant="outline"
            size="sm"
            onClick={addSubscription}
            className="mt-2">
            添加第一个节点
          </Button>
        </div>
      ) : (
        <div className="space-y-3">
          {subscriptions.map((subscription, index) => (
            <Card key={index} className="p-4">
              <div className="grid grid-cols-1 md:grid-cols-4 gap-4 items-end">
                <div className="space-y-2">
                  <Label>节点ID</Label>
                  <Input
                    value={subscription.nodeId}
                    onChange={(e) =>
                      updateSubscription(index, 'nodeId', e.target.value)
                    }
                    placeholder="ns=2;i=2"
                  />
                </div>
                <div className="space-y-2">
                  <Label>显示名称</Label>
                  <Input
                    value={subscription.displayName}
                    onChange={(e) =>
                      updateSubscription(index, 'displayName', e.target.value)
                    }
                    placeholder="温度传感器"
                  />
                </div>
                <div className="space-y-2">
                  <Label>数据类型</Label>
                  <Select
                    value={subscription.dataType}
                    onValueChange={(value) =>
                      updateSubscription(index, 'dataType', value)
                    }>
                    <SelectTrigger>
                      <SelectValue />
                    </SelectTrigger>
                    <SelectContent>
                      <SelectItem value="Boolean">Boolean</SelectItem>
                      <SelectItem value="Byte">Byte</SelectItem>
                      <SelectItem value="Int16">Int16</SelectItem>
                      <SelectItem value="Int32">Int32</SelectItem>
                      <SelectItem value="Float">Float</SelectItem>
                      <SelectItem value="Double">Double</SelectItem>
                      <SelectItem value="String">String</SelectItem>
                    </SelectContent>
                  </Select>
                </div>
                <div className="flex items-center space-x-2">
                  <Switch
                    checked={subscription.enable}
                    onCheckedChange={(checked) =>
                      updateSubscription(index, 'enable', checked)
                    }
                  />
                  <Button
                    variant="ghost"
                    size="icon"
                    onClick={() => deleteSubscription(index)}>
                    <Trash2 className="h-4 w-4" />
                  </Button>
                </div>
              </div>
              <div className="mt-3 space-y-2">
                <Label>采样间隔(ms)</Label>
                <Input
                  type="number"
                  value={subscription.samplingInterval}
                  onChange={(e) =>
                    updateSubscription(
                      index,
                      'samplingInterval',
                      Number(e.target.value)
                    )
                  }
                  placeholder="1000"
                  min="100"
                />
              </div>
            </Card>
          ))}
        </div>
      )}
    </div>
  )

  return (
    <div className="space-y-4">
      <div className="flex items-center space-x-2 mb-4">
        <Badge variant="outline" className="text-sm">
          {getProtocolDisplay(protocolType)}
        </Badge>
        <span className="text-sm text-muted-foreground">
          配置{getProtocolDisplay(protocolType)}连接参数
        </span>
      </div>

      <Tabs value={activeTab} onValueChange={setActiveTab}>
        <TabsList className="grid grid-cols-3 w-full">
          <TabsTrigger value="connection">连接配置</TabsTrigger>
          <TabsTrigger value="advanced">高级配置</TabsTrigger>
          {protocolType === 'OPCUA' && (
            <TabsTrigger value="subscriptions">节点订阅</TabsTrigger>
          )}
        </TabsList>

        <TabsContent value="connection" className="mt-4">
          <div className="space-y-6">
            {protocolType === 'ModbusServer' && renderModbusConfig()}
            {protocolType === 'OPCUA' && renderOPCUAConfig()}
            {protocolType === 'SiemensS7' && renderS7Config()}
            {protocolType === 'EthernetIP' && renderEtherNetIPConfig()}

            <div className="flex items-center justify-end pt-4 border-t">
              <Button
                variant="outline"
                onClick={handleTestConnection}
                disabled={testConnection === 'testing'}>
                <Zap className="h-4 w-4 mr-2" />
                {testConnection === 'testing' ? '测试中...' : '测试连接'}
              </Button>
            </div>

            {testConnection === 'success' && (
              <Alert className="bg-green-50 border-green-200 text-green-800">
                <AlertCircle className="h-4 w-4" />
                <AlertDescription>
                  连接测试成功！协议通信正常。
                </AlertDescription>
              </Alert>
            )}

            {testConnection === 'error' && (
              <Alert variant="destructive">
                <AlertCircle className="h-4 w-4" />
                <AlertDescription>
                  连接测试失败，请检查网络连接和配置参数。
                </AlertDescription>
              </Alert>
            )}
          </div>
        </TabsContent>

        <TabsContent value="advanced" className="mt-4">
          <div className="space-y-4">
            <h4 className="text-sm font-medium">高级配置</h4>
            <Alert>
              <Settings className="h-4 w-4" />
              <AlertDescription>
                高级配置选项将在后续版本中提供，包括数据转换脚本、自定义协议扩展等功能。
              </AlertDescription>
            </Alert>
          </div>
        </TabsContent>

        {protocolType === 'OPCUA' && (
          <TabsContent value="subscriptions" className="mt-4">
            {renderOPCUASubscriptions()}
          </TabsContent>
        )}
      </Tabs>
    </div>
  )
}

// 获取协议显示名称
function getProtocolDisplay(protocol: IndustrialProtocol): string {
  const displayNames: Record<IndustrialProtocol, string> = {
    ModbusServer: 'Modbus服务器',
    OPCUA: 'OPC UA服务器',
    SiemensS7: 'Siemens S7',
    EthernetIP: 'EtherNet/IP',
  }
  return displayNames[protocol] || protocol
}
