import { useState } from 'react'
import { <PERSON><PERSON> } from '@/components/ui/button'
import { Input } from '@/components/ui/input'
import { Label } from '@/components/ui/label'
import {
  Select,
  SelectContent,
  SelectItem,
  SelectTrigger,
  SelectValue,
} from '@/components/ui/select'
import {
  Card,
  CardContent,
  CardDescription,
  CardFooter,
  CardHeader,
  CardTitle,
} from '@/components/ui/card'
import { Textarea } from '@/components/ui/textarea'
import { Plus, Trash2, Edit, Save, Code, Info } from 'lucide-react'
import { Alert, AlertDescription } from '@/components/ui/alert'
import { InfoIcon } from 'lucide-react'
import { Ta<PERSON>, TabsContent, TabsList, TabsTrigger } from '@/components/ui/tabs'
import { Switch } from '@/components/ui/switch'
import {
  Tooltip,
  TooltipContent,
  TooltipProvider,
  TooltipTrigger,
} from '@/components/ui/tooltip'
import { Badge } from '@/components/ui/badge'
import {
  Dialog,
  Dialog<PERSON>ontent,
  <PERSON>alogDes<PERSON>,
  Dialog<PERSON>eader,
  DialogTitle,
} from '@/components/ui/dialog'

export type ChannelType = 'data' | 'status' | 'alarm' | 'command' | 'custom'

export interface WebSocketChannel {
  id: string
  name: string
  channel: string
  direction: 'send' | 'receive'
  channelType: ChannelType
  template?: string
  transformScript?: string
  enable: boolean
  description?: string
}

export interface WebSocketConfig {
  serverUrl: string
  autoReconnect: boolean
  reconnectInterval: number
  maxReconnectAttempts: number
  channels: WebSocketChannel[]
  protocols?: string[]
  headers?: Record<string, string>
}

export interface WebSocketConfigFormProps {
  config: WebSocketConfig
  onChange: (config: WebSocketConfig) => void
}

const defaultConfig: WebSocketConfig = {
  serverUrl: '',
  autoReconnect: true,
  reconnectInterval: 5000,
  maxReconnectAttempts: 10,
  channels: [],
  protocols: [],
  headers: {},
}

// 生成唯一ID
const generateId = () => `ch_${Math.random().toString(36).substring(2, 11)}`

export function WebSocketConfigForm({
  config: initialConfig,
  onChange,
}: WebSocketConfigFormProps) {
  const [config, setConfig] = useState<WebSocketConfig>({
    ...defaultConfig,
    ...initialConfig,
  })
  const [editingChannel, setEditingChannel] = useState<string | null>(null)
  const [activeTab, setActiveTab] = useState<string>('template')
  const [showScriptDialog, setShowScriptDialog] = useState(false)
  const [currentScript, setCurrentScript] = useState<{
    channelId: string
    script: string
  }>({
    channelId: '',
    script: '',
  })
  const [newChannel, setNewChannel] = useState<Omit<WebSocketChannel, 'id'>>({
    name: '',
    channel: '',
    direction: 'send',
    channelType: 'data',
    template: '',
    transformScript: '',
    enable: true,
    description: '',
  })
  const [newProtocol, setNewProtocol] = useState('')
  const [newHeader, setNewHeader] = useState({ key: '', value: '' })

  const updateConfig = <K extends keyof WebSocketConfig>(
    key: K,
    value: WebSocketConfig[K]
  ) => {
    const updatedConfig = { ...config, [key]: value }
    setConfig(updatedConfig)
    onChange(updatedConfig)
  }

  const handleAddChannel = () => {
    if (newChannel.name && newChannel.channel) {
      const channels = [
        ...config.channels,
        {
          ...newChannel,
          id: generateId(),
        },
      ]
      updateConfig('channels', channels)
      setNewChannel({
        name: '',
        channel: '',
        direction: 'send',
        channelType: 'data',
        template: '',
        transformScript: '',
        enable: true,
        description: '',
      })
    }
  }

  const handleDeleteChannel = (id: string) => {
    const channels = config.channels.filter((ch) => ch.id !== id)
    updateConfig('channels', channels)
  }

  const handleEditChannel = (channel: WebSocketChannel) => {
    setEditingChannel(channel.id)
    setActiveTab('template')
  }

  const handleSaveChannel = (
    id: string,
    updatedChannel: Partial<WebSocketChannel>
  ) => {
    const channels = config.channels.map((channel) =>
      channel.id === id ? { ...channel, ...updatedChannel } : channel
    )
    updateConfig('channels', channels)
  }

  const handleAddProtocol = () => {
    if (newProtocol && !config.protocols?.includes(newProtocol)) {
      const protocols = [...(config.protocols || []), newProtocol]
      updateConfig('protocols', protocols)
      setNewProtocol('')
    }
  }

  const handleDeleteProtocol = (protocol: string) => {
    const protocols = config.protocols?.filter((p) => p !== protocol) || []
    updateConfig('protocols', protocols)
  }

  const handleAddHeader = () => {
    if (newHeader.key && newHeader.value) {
      const headers = {
        ...(config.headers || {}),
        [newHeader.key]: newHeader.value,
      }
      updateConfig('headers', headers)
      setNewHeader({ key: '', value: '' })
    }
  }

  const handleDeleteHeader = (key: string) => {
    const headers = { ...(config.headers || {}) }
    delete headers[key]
    updateConfig('headers', headers)
  }

  const openScriptEditor = (channelId: string) => {
    const channel = config.channels.find((ch) => ch.id === channelId)
    if (channel) {
      setCurrentScript({
        channelId,
        script:
          channel.transformScript ||
          getDefaultScript(channel.channelType, channel.direction),
      })
      setShowScriptDialog(true)
    }
  }

  const saveScript = () => {
    const channels = config.channels.map((channel) =>
      channel.id === currentScript.channelId
        ? { ...channel, transformScript: currentScript.script }
        : channel
    )
    updateConfig('channels', channels)
    setShowScriptDialog(false)
  }

  // 获取默认脚本模板
  const getDefaultScript = (
    channelType: ChannelType,
    direction: 'send' | 'receive'
  ): string => {
    if (direction === 'send') {
      switch (channelType) {
        case 'data':
          return `/**
 * 数据转换脚本 - 发送模式
 * @param {Object} data - 原始数据对象
 * @param {Object} context - 上下文信息，包含设备ID、时间戳等
 * @returns {Object} - 转换后的数据对象
 */
function transform(data, context) {
  // 示例：添加时间戳和设备ID
  const transformed = {
    ...data,
    deviceId: context.deviceId,
    timestamp: context.timestamp
  };
  
  // 示例：数据格式转换
  if (data.values && Array.isArray(data.values)) {
    transformed.dataPoints = data.values.map(v => ({
      tag: v.name,
      value: v.value,
      quality: v.quality || 'GOOD'
    }));
    delete transformed.values;
  }
  
  return transformed;
}`
        case 'status':
          return `/**
 * 状态数据转换脚本 - 发送模式
 * @param {Object} status - 原始状态对象
 * @param {Object} context - 上下文信息
 * @returns {Object} - 转换后的状态对象
 */
function transform(status, context) {
  return {
    deviceId: context.deviceId,
    status: status.connected ? "online" : "offline",
    timestamp: context.timestamp,
    details: {
      ip: status.ip,
      lastSeen: status.lastSeen,
      signalStrength: status.signalStrength
    }
  };
}`
        case 'alarm':
          return `/**
 * 告警数据转换脚本 - 发送模式
 * @param {Object} alarm - 原始告警对象
 * @param {Object} context - 上下文信息
 * @returns {Object} - 转换后的告警对象
 */
function transform(alarm, context) {
  return {
    id: alarm.id || context.generateId(),
    deviceId: context.deviceId,
    severity: mapSeverity(alarm.severity),
    message: alarm.message,
    timestamp: context.timestamp,
    source: alarm.source || "device",
    acknowledged: false
  };
}

function mapSeverity(severity) {
  const map = {
    1: "critical",
    2: "major",
    3: "minor",
    4: "warning",
    5: "info"
  };
  return map[severity] || "warning";
}`
        default:
          return `/**
 * 自定义数据转换脚本 - 发送模式
 * @param {Object} data - 原始数据对象
 * @param {Object} context - 上下文信息
 * @returns {Object} - 转换后的数据对象
 */
function transform(data, context) {
  // 在此处添加您的转换逻辑
  return {
    ...data,
    processedAt: context.timestamp,
    source: context.deviceId
  };
}`
      }
    } else {
      // 接收模式
      switch (channelType) {
        case 'command':
          return `/**
 * 命令数据转换脚本 - 接收模式
 * @param {Object} message - 接收到的消息对象
 * @param {Object} context - 上下文信息
 * @returns {Object} - 转换后的命令对象
 */
function transform(message, context) {
  try {
    // 解析接收到的消息
    const command = typeof message === 'string' ? JSON.parse(message) : message;
    
    // 验证命令格式
    if (!command.action) {
      throw new Error('Invalid command format: missing action');
    }
    
    // 转换为标准命令格式
    return {
      commandId: command.id || command.commandId || context.generateId(),
      deviceId: command.deviceId || context.deviceId,
      action: command.action,
      parameters: command.parameters || command.params || {},
      timestamp: command.timestamp || context.timestamp,
      source: 'websocket'
    };
  } catch (error) {
    // 处理错误
    context.logError('Command parsing error: ' + error.message);
    return null; // 返回null表示丢弃此消息
  }
}`
        default:
          return `/**
 * 数据转换脚本 - 接收模式
 * @param {Object|string} message - 接收到的消息
 * @param {Object} context - 上下文信息
 * @returns {Object} - 转换后的数据对象，返回null表示丢弃此消息
 */
function transform(message, context) {
  try {
    // 如果消息是字符串，尝试解析为JSON
    const data = typeof message === 'string' ? JSON.parse(message) : message;
    
    // 在此处添加您的转换逻辑
    return {
      ...data,
      receivedAt: context.timestamp,
      processed: true
    };
  } catch (error) {
    context.logError('Message parsing error: ' + error.message);
    return null; // 返回null表示丢弃此消息
  }
}`
      }
    }
  }

  // 快速添加常用通道模板
  const channelTemplates = [
    {
      name: '设备数据通道',
      channel: 'device.data',
      direction: 'send' as const,
      channelType: 'data' as ChannelType,
      template:
        '{\n  "deviceId": "{{deviceId}}",\n  "timestamp": "{{timestamp}}",\n  "values": {{values}}\n}',
      description: '用于发送设备的实时数据',
    },
    {
      name: '设备状态通道',
      channel: 'device.status',
      direction: 'send' as const,
      channelType: 'status' as ChannelType,
      template:
        '{\n  "deviceId": "{{deviceId}}",\n  "status": "{{status}}",\n  "timestamp": "{{timestamp}}"\n}',
      description: '用于发送设备的状态信息',
    },
    {
      name: '设备告警通道',
      channel: 'device.alarm',
      direction: 'send' as const,
      channelType: 'alarm' as ChannelType,
      template:
        '{\n  "deviceId": "{{deviceId}}",\n  "alarmType": "{{alarmType}}",\n  "severity": {{severity}},\n  "message": "{{message}}",\n  "timestamp": "{{timestamp}}"\n}',
      description: '用于发送设备告警信息',
    },
    {
      name: '命令接收通道',
      channel: 'device.command',
      direction: 'receive' as const,
      channelType: 'command' as ChannelType,
      description: '用于接收发送给设备的命令',
    },
  ]

  const applyChannelTemplate = (template: (typeof channelTemplates)[0]) => {
    setNewChannel({
      name: template.name,
      channel: template.channel,
      direction: template.direction,
      channelType: template.channelType,
      template: template.template,
      transformScript: getDefaultScript(
        template.channelType,
        template.direction
      ),
      enable: true,
      description: template.description,
    })
  }

  // 获取通道类型的显示名称和颜色
  const getChannelTypeInfo = (type: ChannelType) => {
    switch (type) {
      case 'data':
        return {
          name: '数据',
          color: 'bg-blue-100 text-blue-800 hover:bg-blue-200',
        }
      case 'status':
        return {
          name: '状态',
          color: 'bg-green-100 text-green-800 hover:bg-green-200',
        }
      case 'alarm':
        return {
          name: '告警',
          color: 'bg-red-100 text-red-800 hover:bg-red-200',
        }
      case 'command':
        return {
          name: '命令',
          color: 'bg-purple-100 text-purple-800 hover:bg-purple-200',
        }
      default:
        return {
          name: '自定义',
          color: 'bg-gray-100 text-gray-800 hover:bg-gray-200',
        }
    }
  }

  return (
    <div className="space-y-6">
      <div className="space-y-4">
        <div className="flex justify-between items-center">
          <h3 className="text-lg font-medium">WebSocket通道配置</h3>
          <Button
            variant="outline"
            size="sm"
            onClick={() => handleAddChannel()}>
            <Plus className="h-4 w-4 mr-2" />
            添加通道
          </Button>
        </div>

        <Alert
          variant="default"
          className="bg-blue-50 text-blue-800 border-blue-200">
          <InfoIcon className="h-4 w-4" />
          <AlertDescription>
            <span className="font-medium">快速添加：</span>{' '}
            点击下方模板快速添加常用WebSocket通道配置
          </AlertDescription>
        </Alert>

        <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-2">
          {channelTemplates.map((template, index) => (
            <Button
              key={index}
              variant="outline"
              className="justify-start h-auto py-2 px-3"
              onClick={() => applyChannelTemplate(template)}>
              <div className="text-left">
                <div className="font-medium">{template.name}</div>
                <div className="text-xs text-muted-foreground truncate">
                  {template.direction === 'send' ? '发送' : '接收'}:{' '}
                  {template.channel}
                </div>
              </div>
            </Button>
          ))}
        </div>

        <Card>
          <CardHeader>
            <CardTitle>添加新通道</CardTitle>
            <CardDescription>配置WebSocket通信通道</CardDescription>
          </CardHeader>
          <CardContent>
            <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
              <div className="space-y-2">
                <Label htmlFor="channelName">通道名称</Label>
                <Input
                  id="channelName"
                  value={newChannel.name}
                  onChange={(e) =>
                    setNewChannel({ ...newChannel, name: e.target.value })
                  }
                  placeholder="例如: 实时数据"
                />
              </div>

              <div className="space-y-2">
                <Label htmlFor="channelPath">通道标识</Label>
                <Input
                  id="channelPath"
                  value={newChannel.channel}
                  onChange={(e) =>
                    setNewChannel({ ...newChannel, channel: e.target.value })
                  }
                  placeholder="例如: data.realtime"
                />
              </div>

              <div className="space-y-2">
                <Label htmlFor="channelDirection">方向</Label>
                <Select
                  value={newChannel.direction}
                  onValueChange={(value) =>
                    setNewChannel({
                      ...newChannel,
                      direction: value as 'send' | 'receive',
                    })
                  }>
                  <SelectTrigger>
                    <SelectValue placeholder="选择方向" />
                  </SelectTrigger>
                  <SelectContent>
                    <SelectItem value="send">发送</SelectItem>
                    <SelectItem value="receive">接收</SelectItem>
                  </SelectContent>
                </Select>
              </div>

              <div className="space-y-2">
                <Label htmlFor="channelType">通道类型</Label>
                <Select
                  value={newChannel.channelType}
                  onValueChange={(value) =>
                    setNewChannel({
                      ...newChannel,
                      channelType: value as ChannelType,
                    })
                  }>
                  <SelectTrigger>
                    <SelectValue placeholder="选择通道类型" />
                  </SelectTrigger>
                  <SelectContent>
                    <SelectItem value="data">数据</SelectItem>
                    <SelectItem value="status">状态</SelectItem>
                    <SelectItem value="alarm">告警</SelectItem>
                    <SelectItem value="command">命令</SelectItem>
                    <SelectItem value="custom">自定义</SelectItem>
                  </SelectContent>
                </Select>
              </div>

              <div className="space-y-2 md:col-span-2">
                <Label htmlFor="channelDescription">描述</Label>
                <Input
                  id="channelDescription"
                  value={newChannel.description || ''}
                  onChange={(e) =>
                    setNewChannel({
                      ...newChannel,
                      description: e.target.value,
                    })
                  }
                  placeholder="通道用途描述"
                />
              </div>

              {newChannel.direction === 'send' && (
                <div className="space-y-2 md:col-span-2">
                  <Label htmlFor="channelTemplate">数据模板 (JSON)</Label>
                  <Textarea
                    id="channelTemplate"
                    value={newChannel.template || ''}
                    onChange={(e) =>
                      setNewChannel({ ...newChannel, template: e.target.value })
                    }
                    placeholder='{"deviceId":"{{deviceId}}","value":"{{value}}","timestamp":"{{timestamp}}"}'
                    className="font-mono text-sm h-24"
                  />
                  <p className="text-xs text-muted-foreground">
                    使用 {'{{变量名}}'} 作为占位符，例如 {'{{deviceId}}'},{' '}
                    {'{{value}}'}, {'{{timestamp}}'}
                  </p>
                </div>
              )}
            </div>
          </CardContent>
          <CardFooter>
            <Button
              onClick={handleAddChannel}
              disabled={!newChannel.name || !newChannel.channel}>
              添加通道
            </Button>
          </CardFooter>
        </Card>

        <div className="space-y-4">
          <h4 className="font-medium">已配置通道</h4>

          {config.channels.length === 0 ? (
            <p className="text-muted-foreground">暂无配置的通道</p>
          ) : (
            <div className="space-y-4">
              {config.channels.map((channel) => (
                <Card
                  key={channel.id}
                  className={!channel.enable ? 'opacity-60' : ''}>
                  <CardHeader className="pb-2">
                    <div className="flex justify-between items-center">
                      <div className="flex items-center space-x-2">
                        <CardTitle className="text-base">
                          {channel.name}
                        </CardTitle>
                        <Badge
                          variant="outline"
                          className={
                            getChannelTypeInfo(channel.channelType).color
                          }>
                          {getChannelTypeInfo(channel.channelType).name}
                        </Badge>
                        <Badge variant="secondary">
                          {channel.direction === 'send' ? '发送' : '接收'}
                        </Badge>
                      </div>
                      <div className="flex space-x-2">
                        <TooltipProvider>
                          <Tooltip>
                            <TooltipTrigger asChild>
                              <Button
                                variant="ghost"
                                size="icon"
                                onClick={() => openScriptEditor(channel.id)}>
                                <Code className="h-4 w-4" />
                              </Button>
                            </TooltipTrigger>
                            <TooltipContent>
                              <p>编辑转换脚本</p>
                            </TooltipContent>
                          </Tooltip>
                        </TooltipProvider>
                        <TooltipProvider>
                          <Tooltip>
                            <TooltipTrigger asChild>
                              <Button
                                variant="ghost"
                                size="icon"
                                onClick={() => handleEditChannel(channel)}>
                                <Edit className="h-4 w-4" />
                              </Button>
                            </TooltipTrigger>
                            <TooltipContent>
                              <p>编辑通道</p>
                            </TooltipContent>
                          </Tooltip>
                        </TooltipProvider>
                        <TooltipProvider>
                          <Tooltip>
                            <TooltipTrigger asChild>
                              <Button
                                variant="ghost"
                                size="icon"
                                onClick={() => handleDeleteChannel(channel.id)}>
                                <Trash2 className="h-4 w-4" />
                              </Button>
                            </TooltipTrigger>
                            <TooltipContent>
                              <p>删除通道</p>
                            </TooltipContent>
                          </Tooltip>
                        </TooltipProvider>
                      </div>
                    </div>
                    <CardDescription>
                      <div className="flex items-center space-x-2">
                        <span className="truncate">
                          通道: {channel.channel}
                        </span>
                      </div>
                      {channel.description && (
                        <p className="text-xs mt-1 text-muted-foreground">
                          {channel.description}
                        </p>
                      )}
                    </CardDescription>
                  </CardHeader>

                  {editingChannel === channel.id ? (
                    <CardContent>
                      <Tabs value={activeTab} onValueChange={setActiveTab}>
                        <TabsList className="mb-4">
                          {channel.direction === 'send' && (
                            <TabsTrigger value="template">数据模板</TabsTrigger>
                          )}
                          <TabsTrigger value="settings">设置</TabsTrigger>
                        </TabsList>

                        {channel.direction === 'send' && (
                          <TabsContent value="template" className="space-y-4">
                            <div className="space-y-2">
                              <Label htmlFor={`edit-template-${channel.id}`}>
                                数据模板
                              </Label>
                              <Textarea
                                id={`edit-template-${channel.id}`}
                                defaultValue={channel.template}
                                className="font-mono text-sm h-24"
                                onChange={(e) => {
                                  handleSaveChannel(channel.id, {
                                    template: e.target.value,
                                  })
                                }}
                              />
                              <div className="flex justify-between items-center">
                                <p className="text-xs text-muted-foreground">
                                  使用 {'{{变量名}}'} 作为占位符
                                </p>
                                <Button
                                  variant="outline"
                                  size="sm"
                                  onClick={() => openScriptEditor(channel.id)}
                                  className="text-xs">
                                  <Code className="h-3 w-3 mr-1" />
                                  编辑转换脚本
                                </Button>
                              </div>
                            </div>
                          </TabsContent>
                        )}

                        <TabsContent value="settings" className="space-y-4">
                          <div className="grid grid-cols-2 gap-4">
                            <div className="space-y-2">
                              <Label htmlFor={`edit-name-${channel.id}`}>
                                通道名称
                              </Label>
                              <Input
                                id={`edit-name-${channel.id}`}
                                defaultValue={channel.name}
                                onChange={(e) => {
                                  handleSaveChannel(channel.id, {
                                    name: e.target.value,
                                  })
                                }}
                              />
                            </div>

                            <div className="space-y-2">
                              <Label htmlFor={`edit-channel-${channel.id}`}>
                                通道标识
                              </Label>
                              <Input
                                id={`edit-channel-${channel.id}`}
                                defaultValue={channel.channel}
                                onChange={(e) => {
                                  handleSaveChannel(channel.id, {
                                    channel: e.target.value,
                                  })
                                }}
                              />
                            </div>

                            <div className="space-y-2">
                              <Label htmlFor={`edit-direction-${channel.id}`}>
                                方向
                              </Label>
                              <Select
                                defaultValue={channel.direction}
                                onValueChange={(value) =>
                                  handleSaveChannel(channel.id, {
                                    direction: value as 'send' | 'receive',
                                  })
                                }>
                                <SelectTrigger
                                  id={`edit-direction-${channel.id}`}>
                                  <SelectValue placeholder="选择方向" />
                                </SelectTrigger>
                                <SelectContent>
                                  <SelectItem value="send">发送</SelectItem>
                                  <SelectItem value="receive">接收</SelectItem>
                                </SelectContent>
                              </Select>
                            </div>

                            <div className="space-y-2">
                              <Label htmlFor={`edit-type-${channel.id}`}>
                                通道类型
                              </Label>
                              <Select
                                defaultValue={channel.channelType}
                                onValueChange={(value) =>
                                  handleSaveChannel(channel.id, {
                                    channelType: value as ChannelType,
                                  })
                                }>
                                <SelectTrigger id={`edit-type-${channel.id}`}>
                                  <SelectValue placeholder="选择通道类型" />
                                </SelectTrigger>
                                <SelectContent>
                                  <SelectItem value="data">数据</SelectItem>
                                  <SelectItem value="status">状态</SelectItem>
                                  <SelectItem value="alarm">告警</SelectItem>
                                  <SelectItem value="command">命令</SelectItem>
                                  <SelectItem value="custom">自定义</SelectItem>
                                </SelectContent>
                              </Select>
                            </div>

                            <div className="space-y-2 col-span-2">
                              <Label htmlFor={`edit-description-${channel.id}`}>
                                描述
                              </Label>
                              <Input
                                id={`edit-description-${channel.id}`}
                                defaultValue={channel.description}
                                onChange={(e) => {
                                  handleSaveChannel(channel.id, {
                                    description: e.target.value,
                                  })
                                }}
                              />
                            </div>

                            <div className="col-span-2 flex items-center space-x-2">
                              <Switch
                                id={`edit-enable-${channel.id}`}
                                checked={channel.enable}
                                onCheckedChange={(checked) => {
                                  handleSaveChannel(channel.id, {
                                    enable: checked,
                                  })
                                }}
                              />
                              <Label htmlFor={`edit-enable-${channel.id}`}>
                                启用此通道
                              </Label>
                            </div>
                          </div>

                          <Button onClick={() => setEditingChannel(null)}>
                            <Save className="h-4 w-4 mr-2" />
                            完成编辑
                          </Button>
                        </TabsContent>
                      </Tabs>
                    </CardContent>
                  ) : (
                    channel.direction === 'send' &&
                    channel.template && (
                      <CardContent className="pt-0">
                        <div className="bg-muted p-2 rounded-md">
                          <pre className="text-xs font-mono overflow-x-auto whitespace-pre-wrap">
                            {channel.template}
                          </pre>
                        </div>

                        {channel.transformScript && (
                          <div className="mt-2 flex items-center text-xs text-muted-foreground">
                            <Code className="h-3 w-3 mr-1" />
                            <span>已配置数据转换脚本</span>
                          </div>
                        )}
                      </CardContent>
                    )
                  )}
                </Card>
              ))}
            </div>
          )}
        </div>
      </div>

      {/* 脚本编辑对话框 */}
      <Dialog open={showScriptDialog} onOpenChange={setShowScriptDialog}>
        <DialogContent className="max-w-4xl max-h-[80vh]">
          <DialogHeader>
            <DialogTitle>编辑数据转换脚本</DialogTitle>
            <DialogDescription>
              编写JavaScript脚本来转换数据格式，脚本必须包含一个transform函数
            </DialogDescription>
          </DialogHeader>

          <div className="space-y-4">
            <div className="bg-muted p-2 rounded-md">
              <Alert
                variant="default"
                className="bg-blue-50 text-blue-800 border-blue-200 mb-2">
                <Info className="h-4 w-4" />
                <AlertDescription>
                  <p className="font-medium">脚本说明：</p>
                  <ul className="list-disc pl-5 text-xs space-y-1">
                    <li>
                      脚本必须包含一个名为 <code>transform</code>{' '}
                      的函数，接收两个参数：data 和 context
                    </li>
                    <li>
                      data 参数包含原始数据，context
                      包含设备ID、时间戳等上下文信息
                    </li>
                    <li>函数必须返回一个对象，该对象将作为消息的主体</li>
                    <li>可以使用 context.generateId() 生成唯一ID</li>
                    <li>
                      可以使用 context.getExpirationTime(seconds) 获取过期时间戳
                    </li>
                    <li>接收模式下，返回 null 表示丢弃此消息</li>
                  </ul>
                </AlertDescription>
              </Alert>

              <Textarea
                value={currentScript.script}
                onChange={(e) =>
                  setCurrentScript({ ...currentScript, script: e.target.value })
                }
                className="font-mono text-sm h-[400px]"
              />
            </div>

            <div className="flex justify-end space-x-2">
              <Button
                variant="outline"
                onClick={() => setShowScriptDialog(false)}>
                取消
              </Button>
              <Button onClick={saveScript}>保存脚本</Button>
            </div>
          </div>
        </DialogContent>
      </Dialog>
    </div>
  )
}
