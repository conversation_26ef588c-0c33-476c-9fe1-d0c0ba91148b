import { useState, useEffect } from 'react'
import { Input } from '@/components/ui/input'
import { Label } from '@/components/ui/label'
import { Switch } from '@/components/ui/switch'
import {
  Select,
  SelectContent,
  SelectItem,
  SelectTrigger,
  SelectValue,
} from '@/components/ui/select'
import { Card, CardContent } from '@/components/ui/card'
import { Tabs, TabsContent, TabsList, TabsTrigger } from '@/components/ui/tabs'
import { Alert, AlertDescription } from '@/components/ui/alert'
import {
  InfoIcon,
  Clock,
  Calendar,
  AlertTriangle,
  CheckCircle2,
} from 'lucide-react'
import type {
  ForwardMode,
  OfflineStorageConfig,
  RetryConfig,
  StorageMode,
} from '../types'

interface AdvancedConfigProps {
  config: {
    offlineStorageConfig: OfflineStorageConfig
    retryConfig: RetryConfig
    forwardMode: ForwardMode
  }
  onChange: (config: {
    offlineStorageConfig: OfflineStorageConfig
    retryConfig: RetryConfig
    forwardMode: ForwardMode
  }) => void
  renderProtocolConfig?: () => React.ReactNode
}

// 简化的间隔配置
interface IntervalConfig {
  value: number
  unit: 'second' | 'minute' | 'hour' | 'day'
  startTime?: string
}

export function AdvancedConfigForm({ config, onChange, renderProtocolConfig }: AdvancedConfigProps) {
  const [activeTab, setActiveTab] = useState('forwarding')
  const [localConfig, setLocalConfig] = useState(config)

  // 间隔配置
  const [intervalConfig, setIntervalConfig] = useState<IntervalConfig>({
    value: 30,
    unit: 'minute',
    startTime: '08:00',
  })

  // 只在组件挂载和config变化时更新本地状态，避免循环更新
  useEffect(() => {
    // 使用深度比较来避免不必要的更新
    if (JSON.stringify(localConfig) !== JSON.stringify(config)) {
      setLocalConfig(config)
    }
  }, [JSON.stringify(config)]) // eslint-disable-line react-hooks/exhaustive-deps

  // 处理本地配置更改
  const handleConfigChange = (newConfig: typeof localConfig) => {
    setLocalConfig(newConfig)
    onChange(newConfig)
  }

  const updateConfig = <K extends keyof typeof localConfig>(
    key: K,
    value: (typeof localConfig)[K]
  ) => {
    const newConfig = { ...localConfig, [key]: value }
    handleConfigChange(newConfig)
  }

  const updateOfflineStorage = <K extends keyof OfflineStorageConfig>(
    key: K,
    value: OfflineStorageConfig[K]
  ) => {
    const newOfflineStorageConfig = {
      ...localConfig.offlineStorageConfig,
      [key]: value,
    }
    handleConfigChange({
      ...localConfig,
      offlineStorageConfig: newOfflineStorageConfig,
    })
  }

  const updateRetryConfig = <K extends keyof RetryConfig>(
    key: K,
    value: RetryConfig[K]
  ) => {
    const newRetryConfig = { ...localConfig.retryConfig, [key]: value }
    handleConfigChange({ ...localConfig, retryConfig: newRetryConfig })
  }

  // 更新间隔配置
  const updateIntervalConfig = <K extends keyof IntervalConfig>(
    key: K,
    value: IntervalConfig[K]
  ) => {
    setIntervalConfig((prev) => ({ ...prev, [key]: value }))
  }

  // 生成下次执行时间预览
  const generateNextExecutionTimes = () => {
    const now = new Date()
    const nextTimes: Date[] = []

    // 如果设置了开始时间，则从今天的开始时间开始计算
    if (intervalConfig.startTime) {
      const [hours, minutes] = intervalConfig.startTime.split(':').map(Number)
      const startTime = new Date(now)
      startTime.setHours(hours, minutes, 0, 0)

      // 如果开始时间已经过去，则从当前时间开始计算下一个执行时间
      if (startTime < now) {
        const intervalMs = getIntervalInMilliseconds()
        const currentTime = now.getTime()
        const startTimeValue = startTime.getTime()
        const elapsedIntervals = Math.ceil(
          (currentTime - startTimeValue) / intervalMs
        )
        startTime.setTime(startTimeValue + elapsedIntervals * intervalMs)
      }

      nextTimes.push(new Date(startTime))

      // 计算后续执行时间
      for (let i = 1; i < 5; i++) {
        const nextTime = new Date(nextTimes[0])

        if (intervalConfig.unit === 'second') {
          nextTime.setSeconds(nextTime.getSeconds() + intervalConfig.value * i)
        } else if (intervalConfig.unit === 'minute') {
          nextTime.setMinutes(nextTime.getMinutes() + intervalConfig.value * i)
        } else if (intervalConfig.unit === 'hour') {
          nextTime.setHours(nextTime.getHours() + intervalConfig.value * i)
        } else if (intervalConfig.unit === 'day') {
          nextTime.setDate(nextTime.getDate() + intervalConfig.value * i)
        }

        nextTimes.push(nextTime)
      }
    }

    return nextTimes.slice(0, 3) // 只返回前3个时间点
  }

  // 获取间隔的毫秒数
  const getIntervalInMilliseconds = () => {
    switch (intervalConfig.unit) {
      case 'second':
        return intervalConfig.value * 1000
      case 'minute':
        return intervalConfig.value * 60 * 1000
      case 'hour':
        return intervalConfig.value * 60 * 60 * 1000
      case 'day':
        return intervalConfig.value * 24 * 60 * 60 * 1000
      default:
        return intervalConfig.value * 60 * 1000
    }
  }

  // 格式化日期时间
  const formatDateTime = (date: Date) => {
    return date.toLocaleString('zh-CN', {
      year: 'numeric',
      month: '2-digit',
      day: '2-digit',
      hour: '2-digit',
      minute: '2-digit',
      second: '2-digit',
      hour12: false,
    })
  }

  // 获取间隔的���好描述
  const getIntervalDescription = () => {
    const unitText = {
      second: '秒',
      minute: '分钟',
      hour: '小时',
      day: '天',
    }

    return `每 ${intervalConfig.value} ${unitText[intervalConfig.unit]}`
  }

  // 根据转发模式显示不同的配置选项
  const renderForwardModeSpecificConfig = () => {
    switch (localConfig.forwardMode) {
      case 'batch':
        return (
          <div className="mt-4 space-y-4">
            <h4 className="font-medium">批量转发设置</h4>
            <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
              <div className="space-y-2">
                <Label htmlFor="batchSize">批次大小</Label>
                <div className="flex items-center space-x-2">
                  <Input
                    id="batchSize"
                    type="number"
                    value={localConfig.offlineStorageConfig.batchSize}
                    onChange={(e) =>
                      updateOfflineStorage('batchSize', Number(e.target.value))
                    }
                    placeholder="例如: 100"
                  />
                  <span className="text-sm text-muted-foreground">条</span>
                </div>
              </div>
              <div className="space-y-2">
                <Label htmlFor="batchInterval">批次间隔</Label>
                <div className="flex items-center space-x-2">
                  <Input
                    id="batchInterval"
                    type="number"
                    value={localConfig.retryConfig.retryInterval}
                    onChange={(e) =>
                      updateRetryConfig('retryInterval', Number(e.target.value))
                    }
                    placeholder="例如: 5000"
                  />
                  <span className="text-sm text-muted-foreground">毫秒</span>
                </div>
              </div>
            </div>
            <Alert
              variant="default"
              className="bg-blue-50 text-blue-800 border-blue-200">
              <InfoIcon className="h-4 w-4" />
              <AlertDescription>
                批量转发可以减少网络请求次数，适合大量数据场景。建议根据数据量和网络情况调整批次大小和间隔。
              </AlertDescription>
            </Alert>
          </div>
        )
      case 'scheduled':
        return (
          <div className="mt-4 space-y-4">
            <h4 className="font-medium">定时转发设置</h4>

            <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
              <div className="space-y-2">
                <Label htmlFor="intervalValue">间隔数值</Label>
                <Input
                  id="intervalValue"
                  type="number"
                  min={1}
                  value={intervalConfig.value}
                  onChange={(e) =>
                    updateIntervalConfig('value', Number(e.target.value))
                  }
                />
              </div>

              <div className="space-y-2">
                <Label htmlFor="intervalUnit">间隔单位</Label>
                <Select
                  value={intervalConfig.unit}
                  onValueChange={(value) =>
                    updateIntervalConfig(
                      'unit',
                      value as IntervalConfig['unit']
                    )
                  }>
                  <SelectTrigger id="intervalUnit">
                    <SelectValue placeholder="选择间隔单位" />
                  </SelectTrigger>
                  <SelectContent>
                    <SelectItem value="second">秒</SelectItem>
                    <SelectItem value="minute">分钟</SelectItem>
                    <SelectItem value="hour">小时</SelectItem>
                    <SelectItem value="day">天</SelectItem>
                  </SelectContent>
                </Select>
              </div>
            </div>

            <div className="space-y-2">
              <Label htmlFor="startTime">开始时间</Label>
              <Input
                id="startTime"
                type="time"
                value={intervalConfig.startTime}
                onChange={(e) =>
                  updateIntervalConfig('startTime', e.target.value)
                }
              />
              <p className="text-sm text-muted-foreground">
                设置转发任务的开始时间，系统将从该时间点开始按照设定的间隔执行转发
              </p>
            </div>

            <div className="p-3 bg-muted rounded-md">
              <div className="flex items-center mb-2">
                <Calendar className="h-4 w-4 mr-2" />
                <span className="text-sm font-medium">
                  转发计划: {getIntervalDescription()}
                </span>
              </div>
              <div className="text-sm text-muted-foreground">
                {generateNextExecutionTimes().map((time, index) => (
                  <div key={index} className="flex items-center space-x-2 mb-1">
                    <CheckCircle2 className="h-3 w-3 text-green-500" />
                    <span>{formatDateTime(time)}</span>
                  </div>
                ))}
              </div>
            </div>

            <Alert
              variant="default"
              className="bg-blue-50 text-blue-800 border-blue-200">
              <InfoIcon className="h-4 w-4" />
              <AlertDescription>
                定时转发适合需要定期汇总发送数据的场景。您可以设置从指定时间开始，每隔一段时间执行一次转发任务。
              </AlertDescription>
            </Alert>
          </div>
        )
      case 'event':
        return (
          <div className="mt-4 space-y-4">
            <h4 className="font-medium">事件转发设置</h4>
            <div className="space-y-2">
              <Label htmlFor="eventType">触发事件</Label>
              <Select defaultValue="tag_change">
                <SelectTrigger id="eventType">
                  <SelectValue placeholder="选择触发事件" />
                </SelectTrigger>
                <SelectContent>
                  <SelectItem value="tag_change">标签值变化</SelectItem>
                  <SelectItem value="alarm">报警触发</SelectItem>
                  <SelectItem value="device_online">设备上线</SelectItem>
                  <SelectItem value="device_offline">设备离线</SelectItem>
                  <SelectItem value="custom">自定义事件</SelectItem>
                </SelectContent>
              </Select>
            </div>
            <div className="space-y-2">
              <Label htmlFor="eventCondition">事件条件</Label>
              <Input id="eventCondition" placeholder="例如: value > 100" />
            </div>
            <Alert
              variant="default"
              className="bg-blue-50 text-blue-800 border-blue-200">
              <InfoIcon className="h-4 w-4" />
              <AlertDescription>
                事件转发适合需要在特定条件满足时发送数据的场景，如报警触发、设备状态变化等。
              </AlertDescription>
            </Alert>
          </div>
        )
      case 'realtime':
      default:
        return (
          <div className="mt-4 space-y-4">
            <h4 className="font-medium">实时转发设置</h4>
            <div className="space-y-2">
              <Label htmlFor="bufferSize">缓冲区大小</Label>
              <div className="flex items-center space-x-2">
                <Input
                  id="bufferSize"
                  type="number"
                  defaultValue={1000}
                  placeholder="例如: 1000"
                />
                <span className="text-sm text-muted-foreground">条</span>
              </div>
            </div>
            <div className="space-y-2">
              <Label htmlFor="maxDelay">最大延迟</Label>
              <div className="flex items-center space-x-2">
                <Input
                  id="maxDelay"
                  type="number"
                  defaultValue={100}
                  placeholder="例如: 100"
                />
                <span className="text-sm text-muted-foreground">毫秒</span>
              </div>
            </div>
            <Alert
              variant="default"
              className="bg-blue-50 text-blue-800 border-blue-200">
              <InfoIcon className="h-4 w-4" />
              <AlertDescription>
                实时转发适合对实时性要求高的场景，数据产生后会立即发送。缓冲区可以在短时网络波动时保持数据完整性。
              </AlertDescription>
            </Alert>
          </div>
        )
    }
  }

  return (
    <div className="space-y-6">
      <Tabs value={activeTab} onValueChange={setActiveTab} className="w-full">
        <TabsList className="grid grid-cols-4 mb-4">
          <TabsTrigger value="forwarding">转发方式</TabsTrigger>
          <TabsTrigger value="topics">转发主题</TabsTrigger>
          <TabsTrigger value="retry">重试策略</TabsTrigger>
          <TabsTrigger value="storage">离线存储</TabsTrigger>
        </TabsList>

        <TabsContent value="forwarding" className="space-y-4">
          <div className="space-y-2">
            <Label>转发方式</Label>
            <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-4">
              <Card
                className={`cursor-pointer transition-all ${
                  localConfig.forwardMode === 'realtime'
                    ? 'ring-2 ring-primary'
                    : 'hover:bg-muted/50'
                }`}
                onClick={() => updateConfig('forwardMode', 'realtime')}>
                <CardContent className="p-4 text-center">
                  <h4 className="font-medium mb-2">实时转发</h4>
                  <p className="text-xs text-muted-foreground">
                    设备数据产生后立即转发
                  </p>
                </CardContent>
              </Card>

              <Card
                className={`cursor-pointer transition-all ${
                  localConfig.forwardMode === 'batch'
                    ? 'ring-2 ring-primary'
                    : 'hover:bg-muted/50'
                }`}
                onClick={() => updateConfig('forwardMode', 'batch')}>
                <CardContent className="p-4 text-center">
                  <h4 className="font-medium mb-2">批量转发</h4>
                  <p className="text-xs text-muted-foreground">
                    按照指定时间间隔批量转发数据
                  </p>
                </CardContent>
              </Card>

              <Card
                className={`cursor-pointer transition-all ${
                  localConfig.forwardMode === 'scheduled'
                    ? 'ring-2 ring-primary'
                    : 'hover:bg-muted/50'
                }`}
                onClick={() => updateConfig('forwardMode', 'scheduled')}>
                <CardContent className="p-4 text-center">
                  <h4 className="font-medium mb-2">定时转发</h4>
                  <p className="text-xs text-muted-foreground">
                    按照固定时间间隔进行转发
                  </p>
                </CardContent>
              </Card>

              <Card
                className={`cursor-pointer transition-all ${
                  localConfig.forwardMode === 'event'
                    ? 'ring-2 ring-primary'
                    : 'hover:bg-muted/50'
                }`}
                onClick={() => updateConfig('forwardMode', 'event')}>
                <CardContent className="p-4 text-center">
                  <h4 className="font-medium mb-2">事件转发</h4>
                  <p className="text-xs text-muted-foreground">
                    在指定事件触发时进行转发
                  </p>
                </CardContent>
              </Card>
            </div>
          </div>

          {renderForwardModeSpecificConfig()}

          <Alert
            variant="default"
            className="bg-blue-50 text-blue-800 border-blue-200">
            <InfoIcon className="h-4 w-4" />
            <AlertDescription>
              <span className="font-medium">提示：</span>{' '}
              实时转发适合对实时性要求高的场景，批量转发适合需要节省流量或处理大量数据的场景。
            </AlertDescription>
          </Alert>
        </TabsContent>

        <TabsContent value="topics" className="space-y-4">
          {renderProtocolConfig ? renderProtocolConfig() : (
            <div className="text-center text-muted-foreground py-8">
              转发主题配置功能暂未提供
            </div>
          )}
        </TabsContent>

        <TabsContent value="retry" className="space-y-4">
          <div className="flex items-center space-x-2 mb-4">
            <Switch
              id="retryEnable"
              checked={localConfig.retryConfig.enable}
              onCheckedChange={(checked) =>
                updateRetryConfig('enable', checked)
              }
            />
            <Label htmlFor="retryEnable">启用重试</Label>
          </div>

          {localConfig.retryConfig.enable && (
            <>
              <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
                <div className="space-y-2">
                  <Label htmlFor="retryInterval">重试间隔 (毫秒)</Label>
                  <div className="flex items-center space-x-2">
                    <Input
                      id="retryInterval"
                      type="number"
                      value={localConfig.retryConfig.retryInterval}
                      onChange={(e) =>
                        updateRetryConfig(
                          'retryInterval',
                          Number(e.target.value)
                        )
                      }
                      placeholder="例如: 1000"
                    />
                    <span className="text-sm text-muted-foreground">毫秒</span>
                  </div>
                </div>

                <div className="space-y-2">
                  <Label htmlFor="maxRetries">最大重试次数</Label>
                  <Input
                    id="maxRetries"
                    type="number"
                    value={localConfig.retryConfig.maxRetries}
                    onChange={(e) =>
                      updateRetryConfig('maxRetries', Number(e.target.value))
                    }
                    placeholder="例如: 3"
                  />
                </div>

                <div className="space-y-2">
                  <Label htmlFor="initialDelay">初始延迟 (毫秒)</Label>
                  <div className="flex items-center space-x-2">
                    <Input
                      id="initialDelay"
                      type="number"
                      value={localConfig.retryConfig.initialDelay}
                      onChange={(e) =>
                        updateRetryConfig(
                          'initialDelay',
                          Number(e.target.value)
                        )
                      }
                      placeholder="例如: 1000"
                    />
                    <span className="text-sm text-muted-foreground">毫秒</span>
                  </div>
                </div>

                <div className="space-y-2">
                  <Label htmlFor="maxDelay">最大延迟 (毫秒)</Label>
                  <div className="flex items-center space-x-2">
                    <Input
                      id="maxDelay"
                      type="number"
                      value={localConfig.retryConfig.maxDelay}
                      onChange={(e) =>
                        updateRetryConfig('maxDelay', Number(e.target.value))
                      }
                      placeholder="例如: 60000"
                    />
                    <span className="text-sm text-muted-foreground">毫秒</span>
                  </div>
                </div>

                <div className="space-y-2">
                  <Label htmlFor="backoffMultiplier">退避乘数</Label>
                  <Input
                    id="backoffMultiplier"
                    type="number"
                    value={localConfig.retryConfig.backoffMultiplier}
                    onChange={(e) =>
                      updateRetryConfig(
                        'backoffMultiplier',
                        Number(e.target.value)
                      )
                    }
                    placeholder="例如: 2"
                  />
                </div>
              </div>

              <div className="flex flex-col space-y-2">
                <div className="flex items-center space-x-2">
                  <Switch
                    id="enableDeadLetter"
                    checked={localConfig.retryConfig.enableDeadLetter}
                    onCheckedChange={(checked) =>
                      updateRetryConfig('enableDeadLetter', checked)
                    }
                  />
                  <Label htmlFor="enableDeadLetter">启用死信队列</Label>
                </div>

                <div className="flex items-center space-x-2">
                  <Switch
                    id="enableAutoRetry"
                    checked={localConfig.retryConfig.enableAutoRetry}
                    onCheckedChange={(checked) =>
                      updateRetryConfig('enableAutoRetry', checked)
                    }
                  />
                  <Label htmlFor="enableAutoRetry">启用自动重试</Label>
                </div>
              </div>

              <div className="p-3 bg-muted rounded-md">
                <div className="flex items-center mb-2">
                  <Clock className="h-4 w-4 mr-2" />
                  <span className="text-sm font-medium">重试策略预览</span>
                </div>
                <div className="text-sm text-muted-foreground">
                  第1次重试: {localConfig.retryConfig.initialDelay}ms
                  <br />
                  第2次重试:{' '}
                  {localConfig.retryConfig.initialDelay *
                    localConfig.retryConfig.backoffMultiplier}
                  ms
                  <br />
                  第3次重试:{' '}
                  {localConfig.retryConfig.initialDelay *
                    Math.pow(localConfig.retryConfig.backoffMultiplier, 2)}
                  ms
                </div>
              </div>

              <Alert
                variant="default"
                className="bg-blue-50 text-blue-800 border-blue-200">
                <InfoIcon className="h-4 w-4" />
                <AlertDescription>
                  <span className="font-medium">提示：</span>{' '}
                  退避乘数用于计算重试间隔，每次重试后间隔时间会乘以该值，例如初始间隔1秒，乘数为2，则第二次重试间隔为2秒，第三次为4秒。
                </AlertDescription>
              </Alert>
            </>
          )}
        </TabsContent>

        <TabsContent value="storage" className="space-y-4">
          <div className="flex items-center space-x-2 mb-4">
            <Switch
              id="offlineStorage"
              checked={localConfig.offlineStorageConfig.offlineStorage}
              onCheckedChange={(checked) =>
                updateOfflineStorage('offlineStorage', checked)
              }
            />
            <Label htmlFor="offlineStorage">启用离线存储</Label>
          </div>

          {localConfig.offlineStorageConfig.offlineStorage && (
            <>
              <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
                <div className="space-y-2">
                  <Label htmlFor="batchSize">批次大小</Label>
                  <div className="flex items-center space-x-2">
                    <Input
                      id="batchSize"
                      type="number"
                      value={localConfig.offlineStorageConfig.batchSize}
                      onChange={(e) =>
                        updateOfflineStorage(
                          'batchSize',
                          Number(e.target.value)
                        )
                      }
                      placeholder="例如: 100"
                    />
                    <span className="text-sm text-muted-foreground">条</span>
                  </div>
                </div>

                <div className="space-y-2">
                  <Label htmlFor="storageLimit">存储限制</Label>
                  <div className="flex items-center space-x-2">
                    <Input
                      id="storageLimit"
                      type="number"
                      value={localConfig.offlineStorageConfig.storageLimit}
                      onChange={(e) =>
                        updateOfflineStorage(
                          'storageLimit',
                          Number(e.target.value)
                        )
                      }
                      placeholder="例如: 100000"
                    />
                    <span className="text-sm text-muted-foreground">条</span>
                  </div>
                </div>

                <div className="space-y-2">
                  <Label htmlFor="storageMode">存储模式</Label>
                  <Select
                    value={localConfig.offlineStorageConfig.storageMode}
                    onValueChange={(value) =>
                      updateOfflineStorage('storageMode', value as StorageMode)
                    }>
                    <SelectTrigger id="storageMode">
                      <SelectValue placeholder="选择存储模式" />
                    </SelectTrigger>
                    <SelectContent>
                      <SelectItem value="file">文件存储</SelectItem>
                      <SelectItem value="database">数据库存储</SelectItem>
                      <SelectItem value="memory">内存存储</SelectItem>
                    </SelectContent>
                  </Select>
                </div>

                <div className="space-y-2">
                  <Label htmlFor="expireDays">过期天数</Label>
                  <div className="flex items-center space-x-2">
                    <Input
                      id="expireDays"
                      type="number"
                      value={localConfig.offlineStorageConfig.expireDays}
                      onChange={(e) =>
                        updateOfflineStorage(
                          'expireDays',
                          Number(e.target.value)
                        )
                      }
                      placeholder="例如: 7"
                    />
                    <span className="text-sm text-muted-foreground">天</span>
                  </div>
                </div>

                <div className="space-y-2">
                  <Label htmlFor="cleanExpireHours">清理间隔</Label>
                  <div className="flex items-center space-x-2">
                    <Input
                      id="cleanExpireHours"
                      type="number"
                      value={localConfig.offlineStorageConfig.cleanExpireHours}
                      onChange={(e) =>
                        updateOfflineStorage(
                          'cleanExpireHours',
                          Number(e.target.value)
                        )
                      }
                      placeholder="例如: 1"
                    />
                    <span className="text-sm text-muted-foreground">小时</span>
                  </div>
                </div>
              </div>

              <div className="p-3 bg-muted rounded-md">
                <div className="flex items-center mb-2">
                  <AlertTriangle className="h-4 w-4 mr-2 text-amber-500" />
                  <span className="text-sm font-medium">存储容量预估</span>
                </div>
                <div className="text-sm text-muted-foreground">
                  预计存储容量: 约{' '}
                  {(
                    localConfig.offlineStorageConfig.storageLimit * 0.5
                  ).toFixed(2)}{' '}
                  MB
                  <br />
                  预计保留时间: {localConfig.offlineStorageConfig.expireDays} 天
                </div>
              </div>

              <Alert
                variant="default"
                className="bg-blue-50 text-blue-800 border-blue-200">
                <InfoIcon className="h-4 w-4" />
                <AlertDescription>
                  <span className="font-medium">提示：</span>{' '}
                  离线存储可以在网络不稳定时保存数据，待网络恢复后再发送。文件存储适合大量数据，内存存储适合少量数据但速度更快。
                </AlertDescription>
              </Alert>
            </>
          )}
        </TabsContent>
      </Tabs>
    </div>
  )
}
