import { useEffect, useRef, useState, useCallback } from 'react'
import { ScrollArea } from '@/components/ui/scroll-area'
import { Badge } from '@/components/ui/badge'
import { Button } from '@/components/ui/button'
import { Trash2, Download, AlertTriangle } from 'lucide-react'
import { Alert, AlertDescription } from '@/components/ui/alert'
import { useConfigStore } from '@/lib/config/config-store'
import { useSignalR } from '@/lib/signalr/signalr-context'

// 定义SignalR相关类型和默认实现
type SignalRHook = {
  connectionState: string
  reconnect: () => Promise<void>
  connection: any
  subscribe: (methodName: string, callback: (...args: any[]) => void) => void
  unsubscribe: (methodName: string, callback: (...args: any[]) => void) => void
}

// 默认的空实现
const defaultSignalR: SignalRHook = {
  connectionState: 'disconnected',
  reconnect: async () => {},
  connection: null,
  subscribe: () => {},
  unsubscribe: () => {},
}

type LogLevel = 'info' | 'warning' | 'error' | 'success'

interface LogEntry {
  id: string
  timestamp: string
  message: string
  level: LogLevel
  protocol: string
  details?: string
}

// 模拟日志数据
const mockLogs: LogEntry[] = [
  {
    id: '1',
    timestamp: new Date(Date.now() - 60000).toISOString(),
    message: 'MQTT连接已建立',
    level: 'success',
    protocol: 'mqtt',
  },
  {
    id: '2',
    timestamp: new Date(Date.now() - 45000).toISOString(),
    message: '发送实时数据到MQTT主题: device/sensor-01/realtime',
    level: 'info',
    protocol: 'mqtt',
    details: JSON.stringify(
      { deviceId: 'sensor-01', value: 23.5, timestamp: Date.now() - 45000 },
      null,
      2
    ),
  },
  {
    id: '3',
    timestamp: new Date(Date.now() - 30000).toISOString(),
    message: 'HTTP请求失败: 连接超时',
    level: 'error',
    protocol: 'http',
    details: 'Error: Request timeout after 30000ms',
  },
  {
    id: '4',
    timestamp: new Date(Date.now() - 15000).toISOString(),
    message: 'WebSocket连接已断开，尝试重连',
    level: 'warning',
    protocol: 'websocket',
  },
  {
    id: '5',
    timestamp: new Date().toISOString(),
    message: 'WebSocket连接已重新建立',
    level: 'success',
    protocol: 'websocket',
  },
]

interface ForwardingLogsProps {
  useMockData?: boolean
}

export function ForwardingLogs({
  useMockData: forceMockData = false,
}: ForwardingLogsProps) {
  const [logs, setLogs] = useState<LogEntry[]>([])
  const scrollAreaRef = useRef<HTMLDivElement>(null)
  const [autoScroll, setAutoScroll] = useState(true)
  const { config } = useConfigStore()
  const [connectionError, setConnectionError] = useState<string | null>(null)
  const signalR = useSignalR()
  const [signalRLoaded, setSignalRLoaded] = useState(false)

  // 检查是否有SignalR配置
  const hasSignalRConfig = !!config.signalrHubUrl

  // 如果没有配置或强制使用模拟数据，则使用模拟数据
  const useMockData = forceMockData || !hasSignalRConfig

  // 动态加载SignalR hooks
  useEffect(() => {
    if (useMockData) {
      setSignalRLoaded(true)
      return
    }

    if (signalR.connectionState !== 'disconnected') {
      setSignalRLoaded(true)
      return
    }

    if (hasSignalRConfig) {
      setSignalRLoaded(true)
    }
  }, [useMockData, hasSignalRConfig, signalR.connectionState])

  const isConnected = signalR.connectionState === 'connected'

  // 初始化日志数据
  useEffect(() => {
    if (useMockData) {
      setLogs([...mockLogs])
    }
  }, [useMockData])

  const handleNewLog = useCallback((newLog: LogEntry) => {
    setLogs((prevLogs) =>
      [...prevLogs, { ...newLog, id: String(Date.now()) }].slice(-100)
    )
  }, [])

  // 添加重试连接功能
  const handleRetryConnection = async () => {
    setConnectionError(null)
    try {
      await signalR.reconnect()
    } catch (error) {
      setConnectionError((error as Error).message)
    }
  }

  useEffect(() => {
    // 如果使用模拟数据，设置一个定时器来模拟新日志
    if (useMockData) {
      const interval = setInterval(() => {
        const protocols = ['mqtt', 'http', 'websocket']
        const levels: LogLevel[] = ['info', 'warning', 'error', 'success']
        const messages = [
          '发送数据到外部系统',
          '接收来自设备的数据',
          '处理数据转换',
          '重试失败的连接',
          '更新设备状态',
        ]

        const newLog: LogEntry = {
          id: String(Date.now()),
          timestamp: new Date().toISOString(),
          protocol: protocols[Math.floor(Math.random() * protocols.length)],
          level: levels[Math.floor(Math.random() * levels.length)],
          message: messages[Math.floor(Math.random() * messages.length)],
        }

        setLogs((prevLogs) => [...prevLogs, newLog].slice(-100)) // 保留最新的100条日志
      }, 5000) // 每5秒添加一条新日志

      return () => clearInterval(interval)
    } else if (signalRLoaded && signalR.connection) {
      try {
        // 注册 SignalR 事件
        const connection = signalR.connection
        if (connection) {
          // 确保连接状态为已连接
          if (connection.state === 'Connected') {
            connection.on('ForwardingLog', handleNewLog)
            return () => {
              connection.off('ForwardingLog', handleNewLog)
            }
          } else {
            console.warn('SignalR连接未就绪，无法订阅事件:', connection.state)
          }
        }
      } catch (error) {
        console.warn('Failed to subscribe to SignalR events:', error)
        setConnectionError((error as Error).message)
      }
    }
  }, [useMockData, signalRLoaded, handleNewLog, signalR.connection])

  // 自动滚动到底部
  useEffect(() => {
    if (autoScroll && scrollAreaRef.current) {
      const scrollArea = scrollAreaRef.current
      scrollArea.scrollTop = scrollArea.scrollHeight
    }
  }, [logs, autoScroll])

  const clearLogs = () => {
    setLogs([])
  }

  const downloadLogs = () => {
    const logText = logs
      .map(
        (log) =>
          `[${log.timestamp}] [${log.level.toUpperCase()}] [${log.protocol}] ${
            log.message
          }${log.details ? '\n' + log.details : ''}`
      )
      .join('\n')

    const blob = new Blob([logText], { type: 'text/plain' })
    const url = URL.createObjectURL(blob)
    const a = document.createElement('a')
    a.href = url
    a.download = `forwarding-logs-${new Date().toISOString().slice(0, 10)}.txt`
    document.body.appendChild(a)
    a.click()
    document.body.removeChild(a)
    URL.revokeObjectURL(url)
  }

  const getLevelColor = (level: LogLevel) => {
    switch (level) {
      case 'info':
        return 'bg-blue-500'
      case 'warning':
        return 'bg-yellow-500'
      case 'error':
        return 'bg-red-500'
      case 'success':
        return 'bg-green-500'
      default:
        return 'bg-gray-500'
    }
  }

  // 显示连接状态和错误
  const renderConnectionStatus = () => {
    if (useMockData) return null

    if (connectionError) {
      return (
        <Alert variant="destructive" className="mb-4">
          <AlertTriangle className="h-4 w-4 mr-2" />
          <AlertDescription className="flex justify-between items-center">
            <span>SignalR连接错误: {connectionError}</span>
            <Button variant="outline" size="sm" onClick={handleRetryConnection}>
              重试连接
            </Button>
          </AlertDescription>
        </Alert>
      )
    }

    if (!isConnected && signalRLoaded) {
      return (
        <Alert variant="warning" className="mb-4">
          <AlertTriangle className="h-4 w-4 mr-2" />
          <AlertDescription className="flex justify-between items-center">
            <span>实时日志连接状态: {signalR.connectionState}</span>
            <Button variant="outline" size="sm" onClick={handleRetryConnection}>
              重试连接
            </Button>
          </AlertDescription>
        </Alert>
      )
    }

    return null
  }

  return (
    <div className="space-y-2">
      {useMockData && (
        <Alert variant="warning" className="mb-4">
          <AlertTriangle className="h-4 w-4 mr-2" />
          <AlertDescription>
            使用模拟数据显示日志。要查看实时日志，请在系统配置中设置 SignalR Hub
            URL。
          </AlertDescription>
        </Alert>
      )}

      {renderConnectionStatus()}

      <div className="flex justify-between items-center mb-2">
        <div className="flex space-x-2">
          <Badge variant="outline" className="bg-blue-50">
            信息
          </Badge>
          <Badge variant="outline" className="bg-yellow-50">
            警告
          </Badge>
          <Badge variant="outline" className="bg-red-50">
            错误
          </Badge>
          <Badge variant="outline" className="bg-green-50">
            成功
          </Badge>
        </div>
        <div className="flex space-x-2">
          <Button variant="outline" size="sm" onClick={clearLogs}>
            <Trash2 className="h-4 w-4 mr-2" />
            清除
          </Button>
          <Button variant="outline" size="sm" onClick={downloadLogs}>
            <Download className="h-4 w-4 mr-2" />
            下载
          </Button>
        </div>
      </div>

      <ScrollArea className="h-[400px] border rounded-md" ref={scrollAreaRef}>
        <div className="p-4 space-y-2">
          {logs.length === 0 ? (
            <div className="text-center text-muted-foreground py-8">
              暂无日志记录
            </div>
          ) : (
            logs.map((log) => (
              <div key={log.id} className="border-b pb-2 last:border-0">
                <div className="flex items-start">
                  <Badge className={`${getLevelColor(log.level)} mr-2 mt-1`}>
                    {log.level === 'info' && '信息'}
                    {log.level === 'warning' && '警告'}
                    {log.level === 'error' && '错误'}
                    {log.level === 'success' && '成功'}
                  </Badge>
                  <div className="flex-1">
                    <div className="flex justify-between text-sm">
                      <span className="font-medium">
                        {log.protocol.toUpperCase()}
                      </span>
                      <span className="text-muted-foreground">
                        {new Date(log.timestamp).toLocaleString()}
                      </span>
                    </div>
                    <p className="mt-1">{log.message}</p>
                    {log.details && (
                      <pre className="mt-2 p-2 bg-muted rounded-md text-xs overflow-x-auto">
                        {log.details}
                      </pre>
                    )}
                  </div>
                </div>
              </div>
            ))
          )}
        </div>
      </ScrollArea>

      <div className="flex items-center justify-end">
        <div className="flex items-center space-x-2">
          <input
            type="checkbox"
            id="autoScroll"
            checked={autoScroll}
            onChange={(e) => setAutoScroll(e.target.checked)}
            className="rounded border-gray-300"
          />
          <label htmlFor="autoScroll" className="text-sm">
            自动滚动
          </label>
        </div>
      </div>
    </div>
  )
}
