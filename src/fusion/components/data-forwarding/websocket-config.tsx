import { useState } from 'react'
import { But<PERSON> } from '@/components/ui/button'
import { Input } from '@/components/ui/input'
import { Label } from '@/components/ui/label'
import { Switch } from '@/components/ui/switch'
import {
  Select,
  SelectContent,
  SelectItem,
  SelectTrigger,
  SelectValue,
} from '@/components/ui/select'
import {
  Card,
  CardContent,
  CardDescription,
  CardFooter,
  CardHeader,
  CardTitle,
} from '@/components/ui/card'
import { Textarea } from '@/components/ui/textarea'
import { Plus, Trash2, Edit, Save } from 'lucide-react'

export function WebSocketConfig() {
  const [serverUrl, setServerUrl] = useState('wss://ws.example.com')
  const [autoReconnect, setAutoReconnect] = useState(true)
  const [reconnectInterval, setReconnectInterval] = useState('5000')
  const [maxReconnectAttempts, setMaxReconnectAttempts] = useState('10')
  const [channels, setChannels] = useState([
    {
      id: '1',
      name: '实时数据',
      channel: 'data.realtime',
      direction: 'send',
      template:
        '{"deviceId":"{{deviceId}}","value":"{{value}}","timestamp":"{{timestamp}}"}',
    },
    {
      id: '2',
      name: '设备状态',
      channel: 'device.status',
      direction: 'send',
      template: '{"deviceId":"{{deviceId}}","status":"{{status}}"}',
    },
    {
      id: '3',
      name: '命令接收',
      channel: 'command.receive',
      direction: 'receive',
      template: '',
    },
  ])
  const [editingChannel, setEditingChannel] = useState<string | null>(null)
  const [newChannel, setNewChannel] = useState({
    name: '',
    channel: '',
    direction: 'send',
    template: '',
  })

  const handleAddChannel = () => {
    if (newChannel.name && newChannel.channel) {
      setChannels([
        ...channels,
        {
          id: Date.now().toString(),
          ...newChannel,
        },
      ])
      setNewChannel({
        name: '',
        channel: '',
        direction: 'send',
        template: '',
      })
    }
  }

  const handleDeleteChannel = (id: string) => {
    setChannels(channels.filter((channel) => channel.id !== id))
  }

  const handleEditChannel = (id: string) => {
    setEditingChannel(id)
  }

  const handleSaveChannel = (id: string, updatedChannel: any) => {
    setChannels(
      channels.map((channel) =>
        channel.id === id ? { ...channel, ...updatedChannel } : channel
      )
    )
    setEditingChannel(null)
  }

  return (
    <div className="space-y-6">
      <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
        <div className="space-y-4">
          <div className="space-y-2">
            <Label htmlFor="serverUrl">WebSocket 服务器 URL</Label>
            <Input
              id="serverUrl"
              value={serverUrl}
              onChange={(e) => setServerUrl(e.target.value)}
              placeholder="例如: wss://ws.example.com"
            />
          </div>

          <div className="flex items-center space-x-2">
            <Switch
              id="autoReconnect"
              checked={autoReconnect}
              onCheckedChange={setAutoReconnect}
            />
            <Label htmlFor="autoReconnect">自动重连</Label>
          </div>
        </div>

        <div className="space-y-4">
          <div className="space-y-2">
            <Label htmlFor="reconnectInterval">重连间隔 (毫秒)</Label>
            <Input
              id="reconnectInterval"
              value={reconnectInterval}
              onChange={(e) => setReconnectInterval(e.target.value)}
              placeholder="例如: 5000"
              disabled={!autoReconnect}
            />
          </div>

          <div className="space-y-2">
            <Label htmlFor="maxReconnectAttempts">最大重连尝试次数</Label>
            <Input
              id="maxReconnectAttempts"
              value={maxReconnectAttempts}
              onChange={(e) => setMaxReconnectAttempts(e.target.value)}
              placeholder="例如: 10"
              disabled={!autoReconnect}
            />
          </div>
        </div>
      </div>

      <div className="space-y-4">
        <div className="flex justify-between items-center">
          <h3 className="text-lg font-medium">通道配置</h3>
          <Button
            variant="outline"
            size="sm"
            onClick={() => {
              setNewChannel({
                name: '',
                channel: '',
                direction: 'send',
                template: '',
              })
            }}>
            <Plus className="h-4 w-4 mr-2" />
            添加通道
          </Button>
        </div>

        <Card>
          <CardHeader>
            <CardTitle>添加新通道</CardTitle>
            <CardDescription>配置 WebSocket 通信通道</CardDescription>
          </CardHeader>
          <CardContent>
            <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
              <div className="space-y-2">
                <Label htmlFor="channelName">通道名称</Label>
                <Input
                  id="channelName"
                  value={newChannel.name}
                  onChange={(e) =>
                    setNewChannel({ ...newChannel, name: e.target.value })
                  }
                  placeholder="例如: 实时数据"
                />
              </div>

              <div className="space-y-2">
                <Label htmlFor="channelPath">通道标识</Label>
                <Input
                  id="channelPath"
                  value={newChannel.channel}
                  onChange={(e) =>
                    setNewChannel({ ...newChannel, channel: e.target.value })
                  }
                  placeholder="例如: data.realtime"
                />
              </div>

              <div className="space-y-2">
                <Label htmlFor="channelDirection">方向</Label>
                <Select
                  value={newChannel.direction}
                  onValueChange={(value) =>
                    setNewChannel({ ...newChannel, direction: value })
                  }>
                  <SelectTrigger>
                    <SelectValue placeholder="选择方向" />
                  </SelectTrigger>
                  <SelectContent>
                    <SelectItem value="send">发送</SelectItem>
                    <SelectItem value="receive">接收</SelectItem>
                  </SelectContent>
                </Select>
              </div>

              {newChannel.direction === 'send' && (
                <div className="space-y-2 md:col-span-2">
                  <Label htmlFor="channelTemplate">数据模板 (JSON)</Label>
                  <Textarea
                    id="channelTemplate"
                    value={newChannel.template}
                    onChange={(e) =>
                      setNewChannel({ ...newChannel, template: e.target.value })
                    }
                    placeholder='{"deviceId":"{{deviceId}}","value":"{{value}}","timestamp":"{{timestamp}}"}'
                    className="font-mono text-sm h-24"
                  />
                  <p className="text-xs text-muted-foreground">
                    使用 {'{{变量名}}'} 作为占位符，例如 {'{{deviceId}}'},{' '}
                    {'{{value}}'}, {'{{timestamp}}'}
                  </p>
                </div>
              )}
            </div>
          </CardContent>
          <CardFooter>
            <Button onClick={handleAddChannel}>添加通道</Button>
          </CardFooter>
        </Card>

        <div className="space-y-4">
          <h4 className="font-medium">已配置通道</h4>

          {channels.length === 0 ? (
            <p className="text-muted-foreground">暂无配置的通道</p>
          ) : (
            <div className="space-y-4">
              {channels.map((channel) => (
                <Card key={channel.id}>
                  <CardHeader className="pb-2">
                    <div className="flex justify-between items-center">
                      <CardTitle className="text-base">
                        {channel.name}
                      </CardTitle>
                      <div className="flex space-x-2">
                        <Button
                          variant="ghost"
                          size="icon"
                          onClick={() => handleEditChannel(channel.id)}>
                          <Edit className="h-4 w-4" />
                        </Button>
                        <Button
                          variant="ghost"
                          size="icon"
                          onClick={() => handleDeleteChannel(channel.id)}>
                          <Trash2 className="h-4 w-4" />
                        </Button>
                      </div>
                    </div>
                    <CardDescription>
                      {channel.direction === 'send' ? '发送到' : '接收自'}:{' '}
                      {channel.channel}
                    </CardDescription>
                  </CardHeader>

                  {editingChannel === channel.id ? (
                    <CardContent>
                      <div className="space-y-4">
                        <div className="grid grid-cols-2 gap-4">
                          <div className="space-y-2">
                            <Label htmlFor={`edit-name-${channel.id}`}>
                              通道名称
                            </Label>
                            <Input
                              id={`edit-name-${channel.id}`}
                              defaultValue={channel.name}
                              onChange={(e) => {
                                const updatedChannel = {
                                  ...channel,
                                  name: e.target.value,
                                }
                                handleSaveChannel(channel.id, updatedChannel)
                              }}
                            />
                          </div>

                          <div className="space-y-2">
                            <Label htmlFor={`edit-channel-${channel.id}`}>
                              通道标识
                            </Label>
                            <Input
                              id={`edit-channel-${channel.id}`}
                              defaultValue={channel.channel}
                              onChange={(e) => {
                                const updatedChannel = {
                                  ...channel,
                                  channel: e.target.value,
                                }
                                handleSaveChannel(channel.id, updatedChannel)
                              }}
                            />
                          </div>
                        </div>

                        {channel.direction === 'send' && (
                          <div className="space-y-2">
                            <Label htmlFor={`edit-template-${channel.id}`}>
                              数据模板
                            </Label>
                            <Textarea
                              id={`edit-template-${channel.id}`}
                              defaultValue={channel.template}
                              className="font-mono text-sm h-24"
                              onChange={(e) => {
                                const updatedChannel = {
                                  ...channel,
                                  template: e.target.value,
                                }
                                handleSaveChannel(channel.id, updatedChannel)
                              }}
                            />
                          </div>
                        )}

                        <Button onClick={() => setEditingChannel(null)}>
                          <Save className="h-4 w-4 mr-2" />
                          保存
                        </Button>
                      </div>
                    </CardContent>
                  ) : (
                    channel.direction === 'send' &&
                    channel.template && (
                      <CardContent className="pt-0">
                        <div className="bg-muted p-2 rounded-md">
                          <pre className="text-xs font-mono overflow-x-auto whitespace-pre-wrap">
                            {channel.template}
                          </pre>
                        </div>
                      </CardContent>
                    )
                  )}
                </Card>
              ))}
            </div>
          )}
        </div>
      </div>
    </div>
  )
}
