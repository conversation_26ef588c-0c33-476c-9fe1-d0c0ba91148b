import { useEffect } from 'react'
import { Card, CardContent } from '@/components/ui/card'
import { Badge } from '@/components/ui/badge'
import { Switch } from '@/components/ui/switch'
import { Skeleton } from '@/components/ui/skeleton'
import {
  Server,
  Radio,
  Database,
  Network,
  Layers,
  AlertCircle,
  ChevronRight,
} from 'lucide-react'
import { cn } from '@/lib/utils'
import { useServices } from '@/hooks/use-services'

interface ServicesListProps {
  onSelectService: (serviceId: string) => void
  selectedService: string | null
}

export function ServicesList({
  onSelectService,
  selectedService,
}: ServicesListProps) {
  const { services, loading, toggleService, isToggling } = useServices()

  // 自动选择第一个服务
  useEffect(() => {
    if (services.length > 0 && !selectedService) {
      onSelectService(services[0].id)
    }
  }, [services, selectedService, onSelectService])

  const getServiceIcon = (type: string) => {
    switch (type) {
      case 'modbus':
        return <Server className="h-5 w-5" />
      case 'mqtt':
        return <Radio className="h-5 w-5" />
      case 'opcua':
        return <Database className="h-5 w-5" />
      case 'http':
        return <Network className="h-5 w-5" />
      default:
        return <Layers className="h-5 w-5" />
    }
  }

  const getStatusBadge = (status: string) => {
    switch (status) {
      case 'running':
        return (
          <Badge
            variant="outline"
            className="bg-green-50 text-green-700 border-green-200">
            运行中
          </Badge>
        )
      case 'stopped':
        return (
          <Badge
            variant="outline"
            className="bg-gray-50 text-gray-700 border-gray-200">
            已停止
          </Badge>
        )
      case 'error':
        return (
          <Badge
            variant="outline"
            className="bg-red-50 text-red-700 border-red-200">
            错误
          </Badge>
        )
      case 'starting':
        return (
          <Badge
            variant="outline"
            className="bg-blue-50 text-blue-700 border-blue-200">
            启动中
          </Badge>
        )
      case 'stopping':
        return (
          <Badge
            variant="outline"
            className="bg-yellow-50 text-yellow-700 border-yellow-200">
            停止中
          </Badge>
        )
      default:
        return <Badge variant="outline">未知</Badge>
    }
  }

  if (loading) {
    return (
      <Card>
        <CardContent className="p-4">
          <div className="space-y-4">
            {[1, 2, 3, 4, 5].map((i) => (
              <div
                key={i}
                className="flex items-center justify-between p-3 border rounded-md">
                <div className="flex items-center space-x-4">
                  <Skeleton className="h-10 w-10 rounded-md" />
                  <div className="space-y-2">
                    <Skeleton className="h-4 w-[150px]" />
                    <Skeleton className="h-3 w-[250px]" />
                  </div>
                </div>
                <Skeleton className="h-6 w-12" />
              </div>
            ))}
          </div>
        </CardContent>
      </Card>
    )
  }

  return (
    <Card>
      <CardContent className="p-4">
        <div className="space-y-2">
          {services.map((service) => (
            <div
              key={service.id}
              className={cn(
                'flex items-center justify-between p-3 border rounded-md cursor-pointer hover:bg-muted/50 transition-colors',
                selectedService === service.id && 'bg-muted/50 border-primary'
              )}
              onClick={() => onSelectService(service.id)}>
              <div className="flex items-center space-x-4">
                <div className="flex items-center justify-center h-10 w-10 rounded-md bg-primary/10 text-primary">
                  {getServiceIcon(service.type)}
                </div>
                <div className="flex-1 min-w-0">
                  <div className="flex items-center space-x-2">
                    <h3 className="font-medium text-sm">{service.name}</h3>
                    {getStatusBadge(service.status)}
                  </div>
                  <p className="text-xs text-muted-foreground mt-1 truncate">
                    {service.description}
                  </p>
                  {service.port && (
                    <p className="text-xs text-muted-foreground">
                      端口: {service.port}
                    </p>
                  )}
                </div>
              </div>
              <div className="flex items-center space-x-2">
                <Switch
                  checked={service.enabled}
                  onCheckedChange={(checked) =>
                    toggleService(service.id, checked)
                  }
                  disabled={isToggling}
                  onClick={(e) => e.stopPropagation()}
                />
                <ChevronRight className="h-4 w-4 text-muted-foreground" />
              </div>
            </div>
          ))}

          {services.length === 0 && (
            <div className="flex items-center justify-center h-32 text-muted-foreground">
              <div className="text-center">
                <AlertCircle className="h-8 w-8 mx-auto mb-2" />
                <p>暂无可用服务</p>
              </div>
            </div>
          )}
        </div>
      </CardContent>
    </Card>
  )
}
