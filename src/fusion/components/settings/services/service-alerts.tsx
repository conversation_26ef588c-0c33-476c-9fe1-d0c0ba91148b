import { useState, useEffect } from 'react'
import {
  Card,
  CardContent,
  CardDescription,
  CardHeader,
  CardTitle,
} from '@/components/ui/card'
import { <PERSON><PERSON>, <PERSON><PERSON>Content, <PERSON>bsList, TabsTrigger } from '@/components/ui/tabs'
import { Button } from '@/components/ui/button'
import { Input } from '@/components/ui/input'
import { Label } from '@/components/ui/label'
import { Switch } from '@/components/ui/switch'
import {
  Select,
  SelectContent,
  SelectItem,
  SelectTrigger,
  SelectValue,
} from '@/components/ui/select'
import { Badge } from '@/components/ui/badge'
import { Skeleton } from '@/components/ui/skeleton'
import { useToast } from '@/hooks/use-toast'
import { Checkbox } from '@/components/ui/checkbox'
import {
  Activity,
  AlertTriangle,
  Bell,
  BellOff,
  Clock,
  Copy,
  Edit,
  Info,
  MemoryStick,
  MoreVertical,
  Plus,
  Trash2,
  Users,
  Zap,
} from 'lucide-react'
import {
  DropdownMenu,
  DropdownMenuContent,
  DropdownMenuItem,
  DropdownMenuLabel,
  DropdownMenuSeparator,
  DropdownMenuTrigger,
} from '@/components/ui/dropdown-menu'
import {
  Dialog,
  DialogContent,
  DialogDescription,
  DialogFooter,
  DialogHeader,
  DialogTitle,
} from '@/components/ui/dialog'
import { ScrollArea } from '@/components/ui/scroll-area'

interface ServiceAlertsProps {
  serviceId: string
}

// Alert threshold types
interface AlertThreshold {
  id: string
  name: string
  metricType:
    | 'cpu'
    | 'memory'
    | 'connections'
    | 'errors'
    | 'responseTime'
    | 'throughput'
    | 'custom'
  operator: '>' | '>=' | '<' | '<=' | '=='
  value: number
  unit: string
  severity: 'low' | 'medium' | 'high' | 'critical'
  enabled: boolean
  notificationChannels: string[]
  cooldownMinutes: number
  description?: string
  createdAt: string
  lastTriggered?: string
}

// Notification channel types
interface NotificationChannel {
  id: string
  name: string
  type: 'email' | 'sms' | 'webhook' | 'inApp' | 'pushNotification'
  recipients?: string[]
  webhookUrl?: string
  enabled: boolean
  createdAt: string
}

// Alert history entry
interface AlertHistoryEntry {
  id: string
  thresholdId: string
  thresholdName: string
  metricType: string
  value: number
  timestamp: string
  severity: 'low' | 'medium' | 'high' | 'critical'
  acknowledged: boolean
}

export function ServiceAlerts({ serviceId }: ServiceAlertsProps) {
  const [loading, setLoading] = useState(true)
  const [alertThresholds, setAlertThresholds] = useState<AlertThreshold[]>([])
  const [notificationChannels, setNotificationChannels] = useState<
    NotificationChannel[]
  >([])
  const [alertHistory, setAlertHistory] = useState<AlertHistoryEntry[]>([])
  const [editingThreshold, setEditingThreshold] =
    useState<AlertThreshold | null>(null)
  const [editingChannel, setEditingChannel] =
    useState<NotificationChannel | null>(null)
  const [isThresholdDialogOpen, setIsThresholdDialogOpen] = useState(false)
  const [isChannelDialogOpen, setIsChannelDialogOpen] = useState(false)
  const [testAlertStatus, setTestAlertStatus] = useState<
    'idle' | 'sending' | 'success' | 'error'
  >('idle')
  const { toast } = useToast()

  // Load alert settings
  useEffect(() => {
    setLoading(true)

    // Simulate API call to fetch alert settings
    setTimeout(() => {
      // Mock alert thresholds
      const mockThresholds: AlertThreshold[] = [
        {
          id: 'threshold-1',
          name: '高CPU使用率',
          metricType: 'cpu',
          operator: '>',
          value: 80,
          unit: '%',
          severity: 'high',
          enabled: true,
          notificationChannels: ['channel-1', 'channel-3'],
          cooldownMinutes: 15,
          description: 'CPU使用率超过80%时触发警报',
          createdAt: '2025-04-20T10:30:00Z',
          lastTriggered: '2025-04-25T14:22:15Z',
        },
        {
          id: 'threshold-2',
          name: '内存不足',
          metricType: 'memory',
          operator: '>',
          value: 90,
          unit: '%',
          severity: 'critical',
          enabled: true,
          notificationChannels: ['channel-1', 'channel-2'],
          cooldownMinutes: 5,
          description: '内存使用率超过90%时触发警报',
          createdAt: '2025-04-20T11:15:00Z',
        },
        {
          id: 'threshold-3',
          name: '连接数过多',
          metricType: 'connections',
          operator: '>',
          value: 150,
          unit: '连接',
          severity: 'medium',
          enabled: false,
          notificationChannels: ['channel-3'],
          cooldownMinutes: 10,
          description: '连接数超过150时触发警报',
          createdAt: '2025-04-21T09:45:00Z',
          lastTriggered: '2025-04-23T16:08:42Z',
        },
        {
          id: 'threshold-4',
          name: '错误率过高',
          metricType: 'errors',
          operator: '>',
          value: 5,
          unit: '%',
          severity: 'high',
          enabled: true,
          notificationChannels: ['channel-1', 'channel-2', 'channel-3'],
          cooldownMinutes: 5,
          description: '错误率超过5%时触发警报',
          createdAt: '2025-04-21T14:20:00Z',
        },
        {
          id: 'threshold-5',
          name: '响应时间过长',
          metricType: 'responseTime',
          operator: '>',
          value: 500,
          unit: 'ms',
          severity: 'medium',
          enabled: true,
          notificationChannels: ['channel-2'],
          cooldownMinutes: 15,
          description: '响应时间超过500ms时触发警报',
          createdAt: '2025-04-22T08:10:00Z',
          lastTriggered: '2025-04-24T11:37:29Z',
        },
      ]

      // Mock notification channels
      const mockChannels: NotificationChannel[] = [
        {
          id: 'channel-1',
          name: '系统管理员邮件',
          type: 'email',
          recipients: ['<EMAIL>', '<EMAIL>'],
          enabled: true,
          createdAt: '2025-04-15T08:00:00Z',
        },
        {
          id: 'channel-2',
          name: '运维团队短信',
          type: 'sms',
          recipients: ['+8613800138000', '+8613900139000'],
          enabled: true,
          createdAt: '2025-04-15T08:15:00Z',
        },
        {
          id: 'channel-3',
          name: '监控系统Webhook',
          type: 'webhook',
          webhookUrl: 'https://monitor.example.com/api/alerts',
          enabled: true,
          createdAt: '2025-04-15T09:30:00Z',
        },
        {
          id: 'channel-4',
          name: '应用内通知',
          type: 'inApp',
          enabled: true,
          createdAt: '2025-04-15T10:45:00Z',
        },
      ]

      // Mock alert history
      const mockHistory: AlertHistoryEntry[] = [
        {
          id: 'alert-1',
          thresholdId: 'threshold-1',
          thresholdName: '高CPU使用率',
          metricType: 'cpu',
          value: 85.2,
          timestamp: '2025-04-25T14:22:15Z',
          severity: 'high',
          acknowledged: true,
        },
        {
          id: 'alert-2',
          thresholdId: 'threshold-5',
          thresholdName: '响应时间过长',
          metricType: 'responseTime',
          value: 678,
          timestamp: '2025-04-24T11:37:29Z',
          severity: 'medium',
          acknowledged: true,
        },
        {
          id: 'alert-3',
          thresholdId: 'threshold-3',
          thresholdName: '连接数过多',
          metricType: 'connections',
          value: 172,
          timestamp: '2025-04-23T16:08:42Z',
          severity: 'medium',
          acknowledged: false,
        },
        {
          id: 'alert-4',
          thresholdId: 'threshold-1',
          thresholdName: '高CPU使用率',
          metricType: 'cpu',
          value: 92.7,
          timestamp: '2025-04-22T09:14:08Z',
          severity: 'high',
          acknowledged: true,
        },
        {
          id: 'alert-5',
          thresholdId: 'threshold-2',
          thresholdName: '内存不足',
          metricType: 'memory',
          value: 94.3,
          timestamp: '2025-04-21T23:47:52Z',
          severity: 'critical',
          acknowledged: true,
        },
      ]

      setAlertThresholds(mockThresholds)
      setNotificationChannels(mockChannels)
      setAlertHistory(mockHistory)
      setLoading(false)
    }, 1000)
  }, [serviceId])

  // Save alert threshold
  const saveAlertThreshold = (threshold: AlertThreshold) => {
    if (threshold.id) {
      // Update existing threshold
      setAlertThresholds((prev) =>
        prev.map((item) =>
          item.id === threshold.id
            ? { ...threshold, createdAt: item.createdAt }
            : item
        )
      )
    } else {
      // Add new threshold
      const newThreshold = {
        ...threshold,
        id: `threshold-${Date.now()}`,
        createdAt: new Date().toISOString(),
      }
      setAlertThresholds((prev) => [...prev, newThreshold])
    }

    setIsThresholdDialogOpen(false)
    setEditingThreshold(null)

    toast({
      title: '警报阈值已保存',
      description: '警报阈值设置已成功更新',
    })
  }

  // Delete alert threshold
  const deleteAlertThreshold = (id: string) => {
    setAlertThresholds((prev) => prev.filter((item) => item.id !== id))

    toast({
      title: '警报阈值已删除',
      description: '警报阈值设置已成功删除',
    })
  }

  // Save notification channel
  const saveNotificationChannel = (channel: NotificationChannel) => {
    if (channel.id) {
      // Update existing channel
      setNotificationChannels((prev) =>
        prev.map((item) =>
          item.id === channel.id
            ? { ...channel, createdAt: item.createdAt }
            : item
        )
      )
    } else {
      // Add new channel
      const newChannel = {
        ...channel,
        id: `channel-${Date.now()}`,
        createdAt: new Date().toISOString(),
      }
      setNotificationChannels((prev) => [...prev, newChannel])
    }

    setIsChannelDialogOpen(false)
    setEditingChannel(null)

    toast({
      title: '通知渠道已保存',
      description: '通知渠道设置已成功更新',
    })
  }

  // Delete notification channel
  const deleteNotificationChannel = (id: string) => {
    // Check if channel is used by any threshold
    const isChannelInUse = alertThresholds.some((threshold) =>
      threshold.notificationChannels.includes(id)
    )

    if (isChannelInUse) {
      toast({
        title: '无法删除通知渠道',
        description: '该通知渠道正在被一个或多个警报阈值使用',
        variant: 'destructive',
      })
      return
    }

    setNotificationChannels((prev) => prev.filter((item) => item.id !== id))

    toast({
      title: '通知渠道已删除',
      description: '通知渠道设置已成功删除',
    })
  }

  // Toggle alert threshold enabled state
  const toggleThresholdEnabled = (id: string, enabled: boolean) => {
    setAlertThresholds((prev) =>
      prev.map((item) => (item.id === id ? { ...item, enabled } : item))
    )

    toast({
      title: enabled ? '警报阈值已启用' : '警报阈值已禁用',
      description: enabled ? '系统将开始监控该阈值' : '系统将不再监控该阈值',
    })
  }

  // Toggle notification channel enabled state
  const toggleChannelEnabled = (id: string, enabled: boolean) => {
    setNotificationChannels((prev) =>
      prev.map((item) => (item.id === id ? { ...item, enabled } : item))
    )

    toast({
      title: enabled ? '通知渠道已启用' : '通知渠道已禁用',
      description: enabled
        ? '系统将通过该渠道发送通知'
        : '系统将不再通过该渠道发送通知',
    })
  }

  // Acknowledge alert
  const acknowledgeAlert = (id: string) => {
    setAlertHistory((prev) =>
      prev.map((item) =>
        item.id === id ? { ...item, acknowledged: true } : item
      )
    )

    toast({
      title: '警报已确认',
      description: '警报已被标记为已确认',
    })
  }

  // Test alert
  const testAlert = (thresholdId: string) => {
    setTestAlertStatus('sending')

    // Simulate API call to test alert
    setTimeout(() => {
      setTestAlertStatus('success')

      toast({
        title: '测试警报已发送',
        description: '测试警报已成功发送到所有配置的通知渠道',
      })

      // Reset status after a delay
      setTimeout(() => {
        setTestAlertStatus('idle')
      }, 3000)
    }, 2000)
  }

  // Get metric icon
  const getMetricIcon = (metricType: string) => {
    switch (metricType) {
      case 'cpu':
        return <Activity className="h-4 w-4" />
      case 'memory':
        return <MemoryStick className="h-4 w-4" />
      case 'connections':
        return <Users className="h-4 w-4" />
      case 'errors':
        return <AlertTriangle className="h-4 w-4" />
      case 'responseTime':
        return <Clock className="h-4 w-4" />
      case 'throughput':
        return <Zap className="h-4 w-4" />
      default:
        return <Info className="h-4 w-4" />
    }
  }

  // Get notification channel icon
  const getChannelIcon = (channelType: string) => {
    switch (channelType) {
      case 'email':
        return '📧'
      case 'sms':
        return '📱'
      case 'webhook':
        return '🔗'
      case 'inApp':
        return '🔔'
      case 'pushNotification':
        return '📲'
      default:
        return '📣'
    }
  }

  // Get severity badge
  const getSeverityBadge = (severity: string) => {
    switch (severity) {
      case 'low':
        return <Badge variant="outline">低</Badge>
      case 'medium':
        return <Badge variant="secondary">中</Badge>
      case 'high':
        return <Badge variant="warning">高</Badge>
      case 'critical':
        return <Badge variant="destructive">紧急</Badge>
      default:
        return <Badge variant="outline">未知</Badge>
    }
  }

  // Format date
  const formatDate = (dateString: string) => {
    const date = new Date(dateString)
    return date.toLocaleString('zh-CN', {
      year: 'numeric',
      month: '2-digit',
      day: '2-digit',
      hour: '2-digit',
      minute: '2-digit',
    })
  }

  // Get metric name
  const getMetricName = (metricType: string) => {
    switch (metricType) {
      case 'cpu':
        return 'CPU使用率'
      case 'memory':
        return '内存使用率'
      case 'connections':
        return '连接数'
      case 'errors':
        return '错误率'
      case 'responseTime':
        return '响应时间'
      case 'throughput':
        return '吞吐量'
      default:
        return '自定义指标'
    }
  }

  // Get channel name by ID
  const getChannelNameById = (id: string) => {
    const channel = notificationChannels.find((c) => c.id === id)
    return channel ? channel.name : '未知渠道'
  }

  if (loading) {
    return (
      <div className="space-y-4">
        <Skeleton className="h-8 w-[200px]" />
        <Skeleton className="h-[400px] w-full" />
      </div>
    )
  }

  return (
    <div className="space-y-6">
      <Tabs defaultValue="thresholds" className="w-full">
        <TabsList className="mb-4">
          <TabsTrigger value="thresholds">警报阈值</TabsTrigger>
          <TabsTrigger value="channels">通知渠道</TabsTrigger>
          <TabsTrigger value="history">警报历史</TabsTrigger>
        </TabsList>

        <TabsContent value="thresholds">
          <Card>
            <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
              <CardTitle>警报阈值设置</CardTitle>
              <Button
                size="sm"
                onClick={() => {
                  setEditingThreshold({
                    id: '',
                    name: '',
                    metricType: 'cpu',
                    operator: '>',
                    value: 80,
                    unit: '%',
                    severity: 'medium',
                    enabled: true,
                    notificationChannels: [],
                    cooldownMinutes: 15,
                    createdAt: new Date().toISOString(),
                  })
                  setIsThresholdDialogOpen(true)
                }}>
                <Plus className="mr-2 h-4 w-4" />
                添加阈值
              </Button>
            </CardHeader>
            <CardContent>
              {alertThresholds.length === 0 ? (
                <div className="flex flex-col items-center justify-center py-8">
                  <Bell className="h-12 w-12 text-muted-foreground opacity-20" />
                  <h3 className="mt-4 text-lg font-medium">没有警报阈值</h3>
                  <p className="mt-2 text-sm text-muted-foreground">
                    点击"添加阈值"按钮创建第一个警报阈值
                  </p>
                </div>
              ) : (
                <div className="space-y-4">
                  {alertThresholds.map((threshold) => (
                    <Card
                      key={threshold.id}
                      className={threshold.enabled ? '' : 'opacity-60'}>
                      <CardContent className="p-4">
                        <div className="flex items-start justify-between">
                          <div className="flex items-start space-x-4">
                            <div className="mt-0.5">
                              <div className="rounded-full p-2 bg-primary/10">
                                {getMetricIcon(threshold.metricType)}
                              </div>
                            </div>
                            <div>
                              <div className="flex items-center space-x-2">
                                <h4 className="font-medium">
                                  {threshold.name}
                                </h4>
                                {getSeverityBadge(threshold.severity)}
                                {!threshold.enabled && (
                                  <Badge
                                    variant="outline"
                                    className="text-muted-foreground">
                                    已禁用
                                  </Badge>
                                )}
                              </div>
                              <p className="text-sm text-muted-foreground mt-1">
                                {getMetricName(threshold.metricType)}{' '}
                                {threshold.operator} {threshold.value}
                                {threshold.unit}
                              </p>
                              <div className="flex flex-wrap gap-1 mt-2">
                                {threshold.notificationChannels.map(
                                  (channelId) => (
                                    <Badge
                                      key={channelId}
                                      variant="secondary"
                                      className="text-xs">
                                      {getChannelNameById(channelId)}
                                    </Badge>
                                  )
                                )}
                              </div>
                              {threshold.description && (
                                <p className="text-xs text-muted-foreground mt-2">
                                  {threshold.description}
                                </p>
                              )}
                              {threshold.lastTriggered && (
                                <p className="text-xs text-muted-foreground mt-1">
                                  上次触发:{' '}
                                  {formatDate(threshold.lastTriggered)}
                                </p>
                              )}
                            </div>
                          </div>
                          <div className="flex items-center space-x-2">
                            <Switch
                              checked={threshold.enabled}
                              onCheckedChange={(checked) =>
                                toggleThresholdEnabled(threshold.id, checked)
                              }
                            />
                            <DropdownMenu>
                              <DropdownMenuTrigger asChild>
                                <Button
                                  variant="ghost"
                                  size="icon"
                                  className="h-8 w-8">
                                  <MoreVertical className="h-4 w-4" />
                                  <span className="sr-only">操作</span>
                                </Button>
                              </DropdownMenuTrigger>
                              <DropdownMenuContent align="end">
                                <DropdownMenuLabel>警报操作</DropdownMenuLabel>
                                <DropdownMenuItem
                                  onClick={() => {
                                    setEditingThreshold(threshold)
                                    setIsThresholdDialogOpen(true)
                                  }}>
                                  <Edit className="mr-2 h-4 w-4" />
                                  编辑
                                </DropdownMenuItem>
                                <DropdownMenuItem
                                  onClick={() => {
                                    testAlert(threshold.id)
                                  }}>
                                  <Bell className="mr-2 h-4 w-4" />
                                  测试警报
                                </DropdownMenuItem>
                                <DropdownMenuItem
                                  onClick={() => {
                                    const newThreshold = { ...threshold }
                                    newThreshold.id = ''
                                    newThreshold.name = `${threshold.name} (复制)`
                                    setEditingThreshold(newThreshold)
                                    setIsThresholdDialogOpen(true)
                                  }}>
                                  <Copy className="mr-2 h-4 w-4" />
                                  复制
                                </DropdownMenuItem>
                                <DropdownMenuSeparator />
                                <DropdownMenuItem
                                  className="text-destructive focus:text-destructive"
                                  onClick={() =>
                                    deleteAlertThreshold(threshold.id)
                                  }>
                                  <Trash2 className="mr-2 h-4 w-4" />
                                  删除
                                </DropdownMenuItem>
                              </DropdownMenuContent>
                            </DropdownMenu>
                          </div>
                        </div>
                      </CardContent>
                    </Card>
                  ))}
                </div>
              )}
            </CardContent>
          </Card>

          {/* Alert Threshold Dialog */}
          <Dialog
            open={isThresholdDialogOpen}
            onOpenChange={setIsThresholdDialogOpen}>
            <DialogContent className="sm:max-w-[600px]">
              <DialogHeader>
                <DialogTitle>
                  {editingThreshold?.id ? '编辑警报阈值' : '添加警报阈值'}
                </DialogTitle>
                <DialogDescription>
                  设置服务指标的警报阈值，当指标超过阈值时系统将发送通知
                </DialogDescription>
              </DialogHeader>

              {editingThreshold && (
                <div className="grid gap-4 py-4">
                  <div className="grid grid-cols-2 gap-4">
                    <div className="space-y-2">
                      <Label htmlFor="threshold-name">警报名称</Label>
                      <Input
                        id="threshold-name"
                        value={editingThreshold.name}
                        onChange={(e) =>
                          setEditingThreshold({
                            ...editingThreshold,
                            name: e.target.value,
                          })
                        }
                        placeholder="例如：高CPU使用率"
                      />
                    </div>
                    <div className="space-y-2">
                      <Label htmlFor="threshold-metric">监控指标</Label>
                      <Select
                        value={editingThreshold.metricType}
                        onValueChange={(value: any) =>
                          setEditingThreshold({
                            ...editingThreshold,
                            metricType: value,
                          })
                        }>
                        <SelectTrigger id="threshold-metric">
                          <SelectValue placeholder="选择监控指标" />
                        </SelectTrigger>
                        <SelectContent>
                          <SelectItem value="cpu">CPU使用率</SelectItem>
                          <SelectItem value="memory">内存使用率</SelectItem>
                          <SelectItem value="connections">连接数</SelectItem>
                          <SelectItem value="errors">错误率</SelectItem>
                          <SelectItem value="responseTime">响应时间</SelectItem>
                          <SelectItem value="throughput">吞吐量</SelectItem>
                          <SelectItem value="custom">自定义指标</SelectItem>
                        </SelectContent>
                      </Select>
                    </div>
                  </div>

                  <div className="grid grid-cols-3 gap-4">
                    <div className="space-y-2">
                      <Label htmlFor="threshold-operator">条件</Label>
                      <Select
                        value={editingThreshold.operator}
                        onValueChange={(value: any) =>
                          setEditingThreshold({
                            ...editingThreshold,
                            operator: value,
                          })
                        }>
                        <SelectTrigger id="threshold-operator">
                          <SelectValue placeholder="选择条件" />
                        </SelectTrigger>
                        <SelectContent>
                          <SelectItem value=">">大于</SelectItem>
                          <SelectItem value=">=">大于等于</SelectItem>
                          <SelectItem value="<">小于</SelectItem>
                          <SelectItem value="<=">小于等于</SelectItem>
                          <SelectItem value="==">等于</SelectItem>
                        </SelectContent>
                      </Select>
                    </div>
                    <div className="space-y-2">
                      <Label htmlFor="threshold-value">阈值</Label>
                      <Input
                        id="threshold-value"
                        type="number"
                        value={editingThreshold.value}
                        onChange={(e) =>
                          setEditingThreshold({
                            ...editingThreshold,
                            value: Number.parseFloat(e.target.value),
                          })
                        }
                      />
                    </div>
                    <div className="space-y-2">
                      <Label htmlFor="threshold-unit">单位</Label>
                      <Input
                        id="threshold-unit"
                        value={editingThreshold.unit}
                        onChange={(e) =>
                          setEditingThreshold({
                            ...editingThreshold,
                            unit: e.target.value,
                          })
                        }
                        placeholder="例如：%、ms、MB/s"
                      />
                    </div>
                  </div>

                  <div className="grid grid-cols-2 gap-4">
                    <div className="space-y-2">
                      <Label htmlFor="threshold-severity">严重程度</Label>
                      <Select
                        value={editingThreshold.severity}
                        onValueChange={(value: any) =>
                          setEditingThreshold({
                            ...editingThreshold,
                            severity: value,
                          })
                        }>
                        <SelectTrigger id="threshold-severity">
                          <SelectValue placeholder="选择严重程度" />
                        </SelectTrigger>
                        <SelectContent>
                          <SelectItem value="low">低</SelectItem>
                          <SelectItem value="medium">中</SelectItem>
                          <SelectItem value="high">高</SelectItem>
                          <SelectItem value="critical">紧急</SelectItem>
                        </SelectContent>
                      </Select>
                    </div>
                    <div className="space-y-2">
                      <Label htmlFor="threshold-cooldown">
                        冷却时间（分钟）
                      </Label>
                      <Input
                        id="threshold-cooldown"
                        type="number"
                        value={editingThreshold.cooldownMinutes}
                        onChange={(e) =>
                          setEditingThreshold({
                            ...editingThreshold,
                            cooldownMinutes: Number.parseInt(e.target.value),
                          })
                        }
                        placeholder="例如：15"
                      />
                    </div>
                  </div>

                  <div className="space-y-2">
                    <Label>通知渠道</Label>
                    <div className="border rounded-md p-4 space-y-2">
                      {notificationChannels.length === 0 ? (
                        <p className="text-sm text-muted-foreground">
                          没有可用的通知渠道，请先创建通知渠道
                        </p>
                      ) : (
                        notificationChannels.map((channel) => (
                          <div
                            key={channel.id}
                            className="flex items-center space-x-2">
                            <Checkbox
                              id={`channel-${channel.id}`}
                              checked={editingThreshold.notificationChannels.includes(
                                channel.id
                              )}
                              onCheckedChange={(checked) => {
                                if (checked) {
                                  setEditingThreshold({
                                    ...editingThreshold,
                                    notificationChannels: [
                                      ...editingThreshold.notificationChannels,
                                      channel.id,
                                    ],
                                  })
                                } else {
                                  setEditingThreshold({
                                    ...editingThreshold,
                                    notificationChannels:
                                      editingThreshold.notificationChannels.filter(
                                        (id) => id !== channel.id
                                      ),
                                  })
                                }
                              }}
                              disabled={!channel.enabled}
                            />
                            <Label
                              htmlFor={`channel-${channel.id}`}
                              className={`flex items-center ${
                                !channel.enabled ? 'text-muted-foreground' : ''
                              }`}>
                              <span className="mr-2">
                                {getChannelIcon(channel.type)}
                              </span>
                              {channel.name}
                              {!channel.enabled && (
                                <span className="ml-2 text-xs">(已禁用)</span>
                              )}
                            </Label>
                          </div>
                        ))
                      )}
                    </div>
                  </div>

                  <div className="space-y-2">
                    <Label htmlFor="threshold-description">描述（可选）</Label>
                    <Input
                      id="threshold-description"
                      value={editingThreshold.description || ''}
                      onChange={(e) =>
                        setEditingThreshold({
                          ...editingThreshold,
                          description: e.target.value,
                        })
                      }
                      placeholder="描述此警报阈值的用途"
                    />
                  </div>

                  <div className="flex items-center space-x-2">
                    <Switch
                      id="threshold-enabled"
                      checked={editingThreshold.enabled}
                      onCheckedChange={(checked) =>
                        setEditingThreshold({
                          ...editingThreshold,
                          enabled: checked,
                        })
                      }
                    />
                    <Label htmlFor="threshold-enabled">启用此警报阈值</Label>
                  </div>
                </div>
              )}

              <DialogFooter>
                <Button
                  variant="outline"
                  onClick={() => setIsThresholdDialogOpen(false)}>
                  取消
                </Button>
                <Button onClick={() => saveAlertThreshold(editingThreshold!)}>
                  保存
                </Button>
              </DialogFooter>
            </DialogContent>
          </Dialog>
        </TabsContent>

        <TabsContent value="channels">
          <Card>
            <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
              <CardTitle>通知渠道设置</CardTitle>
              <Button
                size="sm"
                onClick={() => {
                  setEditingChannel({
                    id: '',
                    name: '',
                    type: 'email',
                    recipients: [],
                    enabled: true,
                    createdAt: new Date().toISOString(),
                  })
                  setIsChannelDialogOpen(true)
                }}>
                <Plus className="mr-2 h-4 w-4" />
                添加渠道
              </Button>
            </CardHeader>
            <CardContent>
              {notificationChannels.length === 0 ? (
                <div className="flex flex-col items-center justify-center py-8">
                  <BellOff className="h-12 w-12 text-muted-foreground opacity-20" />
                  <h3 className="mt-4 text-lg font-medium">没有通知渠道</h3>
                  <p className="mt-2 text-sm text-muted-foreground">
                    点击"添加渠道"按钮创建第一个通知渠道
                  </p>
                </div>
              ) : (
                <div className="space-y-4">
                  {notificationChannels.map((channel) => (
                    <Card
                      key={channel.id}
                      className={channel.enabled ? '' : 'opacity-60'}>
                      <CardContent className="p-4">
                        <div className="flex items-start justify-between">
                          <div className="flex items-start space-x-4">
                            <div className="mt-0.5 text-2xl">
                              {getChannelIcon(channel.type)}
                            </div>
                            <div>
                              <div className="flex items-center space-x-2">
                                <h4 className="font-medium">{channel.name}</h4>
                                <Badge variant="outline">{channel.type}</Badge>
                                {!channel.enabled && (
                                  <Badge
                                    variant="outline"
                                    className="text-muted-foreground">
                                    已禁用
                                  </Badge>
                                )}
                              </div>
                              {channel.recipients &&
                                channel.recipients.length > 0 && (
                                  <p className="text-sm text-muted-foreground mt-1">
                                    接收者: {channel.recipients.join(', ')}
                                  </p>
                                )}
                              {channel.webhookUrl && (
                                <p className="text-sm text-muted-foreground mt-1">
                                  URL: {channel.webhookUrl}
                                </p>
                              )}
                              <p className="text-xs text-muted-foreground mt-1">
                                创建于: {formatDate(channel.createdAt)}
                              </p>
                            </div>
                          </div>
                          <div className="flex items-center space-x-2">
                            <Switch
                              checked={channel.enabled}
                              onCheckedChange={(checked) =>
                                toggleChannelEnabled(channel.id, checked)
                              }
                            />
                            <DropdownMenu>
                              <DropdownMenuTrigger asChild>
                                <Button
                                  variant="ghost"
                                  size="icon"
                                  className="h-8 w-8">
                                  <MoreVertical className="h-4 w-4" />
                                  <span className="sr-only">操作</span>
                                </Button>
                              </DropdownMenuTrigger>
                              <DropdownMenuContent align="end">
                                <DropdownMenuLabel>渠道操作</DropdownMenuLabel>
                                <DropdownMenuItem
                                  onClick={() => {
                                    setEditingChannel(channel)
                                    setIsChannelDialogOpen(true)
                                  }}>
                                  <Edit className="mr-2 h-4 w-4" />
                                  编辑
                                </DropdownMenuItem>
                                <DropdownMenuItem
                                  onClick={() => {
                                    const newChannel = { ...channel }
                                    newChannel.id = ''
                                    newChannel.name = `${channel.name} (复制)`
                                    setEditingChannel(newChannel)
                                    setIsChannelDialogOpen(true)
                                  }}>
                                  <Copy className="mr-2 h-4 w-4" />
                                  复制
                                </DropdownMenuItem>
                                <DropdownMenuSeparator />
                                <DropdownMenuItem
                                  className="text-destructive focus:text-destructive"
                                  onClick={() =>
                                    deleteNotificationChannel(channel.id)
                                  }>
                                  <Trash2 className="mr-2 h-4 w-4" />
                                  删除
                                </DropdownMenuItem>
                              </DropdownMenuContent>
                            </DropdownMenu>
                          </div>
                        </div>
                      </CardContent>
                    </Card>
                  ))}
                </div>
              )}
            </CardContent>
          </Card>

          {/* Notification Channel Dialog */}
          <Dialog
            open={isChannelDialogOpen}
            onOpenChange={setIsChannelDialogOpen}>
            <DialogContent className="sm:max-w-[600px]">
              <DialogHeader>
                <DialogTitle>
                  {editingChannel?.id ? '编辑通知渠道' : '添加通知渠道'}
                </DialogTitle>
                <DialogDescription>
                  设置警报通知的发送渠道，支持邮件、短信、Webhook等多种方式
                </DialogDescription>
              </DialogHeader>

              {editingChannel && (
                <div className="grid gap-4 py-4">
                  <div className="grid grid-cols-2 gap-4">
                    <div className="space-y-2">
                      <Label htmlFor="channel-name">渠道名称</Label>
                      <Input
                        id="channel-name"
                        value={editingChannel.name}
                        onChange={(e) =>
                          setEditingChannel({
                            ...editingChannel,
                            name: e.target.value,
                          })
                        }
                        placeholder="例如：系统管理员邮件"
                      />
                    </div>
                    <div className="space-y-2">
                      <Label htmlFor="channel-type">渠道类型</Label>
                      <Select
                        value={editingChannel.type}
                        onValueChange={(value: any) =>
                          setEditingChannel({ ...editingChannel, type: value })
                        }>
                        <SelectTrigger id="channel-type">
                          <SelectValue placeholder="选择渠道类型" />
                        </SelectTrigger>
                        <SelectContent>
                          <SelectItem value="email">电子邮件</SelectItem>
                          <SelectItem value="sms">短信</SelectItem>
                          <SelectItem value="webhook">Webhook</SelectItem>
                          <SelectItem value="inApp">应用内通知</SelectItem>
                          <SelectItem value="pushNotification">
                            推送通知
                          </SelectItem>
                        </SelectContent>
                      </Select>
                    </div>
                  </div>

                  {(editingChannel.type === 'email' ||
                    editingChannel.type === 'sms') && (
                    <div className="space-y-2">
                      <Label htmlFor="channel-recipients">
                        接收者{' '}
                        {editingChannel.type === 'email'
                          ? '邮箱地址'
                          : '手机号码'}
                      </Label>
                      <Input
                        id="channel-recipients"
                        value={editingChannel.recipients?.join(', ') || ''}
                        onChange={(e) =>
                          setEditingChannel({
                            ...editingChannel,
                            recipients: e.target.value
                              .split(',')
                              .map((item) => item.trim()),
                          })
                        }
                        placeholder={
                          editingChannel.type === 'email'
                            ? '例如：<EMAIL>, <EMAIL>'
                            : '例如：13800138000, 13900139000'
                        }
                      />
                      <p className="text-xs text-muted-foreground">
                        多个接收者请用逗号分隔
                      </p>
                    </div>
                  )}

                  {editingChannel.type === 'webhook' && (
                    <div className="space-y-2">
                      <Label htmlFor="channel-webhook">Webhook URL</Label>
                      <Input
                        id="channel-webhook"
                        value={editingChannel.webhookUrl || ''}
                        onChange={(e) =>
                          setEditingChannel({
                            ...editingChannel,
                            webhookUrl: e.target.value,
                          })
                        }
                        placeholder="例如：https://example.com/api/webhook"
                      />
                    </div>
                  )}

                  <div className="flex items-center space-x-2">
                    <Switch
                      id="channel-enabled"
                      checked={editingChannel.enabled}
                      onCheckedChange={(checked) =>
                        setEditingChannel({
                          ...editingChannel,
                          enabled: checked,
                        })
                      }
                    />
                    <Label htmlFor="channel-enabled">启用此通知渠道</Label>
                  </div>
                </div>
              )}

              <DialogFooter>
                <Button
                  variant="outline"
                  onClick={() => setIsChannelDialogOpen(false)}>
                  取消
                </Button>
                <Button
                  onClick={() => saveNotificationChannel(editingChannel!)}>
                  保存
                </Button>
              </DialogFooter>
            </DialogContent>
          </Dialog>
        </TabsContent>

        <TabsContent value="history">
          <Card>
            <CardHeader>
              <CardTitle>警报历史记录</CardTitle>
              <CardDescription>
                查看过去触发的警报记录和处理状态
              </CardDescription>
            </CardHeader>
            <CardContent>
              {alertHistory.length === 0 ? (
                <div className="flex flex-col items-center justify-center py-8">
                  <Bell className="h-12 w-12 text-muted-foreground opacity-20" />
                  <h3 className="mt-4 text-lg font-medium">没有警报历史</h3>
                  <p className="mt-2 text-sm text-muted-foreground">
                    当警报被触发时，记录将显示在这里
                  </p>
                </div>
              ) : (
                <ScrollArea className="h-[400px]">
                  <div className="space-y-4">
                    {alertHistory.map((alert) => (
                      <Card key={alert.id}>
                        <CardContent className="p-4">
                          <div className="flex items-start justify-between">
                            <div className="flex items-start space-x-4">
                              <div className="mt-0.5">
                                <div
                                  className={`rounded-full p-2 ${
                                    alert.severity === 'critical'
                                      ? 'bg-red-100 text-red-600 dark:bg-red-900 dark:text-red-300'
                                      : alert.severity === 'high'
                                      ? 'bg-amber-100 text-amber-600 dark:bg-amber-900 dark:text-amber-300'
                                      : 'bg-blue-100 text-blue-600 dark:bg-blue-900 dark:text-blue-300'
                                  }`}>
                                  <AlertTriangle className="h-4 w-4" />
                                </div>
                              </div>
                              <div>
                                <div className="flex items-center space-x-2">
                                  <h4 className="font-medium">
                                    {alert.thresholdName}
                                  </h4>
                                  {getSeverityBadge(alert.severity)}
                                  {alert.acknowledged ? (
                                    <Badge
                                      variant="outline"
                                      className="text-muted-foreground">
                                      已确认
                                    </Badge>
                                  ) : (
                                    <Badge variant="secondary">未确认</Badge>
                                  )}
                                </div>
                                <p className="text-sm text-muted-foreground mt-1">
                                  {getMetricName(alert.metricType)}:{' '}
                                  {alert.value}
                                </p>
                                <p className="text-xs text-muted-foreground mt-1">
                                  触发时间: {formatDate(alert.timestamp)}
                                </p>
                              </div>
                            </div>
                            {!alert.acknowledged && (
                              <Button
                                variant="outline"
                                size="sm"
                                onClick={() => acknowledgeAlert(alert.id)}>
                                确认
                              </Button>
                            )}
                          </div>
                        </CardContent>
                      </Card>
                    ))}
                  </div>
                </ScrollArea>
              )}
            </CardContent>
          </Card>
        </TabsContent>
      </Tabs>
    </div>
  )
}
