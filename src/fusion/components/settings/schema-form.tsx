import { useState, useEffect } from 'react'
import { useForm } from 'react-hook-form'
import { Button } from '@/components/ui/button'
import { Label } from '@/components/ui/label'
import { Input } from '@/components/ui/input'
import { Textarea } from '@/components/ui/textarea'
import { Switch } from '@/components/ui/switch'
import { Slider } from '@/components/ui/slider'
import {
  Select,
  SelectContent,
  SelectItem,
  SelectTrigger,
  SelectValue,
} from '@/components/ui/select'
import {
  Accordion,
  AccordionContent,
  AccordionItem,
  AccordionTrigger,
} from '@/components/ui/accordion'
import {
  Card,
  CardContent,
  CardDescription,
  CardFooter,
  CardHeader,
  CardTitle,
} from '@/components/ui/card'
import {
  Collapsible,
  CollapsibleContent,
  CollapsibleTrigger,
} from '@/components/ui/collapsible'
import { ChevronDown, ChevronUp, Plus, Trash } from 'lucide-react'

interface SchemaFormProps {
  schema: any
  initialData: any
  onSubmit: (data: any) => void
  onCancel: () => void
  readOnly?: boolean
}

export function SchemaForm({
  schema,
  initialData,
  onSubmit,
  onCancel,
  readOnly = false,
}: SchemaFormProps) {
  const {
    register,
    handleSubmit,
    setValue,
    watch,
    control,
    formState: { errors },
  } = useForm({
    defaultValues: initialData || generateDefaultValues(schema),
  })

  // 确保初始值被正确设置
  useEffect(() => {
    if (initialData) {
      const flattenedData = flattenObject(initialData)
      Object.entries(flattenedData).forEach(([key, value]) => {
        setValue(key, value)
      })
    }
  }, [initialData, setValue])

  const renderSchema = (
    schema: any,
    path: string = '',
    required: boolean = false
  ) => {
    if (!schema) return null

    if (schema.type === 'object' && schema.properties) {
      return renderObjectProperties(schema, path, schema.required || [])
    }

    return (
      <SchemaField
        key={path}
        schema={schema}
        path={path}
        required={required}
        register={register}
        setValue={setValue}
        watch={watch}
        control={control}
        errors={errors}
        readOnly={readOnly}
      />
    )
  }

  const renderObjectProperties = (
    schema: any,
    basePath: string = '',
    requiredFields: string[] = []
  ) => {
    if (!schema.properties) return null

    return Object.entries(schema.properties).map(
      ([key, propertySchema]: [string, any]) => {
        const path = basePath ? `${basePath}.${key}` : key
        const isRequired = requiredFields.includes(key)

        return (
          <div key={path} className="mb-4">
            {renderSchema(propertySchema, path, isRequired)}
          </div>
        )
      }
    )
  }

  return (
    <form
      onSubmit={handleSubmit(onSubmit)}
      className="flex flex-col flex-grow min-h-0 space-y-6">
      {renderSchema(schema)}

      {!readOnly && (
        <div className="flex justify-end space-x-2 mt-auto pt-4">
          <Button type="button" variant="outline" onClick={onCancel}>
            取消
          </Button>
          <Button type="submit">保存</Button>
        </div>
      )}
    </form>
  )
}

// 根据Schema类型渲染不同的表单字段
function SchemaField({
  schema,
  path,
  required,
  register,
  setValue,
  watch,
  control,
  errors,
  readOnly,
}: {
  schema: any
  path: string
  required: boolean
  register: any
  setValue: any
  watch: any
  control: any
  errors: any
  readOnly: boolean
}) {
  const [expanded, setExpanded] = useState(true)
  const value = watch(path)

  // 根据Schema类型渲染不同的表单字段
  if (schema.type === 'object' && schema.properties) {
    return (
      <Collapsible
        open={expanded}
        onOpenChange={setExpanded}
        className="border rounded-md p-4 bg-white mb-4">
        <div className="flex items-center justify-between">
          <div>
            <h3 className="text-lg font-medium schema-section-title">
              {schema.title || path}
            </h3>
            {schema.description && (
              <p className="text-sm text-muted-foreground">
                {schema.description}
              </p>
            )}
          </div>
          <CollapsibleTrigger asChild>
            <Button variant="ghost" size="sm">
              {expanded ? (
                <ChevronUp className="h-4 w-4" />
              ) : (
                <ChevronDown className="h-4 w-4" />
              )}
            </Button>
          </CollapsibleTrigger>
        </div>
        <CollapsibleContent className="mt-4 schema-nested">
          {Object.entries(schema.properties).map(
            ([key, propertySchema]: [string, any]) => {
              const fieldPath = path ? `${path}.${key}` : key
              const isRequired = (schema.required || []).includes(key)
              return (
                <div key={fieldPath} className="schema-field">
                  <SchemaField
                    schema={propertySchema}
                    path={fieldPath}
                    required={isRequired}
                    register={register}
                    setValue={setValue}
                    watch={watch}
                    control={control}
                    errors={errors}
                    readOnly={readOnly}
                  />
                </div>
              )
            }
          )}
        </CollapsibleContent>
      </Collapsible>
    )
  }

  if (schema.type === 'array') {
    const itemSchema = schema.items
    return (
      <div className="border rounded-md p-4 bg-white mb-4">
        <div className="flex items-center justify-between mb-4">
          <div>
            <h3 className="text-lg font-medium schema-section-title">
              {schema.title || path}
            </h3>
            {schema.description && (
              <p className="text-sm text-muted-foreground">
                {schema.description}
              </p>
            )}
          </div>
          {!readOnly && (
            <Button
              type="button"
              size="sm"
              variant="outline"
              onClick={() => {
                const currentValue = watch(path) || []
                const newItem = generateDefaultValues(itemSchema)
                setValue(path, [...currentValue, newItem])
              }}>
              <Plus className="h-4 w-4 mr-1" /> 添加
            </Button>
          )}
        </div>

        <div className="space-y-4">
          {Array.isArray(value) &&
            value.map((_, index) => (
              <Card key={`${path}.${index}`} className="relative">
                <CardHeader className="py-3">
                  <div className="flex justify-between items-center">
                    <CardTitle className="text-md">项目 {index + 1}</CardTitle>
                    {!readOnly && (
                      <Button
                        type="button"
                        size="sm"
                        variant="outline"
                        className="h-8 w-8 p-0"
                        onClick={() => {
                          const currentValue = [...(watch(path) || [])]
                          currentValue.splice(index, 1)
                          setValue(path, currentValue)
                        }}>
                        <Trash className="h-4 w-4" />
                      </Button>
                    )}
                  </div>
                </CardHeader>
                <CardContent>
                  <div className="schema-nested">
                    <SchemaField
                      schema={itemSchema}
                      path={`${path}.${index}`}
                      required={false}
                      register={register}
                      setValue={setValue}
                      watch={watch}
                      control={control}
                      errors={errors}
                      readOnly={readOnly}
                    />
                  </div>
                </CardContent>
              </Card>
            ))}
        </div>
      </div>
    )
  }

  // 基本字段渲染
  const fieldId = path.replace(/\./g, '_')
  const errorMessage = errors[path]?.message

  // 文本输入类型
  if (schema.type === 'string' && !schema.enum) {
    // 多行文本
    if (schema.format === 'textarea') {
      return (
        <div className="space-y-2 schema-field">
          <div className="flex items-center">
            <Label htmlFor={fieldId} className={required ? 'required' : ''}>
              {schema.title || path}
            </Label>
          </div>
          {schema.description && (
            <p className="text-sm text-muted-foreground">
              {schema.description}
            </p>
          )}
          <Textarea
            id={fieldId}
            {...register(path, {
              required: required ? '此字段为必填项' : false,
            })}
            readOnly={readOnly}
            className={errorMessage ? 'border-red-500' : ''}
          />
          {errorMessage && (
            <p className="text-sm text-red-500">{errorMessage}</p>
          )}
        </div>
      )
    }

    // 密码类型
    if (schema.format === 'password') {
      return (
        <div className="space-y-2 schema-field">
          <div className="flex items-center">
            <Label htmlFor={fieldId} className={required ? 'required' : ''}>
              {schema.title || path}
            </Label>
          </div>
          {schema.description && (
            <p className="text-sm text-muted-foreground">
              {schema.description}
            </p>
          )}
          <Input
            id={fieldId}
            type="password"
            {...register(path, {
              required: required ? '此字段为必填项' : false,
            })}
            readOnly={readOnly}
            className={errorMessage ? 'border-red-500' : ''}
          />
          {errorMessage && (
            <p className="text-sm text-red-500">{errorMessage}</p>
          )}
        </div>
      )
    }

    // 标准文本输入
    return (
      <div className="space-y-2 schema-field">
        <div className="flex items-center">
          <Label htmlFor={fieldId} className={required ? 'required' : ''}>
            {schema.title || path}
          </Label>
        </div>
        {schema.description && (
          <p className="text-sm text-muted-foreground">{schema.description}</p>
        )}
        <Input
          id={fieldId}
          type="text"
          {...register(path, { required: required ? '此字段为必填项' : false })}
          readOnly={readOnly}
          className={errorMessage ? 'border-red-500' : ''}
        />
        {errorMessage && <p className="text-sm text-red-500">{errorMessage}</p>}
      </div>
    )
  }

  // 数字输入类型
  if (schema.type === 'number' || schema.type === 'integer') {
    // 滑块类型
    if (
      typeof schema.minimum === 'number' &&
      typeof schema.maximum === 'number'
    ) {
      return (
        <div className="space-y-4 schema-field">
          <div className="flex items-center justify-between">
            <Label htmlFor={fieldId} className={required ? 'required' : ''}>
              {schema.title || path}
            </Label>
            <span className="text-sm font-medium">{value}</span>
          </div>
          {schema.description && (
            <p className="text-sm text-muted-foreground">
              {schema.description}
            </p>
          )}
          <Slider
            id={fieldId}
            min={schema.minimum}
            max={schema.maximum}
            step={schema.type === 'integer' ? 1 : 0.1}
            value={[parseFloat(value) || schema.minimum]}
            onValueChange={(values) => setValue(path, values[0])}
            disabled={readOnly}
          />
          {errorMessage && (
            <p className="text-sm text-red-500">{errorMessage}</p>
          )}
        </div>
      )
    }

    // 标准数字输入
    return (
      <div className="space-y-2 schema-field">
        <div className="flex items-center">
          <Label htmlFor={fieldId} className={required ? 'required' : ''}>
            {schema.title || path}
          </Label>
        </div>
        {schema.description && (
          <p className="text-sm text-muted-foreground">{schema.description}</p>
        )}
        <Input
          id={fieldId}
          type="number"
          min={schema.minimum}
          max={schema.maximum}
          step={schema.type === 'integer' ? 1 : 0.1}
          {...register(path, {
            required: required ? '此字段为必填项' : false,
            valueAsNumber: true,
          })}
          readOnly={readOnly}
          className={errorMessage ? 'border-red-500' : ''}
        />
        {errorMessage && <p className="text-sm text-red-500">{errorMessage}</p>}
      </div>
    )
  }

  // 布尔类型
  if (schema.type === 'boolean') {
    return (
      <div className="flex items-center justify-between space-y-0 schema-field">
        <div>
          <Label htmlFor={fieldId} className={required ? 'required' : ''}>
            {schema.title || path}
          </Label>
          {schema.description && (
            <p className="text-sm text-muted-foreground">
              {schema.description}
            </p>
          )}
        </div>
        <Switch
          id={fieldId}
          checked={!!value}
          onCheckedChange={(checked) => setValue(path, checked)}
          disabled={readOnly}
        />
      </div>
    )
  }

  // 枚举类型 (下拉选择)
  if (schema.enum) {
    return (
      <div className="space-y-2 schema-field">
        <div className="flex items-center">
          <Label htmlFor={fieldId} className={required ? 'required' : ''}>
            {schema.title || path}
          </Label>
        </div>
        {schema.description && (
          <p className="text-sm text-muted-foreground">{schema.description}</p>
        )}
        <Select
          value={value}
          onValueChange={(value) => setValue(path, value)}
          disabled={readOnly}>
          <SelectTrigger
            id={fieldId}
            className={errorMessage ? 'border-red-500' : ''}>
            <SelectValue placeholder="选择选项" />
          </SelectTrigger>
          <SelectContent>
            {schema.enum.map((option: any) => (
              <SelectItem key={option} value={option}>
                {option}
              </SelectItem>
            ))}
          </SelectContent>
        </Select>
        {errorMessage && <p className="text-sm text-red-500">{errorMessage}</p>}
      </div>
    )
  }

  // 默认输入类型
  return (
    <div className="space-y-2 schema-field">
      <div className="flex items-center">
        <Label htmlFor={fieldId} className={required ? 'required' : ''}>
          {schema.title || path}
        </Label>
      </div>
      {schema.description && (
        <p className="text-sm text-muted-foreground">{schema.description}</p>
      )}
      <Input
        id={fieldId}
        {...register(path, { required: required ? '此字段为必填项' : false })}
        readOnly={readOnly}
        className={errorMessage ? 'border-red-500' : ''}
      />
      {errorMessage && <p className="text-sm text-red-500">{errorMessage}</p>}
    </div>
  )
}

// 辅助函数：扁平化对象
function flattenObject(obj: any, prefix = '') {
  return Object.keys(obj).reduce((acc, k) => {
    const pre = prefix.length ? `${prefix}.` : ''
    if (
      typeof obj[k] === 'object' &&
      obj[k] !== null &&
      !Array.isArray(obj[k])
    ) {
      Object.assign(acc, flattenObject(obj[k], `${pre}${k}`))
    } else {
      acc[`${pre}${k}`] = obj[k]
    }
    return acc
  }, {} as any)
}

// 辅助函数：根据Schema生成默认值
function generateDefaultValues(schema: any): any {
  if (!schema) return null

  if (schema.type === 'object' && schema.properties) {
    const result: any = {}
    Object.entries(schema.properties).forEach(
      ([key, propSchema]: [string, any]) => {
        result[key] =
          propSchema.default !== undefined
            ? propSchema.default
            : generateDefaultValues(propSchema)
      }
    )
    return result
  }

  if (schema.type === 'array' && schema.items) {
    return schema.default || []
  }

  switch (schema.type) {
    case 'string':
      return schema.default || ''
    case 'number':
    case 'integer':
      return schema.default !== undefined ? schema.default : 0
    case 'boolean':
      return schema.default !== undefined ? schema.default : false
    default:
      return schema.default !== undefined ? schema.default : null
  }
}
