/**
 * 工具对话框基类组件
 *
 * 提供通用的工具对话框功能，包括标准布局、全屏模式和导航
 * 其他工具对话框可以基于此组件进行扩展
 */

import { useState, useEffect, ReactNode } from 'react'
import {
  Dialog,
  DialogContent,
  DialogHeader,
  DialogTitle,
  DialogFooter,
  DialogDescription,
} from '@/components/ui/dialog'
import { Button } from '@/components/ui/button'
import { ExternalLink } from 'lucide-react'
import { useNavigate } from 'react-router-dom'

export interface BaseToolDialogProps {
  /** 对话框是否打开 */
  open: boolean
  /** 对话框开关状态变化回调 */
  onOpenChange: (open: boolean) => void
  /** 工具标题 */
  title: string
  /** 工具图标 */
  icon: ReactNode
  /** 工具内容组件 */
  children: ReactNode
  /** 工具路径，用于导航到完整工具页面 */
  toolPath: string
  /** 工具描述，可选 */
  description?: string
  /** 对话框最大宽度，默认为4xl */
  maxWidth?:
    | 'sm'
    | 'md'
    | 'lg'
    | 'xl'
    | '2xl'
    | '3xl'
    | '4xl'
    | '5xl'
    | '6xl'
    | '7xl'
}

export function BaseToolDialog({
  open,
  onOpenChange,
  title,
  icon,
  children,
  toolPath,
  description = '使用此工具执行相关操作，或打开完整工具获取更多功能',
  maxWidth = '4xl',
}: BaseToolDialogProps) {
  const navigate = useNavigate()
  const [isFullscreen, setIsFullscreen] = useState(false)

  // 处理全屏模式
  useEffect(() => {
    if (!open) {
      setIsFullscreen(false)
    }
  }, [open])

  // 跳转到完整工具页面
  const goToFullTool = () => {
    onOpenChange(false)
    navigate(toolPath)
  }

  if (isFullscreen) {
    return (
      <div className="fixed inset-0 z-50 bg-background flex flex-col">
        <div className="flex items-center justify-between p-4 border-b">
          <div className="flex items-center gap-2">
            {icon}
            <h2 className="text-lg font-semibold">{title}</h2>
          </div>
          <div className="flex items-center gap-2">
            <Button variant="outline" onClick={() => setIsFullscreen(false)}>
              退出全屏
            </Button>
            <Button variant="outline" onClick={goToFullTool}>
              <ExternalLink className="h-4 w-4 mr-2" />
              打开完整工具
            </Button>
            <Button variant="ghost" onClick={() => onOpenChange(false)}>
              关闭
            </Button>
          </div>
        </div>
        <div className="flex-1 p-6 overflow-auto">{children}</div>
      </div>
    )
  }

  return (
    <Dialog open={open} onOpenChange={onOpenChange}>
      <DialogContent
        className={`max-w-${maxWidth} w-[90vw] max-h-[90vh] flex flex-col`}>
        <DialogHeader>
          <DialogTitle className="flex items-center gap-2">
            {icon}
            {title}
          </DialogTitle>
          <DialogDescription>{description}</DialogDescription>
        </DialogHeader>
        <div className="flex-1 overflow-auto py-4">{children}</div>
        <DialogFooter className="flex justify-between items-center border-t pt-2 mt-4">
          <Button variant="outline" onClick={() => setIsFullscreen(true)}>
            全屏模式
          </Button>
          <div className="flex gap-2">
            <Button variant="outline" onClick={goToFullTool}>
              <ExternalLink className="h-4 w-4 mr-2" />
              打开完整工具
            </Button>
            <Button onClick={() => onOpenChange(false)}>关闭</Button>
          </div>
        </DialogFooter>
      </DialogContent>
    </Dialog>
  )
}
