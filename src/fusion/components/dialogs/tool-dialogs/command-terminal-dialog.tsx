/**
 * 命令终端工具对话框组件
 *
 * 复用debug-tools中的命令终端工具，提供给floating-assistant使用
 * 该组件封装了命令终端功能，以对话框的形式展示
 */

import { Terminal } from 'lucide-react'
import { CommandTerminal } from '@/components/debug-tools/command-terminal'
import { BaseToolDialog } from './base-tool-dialog'
import { memo, useState, useEffect } from 'react'

// 使用 memo 包装 CommandTerminal 组件以避免不必要的重新渲染
const MemoizedCommandTerminal = memo(CommandTerminal)

interface CommandTerminalDialogProps {
  open: boolean
  onOpenChange: (open: boolean) => void
}

export function CommandTerminalDialog({
  open,
  onOpenChange,
}: CommandTerminalDialogProps) {
  // 使用状态来控制组件的渲染，避免在对话框关闭时仍然渲染组件
  const [shouldRender, setShouldRender] = useState(false)

  // 当对话框打开状态变化时，更新渲染状态
  useEffect(() => {
    if (open) {
      setShouldRender(true)
    } else {
      // 延迟移除组件，确保关闭动画完成
      const timer = setTimeout(() => {
        setShouldRender(false)
      }, 300)
      return () => clearTimeout(timer)
    }
  }, [open])

  return (
    <BaseToolDialog
      open={open}
      onOpenChange={onOpenChange}
      title="命令终端"
      icon={<Terminal className="h-5 w-5" />}
      description="使用命令行界面执行系统命令，查看系统状态和管理设备"
      toolPath="/debug-tools/command-terminal"
      maxWidth="4xl">
      {shouldRender && <MemoizedCommandTerminal />}
    </BaseToolDialog>
  )
}
