# 调试工具布局组件使用指南

## 布局结构

调试工具模块采用了统一的布局结构，包括以下几个关键组件：

1. `SharedDebugToolLayout` - 共享的调试工具布局容器，为所有子页面提供 MainLayout 包装
2. `DataToolLayout` - 调试工具内容布局组件，提供侧边栏和内容区域的标准结构

## 使用方法

### 创建新的调试工具页面

1. 在 `app/debug-tools/` 目录下创建新的工具目录和页面
2. 使用 `DataToolLayout` 组件作为页面的根组件
3. 不需要再包含 `MainLayout`，因为路由已经通过 `SharedDebugToolLayout` 提供了

示例代码：

```tsx
import { YourIcon } from 'lucide-react'
import { DataToolLayout } from '@/components/debug-tools/data-tool-layout'
import { YourToolComponent } from '@/components/debug-tools/your-tool-component'

export default function YourToolPage() {
  return (
    <DataToolLayout
      title="您的工具名称"
      description="工具简短描述"
      toolIcon={
        <div className="p-2 rounded-md bg-blue-100 text-blue-700 dark:bg-blue-950 dark:text-blue-300">
          <YourIcon className="h-5 w-5" />
        </div>
      }
      sidebar={
        <div className="space-y-4">
          {/* 侧边栏内容 */}
          <div>
            <h3 className="text-sm font-medium mb-2">使用说明</h3>
            <ul className="text-sm space-y-1 text-muted-foreground">
              <li>• 说明项目1</li>
              <li>• 说明项目2</li>
            </ul>
          </div>
        </div>
      }>
      {/* 主要工具内容 */}
      <YourToolComponent />
    </DataToolLayout>
  )
}
```

### DataToolLayout 组件属性

| 属性        | 类型      | 描述         | 是否必须                  |
| ----------- | --------- | ------------ | ------------------------- |
| title       | string    | 工具标题     | 是                        |
| description | string    | 工具描述     | 是                        |
| children    | ReactNode | 工具主要内容 | 是                        |
| sidebar     | ReactNode | 侧边栏内容   | 否                        |
| toolIcon    | ReactNode | 工具图标     | 否                        |
| backLink    | string    | 返回链接     | 否，默认为 "/debug-tools" |

## 路由配置

所有调试工具都应该配置为 `/debug-tools` 的子路由。路由配置在 `router.tsx` 文件中：

```tsx
<Route path="/debug-tools" element={<DebugToolsPage />}>
  {/* 添加新的工具路由 */}
  <Route path="your-tool" element={<YourToolPage />} />
</Route>
```

## 最佳实践

1. **保持一致的样式**：使用提供的组件和样式，保持整个调试工具集的视觉一致性
2. **合理组织侧边栏**：在侧边栏中提供简洁的使用说明、快捷操作和注意事项
3. **响应式设计**：确保工具在各种屏幕尺寸下都能正常工作
4. **清晰的工具图标**：选择能够清晰表达工具功能的图标
5. **简洁的描述**：提供简短但有信息量的工具描述

## 参考示例

可以参考 `app/debug-tools/shared/page.tsx` 中的模板示例，或者查看其他已实现的工具页面，如 `json-formatter` 或 `data-converter`。
