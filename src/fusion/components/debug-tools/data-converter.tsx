/**
 * 数据转换工具UI组件
 *
 * 提供多种数据格式之间的转换功能的用户界面
 * 支持文本、十六进制、二进制、Base64等格式
 */

import { useDataConverter, type DataFormat } from '@/hooks/use-data-converter'
import { Button } from '@/components/ui/button'
import { Input } from '@/components/ui/input'
import { Label } from '@/components/ui/label'
import { Textarea } from '@/components/ui/textarea'
import { Switch } from '@/components/ui/switch'
import { Alert, AlertDescription } from '@/components/ui/alert'
import { Card, CardContent, CardHeader, CardTitle } from '@/components/ui/card'
import {
  Select,
  SelectContent,
  SelectItem,
  SelectTrigger,
  SelectValue,
} from '@/components/ui/select'
import { ArrowRightLeft, Copy, RefreshCw, Trash2, Check } from 'lucide-react'
import { useState } from 'react'

export function DataConverter() {
  const {
    // 输入状态
    inputData,
    setInputData,
    inputFormat,
    setInputFormat,

    // 输出状态
    outputFormat,
    setOutputFormat,
    conversionResult,

    // 转换选项
    upperCaseHex,
    setUpperCaseHex,
    groupBytes,
    setGroupBytes,
    bytesPerGroup,
    setBytesPerGroup,
    addPrefix,
    setAddPrefix,
    prettyPrintJson,
    setPrettyPrintJson,

    // 操作方法
    convert,
    swapFormats,
    copyResultToClipboard,
    clearAll,
  } = useDataConverter()

  // 复制按钮状态
  const [copied, setCopied] = useState(false)

  // 处理复制
  const handleCopy = () => {
    if (copyResultToClipboard()) {
      setCopied(true)
      setTimeout(() => setCopied(false), 2000)
    }
  }

  // 格式选项
  const formatOptions: { value: DataFormat; label: string }[] = [
    { value: 'text', label: '文本 (UTF-8)' },
    { value: 'hex', label: '十六进制' },
    { value: 'binary', label: '二进制' },
    { value: 'base64', label: 'Base64' },
    { value: 'url', label: 'URL 编码' },
    { value: 'decimal', label: '十进制' },
    { value: 'json', label: 'JSON' },
    { value: 'xml', label: 'XML' },
  ]

  return (
    <div className="grid grid-cols-1 gap-6">
      {/* 转换器主体 */}
      <div className="grid grid-cols-1 lg:grid-cols-2 gap-6">
        {/* 输入面板 */}
        <Card>
          <CardHeader className="pb-3">
            <div className="flex justify-between items-center">
              <CardTitle>输入数据</CardTitle>
              <Select
                value={inputFormat}
                onValueChange={(value) => setInputFormat(value as DataFormat)}>
                <SelectTrigger className="w-[180px]">
                  <SelectValue placeholder="选择输入格式" />
                </SelectTrigger>
                <SelectContent>
                  {formatOptions.map((option) => (
                    <SelectItem key={option.value} value={option.value}>
                      {option.label}
                    </SelectItem>
                  ))}
                </SelectContent>
              </Select>
            </div>
          </CardHeader>
          <CardContent>
            <Textarea
              placeholder={`请输入${
                formatOptions.find((f) => f.value === inputFormat)?.label || ''
              }数据...`}
              value={inputData}
              onChange={(e) => setInputData(e.target.value)}
              className="min-h-[300px] font-mono"
            />
            <div className="flex justify-between mt-4">
              <Button variant="outline" onClick={clearAll} size="sm">
                <Trash2 className="h-4 w-4 mr-2" />
                清空
              </Button>
              <Button onClick={convert} size="sm">
                <RefreshCw className="h-4 w-4 mr-2" />
                转换
              </Button>
            </div>
          </CardContent>
        </Card>

        {/* 输出面板 */}
        <Card>
          <CardHeader className="pb-3">
            <div className="flex justify-between items-center">
              <CardTitle>转换结果</CardTitle>
              <Select
                value={outputFormat}
                onValueChange={(value) => setOutputFormat(value as DataFormat)}>
                <SelectTrigger className="w-[180px]">
                  <SelectValue placeholder="选择输出格式" />
                </SelectTrigger>
                <SelectContent>
                  {formatOptions.map((option) => (
                    <SelectItem key={option.value} value={option.value}>
                      {option.label}
                    </SelectItem>
                  ))}
                </SelectContent>
              </Select>
            </div>
          </CardHeader>
          <CardContent>
            {conversionResult.error ? (
              <Alert variant="destructive" className="mb-4">
                <AlertDescription>{conversionResult.error}</AlertDescription>
              </Alert>
            ) : null}

            <Textarea
              value={conversionResult.result}
              readOnly
              className="min-h-[300px] font-mono"
            />

            <div className="flex justify-between mt-4">
              <Button variant="outline" onClick={swapFormats} size="sm">
                <ArrowRightLeft className="h-4 w-4 mr-2" />
                交换格式
              </Button>

              <Button
                variant="outline"
                onClick={handleCopy}
                size="sm"
                disabled={!conversionResult.result}>
                {copied ? (
                  <>
                    <Check className="h-4 w-4 mr-2" />
                    已复制
                  </>
                ) : (
                  <>
                    <Copy className="h-4 w-4 mr-2" />
                    复制结果
                  </>
                )}
              </Button>
            </div>
          </CardContent>
        </Card>
      </div>

      {/* 转换选项 */}
      <Card>
        <CardHeader>
          <CardTitle>转换选项</CardTitle>
        </CardHeader>
        <CardContent>
          <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-6">
            {/* 十六进制选项 */}
            <div className="space-y-4">
              <h3 className="font-medium">十六进制选项</h3>
              <div className="flex items-center space-x-2">
                <Switch
                  id="upperCaseHex"
                  checked={upperCaseHex}
                  onCheckedChange={setUpperCaseHex}
                />
                <Label htmlFor="upperCaseHex">大写字母 (A-F)</Label>
              </div>

              <div className="flex items-center space-x-2">
                <Switch
                  id="addPrefix"
                  checked={addPrefix}
                  onCheckedChange={setAddPrefix}
                />
                <Label htmlFor="addPrefix">添加 0x 前缀</Label>
              </div>
            </div>

            {/* 分组选项 */}
            <div className="space-y-4">
              <h3 className="font-medium">分组选项</h3>
              <div className="flex items-center space-x-2">
                <Switch
                  id="groupBytes"
                  checked={groupBytes}
                  onCheckedChange={setGroupBytes}
                />
                <Label htmlFor="groupBytes">分组显示</Label>
              </div>

              <div className="flex items-center space-x-2">
                <Label htmlFor="bytesPerGroup">每组字节数</Label>
                <Input
                  id="bytesPerGroup"
                  type="number"
                  min="1"
                  max="8"
                  value={bytesPerGroup}
                  onChange={(e) => setBytesPerGroup(Number(e.target.value))}
                  disabled={!groupBytes}
                  className="w-16 h-8"
                />
              </div>
            </div>

            {/* JSON/XML选项 */}
            <div className="space-y-4">
              <h3 className="font-medium">JSON/XML选项</h3>
              <div className="flex items-center space-x-2">
                <Switch
                  id="prettyPrintJson"
                  checked={prettyPrintJson}
                  onCheckedChange={setPrettyPrintJson}
                />
                <Label htmlFor="prettyPrintJson">美化格式</Label>
              </div>
            </div>
          </div>
        </CardContent>
      </Card>

      {/* 格式说明 */}
      <Card>
        <CardHeader>
          <CardTitle>格式说明</CardTitle>
        </CardHeader>
        <CardContent>
          <div className="grid grid-cols-1 md:grid-cols-2 gap-4 text-sm">
            <div>
              <h4 className="font-medium mb-2">文本 (UTF-8)</h4>
              <p className="text-muted-foreground">
                标准UTF-8编码的文本，支持多语言字符和特殊符号。
              </p>
            </div>

            <div>
              <h4 className="font-medium mb-2">十六进制</h4>
              <p className="text-muted-foreground">
                使用0-9和A-F表示的十六进制数据，每个字节用两个字符表示。
              </p>
            </div>

            <div>
              <h4 className="font-medium mb-2">二进制</h4>
              <p className="text-muted-foreground">
                由0和1组成的二进制数据，每个字节用8位表示。
              </p>
            </div>

            <div>
              <h4 className="font-medium mb-2">Base64</h4>
              <p className="text-muted-foreground">
                使用64个可打印字符表示二进制数据的编码方式，常用于在文本中嵌入二进制数据。
              </p>
            </div>

            <div>
              <h4 className="font-medium mb-2">URL编码</h4>
              <p className="text-muted-foreground">
                将特殊字符转换为%后跟两位十六进制数的格式，用于在URL中安全传输数据。
              </p>
            </div>

            <div>
              <h4 className="font-medium mb-2">十进制</h4>
              <p className="text-muted-foreground">
                使用0-255的十进制数字表示每个字节，用逗号或空格分隔。
              </p>
            </div>

            <div>
              <h4 className="font-medium mb-2">JSON</h4>
              <p className="text-muted-foreground">
                JavaScript对象表示法，用于结构化数据的文本格式，支持对象、数组、字符串等。
              </p>
            </div>

            <div>
              <h4 className="font-medium mb-2">XML</h4>
              <p className="text-muted-foreground">
                可扩展标记语言，使用标签来描述数据结构，广泛用于配置文件和数据交换。
              </p>
            </div>
          </div>
        </CardContent>
      </Card>
    </div>
  )
}
