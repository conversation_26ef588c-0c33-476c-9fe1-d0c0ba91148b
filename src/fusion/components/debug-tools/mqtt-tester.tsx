/**
 * MQTT测试工具UI组件
 *
 * 提供MQTT连接、发布、订阅和消息管理功能的用户界面
 * 支持多种QoS级别和保留消息
 */

import {
  useMqttTester,
  type MqttMessage,
  type MqttSubscription,
} from '@/hooks/use-mqtt-tester'
import { Button } from '@/components/ui/button'
import { Input } from '@/components/ui/input'
import { Label } from '@/components/ui/label'
import { Tabs, TabsContent, TabsList, TabsTrigger } from '@/components/ui/tabs'
import { Textarea } from '@/components/ui/textarea'
import { Switch } from '@/components/ui/switch'
import { Alert, AlertDescription } from '@/components/ui/alert'
import { Card, CardContent, CardHeader, CardTitle } from '@/components/ui/card'
import {
  Select,
  SelectContent,
  SelectItem,
  SelectTrigger,
  SelectValue,
} from '@/components/ui/select'
import { ScrollArea } from '@/components/ui/scroll-area'
import { Badge } from '@/components/ui/badge'
import {
  Loader2,
  Play,
  Square,
  Send,
  Trash,
  Download,
  X,
  MessageSquare,
  Wifi,
  ArrowDown,
  ArrowUp,
} from 'lucide-react'
import { formatDistanceToNow } from 'date-fns'
import { cn } from '@/lib/utils'

export function MqttTester() {
  const {
    // 连接设置
    broker,
    setBroker,
    port,
    setPort,
    clientId,
    setClientId,
    username,
    setUsername,
    password,
    setPassword,
    useSSL,
    setUseSSL,

    // 连接状态
    isConnected,
    connectionStatus,
    connectionError,
    isMqttLoaded,

    // 发布设置
    publishTopic,
    setPublishTopic,
    publishPayload,
    setPublishPayload,
    publishQoS,
    setPublishQoS,
    publishRetained,
    setPublishRetained,

    // 订阅设置
    subscribeTopic,
    setSubscribeTopic,
    subscribeQoS,
    setSubscribeQoS,
    subscriptions,

    // 消息历史
    messages,

    // 方法
    connect,
    disconnect,
    publishMessage,
    subscribe,
    unsubscribeTopic,
    clearMessages,
    exportMessages,
  } = useMqttTester()

  return (
    <div className="grid grid-cols-1 gap-6">
      {/* 连接设置卡片 */}
      <Card>
        <CardHeader className="py-4">
          <CardTitle>连接设置</CardTitle>
        </CardHeader>
        <CardContent className="space-y-4">
          <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
            <div className="space-y-2">
              <Label htmlFor="broker">代理地址</Label>
              <Input
                id="broker"
                placeholder="例如: broker.emqx.io"
                value={broker}
                onChange={(e) => setBroker(e.target.value)}
                disabled={isConnected}
                className="h-10"
              />
            </div>

            <div className="space-y-2">
              <Label htmlFor="port">端口</Label>
              <Input
                id="port"
                placeholder="例如: 8083, 8084 (SSL)"
                value={port}
                onChange={(e) => setPort(e.target.value)}
                disabled={isConnected}
                className="h-10"
              />
            </div>
          </div>

          <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
            <div className="space-y-2">
              <Label htmlFor="clientId">客户端ID</Label>
              <Input
                id="clientId"
                placeholder="唯一的客户端标识符"
                value={clientId}
                onChange={(e) => setClientId(e.target.value)}
                disabled={isConnected}
                className="h-10"
              />
            </div>

            <div className="flex items-center space-x-2 pt-8">
              <Switch
                id="useSSL"
                checked={useSSL}
                onCheckedChange={setUseSSL}
                disabled={isConnected}
              />
              <Label htmlFor="useSSL">使用SSL/TLS</Label>
            </div>
          </div>

          <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
            <div className="space-y-2">
              <Label htmlFor="username">用户名 (可选)</Label>
              <Input
                id="username"
                placeholder="用户名"
                value={username}
                onChange={(e) => setUsername(e.target.value)}
                disabled={isConnected}
                className="h-10"
              />
            </div>

            <div className="space-y-2">
              <Label htmlFor="password">密码 (可选)</Label>
              <Input
                id="password"
                type="password"
                placeholder="密码"
                value={password}
                onChange={(e) => setPassword(e.target.value)}
                disabled={isConnected}
                className="h-10"
              />
            </div>
          </div>
        </CardContent>
        <div className="p-4 border-t flex justify-between items-center">
          <div>
            {!isConnected ? (
              <Button
                size="lg"
                onClick={connect}
                disabled={!broker || !port || !clientId || !isMqttLoaded}
                className="h-10">
                {!isMqttLoaded ? (
                  <>
                    <Loader2 className="mr-2 h-4 w-4 animate-spin" />
                    加载中...
                  </>
                ) : (
                  <>
                    <Play className="mr-2 h-4 w-4" />
                    连接
                  </>
                )}
              </Button>
            ) : (
              <Button
                variant="destructive"
                size="lg"
                onClick={disconnect}
                className="h-10">
                <Square className="mr-2 h-4 w-4" />
                断开
              </Button>
            )}
          </div>

          <div className="flex items-center space-x-2">
            <MessageSquare
              className={cn(
                'h-5 w-5',
                isConnected ? 'text-green-500' : 'text-muted-foreground'
              )}
            />
            <span className="text-sm">{connectionStatus}</span>
          </div>
        </div>
      </Card>

      {/* 错误提示 */}
      {connectionError && (
        <Alert variant="destructive">
          <AlertDescription>{connectionError}</AlertDescription>
        </Alert>
      )}

      {/* 主要功能区 */}
      <Tabs defaultValue="publish" className="w-full">
        <TabsList className="grid grid-cols-3">
          <TabsTrigger value="publish">发布</TabsTrigger>
          <TabsTrigger value="subscribe">订阅</TabsTrigger>
          <TabsTrigger value="messages">消息历史</TabsTrigger>
        </TabsList>

        {/* 发布选项卡 */}
        <TabsContent value="publish" className="space-y-4 mt-2">
          <Card>
            <CardContent className="pt-6 space-y-4">
              <div className="space-y-2">
                <Label htmlFor="publishTopic">主题</Label>
                <Input
                  id="publishTopic"
                  placeholder="例如: device/temperature"
                  value={publishTopic}
                  onChange={(e) => setPublishTopic(e.target.value)}
                  disabled={!isConnected}
                  className="h-10"
                />
              </div>

              <div className="space-y-2">
                <Label htmlFor="publishPayload">消息内容</Label>
                <Textarea
                  id="publishPayload"
                  placeholder="输入消息内容..."
                  value={publishPayload}
                  onChange={(e) => setPublishPayload(e.target.value)}
                  disabled={!isConnected}
                  className="min-h-[150px]"
                />
              </div>

              <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
                <div className="space-y-2">
                  <Label htmlFor="publishQoS">QoS级别</Label>
                  <Select
                    value={publishQoS.toString()}
                    onValueChange={(value) => setPublishQoS(parseInt(value))}
                    disabled={!isConnected}>
                    <SelectTrigger id="publishQoS" className="h-10">
                      <SelectValue placeholder="选择QoS级别" />
                    </SelectTrigger>
                    <SelectContent>
                      <SelectItem value="0">0 - 最多一次</SelectItem>
                      <SelectItem value="1">1 - 至少一次</SelectItem>
                      <SelectItem value="2">2 - 恰好一次</SelectItem>
                    </SelectContent>
                  </Select>
                </div>

                <div className="flex items-center space-x-2 pt-8">
                  <Switch
                    id="publishRetained"
                    checked={publishRetained}
                    onCheckedChange={setPublishRetained}
                    disabled={!isConnected}
                  />
                  <Label htmlFor="publishRetained">保留消息</Label>
                </div>
              </div>

              <Button
                className="w-full"
                onClick={publishMessage}
                disabled={!isConnected || !publishTopic}>
                <Send className="mr-2 h-4 w-4" />
                发送消息
              </Button>
            </CardContent>
          </Card>
        </TabsContent>

        {/* 订阅选项卡 */}
        <TabsContent value="subscribe" className="space-y-4 mt-2">
          <Card>
            <CardContent className="pt-6 space-y-4">
              <div className="grid grid-cols-1 md:grid-cols-3 gap-4">
                <div className="md:col-span-2 space-y-2">
                  <Label htmlFor="subscribeTopic">主题</Label>
                  <Input
                    id="subscribeTopic"
                    placeholder="例如: device/# 或 sensor/+/temperature"
                    value={subscribeTopic}
                    onChange={(e) => setSubscribeTopic(e.target.value)}
                    disabled={!isConnected}
                    className="h-10"
                  />
                </div>

                <div className="space-y-2">
                  <Label htmlFor="subscribeQoS">QoS级别</Label>
                  <Select
                    value={subscribeQoS.toString()}
                    onValueChange={(value) => setSubscribeQoS(parseInt(value))}
                    disabled={!isConnected}>
                    <SelectTrigger id="subscribeQoS" className="h-10">
                      <SelectValue placeholder="选择QoS级别" />
                    </SelectTrigger>
                    <SelectContent>
                      <SelectItem value="0">0 - 最多一次</SelectItem>
                      <SelectItem value="1">1 - 至少一次</SelectItem>
                      <SelectItem value="2">2 - 恰好一次</SelectItem>
                    </SelectContent>
                  </Select>
                </div>
              </div>

              <Button
                className="w-full"
                onClick={subscribe}
                disabled={!isConnected || !subscribeTopic}>
                <MessageSquare className="mr-2 h-4 w-4" />
                订阅主题
              </Button>
            </CardContent>
          </Card>

          <Card>
            <CardHeader className="py-4">
              <CardTitle>当前订阅</CardTitle>
            </CardHeader>
            <CardContent>
              {subscriptions.length === 0 ? (
                <div className="text-center text-muted-foreground py-4">
                  暂无订阅
                </div>
              ) : (
                <ScrollArea className="h-[200px]">
                  <div className="space-y-2">
                    {subscriptions.map((subscription) => (
                      <div
                        key={subscription.id}
                        className="flex items-center justify-between p-3 border rounded-md">
                        <div className="flex items-center">
                          <MessageSquare className="h-4 w-4 mr-2 text-blue-500" />
                          <span className="font-medium">
                            {subscription.topic}
                          </span>
                          <Badge variant="outline" className="ml-2">
                            QoS {subscription.qos}
                          </Badge>
                        </div>
                        <Button
                          variant="ghost"
                          size="sm"
                          onClick={() => unsubscribeTopic(subscription.id)}>
                          <X className="h-4 w-4" />
                        </Button>
                      </div>
                    ))}
                  </div>
                </ScrollArea>
              )}
            </CardContent>
          </Card>
        </TabsContent>

        {/* 消息历史选项卡 */}
        <TabsContent value="messages" className="space-y-4 mt-2">
          <Card>
            <CardHeader className="py-4 flex flex-row items-center justify-between">
              <CardTitle>消息历史</CardTitle>
              <div className="flex space-x-2">
                <Button variant="outline" size="sm" onClick={exportMessages}>
                  <Download className="h-4 w-4 mr-2" />
                  导出
                </Button>
                <Button variant="outline" size="sm" onClick={clearMessages}>
                  <Trash className="h-4 w-4 mr-2" />
                  清空
                </Button>
              </div>
            </CardHeader>
            <CardContent>
              {messages.length === 0 ? (
                <div className="text-center text-muted-foreground py-4">
                  暂无消息
                </div>
              ) : (
                <ScrollArea className="h-[400px]">
                  <div className="space-y-3">
                    {messages.map((message) => (
                      <Card key={message.id} className="overflow-hidden">
                        <div
                          className={cn(
                            'px-4 py-2',
                            message.direction === 'publish'
                              ? 'bg-blue-50 dark:bg-blue-950'
                              : 'bg-green-50 dark:bg-green-950'
                          )}>
                          <div className="flex items-center justify-between">
                            <div className="flex items-center">
                              {message.direction === 'publish' ? (
                                <ArrowUp className="h-4 w-4 mr-2 text-blue-500" />
                              ) : (
                                <ArrowDown className="h-4 w-4 mr-2 text-green-500" />
                              )}
                              <span className="font-medium">
                                {message.topic}
                              </span>
                            </div>
                            <div className="flex items-center space-x-2">
                              <Badge variant="outline">QoS {message.qos}</Badge>
                              {message.retained && (
                                <Badge variant="outline">保留</Badge>
                              )}
                              <span className="text-xs text-muted-foreground">
                                {formatDistanceToNow(message.timestamp, {
                                  addSuffix: true,
                                })}
                              </span>
                            </div>
                          </div>
                        </div>
                        <div className="p-4">
                          <pre className="whitespace-pre-wrap text-sm font-mono bg-muted p-2 rounded-md">
                            {message.payload}
                          </pre>
                        </div>
                      </Card>
                    ))}
                  </div>
                </ScrollArea>
              )}
            </CardContent>
          </Card>
        </TabsContent>
      </Tabs>
    </div>
  )
}
