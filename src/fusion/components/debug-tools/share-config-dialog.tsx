import { useState } from 'react'
import {
  <PERSON>alog,
  DialogContent,
  DialogHeader,
  DialogTitle,
  DialogDescription,
} from '@/components/ui/dialog'
import { <PERSON><PERSON>, <PERSON>bsContent, <PERSON><PERSON>List, TabsTrigger } from '@/components/ui/tabs'
import { Button } from '@/components/ui/button'
import { Input } from '@/components/ui/input'
import { Label } from '@/components/ui/label'
import { Checkbox } from '@/components/ui/checkbox'
import {
  Select,
  SelectContent,
  SelectItem,
  SelectTrigger,
  SelectValue,
} from '@/components/ui/select'
import { Copy, Link, Mail, QrCode } from 'lucide-react'
import { toast } from '@/components/ui/use-toast'

export interface ShareableConfig {
  toolName: string
  toolType: string
  version: string
  timestamp: string
  description: string
  config: any
}

interface ShareConfigDialogProps {
  isOpen: boolean
  onClose: () => void
  config: ShareableConfig
}

export function ShareConfigDialog({
  isOpen,
  onClose,
  config,
}: ShareConfigDialogProps) {
  const [activeTab, setActiveTab] = useState('link')
  const [includeAuth, setIncludeAuth] = useState(false)
  const [includeHeaders, setIncludeHeaders] = useState(true)
  const [expiration, setExpiration] = useState('never')
  const [shareLink, setShareLink] = useState('')

  // 生成分享链接
  const generateShareLink = () => {
    try {
      // 创建配置副本，根据用户选择移除敏感信息
      const configToShare = { ...config }

      // 如果用户选择不包含认证信息
      if (!includeAuth && configToShare.config && configToShare.config.auth) {
        delete configToShare.config.auth
      }

      // 如果用户选择不包含请求头
      if (
        !includeHeaders &&
        configToShare.config &&
        configToShare.config.headers
      ) {
        delete configToShare.config.headers
      }

      // 添加过期时间
      if (expiration !== 'never') {
        const now = new Date()
        const expiresAt = new Date(now)

        switch (expiration) {
          case '1h':
            expiresAt.setHours(now.getHours() + 1)
            break
          case '1d':
            expiresAt.setDate(now.getDate() + 1)
            break
          case '7d':
            expiresAt.setDate(now.getDate() + 7)
            break
          case '30d':
            expiresAt.setDate(now.getDate() + 30)
            break
        }

        configToShare.expiresAt = expiresAt.toISOString()
      }

      // 序列化并编码配置
      const configStr = JSON.stringify(configToShare)
      const encodedConfig = btoa(encodeURIComponent(configStr))

      // 生成URL
      const baseUrl = window.location.origin
      const shareUrl = `${baseUrl}/debug-tools/shared?config=${encodedConfig}`

      setShareLink(shareUrl)
      return shareUrl
    } catch (error) {
      console.error('生成分享链接失败', error)
      toast({
        variant: 'destructive',
        title: '生成链接失败',
        description: '无法生成分享链接',
      })
      return ''
    }
  }

  // 复制链接到剪贴板
  const copyLinkToClipboard = () => {
    const link = shareLink || generateShareLink()
    if (!link) return

    navigator.clipboard
      .writeText(link)
      .then(() => {
        toast({
          title: '已复制',
          description: '链接已复制到剪贴板',
        })
      })
      .catch((err) => {
        console.error('复制失败', err)
        toast({
          variant: 'destructive',
          title: '复制失败',
          description: '无法复制链接到剪贴板',
        })
      })
  }

  // 通过邮件分享
  const shareViaEmail = () => {
    const link = shareLink || generateShareLink()
    if (!link) return

    const subject = encodeURIComponent(`分享调试工具配置: ${config.toolName}`)
    const body = encodeURIComponent(
      `我想与你分享一个调试工具配置:\n\n工具: ${config.toolName}\n描述: ${config.description}\n\n点击以下链接查看和应用配置:\n${link}`
    )

    window.open(`mailto:?subject=${subject}&body=${body}`)
  }

  return (
    <Dialog open={isOpen} onOpenChange={onClose}>
      <DialogContent className="sm:max-w-[500px]">
        <DialogHeader>
          <DialogTitle>分享配置</DialogTitle>
          <DialogDescription>
            分享"{config.toolName}"的配置给团队成员
          </DialogDescription>
        </DialogHeader>

        <Tabs
          defaultValue="link"
          value={activeTab}
          onValueChange={setActiveTab}
          className="w-full">
          <TabsList className="grid w-full grid-cols-3">
            <TabsTrigger value="link" className="flex items-center gap-2">
              <Link className="h-4 w-4" />
              链接
            </TabsTrigger>
            <TabsTrigger value="qrcode" className="flex items-center gap-2">
              <QrCode className="h-4 w-4" />
              二维码
            </TabsTrigger>
            <TabsTrigger value="email" className="flex items-center gap-2">
              <Mail className="h-4 w-4" />
              邮件
            </TabsTrigger>
          </TabsList>

          <TabsContent value="link" className="space-y-4 pt-4">
            <div className="space-y-2">
              <Label htmlFor="share-link">分享链接</Label>
              <div className="flex gap-2">
                <Input
                  id="share-link"
                  value={shareLink || '点击生成链接按钮生成分享链接'}
                  readOnly
                  className="flex-1"
                />
                <Button
                  variant="outline"
                  size="icon"
                  onClick={copyLinkToClipboard}>
                  <Copy className="h-4 w-4" />
                </Button>
              </div>
            </div>

            <div className="flex justify-end">
              <Button onClick={generateShareLink}>生成链接</Button>
            </div>
          </TabsContent>

          <TabsContent value="qrcode" className="space-y-4 pt-4">
            <div className="flex flex-col items-center justify-center space-y-4">
              <div className="h-48 w-48 bg-gray-100 flex items-center justify-center border">
                {shareLink ? (
                  <div>二维码将在这里显示</div>
                ) : (
                  <div className="text-center text-sm text-muted-foreground">
                    请先生成链接
                  </div>
                )}
              </div>

              <Button onClick={generateShareLink}>生成二维码</Button>
            </div>
          </TabsContent>

          <TabsContent value="email" className="space-y-4 pt-4">
            <div className="space-y-2">
              <p className="text-sm text-muted-foreground">
                通过邮件分享配置链接给团队成员
              </p>

              <div className="flex justify-center pt-4">
                <Button onClick={shareViaEmail} className="gap-2">
                  <Mail className="h-4 w-4" />
                  通过邮件分享
                </Button>
              </div>
            </div>
          </TabsContent>
        </Tabs>

        <div className="space-y-4 pt-4 border-t">
          <div className="space-y-2">
            <h4 className="text-sm font-medium">安全设置</h4>

            <div className="flex items-center space-x-2">
              <Checkbox
                id="include-auth"
                checked={includeAuth}
                onCheckedChange={(checked) =>
                  setIncludeAuth(checked as boolean)
                }
              />
              <Label htmlFor="include-auth" className="text-sm">
                包含认证信息
              </Label>
            </div>

            <div className="flex items-center space-x-2">
              <Checkbox
                id="include-headers"
                checked={includeHeaders}
                onCheckedChange={(checked) =>
                  setIncludeHeaders(checked as boolean)
                }
              />
              <Label htmlFor="include-headers" className="text-sm">
                包含请求头
              </Label>
            </div>
          </div>

          <div className="space-y-2">
            <Label htmlFor="expiration">链接有效期</Label>
            <Select value={expiration} onValueChange={setExpiration}>
              <SelectTrigger id="expiration">
                <SelectValue placeholder="选择有效期" />
              </SelectTrigger>
              <SelectContent>
                <SelectItem value="1h">1小时</SelectItem>
                <SelectItem value="1d">1天</SelectItem>
                <SelectItem value="7d">7天</SelectItem>
                <SelectItem value="30d">30天</SelectItem>
                <SelectItem value="never">永不过期</SelectItem>
              </SelectContent>
            </Select>
          </div>
        </div>
      </DialogContent>
    </Dialog>
  )
}
