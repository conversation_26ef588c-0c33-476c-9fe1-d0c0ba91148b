import type React from 'react'

import { useState, useRef, useEffect } from 'react'
import { Button } from '@/components/ui/button'
import { Card, CardContent } from '@/components/ui/card'
import { Ta<PERSON>, TabsContent, TabsList, TabsTrigger } from '@/components/ui/tabs'
import { Input } from '@/components/ui/input'
import { Label } from '@/components/ui/label'
import { CodeEditor } from './code-editor'
import {
  Copy,
  Download,
  Upload,
  Trash,
  Check,
  FileJson,
  Wand2,
  Filter,
  ChevronDown,
  ChevronRight,
  Eraser,
  Maximize,
  Minimize,
} from 'lucide-react'
import { toast } from '@/components/ui/use-toast'

export function JsonFormatter() {
  const [jsonInput, setJsonInput] = useState('')
  const [formattedJson, setFormattedJson] = useState('')
  const [compactJson, setCompactJson] = useState('')
  const [filteredJson, setFilteredJson] = useState('')
  const [jsonPath, setJsonPath] = useState('')
  const [activeTab, setActiveTab] = useState('beautify')
  const [error, setError] = useState<string | null>(null)
  const [copied, setCopied] = useState(false)
  const fileInputRef = useRef<HTMLInputElement>(null)
  const [expandedPaths, setExpandedPaths] = useState<Set<string>>(new Set())
  const [jsonObj, setJsonObj] = useState<any>(null)
  const [isFullscreen, setIsFullscreen] = useState(false)

  // 格式化JSON
  const formatJson = (input: string, compact = false) => {
    try {
      if (!input.trim()) {
        setFormattedJson('')
        setCompactJson('')
        setFilteredJson('')
        setJsonObj(null)
        setError(null)
        return
      }

      // 去除注释
      const jsonWithoutComments = removeJsonComments(input)

      const parsedJson = JSON.parse(jsonWithoutComments)
      setJsonObj(parsedJson)

      const beautified = JSON.stringify(parsedJson, null, 2)
      const minified = JSON.stringify(parsedJson)

      setFormattedJson(beautified)
      setCompactJson(minified)
      setFilteredJson(beautified) // 初始时过滤结果与格式化结果相同
      setError(null)

      // 初始展开所有一级节点
      const newExpandedPaths = new Set<string>()
      if (typeof parsedJson === 'object' && parsedJson !== null) {
        Object.keys(parsedJson).forEach((key) => {
          newExpandedPaths.add(key)
        })
      }
      setExpandedPaths(newExpandedPaths)
    } catch (err) {
      setError(`JSON解析错误: ${(err as Error).message}`)
      // 保留原始输入，不清空已格式化的内容
      if (!formattedJson) {
        setFormattedJson(input) // 如果没有格式化内容，显示原始输入
      }
    }
  }

  // 去除JSON注释
  const removeJsonComments = (json: string): string => {
    // 去除单行注释
    let result = json.replace(/\/\/.*$/gm, '')
    // 去除多行注释
    result = result.replace(/\/\*[\s\S]*?\*\//g, '')
    return result
  }

  // 当输入变化时自动格式化
  useEffect(() => {
    if (jsonInput.trim()) {
      formatJson(jsonInput)
    }
  }, [jsonInput])

  // 复制到剪贴板
  const copyToClipboard = (text: string) => {
    navigator.clipboard.writeText(text)
    setCopied(true)
    toast({
      title: '已复制到剪贴板',
      description: 'JSON内容已成功复制到剪贴板',
    })
    setTimeout(() => setCopied(false), 2000)
  }

  // 下载JSON文件
  const downloadJson = () => {
    const content =
      activeTab === 'filter'
        ? filteredJson
        : activeTab === 'beautify'
        ? formattedJson
        : compactJson

    const blob = new Blob([content], { type: 'application/json' })
    const url = URL.createObjectURL(blob)
    const a = document.createElement('a')
    a.href = url
    a.download = 'formatted.json'
    document.body.appendChild(a)
    a.click()
    document.body.removeChild(a)
    URL.revokeObjectURL(url)
  }

  // 上传JSON文件
  const handleFileUpload = (event: React.ChangeEvent<HTMLInputElement>) => {
    const file = event.target.files?.[0]
    if (!file) return

    const reader = new FileReader()
    reader.onload = (e) => {
      const content = e.target?.result as string
      setJsonInput(content)
    }
    reader.readAsText(file)
  }

  // 清空输入
  const clearInput = () => {
    setJsonInput('')
    setFormattedJson('')
    setCompactJson('')
    setFilteredJson('')
    setJsonObj(null)
    setError(null)
  }

  // 生成示例JSON
  const generateSampleJson = () => {
    const sample = {
      name: '示例数据',
      type: 'object',
      properties: {
        id: {
          type: 'integer',
          description: '唯一标识符',
        },
        name: {
          type: 'string',
          description: '名称',
        },
        isActive: {
          type: 'boolean',
          default: true,
        },
        tags: {
          type: 'array',
          items: {
            type: 'string',
          },
          example: ['标签1', '标签2'],
        },
        metadata: {
          type: 'object',
          properties: {
            created: {
              type: 'string',
              format: 'date-time',
            },
            version: {
              type: 'number',
              example: 1.0,
            },
          },
        },
      },
      required: ['id', 'name'],
    }
    setJsonInput(JSON.stringify(sample, null, 2))
  }

  // 过滤JSON
  const filterJson = () => {
    if (!jsonObj) return

    try {
      if (!jsonPath.trim()) {
        setFilteredJson(formattedJson)
        return
      }

      // 简单的JSON路径过滤实现
      const paths = jsonPath.split('.')
      let result = jsonObj

      for (const path of paths) {
        if (path.includes('[') && path.includes(']')) {
          // 处理数组访问，如 items[0]
          const arrayMatch = path.match(/(\w+)\[(\d+)\]/)
          if (arrayMatch) {
            const [_, propName, index] = arrayMatch
            result = result[propName][Number.parseInt(index)]
          }
        } else {
          // 普通属性访问
          result = result[path]
        }

        if (result === undefined) {
          throw new Error(`路径 "${jsonPath}" 未找到`)
        }
      }

      setFilteredJson(
        typeof result === 'object'
          ? JSON.stringify(result, null, 2)
          : String(result)
      )
      setError(null)
    } catch (err) {
      setError(`过滤错误: ${(err as Error).message}`)
    }
  }

  // 切换节点展开/折叠状态
  const toggleNodeExpansion = (path: string) => {
    const newExpandedPaths = new Set(expandedPaths)
    if (newExpandedPaths.has(path)) {
      newExpandedPaths.delete(path)
    } else {
      newExpandedPaths.add(path)
    }
    setExpandedPaths(newExpandedPaths)
  }

  // 渲染可折叠的JSON树
  const renderJsonTree = (obj: any, path = '') => {
    if (obj === null) return <span className="text-gray-500">null</span>

    if (typeof obj !== 'object') {
      if (typeof obj === 'string')
        return <span className="text-green-600">"{obj}"</span>
      if (typeof obj === 'number')
        return <span className="text-blue-600">{obj}</span>
      if (typeof obj === 'boolean')
        return <span className="text-purple-600">{String(obj)}</span>
      return <span>{String(obj)}</span>
    }

    const isArray = Array.isArray(obj)
    const keys = Object.keys(obj)

    if (keys.length === 0) {
      return <span>{isArray ? '[]' : '{}'}</span>
    }

    return (
      <div className="pl-4 border-l border-gray-200 dark:border-gray-700">
        {keys.map((key, index) => {
          const childPath = path ? `${path}.${key}` : key
          const isExpanded = expandedPaths.has(childPath)
          const value = obj[key]
          const isObject = typeof value === 'object' && value !== null

          return (
            <div key={index} className="my-1">
              <div className="flex items-start">
                {isObject ? (
                  <button
                    onClick={() => toggleNodeExpansion(childPath)}
                    className="mr-1 text-gray-500 hover:text-gray-700 dark:hover:text-gray-300 focus:outline-none">
                    {isExpanded ? (
                      <ChevronDown className="h-4 w-4" />
                    ) : (
                      <ChevronRight className="h-4 w-4" />
                    )}
                  </button>
                ) : (
                  <span className="w-4 mr-1"></span>
                )}

                <div className="flex-1">
                  <span className="text-red-500">
                    {isArray ? `[${key}]` : `"${key}"`}
                  </span>
                  <span className="mx-1">:</span>

                  {isObject ? (
                    <>
                      <span className="text-gray-500">
                        {isArray ? '[' : '{'}
                        {!isExpanded && '...'}
                        {!isExpanded && (isArray ? ']' : '}')}
                      </span>

                      {isExpanded && (
                        <div className="mt-1">
                          {renderJsonTree(value, childPath)}
                          <div className="text-gray-500">
                            {isArray ? ']' : '}'}
                          </div>
                        </div>
                      )}
                    </>
                  ) : (
                    renderJsonTree(value, childPath)
                  )}
                </div>
              </div>
            </div>
          )
        })}
      </div>
    )
  }

  // 切换全屏模式
  const toggleFullscreen = () => {
    setIsFullscreen(!isFullscreen)
  }

  // 初始化时不显示错误
  useEffect(() => {
    setError(null)
  }, [])

  return (
    <div
      className={`grid ${
        isFullscreen ? 'fixed inset-0 z-50 bg-background p-6' : ''
      } grid-cols-1 lg:grid-cols-2 gap-6`}>
      {/* 左侧输入区域 */}
      <Card className="overflow-hidden border-t-4 border-t-blue-500 flex flex-col">
        <CardContent className="p-0 flex-1 flex flex-col">
          <div className="flex items-center justify-between bg-muted/40 px-4 py-2 border-b">
            <h3 className="font-medium flex items-center gap-2">
              <FileJson className="h-4 w-4" />
              JSON输入
            </h3>
            <div className="flex items-center gap-1">
              <Button
                variant="ghost"
                size="icon"
                onClick={() => fileInputRef.current?.click()}>
                <Upload className="h-4 w-4" />
                <span className="sr-only">上传</span>
              </Button>
              <input
                type="file"
                ref={fileInputRef}
                onChange={handleFileUpload}
                accept=".json"
                className="hidden"
              />
              <Button variant="ghost" size="icon" onClick={clearInput}>
                <Trash className="h-4 w-4" />
                <span className="sr-only">清空</span>
              </Button>
              <Button variant="ghost" size="icon" onClick={generateSampleJson}>
                <Wand2 className="h-4 w-4" />
                <span className="sr-only">示例</span>
              </Button>
              <Button
                variant="ghost"
                size="icon"
                onClick={() => {
                  const cleaned = removeJsonComments(jsonInput)
                  setJsonInput(cleaned)
                }}
                title="去除注释">
                <Eraser className="h-4 w-4" />
                <span className="sr-only">去除注释</span>
              </Button>
              <Button
                variant="ghost"
                size="icon"
                onClick={toggleFullscreen}
                title={isFullscreen ? '退出全屏' : '全屏模式'}>
                {isFullscreen ? (
                  <Minimize className="h-4 w-4" />
                ) : (
                  <Maximize className="h-4 w-4" />
                )}
                <span className="sr-only">
                  {isFullscreen ? '退出全屏' : '全屏模式'}
                </span>
              </Button>
            </div>
          </div>
          <div className="flex-1 min-h-[400px]">
            <CodeEditor
              value={jsonInput}
              onChange={setJsonInput}
              language="json"
              autoFormat={true}
              lineNumbers={true}
              foldable={true}
              className="h-full border-0"
              placeholder="在此粘贴或输入JSON..."
            />
          </div>
        </CardContent>
      </Card>

      {/* 右侧输出区域 */}
      <Card className="overflow-hidden border-t-4 border-t-blue-500 flex flex-col">
        <CardContent className="p-0 flex-1 flex flex-col">
          <Tabs
            defaultValue="beautify"
            value={activeTab}
            onValueChange={setActiveTab}
            className="w-full flex-1 flex flex-col">
            <div className="flex items-center justify-between bg-muted/40 px-4 py-2 border-b">
              <TabsList className="bg-transparent p-0">
                <TabsTrigger
                  value="beautify"
                  className="data-[state=active]:bg-background">
                  美化
                </TabsTrigger>
                <TabsTrigger
                  value="minify"
                  className="data-[state=active]:bg-background">
                  压缩
                </TabsTrigger>
                <TabsTrigger
                  value="tree"
                  className="data-[state=active]:bg-background">
                  树视图
                </TabsTrigger>
                <TabsTrigger
                  value="filter"
                  className="data-[state=active]:bg-background">
                  过滤
                </TabsTrigger>
              </TabsList>
              <div className="flex items-center gap-1">
                <Button
                  variant="ghost"
                  size="icon"
                  onClick={() => {
                    const content =
                      activeTab === 'filter'
                        ? filteredJson
                        : activeTab === 'beautify'
                        ? formattedJson
                        : activeTab === 'tree'
                        ? formattedJson
                        : compactJson
                    copyToClipboard(content)
                  }}>
                  {copied ? (
                    <Check className="h-4 w-4" />
                  ) : (
                    <Copy className="h-4 w-4" />
                  )}
                  <span className="sr-only">复制</span>
                </Button>
                <Button variant="ghost" size="icon" onClick={downloadJson}>
                  <Download className="h-4 w-4" />
                  <span className="sr-only">下载</span>
                </Button>
              </div>
            </div>

            {activeTab === 'filter' && (
              <div className="flex items-center gap-2 p-2 bg-muted/20 border-b">
                <Label htmlFor="jsonPath" className="whitespace-nowrap">
                  JSON路径:
                </Label>
                <div className="flex-1 flex gap-2">
                  <Input
                    id="jsonPath"
                    value={jsonPath}
                    onChange={(e) => setJsonPath(e.target.value)}
                    onKeyDown={(e) => {
                      if (e.key === 'Enter') {
                        e.preventDefault()
                        filterJson()
                      }
                    }}
                    placeholder="例如: data.items[0].name"
                    className="flex-1"
                  />
                  <Button
                    onClick={filterJson}
                    size="sm"
                    className="whitespace-nowrap">
                    <Filter className="h-4 w-4 mr-1" />
                    过滤
                  </Button>
                </div>
              </div>
            )}

            <TabsContent
              value="beautify"
              className="m-0 flex-1 overflow-hidden">
              {error ? (
                <div className="p-4 text-red-500 bg-red-50 dark:bg-red-950/30 border-l-4 border-red-500">
                  {error}
                </div>
              ) : (
                <div className="h-full min-h-[400px]">
                  <CodeEditor
                    value={formattedJson}
                    onChange={(val) => {}} // 只读模式
                    language="json"
                    className="h-full border-0"
                    placeholder="格式化的JSON将显示在这里..."
                  />
                </div>
              )}
            </TabsContent>

            <TabsContent value="minify" className="m-0 flex-1 overflow-hidden">
              {error ? (
                <div className="p-4 text-red-500 bg-red-50 dark:bg-red-950/30 border-l-4 border-red-500">
                  {error}
                </div>
              ) : (
                <div className="h-full min-h-[400px]">
                  <CodeEditor
                    value={compactJson}
                    onChange={(val) => {}} // 只读模式
                    language="json"
                    className="h-full border-0"
                    placeholder="压缩的JSON将显示在这里..."
                  />
                </div>
              )}
            </TabsContent>

            <TabsContent value="tree" className="m-0 flex-1 overflow-auto">
              {error ? (
                <div className="p-4 text-red-500 bg-red-50 dark:bg-red-950/30 border-l-4 border-red-500">
                  {error}
                </div>
              ) : (
                <div className="min-h-[400px] p-4 text-sm overflow-auto h-full">
                  {jsonObj ? (
                    <div className="font-mono">
                      {Array.isArray(jsonObj) ? '[' : '{'}
                      {renderJsonTree(jsonObj)}
                      <div>{Array.isArray(jsonObj) ? ']' : '}'}</div>
                    </div>
                  ) : (
                    <span className="text-muted-foreground">
                      JSON树视图将显示在这里...
                    </span>
                  )}
                </div>
              )}
            </TabsContent>

            <TabsContent value="filter" className="m-0 flex-1 overflow-hidden">
              {error ? (
                <div className="p-4 text-red-500 bg-red-50 dark:bg-red-950/30 border-l-4 border-red-500">
                  {error}
                </div>
              ) : (
                <div className="h-full min-h-[400px]">
                  <CodeEditor
                    value={filteredJson}
                    onChange={(val) => {}} // 只读模式
                    language="json"
                    className="h-full border-0"
                    placeholder="过滤后的JSON将显示在这里..."
                  />
                </div>
              )}
            </TabsContent>
          </Tabs>
        </CardContent>
      </Card>
    </div>
  )
}
