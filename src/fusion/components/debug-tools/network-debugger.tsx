import { useState } from 'react'
import { Button } from '@/components/ui/button'
import { Laptop, Server, Wifi } from 'lucide-react'
import { TcpClient } from './tcp-client'
import { TcpServer } from './tcp-server'
import { UdpClient } from './udp-client'

type DebugMode = 'tcp-client' | 'tcp-server' | 'udp-client'

export function NetworkDebugger() {
  const [mode, setMode] = useState<DebugMode>('tcp-client')

  // 渲染活动组件
  const renderActiveComponent = () => {
    switch (mode) {
      case 'tcp-client':
        return <TcpClient />
      case 'tcp-server':
        return <TcpServer />
      case 'udp-client':
        return <UdpClient />
      default:
        return <div>请选择一个调试模式</div>
    }
  }

  return (
    <div className="h-full flex flex-col space-y-4">
      {/* 模式选择 - 一排三个图标 */}
      <div className="flex items-center space-x-2 bg-muted p-1 rounded-lg">
        <Button
          variant={mode === 'tcp-client' ? 'default' : 'ghost'}
          size="sm"
          onClick={() => setMode('tcp-client')}
          className="h-10 px-4 flex-1">
          <Laptop className="h-4 w-4 mr-2" />
          客户端模式
        </Button>
        <Button
          variant={mode === 'tcp-server' ? 'default' : 'ghost'}
          size="sm"
          onClick={() => setMode('tcp-server')}
          className="h-10 px-4 flex-1">
          <Server className="h-4 w-4 mr-2" />
          服务器模式
        </Button>
        <Button
          variant={mode === 'udp-client' ? 'default' : 'ghost'}
          size="sm"
          onClick={() => setMode('udp-client')}
          className="h-10 px-4 flex-1">
          <Wifi className="h-4 w-4 mr-2" />
          UDP模式
        </Button>
      </div>

      {/* 活动组件 */}
      <div className="flex-1">{renderActiveComponent()}</div>
    </div>
  )
}
