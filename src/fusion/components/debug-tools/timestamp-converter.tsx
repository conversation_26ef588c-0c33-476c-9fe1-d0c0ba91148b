/**
 * 时间戳转换工具UI组件
 *
 * 提供时间戳与日期时间相互转换的用户界面
 * 包含当前时间戳、时间戳转日期和日期转时间戳功能
 */

import { useTimestampConverter } from '@/hooks/use-timestamp-converter'
import { Button } from '@/components/ui/button'
import { Input } from '@/components/ui/input'
import { Label } from '@/components/ui/label'
import { Copy, Clock, ArrowRight } from 'lucide-react'
import { useEffect, useState, useCallback, useMemo } from 'react'
import { Alert, AlertDescription } from '@/components/ui/alert'

export function TimestampConverter() {
  const {
    timestamp,
    setTimestamp,
    dateTime,
    setDateTime,
    dateInput,
    setDateInput,
    error,
    convertTimestampToDate,
    convertDateToTimestamp,
    getCurrentTimestamp,
    formatDateTime,
  } = useTimestampConverter()

  // 初始化时间戳状态 - 使用 useMemo 而不是直接调用函数
  const initialTimestamp = useMemo(() => getCurrentTimestamp(), [])
  const [currentTime, setCurrentTime] = useState(initialTimestamp)

  // 使用 useCallback 包装更新时间戳的函数
  const updateCurrentTime = useCallback(() => {
    setCurrentTime(getCurrentTimestamp())
  }, [getCurrentTimestamp])

  // 设置定时器更新时间戳
  useEffect(() => {
    const interval = setInterval(updateCurrentTime, 1000)
    return () => clearInterval(interval)
  }, [updateCurrentTime])

  // 设置当前日期时间 - 只在组件挂载时执行一次
  useEffect(() => {
    const now = new Date()
    const localDateString = now.toISOString().slice(0, 16)
    setDateInput(localDateString)
  }, [setDateInput])

  // 复制到剪贴板
  const copyToClipboard = useCallback((text: string) => {
    navigator.clipboard.writeText(text)
  }, [])

  // 获取时间戳转换结果 - 使用 useMemo 避免不必要的计算
  const timestampResult = useMemo(
    () => convertDateToTimestamp(),
    [convertDateToTimestamp, dateInput]
  )

  return (
    <div className="grid gap-8">
      {/* 当前时间戳 */}
      <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
        <div className="space-y-3">
          <Label className="text-base flex items-center gap-2">
            <Clock className="h-4 w-4" />
            当前时间戳 (秒)
          </Label>
          <div className="flex">
            <Input
              value={currentTime.seconds}
              readOnly
              className="font-mono h-12 text-base"
            />
            <Button
              variant="outline"
              className="ml-2 h-12 px-4"
              onClick={() => copyToClipboard(currentTime.seconds.toString())}>
              <Copy className="h-4 w-4 mr-2" />
              复制
            </Button>
          </div>
        </div>

        <div className="space-y-3">
          <Label className="text-base flex items-center gap-2">
            <Clock className="h-4 w-4" />
            当前时间戳 (毫秒)
          </Label>
          <div className="flex">
            <Input
              value={currentTime.milliseconds}
              readOnly
              className="font-mono h-12 text-base"
            />
            <Button
              variant="outline"
              className="ml-2 h-12 px-4"
              onClick={() =>
                copyToClipboard(currentTime.milliseconds.toString())
              }>
              <Copy className="h-4 w-4 mr-2" />
              复制
            </Button>
          </div>
        </div>
      </div>

      {/* 时间戳转日期 */}
      <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
        <div className="space-y-3">
          <Label className="text-base">时间戳转日期时间</Label>
          <div className="flex gap-2">
            <Input
              type="text"
              placeholder="输入秒或毫秒时间戳..."
              className="font-mono h-12 text-base"
              value={timestamp}
              onChange={(e) => setTimestamp(e.target.value)}
            />
            <Button className="h-12 px-4" onClick={convertTimestampToDate}>
              <ArrowRight className="h-4 w-4 mr-2" />
              转换
            </Button>
          </div>
          <div className="text-sm text-muted-foreground">
            支持秒级(10位)或毫秒级(13位)时间戳
          </div>
        </div>

        <div className="space-y-3">
          <Label className="text-base">转换结果</Label>
          <Input
            value={dateTime}
            readOnly
            className="font-mono h-12 text-base"
          />
          {dateTime && (
            <Button
              variant="outline"
              className="w-full"
              onClick={() => copyToClipboard(dateTime)}>
              <Copy className="h-4 w-4 mr-2" />
              复制结果
            </Button>
          )}
        </div>
      </div>

      {/* 日期转时间戳 */}
      <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
        <div className="space-y-3">
          <Label className="text-base">日期时间转时间戳</Label>
          <Input
            type="datetime-local"
            className="font-mono h-12 text-base"
            value={dateInput}
            onChange={(e) => setDateInput(e.target.value)}
          />
          <div className="text-sm text-muted-foreground">
            选择或输入日期时间
          </div>
        </div>

        <div className="space-y-3">
          <Label className="text-base">转换结果</Label>
          {timestampResult ? (
            <div className="space-y-2">
              <div className="p-3 bg-gray-50 rounded-md font-mono">
                秒级时间戳: {timestampResult.seconds}
              </div>
              <div className="p-3 bg-gray-50 rounded-md font-mono">
                毫秒级时间戳: {timestampResult.milliseconds}
              </div>
              <div className="flex gap-2">
                <Button
                  variant="outline"
                  className="flex-1"
                  onClick={() =>
                    copyToClipboard(timestampResult.seconds.toString())
                  }>
                  <Copy className="h-4 w-4 mr-2" />
                  复制秒级
                </Button>
                <Button
                  variant="outline"
                  className="flex-1"
                  onClick={() =>
                    copyToClipboard(timestampResult.milliseconds.toString())
                  }>
                  <Copy className="h-4 w-4 mr-2" />
                  复制毫秒级
                </Button>
              </div>
            </div>
          ) : (
            <div className="p-4 bg-gray-50 rounded-md text-gray-400">
              请选择日期时间
            </div>
          )}
        </div>
      </div>

      {/* 错误提示 */}
      {error && (
        <Alert variant="destructive">
          <AlertDescription>{error}</AlertDescription>
        </Alert>
      )}
    </div>
  )
}
