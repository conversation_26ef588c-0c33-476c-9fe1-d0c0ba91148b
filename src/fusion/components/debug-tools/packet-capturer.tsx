import { useState } from 'react'
import {
  <PERSON>,
  CardContent,
  CardDescription,
  CardFooter,
  CardHeader,
  CardTitle,
} from '@/components/ui/card'
import { But<PERSON> } from '@/components/ui/button'
import { Input } from '@/components/ui/input'
import { Label } from '@/components/ui/label'
import {
  Select,
  SelectContent,
  SelectItem,
  SelectTrigger,
  SelectValue,
} from '@/components/ui/select'
import {
  Table,
  TableBody,
  TableCell,
  TableHead,
  TableHeader,
  TableRow,
} from '@/components/ui/table'
import { <PERSON><PERSON>, <PERSON><PERSON>Content, TabsList, TabsTrigger } from '@/components/ui/tabs'
import { Badge } from '@/components/ui/badge'
import { Checkbox } from '@/components/ui/checkbox'
import { Textarea } from '@/components/ui/textarea'
import { Wifi, Play, Square, Download, Filter, Search } from 'lucide-react'

interface Packet {
  id: number
  timestamp: Date
  source: string
  destination: string
  protocol: string
  length: number
  info: string
  data: string
}

// Generate mock packet data
const generateMockPackets = (count: number): Packet[] => {
  const protocols = ['TCP', 'UDP', 'HTTP', 'HTTPS', 'DNS', 'ICMP', 'ARP']
  const ips = [
    '***********',
    '***********',
    '***********00',
    '********',
    '********',
    '**********',
    '*******',
    '*******',
    '**************',
  ]

  const packets: Packet[] = []

  for (let i = 0; i < count; i++) {
    const protocol = protocols[Math.floor(Math.random() * protocols.length)]
    const source = ips[Math.floor(Math.random() * ips.length)]
    const destination = ips[Math.floor(Math.random() * ips.length)]

    let info = ''
    let data = ''

    switch (protocol) {
      case 'TCP':
        const srcPort = Math.floor(Math.random() * 60000) + 1024
        const dstPort = [80, 443, 22, 21, 3306][Math.floor(Math.random() * 5)]
        info = `${srcPort} → ${dstPort} [SYN, ACK] Seq=0 Ack=0 Win=64240 Len=0`
        data = `45 00 00 34 00 00 40 00 40 06 00 00 ${source
          .split('.')
          .map((octet) => Number.parseInt(octet).toString(16).padStart(2, '0'))
          .join(' ')} ${destination
          .split('.')
          .map((octet) => Number.parseInt(octet).toString(16).padStart(2, '0'))
          .join(' ')} ${
          srcPort
            .toString(16)
            .padStart(4, '0')
            .match(/.{1,2}/g)
            ?.join(' ') || ''
        } ${
          dstPort
            .toString(16)
            .padStart(4, '0')
            .match(/.{1,2}/g)
            ?.join(' ') || ''
        } 00 00 00 00 00 00 00 00 50 02 fa f0 00 00 00 00 00 00 00 00`
        break
      case 'UDP':
        const udpSrcPort = Math.floor(Math.random() * 60000) + 1024
        const udpDstPort = [53, 67, 68, 123, 161][Math.floor(Math.random() * 5)]
        info = `${udpSrcPort} → ${udpDstPort} Len=${
          Math.floor(Math.random() * 100) + 10
        }`
        data = `45 00 00 ${(Math.floor(Math.random() * 100) + 30)
          .toString(16)
          .padStart(2, '0')} 00 00 40 00 40 11 00 00 ${source
          .split('.')
          .map((octet) => Number.parseInt(octet).toString(16).padStart(2, '0'))
          .join(' ')} ${destination
          .split('.')
          .map((octet) => Number.parseInt(octet).toString(16).padStart(2, '0'))
          .join(' ')} ${
          udpSrcPort
            .toString(16)
            .padStart(4, '0')
            .match(/.{1,2}/g)
            ?.join(' ') || ''
        } ${
          udpDstPort
            .toString(16)
            .padStart(4, '0')
            .match(/.{1,2}/g)
            ?.join(' ') || ''
        }`
        break
      case 'HTTP':
        info = `GET /index.html HTTP/1.1`
        data = `47 45 54 20 2f 69 6e 64 65 78 2e 68 74 6d 6c 20 48 54 54 50 2f 31 2e 31 0d 0a 48 6f 73 74 3a 20 65 78 61 6d 70 6c 65 2e 63 6f 6d 0d 0a 55 73 65 72 2d 41 67 65 6e 74 3a 20 4d 6f 7a 69 6c 6c 61 2f 35 2e 30 0d 0a 41 63 63 65 70 74 3a 20 74 65 78 74 2f 68 74 6d 6c 0d 0a 0d 0a`
        break
      case 'DNS':
        info = `Standard query 0x1234 A example.com`
        data = `45 00 00 3c 00 00 40 00 40 11 00 00 ${source
          .split('.')
          .map((octet) => Number.parseInt(octet).toString(16).padStart(2, '0'))
          .join(' ')} ${destination
          .split('.')
          .map((octet) => Number.parseInt(octet).toString(16).padStart(2, '0'))
          .join(
            ' '
          )} 00 35 00 35 00 28 00 00 12 34 01 00 00 01 00 00 00 00 00 00 07 65 78 61 6d 70 6c 65 03 63 6f 6d 00 00 01 00 01`
        break
      default:
        info = `${protocol} packet`
        data = Array.from({ length: 16 }, () =>
          Math.floor(Math.random() * 256)
            .toString(16)
            .padStart(2, '0')
        ).join(' ')
    }

    packets.push({
      id: i + 1,
      timestamp: new Date(Date.now() - Math.floor(Math.random() * 60000)),
      source,
      destination,
      protocol,
      length: Math.floor(Math.random() * 1000) + 64,
      info,
      data,
    })
  }

  // Sort by timestamp
  return packets.sort((a, b) => a.timestamp.getTime() - b.timestamp.getTime())
}

export function PacketCapturer() {
  const [interface_, setInterface] = useState('eth0')
  const [filter, setFilter] = useState('')
  const [isCapturing, setIsCapturing] = useState(false)
  const [packets, setPackets] = useState<Packet[]>([])
  const [selectedPacket, setSelectedPacket] = useState<Packet | null>(null)
  const [searchTerm, setSearchTerm] = useState('')

  // Filter options
  const [showTCP, setShowTCP] = useState(true)
  const [showUDP, setShowUDP] = useState(true)
  const [showHTTP, setShowHTTP] = useState(true)
  const [showDNS, setShowDNS] = useState(true)
  const [showOther, setShowOther] = useState(true)

  const startCapture = () => {
    setIsCapturing(true)
    setPackets([])
    setSelectedPacket(null)

    // In a real application, this would connect to a backend service
    // that performs the actual packet capture
    // Here we're just generating mock data

    // Generate initial batch of packets
    const initialPackets = generateMockPackets(20)
    setPackets(initialPackets)

    // Simulate receiving new packets periodically
    const interval = setInterval(() => {
      const newPacket = generateMockPackets(1)[0]
      setPackets((prev) =>
        [...prev, newPacket].sort(
          (a, b) => a.timestamp.getTime() - b.timestamp.getTime()
        )
      )
    }, 2000)

    // Store the interval ID for cleanup
    // @ts-ignore - we're using window as storage
    window.captureInterval = interval
  }

  const stopCapture = () => {
    setIsCapturing(false)

    // Clear the interval
    // @ts-ignore - we're using window as storage
    if (window.captureInterval) {
      // @ts-ignore
      clearInterval(window.captureInterval)
      // @ts-ignore
      window.captureInterval = null
    }
  }

  const downloadCapture = () => {
    // Create a PCAP-like format (this is simplified)
    const data = JSON.stringify(packets, null, 2)
    const blob = new Blob([data], { type: 'application/json' })
    const url = URL.createObjectURL(blob)

    const a = document.createElement('a')
    a.href = url
    a.download = `packet_capture_${new Date()
      .toISOString()
      .replace(/:/g, '-')}.json`
    document.body.appendChild(a)
    a.click()
    document.body.removeChild(a)
    URL.revokeObjectURL(url)
  }

  // Apply filters to packets
  const filteredPackets = packets.filter((packet) => {
    // Protocol filter
    if (
      (!showTCP && packet.protocol === 'TCP') ||
      (!showUDP && packet.protocol === 'UDP') ||
      (!showHTTP && packet.protocol === 'HTTP') ||
      (!showDNS && packet.protocol === 'DNS') ||
      (!showOther && !['TCP', 'UDP', 'HTTP', 'DNS'].includes(packet.protocol))
    ) {
      return false
    }

    // Search term filter
    if (searchTerm) {
      const term = searchTerm.toLowerCase()
      return (
        packet.source.toLowerCase().includes(term) ||
        packet.destination.toLowerCase().includes(term) ||
        packet.protocol.toLowerCase().includes(term) ||
        packet.info.toLowerCase().includes(term)
      )
    }

    // Custom filter (simplified)
    if (filter) {
      const f = filter.toLowerCase()
      if (f.startsWith('ip.addr ==')) {
        const ip = f.replace('ip.addr ==', '').trim()
        return packet.source === ip || packet.destination === ip
      }
      if (f.startsWith('tcp.port ==')) {
        const port = f.replace('tcp.port ==', '').trim()
        return (
          packet.info.includes(` → ${port}`) ||
          packet.info.includes(`${port} →`)
        )
      }
    }

    return true
  })

  const formatHexDump = (hexString: string) => {
    const bytes = hexString.split(' ')
    const lines = []

    for (let i = 0; i < bytes.length; i += 16) {
      const lineBytes = bytes.slice(i, i + 16)
      const offset = i.toString(16).padStart(4, '0')
      const hexPart = lineBytes.join(' ').padEnd(48, ' ')

      // Convert hex to ASCII (printable chars only)
      const asciiPart = lineBytes
        .map((byte) => {
          const charCode = Number.parseInt(byte, 16)
          return charCode >= 32 && charCode <= 126
            ? String.fromCharCode(charCode)
            : '.'
        })
        .join('')

      lines.push(`${offset}  ${hexPart}  |${asciiPart}|`)
    }

    return lines.join('\n')
  }

  return (
    <div className="space-y-6">
      <Card>
        <CardHeader>
          <CardTitle>网络抓包</CardTitle>
          <CardDescription>捕获和分析网络数据包，监控网络流量</CardDescription>
        </CardHeader>
        <CardContent className="space-y-4">
          <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
            <div className="space-y-2">
              <Label htmlFor="interface">网络接口</Label>
              <Select value={interface_} onValueChange={setInterface}>
                <SelectTrigger id="interface">
                  <SelectValue placeholder="选择网络接口" />
                </SelectTrigger>
                <SelectContent>
                  <SelectItem value="eth0">eth0 (以太网)</SelectItem>
                  <SelectItem value="wlan0">wlan0 (无线网络)</SelectItem>
                  <SelectItem value="lo">lo (本地回环)</SelectItem>
                </SelectContent>
              </Select>
            </div>

            <div className="space-y-2">
              <Label htmlFor="filter">捕获过滤器</Label>
              <div className="flex space-x-2">
                <Input
                  id="filter"
                  placeholder="例如: ip.addr == ***********"
                  value={filter}
                  onChange={(e) => setFilter(e.target.value)}
                  disabled={isCapturing}
                />
                <Button variant="outline" size="icon">
                  <Filter className="h-4 w-4" />
                </Button>
              </div>
              <p className="text-xs text-muted-foreground">
                使用过滤表达式限制捕获的数据包
              </p>
            </div>
          </div>
        </CardContent>
        <CardFooter className="flex justify-between">
          <div className="flex space-x-2">
            {!isCapturing ? (
              <Button onClick={startCapture}>
                <Play className="mr-2 h-4 w-4" />
                开始捕获
              </Button>
            ) : (
              <Button variant="destructive" onClick={stopCapture}>
                <Square className="mr-2 h-4 w-4" />
                停止捕获
              </Button>
            )}

            <Button
              variant="outline"
              onClick={downloadCapture}
              disabled={packets.length === 0}>
              <Download className="mr-2 h-4 w-4" />
              导出捕获
            </Button>
          </div>

          <div className="flex items-center space-x-2">
            <Wifi
              className={`h-5 w-5 ${
                isCapturing
                  ? 'text-green-500 animate-pulse'
                  : 'text-muted-foreground'
              }`}
            />
            <span className="text-sm">
              {isCapturing ? '正在捕获' : '已停止'} • {packets.length} 个数据包
            </span>
          </div>
        </CardFooter>
      </Card>

      {packets.length > 0 && (
        <Card>
          <CardHeader className="pb-0">
            <div className="flex justify-between items-center">
              <CardTitle>数据包列表</CardTitle>
              <div className="flex items-center space-x-4">
                <div className="flex items-center space-x-2">
                  <Input
                    placeholder="搜索数据包..."
                    value={searchTerm}
                    onChange={(e) => setSearchTerm(e.target.value)}
                    className="w-60"
                  />
                  <Button variant="ghost" size="icon">
                    <Search className="h-4 w-4" />
                  </Button>
                </div>
              </div>
            </div>
          </CardHeader>
          <CardContent className="pt-4">
            <div className="flex space-x-4 mb-4">
              <div className="flex items-center space-x-2">
                <Checkbox
                  id="filter-tcp"
                  checked={showTCP}
                  onCheckedChange={(checked) => setShowTCP(!!checked)}
                />
                <Label htmlFor="filter-tcp" className="text-sm">
                  TCP
                </Label>
              </div>
              <div className="flex items-center space-x-2">
                <Checkbox
                  id="filter-udp"
                  checked={showUDP}
                  onCheckedChange={(checked) => setShowUDP(!!checked)}
                />
                <Label htmlFor="filter-udp" className="text-sm">
                  UDP
                </Label>
              </div>
              <div className="flex items-center space-x-2">
                <Checkbox
                  id="filter-http"
                  checked={showHTTP}
                  onCheckedChange={(checked) => setShowHTTP(!!checked)}
                />
                <Label htmlFor="filter-http" className="text-sm">
                  HTTP
                </Label>
              </div>
              <div className="flex items-center space-x-2">
                <Checkbox
                  id="filter-dns"
                  checked={showDNS}
                  onCheckedChange={(checked) => setShowDNS(!!checked)}
                />
                <Label htmlFor="filter-dns" className="text-sm">
                  DNS
                </Label>
              </div>
              <div className="flex items-center space-x-2">
                <Checkbox
                  id="filter-other"
                  checked={showOther}
                  onCheckedChange={(checked) => setShowOther(!!checked)}
                />
                <Label htmlFor="filter-other" className="text-sm">
                  其他
                </Label>
              </div>
            </div>

            <div className="border rounded-md">
              <Table>
                <TableHeader>
                  <TableRow>
                    <TableHead className="w-16">编号</TableHead>
                    <TableHead className="w-32">时间</TableHead>
                    <TableHead>源地址</TableHead>
                    <TableHead>目标地址</TableHead>
                    <TableHead className="w-24">协议</TableHead>
                    <TableHead className="w-20">长度</TableHead>
                    <TableHead>信息</TableHead>
                  </TableRow>
                </TableHeader>
                <TableBody>
                  {filteredPackets.map((packet) => (
                    <TableRow
                      key={packet.id}
                      className={`cursor-pointer ${
                        selectedPacket?.id === packet.id ? 'bg-muted' : ''
                      }`}
                      onClick={() => setSelectedPacket(packet)}>
                      <TableCell>{packet.id}</TableCell>
                      <TableCell>
                        {packet.timestamp.toLocaleTimeString()}
                      </TableCell>
                      <TableCell>{packet.source}</TableCell>
                      <TableCell>{packet.destination}</TableCell>
                      <TableCell>
                        <Badge
                          variant="outline"
                          className={
                            packet.protocol === 'TCP'
                              ? 'border-blue-500 text-blue-500'
                              : packet.protocol === 'UDP'
                              ? 'border-green-500 text-green-500'
                              : packet.protocol === 'HTTP'
                              ? 'border-purple-500 text-purple-500'
                              : packet.protocol === 'DNS'
                              ? 'border-orange-500 text-orange-500'
                              : 'border-gray-500 text-gray-500'
                          }>
                          {packet.protocol}
                        </Badge>
                      </TableCell>
                      <TableCell>{packet.length}</TableCell>
                      <TableCell className="font-mono text-xs">
                        {packet.info}
                      </TableCell>
                    </TableRow>
                  ))}
                </TableBody>
              </Table>
            </div>
          </CardContent>
        </Card>
      )}

      {selectedPacket && (
        <Card>
          <CardHeader>
            <CardTitle>数据包详情 #{selectedPacket.id}</CardTitle>
          </CardHeader>
          <CardContent>
            <Tabs defaultValue="hex">
              <TabsList>
                <TabsTrigger value="hex">十六进制视图</TabsTrigger>
                <TabsTrigger value="details">详细信息</TabsTrigger>
              </TabsList>
              <TabsContent value="hex" className="mt-4">
                <Textarea
                  value={formatHexDump(selectedPacket.data)}
                  readOnly
                  className="font-mono text-xs h-64"
                />
              </TabsContent>
              <TabsContent value="details" className="mt-4">
                <div className="space-y-4">
                  <div className="grid grid-cols-2 gap-4">
                    <div>
                      <h3 className="text-sm font-medium">基本信息</h3>
                      <div className="mt-2 space-y-2">
                        <div className="flex justify-between">
                          <span className="text-sm text-muted-foreground">
                            数据包编号
                          </span>
                          <span className="text-sm">{selectedPacket.id}</span>
                        </div>
                        <div className="flex justify-between">
                          <span className="text-sm text-muted-foreground">
                            捕获时间
                          </span>
                          <span className="text-sm">
                            {selectedPacket.timestamp.toLocaleString()}
                          </span>
                        </div>
                        <div className="flex justify-between">
                          <span className="text-sm text-muted-foreground">
                            数据包长度
                          </span>
                          <span className="text-sm">
                            {selectedPacket.length} 字节
                          </span>
                        </div>
                      </div>
                    </div>

                    <div>
                      <h3 className="text-sm font-medium">协议信息</h3>
                      <div className="mt-2 space-y-2">
                        <div className="flex justify-between">
                          <span className="text-sm text-muted-foreground">
                            协议
                          </span>
                          <span className="text-sm">
                            {selectedPacket.protocol}
                          </span>
                        </div>
                        <div className="flex justify-between">
                          <span className="text-sm text-muted-foreground">
                            源地址
                          </span>
                          <span className="text-sm">
                            {selectedPacket.source}
                          </span>
                        </div>
                        <div className="flex justify-between">
                          <span className="text-sm text-muted-foreground">
                            目标地址
                          </span>
                          <span className="text-sm">
                            {selectedPacket.destination}
                          </span>
                        </div>
                      </div>
                    </div>
                  </div>

                  <div>
                    <h3 className="text-sm font-medium">详细信息</h3>
                    <p className="mt-2 text-sm font-mono">
                      {selectedPacket.info}
                    </p>
                  </div>
                </div>
              </TabsContent>
            </Tabs>
          </CardContent>
        </Card>
      )}
    </div>
  )
}
