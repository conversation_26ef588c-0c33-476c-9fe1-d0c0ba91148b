/**
 * 命令终端工具UI组件
 *
 * 提供交互式命令行终端界面
 * 支持基本命令执行、命令历史记录和补全功能
 */

import { useCommandTerminal } from '@/hooks/use-command-terminal'
import { Button } from '@/components/ui/button'
import { useEffect, useRef } from 'react'
import { Terminal } from 'lucide-react'
import { cn } from '@/lib/utils'

export function CommandTerminal() {
  const {
    commandInput,
    setCommandInput,
    commandOutput,
    handleCommandKeyDown,
    scrollToBottom,
  } = useCommandTerminal()

  const commandInputRef = useRef<HTMLInputElement>(null)
  const commandOutputRef = useRef<HTMLDivElement>(null)

  // 自动滚动到底部
  useEffect(() => {
    if (commandOutputRef.current) {
      commandOutputRef.current.scrollTop = commandOutputRef.current.scrollHeight
    }
  }, [commandOutput])

  // 自动聚焦输入框
  useEffect(() => {
    if (commandInputRef.current) {
      commandInputRef.current.focus()
    }
  }, [])

  // 处理命令输入按键
  const onKeyDown = (e: React.KeyboardEvent<HTMLInputElement>) => {
    const result = handleCommandKeyDown(e)
    if (result.exit) {
      // 处理退出命令
      // 在对话框版本中会关闭对话框，这里我们不需要做什么
    }
  }

  return (
    <div className="flex flex-col h-full bg-gray-900 text-white font-mono">
      <div className="p-2 border-b border-gray-800 bg-gray-800 flex items-center">
        <Terminal className="h-4 w-4 mr-2" />
        <span className="font-medium text-sm">命令终端</span>
      </div>

      <div
        id="command-output"
        ref={commandOutputRef}
        className="flex-1 overflow-y-auto p-4 space-y-1"
        onClick={() => commandInputRef.current?.focus()}>
        {commandOutput.map((line, i) => (
          <div
            key={i}
            className={cn(
              'whitespace-pre-wrap break-all',
              line.type === 'error'
                ? 'text-red-400'
                : line.type === 'input'
                ? 'text-cyan-400'
                : 'text-gray-300'
            )}>
            {line.content}
          </div>
        ))}
      </div>

      <div className="p-4 border-t border-gray-800 flex items-center">
        <span className="text-green-400 mr-2">$</span>
        <input
          ref={commandInputRef}
          type="text"
          value={commandInput}
          onChange={(e) => setCommandInput(e.target.value)}
          onKeyDown={onKeyDown}
          className="flex-1 bg-transparent border-none outline-none text-white"
          autoFocus
          autoComplete="off"
          spellCheck="false"
        />
      </div>
    </div>
  )
}
