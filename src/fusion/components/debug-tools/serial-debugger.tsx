import { useState, useEffect, useRef } from 'react'
import {
  Card,
  CardContent,
  CardDescription,
  CardFooter,
  CardHeader,
  CardTitle,
} from '@/components/ui/card'
import { Button } from '@/components/ui/button'
import { Input } from '@/components/ui/input'
import { Label } from '@/components/ui/label'
import {
  Select,
  SelectContent,
  SelectItem,
  SelectTrigger,
  SelectValue,
} from '@/components/ui/select'
import { Ta<PERSON>, TabsList, TabsTrigger } from '@/components/ui/tabs'
import { Switch } from '@/components/ui/switch'
import {
  Terminal,
  Send,
  Trash2,
  Download,
  Play,
  Square,
  RefreshCw,
} from 'lucide-react'

interface SerialMessage {
  id: number
  timestamp: Date
  direction: 'send' | 'receive'
  data: string
  hex?: string
}

export function SerialDebugger() {
  // Serial port configuration
  const [portName, setPortName] = useState('')
  const [baudRate, setBaudRate] = useState('9600')
  const [dataBits, setDataBits] = useState('8')
  const [stopBits, setStopBits] = useState('1')
  const [parity, setParity] = useState('none')
  const [flowControl, setFlowControl] = useState('none')

  // Connection state
  const [isConnected, setIsConnected] = useState(false)
  const [availablePorts, setAvailablePorts] = useState<string[]>([])

  // Message handling
  const [message, setMessage] = useState('')
  const [messages, setMessages] = useState<SerialMessage[]>([])
  const [displayMode, setDisplayMode] = useState<'text' | 'hex'>('text')
  const [autoScroll, setAutoScroll] = useState(true)

  // Refs
  const messagesEndRef = useRef<HTMLDivElement>(null)
  const messageIdCounter = useRef(0)

  // Mock available ports
  useEffect(() => {
    // In a real application, this would query the system for available serial ports
    setAvailablePorts([
      'COM1',
      'COM2',
      'COM3',
      'COM4',
      '/dev/ttyS0',
      '/dev/ttyS1',
      '/dev/ttyUSB0',
      '/dev/ttyUSB1',
      '/dev/ttyACM0',
    ])
  }, [])

  // Auto-scroll to bottom when messages change
  useEffect(() => {
    if (autoScroll && messagesEndRef.current) {
      messagesEndRef.current.scrollIntoView({ behavior: 'smooth' })
    }
  }, [messages, autoScroll])

  // Connect to serial port
  const connectPort = () => {
    if (!portName || !baudRate) return

    // In a real application, this would establish a connection to the serial port
    // Here we're just simulating the connection
    setIsConnected(true)

    // Simulate receiving data periodically
    const interval = setInterval(() => {
      if (Math.random() > 0.7) {
        receiveMessage()
      }
    }, 3000)

    // Store the interval ID for cleanup
    // @ts-ignore - we're using window as storage
    window.serialInterval = interval
  }

  // Disconnect from serial port
  const disconnectPort = () => {
    setIsConnected(false)

    // Clear the interval
    // @ts-ignore - we're using window as storage
    if (window.serialInterval) {
      // @ts-ignore
      clearInterval(window.serialInterval)
      // @ts-ignore
      window.serialInterval = null
    }
  }

  // Refresh available ports
  const refreshPorts = () => {
    // In a real application, this would re-query the system for available ports
    // Here we're just simulating by adding a random port
    const randomPort = `COM${Math.floor(Math.random() * 10)}`
    if (!availablePorts.includes(randomPort)) {
      setAvailablePorts((prev) => [...prev, randomPort])
    }
  }

  // Send a message
  const sendMessage = () => {
    if (!message || !isConnected) return

    const newMessage: SerialMessage = {
      id: ++messageIdCounter.current,
      timestamp: new Date(),
      direction: 'send',
      data: message,
      hex: stringToHex(message),
    }

    setMessages((prev) => [...prev, newMessage])
    setMessage('')

    // Simulate a response after a short delay
    if (Math.random() > 0.3) {
      setTimeout(receiveMessage, Math.random() * 1000 + 500)
    }
  }

  // Receive a message (simulated)
  const receiveMessage = () => {
    if (!isConnected) return

    const responses = [
      'OK',
      'ERROR',
      'READY',
      '+CMTI: "SM",1',
      '+CREG: 0,1',
      'AT+CSQ=?',
      '+CSQ: 31,0',
      'NO CARRIER',
      '> ',
      '+CPIN: READY',
    ]

    const response = responses[Math.floor(Math.random() * responses.length)]

    const newMessage: SerialMessage = {
      id: ++messageIdCounter.current,
      timestamp: new Date(),
      direction: 'receive',
      data: response,
      hex: stringToHex(response),
    }

    setMessages((prev) => [...prev, newMessage])
  }

  // Clear all messages
  const clearMessages = () => {
    setMessages([])
    messageIdCounter.current = 0
  }

  // Export messages to file
  const exportMessages = () => {
    const data = JSON.stringify(messages, null, 2)
    const blob = new Blob([data], { type: 'application/json' })
    const url = URL.createObjectURL(blob)

    const a = document.createElement('a')
    a.href = url
    a.download = `serial_log_${new Date()
      .toISOString()
      .replace(/:/g, '-')}.json`
    document.body.appendChild(a)
    a.click()
    document.body.removeChild(a)
    URL.revokeObjectURL(url)
  }

  // Convert string to hex representation
  const stringToHex = (str: string): string => {
    return Array.from(str)
      .map((c) => c.charCodeAt(0).toString(16).padStart(2, '0'))
      .join(' ')
  }

  // Convert hex to string
  const hexToString = (hex: string): string => {
    try {
      return hex
        .split(' ')
        .map((byte) => String.fromCharCode(Number.parseInt(byte, 16)))
        .join('')
    } catch (e) {
      return '[Invalid hex]'
    }
  }

  return (
    <div className="space-y-6">
      <Card>
        <CardHeader>
          <CardTitle>串口调试</CardTitle>
          <CardDescription>
            连接和调试串行设备，发送和接收串口数据
          </CardDescription>
        </CardHeader>
        <CardContent className="space-y-4">
          <div className="grid grid-cols-1 md:grid-cols-3 gap-4">
            <div className="space-y-2">
              <Label htmlFor="port">串口</Label>
              <div className="flex space-x-2">
                <Select
                  value={portName}
                  onValueChange={setPortName}
                  disabled={isConnected}>
                  <SelectTrigger id="port" className="flex-1">
                    <SelectValue placeholder="选择串口" />
                  </SelectTrigger>
                  <SelectContent>
                    {availablePorts.map((port) => (
                      <SelectItem key={port} value={port}>
                        {port}
                      </SelectItem>
                    ))}
                  </SelectContent>
                </Select>
                <Button
                  variant="outline"
                  size="icon"
                  onClick={refreshPorts}
                  disabled={isConnected}>
                  <RefreshCw className="h-4 w-4" />
                </Button>
              </div>
            </div>

            <div className="space-y-2">
              <Label htmlFor="baudRate">波特率</Label>
              <Select
                value={baudRate}
                onValueChange={setBaudRate}
                disabled={isConnected}>
                <SelectTrigger id="baudRate">
                  <SelectValue placeholder="选择波特率" />
                </SelectTrigger>
                <SelectContent>
                  <SelectItem value="1200">1200</SelectItem>
                  <SelectItem value="2400">2400</SelectItem>
                  <SelectItem value="4800">4800</SelectItem>
                  <SelectItem value="9600">9600</SelectItem>
                  <SelectItem value="19200">19200</SelectItem>
                  <SelectItem value="38400">38400</SelectItem>
                  <SelectItem value="57600">57600</SelectItem>
                  <SelectItem value="115200">115200</SelectItem>
                </SelectContent>
              </Select>
            </div>

            <div className="space-y-2">
              <Label htmlFor="dataBits">数据位</Label>
              <Select
                value={dataBits}
                onValueChange={setDataBits}
                disabled={isConnected}>
                <SelectTrigger id="dataBits">
                  <SelectValue placeholder="选择数据位" />
                </SelectTrigger>
                <SelectContent>
                  <SelectItem value="5">5</SelectItem>
                  <SelectItem value="6">6</SelectItem>
                  <SelectItem value="7">7</SelectItem>
                  <SelectItem value="8">8</SelectItem>
                </SelectContent>
              </Select>
            </div>
          </div>

          <div className="grid grid-cols-1 md:grid-cols-3 gap-4">
            <div className="space-y-2">
              <Label htmlFor="stopBits">停止位</Label>
              <Select
                value={stopBits}
                onValueChange={setStopBits}
                disabled={isConnected}>
                <SelectTrigger id="stopBits">
                  <SelectValue placeholder="选择停止位" />
                </SelectTrigger>
                <SelectContent>
                  <SelectItem value="1">1</SelectItem>
                  <SelectItem value="1.5">1.5</SelectItem>
                  <SelectItem value="2">2</SelectItem>
                </SelectContent>
              </Select>
            </div>

            <div className="space-y-2">
              <Label htmlFor="parity">校验位</Label>
              <Select
                value={parity}
                onValueChange={setParity}
                disabled={isConnected}>
                <SelectTrigger id="parity">
                  <SelectValue placeholder="选择校验位" />
                </SelectTrigger>
                <SelectContent>
                  <SelectItem value="none">无</SelectItem>
                  <SelectItem value="even">偶校验</SelectItem>
                  <SelectItem value="odd">奇校验</SelectItem>
                  <SelectItem value="mark">标记</SelectItem>
                  <SelectItem value="space">空格</SelectItem>
                </SelectContent>
              </Select>
            </div>

            <div className="space-y-2">
              <Label htmlFor="flowControl">流控制</Label>
              <Select
                value={flowControl}
                onValueChange={setFlowControl}
                disabled={isConnected}>
                <SelectTrigger id="flowControl">
                  <SelectValue placeholder="选择流控制" />
                </SelectTrigger>
                <SelectContent>
                  <SelectItem value="none">无</SelectItem>
                  <SelectItem value="hardware">硬件流控</SelectItem>
                  <SelectItem value="software">软件流控</SelectItem>
                </SelectContent>
              </Select>
            </div>
          </div>
        </CardContent>
        <CardFooter className="flex justify-between">
          <div>
            {!isConnected ? (
              <Button onClick={connectPort} disabled={!portName || !baudRate}>
                <Play className="mr-2 h-4 w-4" />
                连接
              </Button>
            ) : (
              <Button variant="destructive" onClick={disconnectPort}>
                <Square className="mr-2 h-4 w-4" />
                断开
              </Button>
            )}
          </div>

          <div className="flex items-center space-x-2">
            <Terminal
              className={`h-5 w-5 ${
                isConnected ? 'text-green-500' : 'text-muted-foreground'
              }`}
            />
            <span className="text-sm">
              {isConnected ? `已连接到 ${portName} @ ${baudRate}` : '未连接'}
            </span>
          </div>
        </CardFooter>
      </Card>

      <Card>
        <CardHeader className="pb-0">
          <div className="flex justify-between items-center">
            <CardTitle>串口监视器</CardTitle>
            <div className="flex items-center space-x-4">
              <div className="flex items-center space-x-2">
                <Switch
                  id="autoScroll"
                  checked={autoScroll}
                  onCheckedChange={setAutoScroll}
                />
                <Label htmlFor="autoScroll" className="text-sm">
                  自动滚动
                </Label>
              </div>
              <Tabs
                value={displayMode}
                onValueChange={(v) => setDisplayMode(v as 'text' | 'hex')}>
                <TabsList>
                  <TabsTrigger value="text">文本</TabsTrigger>
                  <TabsTrigger value="hex">十六进制</TabsTrigger>
                </TabsList>
              </Tabs>
            </div>
          </div>
        </CardHeader>
        <CardContent className="pt-4">
          <div className="border rounded-md p-4 h-64 overflow-y-auto font-mono text-sm">
            {messages.length === 0 ? (
              <div className="h-full flex items-center justify-center text-muted-foreground">
                <p>无消息记录</p>
              </div>
            ) : (
              messages.map((msg) => (
                <div key={msg.id} className="mb-2">
                  <div className="flex items-center space-x-2">
                    <span className="text-xs text-muted-foreground">
                      {msg.timestamp.toLocaleTimeString()}
                    </span>
                    <span
                      className={`text-xs ${
                        msg.direction === 'send'
                          ? 'text-blue-500'
                          : 'text-green-500'
                      }`}>
                      {msg.direction === 'send' ? '发送' : '接收'}
                    </span>
                  </div>
                  <div
                    className={`pl-4 border-l-2 ${
                      msg.direction === 'send'
                        ? 'border-blue-500'
                        : 'border-green-500'
                    }`}>
                    {displayMode === 'text' ? msg.data : msg.hex}
                  </div>
                </div>
              ))
            )}
            <div ref={messagesEndRef} />
          </div>

          <div className="flex space-x-2 mt-4">
            <Button
              variant="outline"
              size="sm"
              onClick={clearMessages}
              disabled={messages.length === 0}>
              <Trash2 className="mr-2 h-4 w-4" />
              清除
            </Button>
            <Button
              variant="outline"
              size="sm"
              onClick={exportMessages}
              disabled={messages.length === 0}>
              <Download className="mr-2 h-4 w-4" />
              导出
            </Button>
          </div>
        </CardContent>
        <CardFooter>
          <div className="flex space-x-2 w-full">
            <Input
              placeholder="输入要发送的消息..."
              value={message}
              onChange={(e) => setMessage(e.target.value)}
              disabled={!isConnected}
              onKeyDown={(e) => {
                if (e.key === 'Enter' && !e.shiftKey) {
                  e.preventDefault()
                  sendMessage()
                }
              }}
            />
            <Button onClick={sendMessage} disabled={!isConnected || !message}>
              <Send className="mr-2 h-4 w-4" />
              发送
            </Button>
          </div>
        </CardFooter>
      </Card>
    </div>
  )
}
