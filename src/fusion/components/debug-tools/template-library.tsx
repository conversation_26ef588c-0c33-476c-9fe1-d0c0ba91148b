import { useState, useEffect } from 'react'
import {
  Dialog,
  DialogContent,
  DialogDescription,
  DialogHeader,
  DialogTitle,
} from '@/components/ui/dialog'
import { Ta<PERSON>, <PERSON>bsContent, Ta<PERSON>List, TabsTrigger } from '@/components/ui/tabs'
import { Card, CardContent, CardHeader, CardTitle } from '@/components/ui/card'
import { Button } from '@/components/ui/button'
import { Input } from '@/components/ui/input'
import { Badge } from '@/components/ui/badge'
import { ScrollArea } from '@/components/ui/scroll-area'
import {
  Search,
  Star,
  Clock,
  Plus,
  Check,
  Bookmark,
  BookmarkCheck,
} from 'lucide-react'

// 预设模板数据结构
export interface TemplateConfig {
  id: string
  name: string
  description: string
  category: string
  tags: string[]
  thumbnail?: string
  popularity: number
  isOfficial: boolean
  isFavorite?: boolean
  createdAt: string
  updatedAt: string
  config: any
}

// 预设模板数据
const PRESET_TEMPLATES: TemplateConfig[] = [
  {
    id: 'http-get-json',
    name: 'HTTP GET JSON API',
    description: '用于测试返回JSON数据的GET API',
    category: 'http',
    tags: ['HTTP', 'GET', 'JSON', 'API'],
    popularity: 1250,
    isOfficial: true,
    createdAt: '2023-01-15T00:00:00Z',
    updatedAt: '2023-01-15T00:00:00Z',
    config: {
      method: 'GET',
      url: 'https://jsonplaceholder.typicode.com/posts/1',
      headers: [{ key: 'Accept', value: 'application/json' }],
    },
  },
  {
    id: 'http-post-form',
    name: 'HTTP POST 表单提交',
    description: '用于测试表单提交的POST请求',
    category: 'http',
    tags: ['HTTP', 'POST', '表单', 'API'],
    popularity: 980,
    isOfficial: true,
    createdAt: '2023-01-20T00:00:00Z',
    updatedAt: '2023-01-20T00:00:00Z',
    config: {
      method: 'POST',
      url: 'https://jsonplaceholder.typicode.com/posts',
      headers: [
        { key: 'Content-Type', value: 'application/x-www-form-urlencoded' },
      ],
      body: 'title=测试标题&body=测试内容&userId=1',
    },
  },
  {
    id: 'http-post-json',
    name: 'HTTP POST JSON数据',
    description: '用于测试发送JSON数据的POST请求',
    category: 'http',
    tags: ['HTTP', 'POST', 'JSON', 'API'],
    popularity: 1100,
    isOfficial: true,
    createdAt: '2023-01-25T00:00:00Z',
    updatedAt: '2023-01-25T00:00:00Z',
    config: {
      method: 'POST',
      url: 'https://jsonplaceholder.typicode.com/posts',
      headers: [{ key: 'Content-Type', value: 'application/json' }],
      body: JSON.stringify(
        {
          title: '测试标题',
          body: '测试内容',
          userId: 1,
        },
        null,
        2
      ),
    },
  },
  {
    id: 'http-auth-basic',
    name: 'HTTP 基本认证',
    description: '带有基本认证的HTTP请求',
    category: 'http',
    tags: ['HTTP', '认证', 'Basic Auth'],
    popularity: 850,
    isOfficial: true,
    createdAt: '2023-02-05T00:00:00Z',
    updatedAt: '2023-02-05T00:00:00Z',
    config: {
      method: 'GET',
      url: 'https://httpbin.org/basic-auth/user/passwd',
      headers: [{ key: 'Authorization', value: 'Basic dXNlcjpwYXNzd2Q=' }],
    },
  },
  {
    id: 'http-auth-bearer',
    name: 'HTTP Bearer Token认证',
    description: '带有Bearer Token认证的HTTP请求',
    category: 'http',
    tags: ['HTTP', '认证', 'Bearer Token', 'JWT'],
    popularity: 920,
    isOfficial: true,
    createdAt: '2023-02-10T00:00:00Z',
    updatedAt: '2023-02-10T00:00:00Z',
    config: {
      method: 'GET',
      url: 'https://httpbin.org/bearer',
      headers: [
        {
          key: 'Authorization',
          value:
            'Bearer eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.eyJzdWIiOiIxMjM0NTY3ODkwIiwibmFtZSI6IkpvaG4gRG9lIiwiaWF0IjoxNTE2MjM5MDIyfQ.SflKxwRJSMeKKF2QT4fwpMeJf36POk6yJV_adQssw5c',
        },
      ],
    },
  },
  {
    id: 'websocket-echo',
    name: 'WebSocket Echo测试',
    description: '连接到Echo WebSocket服务器进行测试',
    category: 'websocket',
    tags: ['WebSocket', 'Echo', '实时'],
    popularity: 780,
    isOfficial: true,
    createdAt: '2023-03-15T00:00:00Z',
    updatedAt: '2023-03-15T00:00:00Z',
    config: {
      url: 'wss://echo.websocket.org',
      autoConnect: true,
      messages: ['Hello WebSocket!'],
    },
  },
  {
    id: 'mqtt-public-broker',
    name: 'MQTT公共Broker连接',
    description: '连接到公共MQTT Broker进行测试',
    category: 'mqtt',
    tags: ['MQTT', 'IoT', '消息队列'],
    popularity: 650,
    isOfficial: true,
    createdAt: '2023-04-10T00:00:00Z',
    updatedAt: '2023-04-10T00:00:00Z',
    config: {
      broker: 'broker.emqx.io',
      port: 1883,
      clientId: 'mqtt_test_client',
      topic: 'test/topic',
      qos: 0,
    },
  },
  {
    id: 'ping-common-services',
    name: '常用服务Ping测试',
    description: '对常用服务进行Ping测试',
    category: 'ping',
    tags: ['Ping', '网络', '诊断'],
    popularity: 520,
    isOfficial: true,
    createdAt: '2023-05-05T00:00:00Z',
    updatedAt: '2023-05-05T00:00:00Z',
    config: {
      targets: [
        'www.baidu.com',
        'www.google.com',
        'www.github.com',
        'www.microsoft.com',
      ],
      count: 4,
      interval: 1,
    },
  },
]

// 获取用户自定义模板
const getUserTemplates = (): TemplateConfig[] => {
  try {
    const savedTemplates = localStorage.getItem('userTemplates')
    return savedTemplates ? JSON.parse(savedTemplates) : []
  } catch (error) {
    console.error('Failed to load user templates', error)
    return []
  }
}

// 保存用户自定义模板
const saveUserTemplate = (template: TemplateConfig) => {
  try {
    const templates = getUserTemplates()
    const existingIndex = templates.findIndex((t) => t.id === template.id)

    if (existingIndex >= 0) {
      templates[existingIndex] = template
    } else {
      templates.push(template)
    }

    localStorage.setItem('userTemplates', JSON.stringify(templates))
    return true
  } catch (error) {
    console.error('Failed to save user template', error)
    return false
  }
}

// 更新收藏状态
const updateFavoriteStatus = (templateId: string, isFavorite: boolean) => {
  try {
    const favorites = JSON.parse(
      localStorage.getItem('favoriteTemplates') || '[]'
    )

    if (isFavorite && !favorites.includes(templateId)) {
      favorites.push(templateId)
    } else if (!isFavorite && favorites.includes(templateId)) {
      const index = favorites.indexOf(templateId)
      favorites.splice(index, 1)
    }

    localStorage.setItem('favoriteTemplates', JSON.stringify(favorites))
    return true
  } catch (error) {
    console.error('Failed to update favorite status', error)
    return false
  }
}

// 获取收藏状态
const getFavoriteStatus = (templates: TemplateConfig[]): TemplateConfig[] => {
  try {
    const favorites = JSON.parse(
      localStorage.getItem('favoriteTemplates') || '[]'
    )

    return templates.map((template) => ({
      ...template,
      isFavorite: favorites.includes(template.id),
    }))
  } catch (error) {
    console.error('Failed to get favorite status', error)
    return templates
  }
}

interface TemplateLibraryProps {
  isOpen: boolean
  onClose: () => void
  onApplyTemplate: (template: TemplateConfig) => void
  toolType?: string
}

export function TemplateLibrary({
  isOpen,
  onClose,
  onApplyTemplate,
  toolType,
}: TemplateLibraryProps) {
  const [activeTab, setActiveTab] = useState('all')
  const [searchQuery, setSearchQuery] = useState('')
  const [templates, setTemplates] = useState<TemplateConfig[]>([])
  const [userTemplates, setUserTemplates] = useState<TemplateConfig[]>([])
  const [selectedTemplate, setSelectedTemplate] =
    useState<TemplateConfig | null>(null)

  // 初始化模板数据
  useEffect(() => {
    // 加载预设模板
    let filteredTemplates = [...PRESET_TEMPLATES]

    // 如果指定了工具类型，过滤相关模板
    if (toolType) {
      filteredTemplates = filteredTemplates.filter(
        (t) => t.category === toolType
      )
    }

    // 添加收藏状态
    filteredTemplates = getFavoriteStatus(filteredTemplates)

    setTemplates(filteredTemplates)

    // 加载用户模板
    const userTemps = getFavoriteStatus(getUserTemplates())
    if (toolType) {
      setUserTemplates(userTemps.filter((t) => t.category === toolType))
    } else {
      setUserTemplates(userTemps)
    }
  }, [toolType, isOpen])

  // 搜索和过滤模板
  const getFilteredTemplates = () => {
    let filtered = [...templates]

    if (activeTab === 'favorites') {
      filtered = filtered.filter((t) => t.isFavorite)
    } else if (activeTab === 'official') {
      filtered = filtered.filter((t) => t.isOfficial)
    } else if (activeTab === 'user') {
      filtered = [...userTemplates]
    }

    if (searchQuery) {
      const query = searchQuery.toLowerCase()
      filtered = filtered.filter(
        (t) =>
          t.name.toLowerCase().includes(query) ||
          t.description.toLowerCase().includes(query) ||
          t.tags.some((tag) => tag.toLowerCase().includes(query))
      )
    }

    return filtered
  }

  // 切换收藏状态
  const toggleFavorite = (template: TemplateConfig) => {
    const newFavoriteStatus = !template.isFavorite

    if (updateFavoriteStatus(template.id, newFavoriteStatus)) {
      // 更新模板列表
      setTemplates(
        templates.map((t) =>
          t.id === template.id ? { ...t, isFavorite: newFavoriteStatus } : t
        )
      )

      // 更新用户模板列表
      setUserTemplates(
        userTemplates.map((t) =>
          t.id === template.id ? { ...t, isFavorite: newFavoriteStatus } : t
        )
      )

      // 如果当前选中的是这个模板，也更新选中状态
      if (selectedTemplate && selectedTemplate.id === template.id) {
        setSelectedTemplate({
          ...selectedTemplate,
          isFavorite: newFavoriteStatus,
        })
      }
    }
  }

  // 保存当前配置为新模板
  const saveCurrentAsTemplate = () => {
    // 这里应该弹出一个对话框让用户输入模板名称和描述
    const templateName = prompt('请输入模板名称:')
    if (!templateName) return

    const templateDesc = prompt('请输入模板描述:')
    if (!templateDesc) return

    // 获取当前工具的配置
    const currentConfig = {} // 这里应该从当前工具获取配置

    const newTemplate: TemplateConfig = {
      id: `user-${Date.now()}`,
      name: templateName,
      description: templateDesc,
      category: toolType || 'custom',
      tags: ['用户自定义'],
      popularity: 0,
      isOfficial: false,
      isFavorite: false,
      createdAt: new Date().toISOString(),
      updatedAt: new Date().toISOString(),
      config: currentConfig,
    }

    if (saveUserTemplate(newTemplate)) {
      setUserTemplates([...userTemplates, newTemplate])
      alert('模板保存成功!')
    } else {
      alert('模板保存失败!')
    }
  }

  const filteredTemplates = getFilteredTemplates()

  return (
    <Dialog open={isOpen} onOpenChange={onClose}>
      <DialogContent className="max-w-4xl max-h-[80vh] overflow-hidden flex flex-col">
        <DialogHeader>
          <DialogTitle>配置模板库</DialogTitle>
          <DialogDescription>
            选择预设模板快速应用常用配置，或保存当前配置为新模板
          </DialogDescription>
        </DialogHeader>

        <div className="flex space-x-4 mt-4 h-[calc(80vh-120px)]">
          {/* 左侧模板列表 */}
          <div className="w-1/2 flex flex-col">
            <div className="flex items-center space-x-2 mb-4">
              <div className="relative flex-1">
                <Search className="absolute left-2 top-1/2 h-4 w-4 -translate-y-1/2 text-muted-foreground" />
                <Input
                  placeholder="搜索模板..."
                  className="pl-8"
                  value={searchQuery}
                  onChange={(e) => setSearchQuery(e.target.value)}
                />
              </div>
              <Button
                variant="outline"
                size="sm"
                onClick={saveCurrentAsTemplate}>
                <Plus className="h-4 w-4 mr-1" />
                保存当前
              </Button>
            </div>

            <Tabs
              value={activeTab}
              onValueChange={setActiveTab}
              className="flex-1 flex flex-col">
              <TabsList className="grid grid-cols-4">
                <TabsTrigger value="all">全部</TabsTrigger>
                <TabsTrigger value="favorites">收藏</TabsTrigger>
                <TabsTrigger value="official">官方</TabsTrigger>
                <TabsTrigger value="user">我的</TabsTrigger>
              </TabsList>

              <TabsContent value={activeTab} className="flex-1 mt-0">
                <ScrollArea className="h-[calc(80vh-220px)]">
                  <div className="space-y-2 pr-4">
                    {filteredTemplates.length > 0 ? (
                      filteredTemplates.map((template) => (
                        <Card
                          key={template.id}
                          className={`cursor-pointer transition-all hover:border-primary ${
                            selectedTemplate?.id === template.id
                              ? 'border-primary ring-1 ring-primary'
                              : ''
                          }`}
                          onClick={() => setSelectedTemplate(template)}>
                          <CardHeader className="p-3 pb-0">
                            <div className="flex justify-between items-start">
                              <CardTitle className="text-base">
                                {template.name}
                              </CardTitle>
                              <Button
                                variant="ghost"
                                size="icon"
                                className="h-6 w-6"
                                onClick={(e) => {
                                  e.stopPropagation()
                                  toggleFavorite(template)
                                }}>
                                {template.isFavorite ? (
                                  <BookmarkCheck className="h-4 w-4 text-primary" />
                                ) : (
                                  <Bookmark className="h-4 w-4" />
                                )}
                              </Button>
                            </div>
                          </CardHeader>
                          <CardContent className="p-3 pt-1">
                            <p className="text-xs text-muted-foreground line-clamp-2">
                              {template.description}
                            </p>
                            <div className="flex flex-wrap gap-1 mt-2">
                              {template.tags.slice(0, 3).map((tag) => (
                                <Badge
                                  key={tag}
                                  variant="secondary"
                                  className="text-xs">
                                  {tag}
                                </Badge>
                              ))}
                              {template.tags.length > 3 && (
                                <Badge variant="outline" className="text-xs">
                                  +{template.tags.length - 3}
                                </Badge>
                              )}
                            </div>
                          </CardContent>
                        </Card>
                      ))
                    ) : (
                      <div className="flex flex-col items-center justify-center h-40 text-muted-foreground">
                        <p>没有找到匹配的模板</p>
                      </div>
                    )}
                  </div>
                </ScrollArea>
              </TabsContent>
            </Tabs>
          </div>

          {/* 右侧模板详情 */}
          <div className="w-1/2 border rounded-lg p-4 overflow-auto">
            {selectedTemplate ? (
              <div className="h-full flex flex-col">
                <div className="mb-4">
                  <h3 className="text-xl font-bold">{selectedTemplate.name}</h3>
                  <p className="text-muted-foreground mt-1">
                    {selectedTemplate.description}
                  </p>

                  <div className="flex items-center mt-2 text-sm text-muted-foreground">
                    <div className="flex items-center mr-4">
                      <Star className="h-4 w-4 mr-1" />
                      <span>{selectedTemplate.popularity}</span>
                    </div>
                    <div className="flex items-center">
                      <Clock className="h-4 w-4 mr-1" />
                      <span>
                        {new Date(
                          selectedTemplate.updatedAt
                        ).toLocaleDateString()}
                      </span>
                    </div>
                  </div>

                  <div className="flex flex-wrap gap-1 mt-3">
                    {selectedTemplate.tags.map((tag) => (
                      <Badge key={tag} variant="secondary">
                        {tag}
                      </Badge>
                    ))}
                  </div>
                </div>

                <div className="flex-1 overflow-auto">
                  <h4 className="font-medium mb-2">配置预览</h4>
                  <pre className="bg-muted p-3 rounded-md text-xs overflow-auto max-h-[calc(80vh-350px)]">
                    {JSON.stringify(selectedTemplate.config, null, 2)}
                  </pre>
                </div>

                <div className="mt-4 flex justify-end">
                  <Button
                    variant="outline"
                    className="mr-2"
                    onClick={() => toggleFavorite(selectedTemplate)}>
                    {selectedTemplate.isFavorite ? (
                      <>
                        <BookmarkCheck className="h-4 w-4 mr-1" />
                        取消收藏
                      </>
                    ) : (
                      <>
                        <Bookmark className="h-4 w-4 mr-1" />
                        收藏
                      </>
                    )}
                  </Button>
                  <Button
                    onClick={() => {
                      onApplyTemplate(selectedTemplate)
                      onClose()
                    }}>
                    <Check className="h-4 w-4 mr-1" />
                    应用此模板
                  </Button>
                </div>
              </div>
            ) : (
              <div className="h-full flex flex-col items-center justify-center text-muted-foreground">
                <p>选择一个模板查看详情</p>
              </div>
            )}
          </div>
        </div>
      </DialogContent>
    </Dialog>
  )
}
