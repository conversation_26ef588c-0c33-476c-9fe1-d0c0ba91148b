import { Navbar } from '@/components/layout/navbar'
import { NetworkError } from '@/components/ui/network-error'
import { FloatingAssistant } from '@/components/ui/floating-assistant'
import { LockScreenProvider } from '@/lib/lock-screen/lock-screen-context'
import { LockScreen } from '@/components/lock-screen/lock-screen'
import type { ReactNode } from 'react'
import { SignalRFallbackProvider } from '@/components/ui/signalr-fallback'

interface MainLayoutProps {
  children: ReactNode
  showNavbar?: boolean
}

export function MainLayout({ children, showNavbar = true }: MainLayoutProps) {
  return (
    <div className="flex h-screen flex-col">
      <LockScreenProvider>
        {showNavbar && <Navbar />}
        <div className="flex flex-1">
          <SignalRFallbackProvider>
            <main className="flex-1 overflow-auto">{children}</main>
          </SignalRFallbackProvider>
        </div>
        <NetworkError />
        <FloatingAssistant />
        <LockScreen />
      </LockScreenProvider>
    </div>
  )
}

// 同时提供默认导出，以兼容使用默认导入的页面
export default MainLayout
