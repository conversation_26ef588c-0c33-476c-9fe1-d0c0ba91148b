/**
 * 临时简化的MainLayout组件
 * 用于迁移期间避免navbar编码问题
 */

import React from 'react'
import { Link } from 'react-router-dom'
import { FusionTrackLogo } from '@/components/fusion-track-logo'

interface TempMainLayoutProps {
  children: React.ReactNode
  showNavbar?: boolean
}

export function TempMainLayout({ children, showNavbar = true }: TempMainLayoutProps) {
  if (!showNavbar) {
    return <>{children}</>
  }

  return (
    <div className="min-h-screen bg-gray-50">
      {/* 简化的导航栏 */}
      <header className="border-b bg-white shadow-sm">
        <div className="container flex h-16 items-center justify-between">
          <div className="flex items-center space-x-4">
            <FusionTrackLogo size="md" />
            <nav className="flex items-center space-x-4">
              <Link 
                to="/dashboard" 
                className="text-sm font-medium text-gray-700 hover:text-gray-900"
              >
                仪表盘
              </Link>
              <Link 
                to="/examples" 
                className="text-sm font-medium text-gray-700 hover:text-gray-900"
              >
                组件示例
              </Link>
              <Link 
                to="/login" 
                className="text-sm font-medium text-gray-700 hover:text-gray-900"
              >
                登录
              </Link>
            </nav>
          </div>
        </div>
      </header>

      {/* 主要内容区域 */}
      <main className="flex-1">
        {children}
      </main>
    </div>
  )
}
