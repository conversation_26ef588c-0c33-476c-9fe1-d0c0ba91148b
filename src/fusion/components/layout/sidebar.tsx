/**
 * 迁移自: components/layout/sidebar.tsx
 * 迁移时间: 2025-01-14T10:30:00.000Z
 *
 * 迁移说明:
 * - 已替换 next/navigation usePathname 为 react-router-dom useLocation
 * - 已替换 Link href 为 to 属性
 * - 保持所有样式、业务逻辑完全不变
 *
 * 注意: 本文件保持与原始文件100%功能一致性
 */

import { Link, useLocation } from 'react-router-dom'
import { cn } from '@/lib/utils'
import {
  LayoutDashboard,
  Server,
  Database,
  Activity,
  Workflow,
  BarChart2,
  Settings,
  Share,
  BarChart,
  ChevronDown,
  List,
  BellRing,
  CalendarClock,
  ActivityIcon,
  Group,
  FileCode,
  Info,
  RefreshCw,
  FileText,
  Key,
  Sliders,
  Clock,
  Code,
  Terminal,
  FileJson,
  HelpCircle,
  Bell,
  Palette,
  BookOpen,
  TrendingUp,
  HardDrive,
  Plus,
} from 'lucide-react'
import { Button } from '@/components/ui/button'
import { ScrollArea } from '@/components/ui/scroll-area'
import {
  Collapsible,
  CollapsibleContent,
  CollapsibleTrigger,
} from '@/components/ui/collapsible'
import { useState, useEffect } from 'react'

export function Sidebar() {
  const location = useLocation()
  const pathname = location.pathname
  const [openSubmenus, setOpenSubmenus] = useState<Record<string, boolean>>({})

  // 主导航菜单
  const navigation = [
    { name: '仪表盘', href: '/dashboard', icon: LayoutDashboard },
    {
      name: '数据采集',
      href: '/devices/data-collection',
      icon: Server,
      submenu: [
        { name: '设备列表', href: '/devices', icon: List },
        { name: '设备报警', href: '/devices/alarms', icon: BellRing },
        { name: '设备事件', href: '/devices/events', icon: CalendarClock },
        { name: '设备分组', href: '/devices/groups', icon: Group },
        { name: '设备模板', href: '/devices/templates', icon: FileCode },
      ],
    },
    {
      name: '数据转发',
      href: '/data-forwarding',
      icon: Share,
      submenu: [
        { name: '配置管理', href: '/data-forwarding', icon: Settings },
        {
          name: '数据统计',
          href: '/data-forwarding/statistics',
          icon: TrendingUp,
        },
        {
          name: '离线数据',
          href: '/data-forwarding/offline-data',
          icon: HardDrive,
        },
        { name: '新增配置', href: '/data-forwarding/add', icon: Plus },
      ],
    },
    { name: '工作流编排', href: '/workflows', icon: Workflow },
    {
      name: '任务中心',
      href: '/task-center',
      icon: CalendarClock,
      submenu: [
        { name: '任务概览', href: '/task-center', icon: LayoutDashboard },
        { name: '定时任务', href: '/task-center/scheduled-tasks', icon: Clock },
        { name: '任务配置', href: '/task-center/task-config', icon: Settings },
      ],
    },
    {
      name: '数据监控',
      href: '/monitoring',
      icon: Activity,
      submenu: [
        { name: '监控仪表盘', href: '/monitoring', icon: LayoutDashboard },
        { name: '数据库管理', href: '/monitoring/database', icon: Database },
      ],
    },
    { name: '数据分析', href: '/analytics', icon: BarChart2 },
    {
      href: '/data-history',
      name: '历史数据',
      icon: BarChart,
    },
    {
      name: '开发工具',
      href: '/dev-tools',
      icon: Code,
      submenu: [
        { name: 'API管理', href: '/api-management', icon: Code },
        { name: '数据工具', href: '/debug-tools', icon: FileJson },
        { name: '调试工具', href: '/debug-tools', icon: Terminal },
        { name: '脚本模块', href: '/script-modules', icon: FileCode },
        { name: '组件示例', href: '/examples', icon: BookOpen },
      ],
    },
    { name: '通知中心', href: '/notifications', icon: Bell },
    {
      name: '系统设置',
      href: '/settings',
      icon: Settings,
      submenu: [
        { name: '网络配置', href: '/settings/network', icon: Share },
        { name: '系统信息', href: '/settings/info', icon: Info },
        { name: '系统更新', href: '/settings/update', icon: RefreshCw },
        { name: '系统日志', href: '/settings/logs', icon: FileText },
        { name: '系统配置', href: '/settings/config', icon: Settings },
        { name: '品牌设置', href: '/settings/branding', icon: Palette },
        { name: '系统授权', href: '/settings/auth', icon: Key },
        { name: '参数配置', href: '/settings/params', icon: Sliders },
        { name: '系统服务', href: '/settings/services', icon: Server },
      ],
    },
    { name: '帮助中心', href: '/help', icon: HelpCircle },
  ]

  // 检查当前路径是否匹配导航项或其子菜单
  const isActiveNavItem = (item: any) => {
    if (pathname === item.href) return true
    if (item.submenu) {
      return item.submenu.some(
        (subItem: any) =>
          pathname === subItem.href || pathname.startsWith(`${subItem.href}/`)
      )
    }
    return pathname.startsWith(`${item.href}/`)
  }

  // 切换子菜单的展开/折叠状态
  const toggleSubmenu = (name: string) => {
    setOpenSubmenus((prev) => ({
      ...prev,
      [name]: !prev[name],
    }))
  }

  // 当路径变化时，自动展开当前活动的子菜单
  useEffect(() => {
    navigation.forEach((item) => {
      if (item.submenu && isActiveNavItem(item)) {
        setOpenSubmenus((prev) => ({
          ...prev,
          [item.name]: true,
        }))
      }
    })
  }, [pathname])

  return (
    <div className="hidden border-r bg-background md:block md:w-64">
      <div className="flex h-16 items-center border-b px-4">
        <Link to="/" className="flex items-center gap-2 font-semibold">
          <div className="flex h-6 w-6 items-center justify-center rounded-md bg-primary">
            <svg
              xmlns="http://www.w3.org/2000/svg"
              viewBox="0 0 24 24"
              fill="none"
              stroke="currentColor"
              strokeWidth="2"
              strokeLinecap="round"
              strokeLinejoin="round"
              className="h-3 w-3 text-primary-foreground">
              <path d="M18 6H5a2 2 0 0 0-2 2v3a2 2 0 0 0 2 2h13l4-3.5L18 6Z" />
              <path d="M12 13v8" />
              <path d="M5 13v6a2 2 0 0 0 2 2h8" />
            </svg>
          </div>
          <span>工作流编排平台</span>
        </Link>
      </div>
      <ScrollArea className="h-[calc(100vh-4rem)]">
        <div className="px-3 py-2">
          <nav className="grid gap-1 px-2">
            {navigation.map((item) => {
              const isActive = isActiveNavItem(item)

              if (item.submenu) {
                return (
                  <Collapsible
                    key={item.name}
                    open={openSubmenus[item.name]}
                    onOpenChange={() => toggleSubmenu(item.name)}>
                    <CollapsibleTrigger asChild>
                      <Button
                        variant="ghost"
                        className={cn(
                          'flex w-full items-center justify-between px-2',
                          isActive &&
                            !openSubmenus[item.name] &&
                            'bg-accent text-accent-foreground'
                        )}>
                        <div className="flex items-center gap-2">
                          <item.icon className="h-4 w-4" />
                          <span>{item.name}</span>
                        </div>
                        <ChevronDown
                          className={cn(
                            'h-4 w-4 transition-transform',
                            openSubmenus[item.name] && 'rotate-180'
                          )}
                        />
                      </Button>
                    </CollapsibleTrigger>
                    <CollapsibleContent>
                      <div className="ml-4 mt-1 grid gap-1 pl-4 border-l">
                        {item.submenu.map((subItem) => {
                          const isSubActive =
                            pathname === subItem.href ||
                            pathname.startsWith(`${subItem.href}/`)
                          return (
                            <Link
                              key={subItem.name}
                              to={subItem.href}
                              className={cn(
                                'flex items-center gap-2 rounded-md px-2 py-1.5 text-sm',
                                isSubActive
                                  ? 'bg-accent text-accent-foreground font-medium'
                                  : 'text-muted-foreground hover:bg-accent hover:text-accent-foreground'
                              )}>
                              {subItem.icon && (
                                <subItem.icon className="h-4 w-4" />
                              )}
                              <span>{subItem.name}</span>
                            </Link>
                          )
                        })}
                      </div>
                    </CollapsibleContent>
                  </Collapsible>
                )
              }

              return (
                <Link
                  key={item.name}
                  to={item.href}
                  className={cn(
                    'flex items-center gap-2 rounded-md px-2 py-1.5 text-sm',
                    isActive
                      ? 'bg-accent text-accent-foreground font-medium'
                      : 'text-muted-foreground hover:bg-accent hover:text-accent-foreground'
                  )}>
                  <item.icon className="h-4 w-4" />
                  <span>{item.name}</span>
                </Link>
              )
            })}
          </nav>
        </div>
      </ScrollArea>
    </div>
  )
}

// 保留原有的dashboardConfig配置
export const dashboardConfig = {
  mainNav: [
    {
      title: '文档',
      href: '/docs',
    },
    {
      title: '支持',
      href: '/support',
      disabled: true,
    },
  ],
  sidebarNav: [
    {
      title: '仪表盘',
      href: '/dashboard',
      icon: <LayoutDashboard className="h-5 w-5" />,
    },
    {
      title: '历史数据',
      href: '/data-history',
      icon: <BarChart className="h-5 w-5" />,
    },
    {
      title: '设置',
      href: '/dashboard/settings',
      icon: <Settings className="h-5 w-5" />,
    },
  ],
}
