import { useState, useCallback, memo } from 'react'
import { Lock, AlertCircle } from 'lucide-react'
import { Alert, AlertDescription } from '@/components/ui/alert'
import { Button } from '@/components/ui/button'
import { Input } from '@/components/ui/input'
import { Label } from '@/components/ui/label'
import { FusionTrackLogo } from '@/components/fusion-track-logo'
import { useLockScreen } from '@/lib/lock-screen/lock-screen-context'
import {
  Card,
  CardContent,
  CardHeader,
  CardTitle,
  CardDescription,
  CardFooter,
} from '@/components/ui/card'
import { useToast } from '@/components/ui/use-toast'

// 使用React.memo包装组件，当props没有变化时避免重新渲染
const LockScreen = memo(function LockScreen() {
  const { isLocked, unlockScreen, isNetworkConnected, isMounted } =
    useLockScreen()
  const [password, setPassword] = useState('')
  const [error, setError] = useState('')
  const [isUnlocking, setIsUnlocking] = useState(false)
  const { toast } = useToast()

  // 使用useCallback优化输入处理函数
  const handlePasswordChange = useCallback(
    (e: React.ChangeEvent<HTMLInputElement>) => {
      setPassword(e.target.value)
    },
    []
  )

  // 使用useCallback优化事件处理函数
  const handleUnlock = useCallback(
    async (e: React.FormEvent) => {
      e.preventDefault()
      setError('')
      setIsUnlocking(true)

      try {
        const success = await unlockScreen(password)
        if (success) {
          toast({
            title: '解锁成功',
            description: '欢迎回来',
          })
          setPassword('')
        } else {
          setError('密码不正确，请重试')
        }
      } catch (err) {
        setError('解锁时发生错误，请重试')
        console.error('解锁错误:', err)
      } finally {
        setIsUnlocking(false)
      }
    },
    [unlockScreen, password, toast]
  )

  // SSR安全的条件渲染 - 确保服务器端和客户端渲染一致
  // 在组件未挂载时（SSR阶段）不渲染任何内容，避免水合失败
  if (!isMounted) {
    return null
  }

  // 只有在客户端挂载后且需要显示锁屏时才渲染
  if (!isLocked || !isNetworkConnected) {
    return null
  }

  return (
    <div className="fixed inset-0 z-50 bg-gradient-to-br from-gray-900/80 via-gray-950/90 to-gray-900/80 backdrop-blur-md flex items-center justify-center p-6">
      <div className="w-full max-w-lg">
        <div className="flex justify-center mb-8">
          <FusionTrackLogo size="lg" />
        </div>
        <Card className="shadow-2xl border-0 bg-white/95 backdrop-blur-sm">
          <form onSubmit={handleUnlock}>
            <CardHeader className="space-y-2 pb-6">
              <CardTitle className="text-3xl text-center font-semibold text-gray-800">
                系统已锁定
              </CardTitle>
              <CardDescription className="text-center text-lg text-gray-600">
                请输入密码解锁系统
              </CardDescription>
            </CardHeader>
            <CardContent className="space-y-6 px-8">
              {error && (
                <Alert
                  variant="destructive"
                  className="border-red-200 bg-red-50">
                  <AlertCircle className="h-4 w-4" />
                  <AlertDescription className="text-red-700">
                    {error}
                  </AlertDescription>
                </Alert>
              )}

              <div className="space-y-3">
                <Label
                  htmlFor="unlock-password"
                  className="text-base font-medium text-gray-700">
                  密码
                </Label>
                <div className="relative">
                  <Lock className="absolute left-4 top-1/2 transform -translate-y-1/2 h-5 w-5 text-gray-400" />
                  <Input
                    id="unlock-password"
                    type="password"
                    placeholder="••••••••"
                    className="pl-12 h-12 text-base border-gray-300 focus:border-primary focus:ring-2 focus:ring-primary/20 transition-all duration-200"
                    value={password}
                    onChange={handlePasswordChange}
                    required
                    autoFocus
                  />
                </div>
                <p className="text-sm text-gray-500 text-center mt-3">
                  提示：解锁密码为，用户登录密码
                </p>
              </div>
            </CardContent>
            <CardFooter className="px-8 pb-8">
              <Button
                type="submit"
                className="w-full h-12 text-base bg-gradient-to-r from-primary to-primary/90 hover:from-primary/90 hover:to-primary border-0 shadow-lg hover:shadow-xl font-semibold transition-all duration-300 transform hover:scale-[1.02]"
                disabled={isUnlocking}>
                {isUnlocking ? '解锁中...' : '解锁'}
              </Button>
            </CardFooter>
          </form>
        </Card>
      </div>
    </div>
  )
})

// 导出组件
export { LockScreen }
