import { Component, type ErrorInfo, type ReactNode } from 'react'

interface Props {
  children: ReactNode
  fallback?: ReactNode
}

interface State {
  hasError: boolean
  error?: Error
  errorInfo?: ErrorInfo
}

export class ErrorBoundary extends Component<Props, State> {
  constructor(props: Props) {
    super(props)
    this.state = { hasError: false }
  }

  static getDerivedStateFromError(error: Error): State {
    return { hasError: true, error }
  }

  componentDidCatch(error: Error, errorInfo: ErrorInfo): void {
    console.error('ErrorBoundary caught an error:', error, errorInfo)
    this.setState({ errorInfo })
  }

  render() {
    if (this.state.hasError) {
      return (
        <div className="p-4 border border-red-300 bg-red-50 rounded-md">
          <h2 className="text-lg font-bold text-red-700 mb-2">发生错误</h2>
          <details className="whitespace-pre-wrap">
            <summary className="cursor-pointer text-red-600 mb-2">
              查看详细错误信息
            </summary>
            <p className="text-red-600 mb-2">{this.state.error?.toString()}</p>
            <pre className="bg-gray-100 p-2 rounded text-sm overflow-auto">
              {this.state.errorInfo?.componentStack}
            </pre>
          </details>
          {this.props.fallback}
        </div>
      )
    }

    return this.props.children
  }
}
