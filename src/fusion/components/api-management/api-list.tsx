import { Badge } from "@/components/ui/badge"

interface ApiListProps {
  limit?: number
}

export function ApiList({ limit }: ApiListProps) {
  // 模拟API数据
  const apis = [
    {
      id: 1,
      name: "获取设备列表",
      path: "/api/devices",
      method: "GET",
      lastCalled: "10分钟前",
      calls: 1243,
      category: "设备管理",
    },
    {
      id: 2,
      name: "创建设备",
      path: "/api/devices",
      method: "POST",
      lastCalled: "30分钟前",
      calls: 567,
      category: "设备管理",
    },
    {
      id: 3,
      name: "获取数据采集任务",
      path: "/api/data/collection",
      method: "GET",
      lastCalled: "1小时前",
      calls: 892,
      category: "数据管理",
    },
    {
      id: 4,
      name: "获取工作流列表",
      path: "/api/workflows",
      method: "GET",
      lastCalled: "2小时前",
      calls: 456,
      category: "工作流",
    },
    {
      id: 5,
      name: "获取设备详情",
      path: "/api/devices/{id}",
      method: "GET",
      lastCalled: "3小时前",
      calls: 1876,
      category: "设备管理",
    },
    {
      id: 6,
      name: "更新设备",
      path: "/api/devices/{id}",
      method: "PUT",
      lastCalled: "5小时前",
      calls: 342,
      category: "设备管理",
    },
    {
      id: 7,
      name: "删除设备",
      path: "/api/devices/{id}",
      method: "DELETE",
      lastCalled: "1天前",
      calls: 124,
      category: "设备管理",
    },
  ]

  // 如果有限制，则只显示指定数量的API
  const displayedApis = limit ? apis.slice(0, limit) : apis

  // 获取HTTP方法对应的颜色
  const getMethodColor = (method) => {
    switch (method) {
      case "GET":
        return "bg-blue-100 text-blue-800"
      case "POST":
        return "bg-green-100 text-green-800"
      case "PUT":
        return "bg-amber-100 text-amber-800"
      case "DELETE":
        return "bg-red-100 text-red-800"
      default:
        return "bg-gray-100 text-gray-800"
    }
  }

  return (
    <div className="rounded-md border">
      <div className="grid grid-cols-6 p-4 font-medium border-b">
        <div className="col-span-2">API名称</div>
        <div>方法</div>
        <div>最后调用</div>
        <div>调用次数</div>
        <div>分类</div>
      </div>
      <div className="divide-y">
        {displayedApis.map((api) => (
          <div key={api.id} className="grid grid-cols-6 p-4 items-center hover:bg-muted/50">
            <div className="col-span-2">
              <div className="font-medium">{api.name}</div>
              <div className="text-xs font-mono text-muted-foreground">{api.path}</div>
            </div>
            <div>
              <Badge className={getMethodColor(api.method)}>{api.method}</Badge>
            </div>
            <div>{api.lastCalled}</div>
            <div>{api.calls.toLocaleString()}</div>
            <div>{api.category}</div>
          </div>
        ))}
      </div>
    </div>
  )
}
