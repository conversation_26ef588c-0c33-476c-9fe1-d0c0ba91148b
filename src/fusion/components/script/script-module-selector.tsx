import { useState, useEffect } from 'react'
import { Dialog, DialogContent } from '@/components/ui/dialog'
import { But<PERSON> } from '@/components/ui/button'
import { Input } from '@/components/ui/input'
import { ScrollArea } from '@/components/ui/scroll-area'
import { <PERSON><PERSON>, <PERSON><PERSON><PERSON>ontent, TabsList, TabsTrigger } from '@/components/ui/tabs'
import { Badge } from '@/components/ui/badge'
import { Search, Tag, Clock, Code, FileText, Check, X } from 'lucide-react'
import { cn } from '@/lib/utils'

// 模拟脚本模块数据
interface ScriptModule {
  id: string
  name: string
  description: string
  type: string
  version: string
  tags: string[]
  code: string
  updatedAt: string
  author: string
}

// 模拟获取脚本模块的API
const getScriptModules = async (): Promise<ScriptModule[]> => {
  // 模拟API延迟
  await new Promise((resolve) => setTimeout(resolve, 500))

  return [
    {
      id: 'module1',
      name: '时间格式化工具',
      description: '提供各种时间格式化函数，支持自定义格式',
      type: '数据处理',
      version: '1.0.0',
      tags: ['时间', '格式化', '工具'],
      code: "// 时间格式化模块\nexport function formatDate(date, format = 'yyyy-MM-dd') {\n  // 格式化逻辑\n  return '2023-01-01';\n}\n\nexport function formatTime(date, format = 'HH:mm:ss') {\n  // 格式化逻辑\n  return '12:00:00';\n}",
      updatedAt: '2023-01-15T08:30:00Z',
      author: '系统管理员',
    },
    {
      id: 'module2',
      name: '数值计算工具',
      description: '提供常用的数值计算函数，如四舍五入、范围限制等',
      type: '计算公式',
      version: '1.1.2',
      tags: ['数值', '计算', '四舍五入'],
      code: "// 数值计算模块\nexport function round(value, decimals = 2) {\n  return Number(Math.round(value + 'e' + decimals) + 'e-' + decimals);\n}\n\nexport function clamp(value, min, max) {\n  return Math.min(Math.max(value, min), max);\n}",
      updatedAt: '2023-02-20T14:15:00Z',
      author: '张工程师',
    },
    {
      id: 'module3',
      name: '单位转换工具',
      description: '提供各种单位之间的转换函数，如温度、长度、重量等',
      type: '数据处理',
      version: '1.0.5',
      tags: ['单位转换', '温度', '长度'],
      code: '// 单位转换模块\nexport function celsiusToFahrenheit(celsius) {\n  return celsius * 9/5 + 32;\n}\n\nexport function fahrenheitToCelsius(fahrenheit) {\n  return (fahrenheit - 32) * 5/9;\n}\n\nexport function metersToFeet(meters) {\n  return meters * 3.28084;\n}',
      updatedAt: '2023-03-10T09:45:00Z',
      author: '李工程师',
    },
    {
      id: 'module4',
      name: '条件判断助手',
      description: '提供复杂条件判断的辅助函数，简化逻辑表达式',
      type: '条件判断',
      version: '1.2.0',
      tags: ['条件', '判断', '逻辑'],
      code: '// 条件判断模块\nexport function isInRange(value, min, max) {\n  return value >= min && value <= max;\n}\n\nexport function isAnyOf(value, ...options) {\n  return options.includes(value);\n}\n\nexport function isAllTrue(...conditions) {\n  return conditions.every(Boolean);\n}',
      updatedAt: '2023-04-05T16:20:00Z',
      author: '王工程师',
    },
    {
      id: 'module5',
      name: '字符串处理工具',
      description: '提供字符串处理函数，如截取、替换、格式化等',
      type: '数据处理',
      version: '1.1.0',
      tags: ['字符串', '文本', '格式化'],
      code: "// 字符串处理模块\nexport function truncate(str, length, ending = '...') {\n  return str.length > length ? str.substring(0, length - ending.length) + ending : str;\n}\n\nexport function padLeft(str, length, char = ' ') {\n  return str.length >= length ? str : (char.repeat(length - str.length) + str);\n}\n\nexport function padRight(str, length, char = ' ') {\n  return str.length >= length ? str : (str + char.repeat(length - str.length));\n}",
      updatedAt: '2023-05-12T11:30:00Z',
      author: '赵工程师',
    },
  ]
}

interface ScriptModuleSelectorProps {
  open: boolean
  onOpenChange: (open: boolean) => void
  onSelect: (module: ScriptModule) => void
}

export function ScriptModuleSelector({
  open,
  onOpenChange,
  onSelect,
}: ScriptModuleSelectorProps) {
  const [modules, setModules] = useState<ScriptModule[]>([])
  const [filteredModules, setFilteredModules] = useState<ScriptModule[]>([])
  const [searchQuery, setSearchQuery] = useState('')
  const [selectedType, setSelectedType] = useState<string>('all')
  const [loading, setLoading] = useState(false)
  const [selectedModule, setSelectedModule] = useState<ScriptModule | null>(
    null
  )

  // 加载脚本模块
  useEffect(() => {
    if (open) {
      loadModules()
    }
  }, [open])

  const loadModules = async () => {
    setLoading(true)
    try {
      const data = await getScriptModules()
      setModules(data)
      setFilteredModules(data)
    } catch (error) {
      console.error('加载脚本模块失败:', error)
    } finally {
      setLoading(false)
    }
  }

  // 处理搜索和筛选
  useEffect(() => {
    let result = [...modules]

    // 按类型筛选
    if (selectedType !== 'all') {
      result = result.filter((module) => module.type === selectedType)
    }

    // 按搜索词筛选
    if (searchQuery) {
      const query = searchQuery.toLowerCase()
      result = result.filter(
        (module) =>
          module.name.toLowerCase().includes(query) ||
          module.description.toLowerCase().includes(query) ||
          module.tags.some((tag) => tag.toLowerCase().includes(query))
      )
    }

    setFilteredModules(result)
  }, [modules, searchQuery, selectedType])

  // 处理模块选择
  const handleSelect = () => {
    if (selectedModule) {
      onSelect(selectedModule)
      onOpenChange(false)
    }
  }

  // 获取所有可用的模块类型
  const moduleTypes = Array.from(new Set(modules.map((m) => m.type)))

  return (
    <Dialog open={open} onOpenChange={onOpenChange}>
      <DialogContent className="max-w-4xl max-h-[80vh] p-0 overflow-hidden flex flex-col bg-gray-900 text-white border-gray-700">
        {/* 标题栏 */}
        <div className="flex justify-between items-center px-4 py-2 border-b border-gray-700">
          <h2 className="text-lg font-medium">选择脚本模块</h2>
          <button
            onClick={() => onOpenChange(false)}
            className="rounded-full p-1 hover:bg-gray-700 transition-colors">
            <X className="h-5 w-5" />
          </button>
        </div>

        {/* 搜索栏 */}
        <div className="px-4 py-3 border-b border-gray-700 bg-gray-800">
          <div className="relative">
            <Search className="absolute left-3 top-1/2 transform -translate-y-1/2 h-4 w-4 text-gray-400" />
            <Input
              placeholder="搜索模块名称、描述或标签..."
              value={searchQuery}
              onChange={(e) => setSearchQuery(e.target.value)}
              className="pl-9 bg-gray-800 border-gray-700 text-white"
            />
          </div>
        </div>

        {/* 主内容区 */}
        <div className="flex-1 flex flex-col overflow-hidden">
          <Tabs
            defaultValue={selectedType}
            onValueChange={setSelectedType}
            className="flex-1 flex flex-col">
            <div className="border-b border-gray-700 bg-gray-800">
              <TabsList className="p-0 h-auto bg-transparent">
                <TabsTrigger
                  value="all"
                  className={cn(
                    'px-4 py-2 rounded-none data-[state=active]:bg-gray-900 border-b-2 border-transparent data-[state=active]:border-blue-500',
                    'transition-all data-[state=active]:shadow-none text-gray-300 data-[state=active]:text-white'
                  )}>
                  全部
                </TabsTrigger>
                {moduleTypes.map((type) => (
                  <TabsTrigger
                    key={type}
                    value={type}
                    className={cn(
                      'px-4 py-2 rounded-none data-[state=active]:bg-gray-900 border-b-2 border-transparent data-[state=active]:border-blue-500',
                      'transition-all data-[state=active]:shadow-none text-gray-300 data-[state=active]:text-white'
                    )}>
                    {type}
                  </TabsTrigger>
                ))}
              </TabsList>
            </div>

            <TabsContent
              value={selectedType}
              className="flex-1 p-0 m-0 border-0">
              <div className="grid grid-cols-[1fr_1.5fr] h-full">
                {/* 模块列表 */}
                <div className="border-r border-gray-700 overflow-hidden flex flex-col bg-gray-800">
                  <div className="p-3 border-b border-gray-700">
                    <div className="text-sm font-medium">模块列表</div>
                  </div>

                  <ScrollArea className="flex-1">
                    {loading ? (
                      <div className="flex items-center justify-center h-full p-4">
                        <div className="flex flex-col items-center gap-2">
                          <div className="h-8 w-8 animate-spin rounded-full border-2 border-primary border-t-transparent"></div>
                          <p className="text-sm text-gray-400">加载模块中...</p>
                        </div>
                      </div>
                    ) : filteredModules.length === 0 ? (
                      <div className="flex flex-col items-center justify-center h-full p-4 text-gray-400">
                        <FileText className="h-12 w-12 mb-2" />
                        <p>没有找到匹配的模块</p>
                      </div>
                    ) : (
                      <div className="divide-y divide-gray-700">
                        {filteredModules.map((module) => (
                          <div
                            key={module.id}
                            className={cn(
                              'p-3 cursor-pointer transition-colors hover:bg-gray-700',
                              selectedModule?.id === module.id && 'bg-gray-700'
                            )}
                            onClick={() => setSelectedModule(module)}>
                            <div className="flex items-center justify-between">
                              <h3 className="font-medium">{module.name}</h3>
                              {selectedModule?.id === module.id && (
                                <Check className="h-4 w-4 text-blue-400" />
                              )}
                            </div>
                            <p className="text-sm text-gray-400 line-clamp-2 mt-1">
                              {module.description}
                            </p>
                            <div className="flex flex-wrap gap-1 mt-2">
                              {module.tags.map((tag) => (
                                <Badge
                                  key={tag}
                                  variant="outline"
                                  className="text-xs bg-gray-700 text-gray-300 border-gray-600">
                                  {tag}
                                </Badge>
                              ))}
                            </div>
                            <div className="flex items-center mt-2 text-xs text-gray-400">
                              <Badge
                                variant="secondary"
                                className="mr-2 text-xs bg-gray-700 text-gray-300">
                                {module.type}
                              </Badge>
                              <Clock className="h-3 w-3 mr-1" />
                              <span>
                                {new Date(
                                  module.updatedAt
                                ).toLocaleDateString()}
                              </span>
                            </div>
                          </div>
                        ))}
                      </div>
                    )}
                  </ScrollArea>
                </div>

                {/* 模块详情 */}
                <div className="overflow-hidden flex flex-col bg-gray-900">
                  {selectedModule ? (
                    <>
                      <div className="p-3 border-b border-gray-700 bg-gray-800">
                        <div className="text-sm font-medium">模块详情</div>
                      </div>
                      <div className="p-4 overflow-auto flex-1">
                        <div className="mb-4">
                          <h2 className="text-xl font-semibold">
                            {selectedModule.name}
                          </h2>
                          <p className="text-gray-300 mt-1">
                            {selectedModule.description}
                          </p>
                          <div className="flex flex-wrap gap-1 mt-2">
                            {selectedModule.tags.map((tag) => (
                              <Badge
                                key={tag}
                                variant="secondary"
                                className="flex items-center bg-gray-700 text-gray-300">
                                <Tag className="h-3 w-3 mr-1" />
                                {tag}
                              </Badge>
                            ))}
                          </div>
                          <div className="flex items-center mt-2 text-sm text-gray-400">
                            <span className="mr-4">
                              版本: {selectedModule.version}
                            </span>
                            <span>
                              更新时间:{' '}
                              {new Date(
                                selectedModule.updatedAt
                              ).toLocaleString()}
                            </span>
                          </div>
                          <div className="text-sm text-gray-400 mt-1">
                            作者: {selectedModule.author}
                          </div>
                        </div>

                        <div className="mb-2 text-sm font-medium">模块代码</div>
                        <pre className="text-sm font-mono bg-gray-800 p-3 rounded-md overflow-auto max-h-[300px] border border-gray-700 text-gray-300">
                          {selectedModule.code}
                        </pre>
                      </div>
                    </>
                  ) : (
                    <div className="flex flex-col items-center justify-center h-full text-gray-400">
                      <Code className="h-16 w-16 mb-2" />
                      <p className="text-lg">请从左侧选择一个脚本模块</p>
                    </div>
                  )}
                </div>
              </div>
            </TabsContent>
          </Tabs>
        </div>

        {/* 底部按钮 */}
        <div className="flex justify-end items-center px-4 py-3 border-t border-gray-700 bg-gray-800">
          <Button
            variant="outline"
            onClick={() => onOpenChange(false)}
            className="mr-2 bg-gray-700 border-gray-600 hover:bg-gray-600 text-white">
            取消
          </Button>
          <Button
            onClick={handleSelect}
            disabled={!selectedModule}
            className="bg-blue-600 hover:bg-blue-700 text-white">
            选择并使用
          </Button>
        </div>
      </DialogContent>
    </Dialog>
  )
}
