import { useState } from 'react'
import { FileJson } from 'lucide-react'
import { Button } from '@/components/ui/button'
import { Label } from '@/components/ui/label'
import { ToolDialog } from './tool-dialog'

interface JsonFormatterProps {
  open: boolean
  onClose: () => void
}

export function JsonFormatter({ open, onClose }: JsonFormatterProps) {
  const [jsonInput, setJsonInput] = useState('')
  const [jsonOutput, setJsonOutput] = useState('')

  const formatJSON = () => {
    try {
      const parsed = JSON.parse(jsonInput)
      setJsonOutput(JSON.stringify(parsed, null, 2))
    } catch (e) {
      setJsonOutput(`错误: ${(e as Error).message}`)
    }
  }

  const clearJSON = () => {
    setJsonInput('')
    setJsonOutput('')
  }

  return (
    <ToolDialog
      open={open}
      onClose={onClose}
      title="JSON 格式化工具"
      icon={<FileJson className="h-5 w-5" />}>
      <div className="p-6">
        <div className="grid grid-cols-1 lg:grid-cols-2 gap-6 h-full">
          <div className="space-y-3 h-full flex flex-col">
            <Label htmlFor="json-input" className="text-base">
              输入 JSON
            </Label>
            <textarea
              id="json-input"
              className="w-full flex-1 p-3 border rounded-md font-mono text-sm"
              value={jsonInput}
              onChange={(e) => setJsonInput(e.target.value)}
              placeholder="在此粘贴或输入JSON..."
            />
            <div className="flex gap-2">
              <Button size="lg" onClick={formatJSON}>
                格式化
              </Button>
              <Button variant="outline" size="lg" onClick={clearJSON}>
                清空
              </Button>
            </div>
          </div>
          <div className="space-y-3 h-full flex flex-col">
            <Label htmlFor="json-output" className="text-base">
              格式化结果
            </Label>
            <textarea
              id="json-output"
              className="w-full flex-1 p-3 border rounded-md font-mono text-sm bg-gray-50"
              value={jsonOutput}
              readOnly
            />
            <Button
              variant="outline"
              size="lg"
              onClick={() => navigator.clipboard.writeText(jsonOutput)}
              disabled={!jsonOutput}>
              复制结果
            </Button>
          </div>
        </div>
      </div>
    </ToolDialog>
  )
}
