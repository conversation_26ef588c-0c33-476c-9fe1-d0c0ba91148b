import { useEffect, useState, useC<PERSON>back, useMemo } from 'react'
import {
  RefreshCw,
  LayoutGrid,
  LayoutList,
  ChevronDown,
  ChevronRight,
  ChevronLeft,
  ChevronUp,
  Zap,
} from 'lucide-react'
import { Button } from '@/components/ui/button'
import { Card, CardContent, CardHeader, CardTitle } from '@/components/ui/card'
import { <PERSON><PERSON>, TabsContent, TabsList, TabsTrigger } from '@/components/ui/tabs'
import { Badge } from '@/components/ui/badge'
import { toast } from '@/components/ui/use-toast'
import {
  useSignalR,
  useSignalREvent,
  ConnectionState,
} from '@/lib/signalr/signalr-context'
import { SignalRStatus } from '@/components/ui/signalr-status'
import {
  Tooltip,
  TooltipContent,
  TooltipProvider,
  TooltipTrigger,
} from '@/components/ui/tooltip'
import { TrendChart } from './trend-chart'
import {
  getTagDataType,
  isNumericTag,
  getDataTypeDisplayName,
} from '@/lib/utils/data-type-utils'

// 添加自定义样式
const customStyles = `
  @keyframes gentle-pulse {
    0% { opacity: 1; transform: scale(1); }
    50% { opacity: 0.8; transform: scale(1.02); }
    100% { opacity: 1; transform: scale(1); }
  }
  
  @keyframes update-glow {
    0% { box-shadow: 0 0 0 0 rgba(59, 130, 246, 0.3); }
    50% { box-shadow: 0 0 0 4px rgba(59, 130, 246, 0.1); }
    100% { box-shadow: 0 0 0 0 rgba(59, 130, 246, 0); }
  }
  
  @keyframes slide-in-left {
    0% { transform: translateX(-100%); opacity: 0; }
    100% { transform: translateX(0); opacity: 1; }
  }
  
  .gentle-pulse {
    animation: gentle-pulse 1.5s ease-in-out;
  }
  
  .update-glow {
    animation: update-glow 1.5s ease-out;
  }
  
  .slide-in-left {
    animation: slide-in-left 0.3s ease-out;
  }
  
  .update-indicator {
    position: relative;
  }
  
  .update-indicator::before {
    content: '';
    position: absolute;
    top: -2px;
    right: -2px;
    width: 8px;
    height: 8px;
    background: linear-gradient(45deg, #3b82f6, #1d4ed8);
    border-radius: 50%;
    box-shadow: 0 0 0 2px white, 0 2px 4px rgba(59, 130, 246, 0.3);
    z-index: 10;
  }
`

// 注入样式
if (typeof document !== 'undefined') {
  const styleElement = document.getElementById('device-dashboard-styles')
  if (!styleElement) {
    const style = document.createElement('style')
    style.id = 'device-dashboard-styles'
    style.textContent = customStyles
    document.head.appendChild(style)
  }
}

interface Tag {
  id: string
  name: string
  identifier: string
  value: number | string
  unit?: string
  transitionType?: string | null
  timestamp?: string
  time?: number
  description?: string
  status?: string
  errMsg?: string | null
  enabled?: boolean
  custom?: Record<string, string>
}

interface DeviceDashboardProps {
  deviceId: string
  tags: Tag[]
  onRefresh: () => void
}

// 添加值显示组件
function ValueDisplay({
  value,
  className,
  animated = false,
  noDataReceived = false,
  disabled = false,
  custom,
}: {
  value: any
  className?: string
  animated?: boolean
  noDataReceived?: boolean
  disabled?: boolean
  custom?: Record<string, string>
}) {
  const [expanded, setExpanded] = useState(false)

  // 如果点位被禁用，显示禁用状态
  if (disabled) {
    return <span className="text-gray-400 italic font-normal">已禁用</span>
  }

  // 如果没有收到数据，显示特殊状态
  if (noDataReceived) {
    return (
      <span className="text-amber-500 italic font-normal">等待数据...</span>
    )
  }

  // 处理自定义值映射
  const renderMappedValue = (originalValue: any) => {
    if (
      !custom ||
      (typeof originalValue !== 'string' && typeof originalValue !== 'number')
    ) {
      return <span>{String(originalValue)}</span>
    }

    const stringValue = String(originalValue)
    const mappedValue = custom[stringValue]

    if (mappedValue) {
      return (
        <span>
          {stringValue}
          <span className="text-gray-500 ml-1">({mappedValue})</span>
        </span>
      )
    }

    return <span>{stringValue}</span>
  }

  // 处理不同类型的值显示
  const renderValue = () => {
    // 处理null或undefined
    if (value === null || value === undefined) {
      return <span className="text-rose-500 italic">无数据</span>
    }

    // 处理数组
    if (Array.isArray(value)) {
      if (value.length === 0) {
        return <span className="text-muted-foreground italic">空数组</span>
      }

      if (!expanded) {
        return (
          <div
            className="flex items-center cursor-pointer"
            onClick={() => setExpanded(true)}>
            <ChevronRight className="h-4 w-4 mr-1" />
            <span>数组 [{value.length}项]</span>
          </div>
        )
      }

      return (
        <div>
          <div
            className="flex items-center cursor-pointer mb-1"
            onClick={() => setExpanded(false)}>
            <ChevronDown className="h-4 w-4 mr-1" />
            <span>数组 [{value.length}项]</span>
          </div>
          <div className="pl-4 border-l-2 border-gray-200 space-y-1 text-sm">
            {value.map((item, index) => (
              <div key={index} className="flex">
                <span className="text-muted-foreground mr-2">{index}:</span>
                <ValueDisplay value={item} custom={custom} />
              </div>
            ))}
          </div>
        </div>
      )
    }

    // 处理对象
    if (typeof value === 'object') {
      const keys = Object.keys(value)
      if (keys.length === 0) {
        return <span className="text-muted-foreground italic">空对象</span>
      }

      if (!expanded) {
        return (
          <div
            className="flex items-center cursor-pointer"
            onClick={() => setExpanded(true)}>
            <ChevronRight className="h-4 w-4 mr-1" />
            <span>对象 {`{${keys.length}个属性}`}</span>
          </div>
        )
      }

      return (
        <div>
          <div
            className="flex items-center cursor-pointer mb-1"
            onClick={() => setExpanded(false)}>
            <ChevronDown className="h-4 w-4 mr-1" />
            <span>对象 {`{${keys.length}个属性}`}</span>
          </div>
          <div className="pl-4 border-l-2 border-gray-200 space-y-1 text-sm">
            {keys.map((key) => (
              <div key={key} className="flex">
                <span className="text-muted-foreground mr-2">{key}:</span>
                <ValueDisplay value={value[key]} custom={custom} />
              </div>
            ))}
          </div>
        </div>
      )
    }

    // 处理字符串
    if (typeof value === 'string') {
      const isLongString = value.length > 30
      if (isLongString && !expanded) {
        return (
          <TooltipProvider>
            <Tooltip>
              <TooltipTrigger asChild>
                <span
                  className="cursor-pointer"
                  onClick={() => setExpanded(true)}>
                  {custom && custom[value] ? (
                    <>
                      {value.substring(0, 30)}...
                      <span className="text-gray-500 ml-1">
                        ({custom[value]})
                      </span>
                    </>
                  ) : (
                    `${value.substring(0, 30)}...`
                  )}
                </span>
              </TooltipTrigger>
              <TooltipContent>
                <p className="max-w-md break-all">
                  {custom && custom[value] ? (
                    <>
                      {value} ({custom[value]})
                    </>
                  ) : (
                    value
                  )}
                </p>
              </TooltipContent>
            </Tooltip>
          </TooltipProvider>
        )
      }

      if (isLongString && expanded) {
        return (
          <div>
            <div className="break-all">{renderMappedValue(value)}</div>
            <Button
              variant="ghost"
              size="sm"
              className="mt-1 h-6 text-xs px-2"
              onClick={() => setExpanded(false)}>
              折叠
            </Button>
          </div>
        )
      }

      return renderMappedValue(value)
    }

    // 处理数字和布尔值
    return renderMappedValue(value)
  }

  return (
    <div className={`${className} ${animated ? 'gentle-pulse' : ''}`}>
      {renderValue()}
    </div>
  )
}

export function DeviceDashboard({
  deviceId,
  tags,
  onRefresh,
}: DeviceDashboardProps) {
  const [layout, setLayout] = useState<'grid' | 'list'>('grid')
  const [tagData, setTagData] = useState<Tag[]>(tags)
  const [isLoading, setIsLoading] = useState(false)
  const [error, setError] = useState<string | null>(null)
  // 添加状态管理以跟踪最近更新的点位
  const [recentlyUpdatedTags, setRecentlyUpdatedTags] = useState<Set<string>>(
    new Set()
  )
  // 添加订阅状态管理
  const [subscribed, setSubscribed] = useState(false)

  // 添加分页状态
  const [currentPage, setCurrentPage] = useState(1)
  const [itemsPerPage, setItemsPerPage] = useState(16) // 默认每页16个点位（网格布局4×4）
  const [totalItems, setTotalItems] = useState(0)

  // 获取SignalR上下文
  const { connectionState, connection, sendMessage } = useSignalR()

  // 计算总页数
  const totalPages = useMemo(() => {
    return Math.ceil(tagData.length / itemsPerPage)
  }, [tagData.length, itemsPerPage])

  // 根据布局调整每页显示数量
  useEffect(() => {
    // 网格布局时每页显示16个，列表布局时每页显示20个
    setItemsPerPage(layout === 'grid' ? 16 : 20)
    // 切换布局时重置为第一页
    setCurrentPage(1)
  }, [layout])

  // 获取当前页的点位数据
  const currentPageData = useMemo(() => {
    const startIndex = (currentPage - 1) * itemsPerPage
    const endIndex = startIndex + itemsPerPage
    return tagData.slice(startIndex, endIndex)
  }, [currentPage, itemsPerPage, tagData])

  // 页码变化处理
  const handlePageChange = (page: number) => {
    if (page < 1 || page > totalPages) return
    setCurrentPage(page)
  }

  // Format the timestamp for display
  const formatTime = (timestamp?: string, time?: number) => {
    try {
      if (time) {
        return new Date(time).toLocaleTimeString()
      }
      if (timestamp) {
        return new Date(timestamp).toLocaleTimeString()
      }
      return '-'
    } catch (e) {
      return timestamp || '-'
    }
  }

  // 添加趋势图实时更新处理器状态
  const [trendUpdateHandler, setTrendUpdateHandler] = useState<
    ((data: any) => void) | null
  >(null)

  // 修改现有的实时数据更新处理函数，同时更新趋势图
  const handleRealTimeUpdate = useCallback(
    (data: any) => {
      // 检查数据格式并提取点位信息
      if (!data) {
        console.warn('接收到空的实时数据')
        return
      }

      // 追踪已更新的点位ID
      const updatedIds = new Set<string>()

      // 更新点位数据 - 支持数组和单个对象两种格式
      setTagData((prevTags) => {
        // 将数据统一处理为数组格式
        const dataArray = Array.isArray(data) ? data : [data]

        return prevTags.map((tag) => {
          // 查找此点位ID匹配的更新数据
          const updatedData = dataArray.find(
            (item: any) => item.id === tag.identifier
          )

          if (updatedData) {
            // 记录此点位已更新
            updatedIds.add(tag.id)

            // 返回更新后的点位数据
            return {
              ...tag,
              value:
                updatedData.value !== undefined ? updatedData.value : tag.value,
              status: updatedData.status || tag.status,
              timestamp: updatedData.timestamp || new Date().toISOString(),
              time: updatedData.time || Date.now(),
            }
          }
          return tag
        })
      })

      // 设置最近更新的点位列表
      setRecentlyUpdatedTags(updatedIds)

      // 同时更新趋势图数据
      if (trendUpdateHandler) {
        // 将数据转换为趋势图可以理解的格式
        const trendUpdateData = Array.isArray(data) ? data : [data]
        const formattedData = trendUpdateData.map((item: any) => ({
          id: item.id,
          value: item.value,
          timestamp: item.timestamp,
          time: item.time,
        }))
        trendUpdateHandler(formattedData)
      }

      // 1.5秒后清除高亮效果
      setTimeout(() => {
        setRecentlyUpdatedTags(new Set())
      }, 1500)
    },
    [trendUpdateHandler]
  )

  // 处理趋势图的实时更新回调注册
  const handleTrendUpdateHandlerRegistration = useCallback(
    (handler: (data: any) => void) => {
      setTrendUpdateHandler(() => handler)
    },
    []
  )

  // 订阅实时数据更新
  useSignalREvent('online', handleRealTimeUpdate, [
    deviceId,
    handleRealTimeUpdate,
  ])

  // 订阅管理
  useEffect(() => {
    // 当连接状态变为已连接且未订阅时，订阅设备点位更新
    if (connectionState === 'connected' && !subscribed && deviceId) {
      const subscribe = async () => {
        try {
          // 将主题名称和设备ID组合成一个字符串，类似于首页使用模式
          await sendMessage('SubscribeTopic', `online`)
          setSubscribed(true)
          toast({
            title: '实时数据已连接',
            description: '正在接收设备点位实时更新',
            variant: 'success',
          })
        } catch (error) {
          console.error('订阅失败:', error)
          setSubscribed(false)
          // 显示详细错误信息
          toast({
            title: '订阅失败',
            description: '无法订阅实时数据更新，请稍后重试',
            variant: 'destructive',
          })
        }
      }

      subscribe()
    }

    // 组件卸载时取消订阅
    return () => {
      if (subscribed && connection && deviceId) {
        // 使用相同的组合主题名称进行取消订阅
        sendMessage('UnsubscribeTopic', `online`).catch((err) =>
          console.error('取消订阅失败:', err)
        )
      }
    }
  }, [connectionState, deviceId, subscribed, connection, sendMessage])

  // 添加调试信息以追踪props变化
  useEffect(() => {
    setTotalItems(tags.length)
  }, [tags])

  const handleRefresh = useCallback(async () => {
    if (isLoading) return

    setIsLoading(true)
    try {
      onRefresh()
      setError(null)
    } catch (err) {
      console.error('Error refreshing device data:', err)
      setError('获取设备数据失败，请重试')
      toast({
        title: '数据刷新失败',
        description: '无法获取最新设备数据，请检查网络连接',
        variant: 'destructive',
      })
    } finally {
      setIsLoading(false)
    }
  }, [isLoading, onRefresh])

  // Update tagData when tags prop changes
  useEffect(() => {
    // 当父组件的tags更新时，更新本地状态
    setTagData(tags)
    // 重置为第一页
    setCurrentPage(1)
  }, [tags])

  // 手动重新订阅
  const handleResubscribe = useCallback(async () => {
    try {
      // 如果已订阅，先取消订阅
      if (subscribed && connection && connectionState === 'connected') {
        // 使用组合的主题名称取消订阅
        await sendMessage('UnsubscribeTopic', `online`)
        setSubscribed(false)
      }

      // 重新订阅
      if (connection && connectionState === 'connected') {
        // 使用组合的主题名称订阅
        await sendMessage('SubscribeTopic', `online`)
        setSubscribed(true)

        toast({
          title: '已重新订阅',
          description: '实时数据订阅已刷新',
          variant: 'success',
        })
      } else {
        toast({
          title: '无法订阅',
          description: '实时数据服务未连接，请稍后重试',
          variant: 'destructive',
        })
      }
    } catch (error) {
      console.error('重新订阅失败:', error)
      toast({
        title: '订阅失败',
        description: '无法订阅实时数据更新',
        variant: 'destructive',
      })
    }
  }, [connection, connectionState, deviceId, sendMessage, subscribed])

  // Get data status badge
  const getStatusBadge = (status?: string) => {
    switch (status) {
      case 'Good':
        return <Badge variant="success">正常</Badge>
      case 'Warning':
        return <Badge variant="warning">警告</Badge>
      case 'Bad':
        return <Badge variant="destructive">异常</Badge>
      default:
        return null
    }
  }

  // 渲染分页控件
  const renderPagination = () => {
    if (totalPages <= 1) return null

    return (
      <div className="flex items-center justify-between mt-6 p-4 bg-gray-50 dark:bg-gray-800 rounded-lg border border-gray-200 dark:border-gray-700">
        <div className="text-sm text-muted-foreground font-medium">
          显示{' '}
          <span className="font-bold text-gray-900 dark:text-gray-100">
            {tagData.length}
          </span>{' '}
          个点位中的第{' '}
          <span className="font-bold text-blue-600">
            {(currentPage - 1) * itemsPerPage + 1}
          </span>{' '}
          -{' '}
          <span className="font-bold text-blue-600">
            {Math.min(currentPage * itemsPerPage, tagData.length)}
          </span>{' '}
          个
        </div>
        <div className="flex items-center space-x-2">
          <Button
            variant="outline"
            size="sm"
            onClick={() => handlePageChange(currentPage - 1)}
            disabled={currentPage === 1}
            className="h-9 w-9 p-0 bg-white hover:bg-gray-50 border-gray-200 shadow-sm">
            <ChevronLeft className="h-4 w-4" />
          </Button>

          <div className="flex items-center space-x-1 bg-white dark:bg-gray-700 p-1 rounded-md border border-gray-200 dark:border-gray-600">
            {/* 渲染页码按钮 - 仅显示附近的几页 */}
            {Array.from({ length: totalPages }, (_, i) => i + 1)
              .filter(
                (page) =>
                  page === 1 ||
                  page === totalPages ||
                  (page >= currentPage - 1 && page <= currentPage + 1)
              )
              .map((page, index, array) => {
                // 添加省略号
                const showEllipsisBefore =
                  index > 0 && array[index - 1] !== page - 1
                const showEllipsisAfter =
                  index < array.length - 1 && array[index + 1] !== page + 1

                return (
                  <div key={page} className="flex items-center">
                    {showEllipsisBefore && (
                      <span className="px-2 text-muted-foreground">...</span>
                    )}

                    <Button
                      variant={currentPage === page ? 'default' : 'ghost'}
                      size="sm"
                      onClick={() => handlePageChange(page)}
                      className={`h-8 w-8 p-0 ${
                        currentPage === page
                          ? 'bg-blue-600 hover:bg-blue-700 text-white shadow-md'
                          : 'hover:bg-gray-100 text-gray-700'
                      }`}>
                      {page}
                    </Button>

                    {showEllipsisAfter && (
                      <span className="px-2 text-muted-foreground">...</span>
                    )}
                  </div>
                )
              })}
          </div>

          <Button
            variant="outline"
            size="sm"
            onClick={() => handlePageChange(currentPage + 1)}
            disabled={currentPage === totalPages}
            className="h-9 w-9 p-0 bg-white hover:bg-gray-50 border-gray-200 shadow-sm">
            <ChevronRight className="h-4 w-4" />
          </Button>
        </div>
      </div>
    )
  }

  return (
    <div className="space-y-6">
      <div className="flex items-center justify-between bg-white dark:bg-gray-800 p-4 rounded-lg shadow-sm border border-gray-200 dark:border-gray-700">
        <div className="flex items-center space-x-4">
          <Button
            variant="outline"
            size="sm"
            onClick={() => {
              handleRefresh()
            }}
            disabled={isLoading}
            className="bg-white hover:bg-gray-50 border-gray-200 shadow-sm">
            <RefreshCw
              className={`mr-2 h-4 w-4 ${isLoading ? 'animate-spin' : ''}`}
            />
            刷新
          </Button>
          <div className="flex items-center space-x-3">
            <span className="text-sm font-medium text-muted-foreground">
              状态:
            </span>
            {connectionState === 'connected' ? (
              <Badge
                variant="outline"
                className="bg-green-50 text-green-700 border-green-200 shadow-sm">
                <div className="w-2 h-2 bg-green-500 rounded-full mr-1 animate-pulse"></div>
                实时
              </Badge>
            ) : connectionState === 'connecting' ||
              connectionState === 'reconnecting' ? (
              <Badge
                variant="outline"
                className="bg-amber-50 text-amber-700 border-amber-200 shadow-sm">
                <div className="w-2 h-2 bg-amber-500 rounded-full mr-1"></div>
                连接中
              </Badge>
            ) : (
              <Badge
                variant="outline"
                className="bg-gray-100 text-gray-700 border-gray-200 shadow-sm">
                <div className="w-2 h-2 bg-gray-500 rounded-full mr-1"></div>
                离线
              </Badge>
            )}
            <SignalRStatus size="sm" className="ml-2" />
            {connectionState === 'connected' && (
              <Button
                variant="ghost"
                size="sm"
                className="h-7 px-3 text-xs bg-blue-50 hover:bg-blue-100 text-blue-700 border border-blue-200"
                onClick={handleResubscribe}>
                重新订阅
              </Button>
            )}
          </div>
        </div>
        <div className="flex items-center space-x-4">
          <div className="text-sm text-muted-foreground font-medium bg-gray-50 dark:bg-gray-800 px-3 py-1 rounded-md">
            共{' '}
            <span className="font-bold text-gray-900 dark:text-gray-100">
              {totalItems}
            </span>{' '}
            个点位
          </div>
          <div className="flex items-center space-x-1 bg-gray-100 dark:bg-gray-700 p-1 rounded-lg">
            <Button
              variant={layout === 'grid' ? 'default' : 'ghost'}
              size="sm"
              className="h-8 px-3"
              onClick={() => setLayout('grid')}>
              <LayoutGrid className="h-4 w-4 mr-1" />
              网格
            </Button>
            <Button
              variant={layout === 'list' ? 'default' : 'ghost'}
              size="sm"
              className="h-8 px-3"
              onClick={() => setLayout('list')}>
              <LayoutList className="h-4 w-4 mr-1" />
              列表
            </Button>
          </div>
        </div>
      </div>

      <Tabs defaultValue="realtime" className="space-y-4">
        <TabsList className="bg-gray-100 dark:bg-gray-800 p-1 rounded-lg">
          <TabsTrigger
            value="realtime"
            className="data-[state=active]:bg-white data-[state=active]:shadow-sm font-medium">
            📊 数据
          </TabsTrigger>
          <TabsTrigger
            value="trend"
            className="data-[state=active]:bg-white data-[state=active]:shadow-sm font-medium">
            📈 趋势图
          </TabsTrigger>
        </TabsList>
        <TabsContent value="realtime">
          {layout === 'grid' ? (
            // 网格布局
            <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-4">
              {currentPageData.map((tag) => {
                // 确定点位状态
                const isDisabled = tag.enabled === false
                const hasNoData = tag.value === null || tag.value === undefined
                const hasNoTimestamp = !tag.timestamp && !tag.time
                const noDataReceived = hasNoData && hasNoTimestamp

                return (
                  <Card
                    key={tag.id}
                    data-tag-id={tag.id}
                    className={`transition-all duration-300 hover:shadow-md border-0 shadow-sm hover:shadow-lg ${
                      recentlyUpdatedTags.has(tag.id)
                        ? 'update-glow border-l-4 border-l-blue-500 bg-blue-50/30 dark:bg-blue-950/20'
                        : 'hover:border-l-4 hover:border-l-gray-300'
                    } ${
                      isDisabled
                        ? 'opacity-70 bg-gray-50 dark:bg-gray-900 border-dashed'
                        : 'bg-white dark:bg-gray-800'
                    } ${
                      recentlyUpdatedTags.has(tag.id) ? 'update-indicator' : ''
                    }`}>
                    <CardHeader className="pb-3">
                      <CardTitle className="text-sm font-medium flex justify-between items-start">
                        <div className="flex items-center flex-wrap gap-2">
                          <span className="font-semibold text-gray-900 dark:text-gray-100">
                            {tag.name}
                          </span>
                          <Badge
                            variant="outline"
                            className="text-xs bg-gray-50 text-gray-600 border-gray-200 dark:bg-gray-800 dark:text-gray-400">
                            {getDataTypeDisplayName(tag)}
                          </Badge>
                          {isDisabled && (
                            <Badge
                              variant="outline"
                              className="bg-gray-100 text-gray-500 border-gray-300">
                              禁用
                            </Badge>
                          )}
                          {noDataReceived && !isDisabled && (
                            <Badge
                              variant="outline"
                              className="bg-amber-50 text-amber-700 border-amber-200">
                              无数据
                            </Badge>
                          )}
                        </div>
                        <div className="flex items-center gap-2">
                          {recentlyUpdatedTags.has(tag.id) && (
                            <Zap className="h-3 w-3 text-blue-500 animate-pulse" />
                          )}
                          {getStatusBadge(tag.status)}
                        </div>
                      </CardTitle>
                      <p className="text-xs text-muted-foreground leading-relaxed">
                        {tag.identifier}
                      </p>
                    </CardHeader>
                    <CardContent className="pt-0">
                      <div className="tag-value mb-3">
                        <ValueDisplay
                          value={tag.value}
                          className={`text-lg font-bold ${
                            recentlyUpdatedTags.has(tag.id)
                              ? 'text-blue-600 dark:text-blue-400'
                              : 'text-gray-900 dark:text-gray-100'
                          }`}
                          animated={recentlyUpdatedTags.has(tag.id)}
                          noDataReceived={noDataReceived}
                          disabled={isDisabled}
                          custom={tag.custom}
                        />
                        {tag.unit && !isDisabled && !noDataReceived && (
                          <span className="ml-2 text-sm text-muted-foreground font-medium">
                            {tag.unit}
                          </span>
                        )}
                      </div>
                      <div className="text-xs text-muted-foreground flex justify-between items-center">
                        <span className="inline-flex items-center gap-1 tag-time">
                          {isDisabled
                            ? '已禁用'
                            : noDataReceived
                            ? '等待数据'
                            : formatTime(tag.timestamp, tag.time)}
                        </span>
                        {tag.errMsg && (
                          <span className="text-red-500 text-xs bg-red-50 px-2 py-1 rounded">
                            {tag.errMsg}
                          </span>
                        )}
                      </div>
                    </CardContent>
                  </Card>
                )
              })}
            </div>
          ) : (
            // 列表布局 - 新的表格式设计
            <div className="border rounded-lg overflow-hidden shadow-sm bg-white dark:bg-gray-800">
              {/* 表头 */}
              <div className="grid grid-cols-12 gap-1 bg-gradient-to-r from-gray-50 to-gray-100 dark:from-gray-800 dark:to-gray-700 p-4 text-sm font-semibold border-b text-gray-700 dark:text-gray-300">
                <div className="col-span-3">点位名称</div>
                <div className="col-span-2">识别名</div>
                <div className="col-span-3">当前值</div>
                <div className="col-span-1 text-center">数据类型</div>
                <div className="col-span-1 text-center">状态</div>
                <div className="col-span-2 text-center">更新时间</div>
              </div>

              {/* 表格内容 */}
              <div className="divide-y divide-gray-100 dark:divide-gray-700">
                {currentPageData.map((tag) => {
                  // 确定点位状态
                  const isDisabled = tag.enabled === false
                  const hasNoData =
                    tag.value === null || tag.value === undefined
                  const hasNoTimestamp = !tag.timestamp && !tag.time
                  const noDataReceived = hasNoData && hasNoTimestamp

                  return (
                    <div
                      key={tag.id}
                      data-tag-id={tag.id}
                      className={`grid grid-cols-12 gap-1 p-3 items-center text-sm hover:bg-gray-50 dark:hover:bg-gray-900/50 transition-all duration-200 ${
                        recentlyUpdatedTags.has(tag.id)
                          ? 'bg-blue-50/50 dark:bg-blue-950/20 border-l-4 border-l-blue-500 slide-in-left'
                          : 'border-l-4 border-l-transparent hover:border-l-gray-300'
                      } ${
                        isDisabled
                          ? 'opacity-70 bg-gray-50/50 dark:bg-gray-900/50'
                          : ''
                      }`}>
                      {/* 点位名称和描述 */}
                      <div className="col-span-3">
                        <div className="flex items-center gap-2">
                          <span className="font-medium text-gray-900 dark:text-gray-100">
                            {tag.name}
                          </span>
                          {recentlyUpdatedTags.has(tag.id) && (
                            <Zap className="h-3 w-3 text-blue-500 animate-pulse" />
                          )}
                        </div>
                        <div className="text-xs text-muted-foreground truncate max-w-[90%] leading-relaxed">
                          {tag.description || '-'}
                        </div>
                      </div>

                      {/* 识别名 */}
                      <div className="col-span-2 text-muted-foreground truncate font-mono text-xs bg-gray-50 dark:bg-gray-800 px-2 py-1 rounded">
                        {tag.identifier}
                      </div>

                      {/* 当前值 */}
                      <div className="col-span-3">
                        <div
                          className={`flex items-center ${
                            recentlyUpdatedTags.has(tag.id)
                              ? 'text-blue-600 dark:text-blue-400 font-semibold'
                              : 'text-gray-900 dark:text-gray-100'
                          }`}>
                          <ValueDisplay
                            value={tag.value}
                            className="max-w-[200px] truncate"
                            animated={recentlyUpdatedTags.has(tag.id)}
                            noDataReceived={noDataReceived}
                            disabled={isDisabled}
                            custom={tag.custom}
                          />
                          {tag.unit && !isDisabled && !noDataReceived && (
                            <span className="ml-2 text-xs text-muted-foreground font-medium">
                              {tag.unit}
                            </span>
                          )}
                        </div>
                        {tag.errMsg && (
                          <div className="text-xs text-red-500 bg-red-50 px-2 py-1 rounded mt-1 truncate">
                            {tag.errMsg}
                          </div>
                        )}
                      </div>

                      {/* 数据类型 */}
                      <div className="col-span-1 text-center">
                        <Badge
                          variant="outline"
                          className="font-normal bg-gray-50 text-gray-600 border-gray-200 dark:bg-gray-800 dark:text-gray-400 text-xs">
                          {getDataTypeDisplayName(tag)}
                        </Badge>
                      </div>

                      {/* 状态 */}
                      <div className="col-span-1 text-center">
                        {isDisabled ? (
                          <Badge
                            variant="outline"
                            className="font-normal bg-gray-100 text-gray-500 border-gray-300">
                            禁用
                          </Badge>
                        ) : noDataReceived ? (
                          <Badge
                            variant="outline"
                            className="font-normal bg-amber-50 text-amber-700 border-amber-200">
                            无数据
                          </Badge>
                        ) : (
                          getStatusBadge(tag.status) || (
                            <Badge
                              variant="outline"
                              className="font-normal bg-gray-50 text-gray-600">
                              未知
                            </Badge>
                          )
                        )}
                      </div>

                      {/* 更新时间 */}
                      <div className="col-span-2 text-center text-xs text-muted-foreground">
                        <div
                          className={`px-2 py-1 rounded ${
                            recentlyUpdatedTags.has(tag.id)
                              ? 'bg-blue-100 text-blue-700 font-medium'
                              : ''
                          }`}>
                          {isDisabled
                            ? '已禁用'
                            : noDataReceived
                            ? '等待数据'
                            : formatTime(tag.timestamp, tag.time)}
                        </div>
                      </div>
                    </div>
                  )
                })}
              </div>

              {/* 空数据提示 */}
              {currentPageData.length === 0 && (
                <div className="p-12 text-center text-muted-foreground">
                  <div className="text-4xl mb-4">📊</div>
                  <div className="text-lg font-medium mb-2">
                    没有找到点位数据
                  </div>
                  <div className="text-sm">请检查设备连接或刷新数据</div>
                </div>
              )}
            </div>
          )}

          {/* 渲染分页控件 */}
          {renderPagination()}
        </TabsContent>
        <TabsContent value="trend">
          <TrendChart
            deviceId={deviceId}
            tags={tagData}
            onRealTimeUpdate={handleTrendUpdateHandlerRegistration}
          />
        </TabsContent>
      </Tabs>
    </div>
  )
}
