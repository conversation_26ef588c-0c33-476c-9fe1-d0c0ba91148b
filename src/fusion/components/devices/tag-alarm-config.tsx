import { Checkbox } from '@/components/ui/checkbox'
import { useState, useEffect } from 'react'
import {
  Dialog,
  DialogContent,
  DialogDescription,
  DialogFooter,
  DialogHeader,
  DialogTitle,
} from '@/components/ui/dialog'
import { Button } from '@/components/ui/button'
import { Input } from '@/components/ui/input'
import { Label } from '@/components/ui/label'
import { Switch } from '@/components/ui/switch'
import {
  Select,
  SelectContent,
  SelectItem,
  SelectTrigger,
  SelectValue,
} from '@/components/ui/select'
import { Tabs, TabsContent, TabsList, TabsTrigger } from '@/components/ui/tabs'
import { Card, CardContent, CardHeader, CardTitle } from '@/components/ui/card'
import { Badge } from '@/components/ui/badge'
import {
  Loader2,
  Plus,
  Trash2,
  Edit,
  Bell,
  BellOff,
  AlertTriangle,
  AlertCircle,
  Info,
} from 'lucide-react'
import { toast } from '@/components/ui/use-toast'
import type { DeviceTag } from '@/lib/api/tag-api'

interface TagAlarm {
  id: string
  tagId: string
  name: string
  description?: string
  enabled: boolean
  severity: 'info' | 'warning' | 'error' | 'critical'
  condition:
    | 'gt'
    | 'lt'
    | 'eq'
    | 'neq'
    | 'gte'
    | 'lte'
    | 'between'
    | 'change'
    | 'stale'
  threshold?: number
  highThreshold?: number
  lowThreshold?: number
  deadband?: number
  delay?: number
  notificationChannels: string[]
}

interface TagAlarmConfigProps {
  open: boolean
  onOpenChange: (open: boolean) => void
  deviceId: string
  tags: DeviceTag[]
  onAlarmsUpdated?: () => void
}

export function TagAlarmConfig({
  open,
  onOpenChange,
  deviceId,
  tags,
  onAlarmsUpdated,
}: TagAlarmConfigProps) {
  const [alarms, setAlarms] = useState<TagAlarm[]>([])
  const [activeTab, setActiveTab] = useState('alarms')
  const [selectedAlarm, setSelectedAlarm] = useState<TagAlarm | null>(null)
  const [isLoading, setIsLoading] = useState(false)
  const [isEditing, setIsEditing] = useState(false)

  // 新建/编辑报警表单
  const [alarmName, setAlarmName] = useState('')
  const [alarmDescription, setAlarmDescription] = useState('')
  const [alarmEnabled, setAlarmEnabled] = useState(true)
  const [selectedTagId, setSelectedTagId] = useState('')
  const [alarmSeverity, setAlarmSeverity] = useState<
    'info' | 'warning' | 'error' | 'critical'
  >('warning')
  const [alarmCondition, setAlarmCondition] = useState<
    'gt' | 'lt' | 'eq' | 'neq' | 'gte' | 'lte' | 'between' | 'change' | 'stale'
  >('gt')
  const [alarmThreshold, setAlarmThreshold] = useState('')
  const [alarmHighThreshold, setAlarmHighThreshold] = useState('')
  const [alarmLowThreshold, setAlarmLowThreshold] = useState('')
  const [alarmDeadband, setAlarmDeadband] = useState('')
  const [alarmDelay, setAlarmDelay] = useState('')
  const [alarmNotificationChannels, setAlarmNotificationChannels] = useState<
    string[]
  >(['email'])

  // 加载报警配置
  const loadAlarms = async () => {
    setIsLoading(true)
    try {
      // 在实际应用中使用API调用
      // 模拟API调用
      await new Promise((resolve) => setTimeout(resolve, 800))

      // 模拟报警数据
      const mockAlarms: TagAlarm[] = [
        {
          id: 'alarm1',
          tagId: tags.find((t) => t.name.includes('temperature'))?.id || '',
          name: '温度过高报警',
          description: '当温度超过设定阈值时触发报警',
          enabled: true,
          severity: 'error',
          condition: 'gt',
          threshold: 80,
          deadband: 2,
          delay: 10,
          notificationChannels: ['email', 'sms'],
        },
        {
          id: 'alarm2',
          tagId: tags.find((t) => t.name.includes('pressure'))?.id || '',
          name: '压力异常报警',
          description: '当压力超出正常范围时触发报警',
          enabled: true,
          severity: 'critical',
          condition: 'between',
          highThreshold: 9.5,
          lowThreshold: 0.5,
          deadband: 0.1,
          delay: 5,
          notificationChannels: ['email', 'webhook'],
        },
        {
          id: 'alarm3',
          tagId:
            tags.find(
              (t) => t.dataType === 'Boolean' && t.name.includes('status')
            )?.id || '',
          name: '状态变化报警',
          description: '当设备状态发生变化时触发报警',
          enabled: false,
          severity: 'info',
          condition: 'change',
          delay: 0,
          notificationChannels: ['email'],
        },
      ]

      setAlarms(mockAlarms)
    } catch (error) {
      console.error('加载报警配置失败:', error)
      toast({
        title: '加载失败',
        description: '无法加载点位报警配置',
        variant: 'destructive',
      })
    } finally {
      setIsLoading(false)
    }
  }

  // 初始加载
  useEffect(() => {
    if (open) {
      loadAlarms()
      resetForm()
    }
  }, [open, tags])

  // 重置表单
  const resetForm = () => {
    setAlarmName('')
    setAlarmDescription('')
    setAlarmEnabled(true)
    setSelectedTagId('')
    setAlarmSeverity('warning')
    setAlarmCondition('gt')
    setAlarmThreshold('')
    setAlarmHighThreshold('')
    setAlarmLowThreshold('')
    setAlarmDeadband('')
    setAlarmDelay('')
    setAlarmNotificationChannels(['email'])
    setIsEditing(false)
    setSelectedAlarm(null)
  }

  // 处理编辑报警
  const handleEditAlarm = (alarm: TagAlarm) => {
    setSelectedAlarm(alarm)
    setAlarmName(alarm.name)
    setAlarmDescription(alarm.description || '')
    setAlarmEnabled(alarm.enabled)
    setSelectedTagId(alarm.tagId)
    setAlarmSeverity(alarm.severity)
    setAlarmCondition(alarm.condition)
    setAlarmThreshold(alarm.threshold?.toString() || '')
    setAlarmHighThreshold(alarm.highThreshold?.toString() || '')
    setAlarmLowThreshold(alarm.lowThreshold?.toString() || '')
    setAlarmDeadband(alarm.deadband?.toString() || '')
    setAlarmDelay(alarm.delay?.toString() || '')
    setAlarmNotificationChannels(alarm.notificationChannels)
    setIsEditing(true)
    setActiveTab('edit')
  }

  // 处理删除报警
  const handleDeleteAlarm = async (alarmId: string) => {
    if (!confirm('确定要删除此报警配置吗？')) return

    setIsLoading(true)
    try {
      // 在实际应用中使用API调用
      // 模拟API调用
      await new Promise((resolve) => setTimeout(resolve, 800))

      setAlarms(alarms.filter((a) => a.id !== alarmId))

      toast({
        title: '删除成功',
        description: '已成功删除报警配置',
      })

      if (onAlarmsUpdated) {
        onAlarmsUpdated()
      }
    } catch (error) {
      console.error('删除报警配置失败:', error)
      toast({
        title: '删除失败',
        description: '无法删除报警配置',
        variant: 'destructive',
      })
    } finally {
      setIsLoading(false)
    }
  }

  // 处理启用/禁用报警
  const handleToggleAlarm = async (alarm: TagAlarm) => {
    setIsLoading(true)
    try {
      // 在实际应用中使用API调用
      // 模拟API调用
      await new Promise((resolve) => setTimeout(resolve, 500))

      const updatedAlarms = alarms.map((a) =>
        a.id === alarm.id ? { ...a, enabled: !a.enabled } : a
      )
      setAlarms(updatedAlarms)

      toast({
        title: alarm.enabled ? '已禁用报警' : '已启用报警',
        description: `报警 "${alarm.name}" 已${
          alarm.enabled ? '禁用' : '启用'
        }`,
      })

      if (onAlarmsUpdated) {
        onAlarmsUpdated()
      }
    } catch (error) {
      console.error('切换报警状态失败:', error)
      toast({
        title: '操作失败',
        description: '无法切换报警状态',
        variant: 'destructive',
      })
    } finally {
      setIsLoading(false)
    }
  }

  // 处理保存报警
  const handleSaveAlarm = async () => {
    if (!alarmName.trim()) {
      toast({
        title: '验证失败',
        description: '报警名称不能为空',
        variant: 'destructive',
      })
      return
    }

    if (!selectedTagId) {
      toast({
        title: '验证失败',
        description: '请选择一个点位',
        variant: 'destructive',
      })
      return
    }

    // 验证阈值
    if (alarmCondition !== 'change' && alarmCondition !== 'stale') {
      if (alarmCondition === 'between') {
        if (!alarmHighThreshold || !alarmLowThreshold) {
          toast({
            title: '验证失败',
            description: '请输入上限和下限阈值',
            variant: 'destructive',
          })
          return
        }
      } else {
        if (!alarmThreshold) {
          toast({
            title: '验证失败',
            description: '请输入阈值',
            variant: 'destructive',
          })
          return
        }
      }
    }

    setIsLoading(true)
    try {
      // 在实际应用中使用API调用
      // 模拟API调用
      await new Promise((resolve) => setTimeout(resolve, 800))

      const newAlarm: TagAlarm = {
        id:
          isEditing && selectedAlarm ? selectedAlarm.id : `alarm${Date.now()}`,
        tagId: selectedTagId,
        name: alarmName,
        description: alarmDescription,
        enabled: alarmEnabled,
        severity: alarmSeverity,
        condition: alarmCondition,
        threshold: alarmThreshold
          ? Number.parseFloat(alarmThreshold)
          : undefined,
        highThreshold: alarmHighThreshold
          ? Number.parseFloat(alarmHighThreshold)
          : undefined,
        lowThreshold: alarmLowThreshold
          ? Number.parseFloat(alarmLowThreshold)
          : undefined,
        deadband: alarmDeadband ? Number.parseFloat(alarmDeadband) : undefined,
        delay: alarmDelay ? Number.parseInt(alarmDelay) : undefined,
        notificationChannels: alarmNotificationChannels,
      }

      if (isEditing && selectedAlarm) {
        // 更新报警
        const updatedAlarms = alarms.map((a) =>
          a.id === selectedAlarm.id ? newAlarm : a
        )
        setAlarms(updatedAlarms)

        toast({
          title: '更新成功',
          description: '已成功更新报警配置',
        })
      } else {
        // 创建新报警
        setAlarms([...alarms, newAlarm])

        toast({
          title: '创建成功',
          description: '已成功创建报警配置',
        })
      }

      resetForm()
      setActiveTab('alarms')

      if (onAlarmsUpdated) {
        onAlarmsUpdated()
      }
    } catch (error) {
      console.error('保存报警配置失败:', error)
      toast({
        title: '保存失败',
        description: '无法保存报警配置',
        variant: 'destructive',
      })
    } finally {
      setIsLoading(false)
    }
  }

  // 获取报警对应的点位
  const getAlarmTag = (alarm: TagAlarm) => {
    return tags.find((tag) => tag.id === alarm.tagId)
  }

  // 获取报警严重性图标
  const getSeverityIcon = (severity: string) => {
    switch (severity) {
      case 'info':
        return <Info className="h-4 w-4 text-blue-500" />
      case 'warning':
        return <AlertTriangle className="h-4 w-4 text-amber-500" />
      case 'error':
        return <AlertCircle className="h-4 w-4 text-red-500" />
      case 'critical':
        return <AlertCircle className="h-4 w-4 text-purple-500" />
      default:
        return <Info className="h-4 w-4" />
    }
  }

  // 获取报警条件文本
  const getConditionText = (alarm: TagAlarm, tag?: DeviceTag) => {
    const tagUnit = tag?.unit || ''

    switch (alarm.condition) {
      case 'gt':
        return `> ${alarm.threshold}${tagUnit}`
      case 'lt':
        return `< ${alarm.threshold}${tagUnit}`
      case 'eq':
        return `= ${alarm.threshold}${tagUnit}`
      case 'neq':
        return `≠ ${alarm.threshold}${tagUnit}`
      case 'gte':
        return `≥ ${alarm.threshold}${tagUnit}`
      case 'lte':
        return `≤ ${alarm.threshold}${tagUnit}`
      case 'between':
        return `${alarm.lowThreshold}${tagUnit} ~ ${alarm.highThreshold}${tagUnit}`
      case 'change':
        return '值变化时'
      case 'stale':
        return '数据过期时'
      default:
        return ''
    }
  }

  // 渲染条件输入控件
  const renderConditionInputs = () => {
    const selectedTag = tags.find((tag) => tag.id === selectedTagId)
    const tagUnit = selectedTag?.unit || ''

    if (alarmCondition === 'change' || alarmCondition === 'stale') {
      return null
    }

    if (alarmCondition === 'between') {
      return (
        <div className="grid grid-cols-2 gap-4">
          <div className="space-y-2">
            <Label htmlFor="lowThreshold" className="required">
              下限阈值{tagUnit && ` (${tagUnit})`}
            </Label>
            <Input
              id="lowThreshold"
              type="number"
              value={alarmLowThreshold}
              onChange={(e) => setAlarmLowThreshold(e.target.value)}
              placeholder="输入下限阈值"
              min={selectedTag?.min}
              max={selectedTag?.max}
              step={selectedTag?.dataType === 'Integer' ? 1 : 0.01}
            />
          </div>
          <div className="space-y-2">
            <Label htmlFor="highThreshold" className="required">
              上限阈值{tagUnit && ` (${tagUnit})`}
            </Label>
            <Input
              id="highThreshold"
              type="number"
              value={alarmHighThreshold}
              onChange={(e) => setAlarmHighThreshold(e.target.value)}
              placeholder="输入上限阈值"
              min={selectedTag?.min}
              max={selectedTag?.max}
              step={selectedTag?.dataType === 'Integer' ? 1 : 0.01}
            />
          </div>
        </div>
      )
    }

    return (
      <div className="space-y-2">
        <Label htmlFor="threshold" className="required">
          阈值{tagUnit && ` (${tagUnit})`}
        </Label>
        <Input
          id="threshold"
          type="number"
          value={alarmThreshold}
          onChange={(e) => setAlarmThreshold(e.target.value)}
          placeholder="输入阈值"
          min={selectedTag?.min}
          max={selectedTag?.max}
          step={selectedTag?.dataType === 'Integer' ? 1 : 0.01}
        />
      </div>
    )
  }

  return (
    <Dialog open={open} onOpenChange={onOpenChange}>
      <DialogContent className="max-w-3xl max-h-[90vh] overflow-hidden flex flex-col">
        <DialogHeader>
          <DialogTitle>点位报警配置</DialogTitle>
          <DialogDescription>
            创建和管理点位报警规则，设置触发条件和通知方式
          </DialogDescription>
        </DialogHeader>

        <Tabs
          value={activeTab}
          onValueChange={setActiveTab}
          className="flex-1 overflow-hidden flex flex-col">
          <TabsList className="grid grid-cols-2">
            <TabsTrigger value="alarms">报警列表</TabsTrigger>
            <TabsTrigger value="edit">
              {isEditing ? '编辑报警' : '新建报警'}
            </TabsTrigger>
          </TabsList>

          <div className="flex-1 overflow-auto p-1">
            <TabsContent value="alarms" className="mt-0 h-full">
              <div className="space-y-4 p-2">
                <div className="flex justify-between items-center">
                  <h3 className="text-lg font-medium">设备报警</h3>
                  <Button
                    onClick={() => {
                      resetForm()
                      setActiveTab('edit')
                    }}>
                    <Plus className="mr-2 h-4 w-4" />
                    新建报警
                  </Button>
                </div>

                {isLoading ? (
                  <div className="flex justify-center items-center py-12">
                    <Loader2 className="h-8 w-8 animate-spin text-gray-400" />
                  </div>
                ) : alarms.length === 0 ? (
                  <div className="flex flex-col items-center justify-center py-12 text-gray-500 border rounded-lg">
                    <Bell className="h-12 w-12 mb-4 text-gray-300" />
                    <p>暂无报警配置</p>
                    <p className="text-sm mt-2">
                      点击"新建报警"创建第一个报警规则
                    </p>
                  </div>
                ) : (
                  <div className="grid gap-4">
                    {alarms.map((alarm) => {
                      const tag = getAlarmTag(alarm)

                      return (
                        <Card key={alarm.id}>
                          <CardHeader className="pb-2">
                            <div className="flex items-start justify-between">
                              <div className="flex items-center gap-2">
                                {getSeverityIcon(alarm.severity)}
                                <CardTitle className="text-base">
                                  {alarm.name}
                                </CardTitle>
                                <Badge
                                  variant={
                                    alarm.enabled ? 'default' : 'secondary'
                                  }>
                                  {alarm.enabled ? '已启用' : '已禁用'}
                                </Badge>
                              </div>
                              <div className="flex items-center gap-1">
                                <Button
                                  variant="ghost"
                                  size="icon"
                                  onClick={() => handleToggleAlarm(alarm)}
                                  disabled={isLoading}>
                                  {alarm.enabled ? (
                                    <BellOff className="h-4 w-4 text-gray-500" />
                                  ) : (
                                    <Bell className="h-4 w-4 text-gray-500" />
                                  )}
                                </Button>
                                <Button
                                  variant="ghost"
                                  size="icon"
                                  onClick={() => handleEditAlarm(alarm)}>
                                  <Edit className="h-4 w-4" />
                                </Button>
                                <Button
                                  variant="ghost"
                                  size="icon"
                                  onClick={() => handleDeleteAlarm(alarm.id)}>
                                  <Trash2 className="h-4 w-4 text-red-500" />
                                </Button>
                              </div>
                            </div>
                            {alarm.description && (
                              <p className="text-sm text-gray-500">
                                {alarm.description}
                              </p>
                            )}
                          </CardHeader>
                          <CardContent>
                            <div className="grid grid-cols-2 gap-4">
                              <div>
                                <p className="text-sm text-gray-500">点位</p>
                                <p className="font-medium">
                                  {tag?.name || '未知点位'}
                                </p>
                              </div>
                              <div>
                                <p className="text-sm text-gray-500">条件</p>
                                <p className="font-medium">
                                  {getConditionText(alarm, tag)}
                                </p>
                              </div>
                              {alarm.deadband !== undefined && (
                                <div>
                                  <p className="text-sm text-gray-500">死区</p>
                                  <p className="font-medium">
                                    {alarm.deadband}
                                    {tag?.unit || ''}
                                  </p>
                                </div>
                              )}
                              {alarm.delay !== undefined && (
                                <div>
                                  <p className="text-sm text-gray-500">延迟</p>
                                  <p className="font-medium">
                                    {alarm.delay} 秒
                                  </p>
                                </div>
                              )}
                            </div>
                            <div className="mt-2">
                              <p className="text-sm text-gray-500">通知方式</p>
                              <div className="flex flex-wrap gap-2 mt-1">
                                {alarm.notificationChannels.map((channel) => (
                                  <Badge key={channel} variant="outline">
                                    {channel === 'email'
                                      ? '邮件'
                                      : channel === 'sms'
                                      ? '短信'
                                      : channel === 'webhook'
                                      ? 'Webhook'
                                      : channel}
                                  </Badge>
                                ))}
                              </div>
                            </div>
                          </CardContent>
                        </Card>
                      )
                    })}
                  </div>
                )}
              </div>
            </TabsContent>

            <TabsContent value="edit" className="mt-0 h-full">
              <div className="space-y-4 p-2">
                <div className="space-y-4">
                  <div className="space-y-2">
                    <Label htmlFor="alarmName" className="required">
                      报警名称
                    </Label>
                    <Input
                      id="alarmName"
                      value={alarmName}
                      onChange={(e) => setAlarmName(e.target.value)}
                      placeholder="输入报警名称"
                    />
                  </div>

                  <div className="space-y-2">
                    <Label htmlFor="alarmDescription">报警描述</Label>
                    <Input
                      id="alarmDescription"
                      value={alarmDescription}
                      onChange={(e) => setAlarmDescription(e.target.value)}
                      placeholder="输入报警描述（可选）"
                    />
                  </div>

                  <div className="space-y-2">
                    <div className="flex items-center justify-between">
                      <Label htmlFor="alarmEnabled">启用报警</Label>
                      <Switch
                        id="alarmEnabled"
                        checked={alarmEnabled}
                        onCheckedChange={setAlarmEnabled}
                      />
                    </div>
                  </div>

                  <div className="space-y-2">
                    <Label htmlFor="selectedTagId" className="required">
                      选择点位
                    </Label>
                    <Select
                      value={selectedTagId}
                      onValueChange={setSelectedTagId}>
                      <SelectTrigger id="selectedTagId">
                        <SelectValue placeholder="选择点位" />
                      </SelectTrigger>
                      <SelectContent>
                        {tags.map((tag) => (
                          <SelectItem key={tag.id} value={tag.id}>
                            <div className="flex items-center gap-2">
                              <span>{tag.name}</span>
                              <Badge variant="outline" className="text-xs">
                                {tag.dataType}
                              </Badge>
                            </div>
                          </SelectItem>
                        ))}
                      </SelectContent>
                    </Select>
                  </div>

                  <div className="space-y-2">
                    <Label htmlFor="alarmSeverity" className="required">
                      报警级别
                    </Label>
                    <Select
                      value={alarmSeverity}
                      onValueChange={(
                        value: 'info' | 'warning' | 'error' | 'critical'
                      ) => setAlarmSeverity(value)}>
                      <SelectTrigger id="alarmSeverity">
                        <SelectValue placeholder="选择报警级别" />
                      </SelectTrigger>
                      <SelectContent>
                        <SelectItem value="info">
                          <div className="flex items-center gap-2">
                            <Info className="h-4 w-4 text-blue-500" />
                            <span>信息</span>
                          </div>
                        </SelectItem>
                        <SelectItem value="warning">
                          <div className="flex items-center gap-2">
                            <AlertTriangle className="h-4 w-4 text-amber-500" />
                            <span>警告</span>
                          </div>
                        </SelectItem>
                        <SelectItem value="error">
                          <div className="flex items-center gap-2">
                            <AlertCircle className="h-4 w-4 text-red-500" />
                            <span>错误</span>
                          </div>
                        </SelectItem>
                        <SelectItem value="critical">
                          <div className="flex items-center gap-2">
                            <AlertCircle className="h-4 w-4 text-purple-500" />
                            <span>严重</span>
                          </div>
                        </SelectItem>
                      </SelectContent>
                    </Select>
                  </div>

                  <div className="space-y-2">
                    <Label htmlFor="alarmCondition" className="required">
                      报警条件
                    </Label>
                    <Select
                      value={alarmCondition}
                      onValueChange={(
                        value:
                          | 'gt'
                          | 'lt'
                          | 'eq'
                          | 'neq'
                          | 'gte'
                          | 'lte'
                          | 'between'
                          | 'change'
                          | 'stale'
                      ) => setAlarmCondition(value)}>
                      <SelectTrigger id="alarmCondition">
                        <SelectValue placeholder="选择报警条件" />
                      </SelectTrigger>
                      <SelectContent>
                        <SelectItem value="gt">大于 (&gt;)</SelectItem>
                        <SelectItem value="lt">小于 (&lt;)</SelectItem>
                        <SelectItem value="eq">等于 (=)</SelectItem>
                        <SelectItem value="neq">不等于 (≠)</SelectItem>
                        <SelectItem value="gte">大于等于 (≥)</SelectItem>
                        <SelectItem value="lte">小于等于 (≤)</SelectItem>
                        <SelectItem value="between">范围内</SelectItem>
                        <SelectItem value="change">值变化</SelectItem>
                        <SelectItem value="stale">数据过期</SelectItem>
                      </SelectContent>
                    </Select>
                  </div>

                  {renderConditionInputs()}

                  <div className="space-y-2">
                    <Label htmlFor="alarmDeadband">死区</Label>
                    <Input
                      id="alarmDeadband"
                      type="number"
                      value={alarmDeadband}
                      onChange={(e) => setAlarmDeadband(e.target.value)}
                      placeholder="输入死区值（可选）"
                      min="0"
                      step="0.01"
                    />
                    <p className="text-xs text-gray-500">
                      死区用于防止报警频繁触发和恢复，当值在阈值附近波动时提供缓冲区
                    </p>
                  </div>

                  <div className="space-y-2">
                    <Label htmlFor="alarmDelay">延迟（秒）</Label>
                    <Input
                      id="alarmDelay"
                      type="number"
                      value={alarmDelay}
                      onChange={(e) => setAlarmDelay(e.target.value)}
                      placeholder="输入延迟秒数（可选）"
                      min="0"
                      step="1"
                    />
                    <p className="text-xs text-gray-500">
                      延迟用于确保条件持续满足一段时间后才触发报警，避免瞬时波动
                    </p>
                  </div>

                  <div className="space-y-2">
                    <Label className="required">通知方式</Label>
                    <div className="space-y-2">
                      <div className="flex items-center space-x-2">
                        <Checkbox
                          id="email"
                          checked={alarmNotificationChannels.includes('email')}
                          onCheckedChange={(checked) => {
                            if (checked) {
                              setAlarmNotificationChannels([
                                ...alarmNotificationChannels,
                                'email',
                              ])
                            } else {
                              setAlarmNotificationChannels(
                                alarmNotificationChannels.filter(
                                  (c) => c !== 'email'
                                )
                              )
                            }
                          }}
                        />
                        <Label htmlFor="email">邮件通知</Label>
                      </div>
                      <div className="flex items-center space-x-2">
                        <Checkbox
                          id="sms"
                          checked={alarmNotificationChannels.includes('sms')}
                          onCheckedChange={(checked) => {
                            if (checked) {
                              setAlarmNotificationChannels([
                                ...alarmNotificationChannels,
                                'sms',
                              ])
                            } else {
                              setAlarmNotificationChannels(
                                alarmNotificationChannels.filter(
                                  (c) => c !== 'sms'
                                )
                              )
                            }
                          }}
                        />
                        <Label htmlFor="sms">短信通知</Label>
                      </div>
                      <div className="flex items-center space-x-2">
                        <Checkbox
                          id="webhook"
                          checked={alarmNotificationChannels.includes(
                            'webhook'
                          )}
                          onCheckedChange={(checked) => {
                            if (checked) {
                              setAlarmNotificationChannels([
                                ...alarmNotificationChannels,
                                'webhook',
                              ])
                            } else {
                              setAlarmNotificationChannels(
                                alarmNotificationChannels.filter(
                                  (c) => c !== 'webhook'
                                )
                              )
                            }
                          }}
                        />
                        <Label htmlFor="webhook">Webhook通知</Label>
                      </div>
                    </div>
                  </div>
                </div>
              </div>
            </TabsContent>
          </div>
        </Tabs>

        <DialogFooter className="mt-4">
          {activeTab === 'edit' ? (
            <>
              <Button
                variant="outline"
                onClick={() => {
                  setActiveTab('alarms')
                  resetForm()
                }}
                disabled={isLoading}>
                取消
              </Button>
              <Button onClick={handleSaveAlarm} disabled={isLoading}>
                {isLoading ? (
                  <>
                    <Loader2 className="mr-2 h-4 w-4 animate-spin" />
                    保存中...
                  </>
                ) : isEditing ? (
                  '更新报警'
                ) : (
                  '创建报警'
                )}
              </Button>
            </>
          ) : (
            <Button variant="outline" onClick={() => onOpenChange(false)}>
              关闭
            </Button>
          )}
        </DialogFooter>
      </DialogContent>
    </Dialog>
  )
}
