import { useState, useEffect } from 'react'
import { But<PERSON> } from '@/components/ui/button'
import { Input } from '@/components/ui/input'
import { Badge } from '@/components/ui/badge'
import {
  Search,
  RefreshCw,
  Filter,
  ArrowUpDown,
  ThermometerIcon,
  GaugeIcon,
  ToggleLeftIcon,
  BarChart3Icon,
  ZapIcon,
  ChevronLeft,
  ChevronRight,
  ChevronsLeft,
  ChevronsRight,
  LayoutGrid,
  LayoutList,
  AlertCircle,
  HistoryIcon,
  TrendingUp,
  PencilIcon,
} from 'lucide-react'
import {
  Select,
  SelectContent,
  SelectItem,
  SelectTrigger,
  SelectValue,
} from '@/components/ui/select'
import {
  DropdownMenu,
  DropdownMenuContent,
  DropdownMenuItem,
  DropdownMenuTrigger,
} from '@/components/ui/dropdown-menu'
import {
  Tooltip,
  TooltipContent,
  TooltipProvider,
  TooltipTrigger,
} from '@/components/ui/tooltip'
import { Card, CardContent } from '@/components/ui/card'
import { TagDetailCard } from './tag-detail-card'
import { Dialog, DialogContent } from '@/components/ui/dialog'
import { TagHistoryViewer } from './tag-history-viewer'
import { TagTrendAnalyzer } from './tag-trend-analyzer'

// 模拟标签数据类型
interface Tag {
  id: string
  name: string
  value: string | number | null | object
  dataType: string
  status: string
  statusDetails?: string
  unit?: string
  lastUpdate?: string
  description?: string
  address?: string
  min?: number
  max?: number
  readOnly?: boolean
  accessRight?: string
}

// 分页响应类型
interface PaginatedResponse<T> {
  items: T[]
  total: number
  page: number
  pageSize: number
  totalPages: number
}

// 模拟获取标签数据的函数
const fetchTags = async (
  deviceId: string,
  page = 1,
  pageSize = 10,
  search = '',
  status = 'all',
  sortField = 'name',
  sortDirection: 'asc' | 'desc' = 'asc'
): Promise<PaginatedResponse<Tag>> => {
  // 模拟API调用延迟
  await new Promise((resolve) => setTimeout(resolve, 800))

  // 模拟数据
  const allTags: Tag[] = [
    {
      id: '1',
      name: '温度',
      value: 25.5,
      dataType: 'Float',
      status: 'Good',
      unit: '°C',
      lastUpdate: '2023-05-15 14:30:22',
      description: '设备温度传感器',
      address: '40001',
      min: 0,
      max: 100,
      readOnly: false,
      accessRight: '读写',
    },
    {
      id: '2',
      name: '压力',
      value: 101.3,
      dataType: 'Float',
      status: 'Good',
      unit: 'kPa',
      lastUpdate: '2023-05-15 14:30:20',
      description: '设备压力传感器',
      address: '40002',
      min: 0,
      max: 200,
      readOnly: false,
      accessRight: '读写',
    },
    {
      id: '3',
      name: '开关状态',
      value: 1,
      dataType: 'Boolean',
      status: 'Good',
      lastUpdate: '2023-05-15 14:30:18',
      description: '设备开关状态',
      address: '10001',
      readOnly: false,
      accessRight: '读写',
    },
    {
      id: '4',
      name: '流量',
      value: 150,
      dataType: 'Integer',
      status: 'Bad',
      statusDetails: '传感器连接断开，请检查设备连接状态',
      unit: 'L/min',
      lastUpdate: '2023-05-15 14:29:55',
      description: '设备流量计',
      address: '40003',
      min: 0,
      max: 500,
      readOnly: true,
      accessRight: '只读',
    },
    {
      id: '5',
      name: '电压',
      value: 220,
      dataType: 'Integer',
      status: 'Good',
      unit: 'V',
      lastUpdate: '2023-05-15 14:30:15',
      description: '设备电压',
      address: '40004',
      min: 0,
      max: 380,
      readOnly: true,
      accessRight: '只读',
    },
    {
      id: '6',
      name: '湿度',
      value: 45.2,
      dataType: 'Float',
      status: 'Good',
      unit: '%',
      lastUpdate: '2023-05-15 14:30:10',
      description: '环境湿度传感器',
      address: '40005',
      min: 0,
      max: 100,
      readOnly: true,
      accessRight: '只读',
    },
    {
      id: '7',
      name: '风速',
      value: 3.5,
      dataType: 'Float',
      status: 'Good',
      unit: 'm/s',
      lastUpdate: '2023-05-15 14:30:05',
      description: '风速传感器',
      address: '40006',
      min: 0,
      max: 30,
      readOnly: true,
      accessRight: '只读',
    },
    {
      id: '8',
      name: '光照强度',
      value: 850,
      dataType: 'Integer',
      status: 'Good',
      unit: 'lux',
      lastUpdate: '2023-05-15 14:30:00',
      description: '光照传感器',
      address: '40007',
      min: 0,
      max: 2000,
      readOnly: true,
      accessRight: '只读',
    },
    {
      id: '9',
      name: 'CO2浓度',
      value: 420,
      dataType: 'Integer',
      status: 'Good',
      unit: 'ppm',
      lastUpdate: '2023-05-15 14:29:55',
      description: 'CO2传感器',
      address: '40008',
      min: 0,
      max: 2000,
      readOnly: true,
      accessRight: '只读',
    },
    {
      id: '10',
      name: '电机状态',
      value: 0,
      dataType: 'Boolean',
      status: 'Good',
      lastUpdate: '2023-05-15 14:29:50',
      description: '电机运行状态',
      address: '10002',
      readOnly: false,
      accessRight: '读写',
    },
    {
      id: '11',
      name: '阀门开度',
      value: 75,
      dataType: 'Integer',
      status: 'Good',
      unit: '%',
      lastUpdate: '2023-05-15 14:29:45',
      description: '阀门开度控制',
      address: '40009',
      min: 0,
      max: 100,
      readOnly: false,
      accessRight: '读写',
    },
    {
      id: '12',
      name: '水位',
      value: 3.2,
      dataType: 'Float',
      status: 'Warning',
      statusDetails: '水位接近警戒线，请注意监控',
      unit: 'm',
      lastUpdate: '2023-05-15 14:29:40',
      description: '水箱水位',
      address: '40010',
      min: 0,
      max: 5,
      readOnly: true,
      accessRight: '只读',
    },
    {
      id: '13',
      name: '电池电量',
      value: 85,
      dataType: 'Integer',
      status: 'Good',
      unit: '%',
      lastUpdate: '2023-05-15 14:29:35',
      description: '备用电池电量',
      address: '40011',
      min: 0,
      max: 100,
      readOnly: true,
      accessRight: '只读',
    },
    {
      id: '14',
      name: '运行时间',
      value: 1250,
      dataType: 'Integer',
      status: 'Good',
      unit: 'h',
      lastUpdate: '2023-05-15 14:29:30',
      description: '设备运行时间',
      address: '40012',
      min: 0,
      max: 10000,
      readOnly: true,
      accessRight: '只读',
    },
    {
      id: '15',
      name: '紧急停止',
      value: 0,
      dataType: 'Boolean',
      status: 'Good',
      lastUpdate: '2023-05-15 14:29:25',
      description: '紧急停止按钮状态',
      address: '10003',
      readOnly: false,
      accessRight: '读写',
    },
    {
      id: '16',
      name: '功率',
      value: 1.5,
      dataType: 'Float',
      status: 'Good',
      unit: 'kW',
      lastUpdate: '2023-05-15 14:29:20',
      description: '设备功率',
      address: '40013',
      min: 0,
      max: 10,
      readOnly: true,
      accessRight: '只读',
    },
    {
      id: '17',
      name: '转速',
      value: 1800,
      dataType: 'Integer',
      status: 'Good',
      unit: 'rpm',
      lastUpdate: '2023-05-15 14:29:15',
      description: '电机转速',
      address: '40014',
      min: 0,
      max: 3000,
      readOnly: false,
      accessRight: '读写',
    },
    {
      id: '18',
      name: '振动',
      value: 0.15,
      dataType: 'Float',
      status: 'Warning',
      statusDetails: '振动值接近警戒阈值，请检查设备',
      unit: 'mm/s',
      lastUpdate: '2023-05-15 14:29:10',
      description: '设备振动',
      address: '40015',
      min: 0,
      max: 1,
      readOnly: true,
      accessRight: '只读',
    },
    {
      id: '19',
      name: '噪音',
      value: 65,
      dataType: 'Integer',
      status: 'Good',
      unit: 'dB',
      lastUpdate: '2023-05-15 14:29:05',
      description: '环境噪音',
      address: '40016',
      min: 0,
      max: 120,
      readOnly: true,
      accessRight: '只读',
    },
    {
      id: '20',
      name: '自动模式',
      value: 1,
      dataType: 'Boolean',
      status: 'Good',
      lastUpdate: '2023-05-15 14:29:00',
      description: '设备运行模式',
      address: '10004',
      readOnly: false,
      accessRight: '读写',
    },
    {
      id: '21',
      name: '设备配置',
      value: {
        mode: 'auto',
        threshold: 75,
        alerts: {
          email: true,
          sms: false,
          push: true,
        },
        schedule: [
          { day: 'Monday', start: '08:00', end: '18:00' },
          { day: 'Tuesday', start: '08:00', end: '18:00' },
          { day: 'Wednesday', start: '08:00', end: '18:00' },
        ],
      },
      dataType: 'JSON',
      status: 'Good',
      lastUpdate: '2023-05-15 14:28:55',
      description: '设备运行配置',
      address: '40017',
      readOnly: false,
      accessRight: '读写',
    },
    {
      id: '22',
      name: '传感器状态',
      value: {
        connected: true,
        firmware: 'v2.1.5',
        battery: 85,
        errors: [],
      },
      dataType: 'JSON',
      status: 'Good',
      lastUpdate: '2023-05-15 14:28:50',
      description: '传感器状态信息',
      address: '40018',
      readOnly: true,
      accessRight: '只读',
    },
    {
      id: '23',
      name: '系统日志',
      value: [
        { time: '14:25:10', level: 'info', message: '系统启动' },
        { time: '14:26:05', level: 'warning', message: '温度升高' },
        { time: '14:28:30', level: 'info', message: '温度恢复正常' },
      ],
      dataType: 'JSON',
      status: 'Good',
      lastUpdate: '2023-05-15 14:28:45',
      description: '系统运行日志',
      address: '40019',
      readOnly: true,
      accessRight: '只读',
    },
    {
      id: '24',
      name: '故障诊断',
      value: {
        status: 'error',
        code: 'E-1045',
        component: 'pump',
        details: '泵压力异常，可能存在堵塞',
        solutions: ['检查进水管道', '清洗过滤器', '检查泵电机'],
      },
      dataType: 'JSON',
      status: 'Bad',
      statusDetails: '泵系统故障，需要维修',
      lastUpdate: '2023-05-15 14:28:40',
      description: '设备故障诊断',
      address: '40020',
      readOnly: true,
      accessRight: '只读',
    },
  ]

  // 应用过滤
  let filteredTags = [...allTags]

  if (search) {
    filteredTags = filteredTags.filter((tag) =>
      tag.name.toLowerCase().includes(search.toLowerCase())
    )
  }

  if (status !== 'all') {
    filteredTags = filteredTags.filter((tag) => tag.status === status)
  }

  // 应用排序
  filteredTags.sort((a, b) => {
    let valueA = a[sortField as keyof Tag]
    let valueB = b[sortField as keyof Tag]

    if (valueA === null) valueA = ''
    if (valueB === null) valueB = ''

    if (typeof valueA === 'string' && typeof valueB === 'string') {
      return sortDirection === 'asc'
        ? valueA.localeCompare(valueB)
        : valueB.localeCompare(valueA)
    }

    return sortDirection === 'asc'
      ? Number(valueA) - Number(valueB)
      : Number(valueB) - Number(valueA)
  })

  // 计算分页
  const total = filteredTags.length
  const totalPages = Math.ceil(total / pageSize)
  const startIndex = (page - 1) * pageSize
  const endIndex = Math.min(startIndex + pageSize, total)
  const paginatedTags = filteredTags.slice(startIndex, endIndex)

  return {
    items: paginatedTags,
    total,
    page,
    pageSize,
    totalPages,
  }
}

interface SimplifiedTagListProps {
  deviceId: string
  onWriteTag?: (tag: Tag) => void
}

export function SimplifiedTagList({
  deviceId,
  onWriteTag,
}: SimplifiedTagListProps) {
  const [tags, setTags] = useState<Tag[]>([])
  const [loading, setLoading] = useState(false)
  const [searchQuery, setSearchQuery] = useState('')
  const [statusFilter, setStatusFilter] = useState<string>('all')
  const [sortField, setSortField] = useState<string>('name')
  const [sortDirection, setSortDirection] = useState<'asc' | 'desc'>('asc')
  const [selectedTag, setSelectedTag] = useState<Tag | null>(null)

  // 分页状态
  const [page, setPage] = useState(1)
  const [pageSize, setPageSize] = useState(10)
  const [totalItems, setTotalItems] = useState(0)
  const [totalPages, setTotalPages] = useState(1)

  // 视图模式：表格或卡片
  const [viewMode, setViewMode] = useState<'table' | 'card'>('table')

  // 添加状态变量
  const [showHistoryDialog, setShowHistoryDialog] = useState(false)
  const [showTrendDialog, setShowTrendDialog] = useState(false)

  // 加载标签列表
  const loadTags = async () => {
    setLoading(true)
    try {
      const response = await fetchTags(
        deviceId,
        page,
        pageSize,
        searchQuery,
        statusFilter,
        sortField,
        sortDirection
      )

      setTags(response.items)
      setTotalItems(response.total)
      setTotalPages(response.totalPages)

      // 如果当前页超出了总页数，则回到第一页
      if (page > response.totalPages && response.totalPages > 0) {
        setPage(1)
      }
    } catch (error) {
      console.error('加载点位失败:', error)
    } finally {
      setLoading(false)
    }
  }

  // 初始加载和条件变化时重新加载
  useEffect(() => {
    loadTags()
  }, [
    deviceId,
    page,
    pageSize,
    searchQuery,
    statusFilter,
    sortField,
    sortDirection,
  ])

  // 切换排序方向
  const toggleSortDirection = () => {
    setSortDirection(sortDirection === 'asc' ? 'desc' : 'asc')
  }

  // 设置排序字段并切换方向
  const handleSort = (field: string) => {
    if (sortField === field) {
      toggleSortDirection()
    } else {
      setSortField(field)
      setSortDirection('asc')
    }
  }

  // 处理行点击
  const handleTagClick = (tag: Tag) => {
    setSelectedTag(tag)
  }

  // 分页控制
  const goToPage = (newPage: number) => {
    setPage(Math.max(1, Math.min(newPage, totalPages)))
  }

  const goToFirstPage = () => goToPage(1)
  const goToPreviousPage = () => goToPage(page - 1)
  const goToNextPage = () => goToPage(page + 1)
  const goToLastPage = () => goToPage(totalPages)

  // 获取状态徽章
  const getStatusBadge = (status: string, details?: string) => {
    let badge

    switch (status) {
      case 'Good':
        badge = (
          <Badge className="bg-green-100 text-green-800 hover:bg-green-200">
            正常
          </Badge>
        )
        break
      case 'Bad':
        badge = (
          <TooltipProvider>
            <Tooltip>
              <TooltipTrigger asChild>
                <Badge className="bg-red-100 text-red-800 hover:bg-red-200 cursor-help flex items-center gap-1">
                  <AlertCircle className="h-3 w-3" />
                  错误
                </Badge>
              </TooltipTrigger>
              <TooltipContent className="max-w-xs bg-red-50 border-red-200 text-red-800 p-3">
                <p>{details || '未知错误'}</p>
              </TooltipContent>
            </Tooltip>
          </TooltipProvider>
        )
        break
      case 'Warning':
        badge = (
          <TooltipProvider>
            <Tooltip>
              <TooltipTrigger asChild>
                <Badge className="bg-amber-100 text-amber-800 hover:bg-amber-200 cursor-help flex items-center gap-1">
                  <AlertCircle className="h-3 w-3" />
                  警告
                </Badge>
              </TooltipTrigger>
              <TooltipContent className="max-w-xs bg-amber-50 border-amber-200 text-amber-800 p-3">
                <p>{details || '警告状态，请注意关注'}</p>
              </TooltipContent>
            </Tooltip>
          </TooltipProvider>
        )
        break
      default:
        badge = <Badge variant="outline">未知</Badge>
    }

    return badge
  }

  // 获取数据类型图标
  const getDataTypeIcon = (dataType: string) => {
    switch (dataType) {
      case 'Float':
        return <ThermometerIcon className="h-4 w-4 text-blue-500" />
      case 'Integer':
        return <GaugeIcon className="h-4 w-4 text-purple-500" />
      case 'Boolean':
        return <ToggleLeftIcon className="h-4 w-4 text-green-500" />
      case 'String':
        return <BarChart3Icon className="h-4 w-4 text-orange-500" />
      case 'JSON':
        return <ZapIcon className="h-4 w-4 text-indigo-500" />
      default:
        return <ZapIcon className="h-4 w-4 text-gray-500" />
    }
  }

  // 格式化布尔值显示
  const formatBooleanValue = (value: any) => {
    if (value === 1 || value === true || value === '1' || value === 'true') {
      return <Badge className="bg-green-100 text-green-800">开启</Badge>
    }
    return <Badge className="bg-gray-100 text-gray-800">关闭</Badge>
  }

  // 格式化JSON值显示
  const formatJsonValue = (value: any) => {
    if (typeof value !== 'object' || value === null) {
      return <span className="text-muted-foreground">无效数据</span>
    }

    return (
      <div className="relative group">
        <Badge className="bg-indigo-100 text-indigo-800 cursor-pointer">
          JSON数据
        </Badge>
        <div className="absolute z-50 hidden group-hover:block bg-white border rounded-md shadow-lg p-3 mt-1 left-0 min-w-[300px] max-w-md">
          <pre className="text-xs overflow-auto max-h-[200px] whitespace-pre-wrap">
            {JSON.stringify(value, null, 2)}
          </pre>
        </div>
      </div>
    )
  }

  // 渲染标签值
  const renderTagValue = (tag: Tag) => {
    if (tag.value === null) {
      return <span className="text-muted-foreground">-</span>
    }

    if (tag.dataType === 'Boolean') {
      return formatBooleanValue(tag.value)
    }

    if (tag.dataType === 'JSON') {
      return formatJsonValue(tag.value)
    }

    return (
      <span>
        {tag.value.toString()}
        {tag.unit && (
          <span className="text-muted-foreground ml-1">{tag.unit}</span>
        )}
      </span>
    )
  }

  // 添加处理函数
  const handleViewHistory = (tag: Tag) => {
    setSelectedTag(tag)
    setShowHistoryDialog(true)
  }

  const handleViewTrend = (tag: Tag) => {
    setSelectedTag(tag)
    setShowTrendDialog(true)
  }

  // 渲染表格视图
  const renderTableView = () => {
    return (
      <div className="overflow-x-auto rounded-md border">
        <table className="w-full">
          <thead>
            <tr className="bg-muted/50 text-left text-sm border-b">
              <th className="px-4 py-3 font-medium">
                <div
                  className="flex items-center cursor-pointer"
                  onClick={() => handleSort('name')}>
                  名称
                  {sortField === 'name' && (
                    <ArrowUpDown className="ml-1 h-3 w-3" />
                  )}
                </div>
              </th>
              <th className="px-4 py-3 font-medium">
                <div
                  className="flex items-center cursor-pointer"
                  onClick={() => handleSort('dataType')}>
                  数据类型
                  {sortField === 'dataType' && (
                    <ArrowUpDown className="ml-1 h-3 w-3" />
                  )}
                </div>
              </th>
              <th className="px-4 py-3 font-medium">当前值</th>
              <th className="px-4 py-3 font-medium">
                <div
                  className="flex items-center cursor-pointer"
                  onClick={() => handleSort('status')}>
                  状态
                  {sortField === 'status' && (
                    <ArrowUpDown className="ml-1 h-3 w-3" />
                  )}
                </div>
              </th>
              <th className="px-4 py-3 font-medium">最后更新</th>
              <th className="px-4 py-3 font-medium">操作</th>
            </tr>
          </thead>
          <tbody>
            {tags.map((tag) => (
              <tr
                key={tag.id}
                className="border-b hover:bg-muted/20 transition-colors cursor-pointer"
                onClick={() => handleTagClick(tag)}>
                <td className="px-4 py-3 font-medium">{tag.name}</td>
                <td className="px-4 py-3">
                  <div className="flex items-center gap-1.5">
                    {getDataTypeIcon(tag.dataType)}
                    <span>{tag.dataType}</span>
                  </div>
                </td>
                <td className="px-4 py-3">{renderTagValue(tag)}</td>
                <td className="px-4 py-3">
                  {getStatusBadge(tag.status, tag.statusDetails)}
                </td>
                <td className="px-4 py-3 text-muted-foreground text-sm">
                  {tag.lastUpdate || '-'}
                </td>
                <td className="px-4 py-3">
                  <div className="flex items-center space-x-2">
                    <Button
                      variant="ghost"
                      size="icon"
                      onClick={() => handleViewHistory(tag)}>
                      <HistoryIcon className="h-4 w-4" />
                    </Button>
                    <Button
                      variant="ghost"
                      size="icon"
                      onClick={() => handleViewTrend(tag)}>
                      <TrendingUp className="h-4 w-4" />
                    </Button>
                    {onWriteTag && tag.accessRight !== '只读' && (
                      <Button
                        variant="ghost"
                        size="icon"
                        onClick={() => onWriteTag(tag)}>
                        <PencilIcon className="h-4 w-4" />
                      </Button>
                    )}
                  </div>
                </td>
              </tr>
            ))}
          </tbody>
        </table>
      </div>
    )
  }

  // 渲染卡片视图
  const renderCardView = () => {
    return (
      <div className="grid grid-cols-1 sm:grid-cols-2 lg:grid-cols-3 gap-4">
        {tags.map((tag) => (
          <Card
            key={tag.id}
            className={`cursor-pointer hover:shadow-md transition-shadow ${
              tag.status === 'Bad'
                ? 'border-red-200'
                : tag.status === 'Warning'
                ? 'border-amber-200'
                : ''
            }`}
            onClick={() => handleTagClick(tag)}>
            <CardContent className="p-4">
              <div className="flex justify-between items-start mb-2">
                <div className="flex items-center gap-2">
                  {getDataTypeIcon(tag.dataType)}
                  <h3 className="font-medium">{tag.name}</h3>
                </div>
                {getStatusBadge(tag.status, tag.statusDetails)}
              </div>

              <div className="mb-2">
                <div className="text-sm text-muted-foreground">当前值</div>
                <div className="font-medium">{renderTagValue(tag)}</div>
              </div>

              <div className="text-xs text-muted-foreground flex items-center justify-between mt-3">
                <div>{tag.description || '无描述'}</div>
                <div>{tag.lastUpdate || '-'}</div>
              </div>
            </CardContent>
          </Card>
        ))}
      </div>
    )
  }

  return (
    <div className="space-y-4">
      <div className="flex flex-col sm:flex-row gap-4 items-start sm:items-center justify-between">
        <div className="relative w-full sm:w-64">
          <Search className="absolute left-3 top-1/2 transform -translate-y-1/2 h-4 w-4 text-gray-400" />
          <Input
            placeholder="搜索点位..."
            className="pl-10"
            value={searchQuery}
            onChange={(e) => setSearchQuery(e.target.value)}
          />
        </div>

        <div className="flex items-center gap-2 w-full sm:w-auto">
          <Select value={statusFilter} onValueChange={setStatusFilter}>
            <SelectTrigger className="w-full sm:w-[150px]">
              <div className="flex items-center">
                <Filter className="mr-2 h-4 w-4" />
                <SelectValue placeholder="状态过滤" />
              </div>
            </SelectTrigger>
            <SelectContent>
              <SelectItem value="all">所有状态</SelectItem>
              <SelectItem value="Good">正常</SelectItem>
              <SelectItem value="Warning">警告</SelectItem>
              <SelectItem value="Bad">错误</SelectItem>
            </SelectContent>
          </Select>

          <DropdownMenu>
            <DropdownMenuTrigger asChild>
              <Button variant="outline" size="icon">
                <ArrowUpDown className="h-4 w-4" />
              </Button>
            </DropdownMenuTrigger>
            <DropdownMenuContent align="end">
              <DropdownMenuItem onClick={() => handleSort('name')}>
                按名称排序
              </DropdownMenuItem>
              <DropdownMenuItem onClick={() => handleSort('dataType')}>
                按数据类型排序
              </DropdownMenuItem>
              <DropdownMenuItem onClick={() => handleSort('status')}>
                按状态排序
              </DropdownMenuItem>
            </DropdownMenuContent>
          </DropdownMenu>

          {/* 视图切换按钮 */}
          <Button
            variant="outline"
            size="icon"
            onClick={() => setViewMode(viewMode === 'table' ? 'card' : 'table')}
            title={viewMode === 'table' ? '切换到卡片视图' : '切换到表格视图'}>
            {viewMode === 'table' ? (
              <LayoutGrid className="h-4 w-4" />
            ) : (
              <LayoutList className="h-4 w-4" />
            )}
          </Button>

          <Button
            variant="outline"
            size="icon"
            onClick={loadTags}
            disabled={loading}>
            <RefreshCw className={`h-4 w-4 ${loading ? 'animate-spin' : ''}`} />
          </Button>
        </div>
      </div>

      {loading ? (
        <div className="flex justify-center items-center py-12">
          <div className="flex flex-col items-center">
            <RefreshCw className="h-10 w-10 animate-spin text-primary" />
            <p className="mt-4 text-muted-foreground">加载点位中...</p>
          </div>
        </div>
      ) : tags.length === 0 ? (
        <div className="flex justify-center items-center py-12 bg-muted/20 rounded-lg border">
          <p className="text-muted-foreground">未找到点位</p>
        </div>
      ) : (
        <div className="grid grid-cols-1 gap-4">
          {viewMode === 'table' ? renderTableView() : renderCardView()}

          {selectedTag && (
            <TagDetailCard
              tag={selectedTag}
              onClose={() => setSelectedTag(null)}
            />
          )}
        </div>
      )}

      {showHistoryDialog && selectedTag && (
        <Dialog open={showHistoryDialog} onOpenChange={setShowHistoryDialog}>
          <DialogContent className="max-w-5xl max-h-[90vh] overflow-hidden flex flex-col p-0">
            <TagHistoryViewer
              tag={selectedTag}
              onBack={() => setShowHistoryDialog(false)}
            />
          </DialogContent>
        </Dialog>
      )}

      {showTrendDialog && selectedTag && (
        <Dialog open={showTrendDialog} onOpenChange={setShowTrendDialog}>
          <DialogContent className="max-w-5xl max-h-[90vh] overflow-hidden flex flex-col p-0">
            <TagTrendAnalyzer
              tag={selectedTag}
              onBack={() => setShowTrendDialog(false)}
            />
          </DialogContent>
        </Dialog>
      )}

      {/* 分页控制 */}
      <div className="flex flex-col sm:flex-row justify-between items-center gap-4 pt-2">
        <div className="text-sm text-muted-foreground">
          显示 {totalItems} 个点位中的 {(page - 1) * pageSize + 1} -{' '}
          {Math.min(page * pageSize, totalItems)} 个
        </div>

        <div className="flex items-center gap-1">
          <div className="flex items-center mr-2">
            <span className="text-sm text-muted-foreground mr-2">
              每页显示:
            </span>
            <Select
              value={pageSize.toString()}
              onValueChange={(value) => {
                setPageSize(Number(value))
                setPage(1) // 重置到第一页
              }}>
              <SelectTrigger className="w-[70px] h-8">
                <SelectValue />
              </SelectTrigger>
              <SelectContent>
                <SelectItem value="5">5</SelectItem>
                <SelectItem value="10">10</SelectItem>
                <SelectItem value="20">20</SelectItem>
                <SelectItem value="50">50</SelectItem>
              </SelectContent>
            </Select>
          </div>

          <Button
            variant="outline"
            size="icon"
            className="h-8 w-8"
            onClick={goToFirstPage}
            disabled={page === 1 || loading}>
            <ChevronsLeft className="h-4 w-4" />
          </Button>

          <Button
            variant="outline"
            size="icon"
            className="h-8 w-8"
            onClick={goToPreviousPage}
            disabled={page === 1 || loading}>
            <ChevronLeft className="h-4 w-4" />
          </Button>

          <span className="text-sm mx-2">
            第 <span className="font-medium">{page}</span> 页，共{' '}
            <span className="font-medium">{totalPages}</span> 页
          </span>

          <Button
            variant="outline"
            size="icon"
            className="h-8 w-8"
            onClick={goToNextPage}
            disabled={page === totalPages || loading}>
            <ChevronRight className="h-4 w-4" />
          </Button>

          <Button
            variant="outline"
            size="icon"
            className="h-8 w-8"
            onClick={goToLastPage}
            disabled={page === totalPages || loading}>
            <ChevronsRight className="h-4 w-4" />
          </Button>
        </div>
      </div>
    </div>
  )
}
