import { useState } from 'react'
import {
  <PERSON>,
  CardContent,
  CardHeader,
  CardTitle,
  CardFooter,
} from '@/components/ui/card'
import { Badge } from '@/components/ui/badge'
import { Button } from '@/components/ui/button'
import { Ta<PERSON>, <PERSON>bs<PERSON>ontent, <PERSON>bsList, TabsTrigger } from '@/components/ui/tabs'
import {
  ThermometerIcon,
  GaugeIcon,
  ToggleLeftIcon,
  BarChart3Icon,
  ZapIcon,
  ClockIcon,
  HistoryIcon,
  PencilIcon,
  XIcon,
  AlertCircle,
} from 'lucide-react'
import {
  Tooltip,
  TooltipContent,
  TooltipProvider,
  TooltipTrigger,
} from '@/components/ui/tooltip'
import { TagHistoryViewer } from './tag-history-viewer'
import { TagTrendAnalyzer } from './tag-trend-analyzer'

// 标签数据类型
interface Tag {
  id: string
  name: string
  value: string | number | null | object
  dataType: string
  status: string
  statusDetails?: string
  unit?: string
  lastUpdate?: string
  description?: string
  address?: string
  min?: number
  max?: number
  readOnly?: boolean
  custom?: Record<string, string>
}

interface TagDetailCardProps {
  tag: Tag | null
  onClose: () => void
}

export function TagDetailCard({ tag, onClose }: TagDetailCardProps) {
  const [activeTab, setActiveTab] = useState('info')

  if (!tag) return null

  // 获取数据类型图标
  const getDataTypeIcon = (dataType: string) => {
    switch (dataType) {
      case 'Float':
        return <ThermometerIcon className="h-5 w-5 text-blue-500" />
      case 'Integer':
        return <GaugeIcon className="h-5 w-5 text-purple-500" />
      case 'Boolean':
        return <ToggleLeftIcon className="h-5 w-5 text-green-500" />
      case 'String':
        return <BarChart3Icon className="h-5 w-5 text-orange-500" />
      case 'JSON':
        return <ZapIcon className="h-5 w-5 text-indigo-500" />
      default:
        return <ZapIcon className="h-5 w-5 text-gray-500" />
    }
  }

  // 格式化布尔值显示
  const formatBooleanValue = (value: any) => {
    if (value === 1 || value === true || value === '1' || value === 'true') {
      return <Badge className="bg-green-100 text-green-800">开启</Badge>
    }
    return <Badge className="bg-gray-100 text-gray-800">关闭</Badge>
  }

  // 获取状态徽章
  const getStatusBadge = (status: string, details?: string) => {
    switch (status) {
      case 'Good':
        return <Badge className="bg-green-100 text-green-800">正常</Badge>
      case 'Bad':
        return (
          <TooltipProvider>
            <Tooltip>
              <TooltipTrigger asChild>
                <Badge className="bg-red-100 text-red-800 flex items-center gap-1 cursor-help">
                  <AlertCircle className="h-3 w-3" />
                  错误
                </Badge>
              </TooltipTrigger>
              <TooltipContent className="max-w-xs bg-red-50 border-red-200 text-red-800 p-3">
                <p>{details || '未知错误'}</p>
              </TooltipContent>
            </Tooltip>
          </TooltipProvider>
        )
      case 'Warning':
        return (
          <TooltipProvider>
            <Tooltip>
              <TooltipTrigger asChild>
                <Badge className="bg-amber-100 text-amber-800 flex items-center gap-1 cursor-help">
                  <AlertCircle className="h-3 w-3" />
                  警告
                </Badge>
              </TooltipTrigger>
              <TooltipContent className="max-w-xs bg-amber-50 border-amber-200 text-amber-800 p-3">
                <p>{details || '警告状态，请注意关注'}</p>
              </TooltipContent>
            </Tooltip>
          </TooltipProvider>
        )
      default:
        return <Badge variant="outline">未知</Badge>
    }
  }

  // 渲染标签值
  const renderTagValue = () => {
    if (tag.value === null) {
      return <span className="text-muted-foreground">-</span>
    }

    if (tag.dataType === 'Boolean') {
      return formatBooleanValue(tag.value)
    }

    if (tag.dataType === 'JSON') {
      return (
        <div className="font-mono text-sm bg-muted/30 p-3 rounded-md overflow-auto max-h-[200px]">
          <pre className="whitespace-pre-wrap break-words">
            {JSON.stringify(tag.value, null, 2)}
          </pre>
        </div>
      )
    }

    return (
      <span>
        {tag.value.toString()}
        {tag.unit && (
          <span className="text-muted-foreground ml-1 text-sm">{tag.unit}</span>
        )}
      </span>
    )
  }

  return (
    <Card className="shadow-md">
      <CardHeader className="pb-2 flex flex-row items-start justify-between">
        <div>
          <div className="flex items-center gap-2">
            {getDataTypeIcon(tag.dataType)}
            <CardTitle>{tag.name}</CardTitle>
          </div>
          <p className="text-sm text-muted-foreground mt-1">
            {tag.description || '无描述信息'}
          </p>
        </div>
        <Button variant="ghost" size="icon" onClick={onClose}>
          <XIcon className="h-4 w-4" />
        </Button>
      </CardHeader>

      <Tabs value={activeTab} onValueChange={setActiveTab} className="w-full">
        <div className="px-6">
          <TabsList className="grid w-full grid-cols-3">
            <TabsTrigger value="info">基本信息</TabsTrigger>
            <TabsTrigger value="history">历史数据</TabsTrigger>
            <TabsTrigger value="trend">趋势分析</TabsTrigger>
          </TabsList>
        </div>

        <TabsContent value="info" className="pt-2">
          <CardContent>
            <div className="grid grid-cols-2 gap-4">
              <div className="space-y-1 col-span-2">
                <p className="text-sm text-muted-foreground">当前值</p>
                <div className="text-lg font-medium">{renderTagValue()}</div>
              </div>

              <div className="space-y-1">
                <p className="text-sm text-muted-foreground">状态</p>
                <p>{getStatusBadge(tag.status, tag.statusDetails)}</p>
              </div>

              <div className="space-y-1">
                <p className="text-sm text-muted-foreground">数据类型</p>
                <p className="font-mono text-sm">{tag.dataType}</p>
              </div>

              <div className="space-y-1">
                <p className="text-sm text-muted-foreground">地址</p>
                <p className="font-mono text-sm">{tag.address || '-'}</p>
              </div>

              <div className="space-y-1">
                <p className="text-sm text-muted-foreground">范围</p>
                <p className="text-sm">
                  {tag.min !== undefined && tag.max !== undefined ? (
                    <span>
                      {tag.min} ~ {tag.max}
                      {tag.unit && (
                        <span className="text-muted-foreground ml-1">
                          {tag.unit}
                        </span>
                      )}
                    </span>
                  ) : (
                    '-'
                  )}
                </p>
              </div>

              <div className="space-y-1">
                <p className="text-sm text-muted-foreground">访问权限</p>
                <p>
                  {tag.readOnly ? (
                    <Badge variant="outline">只读</Badge>
                  ) : (
                    <Badge variant="outline" className="bg-blue-50">
                      读写
                    </Badge>
                  )}
                </p>
              </div>
            </div>

            <div className="mt-4 pt-4 border-t">
              <div className="flex items-center gap-2 text-sm text-muted-foreground">
                <ClockIcon className="h-4 w-4" />
                <span>最后更新: {tag.lastUpdate || '未知'}</span>
              </div>
            </div>
          </CardContent>
        </TabsContent>

        <TabsContent value="history">
          <CardContent className="p-0">
            <TagHistoryViewer tag={tag} />
          </CardContent>
        </TabsContent>

        <TabsContent value="trend">
          <CardContent className="p-0">
            <TagTrendAnalyzer tag={tag} />
          </CardContent>
        </TabsContent>
      </Tabs>

      <CardFooter className="flex justify-between border-t pt-4">
        <Button variant="outline" size="sm" disabled={tag.readOnly}>
          <PencilIcon className="h-4 w-4 mr-2" />
          写入值
        </Button>
        <Button
          variant="outline"
          size="sm"
          onClick={() => setActiveTab('history')}>
          <HistoryIcon className="h-4 w-4 mr-2" />
          查看历史
        </Button>
      </CardFooter>
    </Card>
  )
}
