import { useState, useEffect, useCallback, useMemo, useRef } from 'react'
import { TagService, type DeviceTag } from '@/lib/api/tag-api'
import { Button } from '@/components/ui/button'
import { Input } from '@/components/ui/input'
import { toast } from '@/components/ui/use-toast'
import { Badge } from '@/components/ui/badge'
import {
  AlertDialog,
  AlertDialogAction,
  AlertDialogCancel,
  AlertDialogContent,
  AlertDialogDescription,
  AlertDialogFooter,
  AlertDialogHeader,
  AlertDialogTitle,
} from '@/components/ui/alert-dialog'
import {
  DropdownMenu,
  DropdownMenuContent,
  DropdownMenuTrigger,
  DropdownMenuSeparator,
  DropdownMenuLabel,
} from '@/components/ui/dropdown-menu'
import { AddDevicePointDialog } from './add-device-point-dialog'
// 导入SignalR钩子函数和ConnectionState类型
import { useSignalREvent, useSignalR } from '@/lib/signalr/signalr-context'
// 添加新的导入
import { Checkbox } from '@/components/ui/checkbox'
import ScriptEditorDialog from '@/components/ui/script-editor-dialog'
import {
  Trash,
  Power,
  Edit,
  RefreshCw,
  Check,
  Activity,
  TrendingUp,
  Wifi,
  WifiOff,
  Columns3,
  Eye,
  EyeOff,
  GripVertical,
  Star,
} from 'lucide-react'
import { BookmarkButton, BatchBookmarkButton } from './bookmark-button'
import { logger, log } from '@/lib/utils/logger'

// 智能值显示组件
const ObjectValueDisplay = ({
  value,
  dataType,
}: {
  value: any
  dataType: string
}) => {
  if (value === null || value === undefined) {
    return <span className="text-gray-400">-</span>
  }

  // 对于基础数据类型，直接显示
  if (typeof value !== 'object') {
    return <span>{String(value)}</span>
  }

  // 对于数组类型
  if (Array.isArray(value)) {
    return (
      <div className="group relative">
        <Badge
          variant="outline"
          className="cursor-pointer bg-blue-50 text-blue-700 border-blue-200">
          [{value.length} 项]
        </Badge>
        {/* 使用双定位策略：默认下方显示，鼠标在下半部分时上方显示 */}
        <div className="absolute z-[9999] hidden group-hover:block bg-white border rounded-md shadow-xl p-3 left-0 min-w-[300px] max-w-md top-full mt-2">
          <div className="text-xs font-medium text-gray-700 mb-2">
            数组内容：
          </div>
          <pre className="text-xs overflow-auto max-h-[200px] whitespace-pre-wrap bg-gray-50 p-2 rounded border">
            {JSON.stringify(value, null, 2)}
          </pre>
          {/* 添加上箭头指示器 */}
          <div className="absolute top-[-8px] left-4 w-0 h-0 border-l-8 border-r-8 border-b-8 border-l-transparent border-r-transparent border-b-white"></div>
        </div>
      </div>
    )
  }

  // 对于对象类型
  const objectKeys = Object.keys(value)
  return (
    <div className="group relative">
      <Badge
        variant="outline"
        className="cursor-pointer bg-indigo-50 text-indigo-700 border-indigo-200">
        {objectKeys.length} 属性
      </Badge>
      {/* 使用下方显示策略 */}
      <div className="absolute z-[9999] hidden group-hover:block bg-white border rounded-md shadow-xl p-3 left-0 min-w-[300px] max-w-md top-full mt-2">
        <div className="text-xs font-medium text-gray-700 mb-2">对象内容：</div>
        <pre className="text-xs overflow-auto max-h-[200px] whitespace-pre-wrap bg-gray-50 p-2 rounded border">
          {JSON.stringify(value, null, 2)}
        </pre>
        {/* 添加上箭头指示器 */}
        <div className="absolute top-[-8px] left-4 w-0 h-0 border-l-8 border-r-8 border-b-8 border-l-transparent border-r-transparent border-b-white"></div>
      </div>
    </div>
  )
}

// 格式化标签值的智能显示函数
const formatTagValue = (value: any, dataType: string) => {
  // 处理空值
  if (value === null || value === undefined) {
    return <span className="text-gray-400 italic">无数据</span>
  }

  // 根据数据类型进行特殊处理
  switch (dataType) {
    case 'Boolean':
      return (
        <Badge
          className={
            value ? 'bg-green-100 text-green-800' : 'bg-red-100 text-red-800'
          }>
          {value ? '真' : '假'}
        </Badge>
      )

    case 'Array':
    case 'Object':
      return <ObjectValueDisplay value={value} dataType={dataType} />

    case 'Binary':
      if (value instanceof ArrayBuffer || value instanceof Uint8Array) {
        const size =
          value instanceof ArrayBuffer ? value.byteLength : 0
        return (
          <Badge variant="outline" className="bg-gray-50 text-gray-700">
            二进制数据 ({size} 字节)
          </Badge>
        )
      }
      break

    default:
      // 对于其他类型，检查是否为对象
      if (typeof value === 'object' && value !== null) {
        return <ObjectValueDisplay value={value} dataType={dataType} />
      }
      return <span>{String(value)}</span>
  }

  return <span>{String(value)}</span>
}

// 添加自定义样式
const customStyles = `
  @keyframes subtle-highlight {
    0% { 
      background-color: rgb(239 246 255);
      opacity: 1;
    }
    50% { 
      background-color: rgb(219 234 254);
      opacity: 0.9;
    }
    100% { 
      background-color: rgb(239 246 255);
      opacity: 1;
    }
  }
  
  @keyframes text-flash {
    0% { color: rgb(37 99 235); }
    50% { color: rgb(59 130 246); }
    100% { color: rgb(37 99 235); }
  }
  
  .realtime-updated {
    background-color: rgb(239 246 255);
    border-left: 2px solid rgb(59 130 246);
    animation: subtle-highlight 2s ease-in-out;
  }
  
  .value-highlight {
    animation: text-flash 1s ease-in-out;
  }
  
  .smooth-transition {
    transition: background-color 0.2s ease, color 0.2s ease, border-color 0.2s ease;
  }
`

// 注入样式
if (typeof document !== 'undefined') {
  const styleElement = document.getElementById('tag-list-styles')
  if (!styleElement) {
    const style = document.createElement('style')
    style.id = 'tag-list-styles'
    style.textContent = customStyles
    document.head.appendChild(style)
  }
}

interface TagListProps {
  deviceId: string
  refreshTrigger?: number
  externalTags?: DeviceTag[]
  isLoading?: boolean
  onBatchEdit?: (tagsToEdit: DeviceTag[]) => void
  id?: string
  protocol?: string
  deviceName?: string
  deviceIdentifier?: string
  onCopyTag?: (tagToCopy: DeviceTag) => void
  onTagDeleted?: () => void
}

// 实时数据统计接口
interface RealTimeStats {
  updateCount: number
  lastUpdateTime: number
  updateFrequency: number
  connectedDuration: number
}

// 列配置接口
interface ColumnConfig {
  id: string
  label: string
  visible: boolean
  order: number
  sortable?: boolean
  width?: string
}

// 默认列配置
const DEFAULT_COLUMNS: ColumnConfig[] = [
  { id: 'name', label: '名称', visible: true, order: 0, sortable: true },
  {
    id: 'identifier',
    label: '标识符',
    visible: false,
    order: 1,
    sortable: true,
  },
  {
    id: 'dataType',
    label: '数据类型',
    visible: true,
    order: 2,
    sortable: true,
  },
  {
    id: 'dataSource',
    label: '数据来源',
    visible: true,
    order: 3,
    sortable: true,
  },
  { id: 'value', label: '当前值', visible: true, order: 4, sortable: false },
  { id: 'status', label: '状态', visible: true, order: 5, sortable: true },
  {
    id: 'timestamp',
    label: '最后更新',
    visible: true,
    order: 6,
    sortable: true,
  },
  { id: 'address', label: '地址', visible: false, order: 7, sortable: true },
  {
    id: 'readWrite',
    label: '读写权限',
    visible: false,
    order: 8,
    sortable: true,
  },
  {
    id: 'enabled',
    label: '启用状态',
    visible: true, // 默认显示启用状态
    order: 9,
    sortable: true,
  },
  { id: 'unit', label: '单位', visible: false, order: 10, sortable: false },
  {
    id: 'description',
    label: '描述',
    visible: false,
    order: 11,
    sortable: false,
  },
  {
    id: 'tags',
    label: '标签',
    visible: false,
    order: 14,
    sortable: false,
  },
  {
    id: 'uploadMethod',
    label: '上报方式',
    visible: false,
    order: 15,
    sortable: true,
  },
  {
    id: 'collectionInterval',
    label: '采集间隔',
    visible: false,
    order: 16,
    sortable: true,
  },
  {
    id: 'decimalPlaces',
    label: '小数位数',
    visible: false,
    order: 17,
    sortable: true,
  },
  {
    id: 'encoding',
    label: '编码格式',
    visible: false,
    order: 18,
    sortable: true,
  },
  {
    id: 'method',
    label: '访问方法',
    visible: false,
    order: 19,
    sortable: true,
  },
  {
    id: 'readLength',
    label: '读取长度',
    visible: false,
    order: 20,
    sortable: true,
  },
  {
    id: 'actionOrder',
    label: '采集优先级',
    visible: false,
    order: 21,
    sortable: true,
  },
  {
    id: 'transitionType',
    label: '转换类型',
    visible: false,
    order: 22,
    sortable: true,
  },

  { id: 'actions', label: '操作', visible: true, order: 24, sortable: false },
]

export function TagList({
  deviceId,
  refreshTrigger = 0,
  externalTags,
  isLoading: externalLoading,
  onBatchEdit,
  id,
  protocol = '',
  deviceName = '',
  deviceIdentifier,
  onCopyTag,
  onTagDeleted,
}: TagListProps) {
  const [tags, setTags] = useState<DeviceTag[]>(externalTags || [])
  const [loading, setLoading] = useState(externalTags ? false : true)
  const [searchTerm, setSearchTerm] = useState('')
  const [showBookmarkedOnly, setShowBookmarkedOnly] = useState(false) // 新增：标记筛选状态

  // 简化高亮状态管理：只使用一个高亮状态
  const [highlightedTags, setHighlightedTags] = useState<Set<number>>(new Set())
  const highlightTimeoutRef = useRef<NodeJS.Timeout | null>(null)

  // 添加实时数据统计状态
  const [realTimeStats, setRealTimeStats] = useState<RealTimeStats>({
    updateCount: 0,
    lastUpdateTime: 0,
    updateFrequency: 0,
    connectedDuration: 0,
  })

  // 添加更新频率控制
  const lastUpdateTimeRef = useRef(0)
  const [updateInterval, setUpdateInterval] = useState(50) // 降低到50ms以提高响应速度

  // 获取SignalR连接状态
  const { connectionState, connection, sendMessage } = useSignalR()

  // 添加订阅状态记录
  const [subscribed, setSubscribed] = useState(false)

  // 添加状态变量管理对话框显示和选中的点位
  const [showEditDialog, setShowEditDialog] = useState(false)
  const [showDeleteDialog, setShowDeleteDialog] = useState(false)
  const [selectedTag, setSelectedTag] = useState<DeviceTag | null>(null)

  // 添加多选模式状态和选中的点位
  const [selectionMode, setSelectionMode] = useState(false)
  const [selectedTags, setSelectedTags] = useState<Set<number>>(new Set())

  // 添加批量删除确认对话框状态
  const [showBatchDeleteDialog, setShowBatchDeleteDialog] = useState(false)

  // 添加批量启用/禁用确认对话框状态
  const [showBatchEnableDialog, setShowBatchEnableDialog] = useState(false)
  const [batchEnableValue, setBatchEnableValue] = useState(true)

  // 添加列管理状态
  const [columns, setColumns] = useState<ColumnConfig[]>(() => {
    // 尝试从localStorage加载配置
    if (typeof window !== 'undefined') {
      const storageKey = `tag-list-columns-${deviceId}-v2` // 使用v2版本key
      const saved = localStorage.getItem(storageKey)
      if (saved) {
        try {
          const savedColumns = JSON.parse(saved)
          // 验证保存的列数量是否与当前DEFAULT_COLUMNS匹配
          if (savedColumns.length === DEFAULT_COLUMNS.length) {
            return savedColumns
          } else {
            // 如果列数量不匹配，清除旧配置并使用默认配置
            localStorage.removeItem(storageKey)
            localStorage.setItem(storageKey, JSON.stringify(DEFAULT_COLUMNS))
            return DEFAULT_COLUMNS
          }
        } catch (e) {
          log.warn('加载列配置失败，使用默认配置')
          // 清除损坏的配置
          localStorage.removeItem(storageKey)
        }
      }
      // 保存默认配置到新版本的key
      localStorage.setItem(storageKey, JSON.stringify(DEFAULT_COLUMNS))
    }
    return DEFAULT_COLUMNS
  })

  // 拖拽状态
  const [draggedItem, setDraggedItem] = useState<string | null>(null)
  const [dragOverItem, setDragOverItem] = useState<string | null>(null)

  // 保存列配置到localStorage
  const saveColumnsConfig = useCallback(
    (newColumns: ColumnConfig[]) => {
      setColumns(newColumns)
      if (typeof window !== 'undefined') {
        const storageKey = `tag-list-columns-${deviceId}-v2` // 使用v2版本key
        localStorage.setItem(storageKey, JSON.stringify(newColumns))
      }
    },
    [deviceId]
  )

  // 切换列显示状态
  const toggleColumn = useCallback(
    (columnId: string) => {
      const newColumns = columns.map((col) =>
        col.id === columnId ? { ...col, visible: !col.visible } : col
      )
      saveColumnsConfig(newColumns)
    },
    [columns, saveColumnsConfig]
  )

  // 重置列配置
  const resetColumns = useCallback(() => {
    saveColumnsConfig(DEFAULT_COLUMNS)
  }, [saveColumnsConfig])

  // 拖拽处理函数
  const handleDragStart = useCallback(
    (e: React.DragEvent, columnId: string) => {
      setDraggedItem(columnId)
      e.dataTransfer.effectAllowed = 'move'
    },
    []
  )

  const handleDragOver = useCallback((e: React.DragEvent, columnId: string) => {
    e.preventDefault()
    e.dataTransfer.dropEffect = 'move'
    setDragOverItem(columnId)
  }, [])

  const handleDragLeave = useCallback(() => {
    setDragOverItem(null)
  }, [])

  const handleDrop = useCallback(
    (e: React.DragEvent, targetColumnId: string) => {
      e.preventDefault()

      if (!draggedItem || draggedItem === targetColumnId) {
        setDraggedItem(null)
        setDragOverItem(null)
        return
      }

      const draggedIndex = columns.findIndex((col) => col.id === draggedItem)
      const targetIndex = columns.findIndex((col) => col.id === targetColumnId)

      if (draggedIndex === -1 || targetIndex === -1) return

      // 创建新的列数组
      const newColumns = [...columns]
      const [draggedColumn] = newColumns.splice(draggedIndex, 1)
      newColumns.splice(targetIndex, 0, draggedColumn)

      // 重新分配order值
      const reorderedColumns = newColumns.map((col, index) => ({
        ...col,
        order: index,
      }))

      saveColumnsConfig(reorderedColumns)
      setDraggedItem(null)
      setDragOverItem(null)
    },
    [draggedItem, columns, saveColumnsConfig]
  )

  // 获取可见列（按order排序）
  const visibleColumns = useMemo(() => {
    return columns
      .filter((col) => col.visible)
      .sort((a, b) => a.order - b.order)
  }, [columns])

  // 获取所有列（按order排序，用于列管理界面）
  const sortedColumns = useMemo(() => {
    return [...columns].sort((a, b) => a.order - b.order)
  }, [columns])

  // 添加连接时间跟踪
  useEffect(() => {
    let interval: NodeJS.Timeout | null = null

    if (connectionState === 'connected') {
      const startTime = Date.now()
      interval = setInterval(() => {
        setRealTimeStats((prev) => ({
          ...prev,
          connectedDuration: Date.now() - startTime,
        }))
      }, 1000)
    }

    return () => {
      if (interval) clearInterval(interval)
    }
  }, [connectionState])

  const fetchTags = useCallback(async () => {
    if (externalTags !== undefined) return

    try {
      setLoading(true)
      const data = await TagService.getTagsByDeviceId(String(deviceId))
      setTags(data || [])
    } catch (error) {
      log.error('获取点位列表失败', error)
      toast({
        title: '错误',
        description: '获取点位列表失败',
        variant: 'destructive',
      })
    } finally {
      setLoading(false)
    }
  }, [deviceId, externalTags])

  useEffect(() => {
    fetchTags()
  }, [fetchTags, refreshTrigger])

  // 处理外部传入的tags数据
  useEffect(() => {
    if (externalTags !== undefined) {
      setTags(externalTags)
    }
  }, [externalTags])

  useEffect(() => {
    if (externalLoading !== undefined) {
      setLoading(externalLoading)
    }
  }, [externalLoading])

  // 初始订阅逻辑
  useEffect(() => {
    const subscribeToTopic = async () => {
      if (connection && connectionState === 'connected' && !subscribed) {
        try {
          await sendMessage('SubscribeTopic', 'online')
          setSubscribed(true)
          toast({
            title: '实时数据已启用',
            description: '已成功订阅点位数据更新',
            duration: 3000,
          })
        } catch (error) {
          log.error('订阅online主题失败:', error)
        }
      }
    }

    subscribeToTopic()

    // 组件卸载时取消订阅
    return () => {
      if (subscribed && connection && connectionState === 'connected') {
        sendMessage('UnsubscribeTopic', 'online').catch((err) => {
          log.error('取消订阅失败:', err)
        })
      }
    }
  }, [connection, connectionState, subscribed, sendMessage])

  // 监听连接状态变化
  useEffect(() => {
    if (connectionState === 'connected') {
      // 连接恢复时刷新数据（仅当不是外部数据时）
      if (externalTags === undefined) {
        // 直接调用API而不依赖fetchTags函数
        const refreshData = async () => {
          try {
            const data = await TagService.getTagsByDeviceId(String(deviceId))
            setTags(data || [])
          } catch (error) {
            log.error('连接恢复后刷新数据失败:', error)
          }
        }
        refreshData()
      }

      // 如果还没订阅，进行订阅
      if (!subscribed) {
        const subscribeToTopic = async () => {
          try {
            await sendMessage('SubscribeTopic', 'online')
            setSubscribed(true)
          } catch (error) {
            log.error('订阅online主题失败:', error)
          }
        }

        subscribeToTopic()
      }
    } else if (connectionState === 'disconnected') {
      // 连接断开时设置为未订阅状态
      if (subscribed) {
        setSubscribed(false)
      }
    }
  }, [connectionState, subscribed, deviceId, sendMessage, externalTags])

  // 添加批量更新的防抖处理
  const updateBatchRef = useRef<Map<string, any>>(new Map())
  const updateTimeoutRef = useRef<NodeJS.Timeout | null>(null)

  // 添加渲染性能监控
  const renderCountRef = useRef(0)
  const lastRenderTimeRef = useRef(performance.now())

  // 优化的批量处理更新函数 - 添加详细调试日志和提高匹配准确性
  const processBatchUpdates = useCallback(() => {
    const batchStartTime = performance.now()
    log.debug('🔄 [Batch] 开始批量处理:', {
      batchStartTime,
      queueSize: updateBatchRef.current.size,
      queueKeys: Array.from(updateBatchRef.current.keys())
    })

    if (updateBatchRef.current.size === 0) {
      log.debug('⚠️ [Batch] 队列为空，跳过处理')
      return
    }

    const updatesToProcess = new Map(updateBatchRef.current)
    updateBatchRef.current.clear()
    log.debug('📋 [Batch] 准备处理的更新:', {
      updateCount: updatesToProcess.size,
      updates: Array.from(updatesToProcess.entries())
    })

    const updatedIds = new Set<number>()
    let matchedCount = 0
    let unmatchedUpdates: any[] = []

    setTags((prevTags) => {
      log.debug('🏷️ [Batch] 当前标签数量:', prevTags.length)

      const newTags = prevTags.map((tag, index) => {
        // 尝试多种匹配方式以确保数据能够正确匹配
        let updatedTagData = updatesToProcess.get(tag.identifier) ||
                           updatesToProcess.get(tag.id.toString()) ||
                           updatesToProcess.get(tag.name) ||
                           updatesToProcess.get(tag.alias)

        if (updatedTagData) {
          matchedCount++
          updatedIds.add(tag.id)

          const oldValue = tag.value
          const newValue = updatedTagData.value !== undefined ? updatedTagData.value : tag.value
          const oldStatus = tag.status
          const newStatus = updatedTagData.status || tag.status
          const newTimestamp = updatedTagData.timestamp || updatedTagData.time || new Date().toISOString()

          log.debug(`✅ [Batch] 匹配成功 - 标签 ${tag.name}:`, {
            tagId: tag.id,
            identifier: tag.identifier,
            matchedBy: updatesToProcess.has(tag.identifier) ? 'identifier' :
                      updatesToProcess.has(tag.id.toString()) ? 'id' :
                      updatesToProcess.has(tag.name) ? 'name' : 'alias',
            oldValue,
            newValue,
            valueChanged: oldValue !== newValue,
            oldStatus,
            newStatus,
            statusChanged: oldStatus !== newStatus,
            newTimestamp,
            updateData: updatedTagData
          })

          return {
            ...tag,
            value: newValue,
            status: newStatus,
            timestamp: newTimestamp,
          }
        }
        return tag
      })

      // 记录未匹配的更新
      updatesToProcess.forEach((updateData, key) => {
        const isMatched = prevTags.some(tag =>
          tag.identifier === key ||
          tag.id.toString() === key ||
          tag.name === key ||
          tag.alias === key
        )
        if (!isMatched) {
          unmatchedUpdates.push({ key, updateData })
        }
      })

      log.debug('📊 [Batch] 匹配结果:', {
        totalTags: prevTags.length,
        totalUpdates: updatesToProcess.size,
        matchedCount,
        unmatchedCount: unmatchedUpdates.length,
        unmatchedUpdates,
        updatedTagIds: Array.from(updatedIds)
      })

      return newTags
    })

    // 更新高亮状态
    setHighlightedTags(new Set(updatedIds))
    log.debug('🎨 [Batch] 设置高亮标签:', Array.from(updatedIds))

    // 清除之前的定时器
    if (highlightTimeoutRef.current) {
      clearTimeout(highlightTimeoutRef.current)
    }

    // 缩短高亮显示时间以提高视觉响应
    highlightTimeoutRef.current = setTimeout(() => {
      setHighlightedTags(new Set())
      log.debug('🎨 [Batch] 清除高亮状态')
    }, 2000)

    const batchEndTime = performance.now()
    log.debug('✨ [Batch] 批量处理完成:', {
      batchEndTime,
      totalBatchTime: batchEndTime - batchStartTime,
      updatedCount: updatedIds.size,
      success: updatedIds.size > 0
    })
  }, [])

  // 优化的实时数据更新处理函数 - 添加详细调试日志
  const handleUpdatedTag = useCallback(
    (updateData: any) => {
      const receiveTime = performance.now()
      log.debug('🔵 [Socket] 接收到数据:', {
        timestamp: new Date().toISOString(),
        performanceTime: receiveTime,
        data: updateData,
        dataType: typeof updateData,
        isArray: Array.isArray(updateData)
      })

      if (!updateData) {
        log.debug('❌ [Socket] 数据为空，跳过处理')
        return
      }

      const currentTime = Date.now()
      const timeSinceLastUpdate = currentTime - lastUpdateTimeRef.current

      // 移除频率控制以确保所有数据都能被处理
      log.debug('⏱️ [Socket] 时间检查:', {
        currentTime,
        lastUpdateTime: lastUpdateTimeRef.current,
        timeSinceLastUpdate,
        willSkip: false // 不再跳过任何更新
      })

      // 将数据统一处理为数组格式
      const dataArray = Array.isArray(updateData) ? updateData : [updateData]
      log.debug('📦 [Socket] 数据数组化:', {
        originalLength: Array.isArray(updateData) ? updateData.length : 1,
        processedLength: dataArray.length,
        dataArray
      })

      // 将更新数据添加到批量处理队列，支持多种标识符格式
      let addedCount = 0
      dataArray.forEach((item: any, index: number) => {
        // 支持多种标识符格式以确保数据匹配
        const identifier = item.identifier || item.id || item.tagId || item.name
        log.debug(`📝 [Socket] 处理数据项 ${index}:`, {
          item,
          identifier,
          hasIdentifier: !!item.identifier,
          hasId: !!item.id,
          hasTagId: !!item.tagId,
          hasName: !!item.name,
          finalIdentifier: identifier
        })

        if (identifier) {
          updateBatchRef.current.set(identifier, item)
          addedCount++
          log.debug(`✅ [Socket] 数据项已添加到队列:`, {
            identifier,
            queueSize: updateBatchRef.current.size,
            item
          })
        } else {
          log.debug(`❌ [Socket] 数据项缺少标识符，跳过:`, item)
        }
      })

      log.debug('📊 [Socket] 队列状态:', {
        addedCount,
        totalQueueSize: updateBatchRef.current.size,
        queueKeys: Array.from(updateBatchRef.current.keys())
      })

      // 更新统计信息
      setRealTimeStats((prev) => {
        const newUpdateCount = prev.updateCount + dataArray.length
        const timeDiff = currentTime - prev.lastUpdateTime
        const newFrequency = timeDiff > 0 ? 1000 / timeDiff : 0

        log.debug('📈 [Socket] 统计信息更新:', {
          newUpdateCount,
          timeDiff,
          newFrequency
        })

        return {
          ...prev,
          updateCount: newUpdateCount,
          lastUpdateTime: currentTime,
          updateFrequency: newFrequency,
        }
      })

      lastUpdateTimeRef.current = currentTime

      // 清除之前的防抖定时器
      if (updateTimeoutRef.current) {
        clearTimeout(updateTimeoutRef.current)
        log.debug('🔄 [Socket] 清除之前的防抖定时器')
      }

      const processStartTime = performance.now()
      log.debug('🚀 [Socket] 立即处理更新:', {
        processStartTime,
        timeSinceReceive: processStartTime - receiveTime
      })

      // 立即处理更新，不使用防抖延迟
      processBatchUpdates()

      const processEndTime = performance.now()
      log.debug('✨ [Socket] 处理完成:', {
        processEndTime,
        totalProcessTime: processEndTime - receiveTime,
        processingTime: processEndTime - processStartTime
      })
    },
    [processBatchUpdates]
  )

  // 订阅'online'方法
  useSignalREvent('online', handleUpdatedTag)

  // 添加渲染性能监控
  useEffect(() => {
    const currentTime = performance.now()
    renderCountRef.current++
    const timeSinceLastRender = currentTime - lastRenderTimeRef.current

    log.debug('🎨 [Render] 组件重新渲染:', {
      renderCount: renderCountRef.current,
      currentTime,
      timeSinceLastRender,
      tagsLength: tags.length,
      highlightedTagsSize: highlightedTags.size,
      filteredTagsLength: filteredTags.length
    })

    lastRenderTimeRef.current = currentTime
  })

  // 监控tags状态变化
  useEffect(() => {
    log.debug('📊 [State] tags状态更新:', {
      timestamp: performance.now(),
      tagsLength: tags.length,
      tags: tags.slice(0, 3).map(tag => ({ // 只显示前3个标签的信息
        id: tag.id,
        name: tag.name,
        identifier: tag.identifier,
        value: tag.value,
        timestamp: tag.timestamp
      }))
    })
  }, [tags])

  // 监控highlightedTags状态变化
  useEffect(() => {
    log.debug('🎨 [State] highlightedTags状态更新:', {
      timestamp: performance.now(),
      highlightedCount: highlightedTags.size,
      highlightedIds: Array.from(highlightedTags)
    })
  }, [highlightedTags])

  const filteredTags = tags.filter((tag) => {
    // 搜索筛选
    const matchesSearch =
      tag.name.toLowerCase().includes(searchTerm.toLowerCase()) ||
      (tag.alias && tag.alias.toLowerCase().includes(searchTerm.toLowerCase()))

    // 标记筛选
    const matchesBookmark = !showBookmarkedOnly || tag.isBookmarked

    return matchesSearch && matchesBookmark
  })

  const getDataTypeDisplay = (dataType: string) => {
    const typeMap: Record<string, string> = {
      Boolean: '布尔值',
      Number: '数值',
      Integer: '整数',
      String: '字符串',
      Array: '数组',
      Object: '对象',
      Binary: '二进制',
    }
    return typeMap[dataType] || dataType
  }

  // 获取上报方式显示
  const getSendTypeDisplay = (tag: DeviceTag) => {
    // 优先使用sendType字段，然后回退到uploadMethod
    const sendType =
      tag.sendType !== undefined ? tag.sendType : tag.uploadMethod
    if (sendType === undefined) return '-'

    const sendTypeMap: Record<
      string | number,
      { text: string; color: string }
    > = {
      // 数字枚举格式（API返回）
      1: {
        text: '总是上报',
        color: 'bg-blue-100 text-blue-800 border-blue-200',
      },
      2: {
        text: '从不上报',
        color: 'bg-gray-100 text-gray-800 border-gray-200',
      },
      3: {
        text: '变化上报',
        color: 'bg-green-100 text-green-800 border-green-200',
      },
      // 字符串格式（兼容）
      Always: {
        text: '总是上报',
        color: 'bg-blue-100 text-blue-800 border-blue-200',
      },
      Never: {
        text: '从不上报',
        color: 'bg-gray-100 text-gray-800 border-gray-200',
      },
      Changed: {
        text: '变化上报',
        color: 'bg-green-100 text-green-800 border-green-200',
      },
      // 中文格式（表单输入）
      总是上报: {
        text: '总是上报',
        color: 'bg-blue-100 text-blue-800 border-blue-200',
      },
      从不上报: {
        text: '从不上报',
        color: 'bg-gray-100 text-gray-800 border-gray-200',
      },
      变化上报: {
        text: '变化上报',
        color: 'bg-green-100 text-green-800 border-green-200',
      },
      定时上报: {
        text: '定时上报',
        color: 'bg-orange-100 text-orange-800 border-orange-200',
      },
      条件上报: {
        text: '条件上报',
        color: 'bg-purple-100 text-purple-800 border-purple-200',
      },
    }

    const config = sendTypeMap[sendType] || {
      text: '未知',
      color: 'bg-gray-100 text-gray-800 border-gray-200',
    }

    return (
      <Badge
        variant="outline"
        className={`text-xs font-medium ${config.color}`}>
        {config.text}
      </Badge>
    )
  }

  // 获取数据来源显示 (更新为处理枚举值)
  const getDataSourceDisplay = (tag: DeviceTag) => {
    // 优先使用valueSource字段，然后回退到dataSourceType
    const dataSource =
      tag.valueSource !== undefined ? tag.valueSource : tag.dataSourceType

    // 如果dataSource为空或undefined，返回默认值
    if (dataSource === undefined) {
      return (
        <Badge
          variant="outline"
          className="text-xs font-medium bg-gray-100 text-gray-800 border-gray-200">
          未知
        </Badge>
      )
    }

    const sourceMap: Record<string | number, { text: string; color: string }> =
      {
        // 数字枚举格式（API返回）
        0: {
          text: '设备读取',
          color: 'bg-blue-100 text-blue-800 border-blue-200',
        },
        1: {
          text: '脚本计算',
          color: 'bg-purple-100 text-purple-800 border-purple-200',
        },
        2: {
          text: '静态属性',
          color: 'bg-gray-100 text-gray-800 border-gray-200',
        },
        3: {
          text: '虚拟属性',
          color: 'bg-orange-100 text-orange-800 border-orange-200',
        },
        // 字符串格式（API可能返回）
        Read: {
          text: '设备读取',
          color: 'bg-blue-100 text-blue-800 border-blue-200',
        },
        Calculate: {
          text: '脚本计算',
          color: 'bg-purple-100 text-purple-800 border-purple-200',
        },
        Static: {
          text: '静态属性',
          color: 'bg-gray-100 text-gray-800 border-gray-200',
        },
        Virtual: {
          text: '虚拟属性',
          color: 'bg-orange-100 text-orange-800 border-orange-200',
        },
        // 内部格式（dataSourceType）
        device: {
          text: '设备读取',
          color: 'bg-blue-100 text-blue-800 border-blue-200',
        },
        script: {
          text: '脚本计算',
          color: 'bg-purple-100 text-purple-800 border-purple-200',
        },
        static: {
          text: '静态属性',
          color: 'bg-gray-100 text-gray-800 border-gray-200',
        },
        virtual: {
          text: '虚拟属性',
          color: 'bg-orange-100 text-orange-800 border-orange-200',
        },
      }

    const config = sourceMap[dataSource] || {
      text: '未知',
      color: 'bg-gray-100 text-gray-800 border-gray-200',
    }

    return (
      <Badge
        variant="outline"
        className={`text-xs font-medium ${config.color}`}>
        {config.text}
      </Badge>
    )
  }

  const getStatusDisplay = (status?: string) => {
    if (!status) return '未知'

    const statusMap: Record<string, { text: string; color: string }> = {
      Good: { text: '正常', color: 'text-green-600' },
      Bad: { text: '异常', color: 'text-red-600' },
      Uncertain: { text: '不确定', color: 'text-yellow-600' },
      Stale: { text: '过期', color: 'text-gray-600' },
    }

    return (
      <span className={statusMap[status]?.color || ''}>
        {statusMap[status]?.text || status}
      </span>
    )
  }

  // 获取读写权限显示
  const getReadWriteDisplay = (tag: DeviceTag) => {
    // 优先使用protectType字段，然后回退到readWrite
    const permission = tag.protectType || tag.readWrite
    if (!permission) return '-'

    const rwMap: Record<string, { text: string; color: string }> = {
      // protectType格式
      OnlyRead: { text: '只读', color: 'bg-gray-100 text-gray-700' },
      OnlyWrite: { text: '只写', color: 'bg-orange-100 text-orange-700' },
      ReadWrite: { text: '读写', color: 'bg-green-100 text-green-700' },
      // readWrite格式（兼容）
      R: { text: '只读', color: 'bg-gray-100 text-gray-700' },
      W: { text: '只写', color: 'bg-orange-100 text-orange-700' },
      RW: { text: '读写', color: 'bg-green-100 text-green-700' },
      Read: { text: '只读', color: 'bg-gray-100 text-gray-700' },
      Write: { text: '只写', color: 'bg-orange-100 text-orange-700' },
    }

    const rw = rwMap[permission] || {
      text: permission,
      color: 'bg-gray-100 text-gray-700',
    }

    return (
      <Badge variant="outline" className={`text-xs ${rw.color}`}>
        {rw.text}
      </Badge>
    )
  }

  // 处理启用/禁用点位
  const handleToggleEnabled = async (tag: DeviceTag) => {
    try {
      const newEnabledState = !(tag.enable !== undefined
        ? tag.enable
        : tag.enabled)

      // 调用API更新启用状态（TagService.enableTag已包含toast提示）
      const success = await TagService.enableTag(
        String(deviceId),
        tag.id,
        newEnabledState
      )

      if (success) {
        // 更新本地状态
        setTags((prevTags) =>
          prevTags.map((t) =>
            t.id === tag.id
              ? {
                  ...t,
                  enabled: newEnabledState,
                  enable: newEnabledState,
                }
              : t
          )
        )
        // TagService.enableTag已经显示了toast，不需要重复显示
      }
    } catch (error) {
      log.error('更新启用状态失败:', error)
      toast({
        title: '操作失败',
        description: '无法更新点位状态，请重试',
        variant: 'destructive',
      })
    }
  }

  // 获取数值范围显示
  const getRangeDisplay = (tag: DeviceTag) => {
    if (tag.min !== undefined && tag.max !== undefined) {
      return `${tag.min} ~ ${tag.max}`
    } else if (tag.min !== undefined) {
      return `≥ ${tag.min}`
    } else if (tag.max !== undefined) {
      return `≤ ${tag.max}`
    }
    return '-'
  }

  // 渲染列内容
  const renderColumnContent = (
    columnId: string,
    tag: DeviceTag,
    isHighlighted: boolean
  ) => {
    switch (columnId) {
      case 'name':
        return (
          <div>
            <div className="font-medium">{tag.name}</div>
            {tag.alias && (
              <div className="text-xs text-gray-500">({tag.alias})</div>
            )}
          </div>
        )

      case 'identifier':
        return (
          <code className="text-xs bg-gray-100 px-1 py-0.5 rounded font-mono">
            {tag.identifier}
          </code>
        )

      case 'dataType':
        return (
          <Badge variant="outline" className="text-xs">
            {getDataTypeDisplay(tag.dataType)}
          </Badge>
        )

      case 'dataSource':
        return getDataSourceDisplay(tag)

      case 'value':
        return (
          <div
            className={`smooth-transition ${
              isHighlighted
                ? 'font-bold text-blue-600 value-highlight'
                : 'font-medium text-gray-900'
            }`}>
            {tag.value !== undefined && tag.value !== null ? (
              formatTagValue(
                tag.value,
                tag.dataType || tag.transitionType || 'String'
              )
            ) : (
              <span className="text-gray-400 italic">无数据</span>
            )}
            {tag.unit && (
              <span className="ml-1 text-xs text-muted-foreground">
                {tag.unit}
              </span>
            )}
          </div>
        )

      case 'status':
        return getStatusDisplay(tag.status)

      case 'timestamp':
        return (
          <div
            className={`text-xs smooth-transition ${
              isHighlighted
                ? 'text-blue-600 font-medium'
                : 'text-muted-foreground'
            }`}>
            {tag.timestamp ? new Date(tag.timestamp).toLocaleString() : '-'}
          </div>
        )

      case 'address':
        const addressValue = tag.registerAddress || tag.address
        return addressValue ? (
          <code className="text-xs bg-gray-100 px-1 py-0.5 rounded font-mono">
            {addressValue}
          </code>
        ) : (
          '-'
        )

      case 'readWrite':
        return getReadWriteDisplay(tag)

      case 'enabled':
        const enabledStatus =
          tag.enable !== undefined ? tag.enable : tag.enabled
        return (
          <div className="flex items-center justify-center">
            <button
              onClick={() => handleToggleEnabled(tag)}
              className={`relative inline-flex h-5 w-9 items-center rounded-full transition-colors focus:outline-none focus:ring-2 focus:ring-blue-500 focus:ring-offset-2 ${
                enabledStatus
                  ? 'bg-green-500 hover:bg-green-600'
                  : 'bg-gray-300 hover:bg-gray-400'
              }`}
              role="switch"
              aria-checked={enabledStatus}
              title={`点击${enabledStatus ? '禁用' : '启用'}点位`}>
              <span
                className={`inline-block h-3 w-3 transform rounded-full bg-white transition-transform ${
                  enabledStatus ? 'translate-x-5' : 'translate-x-1'
                }`}
              />
            </button>
          </div>
        )

      case 'unit':
        return tag.unit ? (
          <Badge variant="outline" className="text-xs">
            {tag.unit}
          </Badge>
        ) : (
          '-'
        )

      case 'description':
        return tag.description ? (
          <div
            className="text-xs text-gray-600 max-w-32 truncate"
            title={tag.description}>
            {tag.description}
          </div>
        ) : (
          '-'
        )

      case 'range':
        return (
          <div className="text-xs text-gray-600">{getRangeDisplay(tag)}</div>
        )

      case 'alias':
        return tag.alias ? (
          <div className="text-xs text-gray-600">{tag.alias}</div>
        ) : (
          '-'
        )

      case 'tags':
        return tag.tags ? (
          <div className="text-xs text-gray-600">{tag.tags.join(', ')}</div>
        ) : (
          '-'
        )

      case 'uploadMethod':
        return getSendTypeDisplay(tag)

      case 'collectionInterval':
        return tag.period ? (
          <div className="text-xs text-gray-600">{tag.period}ms</div>
        ) : (
          '-'
        )

      case 'decimalPlaces':
        return tag.length !== undefined || tag.decimalPlaces !== undefined ? (
          <div className="text-xs text-gray-600">
            {tag.decimalPlaces !== undefined ? tag.decimalPlaces : tag.length}
          </div>
        ) : (
          '-'
        )

      case 'encoding':
        return tag.encoding ? (
          <code className="text-xs bg-gray-100 px-1 py-0.5 rounded font-mono">
            {tag.encoding}
          </code>
        ) : (
          '-'
        )

      case 'method':
        return tag.method ? (
          <code className="text-xs bg-gray-100 px-1 py-0.5 rounded font-mono">
            {tag.method}
          </code>
        ) : (
          '-'
        )

      case 'readLength':
        return tag.readLength ? (
          <div className="text-xs text-gray-600">{tag.readLength}</div>
        ) : (
          '-'
        )

      case 'scriptPriority':
        return tag.actionOrder !== undefined ? (
          <div className="text-xs text-gray-600">{tag.actionOrder}</div>
        ) : (
          '-'
        )

      case 'transitionType':
        return tag.transitionType ? (
          <Badge
            variant="outline"
            className="text-xs font-medium bg-gray-100 text-gray-800 border-gray-200">
            {tag.transitionType}
          </Badge>
        ) : (
          '-'
        )

      case 'content':
        return tag.content ? (
          <div
            className="text-xs text-gray-600 max-w-xs truncate"
            title={tag.content}>
            {tag.content}
          </div>
        ) : (
          '-'
        )

      case 'actions':
        return (
          <div className="flex space-x-1">
            <BookmarkButton
              tag={tag}
              size="sm"
              variant="outline"
              onBookmarkChange={(tagId, isBookmarked) => {
                // 更新本地状态
                setTags((prevTags) =>
                  prevTags.map((t) =>
                    t.id === tagId ? { ...t, isBookmarked } : t
                  )
                )
              }}
            />
            <Button
              variant="outline"
              size="sm"
              className="h-7 px-2 text-xs"
              onClick={() => handleEditTag(tag)}>
              编辑
            </Button>
            {/* 脚本编辑按钮 - 仅当数据来源为脚本计算时显示，或者已有脚本内容时显示 */}
            {(tag.dataSourceType === 'script' ||
              tag.valueSource === 1 ||
              tag.content ||
              tag.processingScript) && (
              <Button
                variant="outline"
                size="sm"
                className="h-7 px-2 text-xs text-purple-600 hover:text-purple-700"
                onClick={() => handleEditScript(tag)}
                title="编辑脚本">
                脚本
              </Button>
            )}
            <Button
              variant="outline"
              size="sm"
              className="h-7 px-2 text-xs text-blue-600 hover:text-blue-700"
              onClick={() => handleCopyTag(tag)}>
              复制
            </Button>
            <Button
              variant="outline"
              size="sm"
              className="h-7 px-2 text-xs text-red-600 hover:text-red-700"
              onClick={() => handleDeleteTag(tag)}>
              删除
            </Button>
          </div>
        )

      default:
        return '-'
    }
  }

  // 处理编辑点位
  const handleEditTag = async (tag: DeviceTag) => {
    log.debug('🚀 [handleEditTag] 开始编辑点位', {
      tagId: tag.id,
      tagIdentifier: tag.identifier,
      listData: {
        dataType: tag.dataType,
        encoding: tag.encoding,
        method: tag.method,
        registerAddress: tag.registerAddress,
        readLength: tag.readLength
      }
    })

    try {
      // 先获取点位详情数据
      const tagDetail = await TagService.getTagDetail(deviceId, tag.id!)

      log.debug('🚀 [handleEditTag] 获取到详情数据', {
        success: !!tagDetail,
        detailData: tagDetail ? {
          dataType: tagDetail.dataType,
          encoding: tagDetail.encoding,
          method: tagDetail.method,
          registerAddress: tagDetail.registerAddress,
          readLength: tagDetail.readLength
        } : null
      })

      if (tagDetail) {
        // 使用详情数据设置选中的标签
        setSelectedTag(tagDetail)
        setShowEditDialog(true)
      } else {
        // 如果获取详情失败，使用原有数据作为备选
        log.warn('获取点位详情失败，使用列表数据')
        toast({
          title: '提示',
          description: '获取点位详情失败，将使用当前列表数据进行编辑',
          variant: 'default',
        })
        setSelectedTag(tag)
        setShowEditDialog(true)
      }
    } catch (error) {
      log.error('获取点位详情失败:', error)
      // 出错时使用原有数据作为备选
      toast({
        title: '提示',
        description: '获取点位详情失败，将使用当前列表数据进行编辑',
        variant: 'default',
      })
      setSelectedTag(tag)
      setShowEditDialog(true)
    }
  }

  // 处理删除点位
  const handleDeleteTag = (tag: DeviceTag) => {
    setSelectedTag(tag)
    setShowDeleteDialog(true)
  }

  // 执行点位删除
  const confirmDeleteTag = async () => {
    if (!selectedTag) return

    try {
      setLoading(true)
      // 确保deviceId是数字或字符串类型
      const success = await TagService.batchDeleteTags(deviceId, [
        selectedTag.id,
      ])

      if (success) {
        toast({
          title: '删除成功',
          description: `点位 ${selectedTag.name || '未命名'} 已成功删除`,
        })

        // 从列表中移除被删除的点位
        setTags(tags.filter((tag) => tag.id !== selectedTag.id))

        // 调用删除回调函数
        if (onTagDeleted) {
          onTagDeleted()
        }
      } else {
        toast({
          title: '删除失败',
          description: '无法删除点位，请重试',
          variant: 'destructive',
        })
      }
    } catch (error) {
      log.error('删除点位失败', error)
      toast({
        title: '删除失败',
        description: '无法删除点位，请重试',
        variant: 'destructive',
      })
    } finally {
      setLoading(false)
      setShowDeleteDialog(false)
      setSelectedTag(null)
    }
  }

  // 处理点位更新
  const handleTagUpdated = (updatedTag: DeviceTag) => {
    // 更新本地标签列表
    setTags((prevTags) =>
      prevTags.map((tag) => (tag.id === updatedTag.id ? updatedTag : tag))
    )

    toast({
      title: '更新成功',
      description: `点位 ${updatedTag.name || '未命名'} 已成功更新`,
    })
  }

  // 处理多选模式切换
  const handleToggleSelectionMode = () => {
    // 如果关闭选择模式，清空所有选择
    if (selectionMode) {
      setSelectedTags(new Set())
    }
    setSelectionMode(!selectionMode)
  }

  // 处理单个标签选择
  const handleTagSelection = (tagId: number) => {
    const newSelectedTags = new Set(selectedTags)
    if (newSelectedTags.has(tagId)) {
      newSelectedTags.delete(tagId)
    } else {
      newSelectedTags.add(tagId)
    }
    setSelectedTags(newSelectedTags)
  }

  // 全选/取消全选
  const handleSelectAll = (selected: boolean) => {
    if (selected) {
      const allIds = filteredTags.map((tag) => tag.id)
      setSelectedTags(new Set(allIds))
    } else {
      setSelectedTags(new Set())
    }
  }

  // 批量删除点位
  const confirmBatchDeleteTags = async () => {
    if (selectedTags.size === 0) return

    try {
      setLoading(true)
      const tagIdsToDelete = Array.from(selectedTags)

      // 在实际应用中调用API删除点位
      const success = await TagService.batchDeleteTags(deviceId, tagIdsToDelete)

      if (success) {
        toast({
          title: '批量删除成功',
          description: `已删除 ${selectedTags.size} 个点位`,
        })

        // 从列表中移除被删除的点位
        setTags(tags.filter((tag) => !selectedTags.has(tag.id)))
        // 清空选择
        setSelectedTags(new Set())

        // 调用删除回调函数
        if (onTagDeleted) {
          onTagDeleted()
        }
      } else {
        toast({
          title: '删除失败',
          description: '无法删除点位，请重试',
          variant: 'destructive',
        })
      }
    } catch (error) {
      log.error('批量删除点位失败', error)
      toast({
        title: '删除失败',
        description: '无法删除点位，请重试',
        variant: 'destructive',
      })
    } finally {
      setLoading(false)
      setShowBatchDeleteDialog(false)
    }
  }

  // 批量启用/禁用点位
  const confirmBatchEnableTags = async () => {
    if (selectedTags.size === 0) return

    try {
      setLoading(true)
      const tagIdsToUpdate = Array.from(selectedTags)

      // 调用真实的API进行批量启用/禁用
      const success = await TagService.batchEnableTags(
        Number(deviceId),
        tagIdsToUpdate,
        batchEnableValue
      )

      if (success) {
        toast({
          title: batchEnableValue ? '批量启用成功' : '批量禁用成功',
          description: `已${batchEnableValue ? '启用' : '禁用'} ${
            selectedTags.size
          } 个点位`,
        })

        // 更新本地点位状态
        setTags(
          tags.map((tag) => {
            if (selectedTags.has(tag.id)) {
              return {
                ...tag,
                enabled: batchEnableValue,
                enable: batchEnableValue, // 同时更新enable字段
              }
            }
            return tag
          })
        )
        // 清空选择
        setSelectedTags(new Set())
      } else {
        toast({
          title: '操作失败',
          description: `无法${batchEnableValue ? '启用' : '禁用'}点位，请重试`,
          variant: 'destructive',
        })
      }
    } catch (error) {
      log.error('批量启用/禁用点位失败', error)
      toast({
        title: '操作失败',
        description: `无法${batchEnableValue ? '启用' : '禁用'}点位，请重试`,
        variant: 'destructive',
      })
    } finally {
      setLoading(false)
      setShowBatchEnableDialog(false)
    }
  }

  // 处理批量编辑
  const handleBatchEdit = () => {
    if (selectedTags.size === 0) {
      toast({
        title: '请选择点位',
        description: '请先选择要编辑的点位',
        variant: 'destructive',
      })
      return
    }

    // 获取选中的点位数据
    const selectedTagsData = tags.filter((tag) => selectedTags.has(tag.id))

    // 如果提供了外部批量编辑处理函数，则调用它
    if (onBatchEdit) {
      onBatchEdit(selectedTagsData)
    } else {
      // 否则显示提示
      toast({
        title: '功能未实现',
        description: `已选择 ${selectedTags.size} 个点位，但批量编辑功能尚未实现`,
      })
    }
  }

  // 处理自定义事件激活批量模式
  useEffect(() => {
    // 仅当id存在时添加事件监听
    if (id) {
      const handleActivateBatchMode = () => {
        // 如果当前不是选择模式，则激活选择模式
        if (!selectionMode) {
          setSelectionMode(true)

          // 显示操作提示
          toast({
            title: '批量管理模式已启用',
            description: '您可以选择多个点位进行批量操作',
          })
        }
      }

      // 添加事件监听器
      const element = document.getElementById(id)
      if (element) {
        element.addEventListener('activate-batch-mode', handleActivateBatchMode)

        // 清理函数
        return () => {
          element.removeEventListener(
            'activate-batch-mode',
            handleActivateBatchMode
          )
        }
      }
    }
  }, [id, selectionMode])

  // 处理复制点位
  const handleCopyTag = (tag: DeviceTag) => {
    if (onCopyTag) {
      onCopyTag(tag)
    }
  }

  // 添加脚本编辑相关状态
  const [scriptEditorOpen, setScriptEditorOpen] = useState(false)
  const [currentScript, setCurrentScript] = useState('')
  const [scriptMode, setScriptMode] = useState<
    'script' | 'point' | 'expression'
  >('point')
  const [scriptEditingTag, setScriptEditingTag] = useState<DeviceTag | null>(
    null
  )

  // 编辑脚本回调函数
  const handleScriptOpen = useCallback(
    (scriptContent: string, mode: string) => {
      setCurrentScript(scriptContent)
      setScriptMode(mode as 'script' | 'point' | 'expression')
      setScriptEditorOpen(true)
    },
    []
  )

  // 脚本保存回调函数
  const handleScriptSave = useCallback(
    (scriptContent: string) => {
      setCurrentScript(scriptContent)

      // 如果有正在编辑的tag，更新它的脚本内容
      if (scriptEditingTag) {
        setTags((prevTags) =>
          prevTags.map((tag) =>
            tag.id === scriptEditingTag.id
              ? {
                  ...tag,
                  content: scriptContent,
                  processingScript: scriptContent,
                }
              : tag
          )
        )

        // 显示保存成功提示
        toast({
          title: '脚本已保存',
          description: '脚本内容已更新到点位',
        })
      }

      setScriptEditorOpen(false)
      setScriptEditingTag(null)
    },
    [scriptEditingTag]
  )

  // 处理脚本编辑
  const handleEditScript = useCallback(
    (tag: DeviceTag) => {
      setScriptEditingTag(tag)
      const scriptContent = tag.content || tag.processingScript || ''
      handleScriptOpen(scriptContent, 'point')
    },
    [handleScriptOpen]
  )

  return (
    <div className="space-y-4" id={id}>
      <div className="flex items-center justify-between gap-4 min-h-[40px]">
        {/* 左侧：搜索和刷新 */}
        <div className="flex items-center gap-3 flex-shrink-0">
          <Input
            placeholder="搜索点位..."
            value={searchTerm}
            onChange={(e) => setSearchTerm(e.target.value)}
            className="w-64"
          />
          <Button
            variant={showBookmarkedOnly ? 'default' : 'outline'}
            size="sm"
            onClick={() => setShowBookmarkedOnly(!showBookmarkedOnly)}
            className="whitespace-nowrap">
            <Star
              className={`mr-2 h-4 w-4 ${
                showBookmarkedOnly ? 'fill-amber-400 text-amber-400' : ''
              }`}
            />
            {showBookmarkedOnly ? '显示全部' : '仅标记'}
          </Button>
          <Button
            variant="outline"
            size="sm"
            onClick={fetchTags}
            disabled={externalTags !== undefined || loading}
            className="whitespace-nowrap">
            <RefreshCw className="mr-2 h-4 w-4" />
            刷新
          </Button>
        </div>

        {/* 右侧：实时信息 + 管理按钮 */}
        <div className="flex items-center gap-3 flex-shrink-0">
          {/* 实时连接状态 */}
          <div className="flex items-center gap-2 text-sm">
            {connectionState === 'connected' ? (
              <Wifi className="h-4 w-4 text-green-500" />
            ) : (
              <WifiOff className="h-4 w-4 text-gray-400" />
            )}
            <Badge
              variant={
                connectionState === 'connected' ? 'success' : 'secondary'
              }
              className={
                connectionState === 'connected'
                  ? 'bg-green-50 text-green-700 border-green-200'
                  : 'bg-gray-100 text-gray-600'
              }>
              {connectionState === 'connected'
                ? subscribed
                  ? '实时'
                  : '已连接'
                : connectionState === 'connecting' ||
                  connectionState === 'reconnecting'
                ? '连接中'
                : '离线'}
            </Badge>

            {/* 实时统计信息 */}
            {connectionState === 'connected' &&
              subscribed &&
              realTimeStats.updateCount > 0 && (
                <div className="flex items-center gap-2 text-xs text-muted-foreground">
                  <span className="flex items-center gap-1">
                    <Activity className="h-3 w-3" />
                    {realTimeStats.updateCount}
                  </span>
                  {realTimeStats.updateFrequency > 0 && (
                    <span className="flex items-center gap-1">
                      <TrendingUp className="h-3 w-3" />
                      {realTimeStats.updateFrequency.toFixed(1)}Hz
                    </span>
                  )}
                </div>
              )}
          </div>

          {/* 管理按钮 */}
          <div className="flex items-center gap-2">
            <Button
              variant={selectionMode ? 'default' : 'outline'}
              size="sm"
              onClick={handleToggleSelectionMode}
              className={
                selectionMode
                  ? 'bg-blue-600 hover:bg-blue-700 text-white'
                  : 'bg-blue-50 border-blue-200 hover:bg-blue-100 text-blue-700'
              }>
              {selectionMode ? (
                <>
                  <Check className="mr-2 h-4 w-4" />
                  退出批量
                </>
              ) : (
                <>
                  <Edit className="mr-2 h-4 w-4" />
                  批量管理
                </>
              )}
            </Button>

            <DropdownMenu>
              <DropdownMenuTrigger asChild>
                <Button variant="outline" size="sm">
                  <Columns3 className="mr-2 h-4 w-4" />
                  列管理
                </Button>
              </DropdownMenuTrigger>
              <DropdownMenuContent align="end" className="w-64">
                <DropdownMenuLabel className="flex items-center justify-between">
                  <span>列管理</span>
                  <Button
                    variant="ghost"
                    size="sm"
                    className="h-6 px-2 text-xs"
                    onClick={resetColumns}>
                    重置
                  </Button>
                </DropdownMenuLabel>
                <DropdownMenuSeparator />
                <div className="text-xs text-muted-foreground px-2 py-1">
                  拖拽调整列顺序，勾选显示/隐藏列
                </div>
                <DropdownMenuSeparator />
                <div className="max-h-64 overflow-y-auto">
                  {sortedColumns.map((column, index) => (
                    <div
                      key={column.id}
                      draggable
                      onDragStart={(e) => handleDragStart(e, column.id)}
                      onDragOver={(e) => handleDragOver(e, column.id)}
                      onDragLeave={handleDragLeave}
                      onDrop={(e) => handleDrop(e, column.id)}
                      className={`flex items-center px-2 py-2 cursor-move hover:bg-gray-50 transition-colors ${
                        draggedItem === column.id ? 'opacity-50' : ''
                      } ${
                        dragOverItem === column.id
                          ? 'bg-blue-50 border-l-2 border-blue-500'
                          : ''
                      }`}>
                      <GripVertical className="h-3 w-3 text-gray-400 mr-2 flex-shrink-0" />
                      <div className="flex items-center justify-between w-full min-w-0">
                        <div className="flex items-center space-x-2 min-w-0 flex-1">
                          <input
                            type="checkbox"
                            checked={column.visible}
                            onChange={() => toggleColumn(column.id)}
                            disabled={column.id === 'name'}
                            className="rounded border-gray-300 text-blue-600 focus:ring-blue-500"
                          />
                          <span
                            className={`text-sm truncate ${
                              column.id === 'name' ? 'text-gray-500' : ''
                            }`}>
                            {column.label}
                          </span>
                        </div>
                        <div className="flex items-center space-x-1 flex-shrink-0">
                          {column.visible ? (
                            <Eye className="h-3 w-3 text-green-500" />
                          ) : (
                            <EyeOff className="h-3 w-3 text-gray-400" />
                          )}
                          <span className="text-xs text-gray-400 w-4 text-center">
                            {index + 1}
                          </span>
                        </div>
                      </div>
                    </div>
                  ))}
                </div>
                <DropdownMenuSeparator />
                <div className="px-2 py-1 text-xs text-muted-foreground">
                  显示 {visibleColumns.length} / {columns.length} 列
                </div>
              </DropdownMenuContent>
            </DropdownMenu>
          </div>
        </div>
      </div>

      {/* 批量操作工具栏 - 增加高亮 */}
      {selectionMode && (
        <div className="flex flex-wrap items-center gap-2 bg-blue-50 p-2 rounded-md border border-blue-100 w-full mt-2">
          <span className="text-sm font-medium">
            已选择 {selectedTags.size} 个点位
          </span>
          <Button
            variant="outline"
            size="sm"
            onClick={() =>
              handleSelectAll(selectedTags.size < filteredTags.length)
            }>
            {selectedTags.size < filteredTags.length ? '全选' : '取消全选'}
          </Button>
          <div className="flex-1"></div> {/* 空白间隔 */}
          <Button
            variant="outline"
            size="sm"
            onClick={handleBatchEdit}
            disabled={selectedTags.size === 0}
            className="bg-white">
            <Edit className="mr-2 h-4 w-4" />
            批量编辑
          </Button>
          <Button
            variant="outline"
            size="sm"
            onClick={() => {
              setBatchEnableValue(true)
              setShowBatchEnableDialog(true)
            }}
            disabled={selectedTags.size === 0}
            className="bg-white">
            <Power className="mr-2 h-4 w-4 text-green-500" />
            批量启用
          </Button>
          <Button
            variant="outline"
            size="sm"
            onClick={() => {
              setBatchEnableValue(false)
              setShowBatchEnableDialog(true)
            }}
            disabled={selectedTags.size === 0}
            className="bg-white">
            <Power className="mr-2 h-4 w-4 text-gray-500" />
            批量禁用
          </Button>
          <BatchBookmarkButton
            selectedTags={tags.filter((tag) => selectedTags.has(tag.id))}
            onBookmarkChange={(tagIds, isBookmarked) => {
              // 更新本地状态
              setTags((prevTags) =>
                prevTags.map((tag) =>
                  tagIds.includes(tag.id) ? { ...tag, isBookmarked } : tag
                )
              )
              // 清空选择
              setSelectedTags(new Set())
            }}
          />
          <Button
            variant="outline"
            size="sm"
            onClick={() => setShowBatchDeleteDialog(true)}
            disabled={selectedTags.size === 0}
            className="text-red-600 hover:text-red-700 bg-white">
            <Trash className="mr-2 h-4 w-4" />
            批量删除
          </Button>
        </div>
      )}

      {loading ? (
        <div className="text-center py-8">加载中...</div>
      ) : filteredTags.length === 0 ? (
        <div className="text-center py-8 text-gray-500">
          {searchTerm ? '没有找到匹配的点位' : '暂无点位数据'}
        </div>
      ) : (
        <div
          className="overflow-x-auto overflow-y-visible"
          style={{ minHeight: '400px', paddingBottom: '250px' }}>
          <table className="w-full border-collapse">
            <thead>
              <tr className="bg-gray-100">
                {selectionMode && (
                  <th className="px-4 py-2">
                    <Checkbox
                      checked={
                        selectedTags.size > 0 &&
                        selectedTags.size === filteredTags.length
                      }
                      onCheckedChange={handleSelectAll}
                    />
                  </th>
                )}
                {visibleColumns.map((column) => (
                  <th key={column.id} className="px-4 py-2 text-left">
                    {column.label}
                  </th>
                ))}
              </tr>
            </thead>
            <tbody>
              {filteredTags.map((tag) => {
                const isHighlighted = highlightedTags.has(tag.id)
                const isSelected = selectedTags.has(tag.id)

                return (
                  <tr
                    key={tag.id}
                    className={`border-b hover:bg-gray-50 smooth-transition ${
                      isHighlighted
                        ? 'realtime-updated'
                        : isSelected
                        ? 'bg-blue-50'
                        : ''
                    }`}>
                    {selectionMode && (
                      <td className="px-4 py-2">
                        <Checkbox
                          checked={selectedTags.has(tag.id)}
                          onCheckedChange={() => handleTagSelection(tag.id)}
                        />
                      </td>
                    )}
                    {visibleColumns.map((column) => (
                      <td key={column.id} className="px-4 py-2">
                        {renderColumnContent(column.id, tag, isHighlighted)}
                      </td>
                    ))}
                  </tr>
                )
              })}
            </tbody>
          </table>
        </div>
      )}

      {/* 编辑点位对话框 - 替换为AddDevicePointDialog */}
      <AddDevicePointDialog
        open={showEditDialog}
        onOpenChange={setShowEditDialog}
        deviceId={deviceId}
        editMode={true}
        tagToEdit={selectedTag}
        onTagUpdated={handleTagUpdated}
        protocol={protocol}
        deviceName={deviceName}
        onScriptOpen={handleScriptOpen}
        onScriptSave={handleScriptSave}
      />

      {/* 删除确认对话框 */}
      <AlertDialog open={showDeleteDialog} onOpenChange={setShowDeleteDialog}>
        <AlertDialogContent className="bg-white border-2 border-gray-300 shadow-xl">
          <AlertDialogHeader>
            <AlertDialogTitle>确认删除</AlertDialogTitle>
            <AlertDialogDescription>
              确定要删除点位 "{selectedTag?.name || '未命名'}"
              吗？此操作无法撤销。
            </AlertDialogDescription>
          </AlertDialogHeader>
          <AlertDialogFooter>
            <AlertDialogCancel>取消</AlertDialogCancel>
            <AlertDialogAction
              className="bg-red-600 hover:bg-red-700 text-white"
              onClick={confirmDeleteTag}>
              删除
            </AlertDialogAction>
          </AlertDialogFooter>
        </AlertDialogContent>
      </AlertDialog>

      {/* 批量删除确认对话框 */}
      <AlertDialog
        open={showBatchDeleteDialog}
        onOpenChange={setShowBatchDeleteDialog}>
        <AlertDialogContent className="bg-white border-2 border-gray-300 shadow-xl">
          <AlertDialogHeader>
            <AlertDialogTitle>确认批量删除</AlertDialogTitle>
            <AlertDialogDescription>
              确定要删除选中的 {selectedTags.size} 个点位吗？此操作无法撤销。
            </AlertDialogDescription>
          </AlertDialogHeader>
          <AlertDialogFooter>
            <AlertDialogCancel>取消</AlertDialogCancel>
            <AlertDialogAction
              className="bg-red-600 hover:bg-red-700 text-white"
              onClick={confirmBatchDeleteTags}>
              批量删除
            </AlertDialogAction>
          </AlertDialogFooter>
        </AlertDialogContent>
      </AlertDialog>

      {/* 批量启用/禁用确认对话框 */}
      <AlertDialog
        open={showBatchEnableDialog}
        onOpenChange={setShowBatchEnableDialog}>
        <AlertDialogContent className="bg-white border-2 border-gray-300 shadow-xl">
          <AlertDialogHeader>
            <AlertDialogTitle>
              确认{batchEnableValue ? '批量启用' : '批量禁用'}
            </AlertDialogTitle>
            <AlertDialogDescription>
              确定要{batchEnableValue ? '启用' : '禁用'}选中的{' '}
              {selectedTags.size} 个点位吗？
            </AlertDialogDescription>
          </AlertDialogHeader>
          <AlertDialogFooter>
            <AlertDialogCancel>取消</AlertDialogCancel>
            <AlertDialogAction
              className={
                batchEnableValue
                  ? 'bg-green-600 hover:bg-green-700 text-white'
                  : 'bg-gray-600 hover:bg-gray-700 text-white'
              }
              onClick={confirmBatchEnableTags}>
              确认{batchEnableValue ? '启用' : '禁用'}
            </AlertDialogAction>
          </AlertDialogFooter>
        </AlertDialogContent>
      </AlertDialog>

      {/* 脚本编辑器对话框 */}
      <ScriptEditorDialog
        open={scriptEditorOpen}
        onOpenChange={setScriptEditorOpen}
        title={
          scriptEditingTag
            ? `编辑点位 "${scriptEditingTag.name}" 的脚本`
            : '编辑脚本'
        }
        initialCode={currentScript}
        onSave={handleScriptSave}
        mode={scriptMode}
        deviceId={deviceId}
        deviceIdentifier={deviceIdentifier}
      />
    </div>
  )
}
