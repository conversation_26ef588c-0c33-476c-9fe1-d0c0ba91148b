import React, { useState } from 'react'
import { But<PERSON> } from '@/components/ui/button'
import {
  Select,
  SelectContent,
  SelectItem,
  SelectTrigger,
  SelectValue,
} from '@/components/ui/select'
import { Tabs, TabsList, TabsTrigger } from '@/components/ui/tabs'
import { Badge } from '@/components/ui/badge'
import { Checkbox } from '@/components/ui/checkbox'
import { Label } from '@/components/ui/label'
import { ScrollArea } from '@/components/ui/scroll-area'
import {
  Popover,
  PopoverContent,
  PopoverTrigger,
} from '@/components/ui/popover'
import {
  Table,
  BarChart3,
  LineChart,
  TrendingUp,
  Download,
  Camera,
  Settings,
  Columns,
  FileSpreadsheet,
  FileText,
  FileJson,
  Image,
} from 'lucide-react'
import { toast } from '@/components/ui/use-toast'
import type {
  DataViewControlsProps,
  ViewMode,
  ChartType,
  ExportOptions,
  ScreenshotOptions,
} from './types'

export function DataViewControls({
  viewMode,
  chartType,
  onViewModeChange,
  onChartTypeChange,
  selectedColumns,
  availableColumns,
  onColumnsChange,
  onExport,
  onScreenshot,
}: DataViewControlsProps) {
  const [showColumnSelector, setShowColumnSelector] = useState(false)
  const [showExportOptions, setShowExportOptions] = useState(false)
  const [showScreenshotOptions, setShowScreenshotOptions] = useState(false)

  // 视图模式配置
  const viewModes = [
    { value: 'table' as ViewMode, label: '表格', icon: Table },
    { value: 'chart' as ViewMode, label: '图表', icon: BarChart3 },
    { value: 'statistics' as ViewMode, label: '统计', icon: TrendingUp },
    { value: 'comparison' as ViewMode, label: '对比', icon: LineChart },
  ]

  // 图表类型配置
  const chartTypes = [
    { value: 'line' as ChartType, label: '折线图', icon: LineChart },
    { value: 'bar' as ChartType, label: '柱状图', icon: BarChart3 },
    { value: 'scatter' as ChartType, label: '散点图', icon: TrendingUp },
    { value: 'area' as ChartType, label: '面积图', icon: LineChart },
  ]

  // 导出格式配置
  const exportFormats = [
    {
      value: 'excel',
      label: 'Excel文件',
      icon: FileSpreadsheet,
      description: '包含数据和图表',
    },
    {
      value: 'csv',
      label: 'CSV文件',
      icon: FileText,
      description: '纯数据文件',
    },
    {
      value: 'json',
      label: 'JSON文件',
      icon: FileJson,
      description: '结构化数据',
    },
  ]

  // 截屏目标配置
  const screenshotTargets = [
    { value: 'table', label: '表格', description: '截取当前表格视图' },
    { value: 'chart', label: '图表', description: '截取当前图表' },
    { value: 'full', label: '完整页面', description: '截取整个页面' },
  ]

  // 切换列选择
  const toggleColumn = (column: string) => {
    if (column === 'timestamp') return // 时间列不能取消选择

    const newColumns = selectedColumns.includes(column)
      ? selectedColumns.filter((col) => col !== column)
      : [...selectedColumns, column]
    onColumnsChange(newColumns)
  }

  // 全选/取消全选列
  const toggleSelectAllColumns = () => {
    if (selectedColumns.length === availableColumns.length) {
      onColumnsChange(['timestamp']) // 保留时间列
    } else {
      onColumnsChange(availableColumns)
    }
  }

  // 执行导出
  const handleExport = (
    format: 'excel' | 'csv' | 'json',
    includeChart: boolean = false
  ) => {
    const options: ExportOptions = {
      format,
      includeChart,
      selectedColumns,
      fileName: `历史数据_${new Date()
        .toISOString()
        .slice(0, 19)
        .replace(/[-:T]/g, '')}`,
    }

    onExport(options)
    setShowExportOptions(false)

    toast({
      title: '导出已开始',
      description: `正在导出${format.toUpperCase()}格式的数据...`,
    })
  }

  // 执行截屏
  const handleScreenshot = (
    target: 'table' | 'chart' | 'full',
    format: 'png' | 'jpeg' = 'png'
  ) => {
    const options: ScreenshotOptions = {
      target,
      format,
      quality: format === 'jpeg' ? 0.9 : undefined,
      fileName: `截屏_${new Date()
        .toISOString()
        .slice(0, 19)
        .replace(/[-:T]/g, '')}`,
    }

    onScreenshot(options)
    setShowScreenshotOptions(false)

    toast({
      title: '截屏已开始',
      description: `正在生成${
        target === 'full' ? '完整页面' : target === 'chart' ? '图表' : '表格'
      }截图...`,
    })
  }

  return (
    <div className="flex items-center justify-between p-4 border-b bg-background/95 backdrop-blur">
      {/* 左侧：视图模式切换 */}
      <div className="flex items-center gap-4">
        <Tabs
          value={viewMode}
          onValueChange={(value) => onViewModeChange(value as ViewMode)}>
          <TabsList className="grid grid-cols-4 w-fit">
            {viewModes.map(({ value, label, icon: Icon }) => (
              <TabsTrigger
                key={value}
                value={value}
                className="flex items-center gap-2">
                <Icon className="h-4 w-4" />
                <span className="hidden sm:inline">{label}</span>
              </TabsTrigger>
            ))}
          </TabsList>
        </Tabs>

        {/* 图表类型选择（仅在图表模式下显示） */}
        {viewMode === 'chart' && (
          <Select
            value={chartType}
            onValueChange={(value) => onChartTypeChange(value as ChartType)}>
            <SelectTrigger className="w-32">
              <SelectValue />
            </SelectTrigger>
            <SelectContent>
              {chartTypes.map(({ value, label, icon: Icon }) => (
                <SelectItem key={value} value={value}>
                  <div className="flex items-center gap-2">
                    <Icon className="h-4 w-4" />
                    {label}
                  </div>
                </SelectItem>
              ))}
            </SelectContent>
          </Select>
        )}
      </div>

      {/* 右侧：工具栏 */}
      <div className="flex items-center gap-2">
        {/* 列选择器（仅在表格模式下显示） */}
        {viewMode === 'table' && (
          <Popover
            open={showColumnSelector}
            onOpenChange={setShowColumnSelector}>
            <PopoverTrigger asChild>
              <Button variant="outline" size="sm">
                <Columns className="h-4 w-4 mr-2" />
                列管理
                <Badge variant="secondary" className="ml-2">
                  {selectedColumns.length}
                </Badge>
              </Button>
            </PopoverTrigger>
            <PopoverContent className="w-80" align="end">
              <div className="space-y-4">
                <div className="flex items-center justify-between">
                  <Label className="text-sm font-medium">显示列</Label>
                  <Button
                    variant="outline"
                    size="sm"
                    onClick={toggleSelectAllColumns}>
                    {selectedColumns.length === availableColumns.length
                      ? '取消全选'
                      : '全选'}
                  </Button>
                </div>

                <ScrollArea className="h-48">
                  <div className="space-y-2">
                    {availableColumns.map((column) => (
                      <div key={column} className="flex items-center space-x-2">
                        <Checkbox
                          id={`column-${column}`}
                          checked={selectedColumns.includes(column)}
                          onCheckedChange={() => toggleColumn(column)}
                          disabled={column === 'timestamp'} // 时间列固定显示
                        />
                        <Label
                          htmlFor={`column-${column}`}
                          className="text-sm cursor-pointer flex-1">
                          {column === 'timestamp' ? '时间' : column}
                          {column === 'timestamp' && (
                            <Badge variant="outline" className="ml-2 text-xs">
                              固定
                            </Badge>
                          )}
                        </Label>
                      </div>
                    ))}
                  </div>
                </ScrollArea>
              </div>
            </PopoverContent>
          </Popover>
        )}

        {/* 导出功能 */}
        <Popover open={showExportOptions} onOpenChange={setShowExportOptions}>
          <PopoverTrigger asChild>
            <Button variant="outline" size="sm">
              <Download className="h-4 w-4 mr-2" />
              导出
            </Button>
          </PopoverTrigger>
          <PopoverContent className="w-80" align="end">
            <div className="space-y-4">
              <div>
                <Label className="text-sm font-medium">导出格式</Label>
                <p className="text-xs text-muted-foreground mt-1">
                  选择要导出的数据格式
                </p>
              </div>

              <div className="space-y-2">
                {exportFormats.map(
                  ({ value, label, icon: Icon, description }) => (
                    <div key={value} className="space-y-2">
                      <Button
                        variant="outline"
                        className="w-full justify-start"
                        onClick={() => handleExport(value as any)}>
                        <Icon className="h-4 w-4 mr-2" />
                        <div className="text-left">
                          <div className="font-medium">{label}</div>
                          <div className="text-xs text-muted-foreground">
                            {description}
                          </div>
                        </div>
                      </Button>

                      {/* Excel可以包含图表 */}
                      {value === 'excel' && viewMode === 'chart' && (
                        <Button
                          variant="outline"
                          size="sm"
                          className="w-full"
                          onClick={() => handleExport('excel', true)}>
                          <FileSpreadsheet className="h-4 w-4 mr-2" />
                          Excel + 图表
                        </Button>
                      )}
                    </div>
                  )
                )}
              </div>
            </div>
          </PopoverContent>
        </Popover>

        {/* 截屏功能 */}
        <Popover
          open={showScreenshotOptions}
          onOpenChange={setShowScreenshotOptions}>
          <PopoverTrigger asChild>
            <Button variant="outline" size="sm">
              <Camera className="h-4 w-4 mr-2" />
              截屏
            </Button>
          </PopoverTrigger>
          <PopoverContent className="w-64" align="end">
            <div className="space-y-4">
              <div>
                <Label className="text-sm font-medium">截屏选项</Label>
                <p className="text-xs text-muted-foreground mt-1">
                  选择要截取的内容
                </p>
              </div>

              <div className="space-y-2">
                {screenshotTargets.map(({ value, label, description }) => (
                  <div key={value} className="space-y-1">
                    <Button
                      variant="outline"
                      className="w-full justify-start"
                      onClick={() => handleScreenshot(value as any)}>
                      <Image className="h-4 w-4 mr-2" />
                      <div className="text-left">
                        <div className="font-medium">{label}</div>
                        <div className="text-xs text-muted-foreground">
                          {description}
                        </div>
                      </div>
                    </Button>
                  </div>
                ))}
              </div>
            </div>
          </PopoverContent>
        </Popover>

        {/* 设置按钮 */}
        <Button variant="outline" size="sm">
          <Settings className="h-4 w-4" />
        </Button>
      </div>
    </div>
  )
}
