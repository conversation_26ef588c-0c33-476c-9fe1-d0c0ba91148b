import React, { useRef, useEffect, useState } from 'react'
import { Card, CardContent, CardHeader, CardTitle } from '@/components/ui/card'
import { Button } from '@/components/ui/button'
import { Badge } from '@/components/ui/badge'
import {
  Select,
  SelectContent,
  SelectItem,
  SelectTrigger,
  SelectValue,
} from '@/components/ui/select'
import { Loader2, Download, Maximize2, Settings } from 'lucide-react'
import { toast } from '@/components/ui/use-toast'
import type {
  HistoryDataChartProps,
  HistoryDataPoint,
  ChartType,
} from './types'

export function HistoryDataChart({
  data,
  selectedTags,
  tagDisplayNames,
  chartType,
  loading,
  deviceName,
  onExportChart,
}: HistoryDataChartProps) {
  const canvasRef = useRef<HTMLCanvasElement>(null)
  const chartInstanceRef = useRef<any>(null)
  const [isChartLoading, setIsChartLoading] = useState(false)
  const [chartError, setChartError] = useState<string | null>(null)

  // 生成图表颜色
  const generateColors = (count: number) => {
    const colors = [
      'rgba(59, 130, 246, 0.8)', // blue
      'rgba(16, 185, 129, 0.8)', // green
      'rgba(245, 101, 101, 0.8)', // red
      'rgba(251, 191, 36, 0.8)', // yellow
      'rgba(139, 92, 246, 0.8)', // purple
      'rgba(236, 72, 153, 0.8)', // pink
      'rgba(34, 197, 94, 0.8)', // emerald
      'rgba(249, 115, 22, 0.8)', // orange
      'rgba(168, 85, 247, 0.8)', // violet
      'rgba(14, 165, 233, 0.8)', // sky
    ]

    return Array.from({ length: count }, (_, i: number) => ({
      border: colors[i % colors.length],
      background: colors[i % colors.length].replace('0.8', '0.2'),
      point: colors[i % colors.length].replace('0.8', '0.9'),
    }))
  }

  // 准备图表数据
  const prepareChartData = () => {
    if (import.meta.env.DEV) {
      console.log('Preparing chart data:', {
        dataLength: data?.length || 0,
        selectedTags,
        tagDisplayNames,
        sampleData: data?.[0],
      })
    }

    if (!data || data.length === 0 || selectedTags.length === 0) {
      if (import.meta.env.DEV) {
        console.log('No data or tags available for chart')
      }
      return { datasets: [] }
    }

    const colors = generateColors(selectedTags.length)

    const datasets = selectedTags.map((tagName, index) => {
      if (import.meta.env.DEV) {
        console.log(`Processing tag: ${tagName}`)
      }

      const tagData = data
        .map((point) => {
          const value = point[tagName]
          if (import.meta.env.DEV) {
            console.log(`Point for ${tagName}:`, {
              timestamp: point.timestamp,
              value,
              type: typeof value,
            })
          }

          // 尝试将值转换为数字（如果是字符串数字）
          let numericValue = value
          if (typeof value === 'string' && !isNaN(Number(value))) {
            numericValue = Number(value)
          }

          return {
            x: point.timestamp,
            y: numericValue,
          }
        })
        .filter((point) => {
          const isValid =
            point.y !== null &&
            point.y !== undefined &&
            !isNaN(Number(point.y)) &&
            isFinite(Number(point.y))
          if (import.meta.env.DEV) {
            console.log(
              `Filtering point for ${tagName}:`,
              point,
              'valid:',
              isValid
            )
          }
          return isValid
        })

      if (import.meta.env.DEV) {
        console.log(`Filtered data for ${tagName}:`, tagData.length, 'points')
      }

      const color = colors[index]

      const baseConfig = {
        label: tagDisplayNames?.[tagName] || tagName,
        data: tagData,
        borderColor: color.border,
        backgroundColor: color.background,
        pointBackgroundColor: color.point,
        pointBorderColor: '#fff',
        pointBorderWidth: 2,
        pointRadius: chartType === 'line' ? 3 : 4,
        pointHoverRadius: 6,
        borderWidth: 2,
        tension: chartType === 'line' ? 0.3 : 0,
      }

      // 根据图表类型调整配置
      switch (chartType) {
        case 'area':
          return {
            ...baseConfig,
            type: 'line' as const,
            fill: true,
            backgroundColor: color.background,
          }
        case 'bar':
          return {
            ...baseConfig,
            type: 'bar' as const,
            borderWidth: 1,
          }
        case 'scatter':
          return {
            ...baseConfig,
            type: 'scatter' as const,
            showLine: false,
            pointRadius: 5,
          }
        default: // line
          return {
            ...baseConfig,
            type: 'line' as const,
          }
      }
    })

    if (import.meta.env.DEV) {
      console.log('Final chart datasets:', datasets)
    }
    return { datasets }
  }

  // 获取图表选项
  const getChartOptions = () => {
    return {
      responsive: true,
      maintainAspectRatio: false,
      interaction: {
        mode: 'index' as const,
        intersect: false,
      },
      scales: {
        x: {
          type: 'time' as const,
          time: {
            unit: getTimeUnit(),
            displayFormats: {
              minute: 'HH:mm',
              hour: 'MM-dd HH:mm',
              day: 'MM-dd',
            },
          },
          title: {
            display: true,
            text: '时间',
            color: '#6b7280',
            font: { size: 12 },
          },
          grid: {
            color: 'rgba(107, 114, 128, 0.1)',
          },
        },
        y: {
          beginAtZero: false,
          title: {
            display: true,
            text: '历史数据',
            color: '#6b7280',
            font: { size: 12 },
          },
          grid: {
            color: 'rgba(107, 114, 128, 0.1)',
          },
        },
      },
      plugins: {
        tooltip: {
          backgroundColor: 'rgba(0, 0, 0, 0.8)',
          titleColor: '#fff',
          bodyColor: '#fff',
          borderColor: 'rgba(107, 114, 128, 0.2)',
          borderWidth: 1,
          cornerRadius: 8,
          displayColors: true,
          callbacks: {
            title: (context: any) => {
              const date = new Date(context[0].parsed.x)
              return date.toLocaleString('zh-CN')
            },
            label: (context: any) => {
              return `${context.dataset.label}: ${context.parsed.y}`
            },
          },
        },
        legend: {
          position: 'top' as const,
          labels: {
            usePointStyle: true,
            pointStyle: 'circle' as const,
            padding: 20,
            color: '#374151',
          },
        },
        title: {
          display: !!deviceName,
          text: deviceName ? `${deviceName} - 历史数据图表` : '',
          color: '#374151',
          font: { size: 16, weight: 'bold' as const },
          padding: 20,
        },
      },
      elements: {
        point: {
          hoverBackgroundColor: '#fff',
          hoverBorderWidth: 3,
        },
      },
      animation: {
        duration: 300,
      },
    }
  }

  // 根据数据范围确定时间单位
  const getTimeUnit = (): 'minute' | 'hour' | 'day' => {
    if (data.length < 2) return 'hour'

    const firstTimestamp = new Date(data[0].timestamp).getTime()
    const lastTimestamp = new Date(data[data.length - 1].timestamp).getTime()
    const diffHours = (lastTimestamp - firstTimestamp) / (1000 * 60 * 60)

    if (diffHours <= 6) return 'minute'
    if (diffHours <= 72) return 'hour'
    return 'day'
  }

  // 初始化图表
  useEffect(() => {
    if (
      !canvasRef.current ||
      loading ||
      data.length === 0 ||
      selectedTags.length === 0
    ) {
      if (import.meta.env.DEV) {
        console.log('Skipping chart initialization:', {
          hasCanvas: !!canvasRef.current,
          loading,
          dataLength: data.length,
          selectedTagsLength: selectedTags.length,
        })
      }
      return
    }

    const initChart = async () => {
      try {
        setIsChartLoading(true)
        setChartError(null)

        if (import.meta.env.DEV) {
          console.log('Starting chart initialization...')
        }

        // 动态导入Chart.js
        const {
          Chart,
          LineController,
          BarController,
          LineElement,
          BarElement,
          PointElement,
          LinearScale,
          TimeScale,
          CategoryScale,
          Tooltip,
          Legend,
          Title,
          Filler,
        } = await import('chart.js')

        // 导入日期适配器
        await import('chartjs-adapter-date-fns')

        if (import.meta.env.DEV) {
          console.log('Chart.js modules loaded successfully')
        }

        // 注册必要的组件
        Chart.register(
          LineController,
          BarController,
          LineElement,
          BarElement,
          PointElement,
          LinearScale,
          TimeScale,
          CategoryScale,
          Tooltip,
          Legend,
          Title,
          Filler
        )

        // 销毁旧图表
        if (chartInstanceRef.current) {
          chartInstanceRef.current.destroy()
        }

        // 创建新图表 - 添加null检查
        if (!canvasRef.current) {
          console.error('Canvas element is null')
          return
        }

        const ctx = canvasRef.current.getContext('2d')
        if (!ctx) {
          console.error('Failed to get 2d context from canvas')
          return
        }

        const chartData = prepareChartData()
        const chartOptions = getChartOptions()

        if (import.meta.env.DEV) {
          console.log('Creating chart with data:', chartData)
          console.log('Chart options:', chartOptions)
        }

        // 检查是否有有效的数据集
        if (!chartData.datasets || chartData.datasets.length === 0) {
          console.warn('No valid datasets for chart')
          setChartError('没有可显示的数据')
          return
        }

        // 检查每个数据集是否有数据点
        const hasValidData = chartData.datasets.some(
          (dataset) =>
            dataset.data &&
            Array.isArray(dataset.data) &&
            dataset.data.length > 0
        )

        if (!hasValidData) {
          console.warn('No valid data points in any dataset')
          setChartError('选择的点位没有有效的数据')
          return
        }

        chartInstanceRef.current = new Chart(ctx, {
          type: chartType === 'bar' ? 'bar' : 'line',
          data: chartData as any, // 临时类型断言以解决复杂的Chart.js类型问题
          options: chartOptions as any, // 临时类型断言以解决复杂的Chart.js类型问题
        })

        if (import.meta.env.DEV) {
          console.log('Chart created successfully')
        }
      } catch (error) {
        console.error('Failed to load chart:', error)
        setChartError('图表加载失败')
        toast({
          title: '图表加载失败',
          description: '无法加载Chart.js库',
          variant: 'destructive',
        })
      } finally {
        setIsChartLoading(false)
      }
    }

    initChart()

    return () => {
      if (chartInstanceRef.current) {
        chartInstanceRef.current.destroy()
        chartInstanceRef.current = null
      }
    }
  }, [data, selectedTags, chartType, loading, deviceName])

  // 导出图表
  const handleExportChart = () => {
    if (!chartInstanceRef.current) {
      toast({
        title: '导出失败',
        description: '当前没有可导出的图表',
        variant: 'destructive',
      })
      return
    }

    try {
      const link = document.createElement('a')
      link.download = `历史数据图表_${
        new Date().toISOString().split('T')[0]
      }.png`
      link.href = chartInstanceRef.current.toBase64Image()
      document.body.appendChild(link)
      link.click()
      document.body.removeChild(link)

      toast({
        title: '导出成功',
        description: '图表已导出为PNG文件',
      })

      onExportChart?.()
    } catch (error) {
      console.error('Export failed:', error)
      toast({
        title: '导出失败',
        description: '无法导出图表文件',
        variant: 'destructive',
      })
    }
  }

  // 全屏显示图表
  const handleFullscreen = () => {
    if (canvasRef.current) {
      canvasRef.current.requestFullscreen?.()
    }
  }

  if (loading || isChartLoading) {
    return (
      <Card>
        <CardContent className="flex items-center justify-center py-12">
          <div className="text-center">
            <Loader2 className="h-8 w-8 animate-spin mx-auto mb-4 text-muted-foreground" />
            <p className="text-muted-foreground">正在加载图表...</p>
          </div>
        </CardContent>
      </Card>
    )
  }

  if (chartError) {
    return (
      <Card>
        <CardContent className="flex items-center justify-center py-12">
          <div className="text-center">
            <div className="text-red-500 mb-4">
              <svg
                className="w-16 h-16 mx-auto mb-4"
                fill="none"
                stroke="currentColor"
                viewBox="0 0 24 24">
                <path
                  strokeLinecap="round"
                  strokeLinejoin="round"
                  strokeWidth={1}
                  d="M12 8v4m0 4h.01M21 12a9 9 0 11-18 0 9 9 0 0118 0z"
                />
              </svg>
              <h3 className="text-lg font-medium text-gray-900 mb-2">
                图表渲染失败
              </h3>
              <p className="text-sm text-gray-600 mb-4">{chartError}</p>
            </div>

            <div className="space-y-2">
              <Button
                variant="outline"
                onClick={() => {
                  setChartError(null)
                  // 重新触发图表初始化
                  setTimeout(() => {
                    if (canvasRef.current) {
                      const event = new Event('resize')
                      window.dispatchEvent(event)
                    }
                  }, 100)
                }}>
                重新加载图表
              </Button>

              {import.meta.env.DEV && (
                <div className="mt-4 p-3 bg-gray-100 rounded text-left text-xs">
                  <div className="font-medium mb-1">调试信息:</div>
                  <div>数据点数: {data.length}</div>
                  <div>选择的标签: {selectedTags.join(', ')}</div>
                  <div>标签显示名映射: {JSON.stringify(tagDisplayNames)}</div>
                </div>
              )}
            </div>
          </div>
        </CardContent>
      </Card>
    )
  }

  if (data.length === 0) {
    return (
      <Card>
        <CardContent className="flex items-center justify-center py-12">
          <div className="text-center text-muted-foreground">
            <div className="text-6xl mb-4">📊</div>
            <h3 className="text-lg font-medium mb-2">暂无数据</h3>
            <p className="text-sm">请调整查询条件后重新查询</p>
          </div>
        </CardContent>
      </Card>
    )
  }

  if (selectedTags.length === 0) {
    return (
      <Card>
        <CardContent className="flex items-center justify-center py-12">
          <div className="text-center text-muted-foreground">
            <div className="text-6xl mb-4">🎯</div>
            <h3 className="text-lg font-medium mb-2">请选择要显示的点位</h3>
            <p className="text-sm">至少选择一个点位来生成图表</p>
          </div>
        </CardContent>
      </Card>
    )
  }

  return (
    <Card>
      <CardHeader>
        <CardTitle className="flex items-center justify-between">
          <div className="flex items-center gap-2">
            <span>历史数据图表</span>
            <Badge variant="outline">{selectedTags.length} 个数据系列</Badge>
          </div>

          <div className="flex items-center gap-2">
            <Button variant="outline" size="sm" onClick={handleExportChart}>
              <Download className="h-4 w-4 mr-2" />
              导出图表
            </Button>
            <Button variant="outline" size="sm" onClick={handleFullscreen}>
              <Maximize2 className="h-4 w-4" />
            </Button>
          </div>
        </CardTitle>
      </CardHeader>

      <CardContent>
        <div className="h-[500px] w-full">
          <canvas ref={canvasRef} className="w-full h-full" />
        </div>

        {/* 图表说明 */}
        <div className="mt-4 pt-4 border-t">
          <div className="flex flex-wrap gap-2">
            {selectedTags.map((tagName, index) => {
              const colors = generateColors(selectedTags.length)
              const color = colors[index]
              return (
                <div key={tagName} className="flex items-center gap-2 text-sm">
                  <div
                    className="w-3 h-3 rounded-full"
                    style={{ backgroundColor: color.border }}
                  />
                  <span>{tagDisplayNames?.[tagName] || tagName}</span>
                </div>
              )
            })}
          </div>

          <div className="mt-2 text-xs text-muted-foreground">
            数据点数: {data.length} | 时间范围:{' '}
            {data.length > 0 && (
              <>
                {new Date(data[0].timestamp).toLocaleString('zh-CN')} 至{' '}
                {new Date(data[data.length - 1].timestamp).toLocaleString(
                  'zh-CN'
                )}
              </>
            )}
          </div>
        </div>
      </CardContent>
    </Card>
  )
}
