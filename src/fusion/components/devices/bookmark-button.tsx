import { useState } from 'react'
import { But<PERSON> } from '@/components/ui/button'
import { Star } from 'lucide-react'
import { cn } from '@/lib/utils'
import { BookmarkService } from '@/lib/services/bookmark-service'
import { toast } from '@/components/ui/use-toast'
import { type DeviceTag } from '@/lib/api/tag-api'

interface BookmarkButtonProps {
  tag: DeviceTag
  deviceName?: string
  size?: 'sm' | 'md' | 'lg'
  variant?: 'ghost' | 'outline' | 'default'
  showText?: boolean
  onBookmarkChange?: (tagId: number, isBookmarked: boolean) => void
  className?: string
}

export function BookmarkButton({
  tag,
  deviceName,
  size = 'sm',
  variant = 'ghost',
  showText = false,
  onBookmarkChange,
  className,
}: BookmarkButtonProps) {
  const [isBookmarked, setIsBookmarked] = useState(tag.isBookmarked || false)
  const [isLoading, setIsLoading] = useState(false)

  const handleToggleBookmark = async (e: React.MouseEvent) => {
    e.preventDefault()
    e.stopPropagation()

    setIsLoading(true)

    try {
      const success = BookmarkService.toggleBookmark(
        tag.id,
        tag.deviceId,
        tag.name,
        deviceName
      )

      if (success) {
        const newBookmarkState = !isBookmarked
        setIsBookmarked(newBookmarkState)

        // 调用回调函数
        onBookmarkChange?.(tag.id, newBookmarkState)

        // 显示提示
        toast({
          title: newBookmarkState ? '已标记' : '已取消标记',
          description: `点位 "${tag.name}" ${
            newBookmarkState ? '已添加到标记列表' : '已从标记列表移除'
          }`,
          variant: 'success',
        })
      } else {
        toast({
          title: '操作失败',
          description: '无法更新标记状态，请重试',
          variant: 'destructive',
        })
      }
    } catch (error) {
      console.error('标记操作失败:', error)
      toast({
        title: '操作失败',
        description: '标记操作失败，请重试',
        variant: 'destructive',
      })
    } finally {
      setIsLoading(false)
    }
  }

  const getButtonSize = () => {
    switch (size) {
      case 'sm':
        return 'h-8 w-8'
      case 'md':
        return 'h-9 w-9'
      case 'lg':
        return 'h-10 w-10'
      default:
        return 'h-8 w-8'
    }
  }

  const getIconSize = () => {
    switch (size) {
      case 'sm':
        return 'h-3 w-3'
      case 'md':
        return 'h-4 w-4'
      case 'lg':
        return 'h-5 w-5'
      default:
        return 'h-3 w-3'
    }
  }

  // Map custom sizes to Button component sizes
  const getButtonComponentSize = () => {
    switch (size) {
      case 'sm':
        return 'sm'
      case 'md':
        return 'default'
      case 'lg':
        return 'lg'
      default:
        return 'sm'
    }
  }

  if (showText) {
    return (
      <Button
        variant={variant}
        size={getButtonComponentSize()}
        onClick={handleToggleBookmark}
        disabled={isLoading}
        className={cn(
          'flex items-center gap-2',
          isBookmarked && 'text-amber-600 hover:text-amber-700',
          className
        )}>
        <Star
          className={cn(
            getIconSize(),
            isBookmarked ? 'fill-amber-400 text-amber-400' : 'text-gray-400'
          )}
        />
        {isBookmarked ? '已标记' : '标记'}
      </Button>
    )
  }

  return (
    <Button
      variant={variant}
      size="icon"
      onClick={handleToggleBookmark}
      disabled={isLoading}
      className={cn(
        getButtonSize(),
        isBookmarked && 'text-amber-600 hover:text-amber-700',
        className
      )}
      title={isBookmarked ? '取消标记' : '标记点位'}>
      <Star
        className={cn(
          getIconSize(),
          isBookmarked ? 'fill-amber-400 text-amber-400' : 'text-gray-400'
        )}
      />
    </Button>
  )
}

// 批量标记按钮组件
interface BatchBookmarkButtonProps {
  selectedTags: DeviceTag[]
  deviceName?: string
  onBookmarkChange?: (tagIds: number[], isBookmarked: boolean) => void
  className?: string
}

export function BatchBookmarkButton({
  selectedTags,
  deviceName,
  onBookmarkChange,
  className,
}: BatchBookmarkButtonProps) {
  const [isLoading, setIsLoading] = useState(false)

  // 检查是否所有选中的点位都已标记
  const allBookmarked = selectedTags.every((tag) => tag.isBookmarked)
  const someBookmarked = selectedTags.some((tag) => tag.isBookmarked)

  const handleBatchBookmark = async (bookmark: boolean) => {
    if (selectedTags.length === 0) return

    setIsLoading(true)

    try {
      const tagIds = selectedTags.map((tag) => tag.id)
      let success = false

      if (bookmark) {
        // 批量添加标记
        const tagsToBookmark = selectedTags.map((tag) => ({
          tagId: tag.id,
          deviceId: tag.deviceId,
          tagName: tag.name,
          deviceName,
        }))
        success = BookmarkService.addMultipleBookmarks(tagsToBookmark)
      } else {
        // 批量移除标记
        success = BookmarkService.removeMultipleBookmarks(tagIds)
      }

      if (success) {
        onBookmarkChange?.(tagIds, bookmark)

        toast({
          title: bookmark ? '批量标记成功' : '批量取消标记成功',
          description: `已${bookmark ? '标记' : '取消标记'} ${
            selectedTags.length
          } 个点位`,
          variant: 'success',
        })
      } else {
        toast({
          title: '操作失败',
          description: '批量标记操作失败，请重试',
          variant: 'destructive',
        })
      }
    } catch (error) {
      console.error('批量标记操作失败:', error)
      toast({
        title: '操作失败',
        description: '批量标记操作失败，请重试',
        variant: 'destructive',
      })
    } finally {
      setIsLoading(false)
    }
  }

  return (
    <div className={cn('flex items-center gap-2', className)}>
      {!allBookmarked && (
        <Button
          variant="outline"
          size="sm"
          onClick={() => handleBatchBookmark(true)}
          disabled={isLoading || selectedTags.length === 0}
          className="bg-white">
          <Star className="mr-2 h-4 w-4 text-amber-500" />
          批量标记
        </Button>
      )}

      {someBookmarked && (
        <Button
          variant="outline"
          size="sm"
          onClick={() => handleBatchBookmark(false)}
          disabled={isLoading || selectedTags.length === 0}
          className="bg-white">
          <Star className="mr-2 h-4 w-4 fill-amber-400 text-amber-400" />
          取消标记
        </Button>
      )}
    </div>
  )
}
