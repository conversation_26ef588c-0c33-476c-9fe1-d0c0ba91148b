import { useState, useEffect } from 'react'
import {
  <PERSON><PERSON>,
  <PERSON>alog<PERSON>ontent,
  <PERSON>alog<PERSON>eader,
  <PERSON>alog<PERSON><PERSON><PERSON>,
  DialogFooter,
} from '@/components/ui/dialog'
import { Button } from '@/components/ui/button'
import { Input } from '@/components/ui/input'
import { Label } from '@/components/ui/label'
import {
  Select,
  SelectContent,
  SelectItem,
  SelectTrigger,
  SelectValue,
} from '@/components/ui/select'
import { <PERSON>bs, TabsContent, TabsList, TabsTrigger } from '@/components/ui/tabs'
import { TagService, type DeviceTag } from '@/lib/api/tag-api'
import { toast } from '@/components/ui/use-toast'
import { Checkbox } from '@/components/ui/checkbox'
import { Separator } from '@/components/ui/separator'
import { Edit, Check } from 'lucide-react'

interface BatchTagDialogProps {
  open: boolean
  onOpenChange: (open: boolean) => void
  deviceId: string | number
  tagsToEdit: DeviceTag[]
  onTagsUpdated?: (tags: DeviceTag[]) => void
}

export function BatchTagDialog({
  open,
  onOpenChange,
  deviceId,
  tagsToEdit,
  onTagsUpdated,
}: BatchTagDialogProps) {
  const [isSubmitting, setIsSubmitting] = useState(false)
  const [activeTab, setActiveTab] = useState('basic')

  // 控制哪些字段将被批量编辑
  const [fieldsToUpdate, setFieldsToUpdate] = useState<Record<string, boolean>>(
    {
      // 基本信息
      name: false,
      description: false,
      unit: false,
      tags: false,

      // 数据配置
      transitionType: false,
      dataType: false,
      protectType: false,
      sendType: false,

      // 采集配置
      period: false,
      uploadInterval: false,
      archiveTime: false,
      length: false,

      // 驱动配置
      encoding: false,
      method: false,
      registerAddress: false,
      readLength: false,

      // 处理脚本
      content: false,
    }
  )

  // 表单字段值
  const [formData, setFormData] = useState({
    // 基本信息
    name: '',
    description: '',
    unit: '',
    tags: [] as string[],

    // 数据配置
    transitionType: 'Number',
    dataType: 'int16',
    protectType: 'OnlyRead',
    sendType: 'Always',

    // 采集配置
    period: 1000,
    uploadInterval: 60,
    archiveTime: 3600,
    length: 2,

    // 驱动配置
    encoding: 'utf8',
    method: 'read',
    registerAddress: '',
    readLength: 1,

    // 处理脚本
    content: '',
  })

  // 重置表单
  useEffect(() => {
    if (!open) {
      // 关闭时重置表单
      resetForm()
    } else if (tagsToEdit.length > 0) {
      // 填充表单默认值为第一个标签的值
      initializeFormValues()
    }
  }, [open, tagsToEdit])

  // 重置表单函数
  const resetForm = () => {
    setFormData({
      description: '',
      dataType: 'Number',
      accessRight: '只读',
      uploadMethod: '变化上报',
      unit: '',
      decimalPlaces: 2,
      min: 0,
      max: 100,
    })

    // 重置待更新字段
    setFieldsToUpdate({
      description: false,
      dataType: false,
      accessRight: false,
      uploadMethod: false,
      unit: false,
      decimalPlaces: false,
      min: false,
      max: false,
    })

    setActiveTab('basic')
  }

  // 初始化表单值为第一个标签的值
  const initializeFormValues = () => {
    if (tagsToEdit.length === 0) return

    const firstTag = tagsToEdit[0]

    setFormData({
      // 基本信息
      name: firstTag.name || '',
      description: firstTag.description || '',
      unit: firstTag.unit || '',
      tags: firstTag.tags || [],

      // 数据配置
      transitionType: firstTag.transitionType || 'Number',
      dataType: firstTag.dataType || 'int16',
      protectType: firstTag.protectType || 'OnlyRead',
      sendType: firstTag.sendType || 'Always',

      // 采集配置
      period: firstTag.period !== undefined ? firstTag.period : 1000,
      uploadInterval: firstTag.uploadInterval !== undefined ? firstTag.uploadInterval : 60,
      archiveTime: firstTag.archiveTime !== undefined ? firstTag.archiveTime : 3600,
      length: firstTag.length !== undefined ? firstTag.length : 2,

      // 驱动配置
      encoding: firstTag.encoding || 'utf8',
      method: firstTag.method || 'read',
      registerAddress: firstTag.registerAddress || '',
      readLength: firstTag.readLength !== undefined ? firstTag.readLength : 1,

      // 处理脚本
      content: firstTag.content || '',
    })
  }

  // 处理表单字段变更
  const handleChange = (field: string, value: any) => {
    setFormData((prev) => ({
      ...prev,
      [field]: value,
    }))
  }

  // 处理待更新字段选择变更
  const handleFieldCheckChange = (field: string, checked: boolean) => {
    setFieldsToUpdate((prev) => ({
      ...prev,
      [field]: checked,
    }))
  }

  // 提交表单处理批量更新
  const handleSubmit = async () => {
    // 检查是否有选择任何字段进行更新
    if (!Object.values(fieldsToUpdate).some((value) => value)) {
      toast({
        title: '请选择要更新的字段',
        description: '请至少选择一个字段进行批量更新',
        variant: 'destructive',
      })
      return
    }

    setIsSubmitting(true)

    try {
      // 构建批量编辑的字段数据
      const editInputs = tagsToEdit.map((tag) => {
        const fields: Record<string, any> = {}

        // 只包含选中要更新的字段
        if (fieldsToUpdate.name) fields.name = formData.name
        if (fieldsToUpdate.description) fields.description = formData.description
        if (fieldsToUpdate.unit) fields.unit = formData.unit
        if (fieldsToUpdate.tags) fields.tags = formData.tags

        if (fieldsToUpdate.transitionType) fields.transitionType = formData.transitionType
        if (fieldsToUpdate.dataType) fields.dataType = formData.dataType
        if (fieldsToUpdate.protectType) fields.protectType = formData.protectType
        if (fieldsToUpdate.sendType) fields.sendType = formData.sendType

        if (fieldsToUpdate.period) fields.period = formData.period
        if (fieldsToUpdate.uploadInterval) fields.uploadInterval = formData.uploadInterval
        if (fieldsToUpdate.archiveTime) fields.archiveTime = formData.archiveTime
        if (fieldsToUpdate.length) fields.length = formData.length

        if (fieldsToUpdate.encoding) fields.encoding = formData.encoding
        if (fieldsToUpdate.method) fields.method = formData.method
        if (fieldsToUpdate.registerAddress) fields.registerAddress = formData.registerAddress
        if (fieldsToUpdate.readLength) fields.readLength = formData.readLength

        if (fieldsToUpdate.content) fields.content = formData.content

        return {
          id: tag.id,
          fields: fields
        }
      })

      console.log('批量编辑数据:', editInputs)

      // 调用批量编辑字段接口
      const success = await TagService.batchEditFields(deviceId, editInputs)

      if (success) {
        toast({
          title: '批量更新成功',
          description: `已更新 ${tagsToEdit.length} 个点位`,
        })

        // 如果提供了回调函数，调用它
        if (onTagsUpdated) {
          // 构建更新后的标签数据用于回调
          const updatedTags = tagsToEdit.map((tag, index) => ({
            ...tag,
            ...editInputs[index].fields
          }))
          onTagsUpdated(updatedTags)
        }

        // 关闭对话框
        onOpenChange(false)
      } else {
        toast({
          title: '更新失败',
          description: '无法更新点位，请重试',
          variant: 'destructive',
        })
      }
    } catch (error) {
      console.error('批量更新点位失败', error)
      toast({
        title: '更新失败',
        description: '无法更新点位，请重试',
        variant: 'destructive',
      })
    } finally {
      setIsSubmitting(false)
    }
  }

  return (
    <Dialog open={open} onOpenChange={onOpenChange}>
      <DialogContent className="sm:max-w-[600px] max-h-[90vh] overflow-y-auto">
        <DialogHeader>
          <DialogTitle className="flex items-center text-lg">
            <span className="p-1 rounded-full bg-blue-100 mr-2 flex items-center justify-center">
              <Edit className="h-4 w-4 text-blue-600" />
            </span>
            批量编辑点位
          </DialogTitle>
        </DialogHeader>

        <div className="py-4">
          <div className="bg-amber-50 border border-amber-200 rounded-md p-3 mb-4">
            <div className="flex items-start">
              <div className="text-amber-600 mr-2 mt-0.5">
                <svg
                  xmlns="http://www.w3.org/2000/svg"
                  width="16"
                  height="16"
                  viewBox="0 0 24 24"
                  fill="none"
                  stroke="currentColor"
                  strokeWidth="2"
                  strokeLinecap="round"
                  strokeLinejoin="round">
                  <path d="m21.73 18-8-14a2 2 0 0 0-3.48 0l-8 14A2 2 0 0 0 4 21h16a2 2 0 0 0 1.73-3Z"></path>
                  <path d="M12 9v4"></path>
                  <path d="M12 17h.01"></path>
                </svg>
              </div>
              <div className="text-sm text-amber-800">
                <span className="font-medium block mb-1">批量编辑说明</span>
                <ol className="list-decimal ml-4 space-y-1">
                  <li>勾选您想要修改的属性对应的复选框</li>
                  <li>设置新的值</li>
                  <li>点击"保存修改"按钮应用更改</li>
                </ol>
                <p className="mt-1 italic">
                  注意：只有勾选的属性会被修改，未勾选的属性保持不变
                </p>
              </div>
            </div>
          </div>

          <p className="text-sm text-muted-foreground mb-4 flex items-center">
            <span className="bg-blue-100 text-blue-800 font-medium px-2 py-0.5 rounded text-xs mr-2">
              {tagsToEdit.length} 个点位
            </span>
            请选择要批量修改的属性并设置新值
          </p>

          <Tabs value={activeTab} onValueChange={setActiveTab}>
            <TabsList className="mb-4 w-full">
              <TabsTrigger value="basic" className="flex-1">
                基本信息
              </TabsTrigger>
              <TabsTrigger value="data" className="flex-1">
                数据配置
              </TabsTrigger>
              <TabsTrigger value="collection" className="flex-1">
                采集配置
              </TabsTrigger>
              <TabsTrigger value="driver" className="flex-1">
                驱动配置
              </TabsTrigger>
            </TabsList>

            <TabsContent value="basic" className="space-y-4">
              {/* 点位名称 */}
              <div className="flex items-start space-x-4 p-3 rounded-md border hover:bg-gray-50 transition-colors">
                <Checkbox
                  id="update-name"
                  checked={fieldsToUpdate.name}
                  onCheckedChange={(checked) =>
                    handleFieldCheckChange('name', !!checked)
                  }
                  className="mt-1"
                />
                <div className="grid w-full gap-1.5">
                  <Label htmlFor="name" className="text-sm font-medium">
                    点位名称
                  </Label>
                  <Input
                    id="name"
                    placeholder="点位名称"
                    value={formData.name}
                    onChange={(e) => handleChange('name', e.target.value)}
                    disabled={!fieldsToUpdate.name}
                    className={
                      fieldsToUpdate.name
                        ? 'border-blue-300 focus-visible:ring-blue-300'
                        : ''
                    }
                  />
                </div>
              </div>

              {/* 描述 */}
              <div className="flex items-start space-x-4 p-3 rounded-md border hover:bg-gray-50 transition-colors">
                <Checkbox
                  id="update-description"
                  checked={fieldsToUpdate.description}
                  onCheckedChange={(checked) =>
                    handleFieldCheckChange('description', !!checked)
                  }
                  className="mt-1"
                />
                <div className="grid w-full gap-1.5">
                  <Label htmlFor="description" className="text-sm font-medium">
                    描述
                  </Label>
                  <Input
                    id="description"
                    placeholder="点位描述"
                    value={formData.description}
                    onChange={(e) =>
                      handleChange('description', e.target.value)
                    }
                    disabled={!fieldsToUpdate.description}
                    className={
                      fieldsToUpdate.description
                        ? 'border-blue-300 focus-visible:ring-blue-300'
                        : ''
                    }
                  />
                </div>
              </div>

              {/* 单位 */}
              <div className="flex items-start space-x-4 p-3 rounded-md border hover:bg-gray-50 transition-colors">
                <Checkbox
                  id="update-unit"
                  checked={fieldsToUpdate.unit}
                  onCheckedChange={(checked) =>
                    handleFieldCheckChange('unit', !!checked)
                  }
                  className="mt-1"
                />
                <div className="grid w-full gap-1.5">
                  <Label htmlFor="unit" className="text-sm font-medium">
                    单位
                  </Label>
                  <Input
                    id="unit"
                    placeholder="例如: °C, kg, m³"
                    value={formData.unit}
                    onChange={(e) => handleChange('unit', e.target.value)}
                    disabled={!fieldsToUpdate.unit}
                    className={
                      fieldsToUpdate.unit
                        ? 'border-blue-300 focus-visible:ring-blue-300'
                        : ''
                    }
                  />
                </div>
              </div>

              {/* 标签 */}
              <div className="flex items-start space-x-4 p-3 rounded-md border hover:bg-gray-50 transition-colors">
                <Checkbox
                  id="update-tags"
                  checked={fieldsToUpdate.tags}
                  onCheckedChange={(checked) =>
                    handleFieldCheckChange('tags', !!checked)
                  }
                  className="mt-1"
                />
                <div className="grid w-full gap-1.5">
                  <Label htmlFor="tags" className="text-sm font-medium">
                    标签 (用逗号分隔)
                  </Label>
                  <Input
                    id="tags"
                    placeholder="例如: 温度,传感器,重要"
                    value={(formData.tags || []).join(', ')}
                    onChange={(e) =>
                      handleChange('tags', e.target.value.split(',').map(tag => tag.trim()).filter(tag => tag))
                    }
                    disabled={!fieldsToUpdate.tags}
                    className={
                      fieldsToUpdate.tags
                        ? 'border-blue-300 focus-visible:ring-blue-300'
                        : ''
                    }
                  />
                </div>
              </div>
            </TabsContent>

            <TabsContent value="data" className="space-y-4">
              {/* 数据转换类型 */}
              <div className="flex items-start space-x-4 p-3 rounded-md border hover:bg-gray-50 transition-colors">
                <Checkbox
                  id="update-transitionType"
                  checked={fieldsToUpdate.transitionType}
                  onCheckedChange={(checked) =>
                    handleFieldCheckChange('transitionType', !!checked)
                  }
                  className="mt-1"
                />
                <div className="grid w-full gap-1.5">
                  <Label htmlFor="transitionType" className="text-sm font-medium">
                    数据转换类型
                  </Label>
                  <Select
                    value={formData.transitionType}
                    onValueChange={(value) => handleChange('transitionType', value)}
                    disabled={!fieldsToUpdate.transitionType}>
                    <SelectTrigger id="transitionType">
                      <SelectValue placeholder="选择数据转换类型" />
                    </SelectTrigger>
                    <SelectContent>
                      <SelectItem value="Number">数值</SelectItem>
                      <SelectItem value="Integer">整数</SelectItem>
                      <SelectItem value="String">字符串</SelectItem>
                      <SelectItem value="Boolean">布尔值</SelectItem>
                    </SelectContent>
                  </Select>
                </div>
              </div>

              {/* 协议数据类型 */}
              <div className="flex items-start space-x-4 p-3 rounded-md border hover:bg-gray-50 transition-colors">
                <Checkbox
                  id="update-dataType"
                  checked={fieldsToUpdate.dataType}
                  onCheckedChange={(checked) =>
                    handleFieldCheckChange('dataType', !!checked)
                  }
                  className="mt-1"
                />
                <div className="grid w-full gap-1.5">
                  <Label htmlFor="dataType" className="text-sm font-medium">
                    协议数据类型
                  </Label>
                  <Select
                    value={formData.dataType}
                    onValueChange={(value) => handleChange('dataType', value)}
                    disabled={!fieldsToUpdate.dataType}>
                    <SelectTrigger id="dataType">
                      <SelectValue placeholder="选择协议数据类型" />
                    </SelectTrigger>
                    <SelectContent>
                      <SelectItem value="int16">int16</SelectItem>
                      <SelectItem value="uint16">uint16</SelectItem>
                      <SelectItem value="int32">int32</SelectItem>
                      <SelectItem value="uint32">uint32</SelectItem>
                      <SelectItem value="float">float</SelectItem>
                      <SelectItem value="double">double</SelectItem>
                      <SelectItem value="boolean">boolean</SelectItem>
                      <SelectItem value="string">string</SelectItem>
                    </SelectContent>
                  </Select>
                </div>
              </div>

              {/* 读写权限 */}
              <div className="flex items-start space-x-4 p-3 rounded-md border hover:bg-gray-50 transition-colors">
                <Checkbox
                  id="update-protectType"
                  checked={fieldsToUpdate.protectType}
                  onCheckedChange={(checked) =>
                    handleFieldCheckChange('protectType', !!checked)
                  }
                  className="mt-1"
                />
                <div className="grid w-full gap-1.5">
                  <Label htmlFor="protectType" className="text-sm font-medium">
                    读写权限
                  </Label>
                  <Select
                    value={formData.protectType}
                    onValueChange={(value) => handleChange('protectType', value)}
                    disabled={!fieldsToUpdate.protectType}>
                    <SelectTrigger id="protectType">
                      <SelectValue placeholder="选择读写权限" />
                    </SelectTrigger>
                    <SelectContent>
                      <SelectItem value="OnlyRead">只读</SelectItem>
                      <SelectItem value="OnlyWrite">只写</SelectItem>
                      <SelectItem value="ReadAndWrite">读写</SelectItem>
                    </SelectContent>
                  </Select>
                </div>
              </div>

              {/* 上报方式 */}
              <div className="flex items-start space-x-4 p-3 rounded-md border hover:bg-gray-50 transition-colors">
                <Checkbox
                  id="update-sendType"
                  checked={fieldsToUpdate.sendType}
                  onCheckedChange={(checked) =>
                    handleFieldCheckChange('sendType', !!checked)
                  }
                  className="mt-1"
                />
                <div className="grid w-full gap-1.5">
                  <Label htmlFor="sendType" className="text-sm font-medium">
                    上报方式
                  </Label>
                  <Select
                    value={formData.sendType}
                    onValueChange={(value) => handleChange('sendType', value)}
                    disabled={!fieldsToUpdate.sendType}>
                    <SelectTrigger id="sendType">
                      <SelectValue placeholder="选择上报方式" />
                    </SelectTrigger>
                    <SelectContent>
                      <SelectItem value="OnChange">变化上报</SelectItem>
                      <SelectItem value="OnTime">定时上报</SelectItem>
                      <SelectItem value="OnChangeAndTime">变化和定时上报</SelectItem>
                      <SelectItem value="Always">总是上报</SelectItem>
                    </SelectContent>
                  </Select>
                </div>
              </div>
            </TabsContent>

            <TabsContent value="collection" className="space-y-4">
              {/* 采集周期 */}
              <div className="flex items-start space-x-4 p-3 rounded-md border hover:bg-gray-50 transition-colors">
                <Checkbox
                  id="update-period"
                  checked={fieldsToUpdate.period}
                  onCheckedChange={(checked) =>
                    handleFieldCheckChange('period', !!checked)
                  }
                  className="mt-1"
                />
                <div className="grid w-full gap-1.5">
                  <Label htmlFor="period" className="text-sm font-medium">
                    采集周期 (毫秒)
                  </Label>
                  <Input
                    id="period"
                    type="number"
                    placeholder="1000"
                    value={formData.period}
                    onChange={(e) => handleChange('period', parseInt(e.target.value) || 0)}
                    disabled={!fieldsToUpdate.period}
                    className={
                      fieldsToUpdate.period
                        ? 'border-blue-300 focus-visible:ring-blue-300'
                        : ''
                    }
                  />
                </div>
              </div>

              {/* 上报间隔 */}
              <div className="flex items-start space-x-4 p-3 rounded-md border hover:bg-gray-50 transition-colors">
                <Checkbox
                  id="update-uploadInterval"
                  checked={fieldsToUpdate.uploadInterval}
                  onCheckedChange={(checked) =>
                    handleFieldCheckChange('uploadInterval', !!checked)
                  }
                  className="mt-1"
                />
                <div className="grid w-full gap-1.5">
                  <Label htmlFor="uploadInterval" className="text-sm font-medium">
                    上报间隔 (秒)
                  </Label>
                  <Input
                    id="uploadInterval"
                    type="number"
                    placeholder="60"
                    value={formData.uploadInterval}
                    onChange={(e) => handleChange('uploadInterval', parseInt(e.target.value) || 0)}
                    disabled={!fieldsToUpdate.uploadInterval}
                    className={
                      fieldsToUpdate.uploadInterval
                        ? 'border-blue-300 focus-visible:ring-blue-300'
                        : ''
                    }
                  />
                </div>
              </div>

              {/* 归档时间 */}
              <div className="flex items-start space-x-4 p-3 rounded-md border hover:bg-gray-50 transition-colors">
                <Checkbox
                  id="update-archiveTime"
                  checked={fieldsToUpdate.archiveTime}
                  onCheckedChange={(checked) =>
                    handleFieldCheckChange('archiveTime', !!checked)
                  }
                  className="mt-1"
                />
                <div className="grid w-full gap-1.5">
                  <Label htmlFor="archiveTime" className="text-sm font-medium">
                    强制归档时间 (秒)
                  </Label>
                  <Input
                    id="archiveTime"
                    type="number"
                    placeholder="3600"
                    value={formData.archiveTime}
                    onChange={(e) => handleChange('archiveTime', parseInt(e.target.value) || 0)}
                    disabled={!fieldsToUpdate.archiveTime}
                    className={
                      fieldsToUpdate.archiveTime
                        ? 'border-blue-300 focus-visible:ring-blue-300'
                        : ''
                    }
                  />
                </div>
              </div>

              {/* 小数位数 */}
              <div className="flex items-start space-x-4 p-3 rounded-md border hover:bg-gray-50 transition-colors">
                <Checkbox
                  id="update-length"
                  checked={fieldsToUpdate.length}
                  onCheckedChange={(checked) =>
                    handleFieldCheckChange('length', !!checked)
                  }
                  className="mt-1"
                />
                <div className="grid w-full gap-1.5">
                  <Label htmlFor="length" className="text-sm font-medium">
                    小数位数
                  </Label>
                  <Input
                    id="length"
                    type="number"
                    placeholder="2"
                    value={formData.length}
                    onChange={(e) => handleChange('length', parseInt(e.target.value) || 0)}
                    disabled={!fieldsToUpdate.length}
                    className={
                      fieldsToUpdate.length
                        ? 'border-blue-300 focus-visible:ring-blue-300'
                        : ''
                    }
                  />
                </div>
              </div>
            </TabsContent>

            <TabsContent value="driver" className="space-y-4">
              {/* 编码方式 */}
              <div className="flex items-start space-x-4 p-3 rounded-md border hover:bg-gray-50 transition-colors">
                <Checkbox
                  id="update-encoding"
                  checked={fieldsToUpdate.encoding}
                  onCheckedChange={(checked) =>
                    handleFieldCheckChange('encoding', !!checked)
                  }
                  className="mt-1"
                />
                <div className="grid w-full gap-1.5">
                  <Label htmlFor="encoding" className="text-sm font-medium">
                    编码方式
                  </Label>
                  <Select
                    value={formData.encoding}
                    onValueChange={(value) => handleChange('encoding', value)}
                    disabled={!fieldsToUpdate.encoding}>
                    <SelectTrigger id="encoding">
                      <SelectValue placeholder="选择编码方式" />
                    </SelectTrigger>
                    <SelectContent>
                      <SelectItem value="utf8">UTF-8</SelectItem>
                      <SelectItem value="unicode">Unicode</SelectItem>
                      <SelectItem value="ascii">ASCII</SelectItem>
                      <SelectItem value="gb2312">GB2312</SelectItem>
                    </SelectContent>
                  </Select>
                </div>
              </div>

              {/* 读取方法 */}
              <div className="flex items-start space-x-4 p-3 rounded-md border hover:bg-gray-50 transition-colors">
                <Checkbox
                  id="update-method"
                  checked={fieldsToUpdate.method}
                  onCheckedChange={(checked) =>
                    handleFieldCheckChange('method', !!checked)
                  }
                  className="mt-1"
                />
                <div className="grid w-full gap-1.5">
                  <Label htmlFor="method" className="text-sm font-medium">
                    读取方法
                  </Label>
                  <Select
                    value={formData.method}
                    onValueChange={(value) => handleChange('method', value)}
                    disabled={!fieldsToUpdate.method}>
                    <SelectTrigger id="method">
                      <SelectValue placeholder="选择读取方法" />
                    </SelectTrigger>
                    <SelectContent>
                      <SelectItem value="read">读取</SelectItem>
                      <SelectItem value="write">写入</SelectItem>
                    </SelectContent>
                  </Select>
                </div>
              </div>

              {/* 寄存器地址 */}
              <div className="flex items-start space-x-4 p-3 rounded-md border hover:bg-gray-50 transition-colors">
                <Checkbox
                  id="update-registerAddress"
                  checked={fieldsToUpdate.registerAddress}
                  onCheckedChange={(checked) =>
                    handleFieldCheckChange('registerAddress', !!checked)
                  }
                  className="mt-1"
                />
                <div className="grid w-full gap-1.5">
                  <Label htmlFor="registerAddress" className="text-sm font-medium">
                    寄存器地址
                  </Label>
                  <Input
                    id="registerAddress"
                    placeholder="例如: 100"
                    value={formData.registerAddress}
                    onChange={(e) => handleChange('registerAddress', e.target.value)}
                    disabled={!fieldsToUpdate.registerAddress}
                    className={
                      fieldsToUpdate.registerAddress
                        ? 'border-blue-300 focus-visible:ring-blue-300'
                        : ''
                    }
                  />
                </div>
              </div>

              {/* 读取长度 */}
              <div className="flex items-start space-x-4 p-3 rounded-md border hover:bg-gray-50 transition-colors">
                <Checkbox
                  id="update-readLength"
                  checked={fieldsToUpdate.readLength}
                  onCheckedChange={(checked) =>
                    handleFieldCheckChange('readLength', !!checked)
                  }
                  className="mt-1"
                />
                <div className="grid w-full gap-1.5">
                  <Label htmlFor="readLength" className="text-sm font-medium">
                    读取长度
                  </Label>
                  <Input
                    id="readLength"
                    type="number"
                    placeholder="1"
                    value={formData.readLength}
                    onChange={(e) => handleChange('readLength', parseInt(e.target.value) || 0)}
                    disabled={!fieldsToUpdate.readLength}
                    className={
                      fieldsToUpdate.readLength
                        ? 'border-blue-300 focus-visible:ring-blue-300'
                        : ''
                    }
                  />
                </div>
              </div>

              {/* 处理脚本 */}
              <div className="flex items-start space-x-4 p-3 rounded-md border hover:bg-gray-50 transition-colors">
                <Checkbox
                  id="update-content"
                  checked={fieldsToUpdate.content}
                  onCheckedChange={(checked) =>
                    handleFieldCheckChange('content', !!checked)
                  }
                  className="mt-1"
                />
                <div className="grid w-full gap-1.5">
                  <Label htmlFor="content" className="text-sm font-medium">
                    处理脚本
                  </Label>
                  <Input
                    id="content"
                    placeholder="处理脚本内容"
                    value={formData.content}
                    onChange={(e) => handleChange('content', e.target.value)}
                    disabled={!fieldsToUpdate.content}
                    className={
                      fieldsToUpdate.content
                        ? 'border-blue-300 focus-visible:ring-blue-300'
                        : ''
                    }
                  />
                </div>
              </div>
            </TabsContent>
          </Tabs>
        </div>

        <Separator />

        <DialogFooter className="pt-4">
          <div className="flex justify-between w-full items-center">
            <div>
              <span className="text-sm text-blue-600 flex items-center">
                <span className="bg-blue-100 h-6 w-6 rounded-full flex items-center justify-center mr-2">
                  <Check className="h-3 w-3" />
                </span>
                将修改 {tagsToEdit.length} 个点位
              </span>
            </div>
            <div className="flex gap-2">
              <Button variant="outline" onClick={() => onOpenChange(false)}>
                取消
              </Button>
              <Button
                onClick={handleSubmit}
                disabled={isSubmitting}
                className={isSubmitting ? '' : 'bg-blue-600 hover:bg-blue-700'}>
                {isSubmitting ? '提交中...' : '保存修改'}
              </Button>
            </div>
          </div>
        </DialogFooter>
      </DialogContent>
    </Dialog>
  )
}
