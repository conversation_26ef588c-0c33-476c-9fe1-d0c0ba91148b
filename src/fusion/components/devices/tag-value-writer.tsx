import { useState } from 'react'
import {
  <PERSON><PERSON>,
  DialogContent,
  DialogHeader,
  DialogTitle,
  DialogFooter,
} from '@/components/ui/dialog'
import { Button } from '@/components/ui/button'
import { Input } from '@/components/ui/input'
import { Label } from '@/components/ui/label'
import { Switch } from '@/components/ui/switch'
import {
  Select,
  SelectContent,
  SelectItem,
  SelectTrigger,
  SelectValue,
} from '@/components/ui/select'
import { toast } from '@/components/ui/use-toast'

interface DeviceTag {
  id: number
  name: string
  dataType: string
  value?: any
  status?: string
  address?: string
  timestamp?: string
  accessRight?: string
  alias?: string
  description?: string
  uploadMethod?: string
}

interface TagValueWriterProps {
  open: boolean
  onOpenChange: (open: boolean) => void
  tag: DeviceTag | null
}

export function TagValueWriter({
  open,
  onOpenChange,
  tag,
}: TagValueWriterProps) {
  const [value, setValue] = useState<string>('')
  const [loading, setLoading] = useState(false)

  // 重置表单
  const resetForm = () => {
    setValue(tag?.value?.toString() || '')
  }

  // 当对话框打开时重置表单
  if (open && tag && value === '') {
    resetForm()
  }

  // 处理值写入
  const handleWriteValue = async () => {
    if (!tag) return

    setLoading(true)
    try {
      // 在实际应用中使用: await TagService.writeTagValue(tag.id, value)
      // 模拟API调用
      await new Promise((resolve) => setTimeout(resolve, 800))

      toast({
        title: '写入成功',
        description: `已将值 "${value}" 写入点位 "${tag.name}"`,
      })
      onOpenChange(false)
    } catch (error) {
      console.error('写入点位值失败:', error)
      toast({
        title: '写入失败',
        description: '无法写入点位值',
        variant: 'destructive',
      })
    } finally {
      setLoading(false)
    }
  }

  // 根据数据类型渲染不同的输入控件
  const renderValueInput = () => {
    if (!tag) return null

    switch (tag.dataType) {
      case 'Boolean':
        return (
          <div className="flex items-center space-x-2">
            <Switch
              id="value-input"
              checked={value === 'true'}
              onCheckedChange={(checked) =>
                setValue(checked ? 'true' : 'false')
              }
            />
            <Label htmlFor="value-input">
              {value === 'true' ? '开启' : '关闭'}
            </Label>
          </div>
        )
      case 'Integer':
      case 'Float':
        return (
          <Input
            id="value-input"
            type="number"
            value={value}
            onChange={(e) => setValue(e.target.value)}
            placeholder={`输入${
              tag.dataType === 'Integer' ? '整数' : '浮点数'
            }值`}
          />
        )
      case 'Enum':
        return (
          <Select value={value} onValueChange={setValue}>
            <SelectTrigger>
              <SelectValue placeholder="选择枚举值" />
            </SelectTrigger>
            <SelectContent>
              <SelectItem value="0">关闭</SelectItem>
              <SelectItem value="1">开启</SelectItem>
              <SelectItem value="2">自动</SelectItem>
              <SelectItem value="3">手动</SelectItem>
            </SelectContent>
          </Select>
        )
      default:
        return (
          <Input
            id="value-input"
            value={value}
            onChange={(e) => setValue(e.target.value)}
            placeholder="输入值"
          />
        )
    }
  }

  return (
    <Dialog open={open} onOpenChange={onOpenChange}>
      <DialogContent className="sm:max-w-[425px]">
        <DialogHeader>
          <DialogTitle>写入点位值</DialogTitle>
        </DialogHeader>
        {tag && (
          <div className="grid gap-4 py-4">
            <div className="grid grid-cols-4 items-center gap-4">
              <Label htmlFor="tag-name" className="text-right">
                点位名称
              </Label>
              <div id="tag-name" className="col-span-3 font-medium">
                {tag.name}
              </div>
            </div>
            <div className="grid grid-cols-4 items-center gap-4">
              <Label htmlFor="tag-address" className="text-right">
                点位地址
              </Label>
              <div id="tag-address" className="col-span-3 font-mono text-sm">
                {tag.address || '-'}
              </div>
            </div>
            <div className="grid grid-cols-4 items-center gap-4">
              <Label htmlFor="tag-type" className="text-right">
                数据类型
              </Label>
              <div id="tag-type" className="col-span-3">
                <span className="px-2 py-1 bg-gray-100 rounded text-sm">
                  {tag.dataType}
                </span>
              </div>
            </div>
            <div className="grid grid-cols-4 items-center gap-4">
              <Label htmlFor="current-value" className="text-right">
                当前值
              </Label>
              <div id="current-value" className="col-span-3">
                {tag.value !== undefined ? String(tag.value) : '-'}
              </div>
            </div>
            <div className="grid grid-cols-4 items-center gap-4">
              <Label htmlFor="value-input" className="text-right">
                新值
              </Label>
              <div className="col-span-3">{renderValueInput()}</div>
            </div>
          </div>
        )}
        <DialogFooter>
          <Button variant="outline" onClick={() => onOpenChange(false)}>
            取消
          </Button>
          <Button onClick={handleWriteValue} disabled={loading}>
            {loading ? '写入中...' : '写入'}
          </Button>
        </DialogFooter>
      </DialogContent>
    </Dialog>
  )
}
