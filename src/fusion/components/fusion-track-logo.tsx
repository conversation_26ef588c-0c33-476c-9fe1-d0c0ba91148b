/**
 * FusionTrack Logo组件 - 系统品牌标识
 *
 * 组件功能：显示FusionTrack工作流编排平台的品牌标识和Logo
 * 使用场景：在页面头部、侧边栏、登录页面等需要显示品牌标识的位置
 *
 * 主要功能：
 * - 品牌Logo图标显示（使用Menu图标作为标识）
 * - 品牌文字标识显示（FusionTrack文字）
 * - 支持多种尺寸大小配置
 * - 支持不同颜色主题变体
 * - 响应式设计适配
 *
 * Props参数：
 * - size?: "sm" | "md" | "lg" - Logo尺寸大小（默认md）
 *   - sm: 小尺寸，适用于紧凑布局
 *   - md: 中等尺寸，适用于常规布局
 *   - lg: 大尺寸，适用于醒目展示
 * - variant?: "default" | "white" - 颜色主题变体（默认default）
 *   - default: 默认深色主题
 *   - white: 白色主题，适用于深色背景
 *
 * 设计特点：
 * - 使用Menu图标作为品牌标识符号
 * - 采用现代化的无衬线字体
 * - 支持响应式尺寸调整
 * - 清晰的品牌识别度
 * - 简洁的设计风格
 *
 * 使用示例：
 * ```tsx
 * // 基础使用
 * <FusionTrackLogo />
 *
 * // 大尺寸白色主题
 * <FusionTrackLogo size="lg" variant="white" />
 *
 * // 小尺寸默认主题
 * <FusionTrackLogo size="sm" variant="default" />
 * ```
 */

import { Menu } from 'lucide-react'

interface FusionTrackLogoProps {
  size?: 'sm' | 'md' | 'lg'
  variant?: 'default' | 'white'
}

export function FusionTrackLogo({
  size = 'md',
  variant = 'default',
}: FusionTrackLogoProps) {
  const sizeClasses = {
    sm: 'h-6',
    md: 'h-8',
    lg: 'h-10',
  }

  const textColor = variant === 'white' ? 'text-white' : 'text-gray-900'

  return (
    <div className="flex items-center space-x-2">
      <Menu
        className={`${
          size === 'sm' ? 'h-5 w-5' : size === 'md' ? 'h-6 w-6' : 'h-7 w-7'
        } text-black`}
      />
      <div
        className={`font-bold ${textColor} ${
          size === 'sm' ? 'text-sm' : size === 'md' ? 'text-base' : 'text-xl'
        }`}>
        <span className="font-bold">Fusion</span>
        <span className="font-bold">Track</span>
      </div>
    </div>
  )
}
