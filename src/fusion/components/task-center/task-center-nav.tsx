/**
 * 迁移自: components/task-center/task-center-nav.tsx
 * 迁移时间: 2025-01-14T09:40:00.000Z
 *
 * 迁移说明:
 * - 已替换 next/navigation usePathname 为 react-router-dom useLocation
 * - 保持所有样式、业务逻辑完全不变
 *
 * 注意: 本文件保持与原始文件100%功能一致性
 */

import { Link, useLocation } from 'react-router-dom'
import { cn } from '@/lib/utils'
import { LayoutDashboard, Clock, Settings, History, Bell } from 'lucide-react'

export function TaskCenterNav() {
  const location = useLocation()
  const pathname = location.pathname

  const navItems = [
    {
      title: '任务概览',
      href: '/task-center',
      icon: LayoutDashboard,
      exact: true,
    },
    {
      title: '定时任务',
      href: '/task-center/scheduled-tasks',
      icon: Clock,
    },
    {
      title: '任务配置',
      href: '/task-center/task-config',
      icon: Settings,
    },
    {
      title: '执行历史',
      href: '/task-center/history',
      icon: History,
    },
    {
      title: '任务监控',
      href: '/task-center/monitoring',
      icon: Bell,
    },
  ]

  return (
    <div className="border-b bg-background/95 backdrop-blur supports-[backdrop-filter]:bg-background/60">
      <nav className="container flex items-center h-16 space-x-4">
        {navItems.map((item) => {
          const isActive = item.exact
            ? pathname === item.href
            : pathname?.startsWith(item.href)

          return (
            <Link
              key={item.href}
              to={item.href}
              className={cn(
                'flex items-center gap-2 rounded-md px-3 py-2 text-sm font-medium transition-colors',
                isActive
                  ? 'bg-primary text-primary-foreground'
                  : 'text-muted-foreground hover:bg-muted hover:text-foreground'
              )}>
              <item.icon className="h-4 w-4" />
              {item.title}
            </Link>
          )
        })}
      </nav>
    </div>
  )
}
