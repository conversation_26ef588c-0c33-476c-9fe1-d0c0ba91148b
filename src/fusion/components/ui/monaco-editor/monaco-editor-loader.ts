import { useEffect, useState } from 'react'

interface MonacoWindow extends Window {
  monaco: any
}

declare const window: MonacoWindow

let isInitialized = false
const loadingState = { isLoading: false }

export function useMonacoEditor() {
  const [monaco, setMonaco] = useState<any>(null)
  const [isLoading, setIsLoading] = useState(false)
  const [error, setError] = useState<Error | null>(null)

  useEffect(() => {
    if (!monaco && !isLoading && !error) {
      setIsLoading(true)
      loadMonaco()
        .then((monacoInstance) => {
          setMonaco(monacoInstance)
          setIsLoading(false)
        })
        .catch((err) => {
          console.error('Failed to load Monaco Editor:', err)
          setError(err instanceof Error ? err : new Error(JSON.stringify(err)))
          setIsLoading(false)
        })
    }
  }, [monaco, isLoading, error])

  return { monaco, isLoading, error }
}

export function loadMonaco(): Promise<any> {
  // 如果已经初始化并且monaco已经加载，直接返回
  if (isInitialized && typeof window !== 'undefined' && window.monaco) {
    return Promise.resolve(window.monaco)
  }

  // 确保我们在浏览器环境中
  if (typeof window === 'undefined') {
    return Promise.reject(
      new Error('Monaco editor can only be loaded in browser environment')
    )
  }

  loadingState.isLoading = true

  // 创建一个新的加载过程
  return new Promise((resolve, reject) => {
    // 设置超时
    const timeoutId = setTimeout(() => {
      reject(new Error('Monaco editor loading timeout after 10 seconds'))
    }, 10000)

    try {
      // 使用动态导入，webpack plugin 会处理依赖加载
      import('monaco-editor')
        .then((monaco) => {
          clearTimeout(timeoutId)
          isInitialized = true
          loadingState.isLoading = false

          // 配置 monaco 全局设置
          configureMonaco(monaco)

          resolve(monaco)
        })
        .catch((err) => {
          clearTimeout(timeoutId)
          loadingState.isLoading = false
          console.error('Failed to import Monaco Editor:', err)
          reject(err)
        })
    } catch (err) {
      clearTimeout(timeoutId)
      loadingState.isLoading = false
      console.error('Error during Monaco Editor import:', err)
      reject(err)
    }
  })
}

// 配置 Monaco Editor 全局设置
function configureMonaco(monaco: any) {
  try {
    // 配置 JavaScript 语言设置
    monaco.languages.typescript.javascriptDefaults.setDiagnosticsOptions({
      noSemanticValidation: false,
      noSyntaxValidation: false,
    })

    // 设置编译器选项
    monaco.languages.typescript.javascriptDefaults.setCompilerOptions({
      target: monaco.languages.typescript.ScriptTarget.ES2020,
      allowNonTsExtensions: true,
      moduleResolution: monaco.languages.typescript.ModuleResolutionKind.NodeJs,
      module: monaco.languages.typescript.ModuleKind.CommonJS,
      noEmit: true,
      typeRoots: ['node_modules/@types'],
      allowJs: true,
      checkJs: false,
      allowReturnOutsideFunction: true,
    })
  } catch (error) {
    console.error('配置 Monaco Editor 失败:', error)
  }
}
