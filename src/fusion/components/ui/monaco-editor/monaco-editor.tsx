import {
  useRef,
  useEffect,
  useState,
  forwardRef,
  useImperativeHandle,
} from 'react'
import { useMonacoEditor } from './monaco-editor-loader'
import { Button } from '@/components/ui/button'
import { RefreshCw, AlertCircle } from 'lucide-react'

export interface MonacoEditorRef {
  getValue: () => string
  setValue: (value: string) => void
  formatCode: () => void
  insertTextAtCursor: (text: string) => void
  focus: () => void
}

export interface MonacoEditorProps {
  value?: string
  onChange?: (value: string) => void
  language?: string
  theme?: string
  height?: string | number
  width?: string | number
  options?: any
  defaultValue?: string
  onError?: (error: any) => void
  onMount?: (editor: any, monaco: any) => void
}

const MonacoEditor = forwardRef<MonacoEditorRef, MonacoEditorProps>(
  (
    {
      value,
      onChange,
      language = 'javascript',
      theme = 'vs-dark',
      height = '100%',
      width = '100%',
      options = {},
      defaultValue = '',
      onError,
      onMount,
    },
    ref
  ) => {
    const { monaco, isLoading, error } = useMonacoEditor()
    const editorRef = useRef<any>(null)
    const containerRef = useRef<HTMLDivElement>(null)
    const [editorValue, setEditorValue] = useState(value || defaultValue)

    // 如果有错误，通知父组件
    useEffect(() => {
      if (error && onError) {
        onError(error)
      }
    }, [error, onError])

    // Expose methods via ref
    useImperativeHandle(ref, () => ({
      getValue: () => editorValue,
      setValue: (newValue: string) => {
        setEditorValue(newValue)
        if (editorRef.current) {
          editorRef.current.setValue(newValue)
        }
      },
      formatCode: () => {
        if (editorRef.current && monaco) {
          try {
            // 尝试获取当前活动的编辑器并执行格式化
            const activeEditor =
              monaco.editor.getEditors()[0] || editorRef.current
            const formatAction = activeEditor.getAction(
              'editor.action.formatDocument'
            )
            if (formatAction) {
              formatAction.run()
            }
          } catch (err) {
            console.error('Format code error:', err)
          }
        }
      },
      insertTextAtCursor: (text: string) => {
        if (editorRef.current) {
          try {
            const selection = editorRef.current.getSelection()
            const id = { major: 1, minor: 1 }
            const op = {
              identifier: id,
              range: selection,
              text: text,
              forceMoveMarkers: true,
            }
            editorRef.current.executeEdits('my-source', [op])
            editorRef.current.focus()
          } catch (err) {
            console.error('Insert text error:', err)
          }
        }
      },
      focus: () => {
        if (editorRef.current) {
          try {
            editorRef.current.focus()
          } catch (err) {
            console.error('Focus error:', err)
          }
        }
      },
    }))

    // Initialize editor
    useEffect(() => {
      let modelToDispose: any = null

      if (monaco && containerRef.current && !editorRef.current) {
        try {
          // Configure editor options
          const editorOptions = {
            value: editorValue,
            language,
            theme,
            automaticLayout: true,
            minimap: { enabled: true },
            lineNumbers: 'on',
            scrollBeyondLastLine: false,
            fontSize: 14,
            tabSize: 2,
            wordWrap: 'on',
            // 增强智能提示
            suggestOnTriggerCharacters: true,
            quickSuggestions: {
              other: true,
              comments: true,
              strings: true,
            },
            parameterHints: {
              enabled: true,
            },
            snippetSuggestions: 'inline',
            formatOnType: true,
            formatOnPaste: true,
            // 禁用可能导致问题的内联补全
            inlineSuggest: { enabled: false },
            ...options,
          }

          // 创建自定义模型并使用它
          try {
            const model = monaco.editor.createModel(
              editorValue,
              language,
              monaco.Uri.parse(
                `file:///main.${language === 'javascript' ? 'js' : language}`
              )
            )

            // 保存模型引用以便在清理时使用
            modelToDispose = model

            // 在选项中使用此模型
            editorOptions.model = model
          } catch (modelError) {
            console.warn('创建自定义模型失败，将使用默认模型:', modelError)
          }

          // Create editor instance
          editorRef.current = monaco.editor.create(
            containerRef.current,
            editorOptions
          )

          // 保存 monaco 对象到全局
          if (typeof window !== 'undefined') {
            window.monaco = monaco
          }

          // 调用onMount回调
          if (onMount && typeof onMount === 'function') {
            try {
              onMount(editorRef.current, monaco)
            } catch (err) {
              console.error('onMount callback error:', err)
            }
          }

          // Set up change event handler
          editorRef.current.onDidChangeModelContent(() => {
            try {
              const newValue = editorRef.current.getValue()
              setEditorValue(newValue)
              onChange && onChange(newValue)
            } catch (err) {
              console.error('Editor change event error:', err)
            }
          })

          // Set up intellisense
          setupIntellisense(monaco)
        } catch (err) {
          console.error('Monaco editor initialization error:', err)
          if (onError) {
            onError(err)
          }
        }
      }

      return () => {
        // 清理编辑器
        if (editorRef.current) {
          try {
            // 清除编辑器引用和监听器
            const editor = editorRef.current
            editorRef.current = null

            try {
              // 清理编辑器实例
              editor.dispose()
            } catch (editorError) {
              console.warn('编辑器释放错误(忽略):', editorError)
            }
          } catch (err) {
            console.error('Editor dispose error:', err)
          }
        }

        // 单独处理模型
        if (modelToDispose) {
          try {
            setTimeout(() => {
              if (modelToDispose && !modelToDispose.isDisposed()) {
                modelToDispose.dispose()
              }
            }, 0)
          } catch (modelErr) {
            console.warn('模型释放错误(忽略):', modelErr)
          }
        }
      }
    }, [monaco, containerRef.current])

    // Update editor value when prop changes
    useEffect(() => {
      if (
        editorRef.current &&
        value !== undefined &&
        value !== editorRef.current.getValue()
      ) {
        try {
          editorRef.current.setValue(value)
        } catch (err) {
          console.error('Set value error:', err)
        }
      }
    }, [value])

    // Setup intellisense
    const setupIntellisense = (monaco: any) => {
      try {
        // Add custom completions for device API
        monaco.languages.registerCompletionItemProvider('javascript', {
          provideCompletionItems: (model: any, position: any) => {
            const word = model.getWordUntilPosition(position)
            const range = {
              startLineNumber: position.lineNumber,
              endLineNumber: position.lineNumber,
              startColumn: word.startColumn,
              endColumn: word.endColumn,
            }

            // Device API suggestions
            const suggestions = [
              {
                label: 'device',
                kind: monaco.languages.CompletionItemKind.Class,
                documentation: '设备操作API',
                insertText: 'device',
                range,
              },
              {
                label: 'device.Get',
                kind: monaco.languages.CompletionItemKind.Method,
                documentation: '获取设备标签值',
                insertText: 'device.Get(${1:deviceId}, ${2:tagName})',
                insertTextRules:
                  monaco.languages.CompletionItemInsertTextRule.InsertAsSnippet,
                range,
              },
              {
                label: 'device.Set',
                kind: monaco.languages.CompletionItemKind.Method,
                documentation: '设置设备标签值',
                insertText:
                  'device.Set(${1:deviceId}, ${2:tagName}, ${3:value})',
                insertTextRules:
                  monaco.languages.CompletionItemInsertTextRule.InsertAsSnippet,
                range,
              },
              // 日期时间API
              {
                label: 'dt',
                kind: monaco.languages.CompletionItemKind.Class,
                documentation: '日期时间API',
                insertText: 'dt',
                range,
              },
              {
                label: 'dt.Format',
                kind: monaco.languages.CompletionItemKind.Method,
                documentation: '格式化日期时间',
                insertText: 'dt.Format(${1:format})',
                insertTextRules:
                  monaco.languages.CompletionItemInsertTextRule.InsertAsSnippet,
                range,
              },
              // 系统API
              {
                label: 'system',
                kind: monaco.languages.CompletionItemKind.Class,
                documentation: '系统API',
                insertText: 'system',
                range,
              },
              {
                label: 'system.Log',
                kind: monaco.languages.CompletionItemKind.Method,
                documentation: '输出日志信息',
                insertText: 'system.Log(${1:message}, ${2:level})',
                insertTextRules:
                  monaco.languages.CompletionItemInsertTextRule.InsertAsSnippet,
                range,
              },
              // 常用函数
              {
                label: 'IF',
                kind: monaco.languages.CompletionItemKind.Function,
                documentation: '条件判断函数：IF(条件, 真值, 假值)',
                insertText:
                  'IF(${1:condition}, ${2:trueValue}, ${3:falseValue})',
                insertTextRules:
                  monaco.languages.CompletionItemInsertTextRule.InsertAsSnippet,
                range,
              },
              {
                label: 'MAX',
                kind: monaco.languages.CompletionItemKind.Function,
                documentation: '最大值函数：MAX(值1, 值2, ...)',
                insertText: 'MAX(${1:value1}, ${2:value2})',
                insertTextRules:
                  monaco.languages.CompletionItemInsertTextRule.InsertAsSnippet,
                range,
              },
              {
                label: 'MIN',
                kind: monaco.languages.CompletionItemKind.Function,
                documentation: '最小值函数：MIN(值1, 值2, ...)',
                insertText: 'MIN(${1:value1}, ${2:value2})',
                insertTextRules:
                  monaco.languages.CompletionItemInsertTextRule.InsertAsSnippet,
                range,
              },
              {
                label: 'AVG',
                kind: monaco.languages.CompletionItemKind.Function,
                documentation: '平均值函数：AVG(值1, 值2, ...)',
                insertText: 'AVG(${1:value1}, ${2:value2})',
                insertTextRules:
                  monaco.languages.CompletionItemInsertTextRule.InsertAsSnippet,
                range,
              },
              {
                label: 'SUM',
                kind: monaco.languages.CompletionItemKind.Function,
                documentation: '求和函数：SUM(值1, 值2, ...)',
                insertText: 'SUM(${1:value1}, ${2:value2})',
                insertTextRules:
                  monaco.languages.CompletionItemInsertTextRule.InsertAsSnippet,
                range,
              },
              {
                label: 'ROUND',
                kind: monaco.languages.CompletionItemKind.Function,
                documentation: '四舍五入：ROUND(数值, 小数位数)',
                insertText: 'ROUND(${1:number}, ${2:digits})',
                insertTextRules:
                  monaco.languages.CompletionItemInsertTextRule.InsertAsSnippet,
                range,
              },
            ]

            return {
              suggestions: suggestions,
            }
          },
        })
      } catch (error) {
        console.error('添加智能提示失败:', error)
      }
    }

    if (isLoading) {
      return (
        <div className="flex flex-col items-center justify-center h-full w-full min-h-[200px] border rounded-md bg-muted p-4">
          <div className="flex items-center justify-center gap-2 mb-4">
            <RefreshCw className="h-5 w-5 animate-spin text-primary" />
            <span className="text-sm text-muted-foreground">
              正在加载编辑器...
            </span>
          </div>
        </div>
      )
    }

    if (error) {
      return (
        <div className="flex flex-col items-center justify-center h-full w-full min-h-[200px] border rounded-md bg-muted/30 p-4">
          <div className="flex items-center justify-center gap-2 mb-4 text-destructive">
            <AlertCircle className="h-5 w-5" />
            <span className="text-sm font-medium">加载编辑器失败</span>
          </div>
          <p className="text-xs text-muted-foreground mb-4">
            {error.message || '发生了未知错误，请刷新页面重试。'}
          </p>
          <Button
            variant="outline"
            size="sm"
            onClick={() => window.location.reload()}>
            刷新页面
          </Button>
        </div>
      )
    }

    return (
      <div
        ref={containerRef}
        style={{
          height,
          width,
          overflow: 'hidden',
          border: '1px solid #e2e8f0',
          borderRadius: '0.375rem',
        }}
        className="monaco-editor-container"></div>
    )
  }
)

MonacoEditor.displayName = 'MonacoEditor'

export { MonacoEditor }
