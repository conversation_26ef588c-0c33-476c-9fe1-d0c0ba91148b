import * as React from 'react'
import { cn } from '@/lib/utils'

/**
 * VisuallyHidden component
 *
 * This component hides content visually but keeps it accessible to screen readers.
 * It's useful for providing additional context or labels that are needed for accessibility
 * but would be redundant for sighted users.
 *
 * Common use cases:
 * - Hiding DialogTitle when the dialog's purpose is clear from context
 * - Adding descriptive text for form controls
 * - Providing additional context for interactive elements
 */

interface VisuallyHiddenProps extends React.HTMLAttributes<HTMLSpanElement> {
  /**
   * The content to be visually hidden but accessible to screen readers
   */
  children: React.ReactNode
  /**
   * The HTML element to render. Defaults to 'span'
   */
  asChild?: boolean
}

const VisuallyHidden = React.forwardRef<HTMLSpanElement, VisuallyHiddenProps>(
  ({ children, className, asChild = false, ...props }, ref) => {
    const Comp = asChild ? React.Fragment : 'span'
    
    if (asChild) {
      return (
        <>
          {React.Children.map(children, (child) => {
            if (React.isValidElement(child)) {
              return React.cloneElement(child, {
                ...child.props,
                className: cn(
                  'sr-only',
                  child.props.className
                ),
              })
            }
            return child
          })}
        </>
      )
    }

    return (
      <Comp
        ref={ref}
        className={cn(
          'sr-only',
          className
        )}
        {...props}
      >
        {children}
      </Comp>
    )
  }
)

VisuallyHidden.displayName = 'VisuallyHidden'

export { VisuallyHidden }
