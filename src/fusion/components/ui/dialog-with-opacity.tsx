import React from 'react'
import { Dialog, DialogContent } from '@/components/ui/dialog'
import { DialogProps } from '@radix-ui/react-dialog'

/**
 * 全局对话框透明度配置
 * 可在此处修改默认透明度值
 */
export const DEFAULT_DIALOG_OPACITY = 0.9

/**
 * 带透明度控制的对话框组件
 */
export const DialogWithOpacity: React.FC<
  React.PropsWithChildren<
    Omit<DialogProps, 'modal'> & { backgroundOpacity?: number }
  >
> = ({ children, backgroundOpacity = DEFAULT_DIALOG_OPACITY, ...props }) => {
  return <Dialog {...props}>{children}</Dialog>
}

/**
 * 带透明度控制的对话框内容组件
 */
export interface DialogContentWithOpacityProps
  extends React.ComponentPropsWithoutRef<typeof DialogContent> {
  backgroundOpacity?: number
}

export const DialogContentWithOpacity: React.FC<
  DialogContentWithOpacityProps
> = ({ children, backgroundOpacity = DEFAULT_DIALOG_OPACITY, ...props }) => {
  return (
    <DialogContent backgroundOpacity={backgroundOpacity} {...props}>
      {children}
    </DialogContent>
  )
}

/**
 * 为组件添加透明度控制的高阶组件
 * 用于包装现有的对话框组件，为其添加透明度控制功能
 */
export function withDialogOpacity<T extends Record<string, any>>(
  Component: React.ComponentType<T>,
  defaultOpacity: number = DEFAULT_DIALOG_OPACITY
): React.FC<T & { backgroundOpacity?: number }> {
  const WithOpacity = ({
    backgroundOpacity = defaultOpacity,
    ...props
  }: T & { backgroundOpacity?: number }) => {
    // 注入透明度到子组件中的DialogContent
    const enhancedChildren = React.Children.map(
      // 我们知道 props.children 是 ReactNode
      props.children as React.ReactNode,
      (child) => {
        if (React.isValidElement(child) && child.type === DialogContent) {
          return React.cloneElement(child, {
            ...(child.props as Record<string, unknown>),
            backgroundOpacity,
          } as any)
        }
        return child
      }
    )

    return <Component {...(props as any)} children={enhancedChildren} />
  }

  return WithOpacity
}
