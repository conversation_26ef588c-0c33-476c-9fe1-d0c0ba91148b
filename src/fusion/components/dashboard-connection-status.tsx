/**
 * 仪表盘连接状态组件 - 实时连接状态显示
 *
 * 组件功能：显示系统实时连接状态，包括连接状态图标、文字提示和重连按钮
 * 使用场景：在仪表盘和其他需要显示连接状态的页面中使用
 *
 * 主要功能：
 * - 实时连接状态监控（已连接/连接中/已断开/错误）
 * - 状态图标可视化显示（WiFi图标变化）
 * - 状态文字提示和工具提示
 * - 可选的重连按钮功能
 * - 支持多种尺寸大小配置
 * - 响应式颜色状态指示
 *
 * Props参数：
 * - showReconnectButton?: boolean - 是否显示重连按钮（默认false）
 * - size?: "sm" | "md" | "lg" - 组件尺寸大小（默认md）
 * - className?: string - 自定义CSS类名
 *
 * 连接状态类型：
 * - connected: 已连接状态（绿色WiFi图标）
 * - connecting: 连接中状态（黄色旋转图标）
 * - disconnected: 已断开状态（红色WiFi关闭图标）
 * - error: 连接错误状态（红色WiFi关闭图标）
 *
 * 技术特点：
 * - 使用Lucide图标库提供状态图标
 * - 集成Tooltip组件提供悬停提示
 * - 支持动画效果（连接中旋转动画）
 * - 使用TypeScript确保类型安全
 * - 可配置的尺寸和样式
 *
 * 使用示例：
 * ```tsx
 * // 基础使用
 * <DashboardConnectionStatus />
 *
 * // 带重连按钮和自定义尺寸
 * <DashboardConnectionStatus showReconnectButton={true} size="lg" />
 * ```
 */

import { useState, useEffect } from 'react'
import { Wifi, WifiOff, Loader2 } from 'lucide-react'
import {
  Tooltip,
  TooltipContent,
  TooltipProvider,
  TooltipTrigger,
} from '@/components/ui/tooltip'

type ConnectionState = 'connected' | 'connecting' | 'disconnected' | 'error'

interface DashboardConnectionStatusProps {
  showReconnectButton?: boolean
  size?: 'sm' | 'md' | 'lg'
  className?: string
}

export function DashboardConnectionStatus({
  showReconnectButton = false,
  size = 'md',
  className = '',
}: DashboardConnectionStatusProps) {
  const [connectionState, setConnectionState] =
    useState<ConnectionState>('connected')

  // 模拟连接状态 - 在实际应用中，这将连接到真实的状态
  useEffect(() => {
    // 这里只是为了演示，实际应用中应该连接到真实的状态
    const interval = setInterval(() => {
      // 随机模拟状态变化，大部分时间保持连接状态
      const random = Math.random()
      if (random > 0.95) {
        setConnectionState('connecting')
        setTimeout(() => setConnectionState('connected'), 2000)
      }
    }, 10000)

    return () => clearInterval(interval)
  }, [])

  const iconSize = {
    sm: 'h-3 w-3',
    md: 'h-4 w-4',
    lg: 'h-5 w-5',
  }[size]

  const getStatusIcon = () => {
    switch (connectionState) {
      case 'connected':
        return <Wifi className={`${iconSize} text-green-500`} />
      case 'connecting':
        return <Loader2 className={`${iconSize} text-amber-500 animate-spin`} />
      case 'disconnected':
      case 'error':
      default:
        return <WifiOff className={`${iconSize} text-red-500`} />
    }
  }

  const getStatusText = () => {
    switch (connectionState) {
      case 'connected':
        return '实时连接已建立'
      case 'connecting':
        return '正在连接实时服务...'
      case 'disconnected':
        return '实时连接已断开'
      case 'error':
        return '连接出错'
      default:
        return '未知状态'
    }
  }

  return (
    <TooltipProvider>
      <Tooltip>
        <TooltipTrigger asChild>
          <div className={`flex items-center gap-2 ${className}`}>
            {getStatusIcon()}
            {connectionState === 'connected' && (
              <span className="text-xs text-green-600">已连接</span>
            )}
            {connectionState === 'connecting' && (
              <span className="text-xs text-amber-500">连接中...</span>
            )}
            {(connectionState === 'disconnected' ||
              connectionState === 'error') && (
              <span className="text-xs text-red-500">已断开</span>
            )}
            {showReconnectButton &&
              (connectionState === 'disconnected' ||
                connectionState === 'error') && (
                <button
                  className="text-xs px-2 py-0.5 rounded bg-gray-100 hover:bg-gray-200"
                  onClick={() => setConnectionState('connecting')}>
                  重连
                </button>
              )}
          </div>
        </TooltipTrigger>
        <TooltipContent>
          <p>{getStatusText()}</p>
        </TooltipContent>
      </Tooltip>
    </TooltipProvider>
  )
}

// 添加默认导出
export default DashboardConnectionStatus
