/**
 * 迁移自: components/nodes/file-operation-node.tsx
 * 迁移时间: 2025-01-14T09:10:00.000Z
 *
 * 迁移说明:
 * - 已移除  指令
 * - 已替换 next/dynamic 为直接导入 reactflow 组件
 * - 保持所有样式、业务逻辑完全不变
 *
 * 注意: 本文件保持与原始文件100%功能一致性
 */

import { FileText, FileUp, FileDown, Variable } from 'lucide-react'
import { Handle, Position } from 'reactflow'

export function FileOperationNode({
  data,
  isConnectable,
}: {
  data: any
  isConnectable: boolean
}) {
  // 根据节点类型选择图标
  const getIcon = () => {
    switch (data.type) {
      case 'file-read':
      case 'file-write':
      case 'file-append':
      case 'csv-read':
      case 'csv-write':
      case 'excel-read':
      case 'excel-write':
        return <FileText className="h-5 w-5" />
      case 'ftp-upload':
      case 'sftp-upload':
        return <FileUp className="h-5 w-5" />
      case 'ftp-download':
      case 'sftp-download':
        return <FileDown className="h-5 w-5" />
      default:
        return <FileText className="h-5 w-5" />
    }
  }

  // 获取节点描述
  const getDescription = () => {
    if (data.type === 'file-read') {
      return `读取: ${data.filePath || '未指定文件'}`
    } else if (data.type === 'file-write') {
      return `写入: ${data.filePath || '未指定文件'}`
    } else if (data.type === 'file-append') {
      return `追加: ${data.filePath || '未指定文件'}`
    } else if (data.type === 'csv-read') {
      return `CSV读取: ${data.filePath || '未指定文件'}`
    } else if (data.type === 'csv-write') {
      return `CSV写入: ${data.filePath || '未指定文件'}`
    } else if (data.type === 'excel-read') {
      return `Excel读取: ${data.filePath || '未指定文件'}`
    } else if (data.type === 'excel-write') {
      return `Excel写入: ${data.filePath || '未指定文件'}`
    } else if (data.type === 'ftp-upload') {
      return `FTP上传: ${data.server || '未指定服务器'}`
    } else if (data.type === 'ftp-download') {
      return `FTP下载: ${data.server || '未指定服务器'}`
    } else if (data.type === 'sftp-upload') {
      return `SFTP上传: ${data.server || '未指定服务器'}`
    } else if (data.type === 'sftp-download') {
      return `SFTP下载: ${data.server || '未指定服务器'}`
    }
    return '文件操作'
  }

  // 检查节点是否有变量
  const hasVariables = data.variables && data.variables.length > 0

  return (
    <div
      className={`rounded-lg border-2 ${
        data.enabled === false
          ? 'border-gray-300 bg-gray-50'
          : 'border-amber-500 bg-white'
      } p-3 shadow-md`}>
      <div className="flex items-center gap-2">
        <div
          className={`flex h-8 w-8 items-center justify-center rounded-full ${
            data.enabled === false
              ? 'bg-gray-200 text-gray-500'
              : 'bg-amber-100 text-amber-500'
          }`}>
          {getIcon()}
        </div>
        <div>
          <div className="text-sm font-medium">{data.label}</div>
          <div className="text-xs text-gray-500">{getDescription()}</div>
        </div>
      </div>

      {/* 显示变量指示器 */}
      {hasVariables && (
        <div className="mt-2 flex items-center gap-1">
          <Variable className="h-3 w-3 text-gray-500" />
          <span className="text-xs text-gray-500">
            {data.variables.length} 个变量
          </span>
        </div>
      )}

      {/* 输入和输出连接点 */}
      <Handle
        type="target"
        position={Position.Top}
        id="in"
        isConnectable={isConnectable}
        className={`h-3 w-3 ${
          data.enabled === false ? 'bg-gray-400' : 'bg-amber-500'
        }`}
      />
      <Handle
        type="source"
        position={Position.Bottom}
        id="out"
        isConnectable={isConnectable}
        className={`h-3 w-3 ${
          data.enabled === false ? 'bg-gray-400' : 'bg-amber-500'
        }`}
      />
    </div>
  )
}

export default FileOperationNode
