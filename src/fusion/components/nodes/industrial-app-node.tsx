/**
 * 迁移自: components/nodes/industrial-app-node.tsx
 * 迁移时间: 2025-01-14T09:12:00.000Z
 *
 * 迁移说明:
 * - 已移除  指令
 * - 已替换 next/dynamic 为直接导入 reactflow 组件
 * - 保持所有样式、业务逻辑完全不变
 *
 * 注意: 本文件保持与原始文件100%功能一致性
 */

import {
  Barcode,
  QrCode,
  Printer,
  Camera,
  Microscope,
  Scale,
  Thermometer,
  Ruler,
  Truck,
  Factory,
  Variable,
} from 'lucide-react'
import { Handle, Position } from 'reactflow'

export function IndustrialAppNode({
  data,
  isConnectable,
}: {
  data: any
  isConnectable: boolean
}) {
  // 根据节点类型选择图标
  const getIcon = () => {
    switch (data.type) {
      case 'barcode-scan':
        return <Barcode className="h-5 w-5" />
      case 'qrcode-scan':
        return <QrCode className="h-5 w-5" />
      case 'printer-control':
        return <Printer className="h-5 w-5" />
      case 'camera-capture':
        return <Camera className="h-5 w-5" />
      case 'image-process':
        return <Microscope className="h-5 w-5" />
      case 'weight-measure':
        return <Scale className="h-5 w-5" />
      case 'temperature-measure':
        return <Thermometer className="h-5 w-5" />
      case 'dimension-measure':
        return <Ruler className="h-5 w-5" />
      case 'robot-control':
        return <Truck className="h-5 w-5" />
      case 'conveyor-control':
        return <Factory className="h-5 w-5" />
      default:
        return <Factory className="h-5 w-5" />
    }
  }

  // 获取节点描述
  const getDescription = () => {
    if (data.type === 'barcode-scan') {
      return `扫描器: ${data.deviceName || '未指定设备'}`
    } else if (data.type === 'qrcode-scan') {
      return `扫描器: ${data.deviceName || '未指定设备'}`
    } else if (data.type === 'printer-control') {
      return `打印机: ${data.printerName || '未指定打印机'}`
    } else if (data.type === 'camera-capture') {
      return `相机: ${data.cameraName || '未指定相机'}`
    } else if (data.type === 'image-process') {
      return `处理: ${data.processType || '未指定处理类型'}`
    } else if (data.type === 'weight-measure') {
      return `秤: ${data.scaleName || '未指定设备'}`
    } else if (data.type === 'temperature-measure') {
      return `传感器: ${data.sensorName || '未指定传感器'}`
    } else if (data.type === 'dimension-measure') {
      return `测量仪: ${data.deviceName || '未指定设备'}`
    } else if (data.type === 'robot-control') {
      return `机器人: ${data.robotName || '未指定机器人'}`
    } else if (data.type === 'conveyor-control') {
      return `传送带: ${data.conveyorName || '未指定传送带'}`
    }
    return '工业应用'
  }

  // 检查节点是否有变量
  const hasVariables = data.variables && data.variables.length > 0

  return (
    <div
      className={`rounded-lg border-2 ${
        data.enabled === false
          ? 'border-gray-300 bg-gray-50'
          : 'border-indigo-500 bg-white'
      } p-3 shadow-md`}>
      <div className="flex items-center gap-2">
        <div
          className={`flex h-8 w-8 items-center justify-center rounded-full ${
            data.enabled === false
              ? 'bg-gray-200 text-gray-500'
              : 'bg-indigo-100 text-indigo-500'
          }`}>
          {getIcon()}
        </div>
        <div>
          <div className="text-sm font-medium">{data.label}</div>
          <div className="text-xs text-gray-500">{getDescription()}</div>
        </div>
      </div>

      {/* 显示变量指示器 */}
      {hasVariables && (
        <div className="mt-2 flex items-center gap-1">
          <Variable className="h-3 w-3 text-gray-500" />
          <span className="text-xs text-gray-500">
            {data.variables.length} 个变量
          </span>
        </div>
      )}

      {/* 输入和输出连接点 */}
      <Handle
        type="target"
        position={Position.Top}
        id="in"
        isConnectable={isConnectable}
        className={`h-3 w-3 ${
          data.enabled === false ? 'bg-gray-400' : 'bg-indigo-500'
        }`}
      />
      <Handle
        type="source"
        position={Position.Bottom}
        id="out"
        isConnectable={isConnectable}
        className={`h-3 w-3 ${
          data.enabled === false ? 'bg-gray-400' : 'bg-indigo-500'
        }`}
      />
    </div>
  )
}

export default IndustrialAppNode
