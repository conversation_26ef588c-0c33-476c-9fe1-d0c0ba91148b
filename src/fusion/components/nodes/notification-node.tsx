/**
 * 迁移自: components/nodes/notification-node.tsx
 * 迁移时间: 2025-01-14T09:44:00.000Z
 *
 * 迁移说明:
 * - 已添加 TypeScript 类型定义
 * - 保持所有样式、业务逻辑完全不变
 *
 * 注意: 本文件保持与原始文件100%功能一致性
 */

import { Mail, Phone, Smartphone, Bell, AlertTriangle, Variable } from "lucide-react"
import { <PERSON>le, Position } from "reactflow"

export function NotificationNode({ data, isConnectable }: { data: any; isConnectable: boolean }) {

  // 根据节点类型选择图标
  const getIcon = () => {
    switch (data.type) {
      case "email-notification":
        return <Mail className="h-5 w-5" />
      case "sms-notification":
        return <Phone className="h-5 w-5" />
      case "app-notification":
        return <Smartphone className="h-5 w-5" />
      case "alarm-action":
        return <Bell className="h-5 w-5" />
      case "alarm-acknowledge":
        return <AlertTriangle className="h-5 w-5" />
      default:
        return <Bell className="h-5 w-5" />
    }
  }

  // 获取节点描述
  const getDescription = () => {
    if (data.type === "email-notification") {
      return `收件人: ${data.recipients ? (data.recipients.length > 20 ? data.recipients.substring(0, 20) + "..." : data.recipients) : "未设置"}`
    } else if (data.type === "sms-notification") {
      return `接收人: ${data.phoneNumbers ? (data.phoneNumbers.length > 20 ? data.phoneNumbers.substring(0, 20) + "..." : data.phoneNumbers) : "未设置"}`
    } else if (data.type === "app-notification") {
      return `推送至: ${data.userGroups || "所有用户"}`
    } else if (data.type === "alarm-action") {
      return `报警级别: ${data.alarmLevel || "未设置"}`
    } else if (data.type === "alarm-acknowledge") {
      return `确认报警ID: ${data.alarmId || "自动"}`
    }
    return "通知"
  }

  // 检查节点是否有变量
  const hasVariables = data.variables && data.variables.length > 0

  return (
    <div
      className={`rounded-lg border-2 ${data.enabled === false ? "border-gray-300 bg-gray-50" : "border-orange-500 bg-white"} p-3 shadow-md`}
    >
      <div className="flex items-center gap-2">
        <div
          className={`flex h-8 w-8 items-center justify-center rounded-full ${data.enabled === false ? "bg-gray-200 text-gray-500" : "bg-orange-100 text-orange-500"}`}
        >
          {getIcon()}
        </div>
        <div>
          <div className="text-sm font-medium">{data.label}</div>
          <div className="text-xs text-gray-500">{getDescription()}</div>
        </div>
      </div>

      {/* 显示变量指示器 */}
      {hasVariables && (
        <div className="mt-2 flex items-center gap-1">
          <Variable className="h-3 w-3 text-gray-500" />
          <span className="text-xs text-gray-500">{data.variables.length} 个变量</span>
        </div>
      )}

      {/* 输入和输出连接点 */}
      <Handle
        type="target"
        position={Position.Top}
        id="in"
        isConnectable={isConnectable}
        className={`h-3 w-3 ${data.enabled === false ? "bg-gray-400" : "bg-orange-500"}`}
      />
      <Handle
        type="source"
        position={Position.Bottom}
        id="out"
        isConnectable={isConnectable}
        className={`h-3 w-3 ${data.enabled === false ? "bg-gray-400" : "bg-orange-500"}`}
      />
    </div>
  )
}

export default NotificationNode
