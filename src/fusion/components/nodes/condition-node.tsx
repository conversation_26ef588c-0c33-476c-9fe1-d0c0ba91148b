import { GitBranch, Variable } from 'lucide-react'
import { <PERSON><PERSON>, Position } from 'reactflow'

export function ConditionNode({ data, isConnectable }) {
  // Placeholder for Position until it's loaded
  const positionValue = {
    Bottom: 'bottom',
    Top: 'top',
    Left: 'left',
    Right: 'right',
  }

  // Get the actual Position value when available
  const getPosition = (pos) => {
    if (Position && Position[pos]) {
      return Position[pos]
    }
    return positionValue[pos] || 'bottom'
  }

  // 获取节点描述
  const getDescription = () => {
    if (data.conditionType === 'expression') {
      // 确保表达式中的特殊字符被安全处理
      const safeExpression = data.expression || '条件表达式'
      return safeExpression.length > 15
        ? safeExpression.substring(0, 15) + '...'
        : safeExpression
    } else if (data.conditionType === 'comparison') {
      return '比较条件'
    } else if (data.conditionType === 'exists') {
      return '存在检查'
    }
    return '条件判断'
  }

  // 检查节点是否有变量
  const hasVariables = data.variables && data.variables.length > 0

  return (
    <div
      className={`rounded-lg border-2 ${
        data.enabled === false
          ? 'border-gray-300 bg-gray-50'
          : 'border-amber-500 bg-white'
      } p-3 shadow-md`}>
      <div className="flex items-center gap-2">
        <div
          className={`flex h-8 w-8 items-center justify-center rounded-full ${
            data.enabled === false
              ? 'bg-gray-200 text-gray-500'
              : 'bg-amber-100 text-amber-500'
          }`}>
          <GitBranch className="h-5 w-5" />
        </div>
        <div>
          <div className="text-sm font-medium">{data.label}</div>
          <div className="text-xs text-gray-500">{getDescription()}</div>
        </div>
      </div>

      {/* 显示变量指示器 */}
      {hasVariables && (
        <div className="mt-2 flex items-center gap-1">
          <Variable className="h-3 w-3 text-gray-500" />
          <span className="text-xs text-gray-500">
            {data.variables.length} 个变量
          </span>
        </div>
      )}

      {/* 输入和多个输出连接点 */}
      <>
        <Handle
          type="target"
          position={getPosition('Top')}
          id="in"
          isConnectable={isConnectable}
          className={`h-3 w-3 ${
            data.enabled === false ? 'bg-gray-400' : 'bg-amber-500'
          }`}
        />
        <Handle
          type="source"
          position={getPosition('Bottom')}
          id="out-true"
          isConnectable={isConnectable}
          className={`left-1/3 h-3 w-3 ${
            data.enabled === false ? 'bg-gray-400' : 'bg-amber-500'
          }`}
        />
        <div className="absolute bottom-[-20px] left-[calc(33%-10px)] text-xs">
          是
        </div>
        <Handle
          type="source"
          position={getPosition('Bottom')}
          id="out-false"
          isConnectable={isConnectable}
          className={`left-2/3 h-3 w-3 ${
            data.enabled === false ? 'bg-gray-400' : 'bg-amber-500'
          }`}
        />
        <div className="absolute bottom-[-20px] left-[calc(67%-10px)] text-xs">
          否
        </div>
      </>
    </div>
  )
}

export default ConditionNode
