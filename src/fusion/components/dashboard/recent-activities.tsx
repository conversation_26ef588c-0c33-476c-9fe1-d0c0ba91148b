import { useState, useEffect } from 'react'
import { Card, CardContent, CardHeader, CardTitle } from '@/components/ui/card'
import { Badge } from '@/components/ui/badge'
import {
  CalendarClock,
  AlertCircle,
  Server,
  Database,
  Workflow,
} from 'lucide-react'
import {
  notificationAPI,
  type NotificationResponse,
} from '@/lib/api/notifications'

// 定义活动数据类型
interface Activity {
  id: number
  type: string
  title: string
  device: string
  time: string
  status: 'critical' | 'warning' | 'success' | 'info'
  icon: React.ComponentType<{ className?: string }>
}

export function RecentActivities() {
  // 添加状态管理
  const [activities, setActivities] = useState<Activity[]>([])
  const [loading, setLoading] = useState(true)
  const [error, setError] = useState<string | null>(null)

  // 根据来源选择图标
  const getIconBySource = (source: string) => {
    switch (source) {
      case 'DeviceManagement':
      case 'DeviceMonitoring':
        return Server
      case 'DataCollection':
      case 'DataForwarding':
        return Database
      case 'WorkflowEngine':
        return Workflow
      default:
        return AlertCircle
    }
  }

  // 格式化相对时间
  const formatRelativeTime = (isoString: string): string => {
    const now = new Date()
    const date = new Date(isoString)
    const diffInMs = now.getTime() - date.getTime()
    const diffInMinutes = Math.floor(diffInMs / (1000 * 60))
    const diffInHours = Math.floor(diffInMinutes / 60)
    const diffInDays = Math.floor(diffInHours / 24)

    if (diffInMinutes < 1) {
      return '刚刚'
    } else if (diffInMinutes < 60) {
      return `${diffInMinutes}分钟前`
    } else if (diffInHours < 24) {
      return `${diffInHours}小时前`
    } else {
      return `${diffInDays}天前`
    }
  }

  // 数据转换函数
  const transformNotificationToActivity = (
    notification: NotificationResponse
  ): Activity => {
    return {
      id: notification.id,
      type: notification.source,
      title: notification.title,
      device: notification.source || '未知设备',
      time: formatRelativeTime(notification.createdTime),
      status: notification.type,
      icon: getIconBySource(notification.source),
    }
  }

  // 获取活动数据
  useEffect(() => {
    const fetchActivities = async () => {
      try {
        setLoading(true)
        setError(null)

        // 获取最近的5条通知作为活动数据
        const response = await notificationAPI.getNotifications({
          page: 1,
          pageSize: 5,
        })

        // 转换数据格式
        const transformedActivities = response.items.map(
          transformNotificationToActivity
        )
        setActivities(transformedActivities)
      } catch (err) {
        console.error('获取活动数据失败:', err)
        setError('获取活动数据失败，请稍后重试')
      } finally {
        setLoading(false)
      }
    }

    fetchActivities()
  }, [])

  // 根据状态返回对应的Badge变体
  const getStatusVariant = (status: string) => {
    switch (status) {
      case 'critical':
        return 'destructive'
      case 'warning':
        return 'warning'
      case 'success':
        return 'success'
      case 'info':
        return 'secondary'
      default:
        return 'outline'
    }
  }

  // 渲染加载状态
  if (loading) {
    return (
      <Card className="dark:border-gray-700 dark:bg-gray-800/50 transition-all">
        <CardHeader className="flex flex-row items-center justify-between pb-2">
          <CardTitle className="text-xl font-bold">最近活动</CardTitle>
          <CalendarClock className="h-5 w-5 text-gray-500" />
        </CardHeader>
        <CardContent>
          <div className="space-y-4">
            {/* 加载骨架屏 */}
            {[1, 2, 3, 4, 5].map((index) => (
              <div
                key={index}
                className="flex items-start space-x-4 border-b pb-4 last:border-0">
                <div className="rounded-full p-2 bg-gray-200 dark:bg-gray-700 animate-pulse">
                  <div className="h-5 w-5 bg-gray-300 dark:bg-gray-600 rounded"></div>
                </div>
                <div className="flex-1 space-y-2">
                  <div className="h-4 bg-gray-200 dark:bg-gray-700 rounded animate-pulse"></div>
                  <div className="h-3 bg-gray-200 dark:bg-gray-700 rounded w-3/4 animate-pulse"></div>
                  <div className="h-3 bg-gray-200 dark:bg-gray-700 rounded w-1/2 animate-pulse"></div>
                </div>
              </div>
            ))}
          </div>
        </CardContent>
      </Card>
    )
  }

  // 渲染错误状态
  if (error) {
    return (
      <Card className="dark:border-gray-700 dark:bg-gray-800/50 transition-all">
        <CardHeader className="flex flex-row items-center justify-between pb-2">
          <CardTitle className="text-xl font-bold">最近活动</CardTitle>
          <CalendarClock className="h-5 w-5 text-gray-500" />
        </CardHeader>
        <CardContent>
          <div className="text-center py-8">
            <AlertCircle className="h-12 w-12 text-red-500 mx-auto mb-4" />
            <p className="text-gray-500 dark:text-gray-400">{error}</p>
          </div>
        </CardContent>
      </Card>
    )
  }

  // 渲染空数据状态
  if (activities.length === 0) {
    return (
      <Card className="dark:border-gray-700 dark:bg-gray-800/50 transition-all">
        <CardHeader className="flex flex-row items-center justify-between pb-2">
          <CardTitle className="text-xl font-bold">最近活动</CardTitle>
          <CalendarClock className="h-5 w-5 text-gray-500" />
        </CardHeader>
        <CardContent>
          <div className="text-center py-8">
            <CalendarClock className="h-12 w-12 text-gray-400 mx-auto mb-4" />
            <p className="text-gray-500 dark:text-gray-400">暂无最近活动</p>
          </div>
        </CardContent>
      </Card>
    )
  }

  return (
    <Card className="dark:border-gray-700 dark:bg-gray-800/50 transition-all">
      <CardHeader className="flex flex-row items-center justify-between pb-2">
        <CardTitle className="text-xl font-bold">最近活动</CardTitle>
        <CalendarClock className="h-5 w-5 text-gray-500" />
      </CardHeader>
      <CardContent>
        <div className="space-y-4">
          {activities.map((activity) => (
            <div
              key={activity.id}
              className="flex items-start space-x-4 border-b pb-4 last:border-0">
              <div
                className={`rounded-full p-2 ${
                  activity.status === 'critical'
                    ? 'bg-red-100 text-red-600 dark:bg-red-900/50 dark:text-red-400'
                    : activity.status === 'warning'
                    ? 'bg-yellow-100 text-yellow-600 dark:bg-yellow-900/50 dark:text-yellow-400'
                    : activity.status === 'success'
                    ? 'bg-green-100 text-green-600 dark:bg-green-900/50 dark:text-green-400'
                    : 'bg-blue-100 text-blue-600 dark:bg-blue-900/50 dark:text-blue-400'
                }`}>
                <activity.icon className="h-5 w-5" />
              </div>
              <div className="flex-1">
                <div className="flex items-center justify-between">
                  <h3 className="font-medium">{activity.title}</h3>
                  <Badge variant={getStatusVariant(activity.status)}>
                    {activity.status === 'critical'
                      ? '紧急'
                      : activity.status === 'warning'
                      ? '警告'
                      : activity.status === 'success'
                      ? '成功'
                      : '信息'}
                  </Badge>
                </div>
                <p className="text-sm text-gray-500 dark:text-gray-300">
                  {activity.device}
                </p>
                <p className="text-xs text-gray-400 dark:text-gray-400 mt-1">
                  {activity.time}
                </p>
              </div>
            </div>
          ))}
        </div>
      </CardContent>
    </Card>
  )
}
