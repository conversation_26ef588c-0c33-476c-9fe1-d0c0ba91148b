import { useState, useEffect } from 'react'
import { <PERSON>, Card<PERSON>ontent, CardHeader, CardTitle } from '@/components/ui/card'
import { Progress } from '@/components/ui/progress'
import {
  Activity,
  Server,
  Database,
  MemoryStickIcon as Memory,
} from 'lucide-react'

export function SystemStatus() {
  // 模拟系统状态数据
  const [systemStatus, setSystemStatus] = useState({
    cpuUsage: 0,
    memoryUsage: 0,
    diskUsage: 0,
    deviceCount: 0,
    deviceOnline: 0,
    dataPoints: 0,
  })

  // 模拟获取系统状态数据
  useEffect(() => {
    // 实际应用中，这里应该从API获取真实数据
    const mockData = {
      cpuUsage: Math.floor(Math.random() * 60) + 10,
      memoryUsage: Math.floor(Math.random() * 70) + 20,
      diskUsage: Math.floor(Math.random() * 50) + 30,
      deviceCount: Math.floor(Math.random() * 100) + 50,
      deviceOnline: Math.floor(Math.random() * 50) + 30,
      dataPoints: Math.floor(Math.random() * 10000) + 5000,
    }

    setSystemStatus(mockData)
  }, [])

  return (
    <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-4 mb-8">
      <Card className="dark:border-gray-700 dark:bg-gray-800/50 transition-all">
        <CardHeader className="flex flex-row items-center justify-between pb-2">
          <CardTitle className="text-sm font-medium">CPU 使用率</CardTitle>
          <Activity className="h-4 w-4 text-gray-500" />
        </CardHeader>
        <CardContent>
          <div className="text-2xl font-bold">{systemStatus.cpuUsage}%</div>
          <Progress
            value={systemStatus.cpuUsage}
            className="h-2 mt-2 dark:bg-gray-700 dark:text-primary"
          />
        </CardContent>
      </Card>

      <Card className="dark:border-gray-700 dark:bg-gray-800/50 transition-all">
        <CardHeader className="flex flex-row items-center justify-between pb-2">
          <CardTitle className="text-sm font-medium">内存使用率</CardTitle>
          <Memory className="h-4 w-4 text-gray-500" />
        </CardHeader>
        <CardContent>
          <div className="text-2xl font-bold">{systemStatus.memoryUsage}%</div>
          <Progress
            value={systemStatus.memoryUsage}
            className="h-2 mt-2 dark:bg-gray-700 dark:text-primary"
          />
        </CardContent>
      </Card>

      <Card className="dark:border-gray-700 dark:bg-gray-800/50 transition-all">
        <CardHeader className="flex flex-row items-center justify-between pb-2">
          <CardTitle className="text-sm font-medium">设备状态</CardTitle>
          <Server className="h-4 w-4 text-gray-500" />
        </CardHeader>
        <CardContent>
          <div className="text-2xl font-bold">
            {systemStatus.deviceOnline}/{systemStatus.deviceCount}
          </div>
          <Progress
            value={(systemStatus.deviceOnline / systemStatus.deviceCount) * 100}
            className="h-2 mt-2 dark:bg-gray-700 dark:text-primary"
          />
          <div className="text-xs text-gray-500 dark:text-gray-300 mt-1">
            在线设备
          </div>
        </CardContent>
      </Card>

      <Card className="dark:border-gray-700 dark:bg-gray-800/50 transition-all">
        <CardHeader className="flex flex-row items-center justify-between pb-2">
          <CardTitle className="text-sm font-medium">数据点数</CardTitle>
          <Database className="h-4 w-4 text-gray-500" />
        </CardHeader>
        <CardContent>
          <div className="text-2xl font-bold">
            {systemStatus.dataPoints.toLocaleString()}
          </div>
          <div className="text-xs text-gray-500 dark:text-gray-300 mt-3">
            总数据点
          </div>
        </CardContent>
      </Card>
    </div>
  )
}
