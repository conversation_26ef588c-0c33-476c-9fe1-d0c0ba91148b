import { useState, useEffect, useCallback } from 'react'
import { useNavigate } from 'react-router-dom'
import { MainLayout } from '@/components/layout/main-layout'
import {
  Card,
  CardContent,
  CardDescription,
  CardHeader,
  CardTitle,
} from '@/components/ui/card'
import { <PERSON><PERSON>, <PERSON><PERSON><PERSON>ontent, <PERSON><PERSON><PERSON>ist, TabsTrigger } from '@/components/ui/tabs'
import { Button } from '@/components/ui/button'
import { Badge } from '@/components/ui/badge'
import { Input } from '@/components/ui/input'
import {
  AlertTriangle,
  Bell,
  CheckCircle2,
  Info,
  Search,
  Trash2,
  Wifi,
  WifiOff,
} from 'lucide-react'
import { toast } from '@/hooks/use-toast'
import {
  notificationAPI,
  getSourceDisplayName,
  transformNotificationResponse,
  type NotificationQueryParams,
} from '@/lib/api/notifications'
import { useSignalR, useSignalREvent } from '@/lib/signalr/signalr-context'

// 通知类型定义
type NotificationType = 'all' | 'critical' | 'warning' | 'success' | 'info'

interface Notification {
  id: number
  title: string
  message: string
  time: string
  date: string
  read: boolean
  type: 'critical' | 'warning' | 'success' | 'info'
  source: string
  details?: string
  actionUrl?: string
}

export default function NotificationsPage() {
  const navigate = useNavigate()

  // 状态管理
  const [notifications, setNotifications] = useState<Notification[]>([])
  const [loading, setLoading] = useState(true)
  const [activeTab, setActiveTab] = useState<NotificationType>('all')
  const [searchQuery, setSearchQuery] = useState('')
  const [unreadCount, setUnreadCount] = useState(0)
  const [currentPage, setCurrentPage] = useState(1)
  const [totalPages, setTotalPages] = useState(1)
  const [error, setError] = useState<string | null>(null)

  const pageSize = 20

  // 使用公共SignalR连接
  const { connectionState } = useSignalR()
  const wsConnected = connectionState === 'connected'

  // 监听通知事件
  useSignalREvent(
    'ReceiveNotification',
    useCallback((newNotification: any) => {
      console.log('New notification received:', newNotification)

      try {
        // 转换通知格式
        const transformedNotification =
          transformNotificationResponse(newNotification)

        // 添加新通知到列表顶部
        setNotifications((prev) => [transformedNotification, ...prev])

        // 更新未读数量
        if (!transformedNotification.read) {
          setUnreadCount((prev) => prev + 1)
        }

        // 显示 toast 通知
        toast({
          title: transformedNotification.title,
          description: transformedNotification.message,
          variant:
            transformedNotification.type === 'critical'
              ? 'destructive'
              : 'default',
        })
      } catch (err) {
        console.error('Failed to process received notification:', err)
      }
    }, [])
  )

  // 监听连接状态变化
  useEffect(() => {
    if (connectionState === 'connected') {
      toast({
        title: '通知连接已建立',
        description: '现在可以接收实时通知',
        variant: 'default',
      })
    } else if (connectionState === 'disconnected') {
      toast({
        title: '连接已断开',
        description: '通知连接已断开，正在尝试重连...',
        variant: 'destructive',
        duration: 5000,
      })
    }
  }, [connectionState])

  // 加载通知数据
  const loadNotifications = useCallback(
    async (params: NotificationQueryParams = {}) => {
      try {
        setLoading(true)
        setError(null)

        const queryParams: NotificationQueryParams = {
          page: params.page || currentPage,
          pageSize,
          ...params,
        }

        // 添加类型过滤
        if (params.type || activeTab !== 'all') {
          queryParams.type = params.type || activeTab
        }

        // 添加关键字搜索
        if (params.keyword || searchQuery) {
          queryParams.keyword = params.keyword || searchQuery
        }

        const response = await notificationAPI.getNotifications(queryParams)

        // 转换 API 响应为前端格式
        const transformedNotifications = response.items.map(
          transformNotificationResponse
        )

        setNotifications(transformedNotifications)
        setTotalPages(response.totalPages)

        // 加载未读数量
        const unreadResponse = await notificationAPI.getUnreadCount()
        setUnreadCount(unreadResponse.count)
      } catch (err) {
        console.error('Failed to load notifications:', err)
        setError('加载通知失败，请稍后重试')
        toast({
          title: '加载失败',
          description: '无法加载通知数据，请检查网络连接',
          variant: 'destructive',
        })
      } finally {
        setLoading(false)
      }
    },
    [currentPage, activeTab, searchQuery]
  )

  // 创建一个不依赖状态的初始加载函数
  const initialLoadNotifications = useCallback(async () => {
    try {
      setLoading(true)
      setError(null)

      const queryParams = {
        page: 1,
        pageSize,
      }

      const response = await notificationAPI.getNotifications(queryParams)

      // 转换 API 响应为前端格式
      const transformedNotifications = response.items.map(
        transformNotificationResponse
      )

      setNotifications(transformedNotifications)
      setTotalPages(response.totalPages)

      // 加载未读数量
      const unreadResponse = await notificationAPI.getUnreadCount()
      setUnreadCount(unreadResponse.count)
    } catch (err) {
      console.error('Failed to load notifications:', err)
      setError('加载通知失败，请稍后重试')
      toast({
        title: '加载失败',
        description: '无法加载通知数据，请检查网络连接',
        variant: 'destructive',
      })
    } finally {
      setLoading(false)
    }
  }, [])

  // 初始化数据和连接
  useEffect(() => {
    initialLoadNotifications()

    return () => {
      // 清理逻辑
    }
  }, [initialLoadNotifications])

  // 监听搜索和筛选变化
  useEffect(() => {
    setCurrentPage(1)
    const debounceTimer = setTimeout(() => {
      loadNotifications()
    }, 300) // 防抖搜索

    return () => clearTimeout(debounceTimer)
  }, [searchQuery, activeTab, loadNotifications])

  // 标记通知为已读
  const markAsRead = async (id: number) => {
    try {
      await notificationAPI.markAsRead(id)

      setNotifications((prev) =>
        prev.map((notification) =>
          notification.id === id
            ? { ...notification, read: true }
            : notification
        )
      )

      // 减少未读数量
      setUnreadCount((prev) => Math.max(0, prev - 1))
    } catch (err) {
      console.error('Failed to mark as read:', err)
      toast({
        title: '操作失败',
        description: '无法标记为已读，请稍后重试',
        variant: 'destructive',
      })
    }
  }

  // 标记所有通知为已读
  const markAllAsRead = async () => {
    try {
      await notificationAPI.markAllAsRead()

      setNotifications((prev) =>
        prev.map((notification) => ({ ...notification, read: true }))
      )
      setUnreadCount(0)

      toast({
        title: '操作成功',
        description: '所有通知已标记为已读',
      })
    } catch (err) {
      console.error('Failed to mark all as read:', err)
      toast({
        title: '操作失败',
        description: '无法标记所有为已读，请稍后重试',
        variant: 'destructive',
      })
    }
  }

  // 删除通知
  const deleteNotification = async (id: number) => {
    try {
      await notificationAPI.deleteNotification(id)

      setNotifications((prev) =>
        prev.filter((notification) => notification.id !== id)
      )

      toast({
        title: '删除成功',
        description: '通知已删除',
      })
    } catch (err) {
      console.error('Failed to delete notification:', err)
      toast({
        title: '删除失败',
        description: '无法删除通知，请稍后重试',
        variant: 'destructive',
      })
    }
  }

  // 删除已读通知
  const deleteReadNotifications = async () => {
    try {
      await notificationAPI.clearReadNotifications()

      setNotifications((prev) =>
        prev.filter((notification) => !notification.read)
      )

      toast({
        title: '清理成功',
        description: '已读通知已清除',
      })
    } catch (err) {
      console.error('Failed to clear read notifications:', err)
      toast({
        title: '清理失败',
        description: '无法清除已读通知，请稍后重试',
        variant: 'destructive',
      })
    }
  }

  // 过滤通知
  const filteredNotifications = notifications.filter((notification) => {
    // 根据标签过滤
    if (activeTab !== 'all' && notification.type !== activeTab) {
      return false
    }

    // 根据搜索查询过滤（客户端二次过滤）
    if (searchQuery) {
      const query = searchQuery.toLowerCase()
      return (
        notification.title.toLowerCase().includes(query) ||
        notification.message.toLowerCase().includes(query) ||
        notification.source.toLowerCase().includes(query)
      )
    }

    return true
  })

  // 按日期分组通知
  const groupedNotifications: Record<string, Notification[]> = {}

  filteredNotifications.forEach((notification) => {
    if (!groupedNotifications[notification.date]) {
      groupedNotifications[notification.date] = []
    }
    groupedNotifications[notification.date].push(notification)
  })

  // 格式化日期
  const formatDate = (dateString: string) => {
    const date = new Date(dateString)
    const today = new Date()
    const yesterday = new Date(today)
    yesterday.setDate(yesterday.getDate() - 1)

    if (dateString === today.toISOString().split('T')[0]) {
      return '今天'
    } else if (dateString === yesterday.toISOString().split('T')[0]) {
      return '昨天'
    } else {
      return date.toLocaleDateString('zh-CN', {
        year: 'numeric',
        month: 'long',
        day: 'numeric',
      })
    }
  }

  // 获取通知图标
  const getNotificationIcon = (type: string) => {
    switch (type) {
      case 'critical':
        return <AlertTriangle className="h-4 w-4" />
      case 'warning':
        return <AlertTriangle className="h-4 w-4" />
      case 'success':
        return <CheckCircle2 className="h-4 w-4" />
      case 'info':
        return <Info className="h-4 w-4" />
      default:
        return <Bell className="h-4 w-4" />
    }
  }

  // 获取通知类型标签
  const getNotificationTypeLabel = (type: string) => {
    switch (type) {
      case 'critical':
        return '紧急'
      case 'warning':
        return '警告'
      case 'success':
        return '成功'
      case 'info':
        return '信息'
      default:
        return '未知'
    }
  }

  // 获取通知类型变体
  const getNotificationVariant = (type: string) => {
    switch (type) {
      case 'critical':
        return 'destructive'
      case 'warning':
        return 'warning'
      case 'success':
        return 'success'
      case 'info':
        return 'secondary'
      default:
        return 'outline'
    }
  }

  // 获取通知类型颜色
  const getNotificationColor = (type: string) => {
    switch (type) {
      case 'critical':
        return 'bg-red-100 text-red-600 dark:bg-red-900 dark:text-red-300'
      case 'warning':
        return 'bg-yellow-100 text-yellow-600 dark:bg-yellow-900 dark:text-yellow-300'
      case 'success':
        return 'bg-green-100 text-green-600 dark:bg-green-900 dark:text-green-300'
      case 'info':
        return 'bg-blue-100 text-blue-600 dark:bg-blue-900 dark:text-blue-300'
      default:
        return 'bg-gray-100 text-gray-600 dark:bg-gray-800 dark:text-gray-300'
    }
  }

  // 处理通知点击
  const handleNotificationClick = (notification: Notification) => {
    if (!notification.read) {
      markAsRead(notification.id)
    }
    if (notification.actionUrl) {
      navigate(notification.actionUrl)
    }
  }

  // 获取类型计数
  const getTypeCount = (type: NotificationType) => {
    if (type === 'all') return notifications.length
    return notifications.filter((n) => n.type === type).length
  }

  return (
    <MainLayout>
      <div className="container mx-auto py-6 space-y-6">
        {error && (
          <Card className="border-red-200 bg-red-50 dark:border-red-800 dark:bg-red-950">
            <CardContent className="pt-6">
              <div className="flex items-center justify-between">
                <div className="flex items-center space-x-2">
                  <AlertTriangle className="h-4 w-4 text-red-600" />
                  <span className="text-red-800 dark:text-red-200">
                    {error}
                  </span>
                </div>
                <Button
                  variant="outline"
                  size="sm"
                  onClick={() => {
                    setError(null)
                    loadNotifications()
                  }}
                  className="ml-auto">
                  重试
                </Button>
              </div>
            </CardContent>
          </Card>
        )}

        <div className="flex flex-col space-y-6">
          <Card>
            <CardHeader className="pb-3">
              <div className="flex flex-col md:flex-row md:items-center md:justify-between gap-4">
                <div>
                  <CardTitle className="flex items-center gap-2">
                    <Bell className="h-5 w-5" />
                    通知中心
                    {loading && (
                      <div className="inline-block animate-spin rounded-full h-4 w-4 border-2 border-primary border-t-transparent"></div>
                    )}
                  </CardTitle>
                  <CardDescription className="flex items-center gap-4">
                    <span>
                      {unreadCount > 0
                        ? `您有 ${unreadCount} 条未读通知`
                        : '没有未读通知'}
                    </span>
                    {/* WebSocket 连接状态指示器 */}
                    <div className="flex items-center space-x-1">
                      {wsConnected ? (
                        <>
                          <Wifi className="h-3 w-3 text-green-600 dark:text-green-400" />
                          <span className="text-xs text-green-600 dark:text-green-400">
                            已连接
                          </span>
                        </>
                      ) : (
                        <>
                          <WifiOff className="h-3 w-3 text-red-600 dark:text-red-400" />
                          <span className="text-xs text-red-600 dark:text-red-400">
                            未连接
                          </span>
                        </>
                      )}
                    </div>
                  </CardDescription>
                </div>

                <div className="flex flex-col sm:flex-row gap-2">
                  <div className="relative">
                    <Search className="absolute left-2 top-1/2 transform -translate-y-1/2 h-4 w-4 text-muted-foreground" />
                    <Input
                      placeholder="搜索通知..."
                      className="pl-8 w-full sm:w-[200px]"
                      value={searchQuery}
                      onChange={(e) => setSearchQuery(e.target.value)}
                    />
                  </div>

                  <div className="flex gap-2">
                    <Button
                      variant="outline"
                      size="sm"
                      onClick={markAllAsRead}
                      disabled={unreadCount === 0 || loading}>
                      全部标为已读
                    </Button>
                    <Button
                      variant="outline"
                      size="sm"
                      onClick={deleteReadNotifications}
                      disabled={notifications.every((n) => !n.read) || loading}>
                      删除已读
                    </Button>
                  </div>
                </div>
              </div>
            </CardHeader>

            <CardContent>
              <Tabs
                defaultValue="all"
                value={activeTab}
                onValueChange={(value) =>
                  setActiveTab(value as NotificationType)
                }>
                <TabsList className="mb-4">
                  <TabsTrigger value="all">
                    全部
                    <Badge className="ml-2" variant="outline">
                      {getTypeCount('all')}
                    </Badge>
                  </TabsTrigger>
                  <TabsTrigger value="critical">
                    紧急
                    <Badge className="ml-2" variant="destructive">
                      {getTypeCount('critical')}
                    </Badge>
                  </TabsTrigger>
                  <TabsTrigger value="warning">
                    警告
                    <Badge className="ml-2" variant="warning">
                      {getTypeCount('warning')}
                    </Badge>
                  </TabsTrigger>
                  <TabsTrigger value="success">
                    成功
                    <Badge className="ml-2" variant="success">
                      {getTypeCount('success')}
                    </Badge>
                  </TabsTrigger>
                  <TabsTrigger value="info">
                    信息
                    <Badge className="ml-2" variant="secondary">
                      {getTypeCount('info')}
                    </Badge>
                  </TabsTrigger>
                </TabsList>

                <TabsContent value={activeTab} className="mt-0">
                  {loading ? (
                    <div className="space-y-4">
                      {/* 骨架屏 */}
                      {[...Array(3)].map((_, index) => (
                        <div
                          key={index}
                          className="p-4 rounded-lg border animate-pulse">
                          <div className="flex items-start justify-between">
                            <div className="flex items-start space-x-4 flex-1">
                              <div className="rounded-full bg-gray-200 dark:bg-gray-700 h-10 w-10"></div>
                              <div className="space-y-2 flex-1">
                                <div className="flex items-center space-x-2">
                                  <div className="h-4 bg-gray-200 dark:bg-gray-700 rounded w-1/3"></div>
                                  <div className="h-5 bg-gray-200 dark:bg-gray-700 rounded w-16"></div>
                                </div>
                                <div className="h-3 bg-gray-200 dark:bg-gray-700 rounded w-3/4"></div>
                                <div className="h-3 bg-gray-200 dark:bg-gray-700 rounded w-1/2"></div>
                                <div className="flex items-center space-x-2">
                                  <div className="h-3 bg-gray-200 dark:bg-gray-700 rounded w-20"></div>
                                  <div className="h-3 bg-gray-200 dark:bg-gray-700 rounded w-16"></div>
                                </div>
                              </div>
                            </div>
                            <div className="h-8 w-8 bg-gray-200 dark:bg-gray-700 rounded"></div>
                          </div>
                        </div>
                      ))}
                    </div>
                  ) : Object.keys(groupedNotifications).length === 0 ? (
                    <div className="text-center py-12">
                      <Bell className="mx-auto h-12 w-12 text-muted-foreground opacity-20" />
                      <h3 className="mt-4 text-lg font-medium">没有通知</h3>
                      <p className="mt-2 text-sm text-muted-foreground">
                        {searchQuery ? '没有匹配的通知' : '当前没有通知'}
                      </p>
                      {searchQuery && (
                        <Button
                          variant="outline"
                          size="sm"
                          onClick={() => setSearchQuery('')}
                          className="mt-4">
                          清除搜索
                        </Button>
                      )}
                    </div>
                  ) : (
                    <div className="space-y-6">
                      {Object.entries(groupedNotifications)
                        .sort(([dateA], [dateB]) => dateB.localeCompare(dateA))
                        .map(([date, notifications]) => (
                          <div key={date} className="space-y-2">
                            <h3 className="text-sm font-medium text-muted-foreground sticky top-0 bg-background py-1">
                              {formatDate(date)}
                            </h3>
                            <div className="space-y-2">
                              {notifications.map((notification) => (
                                <div
                                  key={notification.id}
                                  className={`p-4 rounded-lg border ${
                                    !notification.read ? 'bg-accent/50' : ''
                                  } hover:bg-accent transition-colors`}>
                                  <div className="flex items-start justify-between">
                                    <div
                                      className="flex-1 cursor-pointer"
                                      onClick={() =>
                                        handleNotificationClick(notification)
                                      }>
                                      <div className="flex items-start space-x-4">
                                        <div
                                          className={`rounded-full p-2 ${getNotificationColor(
                                            notification.type
                                          )}`}>
                                          {getNotificationIcon(
                                            notification.type
                                          )}
                                        </div>
                                        <div className="space-y-1">
                                          <div className="flex items-center space-x-2">
                                            <h4 className="font-medium">
                                              {notification.title}
                                            </h4>
                                            <Badge
                                              variant={getNotificationVariant(
                                                notification.type
                                              )}>
                                              {getNotificationTypeLabel(
                                                notification.type
                                              )}
                                            </Badge>
                                            {!notification.read && (
                                              <span className="h-2 w-2 rounded-full bg-primary"></span>
                                            )}
                                          </div>
                                          <p className="text-sm text-muted-foreground">
                                            {notification.message}
                                          </p>
                                          {notification.details && (
                                            <p className="text-xs text-muted-foreground">
                                              {notification.details}
                                            </p>
                                          )}
                                          <div className="flex items-center space-x-2 text-xs text-muted-foreground">
                                            <span>
                                              {getSourceDisplayName(
                                                notification.source
                                              )}
                                            </span>
                                            <span>•</span>
                                            <span>{notification.time}</span>
                                          </div>
                                        </div>
                                      </div>
                                    </div>
                                    <Button
                                      variant="ghost"
                                      size="icon"
                                      onClick={() =>
                                        deleteNotification(notification.id)
                                      }
                                      className="h-8 w-8 text-muted-foreground hover:text-foreground"
                                      disabled={loading}>
                                      <Trash2 className="h-4 w-4" />
                                      <span className="sr-only">删除</span>
                                    </Button>
                                  </div>
                                </div>
                              ))}
                            </div>
                          </div>
                        ))}
                    </div>
                  )}
                </TabsContent>
              </Tabs>
            </CardContent>
          </Card>
        </div>
      </div>
    </MainLayout>
  )
}
