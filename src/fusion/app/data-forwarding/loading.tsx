import { Skeleton } from '@/components/ui/skeleton'
import { Button } from '@/components/ui/button'
import {
  Card,
  CardContent,
  CardDescription,
  CardHeader,
  CardTitle,
} from '@/components/ui/card'
import { Plus } from 'lucide-react'
import { MainLayout } from '@/components/layout/main-layout'

export default function DataForwardingLoading() {
  return (
    <MainLayout>
      <div className="container mx-auto py-6 space-y-6">
        <div className="flex justify-between items-center">
          <div>
            <h1 className="text-3xl font-bold tracking-tight">数据转发</h1>
            <p className="text-muted-foreground">
              管理数据转发配置和监控转发状态
            </p>
          </div>
          <Button disabled>
            <Plus className="mr-2 h-4 w-4" />
            添加转发
          </Button>
        </div>

        <Card>
          <CardHeader>
            <CardTitle>转发配置</CardTitle>
            <CardDescription>管理系统中的数据转发配置</CardDescription>
          </CardHeader>
          <CardContent>
            <div className="space-y-4">
              <Skeleton className="h-12 w-full" />
              <Skeleton className="h-64 w-full" />
            </div>
          </CardContent>
        </Card>
      </div>
    </MainLayout>
  )
}
