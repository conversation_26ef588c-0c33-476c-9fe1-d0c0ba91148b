/**
 * 日志管理页面 - 已集成真实API
 *
 * 更新说明:
 * - 已替换模拟数据，使用真实的LogApi接口
 * - 集成LogService和RealLogDataSource进行数据管理
 * - 支持真实的日志文件列表获取、内容查看、搜索和下载功能
 * - 添加完整的错误处理和加载状态
 * - 保持原有UI设计和用户体验
 *
 * 功能特性:
 * - 实时日志文件列表加载
 * - 大文件虚拟化显示支持
 * - 关键词搜索功能
 * - 文件下载功能
 * - 错误处理和重试机制
 */

import { useState, useCallback, useEffect, useRef } from "react"
import {
  Search,
  Filter,
  Download,
  RefreshCw,
  FileText,
  Folder,
  FolderOpen,
  Calendar,
  AlertCircle,
  Info,
  CheckCircle,
  XCircle,
  ChevronLeft,
  ChevronRight,
  Loader2,
  Settings,
  ChevronDown,
  ChevronUp,
} from "lucide-react"
import { Button } from "@/components/ui/button"
import { Input } from "@/components/ui/input"
import { Card, CardContent, CardHeader, CardTitle } from "@/components/ui/card"
import { Badge } from "@/components/ui/badge"
import { ScrollArea } from "@/components/ui/scroll-area"
import { Separator } from "@/components/ui/separator"
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from "@/components/ui/select"
import { Checkbox } from "@/components/ui/checkbox"
import { Label } from "@/components/ui/label"
import { Progress } from "@/components/ui/progress"
import { MainLayout } from '@/components/layout/main-layout'
import { logService, LogFile, LogLine } from '@/lib/services/log-service'
import { RealLogDataSource, DataChunk } from '@/lib/services/real-log-data-source'
import { toast } from '@/components/ui/use-toast'
import {
  DownloadProgressDialog,
  useDownloadManager,
  type DownloadStatus
} from '@/components/ui/download-progress-dialog'
import { DownloadDiagnosticsPanel } from '@/components/ui/download-diagnostics-panel'

// 使用 debounce hook
import { useDebounce } from '@/hooks/use-debounce'

// 高亮文本组件
const HighlightText = ({ text, searchTerm, caseSensitive = false }: {
  text: string
  searchTerm: string
  caseSensitive?: boolean
}) => {
  if (!searchTerm.trim()) {
    return <span>{text}</span>
  }

  const flags = caseSensitive ? 'g' : 'gi'
  const regex = new RegExp(`(${searchTerm.replace(/[.*+?^${}()|[\]\\]/g, '\\$&')})`, flags)
  const parts = text.split(regex)

  return (
    <span>
      {parts.map((part, index) =>
        regex.test(part) ? (
          <mark key={index} className="bg-yellow-200 text-yellow-900 px-1 rounded">
            {part}
          </mark>
        ) : (
          <span key={index}>{part}</span>
        )
      )}
    </span>
  )
}







// 日志级别图标映射
const getLevelIcon = (level?: string) => {
  switch (level) {
    case "error":
      return <XCircle className="w-4 h-4 text-red-500" />
    case "warn":
      return <AlertCircle className="w-4 h-4 text-yellow-500" />
    case "info":
      return <Info className="w-4 h-4 text-blue-500" />
    case "debug":
      return <CheckCircle className="w-4 h-4 text-gray-500" />
    default:
      return <FileText className="w-4 h-4 text-gray-500" />
  }
}

// 日志级别颜色映射
const getLevelColor = (level?: string) => {
  switch (level) {
    case "error":
      return "bg-red-50 border-red-200"
    case "warn":
      return "bg-yellow-50 border-yellow-200"
    case "info":
      return "bg-blue-50 border-blue-200"
    case "debug":
      return "bg-gray-50 border-gray-200"
    default:
      return "bg-white border-gray-200"
  }
}

// 文件树组件
const FileTree = ({
                    files,
                    onFileSelect,
                    selectedFile,
                    expandedFolders,
                    onToggleFolder,
                  }: {
  files: LogFile[]
  onFileSelect: (file: LogFile) => void
  selectedFile: LogFile | null
  expandedFolders: Set<string>
  onToggleFolder: (folderId: string) => void
}) => {
  const renderFile = (file: LogFile, depth = 0) => {
    const isExpanded = expandedFolders.has(file.id)
    const isSelected = selectedFile?.id === file.id

    return (
        <div key={file.id}>
          <div
              className={`flex items-center gap-2 p-2 hover:bg-gray-100 cursor-pointer rounded-md transition-colors ${
                  isSelected ? "bg-blue-100 border-l-4 border-blue-500" : ""
              }`}
              style={{ paddingLeft: `${depth * 20 + 8}px` }}
              onClick={() => {
                if (file.type === "folder") {
                  onToggleFolder(file.id)
                } else {
                  onFileSelect(file)
                }
              }}
          >
            {file.type === "folder" ? (
                isExpanded ? (
                    <FolderOpen className="w-4 h-4 text-blue-500" />
                ) : (
                    <Folder className="w-4 h-4 text-blue-500" />
                )
            ) : (
                getLevelIcon(file.level)
            )}
            <span className="text-sm font-medium flex-1">{file.name}</span>
            {file.size && <span className="text-xs text-gray-500">{file.size}</span>}
          </div>
          {file.type === "folder" && isExpanded && file.children && (
              <div>{file.children.map((child) => renderFile(child, depth + 1))}</div>
          )}
        </div>
    )
  }

  return <div className="space-y-1">{files.map((file) => renderFile(file))}</div>
}

// 真正的虚拟化日志内容组件
const VirtualizedLogContent = ({
                                 file,
                                 searchTerm,
                                 filterLevel,
                                 useBulkMode,
                                 caseSensitive,
                               }: {
  file: LogFile
  searchTerm: string
  filterLevel: string
  useBulkMode?: boolean
  caseSensitive?: boolean
}) => {
  const [dataSource, setDataSource] = useState<RealLogDataSource | null>(null)
  const [visibleLines, setVisibleLines] = useState<LogLine[]>([])
  const [isLoading, setIsLoading] = useState(false)
  const [isLoadingMore, setIsLoadingMore] = useState(false)
  const [loadingProgress, setLoadingProgress] = useState(0)

  const [error, setError] = useState<string | null>(null)
  const [hasMore, setHasMore] = useState(true)
  const [currentChunkIndex, setCurrentChunkIndex] = useState(0)
  const [totalLines, setTotalLines] = useState(file.lineCount || 0)
  const [filteredLines, setFilteredLines] = useState<LogLine[]>([])
  const [isLocalSearchActive, setIsLocalSearchActive] = useState(false)

  const chunkSize = 100 // 每次加载100行

  // 初始化数据源
  useEffect(() => {
    if (file.logLevel && file.fileName) {
      // 根据文件大小决定是否使用流式模式
      const fileSizeKB = file.fileSize || 0
      const useStreamMode = fileSizeKB < 10240 // 小于10MB的文件使用流式模式

      console.log(`Initializing data source: fileSize=${fileSizeKB}KB, useStreamMode=${useStreamMode}, useBulkMode=${useBulkMode}`)

      const ds = new RealLogDataSource(file, useBulkMode, useStreamMode)
      setDataSource(ds)
      setCurrentChunkIndex(0)
      setVisibleLines([])
      setError(null)
      setHasMore(true)
      setTotalLines(file.lineCount || 0) // 重置为文件的初始行数
      setFilteredLines([])
      setIsLocalSearchActive(false)
    }
  }, [file, useBulkMode])

  // 本地搜索逻辑
  useEffect(() => {
    if (!dataSource || !searchTerm.trim()) {
      setFilteredLines([])
      setIsLocalSearchActive(false)
      return
    }

    // 获取所有已加载的行
    const allLoadedLines = dataSource.getAllLoadedLines()
    if (allLoadedLines.length === 0) {
      setFilteredLines([])
      setIsLocalSearchActive(false)
      return
    }

    // 执行本地搜索
    const searchRegex = new RegExp(searchTerm.replace(/[.*+?^${}()|[\]\\]/g, '\\$&'), (caseSensitive || false) ? 'g' : 'gi')
    const matchingLines = allLoadedLines.filter(line =>
      searchRegex.test(line.content)
    )

    setFilteredLines(matchingLines)
    setIsLocalSearchActive(true)
  }, [searchTerm, caseSensitive, dataSource, visibleLines]) // 依赖visibleLines确保搜索在数据加载后执行

  // 加载初始数据
  const loadInitialData = useCallback(async () => {
    if (!dataSource) return

    console.log('Loading initial data...')
    setIsLoading(true)
    setLoadingProgress(0)
    setError(null)

    try {
      const chunk = await dataSource.loadChunk(0)
      console.log('Initial chunk loaded:', { linesCount: chunk.lines.length })

      setVisibleLines(chunk.lines)
      setCurrentChunkIndex(0)

      // 更新总行数
      const updatedTotalLines = dataSource.getTotalLines()
      setTotalLines(updatedTotalLines)

      // 改进的hasMore计算
      const shouldHaveMore = chunk.lines.length === chunkSize && chunk.lines.length < updatedTotalLines
      console.log('Initial hasMore calculation:', {
        chunkLinesLength: chunk.lines.length,
        chunkSize,
        updatedTotalLines,
        shouldHaveMore
      })
      setHasMore(shouldHaveMore)
      setLoadingProgress(100)
    } catch (error) {
      console.error("Failed to load initial data:", error)
      setError(error instanceof Error ? error.message : '加载日志内容失败')
      toast({
        title: '加载失败',
        description: '无法加载日志内容，请检查网络连接或稍后重试',
        variant: 'destructive',
      })
    } finally {
      setTimeout(() => setIsLoading(false), 200)
    }
  }, [dataSource, chunkSize]) // 移除totalLines依赖

  // 加载更多数据
  const loadMoreData = useCallback(async () => {
    if (!dataSource || isLoadingMore || !hasMore) {
      console.log('loadMoreData blocked:', { dataSource: !!dataSource, isLoadingMore, hasMore })
      return
    }

    console.log('Starting to load more data, currentChunkIndex:', currentChunkIndex)
    setIsLoadingMore(true)
    setError(null)

    try {
      const nextChunkIndex = currentChunkIndex + 1
      const chunk = await dataSource.loadChunk(nextChunkIndex)
      console.log('Loaded chunk:', { nextChunkIndex, linesCount: chunk.lines.length })

      if (chunk.lines.length > 0) {
        setVisibleLines(prev => {
          const newLines = [...prev, ...chunk.lines]
          console.log('Updated visible lines count:', newLines.length)
          return newLines
        })
        setCurrentChunkIndex(nextChunkIndex)

        // 更新总行数
        const updatedTotalLines = dataSource.getTotalLines()
        setTotalLines(updatedTotalLines)

        // 改进的hasMore计算逻辑
        const totalLoadedLines = visibleLines.length + chunk.lines.length
        const shouldHaveMore = chunk.lines.length === chunkSize && totalLoadedLines < updatedTotalLines
        console.log('HasMore calculation:', {
          totalLoadedLines,
          updatedTotalLines,
          chunkLinesLength: chunk.lines.length,
          expectedChunkSize: chunkSize,
          shouldHaveMore
        })
        setHasMore(shouldHaveMore)
      } else {
        console.log('No more lines in chunk, setting hasMore to false')
        setHasMore(false)
      }
    } catch (error) {
      console.error("Failed to load more data:", error)
      setError(error instanceof Error ? error.message : '加载更多数据失败')
      toast({
        title: '加载失败',
        description: '无法加载更多数据，请检查网络连接或稍后重试',
        variant: 'destructive',
      })
    } finally {
      setIsLoadingMore(false)
    }
  }, [dataSource, isLoadingMore, hasMore, currentChunkIndex, chunkSize, visibleLines.length])

  // 初始数据加载
  useEffect(() => {
    loadInitialData()
  }, [loadInitialData])

  // 滚动监听 - 检测滚动到底部时加载更多数据
  const scrollAreaRef = useRef<HTMLDivElement>(null)
  const loadMoreDataRef = useRef(loadMoreData)
  const scrollTimeoutRef = useRef<NodeJS.Timeout | null>(null)

  // 保持loadMoreData引用最新
  useEffect(() => {
    loadMoreDataRef.current = loadMoreData
  }, [loadMoreData])

  useEffect(() => {
    const scrollArea = scrollAreaRef.current
    if (!scrollArea) {
      console.log('ScrollArea ref not found')
      return
    }

    // 查找Radix ScrollArea的viewport元素
    const viewport = scrollArea.querySelector('[data-radix-scroll-area-viewport]') as HTMLDivElement
    if (!viewport) {
      console.log('ScrollArea viewport not found')
      return
    }

    console.log('Setting up scroll listener')

    const handleScroll = () => {
      // 清除之前的超时
      if (scrollTimeoutRef.current) {
        clearTimeout(scrollTimeoutRef.current)
      }

      // 节流处理，避免过于频繁的滚动事件
      scrollTimeoutRef.current = setTimeout(() => {
        const { scrollTop, scrollHeight, clientHeight } = viewport
        const distanceFromBottom = scrollHeight - scrollTop - clientHeight

        // 添加调试信息
        if (distanceFromBottom < 200) { // 只在接近底部时打印日志
          console.log('Scroll position:', {
            scrollTop,
            scrollHeight,
            clientHeight,
            distanceFromBottom,
            hasMore,
            isLoadingMore,
            isLocalSearchActive
          })
        }

        // 当滚动到距离底部100px时开始加载更多数据
        if (distanceFromBottom < 100 && hasMore && !isLoadingMore && !isLocalSearchActive) {
          console.log('Triggering loadMoreData from scroll')
          loadMoreDataRef.current()
        }
      }, 100) // 100ms 节流
    }

    viewport.addEventListener('scroll', handleScroll, { passive: true })

    // 初始检查是否需要加载更多数据（内容不足以填满容器时）
    const initialCheck = () => {
      const { scrollHeight, clientHeight } = viewport
      if (scrollHeight <= clientHeight && hasMore && !isLoadingMore && !isLocalSearchActive) {
        console.log('Initial content too short, loading more data')
        loadMoreDataRef.current()
      }
    }

    // 延迟执行初始检查，确保DOM已渲染
    setTimeout(initialCheck, 100)

    return () => {
      viewport.removeEventListener('scroll', handleScroll)
      if (scrollTimeoutRef.current) {
        clearTimeout(scrollTimeoutRef.current)
      }
    }
  }, [hasMore, isLoadingMore, isLocalSearchActive]) // 移除loadMoreData依赖



  const formatLogLine = (line: LogLine, index: number) => {
    let className = "px-2 py-1 hover:bg-gray-50 transition-colors"
    let textColor = ""

    if (line.content.includes("ERROR")) {
      className += " bg-red-50 border-l-2 border-red-300"
      textColor = "text-red-700"
    } else if (line.content.includes("WARN")) {
      className += " bg-yellow-50 border-l-2 border-yellow-300"
      textColor = "text-yellow-700"
    } else if (line.content.includes("INFO")) {
      className += " bg-blue-50 border-l-2 border-blue-300"
      textColor = "text-blue-700"
    } else if (line.content.includes("DEBUG")) {
      className += " bg-gray-50 border-l-2 border-gray-300"
      textColor = "text-gray-700"
    }

    return (
        <div className={`${className} flex-1 font-mono text-sm ${textColor}`}>
          <HighlightText
            text={line.content}
            searchTerm={searchTerm}
            caseSensitive={caseSensitive || false}
          />
        </div>
    )
  }

  const displayLines = isLocalSearchActive ? filteredLines : visibleLines
  const currentDisplayStart = displayLines.length > 0 ? 1 : 0
  const currentDisplayEnd = displayLines.length
  const displayTotalLines = isLocalSearchActive ? filteredLines.length : totalLines

  return (
      <div className="h-full flex flex-col">
        {/* 状态栏 */}
        <div className="flex items-center justify-between p-3 border-b bg-gray-50">
          <div className="text-sm text-gray-600">
            {isLocalSearchActive ? (
                <span className="flex items-center gap-2">
              <Search className="w-4 h-4" />
              本地搜索结果: {filteredLines.length} 条匹配
            </span>
            ) : (
                <span>
              已加载 {currentDisplayEnd.toLocaleString()} / 共 {displayTotalLines.toLocaleString()} 行
              {hasMore && !isLocalSearchActive && (
                <span className="text-gray-400 ml-2">• 滚动到底部加载更多</span>
              )}
            </span>
            )}
          </div>

          {/* 调试信息 */}
          {!isLocalSearchActive && (
            <div className="text-xs text-gray-500 flex items-center gap-4">
              <span>块: {currentChunkIndex + 1}</span>
              <span>hasMore: {hasMore ? '是' : '否'}</span>
              {isLoadingMore && (
                <span className="flex items-center gap-1">
                  <Loader2 className="w-3 h-3 animate-spin" />
                  加载中...
                </span>
              )}
            </div>
          )}

          {isLoadingMore && (
              <div className="flex items-center gap-2 text-sm text-gray-500">
                <Loader2 className="w-4 h-4 animate-spin" />
                <span>加载更多...</span>
              </div>
          )}
        </div>

        {/* 日志内容 */}
        <div className="flex-1 relative">
          {isLoading && (
              <div className="absolute inset-0 bg-white/90 flex items-center justify-center z-10">
                <div className="text-center">
                  <Loader2 className="w-8 h-8 animate-spin mx-auto mb-2 text-blue-500" />
                  <p className="text-sm text-gray-600 mb-2">加载中...</p>
                  {loadingProgress > 0 && <Progress value={loadingProgress} className="w-48 mx-auto" />}
                </div>
              </div>
          )}

          <ScrollArea className="h-full" ref={scrollAreaRef}>
            <div className="font-mono text-sm">
              {displayLines.length > 0 ? (
                  <>
                    {displayLines.map((line, index) => (
                        <div key={`${line.lineNumber}-${index}`} className="flex">
                    <span className="text-gray-400 w-20 text-right mr-4 select-none py-1 px-2 sticky left-0 bg-gray-50">
                      {line.lineNumber}
                    </span>
                          {formatLogLine(line, index)}
                        </div>
                    ))}

                    {/* 加载更多指示器 */}
                    {hasMore && !isLocalSearchActive && (
                        <div className="flex items-center justify-center py-4 text-gray-500">
                          {isLoadingMore ? (
                              <div className="flex items-center gap-2">
                                <Loader2 className="w-4 h-4 animate-spin" />
                                <span>加载更多数据...</span>
                              </div>
                          ) : (
                              <div className="flex flex-col items-center gap-2">
                                <span className="text-sm">滚动到底部加载更多</span>
                                <Button
                                  variant="outline"
                                  size="sm"
                                  onClick={loadMoreData}
                                  className="text-xs"
                                >
                                  手动加载更多
                                </Button>
                              </div>
                          )}
                        </div>
                    )}

                    {/* 已加载完成指示器 */}
                    {!hasMore && !isLocalSearchActive && displayLines.length > 0 && (
                        <div className="flex items-center justify-center py-4 text-gray-400 text-sm">
                          <span>已加载全部 {displayLines.length} 行日志</span>
                        </div>
                    )}
                  </>
              ) : (
                  <div className="flex items-center justify-center h-32 text-gray-500">
                    {isLocalSearchActive ? "没有找到匹配的结果" : "暂无数据"}
                  </div>
              )}
            </div>
          </ScrollArea>
        </div>
      </div>
  )
}



export default function LogManager() {
  const [selectedFile, setSelectedFile] = useState<LogFile | null>(null)
  const [expandedFolders, setExpandedFolders] = useState<Set<string>>(new Set())
  const [searchTerm, setSearchTerm] = useState("")
  const [filterLevel, setFilterLevel] = useState<string>("all")
  const [isRefreshing, setIsRefreshing] = useState(false)
  const [logFiles, setLogFiles] = useState<LogFile[]>([])
  const [isLoading, setIsLoading] = useState(true)
  const [error, setError] = useState<string | null>(null)

  // 高级搜索状态
  const [showAdvancedSearch, setShowAdvancedSearch] = useState(false)
  const [caseSensitive, setCaseSensitive] = useState(false)
  const [useBulkMode, setUseBulkMode] = useState(false)

  // 下载管理
  const downloadManager = useDownloadManager()
  const [currentDownload, setCurrentDownload] = useState<{
    fileName: string
    progress: number
    status: DownloadStatus
    error?: string
  } | null>(null)
  const [showDownloadDialog, setShowDownloadDialog] = useState(false)

  // 防抖搜索
  const debouncedSearchTerm = useDebounce(searchTerm, 300)

  // 加载日志文件列表
  const loadLogFiles = useCallback(async () => {
    setIsLoading(true)
    setError(null)

    try {
      const files = await logService.getLogFileTree()
      setLogFiles(files)

      // 自动展开所有文件夹
      const folderIds = files.map(file => file.id)
      setExpandedFolders(new Set(folderIds))

      toast({
        title: '加载成功',
        description: `已加载 ${files.length} 个日志分组`,
      })
    } catch (error) {
      console.error('加载日志文件失败:', error)
      setError(error instanceof Error ? error.message : '加载日志文件失败')
      toast({
        title: '加载失败',
        description: '无法加载日志文件列表，请检查网络连接或稍后重试',
        variant: 'destructive',
      })
    } finally {
      setIsLoading(false)
    }
  }, [])

  // 初始加载
  useEffect(() => {
    loadLogFiles()
  }, [loadLogFiles])

  const handleToggleFolder = (folderId: string) => {
    const newExpanded = new Set(expandedFolders)
    if (newExpanded.has(folderId)) {
      newExpanded.delete(folderId)
    } else {
      newExpanded.add(folderId)
    }
    setExpandedFolders(newExpanded)
  }

  const handleRefresh = () => {
    setIsRefreshing(true)
    loadLogFiles().finally(() => {
      setIsRefreshing(false)
    })
  }

  const handleFileSelect = useCallback((file: LogFile) => {
    if (file.type === 'file') {
      setSelectedFile(file)
    }
  }, [])

  const handleDownload = async () => {
    if (!selectedFile || selectedFile.type !== 'file') {
      toast({
        title: '请选择文件',
        description: '请先选择要下载的日志文件',
        variant: 'destructive',
      })
      return
    }

    const fileName = selectedFile.name

    // 初始化下载状态
    setCurrentDownload({
      fileName,
      progress: 0,
      status: 'downloading'
    })
    setShowDownloadDialog(true)

    try {
      await logService.smartDownloadLogFile(
        selectedFile.logLevel || '',
        selectedFile.fileName || '',
        (progress) => {
          // 更新下载进度
          setCurrentDownload(prev => prev ? {
            ...prev,
            progress
          } : null)
        }
      )

      // 下载完成
      setCurrentDownload(prev => prev ? {
        ...prev,
        progress: 100,
        status: 'completed'
      } : null)

      toast({
        title: '下载成功',
        description: `文件 ${fileName} 已下载到您的下载文件夹`,
      })
    } catch (error) {
      console.error('下载文件失败:', error)
      const errorMessage = error instanceof Error ? error.message : '下载文件失败'

      // 更新错误状态
      setCurrentDownload(prev => prev ? {
        ...prev,
        status: 'error',
        error: errorMessage
      } : null)

      toast({
        title: '下载失败',
        description: errorMessage,
        variant: 'destructive',
      })
    }
  }

  // 重试下载
  const handleRetryDownload = () => {
    if (currentDownload) {
      setCurrentDownload({
        ...currentDownload,
        progress: 0,
        status: 'downloading',
        error: undefined
      })
      handleDownload()
    }
  }

  // 取消下载
  const handleCancelDownload = () => {
    setCurrentDownload(null)
    setShowDownloadDialog(false)
    // 这里可以添加取消下载的逻辑
  }

  return (
    <MainLayout>
      <div className="flex h-screen bg-gray-50">
        {/* 左侧文件树 */}
        <div className="w-80 bg-white border-r border-gray-200 flex flex-col">
          <div className="p-4 border-b border-gray-200">
            <h2 className="text-lg font-semibold text-gray-800 mb-3">日志文件管理器</h2>
            <div className="flex gap-2">
              <Button variant="outline" size="sm" onClick={handleRefresh} disabled={isRefreshing}>
                <RefreshCw className={`w-4 h-4 ${isRefreshing ? "animate-spin" : ""}`} />
              </Button>
              <Button variant="outline" size="sm" onClick={handleDownload} disabled={!selectedFile || selectedFile.type !== 'file'}>
                <Download className="w-4 h-4" />
              </Button>
              <DownloadDiagnosticsPanel />
            </div>
          </div>

          <ScrollArea className="flex-1 p-4">
            {isLoading ? (
              <div className="flex items-center justify-center h-32">
                <div className="text-center">
                  <Loader2 className="w-8 h-8 animate-spin mx-auto mb-2 text-blue-500" />
                  <p className="text-sm text-gray-600">加载日志文件列表...</p>
                </div>
              </div>
            ) : error ? (
              <div className="flex items-center justify-center h-32">
                <div className="text-center">
                  <XCircle className="w-8 h-8 mx-auto mb-2 text-red-500" />
                  <p className="text-sm text-gray-600 mb-2">加载失败</p>
                  <p className="text-xs text-gray-400">{error}</p>
                  <Button variant="outline" size="sm" onClick={handleRefresh} className="mt-2">
                    重试
                  </Button>
                </div>
              </div>
            ) : logFiles.length === 0 ? (
              <div className="flex items-center justify-center h-32">
                <div className="text-center">
                  <FileText className="w-8 h-8 mx-auto mb-2 text-gray-400" />
                  <p className="text-sm text-gray-600">暂无日志文件</p>
                </div>
              </div>
            ) : (
              <FileTree
                  files={logFiles}
                  onFileSelect={handleFileSelect}
                  selectedFile={selectedFile}
                  expandedFolders={expandedFolders}
                  onToggleFolder={handleToggleFolder}
              />
            )}
          </ScrollArea>
        </div>

        {/* 右侧内容区域 */}
        <div className="flex-1 flex flex-col">
          {/* 顶部工具栏 */}
          <div className="bg-white border-b border-gray-200 p-4">
            <div className="flex items-center gap-4">
              <div className="flex-1 relative">
                <Search className="absolute left-3 top-1/2 transform -translate-y-1/2 text-gray-400 w-4 h-4" />
                <Input
                    placeholder="搜索日志内容..."
                    value={searchTerm}
                    onChange={(e) => setSearchTerm(e.target.value)}
                    className="pl-10"
                />
              </div>
              <Select value={filterLevel} onValueChange={setFilterLevel}>
                <SelectTrigger className="w-32">
                  <Filter className="w-4 h-4 mr-2" />
                  <SelectValue />
                </SelectTrigger>
                <SelectContent>
                  <SelectItem value="all">全部</SelectItem>
                  <SelectItem value="error">错误</SelectItem>
                  <SelectItem value="warn">警告</SelectItem>
                  <SelectItem value="info">信息</SelectItem>
                  <SelectItem value="debug">调试</SelectItem>
                </SelectContent>
              </Select>
              <Button
                variant="outline"
                size="sm"
                onClick={() => setShowAdvancedSearch(!showAdvancedSearch)}
                className="flex items-center gap-2"
              >
                <Settings className="w-4 h-4" />
                高级搜索
                {showAdvancedSearch ? <ChevronUp className="w-4 h-4" /> : <ChevronDown className="w-4 h-4" />}
              </Button>
            </div>

            {/* 高级搜索面板 */}
            {showAdvancedSearch && (
              <div className="mt-4 p-4 bg-gray-50 rounded-lg border">
                <div className="flex items-center gap-6">
                  <div className="flex items-center space-x-2">
                    <Checkbox
                      id="caseSensitive"
                      checked={caseSensitive}
                      onCheckedChange={(checked) => setCaseSensitive(checked as boolean)}
                    />
                    <Label htmlFor="caseSensitive" className="text-sm">
                      区分大小写
                    </Label>
                  </div>
                  <div className="flex items-center space-x-2">
                    <Checkbox
                      id="useBulkMode"
                      checked={useBulkMode}
                      onCheckedChange={(checked) => setUseBulkMode(checked as boolean)}
                    />
                    <Label htmlFor="useBulkMode" className="text-sm">
                      高性能批量模式
                    </Label>
                  </div>
                </div>
                <div className="mt-2 text-xs text-gray-500">
                  搜索功能已改为前端本地搜索，支持实时高亮匹配内容
                </div>
              </div>
            )}
          </div>

          {/* 主内容区域 */}
          <div className="flex-1">
            {selectedFile ? (
                <div className="h-full p-4">
                  <Card className={`h-full ${getLevelColor(selectedFile.level)}`}>
                    <CardHeader className="pb-3">
                      <div className="flex items-center justify-between">
                        <CardTitle className="flex items-center gap-2">
                          {getLevelIcon(selectedFile.level)}
                          {selectedFile.name}
                        </CardTitle>
                        <div className="flex items-center gap-4 text-sm text-gray-500">
                          <div className="flex items-center gap-1">
                            <Calendar className="w-4 h-4" />
                            {selectedFile.lastModified}
                          </div>
                          <div className="flex items-center gap-1">
                            <FileText className="w-4 h-4" />
                            {selectedFile.size}
                          </div>
                          <div className="flex items-center gap-1">
                            <FileText className="w-4 h-4" />
                            {selectedFile.lineCount?.toLocaleString()} lines
                          </div>
                        </div>
                      </div>
                      <div className="flex items-center gap-2">
                        {selectedFile.level && <Badge variant="outline">{selectedFile.level.toUpperCase()}</Badge>}
                        {selectedFile.sizeBytes && selectedFile.sizeBytes > 100000000 && (
                            <Badge variant="outline" className="text-orange-600 border-orange-300">
                              超大文件
                            </Badge>
                        )}
                        {selectedFile.sizeBytes &&
                            selectedFile.sizeBytes > 10000000 &&
                            selectedFile.sizeBytes <= 100000000 && (
                                <Badge variant="outline" className="text-yellow-600 border-yellow-300">
                                  大文件
                                </Badge>
                            )}
                      </div>
                    </CardHeader>
                    <Separator />
                    <CardContent className="p-0 h-full">
                      <VirtualizedLogContent
                          file={selectedFile}
                          searchTerm={debouncedSearchTerm}
                          filterLevel={filterLevel}
                          useBulkMode={useBulkMode}
                          caseSensitive={caseSensitive}
                      />
                    </CardContent>
                  </Card>
                </div>
            ) : (
                <div className="h-full flex items-center justify-center text-gray-500">
                  <div className="text-center">
                    <FileText className="w-16 h-16 mx-auto mb-4 text-gray-300" />
                    <h3 className="text-lg font-medium mb-2">选择一个日志文件</h3>
                    <p className="text-sm">从左侧文件树中选择要查看的日志文件</p>
                    <p className="text-xs text-gray-400 mt-2">支持GB级大文件的高性能查看</p>
                  </div>
                </div>
            )}
          </div>
        </div>
      </div>

      {/* 下载进度对话框 */}
      {currentDownload && (
        <DownloadProgressDialog
          open={showDownloadDialog}
          onOpenChange={setShowDownloadDialog}
          fileName={currentDownload.fileName}
          progress={currentDownload.progress}
          status={currentDownload.status}
          error={currentDownload.error}
          onRetry={handleRetryDownload}
          onCancel={handleCancelDownload}
        />
      )}
    </MainLayout>
  )
}
