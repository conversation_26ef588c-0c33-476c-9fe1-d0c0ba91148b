import { useState, useEffect } from 'react'
import { SettingsLayout } from '@/components/settings/settings-layout'
import {
  Card,
  CardContent,
  CardDescription,
  CardFooter,
  CardHeader,
  CardTitle,
} from '@/components/ui/card'
import { Button } from '@/components/ui/button'
import { Separator } from '@/components/ui/separator'
import {
  RefreshCw,
  Download,
  CheckCircle,
  AlertCircle,
  Clock,
  ArrowUpCircle,
} from 'lucide-react'
import { toast } from '@/components/ui/use-toast'
import { Progress } from '@/components/ui/progress'
import { Switch } from '@/components/ui/switch'
import { Label } from '@/components/ui/label'
import { RadioGroup, RadioGroupItem } from '@/components/ui/radio-group'
import { Alert, AlertDescription, AlertTitle } from '@/components/ui/alert'
import { Tabs, TabsContent, TabsList, TabsTrigger } from '@/components/ui/tabs'
import { MainLayout } from '@/components/layout/main-layout'

export default function SystemUpdatePage() {
  const [loading, setLoading] = useState(true)
  const [checking, setChecking] = useState(false)
  const [downloading, setDownloading] = useState(false)
  const [installing, setInstalling] = useState(false)
  const [downloadProgress, setDownloadProgress] = useState(0)
  const [installProgress, setInstallProgress] = useState(0)

  // 更新设置
  const [updateSettings, setUpdateSettings] = useState({
    autoCheck: true,
    checkFrequency: 'daily',
    autoDownload: false,
    autoInstall: false,
    updateChannel: 'stable',
    notifyBeforeUpdate: true,
    updateTime: '02:00',
  })

  // 更新信息
  const [updateInfo, setUpdateInfo] = useState({
    currentVersion: '2.5.3',
    latestVersion: '2.5.3',
    releaseDate: '2023-06-15',
    lastCheckTime: '2023-10-15 08:30:45',
    updateAvailable: false,
    updateSize: '0 MB',
    updateNotes: '',
    updateStatus: 'up-to-date', // checking, available, downloading, downloaded, installing, up-to-date, error
    updateError: '',
  })

  // 更新历史
  const [updateHistory, setUpdateHistory] = useState([
    {
      version: '2.5.3',
      date: '2023-06-15',
      status: 'success',
      notes:
        '- 修复了设备连接稳定性问题\n- 优化了数据采集性能\n- 改进了用户界面响应速度\n- 新增了高级数据分析功能',
    },
    {
      version: '2.5.2',
      date: '2023-05-02',
      status: 'success',
      notes:
        '- 修复了数据导出功能的bug\n- 改进了报警通知系统\n- 优化了系统资源占用',
    },
    {
      version: '2.5.1',
      date: '2023-03-20',
      status: 'failed',
      notes:
        '- 修复了用户权限管理问题\n- 新增了设备批量配置功能\n- 优化了数据库性能',
    },
    {
      version: '2.5.0',
      date: '2023-02-10',
      status: 'success',
      notes:
        '- 全新的用户界面设计\n- 新增了数据可视化功能\n- 支持更多类型的设备连接\n- 改进了系统安全性',
    },
  ])

  // 模拟加载数据
  useEffect(() => {
    const timer = setTimeout(() => {
      setLoading(false)
    }, 1000)

    return () => clearTimeout(timer)
  }, [])

  // 处理设置变化
  const handleSettingChange = (key: string, value: any) => {
    setUpdateSettings((prev) => ({
      ...prev,
      [key]: value,
    }))
  }

  // 保存设置
  const handleSaveSettings = () => {
    toast({
      title: '设置已保存',
      description: '系统更新设置已成功保存',
    })
  }

  // 检查更新
  const checkForUpdates = () => {
    setChecking(true)
    setUpdateInfo((prev) => ({
      ...prev,
      updateStatus: 'checking',
    }))

    // 模拟检查更新
    setTimeout(() => {
      const hasUpdate = Math.random() > 0.5 // 随机模拟是否有更新

      if (hasUpdate) {
        setUpdateInfo({
          currentVersion: '2.5.3',
          latestVersion: '2.6.0',
          releaseDate: '2023-10-20',
          lastCheckTime: new Date().toLocaleString(),
          updateAvailable: true,
          updateSize: '156 MB',
          updateNotes:
            '- 全新的数据分析引擎\n- 改进的设备连接稳定性\n- 新增的批量数据处理功能\n- 优化的用户界面和体验\n- 修复了多个已知问题',
          updateStatus: 'available',
          updateError: '',
        })

        toast({
          title: '发现新版本',
          description: '有新版本 2.6.0 可用，您可以立即下载并安装',
        })
      } else {
        setUpdateInfo((prev) => ({
          ...prev,
          lastCheckTime: new Date().toLocaleString(),
          updateStatus: 'up-to-date',
        }))

        toast({
          title: '系统已是最新版本',
          description: '您的系统已经是最新版本，无需更新',
        })
      }

      setChecking(false)
    }, 2000)
  }

  // 下载更新
  const downloadUpdate = () => {
    setDownloading(true)
    setUpdateInfo((prev) => ({
      ...prev,
      updateStatus: 'downloading',
    }))
    setDownloadProgress(0)

    // 模拟下载进度
    const interval = setInterval(() => {
      setDownloadProgress((prev) => {
        if (prev >= 100) {
          clearInterval(interval)
          setDownloading(false)
          setUpdateInfo((prev) => ({
            ...prev,
            updateStatus: 'downloaded',
          }))

          toast({
            title: '更新已下载',
            description: '新版本已成功下载，可以立即安装',
          })

          return 100
        }
        return prev + Math.floor(Math.random() * 10) + 1
      })
    }, 500)
  }

  // 安装更新
  const installUpdate = () => {
    setInstalling(true)
    setUpdateInfo((prev) => ({
      ...prev,
      updateStatus: 'installing',
    }))
    setInstallProgress(0)

    // 模拟安装进度
    const interval = setInterval(() => {
      setInstallProgress((prev) => {
        if (prev >= 100) {
          clearInterval(interval)
          setInstalling(false)

          // 模拟安装完成后的系统状态
          setUpdateInfo({
            currentVersion: '2.6.0',
            latestVersion: '2.6.0',
            releaseDate: '2023-10-20',
            lastCheckTime: new Date().toLocaleString(),
            updateAvailable: false,
            updateSize: '0 MB',
            updateNotes: '',
            updateStatus: 'up-to-date',
            updateError: '',
          })

          // 更新历史记录
          setUpdateHistory((prev) => [
            {
              version: '2.6.0',
              date: new Date().toLocaleDateString(),
              status: 'success',
              notes:
                '- 全新的数据分析引擎\n- 改进的设备连接稳定性\n- 新增的批量数据处理功能\n- 优化的用户界面和体验\n- 修复了多个已知问题',
            },
            ...prev,
          ])

          toast({
            title: '更新已完成',
            description: '系统已成功更新到最新版本 2.6.0',
          })

          return 100
        }
        return prev + Math.floor(Math.random() * 5) + 1
      })
    }, 800)
  }

  if (loading) {
    return (
      <MainLayout>
        <SettingsLayout
          title="系统更新"
          description="管理系统更新和升级，检查新版本并安装更新">
          <div className="flex flex-col items-center justify-center py-12">
            <RefreshCw className="h-8 w-8 animate-spin text-muted-foreground" />
            <p className="mt-4 text-muted-foreground">
              正在加载系统更新信息...
            </p>
          </div>
        </SettingsLayout>
      </MainLayout>
    )
  }

  return (
    <MainLayout>
      <SettingsLayout
        title="系统更新"
        description="管理系统更新和升级，检查新版本并安装更新">
        <Tabs defaultValue="status">
          <TabsList className="mb-4">
            <TabsTrigger value="status">更新状态</TabsTrigger>
            <TabsTrigger value="settings">更新设置</TabsTrigger>
            <TabsTrigger value="history">更新历史</TabsTrigger>
          </TabsList>

          {/* 更新状态 */}
          <TabsContent value="status" className="space-y-6">
            <Card>
              <CardHeader>
                <CardTitle>当前状态</CardTitle>
                <CardDescription>系统版本和更新状态</CardDescription>
              </CardHeader>
              <CardContent className="space-y-4">
                <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
                  <div className="space-y-1">
                    <p className="text-sm font-medium">当前版本</p>
                    <p className="text-sm text-muted-foreground">
                      {updateInfo.currentVersion}
                    </p>
                  </div>
                  <div className="space-y-1">
                    <p className="text-sm font-medium">最新版本</p>
                    <p className="text-sm text-muted-foreground">
                      {updateInfo.latestVersion}
                    </p>
                  </div>
                  <div className="space-y-1">
                    <p className="text-sm font-medium">发布日期</p>
                    <p className="text-sm text-muted-foreground">
                      {updateInfo.releaseDate}
                    </p>
                  </div>
                  <div className="space-y-1">
                    <p className="text-sm font-medium">上次检查时间</p>
                    <p className="text-sm text-muted-foreground">
                      {updateInfo.lastCheckTime}
                    </p>
                  </div>
                  <div className="space-y-1 col-span-2">
                    <p className="text-sm font-medium">更新状态</p>
                    <div className="flex items-center">
                      {updateInfo.updateStatus === 'checking' && (
                        <>
                          <Clock className="h-4 w-4 text-blue-500 mr-2" />
                          <p className="text-sm text-blue-500">
                            正在检查更新...
                          </p>
                        </>
                      )}
                      {updateInfo.updateStatus === 'available' && (
                        <>
                          <ArrowUpCircle className="h-4 w-4 text-green-500 mr-2" />
                          <p className="text-sm text-green-500">
                            有新版本可用 ({updateInfo.latestVersion})
                          </p>
                        </>
                      )}
                      {updateInfo.updateStatus === 'downloading' && (
                        <>
                          <Download className="h-4 w-4 text-blue-500 mr-2" />
                          <p className="text-sm text-blue-500">
                            正在下载更新...
                          </p>
                        </>
                      )}
                      {updateInfo.updateStatus === 'downloaded' && (
                        <>
                          <CheckCircle className="h-4 w-4 text-orange-500 mr-2" />
                          <p className="text-sm text-orange-500">
                            更新已下载，等待安装
                          </p>
                        </>
                      )}
                      {updateInfo.updateStatus === 'installing' && (
                        <>
                          <RefreshCw className="h-4 w-4 text-blue-500 mr-2" />
                          <p className="text-sm text-blue-500">
                            正在安装更新...
                          </p>
                        </>
                      )}
                      {updateInfo.updateStatus === 'up-to-date' && (
                        <>
                          <CheckCircle className="h-4 w-4 text-green-500 mr-2" />
                          <p className="text-sm text-green-500">
                            系统已是最新版本
                          </p>
                        </>
                      )}
                      {updateInfo.updateStatus === 'error' && (
                        <>
                          <AlertCircle className="h-4 w-4 text-red-500 mr-2" />
                          <p className="text-sm text-red-500">
                            更新出错: {updateInfo.updateError}
                          </p>
                        </>
                      )}
                    </div>
                  </div>
                </div>

                {updateInfo.updateStatus === 'downloading' && (
                  <div className="space-y-2">
                    <div className="flex justify-between">
                      <p className="text-sm font-medium">下载进度</p>
                      <p className="text-sm text-muted-foreground">
                        {downloadProgress}%
                      </p>
                    </div>
                    <Progress value={downloadProgress} className="h-2" />
                  </div>
                )}

                {updateInfo.updateStatus === 'installing' && (
                  <div className="space-y-2">
                    <div className="flex justify-between">
                      <p className="text-sm font-medium">安装进度</p>
                      <p className="text-sm text-muted-foreground">
                        {installProgress}%
                      </p>
                    </div>
                    <Progress value={installProgress} className="h-2" />
                  </div>
                )}

                {updateInfo.updateAvailable && (
                  <>
                    <Separator className="my-4" />

                    <div className="space-y-2">
                      <p className="text-sm font-medium">更新大小</p>
                      <p className="text-sm text-muted-foreground">
                        {updateInfo.updateSize}
                      </p>
                    </div>

                    <div className="space-y-2">
                      <p className="text-sm font-medium">更新内容</p>
                      <pre className="text-sm text-muted-foreground whitespace-pre-line bg-muted p-3 rounded-md">
                        {updateInfo.updateNotes}
                      </pre>
                    </div>
                  </>
                )}
              </CardContent>
              <CardFooter className="flex flex-wrap gap-4">
                <Button
                  variant="outline"
                  onClick={checkForUpdates}
                  disabled={
                    checking ||
                    updateInfo.updateStatus === 'downloading' ||
                    updateInfo.updateStatus === 'installing'
                  }>
                  {checking ? (
                    <>
                      <RefreshCw className="mr-2 h-4 w-4 animate-spin" />
                      检查中...
                    </>
                  ) : (
                    <>
                      <RefreshCw className="mr-2 h-4 w-4" />
                      检查更新
                    </>
                  )}
                </Button>

                {updateInfo.updateStatus === 'available' && (
                  <Button onClick={downloadUpdate} disabled={downloading}>
                    {downloading ? (
                      <>
                        <Download className="mr-2 h-4 w-4 animate-spin" />
                        下载中...
                      </>
                    ) : (
                      <>
                        <Download className="mr-2 h-4 w-4" />
                        下载更新
                      </>
                    )}
                  </Button>
                )}

                {updateInfo.updateStatus === 'downloaded' && (
                  <Button onClick={installUpdate} disabled={installing}>
                    {installing ? (
                      <>
                        <RefreshCw className="mr-2 h-4 w-4 animate-spin" />
                        安装中...
                      </>
                    ) : (
                      <>
                        <ArrowUpCircle className="mr-2 h-4 w-4" />
                        立即安装
                      </>
                    )}
                  </Button>
                )}
              </CardFooter>
            </Card>
          </TabsContent>

          {/* 更新设置 */}
          <TabsContent value="settings" className="space-y-6">
            <Card>
              <CardHeader>
                <CardTitle>更新设置</CardTitle>
                <CardDescription>配置系统更新行为</CardDescription>
              </CardHeader>
              <CardContent className="space-y-4">
                <div className="flex items-center justify-between">
                  <div className="space-y-0.5">
                    <Label htmlFor="autoCheck">自动检查更新</Label>
                    <p className="text-sm text-muted-foreground">
                      定期自动检查系统更新
                    </p>
                  </div>
                  <Switch
                    id="autoCheck"
                    checked={updateSettings.autoCheck}
                    onCheckedChange={(checked) =>
                      handleSettingChange('autoCheck', checked)
                    }
                  />
                </div>

                {updateSettings.autoCheck && (
                  <div className="space-y-2 pl-6">
                    <Label>检查频率</Label>
                    <RadioGroup
                      value={updateSettings.checkFrequency}
                      onValueChange={(value) =>
                        handleSettingChange('checkFrequency', value)
                      }
                      className="flex flex-col space-y-1">
                      <div className="flex items-center space-x-2">
                        <RadioGroupItem value="daily" id="check-daily" />
                        <Label htmlFor="check-daily">每天</Label>
                      </div>
                      <div className="flex items-center space-x-2">
                        <RadioGroupItem value="weekly" id="check-weekly" />
                        <Label htmlFor="check-weekly">每周</Label>
                      </div>
                      <div className="flex items-center space-x-2">
                        <RadioGroupItem value="monthly" id="check-monthly" />
                        <Label htmlFor="check-monthly">每月</Label>
                      </div>
                    </RadioGroup>
                  </div>
                )}

                <Separator />

                <div className="flex items-center justify-between">
                  <div className="space-y-0.5">
                    <Label htmlFor="autoDownload">自动下载更新</Label>
                    <p className="text-sm text-muted-foreground">
                      发现更新时自动下载
                    </p>
                  </div>
                  <Switch
                    id="autoDownload"
                    checked={updateSettings.autoDownload}
                    onCheckedChange={(checked) =>
                      handleSettingChange('autoDownload', checked)
                    }
                  />
                </div>

                <div className="flex items-center justify-between">
                  <div className="space-y-0.5">
                    <Label htmlFor="autoInstall">自动安装更新</Label>
                    <p className="text-sm text-muted-foreground">
                      下载完成后自动安装更新
                    </p>
                  </div>
                  <Switch
                    id="autoInstall"
                    checked={updateSettings.autoInstall}
                    onCheckedChange={(checked) =>
                      handleSettingChange('autoInstall', checked)
                    }
                  />
                </div>

                <Separator />

                <div className="space-y-2">
                  <Label>更新渠道</Label>
                  <RadioGroup
                    value={updateSettings.updateChannel}
                    onValueChange={(value) =>
                      handleSettingChange('updateChannel', value)
                    }
                    className="flex flex-col space-y-1">
                    <div className="flex items-center space-x-2">
                      <RadioGroupItem value="stable" id="channel-stable" />
                      <Label htmlFor="channel-stable">稳定版</Label>
                    </div>
                    <div className="flex items-center space-x-2">
                      <RadioGroupItem value="beta" id="channel-beta" />
                      <Label htmlFor="channel-beta">测试版</Label>
                    </div>
                    <div className="flex items-center space-x-2">
                      <RadioGroupItem value="dev" id="channel-dev" />
                      <Label htmlFor="channel-dev">开发版</Label>
                    </div>
                  </RadioGroup>
                </div>

                {updateSettings.updateChannel !== 'stable' && (
                  <Alert variant="warning">
                    <AlertCircle className="h-4 w-4" />
                    <AlertTitle>注意</AlertTitle>
                    <AlertDescription>
                      非稳定版本可能包含实验性功能和未知问题，不建议在生产环境中使用。
                    </AlertDescription>
                  </Alert>
                )}

                <Separator />

                <div className="flex items-center justify-between">
                  <div className="space-y-0.5">
                    <Label htmlFor="notifyBeforeUpdate">更新前通知</Label>
                    <p className="text-sm text-muted-foreground">
                      安装更新前显示通知
                    </p>
                  </div>
                  <Switch
                    id="notifyBeforeUpdate"
                    checked={updateSettings.notifyBeforeUpdate}
                    onCheckedChange={(checked) =>
                      handleSettingChange('notifyBeforeUpdate', checked)
                    }
                  />
                </div>
              </CardContent>
              <CardFooter>
                <Button onClick={handleSaveSettings}>保存设置</Button>
              </CardFooter>
            </Card>
          </TabsContent>

          {/* 更新历史 */}
          <TabsContent value="history" className="space-y-6">
            <Card>
              <CardHeader>
                <CardTitle>更新历史</CardTitle>
                <CardDescription>系统更新记录和版本说明</CardDescription>
              </CardHeader>
              <CardContent className="space-y-6">
                {updateHistory.map((update, index) => (
                  <div key={index} className="space-y-2">
                    <div className="flex items-center justify-between">
                      <div className="flex items-center">
                        {update.status === 'success' ? (
                          <CheckCircle className="h-4 w-4 text-green-500 mr-2" />
                        ) : (
                          <AlertCircle className="h-4 w-4 text-red-500 mr-2" />
                        )}
                        <h3 className="text-lg font-medium">
                          版本 {update.version}
                        </h3>
                      </div>
                      <p className="text-sm text-muted-foreground">
                        {update.date}
                      </p>
                    </div>
                    <pre className="text-sm text-muted-foreground whitespace-pre-line bg-muted p-3 rounded-md">
                      {update.notes}
                    </pre>
                    {index < updateHistory.length - 1 && (
                      <Separator className="my-4" />
                    )}
                  </div>
                ))}
              </CardContent>
            </Card>
          </TabsContent>
        </Tabs>
      </SettingsLayout>
    </MainLayout>
  )
}
