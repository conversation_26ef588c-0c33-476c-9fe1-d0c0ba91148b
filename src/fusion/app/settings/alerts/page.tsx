import { useState } from 'react'
import { SettingsLayout } from '@/components/settings/settings-layout'
import {
  Card,
  CardContent,
  CardDescription,
  CardHeader,
  CardTitle,
} from '@/components/ui/card'
import { <PERSON><PERSON>, <PERSON><PERSON>Content, <PERSON><PERSON>List, TabsTrigger } from '@/components/ui/tabs'
import { Button } from '@/components/ui/button'
import { Switch } from '@/components/ui/switch'
import { Label } from '@/components/ui/label'
import { Input } from '@/components/ui/input'
import {
  Select,
  SelectContent,
  SelectItem,
  SelectTrigger,
  SelectValue,
} from '@/components/ui/select'
import { Separator } from '@/components/ui/separator'
import { Check, Mail, MessageSquare, Settings, Smartphone } from 'lucide-react'

export default function AlertsSettingsPage() {
  const [globalAlertsEnabled, setGlobalAlertsEnabled] = useState(true)
  const [emailEnabled, setEmailEnabled] = useState(true)
  const [smsEnabled, setSmsEnabled] = useState(true)
  const [inAppEnabled, setInAppEnabled] = useState(true)
  const [emailAddress, setEmailAddress] = useState('<EMAIL>')
  const [phoneNumber, setPhoneNumber] = useState('+8613800138000')
  const [alertRetentionDays, setAlertRetentionDays] = useState(30)
  const [defaultSeverity, setDefaultSeverity] = useState('medium')

  // Dummy data for templates
  const severity = 'High'
  const serviceName = 'API Service'
  const alertName = 'High Latency'
  const timestamp = new Date().toLocaleString()
  const metricValue = 120
  const unit = 'ms'
  const operator = '>'
  const thresholdValue = 100
  const metricName = 'Latency'

  return (
    <SettingsLayout
      title="警报设置"
      description="配置系统警报的全局设置和通知首选项">
        <div className="flex flex-col space-y-6">

          <Tabs defaultValue="general" className="w-full">
            <TabsList className="mb-4">
              <TabsTrigger value="general">常规设置</TabsTrigger>
              <TabsTrigger value="notifications">通知设置</TabsTrigger>
              <TabsTrigger value="templates">通知模板</TabsTrigger>
            </TabsList>

            <TabsContent value="general">
              <Card>
                <CardHeader>
                  <CardTitle>常规警报设置</CardTitle>
                  <CardDescription>
                    配置系统警报的基本行为和默认值
                  </CardDescription>
                </CardHeader>
                <CardContent className="space-y-6">
                  <div className="flex items-center justify-between">
                    <div className="space-y-0.5">
                      <Label className="text-base">全局警报</Label>
                      <p className="text-sm text-muted-foreground">
                        启用或禁用系统中的所有警报
                      </p>
                    </div>
                    <Switch
                      checked={globalAlertsEnabled}
                      onCheckedChange={setGlobalAlertsEnabled}
                    />
                  </div>
                  <Separator />

                  <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
                    <div className="space-y-2">
                      <Label htmlFor="alert-retention">警报保留天数</Label>
                      <Input
                        id="alert-retention"
                        type="number"
                        value={alertRetentionDays}
                        onChange={(e) =>
                          setAlertRetentionDays(Number.parseInt(e.target.value))
                        }
                      />
                      <p className="text-xs text-muted-foreground">
                        系统将保留警报历史记录的天数
                      </p>
                    </div>

                    <div className="space-y-2">
                      <Label htmlFor="default-severity">默认严重程度</Label>
                      <Select
                        value={defaultSeverity}
                        onValueChange={setDefaultSeverity}>
                        <SelectTrigger id="default-severity">
                          <SelectValue placeholder="选择默认严重程度" />
                        </SelectTrigger>
                        <SelectContent>
                          <SelectItem value="low">低</SelectItem>
                          <SelectItem value="medium">中</SelectItem>
                          <SelectItem value="high">高</SelectItem>
                          <SelectItem value="critical">紧急</SelectItem>
                        </SelectContent>
                      </Select>
                      <p className="text-xs text-muted-foreground">
                        新建警报阈值时的默认严重程度
                      </p>
                    </div>
                  </div>

                  <Separator />

                  <div className="space-y-4">
                    <h3 className="text-lg font-medium">警报聚合设置</h3>
                    <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
                      <div className="space-y-2">
                        <Label htmlFor="aggregation-window">
                          聚合时间窗口（分钟）
                        </Label>
                        <Input
                          id="aggregation-window"
                          type="number"
                          defaultValue={15}
                        />
                        <p className="text-xs text-muted-foreground">
                          在此时间窗口内的相同警报将被聚合为一条通知
                        </p>
                      </div>

                      <div className="space-y-2">
                        <Label htmlFor="max-alerts">最大警报数量</Label>
                        <Input
                          id="max-alerts"
                          type="number"
                          defaultValue={100}
                        />
                        <p className="text-xs text-muted-foreground">
                          每个时间窗口内允许的最大警报数量，超过此数量将触发限流
                        </p>
                      </div>
                    </div>
                  </div>

                  <div className="flex justify-end">
                    <Button>
                      <Check className="mr-2 h-4 w-4" />
                      保存设置
                    </Button>
                  </div>
                </CardContent>
              </Card>
            </TabsContent>

            <TabsContent value="notifications">
              <Card>
                <CardHeader>
                  <CardTitle>通知设置</CardTitle>
                  <CardDescription>
                    配置警报通知的发送方式和接收人
                  </CardDescription>
                </CardHeader>
                <CardContent className="space-y-6">
                  <div className="space-y-4">
                    <h3 className="text-lg font-medium">通知渠道</h3>

                    <div className="grid grid-cols-1 md:grid-cols-3 gap-4">
                      <Card>
                        <CardContent className="p-4 space-y-4">
                          <div className="flex justify-between items-center">
                            <div className="flex items-center">
                              <Mail className="h-5 w-5 mr-2 text-blue-500" />
                              <h4 className="font-medium">电子邮件</h4>
                            </div>
                            <Switch
                              checked={emailEnabled}
                              onCheckedChange={setEmailEnabled}
                            />
                          </div>
                          <div className="space-y-2">
                            <Label htmlFor="email-address">邮箱地址</Label>
                            <Input
                              id="email-address"
                              value={emailAddress}
                              onChange={(e) => setEmailAddress(e.target.value)}
                              disabled={!emailEnabled}
                            />
                          </div>
                        </CardContent>
                      </Card>

                      <Card>
                        <CardContent className="p-4 space-y-4">
                          <div className="flex justify-between items-center">
                            <div className="flex items-center">
                              <Smartphone className="h-5 w-5 mr-2 text-green-500" />
                              <h4 className="font-medium">短信</h4>
                            </div>
                            <Switch
                              checked={smsEnabled}
                              onCheckedChange={setSmsEnabled}
                            />
                          </div>
                          <div className="space-y-2">
                            <Label htmlFor="phone-number">手机号码</Label>
                            <Input
                              id="phone-number"
                              value={phoneNumber}
                              onChange={(e) => setPhoneNumber(e.target.value)}
                              disabled={!smsEnabled}
                            />
                          </div>
                        </CardContent>
                      </Card>

                      <Card>
                        <CardContent className="p-4 space-y-4">
                          <div className="flex justify-between items-center">
                            <div className="flex items-center">
                              <MessageSquare className="h-5 w-5 mr-2 text-purple-500" />
                              <h4 className="font-medium">应用内通知</h4>
                            </div>
                            <Switch
                              checked={inAppEnabled}
                              onCheckedChange={setInAppEnabled}
                            />
                          </div>
                          <div className="space-y-2">
                            <Label htmlFor="notification-expiry">
                              通知过期时间（天）
                            </Label>
                            <Input
                              id="notification-expiry"
                              type="number"
                              defaultValue={7}
                              disabled={!inAppEnabled}
                            />
                          </div>
                        </CardContent>
                      </Card>
                    </div>
                  </div>

                  <Separator />

                  <div className="space-y-4">
                    <h3 className="text-lg font-medium">通知时间设置</h3>

                    <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
                      <div className="space-y-2">
                        <Label htmlFor="quiet-hours-start">
                          免打扰时段开始
                        </Label>
                        <Input
                          id="quiet-hours-start"
                          type="time"
                          defaultValue="22:00"
                        />
                      </div>

                      <div className="space-y-2">
                        <Label htmlFor="quiet-hours-end">免打扰时段结束</Label>
                        <Input
                          id="quiet-hours-end"
                          type="time"
                          defaultValue="08:00"
                        />
                      </div>
                    </div>

                    <div className="flex items-center space-x-2">
                      <Switch id="respect-quiet-hours" defaultChecked />
                      <Label htmlFor="respect-quiet-hours">
                        在免打扰时段内仅发送紧急警报
                      </Label>
                    </div>
                  </div>

                  <div className="flex justify-end">
                    <Button>
                      <Check className="mr-2 h-4 w-4" />
                      保存设置
                    </Button>
                  </div>
                </CardContent>
              </Card>
            </TabsContent>

            <TabsContent value="templates">
              <Card>
                <CardHeader>
                  <CardTitle>通知模板</CardTitle>
                  <CardDescription>
                    自定义不同类型警报的通知模板
                  </CardDescription>
                </CardHeader>
                <CardContent className="space-y-6">
                  <div className="space-y-4">
                    <div className="flex items-center justify-between">
                      <h3 className="text-lg font-medium">电子邮件模板</h3>
                      <Button variant="outline" size="sm">
                        <Settings className="mr-2 h-4 w-4" />
                        编辑模板
                      </Button>
                    </div>

                    <Card className="bg-muted/50">
                      <CardContent className="p-4">
                        <div className="space-y-2">
                          <p className="text-sm font-medium">
                            主题: [{severity}] {serviceName} - {alertName}
                          </p>
                          <p className="text-sm">
                            警报: {alertName}
                            <br />
                            服务: {serviceName}
                            <br />
                            时间: {timestamp}
                            <br />
                            严重程度: {severity}
                            <br />
                            指标值: {metricValue} {unit}
                            <br />
                            阈值: {operator} {thresholdValue} {unit}
                            <br />
                            <br />
                            请登录系统查看详细信息并处理此警报。
                          </p>
                        </div>
                      </CardContent>
                    </Card>
                  </div>

                  <Separator />

                  <div className="space-y-4">
                    <div className="flex items-center justify-between">
                      <h3 className="text-lg font-medium">短信模板</h3>
                      <Button variant="outline" size="sm">
                        <Settings className="mr-2 h-4 w-4" />
                        编辑模板
                      </Button>
                    </div>

                    <Card className="bg-muted/50">
                      <CardContent className="p-4">
                        <p className="text-sm">
                          [{severity}] {serviceName}: {alertName} -{' '}
                          {metricValue}
                          {unit}，时间:
                          {timestamp}。请登录系统处理。
                        </p>
                      </CardContent>
                    </Card>
                  </div>

                  <Separator />

                  <div className="space-y-4">
                    <div className="flex items-center justify-between">
                      <h3 className="text-lg font-medium">应用内通知模板</h3>
                      <Button variant="outline" size="sm">
                        <Settings className="mr-2 h-4 w-4" />
                        编辑模板
                      </Button>
                    </div>

                    <Card className="bg-muted/50">
                      <CardContent className="p-4">
                        <div className="space-y-2">
                          <p className="text-sm font-medium">
                            {serviceName} - {alertName}
                          </p>
                          <p className="text-sm">
                            指标 {metricName} 当前值为 {metricValue}
                            {unit}，已{operator}阈值
                            {thresholdValue}
                            {unit}
                          </p>
                        </div>
                      </CardContent>
                    </Card>
                  </div>

                  <div className="flex justify-end">
                    <Button>
                      <Check className="mr-2 h-4 w-4" />
                      保存模板
                    </Button>
                  </div>
                </CardContent>
              </Card>
            </TabsContent>
          </Tabs>
        </div>
    </SettingsLayout>
  )
}
