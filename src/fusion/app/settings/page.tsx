/**
 * 系统设置主页面 - 系统配置和管理中心
 *
 * 菜单位置：侧边栏主导航 > 系统设置
 * 路由地址：/settings
 * 页面功能：工作流编排平台的系统设置入口，提供系统配置和管理功能的统一访问
 *
 * 安全考虑：
 * - 敏感配置的权限验证
 * - 配置变更的审计日志
 * - 重要操作的二次确认
 * - 配置备份和恢复机制
 * - 安全参数的加密存储
 *
 * 业务价值：
 * - 提供系统管理的统一入口
 * - 简化系统配置和维护工作
 * - 提升系统管理效率和准确性
 * - 支持系统的灵活配置和定制
 * - 确保系统的稳定运行和安全性
 * - 降低系统运维的技术门槛
 */

import {
  Card,
  CardContent,
  CardDescription,
  CardHeader,
  CardTitle,
} from '@/components/ui/card'
import { Link } from 'react-router-dom'
import {
  Network,
  Info,
  RefreshCw,
  FileText,
  Settings,
  Key,
  Sliders,
  Server,
  Palette,
} from 'lucide-react'
import { MainLayout } from '@/components/layout/main-layout'

export default function SettingsPage() {
  const settingsCategories = [
    {
      title: '网络配置',
      description: '配置系统网络设置，包括IP地址、DNS、代理等',
      href: '/settings/network',
      icon: Network,
    },
    {
      title: '系统信息',
      description: '查看系统基本信息，包括版本、硬件信息、运行状态等',
      href: '/settings/info',
      icon: Info,
    },
    {
      title: '系统更新',
      description: '管理系统更新和升级，检查新版本并安装更新',
      href: '/settings/update',
      icon: RefreshCw,
    },
    {
      title: '系统日志',
      description: '查看系统日志记录，包括错误、警告和信息日志',
      href: '/settings/logs',
      icon: FileText,
    },
    {
      title: '系统配置',
      description: '管理系统基本配置，包括时间、语言、显示等设置',
      href: '/settings/config',
      icon: Settings,
    },
    {
      title: '品牌设置',
      description: '配置系统品牌，包括系统名称、Logo和图标',
      href: '/settings/branding',
      icon: Palette,
    },
    {
      title: '系统授权',
      description: '管理系统授权和许可证，查看授权状态和更新许可',
      href: '/settings/auth',
      icon: Key,
    },
    {
      title: '参数配置',
      description: '配置系统参数，包括性能、安全和高级设置',
      href: '/settings/params',
      icon: Sliders,
    },
    {
      title: '系统服务',
      description: '管理系统服务，包括启动、停止和配置服务',
      href: '/settings/services',
      icon: Server,
    },
  ]

  return (
    <MainLayout>
      <div className="container mx-auto py-6 space-y-6">
        <div className="flex flex-col space-y-2">
          <h1 className="text-3xl font-bold tracking-tight">系统设置</h1>
          <p className="text-muted-foreground">管理和配置系统的各项设置</p>
        </div>

        <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-4">
          {settingsCategories.map((category) => (
            <Link to={category.href} key={category.href}>
              <Card className="h-full hover:bg-muted/50 transition-colors cursor-pointer">
                <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
                  <CardTitle className="text-lg font-medium">
                    {category.title}
                  </CardTitle>
                  <category.icon className="h-5 w-5 text-muted-foreground" />
                </CardHeader>
                <CardContent>
                  <CardDescription className="text-sm">
                    {category.description}
                  </CardDescription>
                </CardContent>
              </Card>
            </Link>
          ))}
        </div>
      </div>
    </MainLayout>
  )
}
