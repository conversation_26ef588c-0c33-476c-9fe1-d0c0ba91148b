import { useState } from 'react'
import { <PERSON><PERSON>, <PERSON><PERSON><PERSON>onte<PERSON>, <PERSON><PERSON><PERSON>ist, TabsTrigger } from '@/components/ui/tabs'
import { ServicesList } from '@/components/settings/services/services-list'
import { ServiceConfig } from '@/components/settings/services/service-config'
import { ServicePerformance } from '@/components/settings/services/service-performance'
import { ServiceAlerts } from '@/components/settings/services/service-alerts'
import { ServiceLogs } from '@/components/settings/services/service-logs'
import { SettingsLayout } from '@/components/settings/settings-layout'
import { SignalRProvider } from '@/lib/signalr/signalr-context'
import { MainLayout } from '@/components/layout/main-layout'

export default function ServicesPage() {
  const [selectedService, setSelectedService] = useState<string | null>(null)

  return (
    <MainLayout>
      <SettingsLayout
        title="服务管理"
        description="管理系统内置的通信服务，包括Modbus、MQTT、OPC UA等协议服务">
        <SignalRProvider>
          <div className="container mx-auto py-6">
            <div className="flex flex-col space-y-6">
              <div>
                <h1 className="text-3xl font-bold tracking-tight">服务管理</h1>
                <p className="text-muted-foreground mt-2">
                  管理系统内置的通信服务，包括Modbus、MQTT、OPC UA等协议服务
                </p>
              </div>

              <div className="grid grid-cols-1 md:grid-cols-3 gap-6">
                <div className="md:col-span-1">
                  <ServicesList
                    onSelectService={setSelectedService}
                    selectedService={selectedService}
                  />
                </div>

                <div className="md:col-span-2">
                  {selectedService ? (
                    <Tabs defaultValue="performance" className="w-full">
                      <TabsList className="mb-4">
                        <TabsTrigger value="performance">性能监控</TabsTrigger>
                        <TabsTrigger value="alerts">警报设置</TabsTrigger>
                        <TabsTrigger value="config">配置</TabsTrigger>
                        <TabsTrigger value="logs">日志</TabsTrigger>
                      </TabsList>

                      <TabsContent value="performance">
                        <ServicePerformance serviceId={selectedService} />
                      </TabsContent>

                      <TabsContent value="alerts">
                        <ServiceAlerts serviceId={selectedService} />
                      </TabsContent>

                      <TabsContent value="config">
                        <ServiceConfig serviceId={selectedService} />
                      </TabsContent>

                      <TabsContent value="logs">
                        <ServiceLogs serviceId={selectedService} />
                      </TabsContent>
                    </Tabs>
                  ) : (
                    <div className="flex items-center justify-center h-64 border rounded-md bg-muted/10">
                      <p className="text-muted-foreground">
                        请从左侧选择一个服务
                      </p>
                    </div>
                  )}
                </div>
              </div>
            </div>
          </div>
        </SignalRProvider>
      </SettingsLayout>
    </MainLayout>
  )
}
