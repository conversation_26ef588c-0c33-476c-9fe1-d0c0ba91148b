import { Link } from 'react-router-dom'
import { But<PERSON> } from '@/components/ui/button'
import { MainLayout } from '@/components/layout/main-layout'
import { ServerIcon } from 'lucide-react'

export default function NotFoundPage() {
  return (
    <MainLayout>
      <div className="flex flex-col items-center justify-center min-h-[calc(100vh-4rem)] px-4 text-center">
        <div className="rounded-full bg-gray-100 p-6 mb-6">
          <ServerIcon className="h-10 w-10 text-gray-500" />
        </div>
        <h1 className="text-4xl font-bold tracking-tight text-gray-900 mb-2">
          404
        </h1>
        <h2 className="text-2xl font-semibold text-gray-800 mb-4">
          页面未找到
        </h2>
        <p className="text-gray-500 max-w-md mb-8">
          抱歉，您尝试访问的页面不存在或已被移除。请检查URL是否正确或返回仪表盘。
        </p>
        <div className="flex gap-4">
          <Button asChild variant="outline">
            <Link to="/dashboard">返回仪表盘</Link>
          </Button>
          <Button asChild>
            <Link to="/dashboard">继续浏览</Link>
          </Button>
        </div>
      </div>
    </MainLayout>
  )
}
