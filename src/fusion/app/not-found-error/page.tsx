import { useEffect, useState } from 'react'
import { useNavigate } from 'react-router-dom'
import { Button } from '@/components/ui/button'
import {
  AlertCircle,
  FileQuestion,
  ArrowLeft,
  Home,
  Search,
} from 'lucide-react'

export default function NotFoundErrorPage() {
  const navigate = useNavigate()
  const [currentUrl, setCurrentUrl] = useState<string>('')
  const [errorDetails, setErrorDetails] = useState<string | null>(null)

  useEffect(() => {
    // 获取当前URL
    setCurrentUrl(window.location.href)

    // 尝试从URL参数中获取错误详情
    const urlParams = new URLSearchParams(window.location.search)
    const errorParam = urlParams.get('error')
    if (errorParam) {
      setErrorDetails(decodeURIComponent(errorParam))
    }
  }, [])

  // 处理返回上一页
  const handleGoBack = () => {
    navigate(-1)
  }

  // 处理返回首页
  const handleGoHome = () => {
    navigate('/dashboard')
  }

  // 处理跳转到配置页面
  const handleGoToConfig = () => {
    navigate('/settings/config')
  }

  return (
    <div className="min-h-screen bg-gray-50 flex flex-col items-center justify-center p-4">
      <div className="w-full max-w-2xl bg-white rounded-lg shadow-lg overflow-hidden">
        {/* 头部 */}
        <div className="bg-orange-50 p-6 border-b border-orange-100">
          <div className="flex items-center">
            <div className="h-12 w-12 rounded-full bg-orange-100 flex items-center justify-center mr-4">
              <FileQuestion className="h-6 w-6 text-orange-600" />
            </div>
            <div>
              <h1 className="text-xl font-semibold text-orange-800">
                404 - 资源未找到
              </h1>
              <p className="text-orange-600">请求的资源不存在或已被移除</p>
            </div>
          </div>
        </div>

        {/* 错误详情 */}
        <div className="p-6 border-b border-gray-100">
          <h2 className="text-lg font-medium mb-4">请求信息</h2>
          <p className="text-sm text-gray-600 mb-2">
            <span className="font-medium">请求URL:</span> {currentUrl}
          </p>

          {errorDetails && (
            <div className="bg-orange-50 p-3 rounded-md mb-4 text-sm text-orange-700">
              <div className="font-medium">错误详情:</div>
              <div className="mt-1">{errorDetails}</div>
            </div>
          )}

          <div className="mt-4 bg-yellow-50 p-4 rounded-md">
            <div className="flex items-center mb-2">
              <AlertCircle className="h-5 w-5 text-yellow-600 mr-2" />
              <span className="font-medium text-yellow-700">可能的原因</span>
            </div>
            <ul className="list-disc pl-5 space-y-1 text-sm text-yellow-700">
              <li>请求的资源已被删除或移动</li>
              <li>URL拼写错误</li>
              <li>您没有访问此资源的权限</li>
              <li>资源可能暂时不可用</li>
            </ul>
          </div>
        </div>

        {/* 操作 */}
        <div className="p-6 flex flex-wrap gap-4">
          <Button
            variant="outline"
            className="flex items-center"
            onClick={handleGoBack}>
            <ArrowLeft className="h-4 w-4 mr-2" />
            返回上一页
          </Button>

          <Button className="flex items-center" onClick={handleGoHome}>
            <Home className="h-4 w-4 mr-2" />
            返回首页
          </Button>

          <Button
            variant="secondary"
            className="flex items-center"
            onClick={handleGoToConfig}>
            <AlertCircle className="h-4 w-4 mr-2" />
            检查API配置
          </Button>

          <Button
            variant="outline"
            className="flex items-center ml-auto"
            onClick={() => window.location.reload()}>
            <Search className="h-4 w-4 mr-2" />
            重试请求
          </Button>
        </div>
      </div>

      {/* 故障排除提示 */}
      <div className="mt-6 w-full max-w-2xl bg-white rounded-lg shadow-lg p-6">
        <div className="flex items-center mb-4">
          <AlertCircle className="h-5 w-5 mr-2 text-amber-500" />
          <h2 className="text-lg font-medium">故障排除提示</h2>
        </div>
        <ul className="list-disc pl-5 space-y-2 text-sm text-gray-600">
          <li>检查URL是否正确拼写</li>
          <li>确认您是否有权限访问此资源</li>
          <li>返回首页并从那里导航到所需资源</li>
          <li>如果问题持续存在，请联系系统管理员</li>
          <li>确认相关的API服务是否正常运行</li>
          <li className="text-orange-600 font-medium">
            如果您刚刚修改了API配置，请点击上方的"检查API配置"按钮进行调整
          </li>
        </ul>
      </div>
    </div>
  )
}
