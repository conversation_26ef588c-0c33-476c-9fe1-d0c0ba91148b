/**
 * 工作流编排主页面 - 工作流管理中心
 *
 * 菜单位置：侧边栏主导航 > 工作流编排
 * 路由地址：/workflows
 * 页面功能：工作流编排平台的核心模块，提供自动化工作流的全生命周期管理
 *
 * 主要功能模块：
 * 1. 工作流列表管理
 *    - 卡片式工作流展示，包含名称、描述、状态信息
 *    - 工作流运行状态监控（运行中/已暂停/草稿）
 *    - 最后运行时间和结果状态指示
 *    - 工作流标签分类和快速识别
 *    - 创建者和创建时间信息展示
 *
 * 2. 工作流搜索与筛选
 *    - 按工作流名称和描述关键词搜索
 *    - 按状态筛选（运行中/已暂停/草稿/所有状态）
 *    - 多维度排序功能（按创建时间、最后运行时间等）
 *    - 高级筛选条件（创建者、标签、运行结果等）
 *
 * 3. 工作流分类展示
 *    - 所有工作流：显示系统中所有可见的工作流
 *    - 我的工作流：显示当前用户创建的工作流
 *    - 共享给我的：显示其他用户共享的工作流
 *    - 最近修改：显示最近编辑或运行的工作流
 *
 * 4. 工作流操作管理
 *    - 创建新工作流（跳转到/workflows/new）
 *    - 编辑现有工作流（跳转到工作流设计器）
 *    - 立即运行工作流（手动触发执行）
 *    - 暂停/恢复工作流（控制自动化执行）
 *    - 复制工作流（基于现有工作流创建副本）
 *    - 删除工作流（带确认机制）
 *
 * 5. 工作流状态监控
 *    - 实时工作流执行状态显示
 *    - 最后运行结果状态（成功/失败/运行中/等待中）
 *    - 运行历史记录跟踪
 *    - 错误状态告警和通知
 *    - 执行性能统计信息
 *
 * 6. 工作流模板支持
 *    - 从模板创建工作流快捷入口
 *    - 预定义工作流模板选择
 *    - 常用业务场景模板（数据同步、报告生成等）
 *    - 自定义模板保存和分享
 *
 * 工作流业务场景示例：
 * - 客户数据同步：CRM与营销平台间的数据同步自动化
 * - 订单处理流程：电商平台订单到ERP系统的自动化处理
 * - 每日库存报告：定时生成并分发库存报告
 * - 用户入职流程：新用户注册后的自动化欢迎流程
 * - 支付对账：支付与发票的自动匹配和会计系统更新
 *
 * 工作流状态类型：
 * - 运行中（active）：工作流处于活跃状态，可以被触发执行
 * - 已暂停（paused）：工作流被暂停，不会自动执行
 * - 草稿（draft）：工作流正在设计中，尚未发布
 *
 * 运行结果状态：
 * - 成功（success）：工作流执行成功完成
 * - 失败（failed）：工作流执行过程中出现错误
 * - 运行中（running）：工作流正在执行中
 * - 等待中（pending）：工作流等待触发条件或资源
 *
 * 技术特点：
 * - 基于React和TypeScript开发
 * - 响应式卡片布局设计
 * - 支持多标签页分类展示
 * - 集成搜索和筛选功能
 * - 实时状态更新和监控
 * - 用户友好的操作界面
 *
 * 子功能页面：
 * - /workflows/new - 创建新工作流页面
 * - /workflows/[id] - 工作流详情页面
 * - /workflows/[id]/edit - 工作流编辑器页面
 * - /workflows/[id]/runs - 工作流运行历史页面
 * - /workflows/templates - 工作流模板库页面
 *
 * 业务价值：
 * - 实现业务流程的自动化，提升工作效率
 * - 减少人工干预，降低操作错误风险
 * - 支持复杂业务逻辑的可视化设计
 * - 提供统一的自动化管理平台
 * - 支持跨系统的数据和流程集成
 */

import { Link } from 'react-router-dom'
import { Button } from '@/components/ui/button'
import {
  Card,
  CardContent,
  CardDescription,
  CardFooter,
  CardHeader,
  CardTitle,
} from '@/components/ui/card'
import {
  DropdownMenu,
  DropdownMenuContent,
  DropdownMenuItem,
  DropdownMenuLabel,
  DropdownMenuSeparator,
  DropdownMenuTrigger,
} from '@/components/ui/dropdown-menu'
import { Input } from '@/components/ui/input'
import {
  Select,
  SelectContent,
  SelectItem,
  SelectTrigger,
  SelectValue,
} from '@/components/ui/select'
import { Tabs, TabsContent, TabsList, TabsTrigger } from '@/components/ui/tabs'
import {
  Clock,
  Filter,
  MoreVertical,
  Plus,
  Search,
  ArrowUpDown,
  Play,
  Pause,
  Copy,
  Edit,
  Trash2,
  CheckCircle2,
  AlertCircle,
  Clock3,
} from 'lucide-react'
import { Badge } from '@/components/ui/badge'
import { MainLayout } from '@/components/layout/main-layout'

export default function WorkflowsPage() {
  const workflows = [
    {
      id: 'wf-001',
      name: '客户数据同步',
      description: '在CRM和营销平台之间同步客户数据',
      status: 'active',
      lastRun: '2小时前',
      lastRunStatus: 'success',
      createdBy: '张三',
      createdAt: '2025-03-15',
      tags: ['数据同步', '营销'],
    },
    {
      id: 'wf-002',
      name: '订单处理流程',
      description: '将电商平台的新订单处理到ERP系统',
      status: 'active',
      lastRun: '30分钟前',
      lastRunStatus: 'failed',
      createdBy: '李四',
      createdAt: '2025-02-28',
      tags: ['订单', '电商'],
    },
    {
      id: 'wf-003',
      name: '每日库存报告',
      description: '生成并分发库存报告给相关人员',
      status: 'paused',
      lastRun: '1天前',
      lastRunStatus: 'success',
      createdBy: '王五',
      createdAt: '2025-01-10',
      tags: ['报告', '库存'],
    },
    {
      id: 'wf-004',
      name: '用户入职流程',
      description: '自动发送欢迎邮件和设置新用户任务',
      status: 'draft',
      lastRun: '从未运行',
      lastRunStatus: 'pending',
      createdBy: '赵六',
      createdAt: '2025-03-28',
      tags: ['入职', '邮件'],
    },
    {
      id: 'wf-005',
      name: '支付对账',
      description: '匹配支付与发票并更新会计系统',
      status: 'active',
      lastRun: '4小时前',
      lastRunStatus: 'running',
      createdBy: '张三',
      createdAt: '2025-02-15',
      tags: ['财务', '会计'],
    },
  ]

  const getStatusBadge = (status: string) => {
    switch (status) {
      case 'active':
        return (
          <Badge className="bg-green-100 text-green-800 hover:bg-green-100">
            运行中
          </Badge>
        )
      case 'paused':
        return (
          <Badge className="bg-amber-100 text-amber-800 hover:bg-amber-100">
            已暂停
          </Badge>
        )
      case 'draft':
        return (
          <Badge className="bg-gray-100 text-gray-800 hover:bg-gray-100">
            草稿
          </Badge>
        )
      default:
        return <Badge variant="outline">{status}</Badge>
    }
  }

  const getRunStatusIcon = (status: string) => {
    switch (status) {
      case 'success':
        return <CheckCircle2 className="h-5 w-5 text-green-500" />
      case 'failed':
        return <AlertCircle className="h-5 w-5 text-red-500" />
      case 'running':
        return <Clock3 className="h-5 w-5 text-blue-500" />
      case 'pending':
        return <Clock className="h-5 w-5 text-gray-500" />
      default:
        return null
    }
  }

  return (
    <MainLayout>
      <div className="w-full max-w-none px-6 py-6">
        <div className="flex items-center justify-between mb-6">
          <div>
            <h1 className="text-3xl font-bold tracking-tight">工作流管理</h1>
            <p className="text-gray-500">创建和管理自动化工作流</p>
          </div>
          <Link to="/workflows/new">
            <Button>
              <Plus className="mr-2 h-4 w-4" /> 新建工作流
            </Button>
          </Link>
        </div>

        <div className="flex items-center justify-between mb-6">
          <div className="flex items-center space-x-2">
            <div className="relative">
              <Search className="absolute left-2.5 top-2.5 h-4 w-4 text-gray-500" />
              <Input
                type="search"
                placeholder="搜索工作流..."
                className="pl-8 w-[300px]"
              />
            </div>
            <Button variant="outline" size="icon">
              <Filter className="h-4 w-4" />
              <span className="sr-only">筛选</span>
            </Button>
          </div>
          <div className="flex items-center gap-2">
            <Select defaultValue="all">
              <SelectTrigger className="w-[180px]">
                <SelectValue placeholder="按状态筛选" />
              </SelectTrigger>
              <SelectContent>
                <SelectItem value="all">所有状态</SelectItem>
                <SelectItem value="active">运行中</SelectItem>
                <SelectItem value="paused">已暂停</SelectItem>
                <SelectItem value="draft">草稿</SelectItem>
              </SelectContent>
            </Select>
            <Button variant="outline" size="sm">
              <ArrowUpDown className="mr-2 h-4 w-4" />
              排序
            </Button>
          </div>
        </div>

        <Tabs defaultValue="all" className="space-y-4">
          <TabsList>
            <TabsTrigger value="all">所有工作流</TabsTrigger>
            <TabsTrigger value="my">我的工作流</TabsTrigger>
            <TabsTrigger value="shared">共享给我的</TabsTrigger>
            <TabsTrigger value="recent">最近修改</TabsTrigger>
          </TabsList>
          <TabsContent value="all" className="space-y-4">
            <div className="grid gap-6 grid-cols-1 sm:grid-cols-2 lg:grid-cols-3 xl:grid-cols-4 2xl:grid-cols-5">
              {workflows.map((workflow) => (
                <Card key={workflow.id} className="overflow-hidden">
                  <CardHeader className="pb-3">
                    <div className="flex items-start justify-between">
                      <div>
                        <CardTitle className="text-lg">
                          {workflow.name}
                        </CardTitle>
                        <CardDescription className="mt-1 line-clamp-2">
                          {workflow.description}
                        </CardDescription>
                      </div>
                      <DropdownMenu>
                        <DropdownMenuTrigger asChild>
                          <Button variant="ghost" size="icon">
                            <MoreVertical className="h-4 w-4" />
                            <span className="sr-only">菜单</span>
                          </Button>
                        </DropdownMenuTrigger>
                        <DropdownMenuContent align="end">
                          <DropdownMenuLabel>操作</DropdownMenuLabel>
                          <DropdownMenuItem>
                            <Edit className="mr-2 h-4 w-4" /> 编辑
                          </DropdownMenuItem>
                          <DropdownMenuItem>
                            <Play className="mr-2 h-4 w-4" /> 立即运行
                          </DropdownMenuItem>
                          <DropdownMenuItem>
                            {workflow.status === 'paused' ? (
                              <>
                                <Play className="mr-2 h-4 w-4" /> 恢复
                              </>
                            ) : (
                              <>
                                <Pause className="mr-2 h-4 w-4" /> 暂停
                              </>
                            )}
                          </DropdownMenuItem>
                          <DropdownMenuItem>
                            <Copy className="mr-2 h-4 w-4" /> 复制
                          </DropdownMenuItem>
                          <DropdownMenuSeparator />
                          <DropdownMenuItem className="text-red-600">
                            <Trash2 className="mr-2 h-4 w-4" /> 删除
                          </DropdownMenuItem>
                        </DropdownMenuContent>
                      </DropdownMenu>
                    </div>
                  </CardHeader>
                  <CardContent className="pb-2">
                    <div className="flex flex-wrap gap-2">
                      {workflow.tags.map((tag) => (
                        <Badge
                          key={tag}
                          variant="secondary"
                          className="text-xs">
                          {tag}
                        </Badge>
                      ))}
                    </div>
                  </CardContent>
                  <CardFooter className="border-t pt-3">
                    <div className="flex w-full items-center justify-between">
                      <div className="flex items-center text-sm text-gray-500">
                        <Clock className="mr-1 h-4 w-4" />
                        {workflow.lastRun}
                      </div>
                      <div className="flex items-center gap-2">
                        {getRunStatusIcon(workflow.lastRunStatus)}
                        {getStatusBadge(workflow.status)}
                      </div>
                    </div>
                  </CardFooter>
                </Card>
              ))}
              <Card className="flex h-full flex-col items-center justify-center border-dashed p-6">
                <div className="flex flex-col items-center justify-center text-center">
                  <div className="mb-4 rounded-full bg-primary/10 p-3">
                    <Plus className="h-6 w-6 text-primary" />
                  </div>
                  <h3 className="mb-1 text-lg font-medium">创建新工作流</h3>
                  <p className="mb-4 text-sm text-gray-500">
                    从头开始或使用模板
                  </p>
                  <Link to="/workflows/new">
                    <Button>创建工作流</Button>
                  </Link>
                </div>
              </Card>
            </div>
          </TabsContent>
          <TabsContent value="my">
            <div className="rounded-md border p-8 text-center">
              <h3 className="text-lg font-medium">我的工作流</h3>
              <p className="mt-2 text-sm text-gray-500">
                您创建的工作流将显示在这里
              </p>
            </div>
          </TabsContent>
          <TabsContent value="shared">
            <div className="rounded-md border p-8 text-center">
              <h3 className="text-lg font-medium">共享工作流</h3>
              <p className="mt-2 text-sm text-gray-500">
                共享给您的工作流将显示在这里
              </p>
            </div>
          </TabsContent>
          <TabsContent value="recent">
            <div className="rounded-md border p-8 text-center">
              <h3 className="text-lg font-medium">最近修改</h3>
              <p className="mt-2 text-sm text-gray-500">
                最近修改的工作流将显示在这里
              </p>
            </div>
          </TabsContent>
        </Tabs>
      </div>
    </MainLayout>
  )
}
