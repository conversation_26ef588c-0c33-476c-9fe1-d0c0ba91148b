/**
 * 工作流监控中心页面 - 工作流运行监控与分析
 *
 * 菜单位置：侧边栏主导航 > 工作流编排 > 监控中心
 * 路由地址：/workflows/monitoring
 * 页面功能：提供工作流运行状态的实时监控、性能分析和报警管理
 *
 * 主要功能模块：
 * 1. 实时监控仪表盘
 *    - 工作流运行状态概览
 *    - 实时性能指标监控
 *    - 系统资源使用情况
 *    - 活跃工作流实例统计
 *
 * 2. 运行历史与日志
 *    - 工作流执行历史记录
 *    - 详细运行日志查看
 *    - 错误追踪和分析
 *    - 执行时间性能统计
 *
 * 3. 报警与通知管理
 *    - 工作流失败报警
 *    - 性能异常告警
 *    - 自定义监控规则
 *    - 通知渠道配置
 *
 * 4. 统计分析与报表
 *    - 执行成功率统计
 *    - 性能趋势分析
 *    - 资源使用报表
 *    - 用户行为分析
 */

import { Button } from '@/components/ui/button'
import {
  Card,
  CardContent,
  CardDescription,
  CardHeader,
  CardTitle,
} from '@/components/ui/card'
import { Input } from '@/components/ui/input'
import {
  Select,
  SelectContent,
  SelectItem,
  SelectTrigger,
  SelectValue,
} from '@/components/ui/select'
import { Tabs, TabsContent, TabsList, TabsTrigger } from '@/components/ui/tabs'
import {
  Activity,
  AlertTriangle,
  CheckCircle2,
  Clock,
  Play,
  Pause,
  RefreshCw,
  Calendar,
  TrendingUp,
  TrendingDown,
  Search,
  Filter,
  BarChart3,
  PieChart,
  Monitor,
  Server,
  Cpu,
  MemoryStick,
  Zap,
} from 'lucide-react'
import { Badge } from '@/components/ui/badge'
import { MainLayout } from '@/components/layout/main-layout'
import { useState } from 'react'
import { Progress } from '@/components/ui/progress'

export default function WorkflowMonitoringPage() {
  const [timeRange, setTimeRange] = useState('24h')
  const [refreshInterval, setRefreshInterval] = useState('30s')

  // 模拟实时数据
  const stats = {
    totalWorkflows: 156,
    activeWorkflows: 23,
    completedToday: 89,
    failedToday: 7,
    successRate: 92.7,
    avgExecutionTime: '2.3分钟',
  }

  const systemMetrics = {
    cpuUsage: 45,
    memoryUsage: 62,
    diskUsage: 38,
    networkThroughput: '15.2 MB/s',
  }

  const recentExecutions = [
    {
      id: 'exec-001',
      workflowName: '客户数据同步',
      status: 'success',
      startTime: '2025-06-13 14:30:15',
      duration: '1分32秒',
      user: '张三',
    },
    {
      id: 'exec-002',
      workflowName: '订单处理流程',
      status: 'running',
      startTime: '2025-06-13 14:25:08',
      duration: '运行中...',
      user: '李四',
    },
    {
      id: 'exec-003',
      workflowName: '每日销售报告',
      status: 'failed',
      startTime: '2025-06-13 14:20:45',
      duration: '失败',
      user: '系统',
    },
    {
      id: 'exec-004',
      workflowName: '库存预警通知',
      status: 'success',
      startTime: '2025-06-13 14:15:22',
      duration: '45秒',
      user: '王五',
    },
    {
      id: 'exec-005',
      workflowName: 'API数据聚合',
      status: 'success',
      startTime: '2025-06-13 14:10:33',
      duration: '3分15秒',
      user: '赵六',
    },
  ]

  const alerts = [
    {
      id: 'alert-001',
      type: 'error',
      message: '订单处理流程执行失败',
      time: '5分钟前',
      severity: 'high',
    },
    {
      id: 'alert-002',
      type: 'warning',
      message: 'CPU使用率超过80%',
      time: '10分钟前',
      severity: 'medium',
    },
    {
      id: 'alert-003',
      type: 'info',
      message: '系统维护计划通知',
      time: '30分钟前',
      severity: 'low',
    },
  ]

  const getStatusIcon = (status: string) => {
    switch (status) {
      case 'success':
        return <CheckCircle2 className="h-4 w-4 text-green-500" />
      case 'failed':
        return <AlertTriangle className="h-4 w-4 text-red-500" />
      case 'running':
        return <Play className="h-4 w-4 text-blue-500" />
      default:
        return <Clock className="h-4 w-4 text-gray-500" />
    }
  }

  const getStatusBadge = (status: string) => {
    switch (status) {
      case 'success':
        return <Badge className="bg-green-100 text-green-800">成功</Badge>
      case 'failed':
        return <Badge className="bg-red-100 text-red-800">失败</Badge>
      case 'running':
        return <Badge className="bg-blue-100 text-blue-800">运行中</Badge>
      default:
        return <Badge variant="outline">{status}</Badge>
    }
  }

  const getAlertIcon = (type: string) => {
    switch (type) {
      case 'error':
        return <AlertTriangle className="h-4 w-4 text-red-500" />
      case 'warning':
        return <AlertTriangle className="h-4 w-4 text-amber-500" />
      case 'info':
        return <Activity className="h-4 w-4 text-blue-500" />
      default:
        return <Activity className="h-4 w-4 text-gray-500" />
    }
  }

  return (
    <MainLayout>
      <div className="w-full max-w-none px-6 py-6">
        <div className="flex items-center justify-between mb-6">
          <div>
            <h1 className="text-3xl font-bold tracking-tight">
              工作流监控中心
            </h1>
            <p className="text-gray-500">实时监控工作流运行状态和系统性能</p>
          </div>
          <div className="flex items-center gap-2">
            <Select value={timeRange} onValueChange={setTimeRange}>
              <SelectTrigger className="w-[120px]">
                <SelectValue />
              </SelectTrigger>
              <SelectContent>
                <SelectItem value="1h">1小时</SelectItem>
                <SelectItem value="24h">24小时</SelectItem>
                <SelectItem value="7d">7天</SelectItem>
                <SelectItem value="30d">30天</SelectItem>
              </SelectContent>
            </Select>
            <Select value={refreshInterval} onValueChange={setRefreshInterval}>
              <SelectTrigger className="w-[120px]">
                <SelectValue />
              </SelectTrigger>
              <SelectContent>
                <SelectItem value="10s">10秒</SelectItem>
                <SelectItem value="30s">30秒</SelectItem>
                <SelectItem value="1m">1分钟</SelectItem>
                <SelectItem value="5m">5分钟</SelectItem>
              </SelectContent>
            </Select>
            <Button variant="outline" size="icon">
              <RefreshCw className="h-4 w-4" />
            </Button>
          </div>
        </div>

        {/* 实时统计卡片 */}
        <div className="grid gap-4 md:grid-cols-2 lg:grid-cols-4 mb-6">
          <Card>
            <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
              <CardTitle className="text-sm font-medium">总工作流</CardTitle>
              <Activity className="h-4 w-4 text-muted-foreground" />
            </CardHeader>
            <CardContent>
              <div className="text-2xl font-bold">{stats.totalWorkflows}</div>
              <p className="text-xs text-muted-foreground">+12% 较上周</p>
            </CardContent>
          </Card>
          <Card>
            <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
              <CardTitle className="text-sm font-medium">运行中</CardTitle>
              <Play className="h-4 w-4 text-muted-foreground" />
            </CardHeader>
            <CardContent>
              <div className="text-2xl font-bold">{stats.activeWorkflows}</div>
              <p className="text-xs text-muted-foreground">实时活跃实例</p>
            </CardContent>
          </Card>
          <Card>
            <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
              <CardTitle className="text-sm font-medium">今日完成</CardTitle>
              <CheckCircle2 className="h-4 w-4 text-muted-foreground" />
            </CardHeader>
            <CardContent>
              <div className="text-2xl font-bold">{stats.completedToday}</div>
              <p className="text-xs text-muted-foreground">+7% 较昨日</p>
            </CardContent>
          </Card>
          <Card>
            <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
              <CardTitle className="text-sm font-medium">成功率</CardTitle>
              <TrendingUp className="h-4 w-4 text-muted-foreground" />
            </CardHeader>
            <CardContent>
              <div className="text-2xl font-bold">{stats.successRate}%</div>
              <p className="text-xs text-muted-foreground">+2.1% 较上周</p>
            </CardContent>
          </Card>
        </div>

        <Tabs defaultValue="overview" className="space-y-4">
          <TabsList>
            <TabsTrigger value="overview">概览</TabsTrigger>
            <TabsTrigger value="executions">执行历史</TabsTrigger>
            <TabsTrigger value="alerts">报警中心</TabsTrigger>
            <TabsTrigger value="performance">性能分析</TabsTrigger>
          </TabsList>

          <TabsContent value="overview" className="space-y-4">
            <div className="grid gap-4 md:grid-cols-2 lg:grid-cols-3">
              {/* 系统资源监控 */}
              <Card className="lg:col-span-2">
                <CardHeader>
                  <CardTitle className="flex items-center gap-2">
                    <Monitor className="h-5 w-5" />
                    系统资源监控
                  </CardTitle>
                  <CardDescription>服务器资源使用情况</CardDescription>
                </CardHeader>
                <CardContent className="space-y-4">
                  <div className="space-y-2">
                    <div className="flex items-center justify-between">
                      <div className="flex items-center gap-2">
                        <Cpu className="h-4 w-4" />
                        <span className="text-sm">CPU使用率</span>
                      </div>
                      <span className="text-sm font-medium">
                        {systemMetrics.cpuUsage}%
                      </span>
                    </div>
                    <Progress value={systemMetrics.cpuUsage} className="h-2" />
                  </div>
                  <div className="space-y-2">
                    <div className="flex items-center justify-between">
                      <div className="flex items-center gap-2">
                        <MemoryStick className="h-4 w-4" />
                        <span className="text-sm">内存使用率</span>
                      </div>
                      <span className="text-sm font-medium">
                        {systemMetrics.memoryUsage}%
                      </span>
                    </div>
                    <Progress
                      value={systemMetrics.memoryUsage}
                      className="h-2"
                    />
                  </div>
                  <div className="space-y-2">
                    <div className="flex items-center justify-between">
                      <div className="flex items-center gap-2">
                        <Server className="h-4 w-4" />
                        <span className="text-sm">磁盘使用率</span>
                      </div>
                      <span className="text-sm font-medium">
                        {systemMetrics.diskUsage}%
                      </span>
                    </div>
                    <Progress value={systemMetrics.diskUsage} className="h-2" />
                  </div>
                  <div className="flex items-center justify-between">
                    <div className="flex items-center gap-2">
                      <Zap className="h-4 w-4" />
                      <span className="text-sm">网络吞吐量</span>
                    </div>
                    <span className="text-sm font-medium">
                      {systemMetrics.networkThroughput}
                    </span>
                  </div>
                </CardContent>
              </Card>

              {/* 最新报警 */}
              <Card>
                <CardHeader>
                  <CardTitle className="flex items-center gap-2">
                    <AlertTriangle className="h-5 w-5" />
                    最新报警
                  </CardTitle>
                  <CardDescription>系统报警与通知</CardDescription>
                </CardHeader>
                <CardContent>
                  <div className="space-y-3">
                    {alerts.slice(0, 3).map((alert) => (
                      <div
                        key={alert.id}
                        className="flex items-start gap-3 p-2 rounded-md border">
                        {getAlertIcon(alert.type)}
                        <div className="flex-1 min-w-0">
                          <p className="text-sm font-medium line-clamp-2">
                            {alert.message}
                          </p>
                          <p className="text-xs text-muted-foreground">
                            {alert.time}
                          </p>
                        </div>
                      </div>
                    ))}
                  </div>
                </CardContent>
              </Card>
            </div>
          </TabsContent>

          <TabsContent value="executions" className="space-y-4">
            <div className="flex items-center justify-between">
              <div className="flex items-center space-x-2">
                <div className="relative">
                  <Search className="absolute left-2.5 top-2.5 h-4 w-4 text-gray-500" />
                  <Input
                    type="search"
                    placeholder="搜索执行记录..."
                    className="pl-8 w-[300px]"
                  />
                </div>
                <Button variant="outline" size="icon">
                  <Filter className="h-4 w-4" />
                </Button>
              </div>
              <div className="flex items-center gap-2">
                <Select defaultValue="all">
                  <SelectTrigger className="w-[150px]">
                    <SelectValue placeholder="状态筛选" />
                  </SelectTrigger>
                  <SelectContent>
                    <SelectItem value="all">所有状态</SelectItem>
                    <SelectItem value="success">成功</SelectItem>
                    <SelectItem value="failed">失败</SelectItem>
                    <SelectItem value="running">运行中</SelectItem>
                  </SelectContent>
                </Select>
              </div>
            </div>

            <Card>
              <CardHeader>
                <CardTitle>执行历史</CardTitle>
                <CardDescription>最近的工作流执行记录</CardDescription>
              </CardHeader>
              <CardContent>
                <div className="space-y-3">
                  {recentExecutions.map((execution) => (
                    <div
                      key={execution.id}
                      className="flex items-center justify-between p-3 border rounded-md">
                      <div className="flex items-center gap-3">
                        {getStatusIcon(execution.status)}
                        <div>
                          <p className="font-medium">
                            {execution.workflowName}
                          </p>
                          <p className="text-sm text-muted-foreground">
                            {execution.startTime} · {execution.user}
                          </p>
                        </div>
                      </div>
                      <div className="flex items-center gap-3">
                        <span className="text-sm text-muted-foreground">
                          {execution.duration}
                        </span>
                        {getStatusBadge(execution.status)}
                      </div>
                    </div>
                  ))}
                </div>
              </CardContent>
            </Card>
          </TabsContent>

          <TabsContent value="alerts">
            <Card>
              <CardHeader>
                <CardTitle>报警中心</CardTitle>
                <CardDescription>系统报警和通知管理</CardDescription>
              </CardHeader>
              <CardContent>
                <div className="space-y-4">
                  {alerts.map((alert) => (
                    <div
                      key={alert.id}
                      className="flex items-start gap-3 p-4 border rounded-md">
                      {getAlertIcon(alert.type)}
                      <div className="flex-1">
                        <div className="flex items-center justify-between">
                          <p className="font-medium">{alert.message}</p>
                          <Badge
                            variant={
                              alert.severity === 'high'
                                ? 'destructive'
                                : 'secondary'
                            }>
                            {alert.severity === 'high'
                              ? '高'
                              : alert.severity === 'medium'
                              ? '中'
                              : '低'}
                          </Badge>
                        </div>
                        <p className="text-sm text-muted-foreground mt-1">
                          {alert.time}
                        </p>
                      </div>
                    </div>
                  ))}
                </div>
              </CardContent>
            </Card>
          </TabsContent>

          <TabsContent value="performance">
            <div className="grid gap-4 md:grid-cols-2">
              <Card>
                <CardHeader>
                  <CardTitle className="flex items-center gap-2">
                    <BarChart3 className="h-5 w-5" />
                    执行趋势
                  </CardTitle>
                  <CardDescription>工作流执行次数趋势</CardDescription>
                </CardHeader>
                <CardContent>
                  <div className="h-[200px] flex items-center justify-center border-2 border-dashed rounded-md">
                    <p className="text-muted-foreground">图表区域 - 执行趋势</p>
                  </div>
                </CardContent>
              </Card>

              <Card>
                <CardHeader>
                  <CardTitle className="flex items-center gap-2">
                    <PieChart className="h-5 w-5" />
                    成功率分布
                  </CardTitle>
                  <CardDescription>工作流成功率统计</CardDescription>
                </CardHeader>
                <CardContent>
                  <div className="h-[200px] flex items-center justify-center border-2 border-dashed rounded-md">
                    <p className="text-muted-foreground">
                      图表区域 - 成功率分布
                    </p>
                  </div>
                </CardContent>
              </Card>
            </div>
          </TabsContent>
        </Tabs>
      </div>
    </MainLayout>
  )
}
