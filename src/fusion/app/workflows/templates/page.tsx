/**
 * 工作流模板库页面 - 工作流模板管理中心
 *
 * 菜单位置：侧边栏主导航 > 工作流编排 > 模板库
 * 路由地址：/workflows/templates
 * 页面功能：提供预定义工作流模板的浏览、管理和使用功能
 *
 * 主要功能模块：
 * 1. 模板浏览与展示
 *    - 卡片式模板展示，包含名称、描述、分类信息
 *    - 模板预览图和使用统计
 *    - 模板难度等级和适用场景标识
 *    - 创建者和更新时间信息展示
 *
 * 2. 模板搜索与筛选
 *    - 按模板名称和描述关键词搜索
 *    - 按分类筛选（集成、业务流程、报表等）
 *    - 按难度筛选（简单、中等、复杂）
 *    - 按使用频率排序
 *
 * 3. 模板分类管理
 *    - 所有模板：显示系统中所有可用模板
 *    - 推荐模板：显示系统推荐的热门模板
 *    - 我的模板：显示用户创建或收藏的模板
 *    - 最近使用：显示最近使用过的模板
 */

import { Link } from 'react-router-dom'
import { Button } from '@/components/ui/button'
import {
  Card,
  CardContent,
  CardDescription,
  CardFooter,
  CardHeader,
  CardTitle,
} from '@/components/ui/card'
import { Input } from '@/components/ui/input'
import {
  Select,
  SelectContent,
  SelectItem,
  SelectTrigger,
  SelectValue,
} from '@/components/ui/select'
import { Tabs, TabsContent, TabsList, TabsTrigger } from '@/components/ui/tabs'
import {
  Search,
  Filter,
  Star,
  Download,
  Eye,
  Plus,
  Heart,
  Clock,
  Users,
  ArrowUpDown,
} from 'lucide-react'
import { Badge } from '@/components/ui/badge'
import { MainLayout } from '@/components/layout/main-layout'
import { useState } from 'react'

export default function WorkflowTemplatesPage() {
  const [searchTerm, setSearchTerm] = useState('')
  const [selectedCategory, setSelectedCategory] = useState('all')
  const [selectedComplexity, setSelectedComplexity] = useState('all')

  const templates = [
    {
      id: 'template-001',
      name: '客户数据同步',
      description:
        '在CRM和营销平台之间自动同步客户数据，包含数据验证和错误处理',
      category: '数据集成',
      complexity: '中等',
      usageCount: 245,
      rating: 4.8,
      author: '系统管理员',
      updatedAt: '2025-03-15',
      tags: ['CRM', '数据同步', '营销'],
      featured: true,
    },
    {
      id: 'template-002',
      name: '订单审批流程',
      description: '多级订单审批工作流，支持并行审批和条件分支',
      category: '业务流程',
      complexity: '复杂',
      usageCount: 189,
      rating: 4.6,
      author: '李四',
      updatedAt: '2025-03-10',
      tags: ['审批', '订单', '业务流程'],
      featured: true,
    },
    {
      id: 'template-003',
      name: '每日销售报告',
      description: '自动生成并分发每日销售数据报告给相关团队',
      category: '报表生成',
      complexity: '简单',
      usageCount: 312,
      rating: 4.9,
      author: '王五',
      updatedAt: '2025-03-12',
      tags: ['报表', '销售', '定时任务'],
      featured: false,
    },
    {
      id: 'template-004',
      name: '新员工入职流程',
      description: '自动化新员工入职流程，包含账号创建、权限分配和欢迎邮件',
      category: '人事管理',
      complexity: '中等',
      usageCount: 156,
      rating: 4.7,
      author: '赵六',
      updatedAt: '2025-03-08',
      tags: ['入职', '人事', '自动化'],
      featured: false,
    },
    {
      id: 'template-005',
      name: 'API数据聚合',
      description: '从多个API源收集数据并进行聚合分析',
      category: '数据集成',
      complexity: '复杂',
      usageCount: 98,
      rating: 4.5,
      author: '张三',
      updatedAt: '2025-03-05',
      tags: ['API', '数据聚合', '分析'],
      featured: false,
    },
    {
      id: 'template-006',
      name: '库存预警通知',
      description: '监控库存水平并发送预警通知给采购团队',
      category: '监控告警',
      complexity: '简单',
      usageCount: 203,
      rating: 4.4,
      author: '刘七',
      updatedAt: '2025-03-01',
      tags: ['库存', '预警', '通知'],
      featured: false,
    },
  ]

  const categories = [
    { value: 'all', label: '所有分类' },
    { value: '数据集成', label: '数据集成' },
    { value: '业务流程', label: '业务流程' },
    { value: '报表生成', label: '报表生成' },
    { value: '人事管理', label: '人事管理' },
    { value: '监控告警', label: '监控告警' },
  ]

  const complexityLevels = [
    { value: 'all', label: '所有难度' },
    { value: '简单', label: '简单' },
    { value: '中等', label: '中等' },
    { value: '复杂', label: '复杂' },
  ]

  const getComplexityBadge = (complexity: string) => {
    switch (complexity) {
      case '简单':
        return (
          <Badge className="bg-green-100 text-green-800 hover:bg-green-100">
            {complexity}
          </Badge>
        )
      case '中等':
        return (
          <Badge className="bg-amber-100 text-amber-800 hover:bg-amber-100">
            {complexity}
          </Badge>
        )
      case '复杂':
        return (
          <Badge className="bg-red-100 text-red-800 hover:bg-red-100">
            {complexity}
          </Badge>
        )
      default:
        return <Badge variant="outline">{complexity}</Badge>
    }
  }

  const filteredTemplates = templates.filter((template) => {
    const matchesSearch =
      template.name.toLowerCase().includes(searchTerm.toLowerCase()) ||
      template.description.toLowerCase().includes(searchTerm.toLowerCase())
    const matchesCategory =
      selectedCategory === 'all' || template.category === selectedCategory
    const matchesComplexity =
      selectedComplexity === 'all' || template.complexity === selectedComplexity

    return matchesSearch && matchesCategory && matchesComplexity
  })

  const featuredTemplates = templates.filter((template) => template.featured)

  return (
    <MainLayout>
      <div className="w-full max-w-none px-6 py-6">
        <div className="flex items-center justify-between mb-6">
          <div>
            <h1 className="text-3xl font-bold tracking-tight">工作流模板库</h1>
            <p className="text-gray-500">浏览和使用预定义的工作流模板</p>
          </div>
          <Button asChild>
            <Link to="/workflows/new">
              <Plus className="mr-2 h-4 w-4" /> 创建模板
            </Link>
          </Button>
        </div>

        <div className="flex items-center justify-between mb-6">
          <div className="flex items-center space-x-2">
            <div className="relative">
              <Search className="absolute left-2.5 top-2.5 h-4 w-4 text-gray-500" />
              <Input
                type="search"
                placeholder="搜索模板..."
                className="pl-8 w-[300px]"
                value={searchTerm}
                onChange={(e) => setSearchTerm(e.target.value)}
              />
            </div>
            <Button variant="outline" size="icon">
              <Filter className="h-4 w-4" />
              <span className="sr-only">筛选</span>
            </Button>
          </div>
          <div className="flex items-center gap-2">
            <Select
              value={selectedCategory}
              onValueChange={setSelectedCategory}>
              <SelectTrigger className="w-[180px]">
                <SelectValue />
              </SelectTrigger>
              <SelectContent>
                {categories.map((category) => (
                  <SelectItem key={category.value} value={category.value}>
                    {category.label}
                  </SelectItem>
                ))}
              </SelectContent>
            </Select>
            <Select
              value={selectedComplexity}
              onValueChange={setSelectedComplexity}>
              <SelectTrigger className="w-[180px]">
                <SelectValue />
              </SelectTrigger>
              <SelectContent>
                {complexityLevels.map((level) => (
                  <SelectItem key={level.value} value={level.value}>
                    {level.label}
                  </SelectItem>
                ))}
              </SelectContent>
            </Select>
            <Button variant="outline" size="sm">
              <ArrowUpDown className="mr-2 h-4 w-4" />
              排序
            </Button>
          </div>
        </div>

        <Tabs defaultValue="all" className="space-y-4">
          <TabsList>
            <TabsTrigger value="all">所有模板</TabsTrigger>
            <TabsTrigger value="featured">推荐模板</TabsTrigger>
            <TabsTrigger value="my">我的模板</TabsTrigger>
            <TabsTrigger value="recent">最近使用</TabsTrigger>
          </TabsList>

          <TabsContent value="all" className="space-y-4">
            <div className="grid gap-6 grid-cols-1 sm:grid-cols-2 lg:grid-cols-3 xl:grid-cols-4 2xl:grid-cols-5">
              {filteredTemplates.map((template) => (
                <Card
                  key={template.id}
                  className="overflow-hidden hover:shadow-lg transition-shadow">
                  <CardHeader className="pb-3">
                    <div className="flex items-start justify-between">
                      <div className="flex-1">
                        <div className="flex items-center gap-2 mb-2">
                          <CardTitle className="text-lg line-clamp-1">
                            {template.name}
                          </CardTitle>
                          {template.featured && (
                            <Star className="h-4 w-4 text-yellow-500 fill-current" />
                          )}
                        </div>
                        <CardDescription className="line-clamp-2">
                          {template.description}
                        </CardDescription>
                      </div>
                    </div>
                  </CardHeader>
                  <CardContent className="pb-2">
                    <div className="flex flex-wrap gap-2 mb-3">
                      <Badge variant="secondary">{template.category}</Badge>
                      {getComplexityBadge(template.complexity)}
                    </div>
                    <div className="flex flex-wrap gap-1">
                      {template.tags.map((tag) => (
                        <Badge key={tag} variant="outline" className="text-xs">
                          {tag}
                        </Badge>
                      ))}
                    </div>
                  </CardContent>
                  <CardFooter className="border-t pt-3">
                    <div className="flex w-full items-center justify-between text-sm text-gray-500">
                      <div className="flex items-center gap-4">
                        <div className="flex items-center gap-1">
                          <Users className="h-3 w-3" />
                          {template.usageCount}
                        </div>
                        <div className="flex items-center gap-1">
                          <Star className="h-3 w-3" />
                          {template.rating}
                        </div>
                      </div>
                      <div className="flex gap-1">
                        <Button variant="ghost" size="sm">
                          <Eye className="h-4 w-4" />
                        </Button>
                        <Button variant="ghost" size="sm">
                          <Heart className="h-4 w-4" />
                        </Button>
                        <Button variant="ghost" size="sm" asChild>
                          <Link to={`/workflows/new?template=${template.id}`}>
                            <Download className="h-4 w-4" />
                          </Link>
                        </Button>
                      </div>
                    </div>
                  </CardFooter>
                </Card>
              ))}
            </div>
          </TabsContent>

          <TabsContent value="featured" className="space-y-4">
            <div className="grid gap-6 grid-cols-1 sm:grid-cols-2 lg:grid-cols-3 xl:grid-cols-4 2xl:grid-cols-5">
              {featuredTemplates.map((template) => (
                <Card
                  key={template.id}
                  className="overflow-hidden hover:shadow-lg transition-shadow">
                  <CardHeader className="pb-3">
                    <div className="flex items-start justify-between">
                      <div className="flex-1">
                        <div className="flex items-center gap-2 mb-2">
                          <CardTitle className="text-lg line-clamp-1">
                            {template.name}
                          </CardTitle>
                          <Star className="h-4 w-4 text-yellow-500 fill-current" />
                        </div>
                        <CardDescription className="line-clamp-2">
                          {template.description}
                        </CardDescription>
                      </div>
                    </div>
                  </CardHeader>
                  <CardContent className="pb-2">
                    <div className="flex flex-wrap gap-2 mb-3">
                      <Badge variant="secondary">{template.category}</Badge>
                      {getComplexityBadge(template.complexity)}
                    </div>
                    <div className="flex flex-wrap gap-1">
                      {template.tags.map((tag) => (
                        <Badge key={tag} variant="outline" className="text-xs">
                          {tag}
                        </Badge>
                      ))}
                    </div>
                  </CardContent>
                  <CardFooter className="border-t pt-3">
                    <div className="flex w-full items-center justify-between text-sm text-gray-500">
                      <div className="flex items-center gap-4">
                        <div className="flex items-center gap-1">
                          <Users className="h-3 w-3" />
                          {template.usageCount}
                        </div>
                        <div className="flex items-center gap-1">
                          <Star className="h-3 w-3" />
                          {template.rating}
                        </div>
                      </div>
                      <div className="flex gap-1">
                        <Button variant="ghost" size="sm">
                          <Eye className="h-4 w-4" />
                        </Button>
                        <Button variant="ghost" size="sm">
                          <Heart className="h-4 w-4" />
                        </Button>
                        <Button variant="ghost" size="sm" asChild>
                          <Link to={`/workflows/new?template=${template.id}`}>
                            <Download className="h-4 w-4" />
                          </Link>
                        </Button>
                      </div>
                    </div>
                  </CardFooter>
                </Card>
              ))}
            </div>
          </TabsContent>

          <TabsContent value="my">
            <div className="rounded-md border p-8 text-center">
              <h3 className="text-lg font-medium">我的模板</h3>
              <p className="mt-2 text-sm text-gray-500">
                您创建或收藏的模板将显示在这里
              </p>
            </div>
          </TabsContent>

          <TabsContent value="recent">
            <div className="rounded-md border p-8 text-center">
              <h3 className="text-lg font-medium">最近使用</h3>
              <p className="mt-2 text-sm text-gray-500">
                最近使用的模板将显示在这里
              </p>
            </div>
          </TabsContent>
        </Tabs>
      </div>
    </MainLayout>
  )
}
