import { useState, useEffect } from 'react'
import { useNavigate } from 'react-router-dom'
import { MainLayout } from '@/components/layout/main-layout'
import { <PERSON><PERSON>, <PERSON><PERSON>Content, <PERSON><PERSON>List, TabsTrigger } from '@/components/ui/tabs'
import { ProfileForm } from '@/components/profile/profile-form'
import { SecuritySettings } from '@/components/profile/security-settings'
import { NotificationPreferences } from '@/components/profile/notification-preferences'
import { ActivityLog } from '@/components/profile/activity-log'
import { User, Shield, Bell, ClipboardList, Loader2 } from 'lucide-react'
import { authApi } from '@/lib/api/auth-api'
import { LoginUserOutput } from '@/lib/api-services/models/login-user-output'
import { useToast } from '@/components/ui/use-toast'

export default function ProfilePage() {
  const [activeTab, setActiveTab] = useState('profile')
  const [userInfo, setUserInfo] = useState<LoginUserOutput | null>(null)
  const [isLoading, setIsLoading] = useState(true)
  const navigate = useNavigate()
  const { toast } = useToast()

  // 获取用户信息
  useEffect(() => {
    const fetchUserInfo = async () => {
      try {
        setIsLoading(true)
        const response = await authApi.getUserInfo()
        if (response && response.data && response.data.data) {
          setUserInfo(response.data.data)
          console.log('获取到用户信息:', response.data.data)
        } else {
          console.warn('无法获取用户信息，可能未登录')
          toast({
            title: '获取用户信息失败',
            description: '请重新登录后再试',
            variant: 'destructive',
          })
          navigate('/login')
        }
      } catch (error) {
        console.error('获取用户信息出错:', error)
        toast({
          title: '获取用户信息失败',
          description: '请检查网络连接或重新登录',
          variant: 'destructive',
        })
      } finally {
        setIsLoading(false)
      }
    }

    fetchUserInfo()
  }, [navigate, toast])

  return (
    <MainLayout>
      <div className="container mx-auto max-w-7xl w-full px-4 sm:px-6 lg:px-8 py-6">
        <div className="flex flex-col space-y-2 mb-6">
          <h1 className="text-3xl font-bold tracking-tight">个人中心</h1>
          <p className="text-gray-500">管理您的个人信息和账户设置</p>
        </div>

        {isLoading ? (
          <div className="flex justify-center items-center py-12">
            <Loader2 className="h-8 w-8 animate-spin text-primary" />
            <span className="ml-2 text-lg">加载用户信息中...</span>
          </div>
        ) : (
          <Tabs
            value={activeTab}
            onValueChange={setActiveTab}
            className="space-y-6 w-full">
            <TabsList className="bg-muted w-full justify-center">
              <TabsTrigger value="profile" className="flex items-center gap-2">
                <User className="h-4 w-4" />
                <span>个人信息</span>
              </TabsTrigger>
              <TabsTrigger value="security" className="flex items-center gap-2">
                <Shield className="h-4 w-4" />
                <span>安全设置</span>
              </TabsTrigger>
              <TabsTrigger
                value="notifications"
                className="flex items-center gap-2">
                <Bell className="h-4 w-4" />
                <span>通知偏好</span>
              </TabsTrigger>
              <TabsTrigger value="activity" className="flex items-center gap-2">
                <ClipboardList className="h-4 w-4" />
                <span>活动日志</span>
              </TabsTrigger>
            </TabsList>

            <TabsContent value="profile" className="space-y-4">
              <ProfileForm userInfo={userInfo} />
            </TabsContent>

            <TabsContent value="security" className="space-y-4">
              <SecuritySettings />
            </TabsContent>

            <TabsContent value="notifications" className="space-y-4">
              <NotificationPreferences />
            </TabsContent>

            <TabsContent value="activity" className="space-y-4">
              <ActivityLog />
            </TabsContent>
          </Tabs>
        )}
      </div>
    </MainLayout>
  )
}
