import { <PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON>, TabsTrigger } from "@/components/ui/tabs"
import { Card, CardContent, CardHeader, CardTitle } from "@/components/ui/card"
import { Button } from "@/components/ui/button"
import { Plus, RefreshCw, Filter, Download, Clock, Settings, ListChecks } from "lucide-react"
import { TaskCenterNav } from "@/components/task-center/task-center-nav"

export default function TaskCenterPage() {
  return (
    <div className="flex flex-col">
      <TaskCenterNav />

      <div className="flex flex-col space-y-6 p-6">
        <div className="flex items-center justify-between">
          <div>
            <h1 className="text-2xl font-bold tracking-tight">任务中心</h1>
            <p className="text-muted-foreground">管理和监控系统中的所有任务</p>
          </div>
          <div className="flex items-center gap-2">
            <Button variant="outline" size="sm">
              <RefreshCw className="mr-2 h-4 w-4" />
              刷新
            </Button>
            <Button variant="outline" size="sm">
              <Filter className="mr-2 h-4 w-4" />
              筛选
            </Button>
            <Button variant="outline" size="sm">
              <Download className="mr-2 h-4 w-4" />
              导出
            </Button>
            <Button size="sm">
              <Plus className="mr-2 h-4 w-4" />
              新建任务
            </Button>
          </div>
        </div>

        <Tabs defaultValue="all" className="w-full">
          <TabsList>
            <TabsTrigger value="all">全部任务</TabsTrigger>
            <TabsTrigger value="running">运行中</TabsTrigger>
            <TabsTrigger value="scheduled">计划中</TabsTrigger>
            <TabsTrigger value="completed">已完成</TabsTrigger>
            <TabsTrigger value="failed">失败</TabsTrigger>
          </TabsList>
          <TabsContent value="all" className="space-y-4">
            <TaskSummary />
            <TaskList />
          </TabsContent>
          <TabsContent value="running" className="space-y-4">
            <TaskSummary status="running" />
            <TaskList status="running" />
          </TabsContent>
          <TabsContent value="scheduled" className="space-y-4">
            <TaskSummary status="scheduled" />
            <TaskList status="scheduled" />
          </TabsContent>
          <TabsContent value="completed" className="space-y-4">
            <TaskSummary status="completed" />
            <TaskList status="completed" />
          </TabsContent>
          <TabsContent value="failed" className="space-y-4">
            <TaskSummary status="failed" />
            <TaskList status="failed" />
          </TabsContent>
        </Tabs>
      </div>
    </div>
  )
}

function TaskSummary({ status = "all" }: { status?: string }) {
  return (
    <div className="grid gap-4 md:grid-cols-2 lg:grid-cols-4">
      <Card>
        <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
          <CardTitle className="text-sm font-medium">总任务数</CardTitle>
          <ListChecks className="h-4 w-4 text-muted-foreground" />
        </CardHeader>
        <CardContent>
          <div className="text-2xl font-bold">128</div>
          <p className="text-xs text-muted-foreground">
            较上周 <span className="text-green-500">+8.2%</span>
          </p>
        </CardContent>
      </Card>
      <Card>
        <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
          <CardTitle className="text-sm font-medium">运行中任务</CardTitle>
          <RefreshCw className="h-4 w-4 text-muted-foreground" />
        </CardHeader>
        <CardContent>
          <div className="text-2xl font-bold">24</div>
          <p className="text-xs text-muted-foreground">
            较上周 <span className="text-green-500">+12.5%</span>
          </p>
        </CardContent>
      </Card>
      <Card>
        <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
          <CardTitle className="text-sm font-medium">计划中任务</CardTitle>
          <Clock className="h-4 w-4 text-muted-foreground" />
        </CardHeader>
        <CardContent>
          <div className="text-2xl font-bold">42</div>
          <p className="text-xs text-muted-foreground">
            较上周 <span className="text-green-500">+5.3%</span>
          </p>
        </CardContent>
      </Card>
      <Card>
        <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
          <CardTitle className="text-sm font-medium">失败任务</CardTitle>
          <svg
            xmlns="http://www.w3.org/2000/svg"
            viewBox="0 0 24 24"
            fill="none"
            stroke="currentColor"
            strokeLinecap="round"
            strokeLinejoin="round"
            strokeWidth="2"
            className="h-4 w-4 text-muted-foreground"
          >
            <path d="M12 2v20M17 5H9.5a3.5 3.5 0 0 0 0 7h5a3.5 3.5 0 0 1 0 7H6" />
          </svg>
        </CardHeader>
        <CardContent>
          <div className="text-2xl font-bold">3</div>
          <p className="text-xs text-muted-foreground">
            较上周 <span className="text-red-500">+1.5%</span>
          </p>
        </CardContent>
      </Card>
    </div>
  )
}

function TaskList({ status = "all" }: { status?: string }) {
  // 模拟任务数据
  const tasks = [
    {
      id: "TASK-1001",
      name: "数据同步任务",
      type: "定时任务",
      status: "运行中",
      progress: 45,
      nextRun: "2023-05-15 08:00:00",
      lastRun: "2023-05-14 08:00:00",
      createdBy: "系统管理员",
    },
    {
      id: "TASK-1002",
      name: "数据备份任务",
      type: "定时任务",
      status: "计划中",
      progress: 0,
      nextRun: "2023-05-15 23:00:00",
      lastRun: "2023-05-14 23:00:00",
      createdBy: "系统管理员",
    },
    {
      id: "TASK-1003",
      name: "系统状态检查",
      type: "定时任务",
      status: "已完成",
      progress: 100,
      nextRun: "2023-05-16 00:00:00",
      lastRun: "2023-05-15 00:00:00",
      createdBy: "系统管理员",
    },
    {
      id: "TASK-1004",
      name: "日志清理任务",
      type: "定时任务",
      status: "失败",
      progress: 23,
      nextRun: "2023-05-15 02:00:00",
      lastRun: "2023-05-14 02:00:00",
      createdBy: "系统管理员",
    },
    {
      id: "TASK-1005",
      name: "设备数据采集",
      type: "手动任务",
      status: "运行中",
      progress: 78,
      nextRun: "-",
      lastRun: "2023-05-15 10:30:00",
      createdBy: "操作员",
    },
  ]

  // 根据状态筛选任务
  const filteredTasks =
    status === "all"
      ? tasks
      : tasks.filter((task) => {
          if (status === "running") return task.status === "运行中"
          if (status === "scheduled") return task.status === "计划中"
          if (status === "completed") return task.status === "已完成"
          if (status === "failed") return task.status === "失败"
          return true
        })

  return (
    <div className="rounded-md border">
      <div className="relative w-full overflow-auto">
        <table className="w-full caption-bottom text-sm">
          <thead className="[&_tr]:border-b">
            <tr className="border-b transition-colors hover:bg-muted/50 data-[state=selected]:bg-muted">
              <th className="h-12 px-4 text-left align-middle font-medium text-muted-foreground">任务ID</th>
              <th className="h-12 px-4 text-left align-middle font-medium text-muted-foreground">任务名称</th>
              <th className="h-12 px-4 text-left align-middle font-medium text-muted-foreground">任务类型</th>
              <th className="h-12 px-4 text-left align-middle font-medium text-muted-foreground">状态</th>
              <th className="h-12 px-4 text-left align-middle font-medium text-muted-foreground">进度</th>
              <th className="h-12 px-4 text-left align-middle font-medium text-muted-foreground">下次执行</th>
              <th className="h-12 px-4 text-left align-middle font-medium text-muted-foreground">上次执行</th>
              <th className="h-12 px-4 text-left align-middle font-medium text-muted-foreground">创建人</th>
              <th className="h-12 px-4 text-left align-middle font-medium text-muted-foreground">操作</th>
            </tr>
          </thead>
          <tbody className="[&_tr:last-child]:border-0">
            {filteredTasks.map((task) => (
              <tr key={task.id} className="border-b transition-colors hover:bg-muted/50 data-[state=selected]:bg-muted">
                <td className="p-4 align-middle">{task.id}</td>
                <td className="p-4 align-middle font-medium">{task.name}</td>
                <td className="p-4 align-middle">{task.type}</td>
                <td className="p-4 align-middle">
                  <span
                    className={`inline-flex items-center rounded-full px-2.5 py-0.5 text-xs font-medium ${
                      task.status === "运行中"
                        ? "bg-blue-100 text-blue-800 dark:bg-blue-900 dark:text-blue-300"
                        : task.status === "计划中"
                          ? "bg-yellow-100 text-yellow-800 dark:bg-yellow-900 dark:text-yellow-300"
                          : task.status === "已完成"
                            ? "bg-green-100 text-green-800 dark:bg-green-900 dark:text-green-300"
                            : "bg-red-100 text-red-800 dark:bg-red-900 dark:text-red-300"
                    }`}
                  >
                    {task.status}
                  </span>
                </td>
                <td className="p-4 align-middle">
                  <div className="w-full bg-gray-200 rounded-full h-2.5 dark:bg-gray-700">
                    <div
                      className={`h-2.5 rounded-full ${
                        task.status === "失败"
                          ? "bg-red-600"
                          : task.status === "已完成"
                            ? "bg-green-600"
                            : "bg-blue-600"
                      }`}
                      style={{ width: `${task.progress}%` }}
                    ></div>
                  </div>
                  <span className="text-xs text-muted-foreground">{task.progress}%</span>
                </td>
                <td className="p-4 align-middle">{task.nextRun}</td>
                <td className="p-4 align-middle">{task.lastRun}</td>
                <td className="p-4 align-middle">{task.createdBy}</td>
                <td className="p-4 align-middle">
                  <div className="flex items-center gap-2">
                    <Button variant="ghost" size="icon" className="h-8 w-8">
                      <RefreshCw className="h-4 w-4" />
                      <span className="sr-only">重新运行</span>
                    </Button>
                    <Button variant="ghost" size="icon" className="h-8 w-8">
                      <Settings className="h-4 w-4" />
                      <span className="sr-only">设置</span>
                    </Button>
                  </div>
                </td>
              </tr>
            ))}
          </tbody>
        </table>
      </div>
    </div>
  )
}
