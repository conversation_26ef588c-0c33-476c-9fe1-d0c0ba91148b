import { TaskCenterNav } from "@/components/task-center/task-center-nav"
import { But<PERSON> } from "@/components/ui/button"
import { Card, CardContent, CardHeader, CardTitle } from "@/components/ui/card"
import { Ta<PERSON>, TabsContent, TabsList, TabsTrigger } from "@/components/ui/tabs"
import { Calendar, CheckCircle, Download, Filter, History, RefreshCw, XCircle } from "lucide-react"

export default function TaskHistoryPage() {
  return (
    <div className="flex flex-col">
      <TaskCenterNav />

      <div className="flex flex-col space-y-6 p-6">
        <div className="flex items-center justify-between">
          <div>
            <h1 className="text-2xl font-bold tracking-tight">执行历史</h1>
            <p className="text-muted-foreground">查看任务的历史执行记录和结果</p>
          </div>
          <div className="flex items-center gap-2">
            <Button variant="outline" size="sm">
              <RefreshCw className="mr-2 h-4 w-4" />
              刷新
            </Button>
            <Button variant="outline" size="sm">
              <Filter className="mr-2 h-4 w-4" />
              筛选
            </Button>
            <Button variant="outline" size="sm">
              <Download className="mr-2 h-4 w-4" />
              导出
            </Button>
          </div>
        </div>

        <Tabs defaultValue="all" className="w-full">
          <TabsList>
            <TabsTrigger value="all">全部记录</TabsTrigger>
            <TabsTrigger value="success">成功</TabsTrigger>
            <TabsTrigger value="failed">失败</TabsTrigger>
            <TabsTrigger value="today">今日</TabsTrigger>
            <TabsTrigger value="week">本周</TabsTrigger>
          </TabsList>
          <TabsContent value="all" className="space-y-4">
            <HistorySummary />
            <HistoryList />
          </TabsContent>
          <TabsContent value="success" className="space-y-4">
            <HistorySummary status="success" />
            <HistoryList status="success" />
          </TabsContent>
          <TabsContent value="failed" className="space-y-4">
            <HistorySummary status="failed" />
            <HistoryList status="failed" />
          </TabsContent>
          <TabsContent value="today" className="space-y-4">
            <HistorySummary period="today" />
            <HistoryList period="today" />
          </TabsContent>
          <TabsContent value="week" className="space-y-4">
            <HistorySummary period="week" />
            <HistoryList period="week" />
          </TabsContent>
        </Tabs>
      </div>
    </div>
  )
}

function HistorySummary({ status, period }: { status?: string; period?: string }) {
  return (
    <div className="grid gap-4 md:grid-cols-2 lg:grid-cols-4">
      <Card>
        <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
          <CardTitle className="text-sm font-medium">总执行次数</CardTitle>
          <History className="h-4 w-4 text-muted-foreground" />
        </CardHeader>
        <CardContent>
          <div className="text-2xl font-bold">1,248</div>
          <p className="text-xs text-muted-foreground">
            较上周 <span className="text-green-500">+12.5%</span>
          </p>
        </CardContent>
      </Card>
      <Card>
        <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
          <CardTitle className="text-sm font-medium">成功次数</CardTitle>
          <CheckCircle className="h-4 w-4 text-muted-foreground" />
        </CardHeader>
        <CardContent>
          <div className="text-2xl font-bold">1,186</div>
          <p className="text-xs text-muted-foreground">
            成功率 <span className="text-green-500">95.03%</span>
          </p>
        </CardContent>
      </Card>
      <Card>
        <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
          <CardTitle className="text-sm font-medium">失败次数</CardTitle>
          <XCircle className="h-4 w-4 text-muted-foreground" />
        </CardHeader>
        <CardContent>
          <div className="text-2xl font-bold">62</div>
          <p className="text-xs text-muted-foreground">
            失败率 <span className="text-red-500">4.97%</span>
          </p>
        </CardContent>
      </Card>
      <Card>
        <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
          <CardTitle className="text-sm font-medium">平均执行时间</CardTitle>
          <Calendar className="h-4 w-4 text-muted-foreground" />
        </CardHeader>
        <CardContent>
          <div className="text-2xl font-bold">2.4s</div>
          <p className="text-xs text-muted-foreground">
            较上周 <span className="text-green-500">-0.3s</span>
          </p>
        </CardContent>
      </Card>
    </div>
  )
}

function HistoryList({ status, period }: { status?: string; period?: string }) {
  // 模拟历史数据
  const histories = [
    {
      id: "EXEC-10001",
      taskId: "TASK-1001",
      taskName: "数据同步任务",
      startTime: "2023-05-15 08:00:00",
      endTime: "2023-05-15 08:01:23",
      duration: "1分23秒",
      status: "成功",
      result: "同步完成: 1,234条记录",
      triggeredBy: "计划",
    },
    {
      id: "EXEC-10002",
      taskId: "TASK-1002",
      taskName: "数据备份任务",
      startTime: "2023-05-14 23:00:00",
      endTime: "2023-05-14 23:05:46",
      duration: "5分46秒",
      status: "成功",
      result: "备份完成: 2.3GB",
      triggeredBy: "计划",
    },
    {
      id: "EXEC-10003",
      taskId: "TASK-1003",
      taskName: "系统状态检查",
      startTime: "2023-05-15 00:00:00",
      endTime: "2023-05-15 00:00:12",
      duration: "12秒",
      status: "成功",
      result: "所有系统正常",
      triggeredBy: "计划",
    },
    {
      id: "EXEC-10004",
      taskId: "TASK-1004",
      taskName: "日志清理任务",
      startTime: "2023-05-14 02:00:00",
      endTime: "2023-05-14 02:00:23",
      duration: "23秒",
      status: "失败",
      result: "权限不足: 无法访问日志目录",
      triggeredBy: "计划",
    },
    {
      id: "EXEC-10005",
      taskId: "TASK-1005",
      taskName: "设备数据采集",
      startTime: "2023-05-15 10:30:00",
      endTime: "2023-05-15 10:32:15",
      duration: "2分15秒",
      status: "成功",
      result: "采集完成: 5,678条记录",
      triggeredBy: "手动",
    },
  ]

  // 根据状态和时间筛选历史记录
  let filteredHistories = histories
  if (status === "success") {
    filteredHistories = histories.filter((history) => history.status === "成功")
  } else if (status === "failed") {
    filteredHistories = histories.filter((history) => history.status === "失败")
  }

  // 这里可以添加按时间段筛选的逻辑

  return (
    <div className="rounded-md border">
      <div className="relative w-full overflow-auto">
        <table className="w-full caption-bottom text-sm">
          <thead className="[&_tr]:border-b">
            <tr className="border-b transition-colors hover:bg-muted/50 data-[state=selected]:bg-muted">
              <th className="h-12 px-4 text-left align-middle font-medium text-muted-foreground">执行ID</th>
              <th className="h-12 px-4 text-left align-middle font-medium text-muted-foreground">任务ID</th>
              <th className="h-12 px-4 text-left align-middle font-medium text-muted-foreground">任务名称</th>
              <th className="h-12 px-4 text-left align-middle font-medium text-muted-foreground">开始时间</th>
              <th className="h-12 px-4 text-left align-middle font-medium text-muted-foreground">结束时间</th>
              <th className="h-12 px-4 text-left align-middle font-medium text-muted-foreground">执行时长</th>
              <th className="h-12 px-4 text-left align-middle font-medium text-muted-foreground">状态</th>
              <th className="h-12 px-4 text-left align-middle font-medium text-muted-foreground">结果</th>
              <th className="h-12 px-4 text-left align-middle font-medium text-muted-foreground">触发方式</th>
            </tr>
          </thead>
          <tbody className="[&_tr:last-child]:border-0">
            {filteredHistories.map((history) => (
              <tr
                key={history.id}
                className="border-b transition-colors hover:bg-muted/50 data-[state=selected]:bg-muted"
              >
                <td className="p-4 align-middle">{history.id}</td>
                <td className="p-4 align-middle">{history.taskId}</td>
                <td className="p-4 align-middle font-medium">{history.taskName}</td>
                <td className="p-4 align-middle">{history.startTime}</td>
                <td className="p-4 align-middle">{history.endTime}</td>
                <td className="p-4 align-middle">{history.duration}</td>
                <td className="p-4 align-middle">
                  <span
                    className={`inline-flex items-center rounded-full px-2.5 py-0.5 text-xs font-medium ${
                      history.status === "成功"
                        ? "bg-green-100 text-green-800 dark:bg-green-900 dark:text-green-300"
                        : "bg-red-100 text-red-800 dark:bg-red-900 dark:text-red-300"
                    }`}
                  >
                    {history.status}
                  </span>
                </td>
                <td className="p-4 align-middle">{history.result}</td>
                <td className="p-4 align-middle">{history.triggeredBy}</td>
              </tr>
            ))}
          </tbody>
        </table>
      </div>
    </div>
  )
}
