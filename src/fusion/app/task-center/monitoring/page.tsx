import { TaskCenterNav } from "@/components/task-center/task-center-nav"
import { But<PERSON> } from "@/components/ui/button"
import { Card, CardContent, CardHeader, CardTitle } from "@/components/ui/card"
import { <PERSON><PERSON>, <PERSON>bsContent, TabsList, TabsTrigger } from "@/components/ui/tabs"
import { Activity, AlertTriangle, CheckCircle, Clock, CpuIcon, Download, Filter, RefreshCw } from "lucide-react"

export default function TaskMonitoringPage() {
  return (
    <div className="flex flex-col">
      <TaskCenterNav />

      <div className="flex flex-col space-y-6 p-6">
        <div className="flex items-center justify-between">
          <div>
            <h1 className="text-2xl font-bold tracking-tight">任务监控</h1>
            <p className="text-muted-foreground">实时监控任务执行状态和系统资源</p>
          </div>
          <div className="flex items-center gap-2">
            <Button variant="outline" size="sm">
              <RefreshCw className="mr-2 h-4 w-4" />
              刷新
            </Button>
            <Button variant="outline" size="sm">
              <Filter className="mr-2 h-4 w-4" />
              筛选
            </Button>
            <Button variant="outline" size="sm">
              <Download className="mr-2 h-4 w-4" />
              导出
            </Button>
          </div>
        </div>

        <Tabs defaultValue="overview" className="w-full">
          <TabsList>
            <TabsTrigger value="overview">概览</TabsTrigger>
            <TabsTrigger value="active">活动任务</TabsTrigger>
            <TabsTrigger value="resources">资源使用</TabsTrigger>
            <TabsTrigger value="alerts">告警</TabsTrigger>
          </TabsList>
          <TabsContent value="overview" className="space-y-4">
            <MonitoringSummary />
            <div className="grid gap-4 md:grid-cols-2">
              <Card>
                <CardHeader>
                  <CardTitle>活动任务</CardTitle>
                </CardHeader>
                <CardContent>
                  <p className="text-sm text-muted-foreground">实时任务执行状态图表将显示在这里</p>
                </CardContent>
              </Card>
              <Card>
                <CardHeader>
                  <CardTitle>系统资源</CardTitle>
                </CardHeader>
                <CardContent>
                  <p className="text-sm text-muted-foreground">系统资源使用图表将显示在这里</p>
                </CardContent>
              </Card>
            </div>
          </TabsContent>
          <TabsContent value="active" className="space-y-4">
            <MonitoringSummary type="active" />
            <ActiveTasksList />
          </TabsContent>
          <TabsContent value="resources" className="space-y-4">
            <MonitoringSummary type="resources" />
            <div className="grid gap-4 md:grid-cols-2">
              <Card>
                <CardHeader>
                  <CardTitle>CPU 使用率</CardTitle>
                </CardHeader>
                <CardContent>
                  <p className="text-sm text-muted-foreground">CPU使用率图表将显示在这里</p>
                </CardContent>
              </Card>
              <Card>
                <CardHeader>
                  <CardTitle>内存使用率</CardTitle>
                </CardHeader>
                <CardContent>
                  <p className="text-sm text-muted-foreground">内存使用率图表将显示在这里</p>
                </CardContent>
              </Card>
              <Card>
                <CardHeader>
                  <CardTitle>磁盘I/O</CardTitle>
                </CardHeader>
                <CardContent>
                  <p className="text-sm text-muted-foreground">磁盘I/O图表将显示在这里</p>
                </CardContent>
              </Card>
              <Card>
                <CardHeader>
                  <CardTitle>网络I/O</CardTitle>
                </CardHeader>
                <CardContent>
                  <p className="text-sm text-muted-foreground">网络I/O图表将显示在这里</p>
                </CardContent>
              </Card>
            </div>
          </TabsContent>
          <TabsContent value="alerts" className="space-y-4">
            <MonitoringSummary type="alerts" />
            <AlertsList />
          </TabsContent>
        </Tabs>
      </div>
    </div>
  )
}

function MonitoringSummary({ type = "overview" }: { type?: string }) {
  return (
    <div className="grid gap-4 md:grid-cols-2 lg:grid-cols-4">
      <Card>
        <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
          <CardTitle className="text-sm font-medium">活动任务</CardTitle>
          <Activity className="h-4 w-4 text-muted-foreground" />
        </CardHeader>
        <CardContent>
          <div className="text-2xl font-bold">24</div>
          <p className="text-xs text-muted-foreground">
            较平均值 <span className="text-green-500">+8.2%</span>
          </p>
        </CardContent>
      </Card>
      <Card>
        <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
          <CardTitle className="text-sm font-medium">CPU使用率</CardTitle>
          <CpuIcon className="h-4 w-4 text-muted-foreground" />
        </CardHeader>
        <CardContent>
          <div className="text-2xl font-bold">42%</div>
          <p className="text-xs text-muted-foreground">
            较平均值 <span className="text-green-500">-5.3%</span>
          </p>
        </CardContent>
      </Card>
      <Card>
        <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
          <CardTitle className="text-sm font-medium">平均响应时间</CardTitle>
          <Clock className="h-4 w-4 text-muted-foreground" />
        </CardHeader>
        <CardContent>
          <div className="text-2xl font-bold">1.8s</div>
          <p className="text-xs text-muted-foreground">
            较平均值 <span className="text-green-500">-0.3s</span>
          </p>
        </CardContent>
      </Card>
      <Card>
        <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
          <CardTitle className="text-sm font-medium">活动告警</CardTitle>
          <AlertTriangle className="h-4 w-4 text-muted-foreground" />
        </CardHeader>
        <CardContent>
          <div className="text-2xl font-bold">2</div>
          <p className="text-xs text-muted-foreground">
            较昨日 <span className="text-red-500">+1</span>
          </p>
        </CardContent>
      </Card>
    </div>
  )
}

function ActiveTasksList() {
  // 模拟活动任务数据
  const activeTasks = [
    {
      id: "TASK-1001",
      name: "数据同步任务",
      startTime: "2023-05-15 08:00:00",
      runningTime: "10分钟",
      progress: 45,
      cpu: "12%",
      memory: "256MB",
      status: "正常",
    },
    {
      id: "TASK-1005",
      name: "设备数据采集",
      startTime: "2023-05-15 10:30:00",
      runningTime: "5分钟",
      progress: 78,
      cpu: "8%",
      memory: "128MB",
      status: "正常",
    },
    {
      id: "TASK-1008",
      name: "系统监控任务",
      startTime: "2023-05-15 09:15:00",
      runningTime: "1小时25分钟",
      progress: 92,
      cpu: "5%",
      memory: "96MB",
      status: "正常",
    },
    {
      id: "TASK-1012",
      name: "数据库优化",
      startTime: "2023-05-15 10:45:00",
      runningTime: "3分钟",
      progress: 15,
      cpu: "25%",
      memory: "512MB",
      status: "高负载",
    },
    {
      id: "TASK-1015",
      name: "文件索引更新",
      startTime: "2023-05-15 10:40:00",
      runningTime: "8分钟",
      progress: 60,
      cpu: "10%",
      memory: "320MB",
      status: "正常",
    },
  ]

  return (
    <div className="rounded-md border">
      <div className="relative w-full overflow-auto">
        <table className="w-full caption-bottom text-sm">
          <thead className="[&_tr]:border-b">
            <tr className="border-b transition-colors hover:bg-muted/50 data-[state=selected]:bg-muted">
              <th className="h-12 px-4 text-left align-middle font-medium text-muted-foreground">任务ID</th>
              <th className="h-12 px-4 text-left align-middle font-medium text-muted-foreground">任务名称</th>
              <th className="h-12 px-4 text-left align-middle font-medium text-muted-foreground">开始时间</th>
              <th className="h-12 px-4 text-left align-middle font-medium text-muted-foreground">运行时长</th>
              <th className="h-12 px-4 text-left align-middle font-medium text-muted-foreground">进度</th>
              <th className="h-12 px-4 text-left align-middle font-medium text-muted-foreground">CPU使用</th>
              <th className="h-12 px-4 text-left align-middle font-medium text-muted-foreground">内存使用</th>
              <th className="h-12 px-4 text-left align-middle font-medium text-muted-foreground">状态</th>
              <th className="h-12 px-4 text-left align-middle font-medium text-muted-foreground">操作</th>
            </tr>
          </thead>
          <tbody className="[&_tr:last-child]:border-0">
            {activeTasks.map((task) => (
              <tr key={task.id} className="border-b transition-colors hover:bg-muted/50 data-[state=selected]:bg-muted">
                <td className="p-4 align-middle">{task.id}</td>
                <td className="p-4 align-middle font-medium">{task.name}</td>
                <td className="p-4 align-middle">{task.startTime}</td>
                <td className="p-4 align-middle">{task.runningTime}</td>
                <td className="p-4 align-middle">
                  <div className="w-full bg-gray-200 rounded-full h-2.5 dark:bg-gray-700">
                    <div
                      className={`h-2.5 rounded-full ${task.status === "高负载" ? "bg-yellow-600" : "bg-blue-600"}`}
                      style={{ width: `${task.progress}%` }}
                    ></div>
                  </div>
                  <span className="text-xs text-muted-foreground">{task.progress}%</span>
                </td>
                <td className="p-4 align-middle">{task.cpu}</td>
                <td className="p-4 align-middle">{task.memory}</td>
                <td className="p-4 align-middle">
                  <span
                    className={`inline-flex items-center rounded-full px-2.5 py-0.5 text-xs font-medium ${
                      task.status === "正常"
                        ? "bg-green-100 text-green-800 dark:bg-green-900 dark:text-green-300"
                        : task.status === "高负载"
                          ? "bg-yellow-100 text-yellow-800 dark:bg-yellow-900 dark:text-yellow-300"
                          : "bg-red-100 text-red-800 dark:bg-red-900 dark:text-red-300"
                    }`}
                  >
                    {task.status}
                  </span>
                </td>
                <td className="p-4 align-middle">
                  <Button variant="ghost" size="icon" className="h-8 w-8">
                    <RefreshCw className="h-4 w-4" />
                    <span className="sr-only">刷新</span>
                  </Button>
                </td>
              </tr>
            ))}
          </tbody>
        </table>
      </div>
    </div>
  )
}

function AlertsList() {
  // 模拟告警数据
  const alerts = [
    {
      id: "ALERT-1001",
      taskId: "TASK-1012",
      taskName: "数据库优化",
      time: "2023-05-15 10:48:23",
      level: "警告",
      message: "CPU使用率超过20%",
      status: "活动",
    },
    {
      id: "ALERT-1002",
      taskId: "TASK-1012",
      taskName: "数据库优化",
      time: "2023-05-15 10:50:12",
      level: "警告",
      message: "内存使用率超过500MB",
      status: "活动",
    },
    {
      id: "ALERT-1003",
      taskId: "TASK-1004",
      taskName: "日志清理任务",
      time: "2023-05-14 02:00:23",
      level: "错误",
      message: "任务执行失败: 权限不足",
      status: "已解决",
    },
    {
      id: "ALERT-1004",
      taskId: "TASK-1009",
      taskName: "报表生成任务",
      time: "2023-05-14 15:30:45",
      level: "警告",
      message: "任务执行时间超过预期",
      status: "已解决",
    },
    {
      id: "ALERT-1005",
      taskId: "TASK-1007",
      taskName: "系统备份任务",
      time: "2023-05-13 23:15:30",
      level: "信息",
      message: "备份空间不足, 已自动清理旧备份",
      status: "已解决",
    },
  ]

  return (
    <div className="rounded-md border">
      <div className="relative w-full overflow-auto">
        <table className="w-full caption-bottom text-sm">
          <thead className="[&_tr]:border-b">
            <tr className="border-b transition-colors hover:bg-muted/50 data-[state=selected]:bg-muted">
              <th className="h-12 px-4 text-left align-middle font-medium text-muted-foreground">告警ID</th>
              <th className="h-12 px-4 text-left align-middle font-medium text-muted-foreground">任务ID</th>
              <th className="h-12 px-4 text-left align-middle font-medium text-muted-foreground">任务名称</th>
              <th className="h-12 px-4 text-left align-middle font-medium text-muted-foreground">时间</th>
              <th className="h-12 px-4 text-left align-middle font-medium text-muted-foreground">级别</th>
              <th className="h-12 px-4 text-left align-middle font-medium text-muted-foreground">消息</th>
              <th className="h-12 px-4 text-left align-middle font-medium text-muted-foreground">状态</th>
              <th className="h-12 px-4 text-left align-middle font-medium text-muted-foreground">操作</th>
            </tr>
          </thead>
          <tbody className="[&_tr:last-child]:border-0">
            {alerts.map((alert) => (
              <tr
                key={alert.id}
                className="border-b transition-colors hover:bg-muted/50 data-[state=selected]:bg-muted"
              >
                <td className="p-4 align-middle">{alert.id}</td>
                <td className="p-4 align-middle">{alert.taskId}</td>
                <td className="p-4 align-middle font-medium">{alert.taskName}</td>
                <td className="p-4 align-middle">{alert.time}</td>
                <td className="p-4 align-middle">
                  <span
                    className={`inline-flex items-center rounded-full px-2.5 py-0.5 text-xs font-medium ${
                      alert.level === "错误"
                        ? "bg-red-100 text-red-800 dark:bg-red-900 dark:text-red-300"
                        : alert.level === "警告"
                          ? "bg-yellow-100 text-yellow-800 dark:bg-yellow-900 dark:text-yellow-300"
                          : "bg-blue-100 text-blue-800 dark:bg-blue-900 dark:text-blue-300"
                    }`}
                  >
                    {alert.level}
                  </span>
                </td>
                <td className="p-4 align-middle">{alert.message}</td>
                <td className="p-4 align-middle">
                  <span
                    className={`inline-flex items-center rounded-full px-2.5 py-0.5 text-xs font-medium ${
                      alert.status === "活动"
                        ? "bg-red-100 text-red-800 dark:bg-red-900 dark:text-red-300"
                        : "bg-green-100 text-green-800 dark:bg-green-900 dark:text-green-300"
                    }`}
                  >
                    {alert.status}
                  </span>
                </td>
                <td className="p-4 align-middle">
                  <Button variant="ghost" size="icon" className="h-8 w-8">
                    <CheckCircle className="h-4 w-4" />
                    <span className="sr-only">标记为已解决</span>
                  </Button>
                </td>
              </tr>
            ))}
          </tbody>
        </table>
      </div>
    </div>
  )
}
