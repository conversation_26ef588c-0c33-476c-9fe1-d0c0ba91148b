import { useState } from 'react'
import { MainLayout } from '@/components/layout/main-layout'
import { HistoryQueryForm } from '@/components/data-history/history-query-form'
import { HistoryDataTable } from '@/components/data-history/history-data-table'
import {
  Card,
  CardContent,
  CardDescription,
  CardHeader,
  CardTitle,
} from '@/components/ui/card'
import { Ta<PERSON>, TabsContent, TabsList, TabsTrigger } from '@/components/ui/tabs'
import { HistoryDataChart } from '@/components/data-history/history-data-chart'
import type { HistoryQueryCriteria } from '@/components/data-history/types'
import { Button } from '@/components/ui/button'
import {
  Collapsible,
  CollapsibleContent,
  CollapsibleTrigger,
} from '@/components/ui/collapsible'
import {
  ChevronDown,
  ChevronUp,
  Download,
  FileSpreadsheet,
  FileJson,
  FileSpreadsheetIcon as FileCsv,
} from 'lucide-react'
import {
  DropdownMenu,
  DropdownMenuContent,
  DropdownMenuItem,
  DropdownMenuTrigger,
} from '@/components/ui/dropdown-menu'

export default function DataHistoryPage() {
  const [queryResults, setQueryResults] = useState<any[]>([])
  const [selectedColumns, setSelectedColumns] = useState<string[]>([])
  const [isLoading, setIsLoading] = useState(false)
  const [queryCriteria, setQueryCriteria] =
    useState<HistoryQueryCriteria | null>(null)
  const [isResultsCollapsed, setIsResultsCollapsed] = useState(false)

  // 处理查询提交
  const handleQuerySubmit = async (criteria: HistoryQueryCriteria) => {
    setIsLoading(true)
    setQueryCriteria(criteria)
    setIsResultsCollapsed(false) // 展开结果区域

    try {
      // 模拟API调用延迟
      await new Promise((resolve) => setTimeout(resolve, 1000))

      // 生成模拟数据
      const results = generateMockHistoryData(criteria)
      setQueryResults(results)

      // 设置选中的列
      const columns = ['timestamp', ...criteria.tagIds.map((id) => id)]
      setSelectedColumns(columns)
    } catch (error) {
      console.error('查询历史数据失败:', error)
    } finally {
      setIsLoading(false)
    }
  }

  // 生成模拟历史数据
  const generateMockHistoryData = (criteria: HistoryQueryCriteria) => {
    const results: any[] = []
    const { startTime, endTime, tagIds, interval } = criteria

    const start = new Date(startTime).getTime()
    const end = new Date(endTime).getTime()
    const intervalMs = parseIntervalToMs(interval || '1h')

    // 计算数据点数量
    const pointCount = Math.min(500, Math.floor((end - start) / intervalMs))

    // 生成每个时间点的数据
    for (let i = 0; i < pointCount; i++) {
      const timestamp = new Date(start + (i * (end - start)) / pointCount)

      const dataPoint: any = {
        timestamp: timestamp.toISOString(),
      }

      // 为每个选中的点位生成随机值
      tagIds.forEach((tagId) => {
        // 根据tagId生成不同范围的随机值
        const baseValue = Number.parseInt(tagId.replace(/\D/g, '')) || 50
        const randomFactor = Math.random() * 0.4 + 0.8 // 0.8 到 1.2 之间的随机因子

        dataPoint[tagId] = Number((baseValue * randomFactor).toFixed(2))

        // 随机添加质量码
        const qualityRandom = Math.random()
        if (qualityRandom > 0.95) {
          dataPoint[`${tagId}_quality`] = 'Bad'
        } else if (qualityRandom > 0.9) {
          dataPoint[`${tagId}_quality`] = 'Uncertain'
        } else {
          dataPoint[`${tagId}_quality`] = 'Good'
        }
      })

      results.push(dataPoint)
    }

    return results
  }

  // 将时间间隔字符串解析为毫秒数
  const parseIntervalToMs = (intervalStr: string): number => {
    switch (intervalStr) {
      case '1m':
        return 60 * 1000
      case '5m':
        return 5 * 60 * 1000
      case '15m':
        return 15 * 60 * 1000
      case '30m':
        return 30 * 60 * 1000
      case '1h':
        return 60 * 60 * 1000
      case '6h':
        return 6 * 60 * 60 * 1000
      case '12h':
        return 12 * 60 * 60 * 1000
      case '1d':
        return 24 * 60 * 60 * 1000
      default:
        return 60 * 60 * 1000
    }
  }

  // 导出为Excel
  const exportToExcel = () => {
    if (!queryResults.length) return

    // 创建CSV内容，添加BOM以便Excel正确识别中文
    let csvContent = '\uFEFF' + selectedColumns.join(',') + '\n'

    queryResults.forEach((row) => {
      const rowValues = selectedColumns.map((column) => {
        const value = row[column]

        // 处理特殊情况
        if (value === undefined || value === null) return ''
        if (column === 'timestamp') {
          const date = new Date(value)
          return `${date.getFullYear()}-${String(date.getMonth() + 1).padStart(
            2,
            '0'
          )}-${String(date.getDate()).padStart(2, '0')} ${String(
            date.getHours()
          ).padStart(2, '0')}:${String(date.getMinutes()).padStart(
            2,
            '0'
          )}:${String(date.getSeconds()).padStart(2, '0')}`
        }
        if (typeof value === 'string' && value.includes(','))
          return `"${value}"`

        return value
      })

      csvContent += rowValues.join(',') + '\n'
    })

    // 创建Blob并下载
    const blob = new Blob([csvContent], {
      type: 'application/vnd.ms-excel;charset=utf-8;',
    })
    const url = URL.createObjectURL(blob)
    const link = document.createElement('a')
    link.setAttribute('href', url)
    link.setAttribute(
      'download',
      `历史数据_${new Date()
        .toISOString()
        .slice(0, 19)
        .replace(/[-:T]/g, '')}.xlsx`
    )
    document.body.appendChild(link)
    link.click()
    document.body.removeChild(link)
  }

  // 导出为CSV
  const exportToCsv = () => {
    if (!queryResults.length) return

    // 创建CSV内容
    let csvContent = selectedColumns.join(',') + '\n'

    queryResults.forEach((row) => {
      const rowValues = selectedColumns.map((column) => {
        const value = row[column]

        // 处理特殊情况
        if (value === undefined || value === null) return ''
        if (column === 'timestamp') {
          const date = new Date(value)
          return `${date.getFullYear()}-${String(date.getMonth() + 1).padStart(
            2,
            '0'
          )}-${String(date.getDate()).padStart(2, '0')} ${String(
            date.getHours()
          ).padStart(2, '0')}:${String(date.getMinutes()).padStart(
            2,
            '0'
          )}:${String(date.getSeconds()).padStart(2, '0')}`
        }
        if (typeof value === 'string' && value.includes(','))
          return `"${value}"`

        return value
      })

      csvContent += rowValues.join(',') + '\n'
    })

    // 创建Blob并下载
    const blob = new Blob([csvContent], { type: 'text/csv;charset=utf-8;' })
    const url = URL.createObjectURL(blob)
    const link = document.createElement('a')
    link.setAttribute('href', url)
    link.setAttribute(
      'download',
      `历史数据_${new Date()
        .toISOString()
        .slice(0, 19)
        .replace(/[-:T]/g, '')}.csv`
    )
    document.body.appendChild(link)
    link.click()
    document.body.removeChild(link)
  }

  // 导出为JSON
  const exportToJson = () => {
    if (!queryResults.length) return

    // 创建JSON内容
    const jsonData = {
      criteria: queryCriteria,
      exportTime: new Date().toISOString(),
      columns: selectedColumns,
      data: queryResults,
    }

    const jsonContent = JSON.stringify(jsonData, null, 2)

    // 创建Blob并下载
    const blob = new Blob([jsonContent], {
      type: 'application/json;charset=utf-8;',
    })
    const url = URL.createObjectURL(blob)
    const link = document.createElement('a')
    link.setAttribute('href', url)
    link.setAttribute(
      'download',
      `历史数据_${new Date()
        .toISOString()
        .slice(0, 19)
        .replace(/[-:T]/g, '')}.json`
    )
    document.body.appendChild(link)
    link.click()
    document.body.removeChild(link)
  }

  return (
    <MainLayout>
      <div className="container mx-auto py-6 space-y-6">
        <div className="flex justify-between items-center">
          <h1 className="text-2xl font-bold">设备历史数据查询</h1>
        </div>

        <Card>
          <CardHeader className="pb-3">
            <CardTitle>查询条件</CardTitle>
            <CardDescription>
              选择设备、点位和时间范围进行历史数据查询
            </CardDescription>
          </CardHeader>
          <CardContent>
            <HistoryQueryForm
              onSubmit={handleQuerySubmit}
              isLoading={isLoading}
            />
          </CardContent>
        </Card>

        {queryResults.length > 0 && (
          <Collapsible
            open={!isResultsCollapsed}
            onOpenChange={(open) => setIsResultsCollapsed(!open)}
            className="space-y-4">
            <div className="flex justify-between items-center">
              <div className="flex items-center">
                <h2 className="text-xl font-semibold">查询结果</h2>
                <span className="ml-2 text-sm text-muted-foreground">
                  {queryResults.length} 条记录
                </span>
              </div>
              <div className="flex items-center space-x-2">
                <DropdownMenu>
                  <DropdownMenuTrigger asChild>
                    <Button variant="outline" className="flex items-center">
                      <Download className="mr-2 h-4 w-4" />
                      导出数据
                    </Button>
                  </DropdownMenuTrigger>
                  <DropdownMenuContent align="end">
                    <DropdownMenuItem onClick={exportToExcel}>
                      <FileSpreadsheet className="mr-2 h-4 w-4" />
                      导出为Excel
                    </DropdownMenuItem>
                    <DropdownMenuItem onClick={exportToCsv}>
                      <FileCsv className="mr-2 h-4 w-4" />
                      导出为CSV
                    </DropdownMenuItem>
                    <DropdownMenuItem onClick={exportToJson}>
                      <FileJson className="mr-2 h-4 w-4" />
                      导出为JSON
                    </DropdownMenuItem>
                  </DropdownMenuContent>
                </DropdownMenu>
                <CollapsibleTrigger asChild>
                  <Button variant="ghost" size="sm">
                    {isResultsCollapsed ? (
                      <div className="flex items-center">
                        <ChevronDown className="h-4 w-4 mr-1" />
                        展开
                      </div>
                    ) : (
                      <div className="flex items-center">
                        <ChevronUp className="h-4 w-4 mr-1" />
                        收起
                      </div>
                    )}
                  </Button>
                </CollapsibleTrigger>
              </div>
            </div>

            <CollapsibleContent>
              <Tabs defaultValue="table">
                <TabsList>
                  <TabsTrigger value="table">表格视图</TabsTrigger>
                  <TabsTrigger value="chart">图表视图</TabsTrigger>
                </TabsList>
                <TabsContent value="table" className="mt-4">
                  <Card>
                    <CardContent className="pt-6">
                      <HistoryDataTable
                        data={queryResults}
                        columns={selectedColumns}
                        isLoading={isLoading}
                      />
                    </CardContent>
                  </Card>
                </TabsContent>
                <TabsContent value="chart" className="mt-4">
                  <Card>
                    <CardContent className="pt-6">
                      <HistoryDataChart
                        data={queryResults}
                        tagIds={queryCriteria?.tagIds || []}
                        isLoading={isLoading}
                      />
                    </CardContent>
                  </Card>
                </TabsContent>
              </Tabs>
            </CollapsibleContent>
          </Collapsible>
        )}
      </div>
    </MainLayout>
  )
}
