/**
 * Logger测试页面
 * 用于演示和测试Logger功能
 */

import { useState } from 'react'
import { DataToolLayout } from '@/components/debug-tools/data-tool-layout'
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from '@/components/ui/card'
import { Button } from '@/components/ui/button'
import { Input } from '@/components/ui/input'
import { Label } from '@/components/ui/label'
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from '@/components/ui/select'
import { Textarea } from '@/components/ui/textarea'
import { Badge } from '@/components/ui/badge'
import { Separator } from '@/components/ui/separator'
import { logger, log, LogLevel } from '@/lib/utils/logger'
import { getLoggerStatus } from '@/lib/config/logger-config'
import { useConfigStore } from '@/lib/config/config-store'
import {
  safeExecute,
  safeExecuteAsync,
  safeExecuteWithResult,
  safeExecuteWithRetry
} from '@/lib/utils/safe-execute'
import { toast } from '@/components/ui/use-toast'
import { 
  Bug, 
  Info, 
  AlertTriangle, 
  XCircle, 
  Download, 
  Trash2, 
  Settings,
  Play,
  RefreshCw
} from 'lucide-react'

export default function LoggerTestPage() {
  const [testMessage, setTestMessage] = useState('这是一条测试日志消息')
  const [testLevel, setTestLevel] = useState<'debug' | 'info' | 'warn' | 'error'>('info')
  const [loggerStatus, setLoggerStatus] = useState(getLoggerStatus())
  const { config } = useConfigStore()

  // 刷新Logger状态
  const refreshStatus = () => {
    setLoggerStatus(getLoggerStatus())
  }

  // 发送测试日志
  const sendTestLog = () => {
    const timestamp = new Date().toISOString()
    const testData = {
      timestamp,
      component: 'LoggerTestPage',
      action: 'test_log',
      level: testLevel
    }

    switch (testLevel) {
      case 'debug':
        log.debug(testMessage, testData)
        break
      case 'info':
        log.info(testMessage, testData)
        break
      case 'warn':
        log.warn(testMessage, testData)
        break
      case 'error':
        log.error(testMessage, testData)
        break
    }

    toast({
      title: '日志已发送',
      description: `${testLevel.toUpperCase()} 级别的日志已发送到Logger`,
    })
  }

  // 批量发送测试日志
  const sendBatchLogs = () => {
    const messages = [
      { level: 'debug', message: '调试信息：组件初始化完成' },
      { level: 'info', message: '信息：用户执行了批量测试操作' },
      { level: 'warn', message: '警告：检测到潜在的性能问题' },
      { level: 'error', message: '错误：模拟的错误情况' },
    ]

    messages.forEach((item, index) => {
      setTimeout(() => {
        const testData = {
          timestamp: new Date().toISOString(),
          component: 'LoggerTestPage',
          action: 'batch_test',
          index: index + 1,
          total: messages.length
        }

        switch (item.level) {
          case 'debug':
            log.debug(item.message, testData)
            break
          case 'info':
            log.info(item.message, testData)
            break
          case 'warn':
            log.warn(item.message, testData)
            break
          case 'error':
            log.error(item.message, testData)
            break
        }
      }, index * 500) // 每500ms发送一条
    })

    toast({
      title: '批量日志已发送',
      description: '4条不同级别的测试日志已发送',
    })
  }

  // 获取历史日志
  const getHistoryLogs = () => {
    const logs = logger.getLogs()
    const logText = logs.map(log => 
      `[${new Date(log.timestamp).toLocaleString()}] [${LogLevel[log.level]}] ${log.message} ${log.args.length > 0 ? JSON.stringify(log.args) : ''}`
    ).join('\n')

    if (logs.length === 0) {
      toast({
        title: '无历史日志',
        description: '当前没有保存的历史日志',
        variant: 'destructive'
      })
      return
    }

    // 创建下载链接
    const blob = new Blob([logText], { type: 'text/plain' })
    const url = URL.createObjectURL(blob)
    const a = document.createElement('a')
    a.href = url
    a.download = `logger-history-${new Date().toISOString().slice(0, 19).replace(/:/g, '-')}.txt`
    document.body.appendChild(a)
    a.click()
    document.body.removeChild(a)
    URL.revokeObjectURL(url)

    toast({
      title: '日志已导出',
      description: `已导出 ${logs.length} 条历史日志`,
    })
  }

  // 清空日志
  const clearLogs = () => {
    logger.clearLogs()
    refreshStatus()
    toast({
      title: '日志已清空',
      description: '所有历史日志已被清除',
    })
  }

  // 导出Logger配置
  const exportLoggerData = () => {
    const exportData = logger.exportLogs()
    const blob = new Blob([exportData], { type: 'application/json' })
    const url = URL.createObjectURL(blob)
    const a = document.createElement('a')
    a.href = url
    a.download = `logger-export-${new Date().toISOString().slice(0, 19).replace(/:/g, '-')}.json`
    document.body.appendChild(a)
    a.click()
    document.body.removeChild(a)
    URL.revokeObjectURL(url)

    toast({
      title: 'Logger数据已导出',
      description: '包含配置和日志的完整数据已导出',
    })
  }

  const getLevelIcon = (level: string) => {
    switch (level) {
      case 'debug': return <Bug className="h-4 w-4" />
      case 'info': return <Info className="h-4 w-4" />
      case 'warn': return <AlertTriangle className="h-4 w-4" />
      case 'error': return <XCircle className="h-4 w-4" />
      default: return <Info className="h-4 w-4" />
    }
  }

  const getLevelColor = (level: string) => {
    switch (level) {
      case 'debug': return 'bg-gray-100 text-gray-800'
      case 'info': return 'bg-blue-100 text-blue-800'
      case 'warn': return 'bg-yellow-100 text-yellow-800'
      case 'error': return 'bg-red-100 text-red-800'
      default: return 'bg-gray-100 text-gray-800'
    }
  }

  return (
    <DataToolLayout
      title="Logger 测试工具"
      description="测试和验证统一日志管理系统和错误处理功能"
      toolIcon={
        <div className="p-2 rounded-md bg-indigo-100 text-indigo-700 dark:bg-indigo-950 dark:text-indigo-300">
          <Bug className="h-5 w-5" />
        </div>
      }
    >
      <div className="space-y-6">
        <div className="flex items-center justify-between">
          <div>
            <h1 className="text-2xl font-bold">Logger 测试工具</h1>
            <p className="text-muted-foreground">测试和验证统一日志管理系统的功能</p>
          </div>
          <Button variant="outline" onClick={refreshStatus}>
            <RefreshCw className="h-4 w-4 mr-2" />
            刷新状态
          </Button>
        </div>

        {/* Logger状态 */}
        <Card>
          <CardHeader>
            <CardTitle className="flex items-center gap-2">
              <Settings className="h-5 w-5" />
              Logger 状态
            </CardTitle>
            <CardDescription>当前Logger配置和运行状态</CardDescription>
          </CardHeader>
          <CardContent className="space-y-4">
            <div className="grid grid-cols-2 md:grid-cols-4 gap-4">
              <div className="space-y-1">
                <Label className="text-sm font-medium">当前级别</Label>
                <Badge className={getLevelColor(loggerStatus.levelName.toLowerCase())}>
                  {getLevelIcon(loggerStatus.levelName.toLowerCase())}
                  <span className="ml-1">{loggerStatus.levelName}</span>
                </Badge>
              </div>
              <div className="space-y-1">
                <Label className="text-sm font-medium">控制台输出</Label>
                <Badge variant={loggerStatus.enabled ? 'default' : 'secondary'}>
                  {loggerStatus.enabled ? '启用' : '禁用'}
                </Badge>
              </div>
              <div className="space-y-1">
                <Label className="text-sm font-medium">日志持久化</Label>
                <Badge variant={loggerStatus.persist ? 'default' : 'secondary'}>
                  {loggerStatus.persist ? '启用' : '禁用'}
                </Badge>
              </div>
              <div className="space-y-1">
                <Label className="text-sm font-medium">历史日志</Label>
                <Badge variant="outline">
                  {loggerStatus.logCount} / {loggerStatus.maxLogs}
                </Badge>
              </div>
            </div>

            <Separator />

            <div className="grid grid-cols-2 md:grid-cols-3 gap-4">
              <div className="space-y-1">
                <Label className="text-sm font-medium">调试模式</Label>
                <Badge variant={config.debugMode ? 'default' : 'secondary'}>
                  {config.debugMode ? '开启' : '关闭'}
                </Badge>
              </div>
              <div className="space-y-1">
                <Label className="text-sm font-medium">运行环境</Label>
                <Badge variant={loggerStatus.isDevelopment ? 'default' : 'secondary'}>
                  {loggerStatus.isDevelopment ? '开发环境' : '生产环境'}
                </Badge>
              </div>
              <div className="space-y-1">
                <Label className="text-sm font-medium">配置同步</Label>
                <Badge variant={loggerStatus.isConfigSynced ? 'default' : 'destructive'}>
                  {loggerStatus.isConfigSynced ? '已同步' : '未同步'}
                </Badge>
              </div>
            </div>
          </CardContent>
        </Card>

        {/* 测试工具 */}
        <div className="grid md:grid-cols-2 gap-6">
          {/* 单条日志测试 */}
          <Card>
            <CardHeader>
              <CardTitle className="flex items-center gap-2">
                <Play className="h-5 w-5" />
                单条日志测试
              </CardTitle>
              <CardDescription>发送单条测试日志消息</CardDescription>
            </CardHeader>
            <CardContent className="space-y-4">
              <div className="space-y-2">
                <Label htmlFor="testLevel">日志级别</Label>
                <Select value={testLevel} onValueChange={(value: any) => setTestLevel(value)}>
                  <SelectTrigger>
                    <SelectValue />
                  </SelectTrigger>
                  <SelectContent>
                    <SelectItem value="debug">
                      <div className="flex items-center gap-2">
                        <Bug className="h-4 w-4" />
                        DEBUG
                      </div>
                    </SelectItem>
                    <SelectItem value="info">
                      <div className="flex items-center gap-2">
                        <Info className="h-4 w-4" />
                        INFO
                      </div>
                    </SelectItem>
                    <SelectItem value="warn">
                      <div className="flex items-center gap-2">
                        <AlertTriangle className="h-4 w-4" />
                        WARN
                      </div>
                    </SelectItem>
                    <SelectItem value="error">
                      <div className="flex items-center gap-2">
                        <XCircle className="h-4 w-4" />
                        ERROR
                      </div>
                    </SelectItem>
                  </SelectContent>
                </Select>
              </div>

              <div className="space-y-2">
                <Label htmlFor="testMessage">日志消息</Label>
                <Textarea
                  id="testMessage"
                  value={testMessage}
                  onChange={(e) => setTestMessage(e.target.value)}
                  placeholder="输入测试日志消息..."
                  rows={3}
                />
              </div>

              <Button onClick={sendTestLog} className="w-full">
                <Play className="h-4 w-4 mr-2" />
                发送测试日志
              </Button>
            </CardContent>
          </Card>

          {/* 批量测试和管理 */}
          <Card>
            <CardHeader>
              <CardTitle>批量测试和管理</CardTitle>
              <CardDescription>批量操作和日志管理功能</CardDescription>
            </CardHeader>
            <CardContent className="space-y-4">
              <Button onClick={sendBatchLogs} variant="outline" className="w-full">
                <Play className="h-4 w-4 mr-2" />
                发送批量测试日志
              </Button>

              <Separator />

              <div className="space-y-2">
                <Button onClick={getHistoryLogs} variant="outline" className="w-full">
                  <Download className="h-4 w-4 mr-2" />
                  导出历史日志
                </Button>

                <Button onClick={exportLoggerData} variant="outline" className="w-full">
                  <Download className="h-4 w-4 mr-2" />
                  导出完整数据
                </Button>

                <Button onClick={clearLogs} variant="destructive" className="w-full">
                  <Trash2 className="h-4 w-4 mr-2" />
                  清空所有日志
                </Button>
              </div>
            </CardContent>
          </Card>
        </div>

        {/* 使用说明 */}
        <Card>
          <CardHeader>
            <CardTitle>使用说明</CardTitle>
            <CardDescription>Logger系统的使用方法和注意事项</CardDescription>
          </CardHeader>
          <CardContent className="space-y-4">
            <div className="grid md:grid-cols-2 gap-6">
              <div>
                <h4 className="font-medium mb-2">基本使用</h4>
                <ul className="text-sm text-muted-foreground space-y-1">
                  <li>• 使用 log.debug/info/warn/error() 发送日志</li>
                  <li>• 日志会根据当前级别设置进行过滤</li>
                  <li>• 调试模式下强制显示所有日志</li>
                  <li>• 生产环境默认只显示警告和错误</li>
                </ul>
              </div>
              <div>
                <h4 className="font-medium mb-2">配置管理</h4>
                <ul className="text-sm text-muted-foreground space-y-1">
                  <li>• 在设置页面可以调整日志配置</li>
                  <li>• 配置变更会自动同步到Logger</li>
                  <li>• 支持日志持久化到本地存储</li>
                  <li>• 可以设置最大日志保存数量</li>
                </ul>
              </div>
            </div>
          </CardContent>
        </Card>
      </div>
    </DataToolLayout>
  )
}
