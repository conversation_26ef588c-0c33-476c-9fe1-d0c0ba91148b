import { useState, useRef, useMemo } from 'react'
import { DebugToolLayout } from '@/components/debug-tools/debug-tool-layout'
import { Card, CardContent, CardHeader, CardTitle } from '@/components/ui/card'
import { Button } from '@/components/ui/button'
import { Input } from '@/components/ui/input'
import { Textarea } from '@/components/ui/textarea'
import { Badge } from '@/components/ui/badge'
import { Separator } from '@/components/ui/separator'
import { ScrollArea } from '@/components/ui/scroll-area'
import { Tabs, TabsContent, TabsList, TabsTrigger } from '@/components/ui/tabs'
import {
  Select,
  SelectContent,
  SelectItem,
  SelectTrigger,
  SelectValue,
} from '@/components/ui/select'
import {
  FileText,
  Upload,
  Search,
  Filter,
  Download,
  BarChart3,
  AlertTriangle,
  Info,
  XCircle,
  CheckCircle,
  Clock,
  Hash,
  Eye,
  EyeOff,
  RefreshCw,
  Trash2,
} from 'lucide-react'
import { cn } from '@/lib/utils'
import { toast } from '@/components/ui/use-toast'

interface LogEntry {
  id: string
  timestamp: string
  level: 'DEBUG' | 'INFO' | 'WARN' | 'ERROR' | 'FATAL'
  message: string
  source?: string
  thread?: string
  lineNumber: number
  rawLine: string
}

interface LogStats {
  totalLines: number
  errorCount: number
  warnCount: number
  infoCount: number
  debugCount: number
  timeRange: {
    start: string
    end: string
  }
}

export default function LogAnalysisPage() {
  const [logContent, setLogContent] = useState('')
  const [parsedLogs, setParsedLogs] = useState<LogEntry[]>([])
  const [logStats, setLogStats] = useState<LogStats | null>(null)
  const [searchTerm, setSearchTerm] = useState('')
  const [levelFilter, setLevelFilter] = useState('ALL')
  const [sourceFilter, setSourceFilter] = useState('ALL')
  const [showRawLines, setShowRawLines] = useState(false)
  const [isAnalyzing, setIsAnalyzing] = useState(false)

  const fileInputRef = useRef<HTMLInputElement>(null)

  // 日志级别配置
  const LOG_LEVELS = [
    { value: 'ALL', label: '全部', color: 'bg-gray-500' },
    { value: 'DEBUG', label: '调试', color: 'bg-blue-500' },
    { value: 'INFO', label: '信息', color: 'bg-green-500' },
    { value: 'WARN', label: '警告', color: 'bg-yellow-500' },
    { value: 'ERROR', label: '错误', color: 'bg-red-500' },
    { value: 'FATAL', label: '致命', color: 'bg-purple-500' },
  ]

  // 过滤后的日志
  const filteredLogs = useMemo(() => {
    return parsedLogs.filter((log) => {
      const matchesSearch =
        searchTerm === '' ||
        log.message.toLowerCase().includes(searchTerm.toLowerCase()) ||
        (log.source &&
          log.source.toLowerCase().includes(searchTerm.toLowerCase()))

      const matchesLevel = levelFilter === 'ALL' || log.level === levelFilter
      const matchesSource =
        sourceFilter === 'ALL' || log.source === sourceFilter

      return matchesSearch && matchesLevel && matchesSource
    })
  }, [parsedLogs, searchTerm, levelFilter, sourceFilter])

  // 获取唯一的日志源
  const uniqueSources = useMemo(() => {
    const sources = new Set(parsedLogs.map((log) => log.source).filter(Boolean))
    return Array.from(sources)
  }, [parsedLogs])

  // 解析日志内容
  const parseLogContent = (content: string): LogEntry[] => {
    const lines = content.split('\n').filter((line) => line.trim())
    const logs: LogEntry[] = []

    // 常见的日志格式正则表达式
    const patterns = [
      // 标准格式: 2024-01-14 10:30:00 [INFO] [main] Message
      /^(\d{4}-\d{2}-\d{2}\s+\d{2}:\d{2}:\d{2})\s*\[(\w+)\]\s*(?:\[([^\]]+)\])?\s*(.+)$/,
      // 简单格式: [INFO] 2024-01-14 10:30:00 Message
      /^\[(\w+)\]\s+(\d{4}-\d{2}-\d{2}\s+\d{2}:\d{2}:\d{2})\s+(.+)$/,
      // Java格式: 2024-01-14 10:30:00.123 INFO [thread] Class - Message
      /^(\d{4}-\d{2}-\d{2}\s+\d{2}:\d{2}:\d{2}(?:\.\d{3})?)\s+(\w+)\s+(?:\[([^\]]+)\])?\s*(?:([^\s]+)\s*-\s*)?(.+)$/,
    ]

    lines.forEach((line, index) => {
      let parsed = false

      for (const pattern of patterns) {
        const match = line.match(pattern)
        if (match) {
          let timestamp, level, source, message, thread

          if (pattern === patterns[0]) {
            ;[, timestamp, level, thread, message] = match
          } else if (pattern === patterns[1]) {
            ;[, level, timestamp, message] = match
          } else if (pattern === patterns[2]) {
            ;[, timestamp, level, thread, source, message] = match
          }

          logs.push({
            id: `log-${index}`,
            timestamp: timestamp || new Date().toISOString(),
            level: (level?.toUpperCase() as LogEntry['level']) || 'INFO',
            message: message || line,
            source: source || thread,
            thread,
            lineNumber: index + 1,
            rawLine: line,
          })
          parsed = true
          break
        }
      }

      // 如果无法解析，作为普通信息日志处理
      if (!parsed && line.trim()) {
        logs.push({
          id: `log-${index}`,
          timestamp: new Date().toISOString(),
          level: 'INFO',
          message: line,
          lineNumber: index + 1,
          rawLine: line,
        })
      }
    })

    return logs
  }

  // 计算日志统计
  const calculateStats = (logs: LogEntry[]): LogStats => {
    const stats = {
      totalLines: logs.length,
      errorCount: 0,
      warnCount: 0,
      infoCount: 0,
      debugCount: 0,
      timeRange: {
        start: '',
        end: '',
      },
    }

    logs.forEach((log) => {
      switch (log.level) {
        case 'ERROR':
        case 'FATAL':
          stats.errorCount++
          break
        case 'WARN':
          stats.warnCount++
          break
        case 'INFO':
          stats.infoCount++
          break
        case 'DEBUG':
          stats.debugCount++
          break
      }
    })

    // 计算时间范围
    const timestamps = logs
      .map((log) => new Date(log.timestamp))
      .filter((date) => !isNaN(date.getTime()))
    if (timestamps.length > 0) {
      timestamps.sort((a, b) => a.getTime() - b.getTime())
      stats.timeRange.start = timestamps[0].toISOString()
      stats.timeRange.end = timestamps[timestamps.length - 1].toISOString()
    }

    return stats
  }

  // 分析日志
  const analyzeLog = () => {
    if (!logContent.trim()) {
      toast({
        title: '错误',
        description: '请先输入或上传日志内容',
        variant: 'destructive',
      })
      return
    }

    setIsAnalyzing(true)

    // 模拟分析延迟
    setTimeout(() => {
      const logs = parseLogContent(logContent)
      const stats = calculateStats(logs)

      setParsedLogs(logs)
      setLogStats(stats)
      setIsAnalyzing(false)

      toast({
        title: '分析完成',
        description: `成功解析 ${logs.length} 条日志记录`,
      })
    }, 1000)
  }

  // 上传文件
  const handleFileUpload = (event: React.ChangeEvent<HTMLInputElement>) => {
    const file = event.target.files?.[0]
    if (!file) return

    if (file.size > 10 * 1024 * 1024) {
      // 10MB限制
      toast({
        title: '文件过大',
        description: '文件大小不能超过10MB',
        variant: 'destructive',
      })
      return
    }

    const reader = new FileReader()
    reader.onload = (e) => {
      const content = e.target?.result as string
      setLogContent(content)
      toast({
        title: '文件上传成功',
        description: `已加载 ${file.name}`,
      })
    }
    reader.readAsText(file)
  }

  // 清空日志
  const clearLogs = () => {
    setLogContent('')
    setParsedLogs([])
    setLogStats(null)
    setSearchTerm('')
    setLevelFilter('ALL')
    setSourceFilter('ALL')
  }

  // 导出分析结果
  const exportResults = () => {
    if (filteredLogs.length === 0) return

    const csvContent = [
      'Line,Timestamp,Level,Source,Message',
      ...filteredLogs.map(
        (log) =>
          `${log.lineNumber},"${log.timestamp}","${log.level}","${
            log.source || ''
          }","${log.message.replace(/"/g, '""')}"`
      ),
    ].join('\n')

    const blob = new Blob([csvContent], { type: 'text/csv' })
    const url = URL.createObjectURL(blob)
    const a = document.createElement('a')
    a.href = url
    a.download = `log-analysis-${new Date().toISOString().split('T')[0]}.csv`
    document.body.appendChild(a)
    a.click()
    document.body.removeChild(a)
    URL.revokeObjectURL(url)
  }

  // 获取日志级别颜色
  const getLogLevelColor = (level: string) => {
    switch (level) {
      case 'DEBUG':
        return 'border-l-blue-500 bg-blue-50'
      case 'INFO':
        return 'border-l-green-500 bg-green-50'
      case 'WARN':
        return 'border-l-yellow-500 bg-yellow-50'
      case 'ERROR':
      case 'FATAL':
        return 'border-l-red-500 bg-red-50'
      default:
        return 'border-l-gray-500 bg-gray-50'
    }
  }

  // 获取日志级别图标
  const getLogIcon = (level: string) => {
    switch (level) {
      case 'DEBUG':
        return <Info className="h-4 w-4 text-blue-500" />
      case 'INFO':
        return <CheckCircle className="h-4 w-4 text-green-500" />
      case 'WARN':
        return <AlertTriangle className="h-4 w-4 text-yellow-500" />
      case 'ERROR':
      case 'FATAL':
        return <XCircle className="h-4 w-4 text-red-500" />
      default:
        return <Info className="h-4 w-4 text-gray-500" />
    }
  }

  return (
    <DebugToolLayout
      title="日志分析"
      description="分析和解析系统日志文件"
      toolIcon={<FileText className="h-6 w-6" />}
      sidebar={
        <div className="space-y-4">
          <div>
            <h3 className="font-medium text-sm mb-2">支持的格式</h3>
            <div className="text-xs text-muted-foreground space-y-2">
              <p>• 标准日志格式</p>
              <p>• Java应用日志</p>
              <p>• Nginx/Apache访问日志</p>
              <p>• 系统日志(syslog)</p>
              <p>• 自定义格式日志</p>
            </div>
          </div>

          <div>
            <h3 className="font-medium text-sm mb-2">分析功能</h3>
            <div className="text-xs text-muted-foreground space-y-1">
              <p>• 日志级别统计</p>
              <p>• 时间范围分析</p>
              <p>• 错误模式识别</p>
              <p>• 关键词搜索</p>
              <p>• 数据导出</p>
            </div>
          </div>

          <div>
            <h3 className="font-medium text-sm mb-2">使用提示</h3>
            <div className="text-xs text-muted-foreground space-y-1">
              <p>• 支持最大10MB文件</p>
              <p>• 可直接粘贴日志内容</p>
              <p>• 支持多种时间格式</p>
              <p>• 可按级别和来源过滤</p>
            </div>
          </div>
        </div>
      }>
      <div className="space-y-6">
        <Tabs defaultValue="input" className="w-full">
          <TabsList>
            <TabsTrigger value="input">日志输入</TabsTrigger>
            <TabsTrigger value="analysis">分析结果</TabsTrigger>
            <TabsTrigger value="stats">统计信息</TabsTrigger>
          </TabsList>

          <TabsContent value="input" className="space-y-4">
            <Card>
              <CardHeader>
                <CardTitle className="flex items-center gap-2">
                  <Upload className="h-5 w-5" />
                  日志输入
                </CardTitle>
              </CardHeader>
              <CardContent className="space-y-4">
                <div className="flex items-center gap-2">
                  <Button
                    onClick={() => fileInputRef.current?.click()}
                    variant="outline"
                    className="flex items-center gap-2">
                    <Upload className="h-4 w-4" />
                    上传文件
                  </Button>
                  <Button
                    onClick={analyzeLog}
                    disabled={!logContent.trim() || isAnalyzing}
                    className="flex items-center gap-2">
                    {isAnalyzing ? (
                      <RefreshCw className="h-4 w-4 animate-spin" />
                    ) : (
                      <BarChart3 className="h-4 w-4" />
                    )}
                    {isAnalyzing ? '分析中...' : '开始分析'}
                  </Button>
                  <Button
                    onClick={clearLogs}
                    variant="outline"
                    className="flex items-center gap-2">
                    <Trash2 className="h-4 w-4" />
                    清空
                  </Button>
                </div>

                <input
                  ref={fileInputRef}
                  type="file"
                  accept=".log,.txt"
                  onChange={handleFileUpload}
                  className="hidden"
                />

                <Textarea
                  value={logContent}
                  onChange={(e) => setLogContent(e.target.value)}
                  placeholder="粘贴日志内容或上传日志文件..."
                  className="min-h-[300px] font-mono text-sm"
                />
              </CardContent>
            </Card>
          </TabsContent>

          <TabsContent value="analysis" className="space-y-4">
            {parsedLogs.length > 0 && (
              <>
                {/* 过滤控件 */}
                <Card>
                  <CardContent className="pt-6">
                    <div className="flex items-center gap-4 mb-4">
                      <div className="relative flex-1">
                        <Search className="absolute left-3 top-1/2 transform -translate-y-1/2 h-4 w-4 text-gray-400" />
                        <Input
                          placeholder="搜索日志消息..."
                          value={searchTerm}
                          onChange={(e) => setSearchTerm(e.target.value)}
                          className="pl-10"
                        />
                      </div>

                      <Select
                        value={levelFilter}
                        onValueChange={setLevelFilter}>
                        <SelectTrigger className="w-32">
                          <SelectValue />
                        </SelectTrigger>
                        <SelectContent>
                          {LOG_LEVELS.map((level) => (
                            <SelectItem key={level.value} value={level.value}>
                              {level.label}
                            </SelectItem>
                          ))}
                        </SelectContent>
                      </Select>

                      <Select
                        value={sourceFilter}
                        onValueChange={setSourceFilter}>
                        <SelectTrigger className="w-40">
                          <SelectValue placeholder="选择来源" />
                        </SelectTrigger>
                        <SelectContent>
                          <SelectItem value="ALL">全部来源</SelectItem>
                          {uniqueSources.map((source) => (
                            <SelectItem key={source} value={source}>
                              {source}
                            </SelectItem>
                          ))}
                        </SelectContent>
                      </Select>

                      <Button
                        onClick={() => setShowRawLines(!showRawLines)}
                        variant="outline"
                        size="sm"
                        className="flex items-center gap-2">
                        {showRawLines ? (
                          <EyeOff className="h-4 w-4" />
                        ) : (
                          <Eye className="h-4 w-4" />
                        )}
                        {showRawLines ? '隐藏原始' : '显示原始'}
                      </Button>

                      <Button
                        onClick={exportResults}
                        variant="outline"
                        size="sm"
                        disabled={filteredLogs.length === 0}
                        className="flex items-center gap-2">
                        <Download className="h-4 w-4" />
                        导出
                      </Button>
                    </div>

                    <div className="text-sm text-muted-foreground">
                      显示 {filteredLogs.length} / {parsedLogs.length} 条日志
                    </div>
                  </CardContent>
                </Card>

                {/* 日志列表 */}
                <Card>
                  <CardContent className="p-0">
                    <ScrollArea className="h-96">
                      <div className="p-4 space-y-2">
                        {filteredLogs.map((log) => (
                          <div
                            key={log.id}
                            className={cn(
                              'flex items-start gap-3 p-3 rounded-lg border-l-4 transition-colors hover:bg-gray-50',
                              getLogLevelColor(log.level)
                            )}>
                            <div className="flex-shrink-0 mt-0.5">
                              {getLogIcon(log.level)}
                            </div>

                            <div className="flex-1 min-w-0">
                              <div className="flex items-center gap-2 mb-1">
                                <Badge variant="outline" className="text-xs">
                                  {log.level}
                                </Badge>
                                {log.source && (
                                  <Badge
                                    variant="secondary"
                                    className="text-xs">
                                    {log.source}
                                  </Badge>
                                )}
                                <span className="text-xs text-gray-500">
                                  行 {log.lineNumber}
                                </span>
                                <span className="text-xs text-gray-400 ml-auto">
                                  {new Date(log.timestamp).toLocaleString()}
                                </span>
                              </div>

                              <p className="text-sm text-gray-900 break-words">
                                {log.message}
                              </p>

                              {showRawLines && (
                                <div className="mt-2 p-2 bg-gray-100 rounded text-xs font-mono text-gray-600">
                                  {log.rawLine}
                                </div>
                              )}
                            </div>
                          </div>
                        ))}
                      </div>
                    </ScrollArea>
                  </CardContent>
                </Card>
              </>
            )}

            {parsedLogs.length === 0 && (
              <Card>
                <CardContent className="text-center py-8">
                  <FileText className="h-12 w-12 text-muted-foreground mx-auto mb-4" />
                  <h3 className="text-lg font-medium mb-2">暂无分析结果</h3>
                  <p className="text-muted-foreground">
                    请先在"日志输入"标签页中输入或上传日志内容
                  </p>
                </CardContent>
              </Card>
            )}
          </TabsContent>

          <TabsContent value="stats">
            {logStats && (
              <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-4">
                <Card>
                  <CardHeader>
                    <CardTitle className="flex items-center gap-2 text-base">
                      <Hash className="h-4 w-4" />
                      总计
                    </CardTitle>
                  </CardHeader>
                  <CardContent>
                    <div className="text-2xl font-bold">
                      {logStats.totalLines}
                    </div>
                    <div className="text-sm text-muted-foreground">
                      日志条数
                    </div>
                  </CardContent>
                </Card>

                <Card>
                  <CardHeader>
                    <CardTitle className="flex items-center gap-2 text-base">
                      <XCircle className="h-4 w-4 text-red-500" />
                      错误
                    </CardTitle>
                  </CardHeader>
                  <CardContent>
                    <div className="text-2xl font-bold text-red-600">
                      {logStats.errorCount}
                    </div>
                    <div className="text-sm text-muted-foreground">
                      {(
                        (logStats.errorCount / logStats.totalLines) *
                        100
                      ).toFixed(1)}
                      %
                    </div>
                  </CardContent>
                </Card>

                <Card>
                  <CardHeader>
                    <CardTitle className="flex items-center gap-2 text-base">
                      <AlertTriangle className="h-4 w-4 text-yellow-500" />
                      警告
                    </CardTitle>
                  </CardHeader>
                  <CardContent>
                    <div className="text-2xl font-bold text-yellow-600">
                      {logStats.warnCount}
                    </div>
                    <div className="text-sm text-muted-foreground">
                      {(
                        (logStats.warnCount / logStats.totalLines) *
                        100
                      ).toFixed(1)}
                      %
                    </div>
                  </CardContent>
                </Card>

                <Card>
                  <CardHeader>
                    <CardTitle className="flex items-center gap-2 text-base">
                      <CheckCircle className="h-4 w-4 text-green-500" />
                      信息
                    </CardTitle>
                  </CardHeader>
                  <CardContent>
                    <div className="text-2xl font-bold text-green-600">
                      {logStats.infoCount}
                    </div>
                    <div className="text-sm text-muted-foreground">
                      {(
                        (logStats.infoCount / logStats.totalLines) *
                        100
                      ).toFixed(1)}
                      %
                    </div>
                  </CardContent>
                </Card>

                <Card>
                  <CardHeader>
                    <CardTitle className="flex items-center gap-2 text-base">
                      <Info className="h-4 w-4 text-blue-500" />
                      调试
                    </CardTitle>
                  </CardHeader>
                  <CardContent>
                    <div className="text-2xl font-bold text-blue-600">
                      {logStats.debugCount}
                    </div>
                    <div className="text-sm text-muted-foreground">
                      {(
                        (logStats.debugCount / logStats.totalLines) *
                        100
                      ).toFixed(1)}
                      %
                    </div>
                  </CardContent>
                </Card>

                <Card>
                  <CardHeader>
                    <CardTitle className="flex items-center gap-2 text-base">
                      <Clock className="h-4 w-4" />
                      时间范围
                    </CardTitle>
                  </CardHeader>
                  <CardContent>
                    <div className="space-y-2 text-sm">
                      <div>
                        <span className="text-muted-foreground">开始: </span>
                        {logStats.timeRange.start
                          ? new Date(logStats.timeRange.start).toLocaleString()
                          : '未知'}
                      </div>
                      <div>
                        <span className="text-muted-foreground">结束: </span>
                        {logStats.timeRange.end
                          ? new Date(logStats.timeRange.end).toLocaleString()
                          : '未知'}
                      </div>
                    </div>
                  </CardContent>
                </Card>
              </div>
            )}

            {!logStats && (
              <Card>
                <CardContent className="text-center py-8">
                  <BarChart3 className="h-12 w-12 text-muted-foreground mx-auto mb-4" />
                  <h3 className="text-lg font-medium mb-2">暂无统计数据</h3>
                  <p className="text-muted-foreground">
                    请先分析日志内容以查看统计信息
                  </p>
                </CardContent>
              </Card>
            )}
          </TabsContent>
        </Tabs>
      </div>
    </DebugToolLayout>
  )
}
