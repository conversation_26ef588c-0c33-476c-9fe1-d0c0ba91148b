/**
 * 存储管理页面
 * 用于查看和管理localStorage使用情况
 */

import { useState, useEffect } from 'react'
import { DataToolLayout } from '@/components/debug-tools/data-tool-layout'
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from '@/components/ui/card'
import { Button } from '@/components/ui/button'
import { Badge } from '@/components/ui/badge'
import { Progress } from '@/components/ui/progress'
import { Separator } from '@/components/ui/separator'
import { Alert, AlertDescription } from '@/components/ui/alert'
import { storageManager, StorageInfo } from '@/lib/utils/storage-manager'
import { toast } from '@/components/ui/use-toast'
import { 
  HardDrive, 
  Trash2, 
  RefreshCw, 
  Download, 
  AlertTriangle,
  CheckCircle,
  Database,
  Zap
} from 'lucide-react'

export default function StorageManagerPage() {
  const [storageInfo, setStorageInfo] = useState<StorageInfo | null>(null)
  const [isLoading, setIsLoading] = useState(false)

  // 加载存储信息
  const loadStorageInfo = () => {
    setIsLoading(true)
    try {
      const info = storageManager.getStorageInfo()
      setStorageInfo(info)
    } catch (error) {
      toast({
        title: '加载失败',
        description: '无法获取存储信息',
        variant: 'destructive'
      })
    } finally {
      setIsLoading(false)
    }
  }

  // 智能清理
  const handleSmartCleanup = () => {
    setIsLoading(true)
    try {
      const result = storageManager.smartCleanup()
      
      toast({
        title: '清理完成',
        description: `已清理 ${result.cleaned} 个项目，释放 ${formatBytes(result.freedBytes)} 空间`,
      })
      
      loadStorageInfo()
    } catch (error) {
      toast({
        title: '清理失败',
        description: '清理过程中发生错误',
        variant: 'destructive'
      })
    } finally {
      setIsLoading(false)
    }
  }

  // 清理指定项目
  const handleRemoveItem = (key: string) => {
    try {
      storageManager.safeRemoveItem(key)
      toast({
        title: '删除成功',
        description: `已删除项目: ${key}`,
      })
      loadStorageInfo()
    } catch (error) {
      toast({
        title: '删除失败',
        description: `无法删除项目: ${key}`,
        variant: 'destructive'
      })
    }
  }

  // 导出存储报告
  const handleExportReport = () => {
    try {
      const report = storageManager.getStorageReport()
      const blob = new Blob([report], { type: 'text/plain' })
      const url = URL.createObjectURL(blob)
      const a = document.createElement('a')
      a.href = url
      a.download = `storage-report-${new Date().toISOString().slice(0, 19).replace(/:/g, '-')}.txt`
      document.body.appendChild(a)
      a.click()
      document.body.removeChild(a)
      URL.revokeObjectURL(url)

      toast({
        title: '导出成功',
        description: '存储报告已导出',
      })
    } catch (error) {
      toast({
        title: '导出失败',
        description: '无法导出存储报告',
        variant: 'destructive'
      })
    }
  }

  // 格式化字节数
  const formatBytes = (bytes: number): string => {
    if (bytes === 0) return '0 B'
    const k = 1024
    const sizes = ['B', 'KB', 'MB', 'GB']
    const i = Math.floor(Math.log(bytes) / Math.log(k))
    return parseFloat((bytes / Math.pow(k, i)).toFixed(2)) + ' ' + sizes[i]
  }

  // 获取使用率颜色
  const getUsageColor = (percentage: number) => {
    if (percentage < 50) return 'bg-green-500'
    if (percentage < 80) return 'bg-yellow-500'
    return 'bg-red-500'
  }

  // 检查是否为受保护的键
  const isProtectedKey = (key: string): boolean => {
    const protectedPrefixes = [
      'fusion:auth:',
      'fusion:user:',
      'fusion:config:',
      'fusion:settings:',
      'logger-settings'
    ]
    return protectedPrefixes.some(prefix => key.startsWith(prefix))
  }

  useEffect(() => {
    loadStorageInfo()
  }, [])

  return (
    <DataToolLayout
      title="存储管理工具"
      description="查看和管理浏览器localStorage使用情况"
      toolIcon={
        <div className="p-2 rounded-md bg-purple-100 text-purple-700 dark:bg-purple-950 dark:text-purple-300">
          <HardDrive className="h-5 w-5" />
        </div>
      }
    >
      <div className="space-y-6">
        <div className="flex items-center justify-between">
          <div>
            <h1 className="text-2xl font-bold">存储管理工具</h1>
            <p className="text-muted-foreground">监控和管理浏览器localStorage使用情况</p>
          </div>
          <div className="flex gap-2">
            <Button variant="outline" onClick={loadStorageInfo} disabled={isLoading}>
              <RefreshCw className={`h-4 w-4 mr-2 ${isLoading ? 'animate-spin' : ''}`} />
              刷新
            </Button>
            <Button variant="outline" onClick={handleExportReport}>
              <Download className="h-4 w-4 mr-2" />
              导出报告
            </Button>
          </div>
        </div>

        {storageInfo && (
          <>
            {/* 存储概览 */}
            <Card>
              <CardHeader>
                <CardTitle className="flex items-center gap-2">
                  <Database className="h-5 w-5" />
                  存储概览
                </CardTitle>
                <CardDescription>localStorage使用情况统计</CardDescription>
              </CardHeader>
              <CardContent className="space-y-4">
                <div className="grid grid-cols-1 md:grid-cols-3 gap-4">
                  <div className="space-y-2">
                    <div className="flex justify-between text-sm">
                      <span>已使用</span>
                      <span className="font-medium">{formatBytes(storageInfo.used)}</span>
                    </div>
                    <div className="flex justify-between text-sm">
                      <span>可用空间</span>
                      <span className="font-medium">{formatBytes(storageInfo.available)}</span>
                    </div>
                    <div className="flex justify-between text-sm">
                      <span>总容量</span>
                      <span className="font-medium">{formatBytes(storageInfo.total)}</span>
                    </div>
                  </div>
                  
                  <div className="space-y-2">
                    <div className="flex justify-between text-sm">
                      <span>使用率</span>
                      <span className="font-medium">{storageInfo.usagePercentage.toFixed(1)}%</span>
                    </div>
                    <Progress 
                      value={storageInfo.usagePercentage} 
                      className="h-2"
                    />
                    <div className="text-xs text-muted-foreground">
                      {storageInfo.usagePercentage > 80 ? '存储空间紧张' : '存储空间充足'}
                    </div>
                  </div>

                  <div className="space-y-2">
                    <div className="flex justify-between text-sm">
                      <span>存储项数量</span>
                      <span className="font-medium">{storageInfo.items.length}</span>
                    </div>
                    <div className="flex justify-between text-sm">
                      <span>平均大小</span>
                      <span className="font-medium">
                        {storageInfo.items.length > 0 
                          ? formatBytes(storageInfo.used / storageInfo.items.length)
                          : '0 B'
                        }
                      </span>
                    </div>
                  </div>
                </div>

                {storageInfo.usagePercentage > 80 && (
                  <Alert>
                    <AlertTriangle className="h-4 w-4" />
                    <AlertDescription>
                      存储空间使用率较高，建议进行清理以避免存储失败。
                    </AlertDescription>
                  </Alert>
                )}
              </CardContent>
            </Card>

            {/* 快速操作 */}
            <Card>
              <CardHeader>
                <CardTitle className="flex items-center gap-2">
                  <Zap className="h-5 w-5" />
                  快速操作
                </CardTitle>
                <CardDescription>常用的存储管理操作</CardDescription>
              </CardHeader>
              <CardContent>
                <div className="flex flex-wrap gap-2">
                  <Button onClick={handleSmartCleanup} disabled={isLoading}>
                    <Trash2 className="h-4 w-4 mr-2" />
                    智能清理
                  </Button>
                  <Button 
                    variant="outline" 
                    onClick={() => {
                      const cleaned = storageManager.clearByPrefix('temp:')
                      toast({
                        title: '清理完成',
                        description: `已清理 ${cleaned} 个临时文件`,
                      })
                      loadStorageInfo()
                    }}
                  >
                    清理临时文件
                  </Button>
                  <Button 
                    variant="outline" 
                    onClick={() => {
                      const cleaned = storageManager.clearByPrefix('cache:')
                      toast({
                        title: '清理完成',
                        description: `已清理 ${cleaned} 个缓存文件`,
                      })
                      loadStorageInfo()
                    }}
                  >
                    清理缓存
                  </Button>
                  <Button 
                    variant="outline" 
                    onClick={() => {
                      const cleaned = storageManager.clearByPrefix('app-logs')
                      toast({
                        title: '清理完成',
                        description: `已清理 ${cleaned} 个日志文件`,
                      })
                      loadStorageInfo()
                    }}
                  >
                    清理日志
                  </Button>
                </div>
              </CardContent>
            </Card>

            {/* 存储项详情 */}
            <Card>
              <CardHeader>
                <CardTitle>存储项详情</CardTitle>
                <CardDescription>所有localStorage项目的详细信息</CardDescription>
              </CardHeader>
              <CardContent>
                <div className="space-y-2">
                  {storageInfo.items.map((item, index) => (
                    <div key={item.key} className="flex items-center justify-between p-3 border rounded-lg">
                      <div className="flex-1 min-w-0">
                        <div className="flex items-center gap-2">
                          <span className="font-medium truncate">{item.key}</span>
                          {isProtectedKey(item.key) && (
                            <Badge variant="secondary" className="text-xs">
                              <CheckCircle className="h-3 w-3 mr-1" />
                              受保护
                            </Badge>
                          )}
                        </div>
                        <div className="text-sm text-muted-foreground">
                          大小: {item.sizeFormatted}
                        </div>
                      </div>
                      <div className="flex items-center gap-2">
                        <Badge variant="outline">
                          #{index + 1}
                        </Badge>
                        {!isProtectedKey(item.key) && (
                          <Button
                            variant="destructive"
                            size="sm"
                            onClick={() => handleRemoveItem(item.key)}
                          >
                            <Trash2 className="h-3 w-3" />
                          </Button>
                        )}
                      </div>
                    </div>
                  ))}
                  
                  {storageInfo.items.length === 0 && (
                    <div className="text-center py-8 text-muted-foreground">
                      没有找到任何存储项目
                    </div>
                  )}
                </div>
              </CardContent>
            </Card>
          </>
        )}

        {!storageInfo && !isLoading && (
          <Card>
            <CardContent className="text-center py-8">
              <p className="text-muted-foreground">点击刷新按钮加载存储信息</p>
            </CardContent>
          </Card>
        )}
      </div>
    </DataToolLayout>
  )
}
