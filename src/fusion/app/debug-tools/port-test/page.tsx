import { PortScanner } from '@/components/debug-tools/port-scanner'
import { DebugToolLayout } from '@/components/debug-tools/debug-tool-layout'
import {
  Network,
  Server,
  Database,
  Globe,
  Clock,
  List,
  Info,
  Zap,
} from 'lucide-react'
import { useState } from 'react'

export default function PortTestPage() {
  const [selectedHost, setSelectedHost] = useState<string>('')
  const [selectedPortRange, setSelectedPortRange] = useState<string>('')

  // 预设的主机列表
  const commonHosts = [
    {
      name: 'localhost',
      value: 'localhost',
      icon: <Server className="h-4 w-4 mr-2 text-blue-500" />,
    },
    {
      name: '***********',
      value: '***********',
      icon: <Network className="h-4 w-4 mr-2 text-green-500" />,
    },
    {
      name: 'example.com',
      value: 'example.com',
      icon: <Globe className="h-4 w-4 mr-2 text-purple-500" />,
    },
  ]

  // 预设的端口范围
  const portRanges = [
    {
      name: '1-1024 (常用端口)',
      value: '1-1024',
      icon: <List className="h-4 w-4 mr-2 text-blue-500" />,
    },
    {
      name: '1-65535 (全部端口)',
      value: '1-65535',
      icon: <Zap className="h-4 w-4 mr-2 text-orange-500" />,
    },
    {
      name: '20-25,80,443 (Web服务)',
      value: '20-25,80,443',
      icon: <Globe className="h-4 w-4 mr-2 text-green-500" />,
    },
    {
      name: '3306,5432,1433 (数据库)',
      value: '3306,5432,1433',
      icon: <Database className="h-4 w-4 mr-2 text-purple-500" />,
    },
  ]

  const handleHostSelect = (host: string) => {
    setSelectedHost(host)
  }

  const handlePortRangeSelect = (range: string) => {
    setSelectedPortRange(range)
  }

  return (
    <DebugToolLayout
      title="端口测试"
      description="扫描主机开放的网络端口，检测服务可用性"
      toolIcon={<Network className="h-8 w-8 text-purple-500" />}
      sidebar={
        <div className="space-y-6">
          <div className="bg-card rounded-lg p-3 border shadow-sm">
            <h3 className="mb-3 text-sm font-medium flex items-center">
              <Server className="h-4 w-4 mr-2 text-blue-500" />
              常用主机
            </h3>
            <ul className="space-y-1 text-sm">
              {commonHosts.map((host, index) => (
                <li
                  key={index}
                  className={`rounded-md px-3 py-2 cursor-pointer flex items-center transition-colors
                      ${
                        selectedHost === host.value
                          ? 'bg-accent text-accent-foreground font-medium shadow-sm'
                          : 'hover:bg-accent/50'
                      }`}
                  onClick={() => handleHostSelect(host.value)}>
                  {host.icon}
                  {host.name}
                </li>
              ))}
            </ul>
          </div>

          <div className="bg-card rounded-lg p-3 border shadow-sm">
            <h3 className="mb-3 text-sm font-medium flex items-center">
              <Clock className="h-4 w-4 mr-2 text-blue-500" />
              常用端口范围
            </h3>
            <ul className="space-y-1 text-sm">
              {portRanges.map((range, index) => (
                <li
                  key={index}
                  className={`rounded-md px-3 py-2 cursor-pointer flex items-center transition-colors
                      ${
                        selectedPortRange === range.value
                          ? 'bg-accent text-accent-foreground font-medium shadow-sm'
                          : 'hover:bg-accent/50'
                      }`}
                  onClick={() => handlePortRangeSelect(range.value)}>
                  {range.icon}
                  {range.name}
                </li>
              ))}
            </ul>
          </div>

          <div className="bg-card rounded-lg p-3 border shadow-sm">
            <h3 className="mb-3 text-sm font-medium flex items-center">
              <Info className="h-4 w-4 mr-2 text-blue-500" />
              常见服务端口
            </h3>
            <ul className="space-y-1 text-sm">
              <li className="rounded-md px-3 py-2 cursor-pointer hover:bg-accent/50 flex items-center">
                <Database className="h-4 w-4 mr-2 text-blue-500" />
                21 - FTP
              </li>
              <li className="rounded-md px-3 py-2 cursor-pointer hover:bg-accent/50 flex items-center">
                <Database className="h-4 w-4 mr-2 text-green-500" />
                22 - SSH
              </li>
              <li className="rounded-md px-3 py-2 cursor-pointer hover:bg-accent/50 flex items-center">
                <Globe className="h-4 w-4 mr-2 text-purple-500" />
                80 - HTTP
              </li>
              <li className="rounded-md px-3 py-2 cursor-pointer hover:bg-accent/50 flex items-center">
                <Globe className="h-4 w-4 mr-2 text-purple-500" />
                443 - HTTPS
              </li>
              <li className="rounded-md px-3 py-2 cursor-pointer hover:bg-accent/50 flex items-center">
                <Database className="h-4 w-4 mr-2 text-orange-500" />
                3306 - MySQL
              </li>
              <li className="rounded-md px-3 py-2 cursor-pointer hover:bg-accent/50 flex items-center">
                <Database className="h-4 w-4 mr-2 text-blue-500" />
                5432 - PostgreSQL
              </li>
              <li className="rounded-md px-3 py-2 cursor-pointer hover:bg-accent/50 flex items-center">
                <Database className="h-4 w-4 mr-2 text-red-500" />
                6379 - Redis
              </li>
              <li className="rounded-md px-3 py-2 cursor-pointer hover:bg-accent/50 flex items-center">
                <Database className="h-4 w-4 mr-2 text-green-500" />
                27017 - MongoDB
              </li>
            </ul>
          </div>
        </div>
      }>
      <PortScanner
        selectedHost={selectedHost}
        selectedPortRange={selectedPortRange}
      />
    </DebugToolLayout>
  )
}
