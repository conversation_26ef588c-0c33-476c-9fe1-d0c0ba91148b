import { FileJson } from 'lucide-react'
import { DataToolLayout } from '@/components/debug-tools/data-tool-layout'
import { JsonFormatter } from '@/components/debug-tools/json-formatter'

// 调试工具-JSON格式化工具
export default function JsonFormatterPage() {
  return (
    <DataToolLayout
      title="JSON格式化工具"
      description="格式化、验证和美化JSON数据"
      toolIcon={
        <div className="p-2 rounded-md bg-blue-100 text-blue-700 dark:bg-blue-950 dark:text-blue-300">
          <FileJson className="h-5 w-5" />
        </div>
      }
      sidebar={
        <div className="space-y-4">
          <div>
            <h3 className="text-sm font-medium mb-2">使用说明</h3>
            <ul className="text-sm space-y-1 text-muted-foreground">
              <li>• 在左侧输入或粘贴JSON数据</li>
              <li>• 自动格式化和验证JSON</li>
              <li>• 切换美化或压缩模式</li>
              <li>• 复制或下载格式化后的JSON</li>
            </ul>
          </div>

          <div>
            <h3 className="text-sm font-medium mb-2">快捷操作</h3>
            <ul className="text-sm space-y-1 text-muted-foreground">
              <li>• 上传JSON文件</li>
              <li>• 生成示例JSON</li>
              <li>• 清空输入区域</li>
              <li>• 复制格式化结果</li>
            </ul>
          </div>

          <div>
            <h3 className="text-sm font-medium mb-2">常见问题</h3>
            <ul className="text-sm space-y-1 text-muted-foreground">
              <li>• JSON中的键必须使用双引号</li>
              <li>• 最后一个元素后不能有逗号</li>
              <li>• 字符串值必须使用双引号</li>
              <li>• 数字不能以0开头（除非是0本身）</li>
            </ul>
          </div>
        </div>
      }>
      <JsonFormatter />
    </DataToolLayout>
  )
}
