// 数据转换工具
import { Repeat } from 'lucide-react'
import { DataToolLayout } from '@/components/debug-tools/data-tool-layout'
import { DataConverter } from '@/components/debug-tools/data-converter'

export default function DataConverterPage() {
  return (
    <DataToolLayout
      title="数据转换工具"
      description="各种数据格式互相转换"
      toolIcon={
        <div className="p-2 rounded-md bg-emerald-100 text-emerald-700 dark:bg-emerald-950 dark:text-emerald-300">
          <Repeat className="h-5 w-5" />
        </div>
      }
      sidebar={
        <div className="space-y-4">
          <div>
            <h3 className="text-sm font-medium mb-2">使用说明</h3>
            <ul className="text-sm space-y-1 text-muted-foreground">
              <li>• 选择要转换的数据类型</li>
              <li>• 输入原始数据</li>
              <li>• 设置转换参数</li>
              <li>• 查看转换结果</li>
            </ul>
          </div>

          <div>
            <h3 className="text-sm font-medium mb-2">支持的转换类型</h3>
            <ul className="text-sm space-y-1 text-muted-foreground">
              <li>• 整数转换：各种整数类型互转</li>
              <li>• 浮点数转换：float和double转换</li>
              <li>• 字符串转换：多种编码格式转换</li>
              <li>• 时间转换：时间戳与日期时间互转</li>
            </ul>
          </div>

          <div>
            <h3 className="text-sm font-medium mb-2">字节顺序说明</h3>
            <ul className="text-sm space-y-1 text-muted-foreground">
              <li>• DCBA：小端序（默认）</li>
              <li>• ABCD：大端序</li>
              <li>• BADC：字节对交换</li>
              <li>• CDAB：字节对交换（大端序）</li>
            </ul>
          </div>
        </div>
      }>
      <DataConverter />
    </DataToolLayout>
  )
}
