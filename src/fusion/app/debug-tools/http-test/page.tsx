import { HttpTester } from '@/components/debug-tools/http-tester'
import { DebugToolLayout } from '@/components/debug-tools/debug-tool-layout'
import {
  Globe,
  Server,
  Code,
  History,
  ArrowRight,
  ExternalLink,
  Settings,
  CheckCircle2,
  FileText,
  BookOpen,
  HelpCircle,
  Lock,
} from 'lucide-react'
import { Card, CardContent, CardHeader, CardTitle } from '@/components/ui/card'
import { Separator } from '@/components/ui/separator'
import { Badge } from '@/components/ui/badge'
import { Button } from '@/components/ui/button'
import {
  Tooltip,
  TooltipContent,
  TooltipProvider,
  TooltipTrigger,
} from '@/components/ui/tooltip'
import { Link } from 'react-router-dom'
import { HttpSidebar } from '@/components/debug-tools/http-sidebar'
import { useState } from 'react'
import {
  ResizablePanelGroup,
  ResizablePanel,
  ResizableHandle,
} from '@/components/ui/resizable'
import {
  Dialog,
  DialogContent,
  DialogDescription,
  DialogHeader,
  DialogTitle,
  DialogTrigger,
} from '@/components/ui/dialog'
import {
  Environment,
  HttpEnvironment,
} from '@/components/debug-tools/http-environment'

export default function HttpTestPage() {
  // 创建共享状态，用于在侧边栏和主内容区域之间传递数据
  const [selectedExample, setSelectedExample] = useState<any>(null)
  const [selectedHistoryRequest, setSelectedHistoryRequest] =
    useState<any>(null)
  // 添加环境变量管理对话框状态
  const [showEnvDialog, setShowEnvDialog] = useState(false)
  // 添加当前环境状态
  const [activeEnvironment, setActiveEnvironment] =
    useState<Environment | null>(null)

  // 处理侧边栏中的示例选择
  const handleSelectExample = (example: any) => {
    setSelectedExample(example)
  }

  // 处理侧边栏中的历史记录选择
  const handleSelectHistoryRequest = (request: any) => {
    setSelectedHistoryRequest(request)
  }

  // 处理环境变更
  const handleEnvironmentChange = (environment: Environment | null) => {
    setActiveEnvironment(environment)
  }

  return (
    <DebugToolLayout
      title="HTTP测试工具"
      description="测试HTTP请求和API接口"
      toolIcon={<Globe className="h-6 w-6 text-blue-500" />}
      toolType="http"
      sidebar={
        <div className="space-y-6 px-2">
          {/* 工具介绍 */}
          <div>
            <h3 className="font-medium text-sm mb-2">工具介绍</h3>
            <div className="text-xs text-muted-foreground space-y-1.5">
              <p>
                HTTP测试工具是一个功能强大的接口测试工具，用于发送HTTP请求并分析响应结果，适用于API开发、调试和测试。
              </p>
            </div>
          </div>

          {/* 主要功能 */}
          <div>
            <h3 className="font-medium text-sm mb-2">主要功能</h3>
            <div className="space-y-2">
              {[
                {
                  icon: <Globe className="h-3.5 w-3.5 text-blue-500" />,
                  text: '支持所有标准HTTP方法',
                },
                {
                  icon: <Server className="h-3.5 w-3.5 text-green-500" />,
                  text: '多种请求体格式支持',
                },
                {
                  icon: <Code className="h-3.5 w-3.5 text-purple-500" />,
                  text: '自动格式化JSON响应',
                },
                {
                  icon: <Settings className="h-3.5 w-3.5 text-amber-500" />,
                  text: '环境变量管理系统',
                },
                {
                  icon: <Lock className="h-3.5 w-3.5 text-red-500" />,
                  text: '安全存储敏感信息',
                },
                {
                  icon: <History className="h-3.5 w-3.5 text-indigo-500" />,
                  text: '请求历史记录与重用',
                },
              ].map((item, index) => (
                <div key={index} className="flex items-start">
                  <div className="mr-2 mt-0.5">{item.icon}</div>
                  <span className="text-xs">{item.text}</span>
                </div>
              ))}
            </div>
          </div>

          {/* 使用指南 */}
          <div>
            <h3 className="font-medium text-sm mb-2">使用指南</h3>
            <div className="space-y-2">
              {[
                '在URL字段输入目标地址，选择HTTP方法',
                '添加必要的请求头和查询参数',
                '对于POST/PUT等方法，输入请求体内容',
                '点击"发送请求"按钮执行请求',
                '在响应标签页查看服务器返回结果',
                '可以保存请求为模板或查看历史记录',
              ].map((tip, index) => (
                <div key={index} className="flex items-start">
                  <CheckCircle2 className="h-3 w-3 text-green-500 mt-0.5 mr-1.5 flex-shrink-0" />
                  <span className="text-xs">{tip}</span>
                </div>
              ))}
            </div>
          </div>

          {/* 环境变量提示 */}
          <div>
            <h3 className="font-medium text-sm mb-2">环境变量使用</h3>
            <div className="text-xs text-muted-foreground space-y-1.5">
              <p>
                使用{' '}
                <code className="px-1 py-0.5 bg-gray-100 rounded text-xs">
                  {'{{变量名}}'}
                </code>{' '}
                格式在请求中引用环境变量，例如：
              </p>
              <div className="bg-gray-50 p-2 rounded border text-xs font-mono">
                https://api.example.com/v1/users
                <br />
                Authorization: Bearer {'{{token}}'}
              </div>
              <p className="mt-1">点击右上角"环境变量"按钮进行管理</p>
            </div>
          </div>

          {/* 相关资源 */}
          <div>
            <h3 className="font-medium text-sm mb-2">相关资源</h3>
            <div className="space-y-2">
              <Link
                to="https://developer.mozilla.org/zh-CN/docs/Web/HTTP"
                target="_blank"
                className="flex items-center text-xs text-blue-600 hover:underline">
                <BookOpen className="h-3 w-3 mr-1.5" />
                MDN HTTP 文档
              </Link>
              <Link
                to="https://jsonplaceholder.typicode.com/"
                target="_blank"
                className="flex items-center text-xs text-blue-600 hover:underline">
                <Server className="h-3 w-3 mr-1.5" />
                JSONPlaceholder (测试API)
              </Link>
              <Link
                to="https://www.postman.com/postman/workspace/postman-public-workspace/documentation/12959542-c8142d51-e97c-46b6-bd77-52bb66712c9a"
                target="_blank"
                className="flex items-center text-xs text-blue-600 hover:underline">
                <FileText className="h-3 w-3 mr-1.5" />
                API测试最佳实践
              </Link>
            </div>
          </div>

          {/* 提示和技巧 */}
          <div>
            <h3 className="font-medium text-sm mb-2">提示和技巧</h3>
            <div className="bg-blue-50 border border-blue-100 rounded p-2">
              <div className="flex">
                <HelpCircle className="h-3.5 w-3.5 text-blue-500 mt-0.5 mr-1.5 flex-shrink-0" />
                <div className="text-xs text-blue-700">
                  <p>
                    双击JSON响应可自动格式化内容。使用环境变量可以轻松切换开发、测试和生产环境。
                  </p>
                </div>
              </div>
            </div>
          </div>
        </div>
      }>
      {/* 使用ResizablePanelGroup实现可调整大小的两列布局 */}
      <ResizablePanelGroup direction="horizontal">
        {/* 第一列：请求资源库（示例、历史记录和参考资料） */}
        <ResizablePanel defaultSize={25} minSize={20} maxSize={35}>
          <HttpSidebar
            onSelectExample={handleSelectExample}
            onSelectHistoryRequest={handleSelectHistoryRequest}
          />
        </ResizablePanel>

        {/* 第一列和第二列之间的分隔条 */}
        <ResizableHandle withHandle />

        {/* 第二列：主内容区域 */}
        <ResizablePanel defaultSize={75}>
          {/* 增强的页面头部 */}
          <div className="mb-6 space-y-4 px-6">
            <div className="flex items-center justify-between">
              <div className="flex items-center space-x-2">
                <div className="p-2 bg-blue-100 rounded-lg">
                  <Globe className="h-8 w-8 text-blue-600" />
                </div>
                <div>
                  <h1 className="text-2xl font-bold tracking-tight">
                    HTTP测试工具
                  </h1>
                  <p className="text-muted-foreground">
                    发送自定义HTTP请求并分析响应结果
                  </p>
                </div>
              </div>
              <div className="flex items-center gap-2">
                {/* 环境变量管理入口 */}
                <Dialog open={showEnvDialog} onOpenChange={setShowEnvDialog}>
                  <DialogTrigger asChild>
                    <Button variant="outline" size="sm">
                      <Settings className="mr-2 h-4 w-4" />
                      环境变量
                      {activeEnvironment && (
                        <Badge variant="outline" className="ml-2">
                          {activeEnvironment.name}
                        </Badge>
                      )}
                    </Button>
                  </DialogTrigger>
                  <DialogContent className="sm:max-w-[600px]">
                    <DialogHeader>
                      <DialogTitle>环境变量管理</DialogTitle>
                      <DialogDescription>
                        创建和管理环境变量，用于在请求中使用。变量格式:{' '}
                        {'{{变量名}}'}
                      </DialogDescription>
                    </DialogHeader>
                    <div className="py-4">
                      <HttpEnvironment
                        onEnvironmentChange={handleEnvironmentChange}
                      />
                    </div>
                  </DialogContent>
                </Dialog>

                <TooltipProvider>
                  <Tooltip>
                    <TooltipTrigger asChild>
                      <Button variant="outline" size="sm" asChild>
                        <Link
                          to="https://developer.mozilla.org/zh-CN/docs/Web/HTTP"
                          target="_blank">
                          <ExternalLink className="mr-2 h-4 w-4" />
                          HTTP协议文档
                        </Link>
                      </Button>
                    </TooltipTrigger>
                    <TooltipContent>
                      <p>查看HTTP协议相关文档</p>
                    </TooltipContent>
                  </Tooltip>
                </TooltipProvider>
              </div>
            </div>

            <div className="grid grid-cols-1 md:grid-cols-3 gap-4">
              <Card className="border-l-4 border-l-blue-500">
                <CardHeader className="pb-2">
                  <CardTitle className="text-sm font-medium flex items-center">
                    <Server className="h-4 w-4 mr-2 text-blue-500" />
                    请求配置
                  </CardTitle>
                </CardHeader>
                <CardContent>
                  <p className="text-xs text-muted-foreground">
                    配置HTTP方法、URL、请求头和请求体，发送自定义请求
                  </p>
                </CardContent>
              </Card>

              <Card className="border-l-4 border-l-green-500">
                <CardHeader className="pb-2">
                  <CardTitle className="text-sm font-medium flex items-center">
                    <Code className="h-4 w-4 mr-2 text-green-500" />
                    响应分析
                  </CardTitle>
                </CardHeader>
                <CardContent>
                  <p className="text-xs text-muted-foreground">
                    查看响应状态、头信息和正文，支持JSON格式化和预览
                  </p>
                </CardContent>
              </Card>

              <Card className="border-l-4 border-l-purple-500">
                <CardHeader className="pb-2">
                  <CardTitle className="text-sm font-medium flex items-center">
                    <History className="h-4 w-4 mr-2 text-purple-500" />
                    请求资源库
                  </CardTitle>
                </CardHeader>
                <CardContent>
                  <p className="text-xs text-muted-foreground">
                    浏览示例请求和历史记录，快速重复或修改之前的请求
                  </p>
                </CardContent>
              </Card>
            </div>

            <div className="bg-blue-50 border border-blue-200 rounded-lg p-3 text-sm flex items-start">
              <div className="bg-blue-100 p-1 rounded mr-3 mt-0.5">
                <ArrowRight className="h-4 w-4 text-blue-700" />
              </div>
              <div>
                <p className="text-blue-900">
                  <span className="font-medium">小提示：</span>
                  使用左侧的请求示例快速开始，或点击历史记录加载之前的请求。双击响应体可以自动格式化JSON内容。
                  您还可以使用环境变量（格式：{'{{变量名}}'}）来简化请求配置。
                </p>
              </div>
            </div>
          </div>

          <Separator className="my-6" />

          {/* 主要工具组件 */}
          <div className="bg-white rounded-lg border shadow-sm mx-6">
            <HttpTester
              selectedExample={selectedExample}
              selectedHistoryRequest={selectedHistoryRequest}
            />
          </div>

          {/* 页脚信息 */}
          <div className="mt-8 text-center text-sm text-muted-foreground px-6">
            <p>HTTP测试工具 · 版本 1.0.0</p>
            <div className="flex justify-center space-x-2 mt-1">
              <Badge variant="outline">REST</Badge>
              <Badge variant="outline">API</Badge>
              <Badge variant="outline">调试</Badge>
            </div>
          </div>
        </ResizablePanel>
      </ResizablePanelGroup>
    </DebugToolLayout>
  )
}
