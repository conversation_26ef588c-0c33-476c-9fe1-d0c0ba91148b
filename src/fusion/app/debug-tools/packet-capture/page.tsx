import { PacketCapturer } from '@/components/debug-tools/packet-capturer'
import { DebugToolLayout } from '@/components/debug-tools/debug-tool-layout'
import { Wifi } from 'lucide-react'

export const metadata = {
  title: '网络抓包 | 调试工具',
  description: '捕获和分析网络数据包，监控网络流量',
}

export default function PacketCapturePage() {
  return (
    <DebugToolLayout
      title="网络抓包"
      description="捕获和分析网络数据包，监控网络流量"
      toolIcon={<Wifi className="h-8 w-8 text-blue-500" />}
      sidebar={
        <div className="space-y-4">
          <div>
            <h3 className="mb-2 text-sm font-medium">捕获过滤器</h3>
            <ul className="space-y-1 text-sm">
              <li className="rounded-md bg-accent/50 px-2 py-1 cursor-pointer hover:bg-accent">
                ip.addr == ***********
              </li>
              <li className="rounded-md px-2 py-1 cursor-pointer hover:bg-accent">
                tcp.port == 80
              </li>
              <li className="rounded-md px-2 py-1 cursor-pointer hover:bg-accent">
                http
              </li>
              <li className="rounded-md px-2 py-1 cursor-pointer hover:bg-accent">
                dns
              </li>
              <li className="rounded-md px-2 py-1 cursor-pointer hover:bg-accent">
                icmp
              </li>
            </ul>
          </div>

          <div>
            <h3 className="mb-2 text-sm font-medium">保存的捕获</h3>
            <ul className="space-y-1 text-sm">
              <li className="rounded-md px-2 py-1 cursor-pointer hover:bg-accent">
                HTTP会话 (2023-10-15)
              </li>
              <li className="rounded-md px-2 py-1 cursor-pointer hover:bg-accent">
                DNS查询 (2023-10-10)
              </li>
              <li className="rounded-md px-2 py-1 cursor-pointer hover:bg-accent">
                TCP连接问题 (2023-09-28)
              </li>
            </ul>
          </div>

          <div>
            <h3 className="mb-2 text-sm font-medium">协议分析</h3>
            <ul className="space-y-1 text-sm">
              <li className="rounded-md px-2 py-1 cursor-pointer hover:bg-accent">
                HTTP协议
              </li>
              <li className="rounded-md px-2 py-1 cursor-pointer hover:bg-accent">
                TCP协议
              </li>
              <li className="rounded-md px-2 py-1 cursor-pointer hover:bg-accent">
                UDP协议
              </li>
              <li className="rounded-md px-2 py-1 cursor-pointer hover:bg-accent">
                DNS协议
              </li>
              <li className="rounded-md px-2 py-1 cursor-pointer hover:bg-accent">
                ICMP协议
              </li>
            </ul>
          </div>
        </div>
      }>
      <PacketCapturer />
    </DebugToolLayout>
  )
}
