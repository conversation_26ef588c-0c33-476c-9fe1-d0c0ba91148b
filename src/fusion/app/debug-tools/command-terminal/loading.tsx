/**
 * 命令终端页面加载组件
 *
 * 提供命令终端页面加载中的显示状态
 */

import { MainLayout } from '@/components/layout/main-layout'
import { Skeleton } from '@/components/ui/skeleton'

export default function CommandTerminalLoading() {
  return (
    <MainLayout>
      <div className="container py-6 space-y-6">
        {/* 标题加载状态 */}
        <div className="flex items-center gap-3">
          <Skeleton className="h-12 w-12" />
          <div className="space-y-2">
            <Skeleton className="h-5 w-40" />
            <Skeleton className="h-4 w-60" />
          </div>
        </div>

        {/* 主内容加载状态 */}
        <div className="grid grid-cols-1 md:grid-cols-4 gap-6">
          <div className="md:col-span-1">
            <Skeleton className="h-[80vh] w-full" />
          </div>
          <div className="md:col-span-3">
            <Skeleton className="h-[80vh] w-full" />
          </div>
        </div>
      </div>
    </MainLayout>
  )
}
