/**
 * 迁移自: app/api-management/explore/page.tsx
 * 迁移时间: 2025-07-13T16:10:00.000Z
 *
 * 迁移说明:
 * - 已移除  指令
 * - 已替换 next/navigation useRouter 为 react-router-dom useNavigate
 * - 保持所有样式、API调用、业务逻辑完全不变
 *
 * 注意: 本文件保持与原始文件100%功能一致性
 */

import { useState } from 'react'
import { useNavigate } from 'react-router-dom'
import { MainLayout } from '@/components/layout/main-layout'
import { Button } from '@/components/ui/button'
import {
  Card,
  CardContent,
  CardDescription,
  CardHeader,
  CardTitle,
} from '@/components/ui/card'
import { Tabs, TabsContent, TabsList, TabsTrigger } from '@/components/ui/tabs'
import { Input } from '@/components/ui/input'
import {
  Search,
  ArrowLeft,
  ChevronDown,
  ChevronRight,
  Code,
  Send,
  Download,
  Copy,
  Check,
} from 'lucide-react'
import { Badge } from '@/components/ui/badge'

export default function ExploreApiPage() {
  const navigate = useNavigate()
  const [searchTerm, setSearchTerm] = useState('')
  const [expandedEndpoint, setExpandedEndpoint] = useState(null)
  const [activeTab, setActiveTab] = useState('docs')
  const [copied, setCopied] = useState(false)

  // 模拟API分类
  const apiCategories = [
    { id: 'device', name: '设备管理', count: 12 },
    { id: 'data', name: '数据管理', count: 8 },
    { id: 'workflow', name: '工作流', count: 15 },
    { id: 'system', name: '系统管理', count: 10 },
    { id: 'analytics', name: '数据分析', count: 6 },
    { id: 'monitoring', name: '监控告警', count: 9 },
  ]

  // 模拟API端点
  const apiEndpoints = [
    {
      id: 1,
      method: 'GET',
      path: '/api/devices',
      summary: '获取设备列表',
      description: '获取系统中所有设备的列表，支持分页和筛选',
      category: 'device',
      parameters: [
        {
          name: 'page',
          in: 'query',
          type: 'integer',
          description: '页码，默认为1',
        },
        {
          name: 'pageSize',
          in: 'query',
          type: 'integer',
          description: '每页数量，默认为20',
        },
        {
          name: 'status',
          in: 'query',
          type: 'string',
          description: '设备状态筛选',
        },
      ],
      responses: {
        200: {
          description: '成功',
          schema: {
            type: 'object',
            properties: {
              items: {
                type: 'array',
                items: {
                  type: 'object',
                  properties: { id: { type: 'string' } },
                },
              },
              total: { type: 'integer' },
              page: { type: 'integer' },
              pageSize: { type: 'integer' },
            },
          },
        },
        400: { description: '请求参数错误' },
        401: { description: '未授权' },
      },
    },
    {
      id: 2,
      method: 'GET',
      path: '/api/devices/{id}',
      summary: '获取设备详情',
      description: '根据设备ID获取设备的详细信息',
      category: 'device',
      parameters: [
        {
          name: 'id',
          in: 'path',
          type: 'string',
          required: true,
          description: '设备ID',
        },
      ],
      responses: {
        200: { description: '成功' },
        404: { description: '设备不存在' },
        401: { description: '未授权' },
      },
    },
    {
      id: 3,
      method: 'POST',
      path: '/api/devices',
      summary: '创建设备',
      description: '创建新的设备',
      category: 'device',
      parameters: [
        {
          name: 'body',
          in: 'body',
          description: '设备信息',
          schema: {
            type: 'object',
            required: ['name', 'type'],
            properties: {
              name: { type: 'string' },
              type: { type: 'string' },
              location: { type: 'string' },
            },
          },
        },
      ],
      responses: {
        201: { description: '创建成功' },
        400: { description: '请求参数错误' },
        401: { description: '未授权' },
      },
    },
    {
      id: 4,
      method: 'GET',
      path: '/api/data/collection',
      summary: '获取数据采集任务',
      description: '获取所有数据采集任务的列表',
      category: 'data',
      parameters: [],
      responses: {
        200: { description: '成功' },
        401: { description: '未授权' },
      },
    },
    {
      id: 5,
      method: 'GET',
      path: '/api/workflows',
      summary: '获取工作流列表',
      description: '获取所有工作流的列表',
      category: 'workflow',
      parameters: [],
      responses: {
        200: { description: '成功' },
        401: { description: '未授权' },
      },
    },
  ]

  // 根据搜索和分类筛选API端点
  const filteredEndpoints = apiEndpoints.filter(
    (endpoint) =>
      (searchTerm === '' ||
        endpoint.path.toLowerCase().includes(searchTerm.toLowerCase()) ||
        endpoint.summary.toLowerCase().includes(searchTerm.toLowerCase())) &&
      (activeTab === 'all' || endpoint.category === activeTab)
  )

  // 处理复制URL
  const handleCopyUrl = (url) => {
    navigator.clipboard.writeText(url)
    setCopied(true)
    setTimeout(() => setCopied(false), 2000)
  }

  // 获取HTTP方法对应的颜色
  const getMethodColor = (method) => {
    switch (method) {
      case 'GET':
        return 'bg-blue-100 text-blue-800'
      case 'POST':
        return 'bg-green-100 text-green-800'
      case 'PUT':
        return 'bg-amber-100 text-amber-800'
      case 'DELETE':
        return 'bg-red-100 text-red-800'
      default:
        return 'bg-gray-100 text-gray-800'
    }
  }

  return (
    <MainLayout>
      <div className="container py-6">
        <div className="flex items-center mb-6">
          <Button
            variant="ghost"
            size="sm"
            className="mr-2"
            onClick={() => navigate('/api-management')}>
            <ArrowLeft className="h-4 w-4 mr-1" />
            返回
          </Button>
          <div>
            <h1 className="text-3xl font-bold tracking-tight">API浏览器</h1>
            <p className="text-muted-foreground mt-1">
              浏览和测试可用的API接口
            </p>
          </div>
        </div>

        <div className="grid grid-cols-1 md:grid-cols-4 gap-6">
          {/* 左侧分类和搜索 */}
          <div className="md:col-span-1">
            <Card>
              <CardHeader>
                <CardTitle>API分类</CardTitle>
                <CardDescription>按分类浏览API</CardDescription>
                <div className="relative mt-2">
                  <Search className="absolute left-2.5 top-2.5 h-4 w-4 text-muted-foreground" />
                  <Input
                    type="search"
                    placeholder="搜索API..."
                    className="pl-8"
                    value={searchTerm}
                    onChange={(e) => setSearchTerm(e.target.value)}
                  />
                </div>
              </CardHeader>
              <CardContent>
                <div className="space-y-1">
                  <div
                    className={`px-3 py-2 rounded-md cursor-pointer ${
                      activeTab === 'all'
                        ? 'bg-primary text-primary-foreground'
                        : 'hover:bg-muted'
                    }`}
                    onClick={() => setActiveTab('all')}>
                    所有API ({apiEndpoints.length})
                  </div>
                  {apiCategories.map((category) => (
                    <div
                      key={category.id}
                      className={`px-3 py-2 rounded-md cursor-pointer ${
                        activeTab === category.id
                          ? 'bg-primary text-primary-foreground'
                          : 'hover:bg-muted'
                      }`}
                      onClick={() => setActiveTab(category.id)}>
                      {category.name} ({category.count})
                    </div>
                  ))}
                </div>

                <div className="mt-6">
                  <Button variant="outline" className="w-full">
                    <Download className="h-4 w-4 mr-2" />
                    导出API文档
                  </Button>
                </div>
              </CardContent>
            </Card>
          </div>

          {/* 右侧API浏览器 */}
          <div className="md:col-span-3">
            <Card>
              <CardHeader>
                <CardTitle>API端点</CardTitle>
                <CardDescription>
                  {activeTab === 'all'
                    ? '所有API端点'
                    : `${
                        apiCategories.find((c) => c.id === activeTab)?.name ||
                        ''
                      } API端点`}
                </CardDescription>
              </CardHeader>
              <CardContent>
                {filteredEndpoints.length === 0 ? (
                  <div className="text-center py-8">
                    <div className="text-muted-foreground">
                      未找到匹配的API端点
                    </div>
                  </div>
                ) : (
                  <div className="space-y-4">
                    {filteredEndpoints.map((endpoint) => (
                      <div
                        key={endpoint.id}
                        className="border rounded-md overflow-hidden">
                        {/* 端点标题栏 */}
                        <div
                          className="flex items-center justify-between p-4 cursor-pointer hover:bg-muted"
                          onClick={() =>
                            setExpandedEndpoint(
                              expandedEndpoint === endpoint.id
                                ? null
                                : endpoint.id
                            )
                          }>
                          <div className="flex items-center">
                            <Badge
                              className={`mr-3 ${getMethodColor(
                                endpoint.method
                              )}`}>
                              {endpoint.method}
                            </Badge>
                            <span className="font-mono text-sm">
                              {endpoint.path}
                            </span>
                          </div>
                          <div className="flex items-center">
                            <span className="text-sm text-muted-foreground mr-2">
                              {endpoint.summary}
                            </span>
                            {expandedEndpoint === endpoint.id ? (
                              <ChevronDown className="h-5 w-5" />
                            ) : (
                              <ChevronRight className="h-5 w-5" />
                            )}
                          </div>
                        </div>

                        {/* 展开的详细内容 */}
                        {expandedEndpoint === endpoint.id && (
                          <div className="border-t p-4">
                            <Tabs
                              value={activeTab === 'try' ? 'try' : 'docs'}
                              onValueChange={setActiveTab}>
                              <TabsList className="grid w-full grid-cols-2">
                                <TabsTrigger value="docs">文档</TabsTrigger>
                                <TabsTrigger value="try">在线调试</TabsTrigger>
                              </TabsList>

                              <TabsContent value="docs" className="mt-4">
                                <div className="space-y-4">
                                  <div>
                                    <h4 className="text-sm font-medium mb-2">
                                      描述
                                    </h4>
                                    <p className="text-sm">
                                      {endpoint.description}
                                    </p>
                                  </div>

                                  {endpoint.parameters &&
                                    endpoint.parameters.length > 0 && (
                                      <div>
                                        <h4 className="text-sm font-medium mb-2">
                                          请求参数
                                        </h4>
                                        <div className="rounded-md border overflow-hidden">
                                          <div className="grid grid-cols-4 p-3 font-medium bg-muted text-sm">
                                            <div>名称</div>
                                            <div>位置</div>
                                            <div>类型</div>
                                            <div>描述</div>
                                          </div>
                                          <div className="divide-y">
                                            {endpoint.parameters.map(
                                              (param, idx) => (
                                                <div
                                                  key={idx}
                                                  className="grid grid-cols-4 p-3 text-sm">
                                                  <div className="font-mono">
                                                    {param.name}
                                                    {param.required && (
                                                      <span className="text-red-500">
                                                        *
                                                      </span>
                                                    )}
                                                  </div>
                                                  <div>{param.in}</div>
                                                  <div>
                                                    {param.type || 'object'}
                                                  </div>
                                                  <div>{param.description}</div>
                                                </div>
                                              )
                                            )}
                                          </div>
                                        </div>
                                      </div>
                                    )}

                                  {endpoint.responses && (
                                    <div>
                                      <h4 className="text-sm font-medium mb-2">
                                        响应
                                      </h4>
                                      <div className="rounded-md border overflow-hidden">
                                        <div className="grid grid-cols-2 p-3 font-medium bg-muted text-sm">
                                          <div>状态码</div>
                                          <div>描述</div>
                                        </div>
                                        <div className="divide-y">
                                          {Object.entries(
                                            endpoint.responses
                                          ).map(([code, response]) => (
                                            <div
                                              key={code}
                                              className="grid grid-cols-2 p-3 text-sm">
                                              <div className="font-mono">
                                                {code}
                                              </div>
                                              <div>{response.description}</div>
                                            </div>
                                          ))}
                                        </div>
                                      </div>
                                    </div>
                                  )}

                                  <div className="flex items-center justify-between pt-2">
                                    <div className="flex items-center">
                                      <Button
                                        variant="outline"
                                        size="sm"
                                        className="mr-2"
                                        onClick={() =>
                                          handleCopyUrl(endpoint.path)
                                        }>
                                        {copied ? (
                                          <Check className="h-4 w-4 mr-1" />
                                        ) : (
                                          <Copy className="h-4 w-4 mr-1" />
                                        )}
                                        复制URL
                                      </Button>
                                      <Button variant="outline" size="sm">
                                        <Code className="h-4 w-4 mr-1" />
                                        示例代码
                                      </Button>
                                    </div>
                                    <Button
                                      size="sm"
                                      onClick={() => setActiveTab('try')}>
                                      在线调试
                                    </Button>
                                  </div>
                                </div>
                              </TabsContent>

                              <TabsContent value="try" className="mt-4">
                                <div className="space-y-4">
                                  <div>
                                    <h4 className="text-sm font-medium mb-2">
                                      请求URL
                                    </h4>
                                    <div className="flex items-center">
                                      <Badge
                                        className={`mr-2 ${getMethodColor(
                                          endpoint.method
                                        )}`}>
                                        {endpoint.method}
                                      </Badge>
                                      <code className="flex-1 p-2 bg-muted rounded-md font-mono text-sm">
                                        {endpoint.path}
                                      </code>
                                    </div>
                                  </div>

                                  {endpoint.parameters &&
                                    endpoint.parameters.length > 0 && (
                                      <div>
                                        <h4 className="text-sm font-medium mb-2">
                                          请求参数
                                        </h4>
                                        <div className="space-y-3">
                                          {endpoint.parameters
                                            .filter(
                                              (param) => param.in !== 'body'
                                            )
                                            .map((param, idx) => (
                                              <div
                                                key={idx}
                                                className="grid grid-cols-3 gap-4 items-center">
                                                <div className="col-span-1">
                                                  <label className="text-sm font-medium">
                                                    {param.name}
                                                    {param.required && (
                                                      <span className="text-red-500">
                                                        *
                                                      </span>
                                                    )}
                                                  </label>
                                                </div>
                                                <div className="col-span-2">
                                                  <Input
                                                    placeholder={
                                                      param.description
                                                    }
                                                  />
                                                </div>
                                              </div>
                                            ))}

                                          {endpoint.parameters.find(
                                            (param) => param.in === 'body'
                                          ) && (
                                            <div>
                                              <label className="block text-sm font-medium mb-2">
                                                请求体
                                              </label>
                                              <div className="border rounded-md p-2 bg-muted font-mono text-sm h-32 overflow-auto">
                                                {JSON.stringify(
                                                  {
                                                    // 示例请求体
                                                    example: 'data',
                                                    nested: {
                                                      property: 'value',
                                                    },
                                                  },
                                                  null,
                                                  2
                                                )}
                                              </div>
                                            </div>
                                          )}
                                        </div>
                                      </div>
                                    )}

                                  <div>
                                    <h4 className="text-sm font-medium mb-2">
                                      认证
                                    </h4>
                                    <div className="grid grid-cols-3 gap-4 items-center">
                                      <div className="col-span-1">
                                        <label className="text-sm font-medium">
                                          API密钥
                                        </label>
                                      </div>
                                      <div className="col-span-2">
                                        <Input
                                          placeholder="输入您的API密钥"
                                          type="password"
                                        />
                                      </div>
                                    </div>
                                  </div>

                                  <div className="pt-2">
                                    <Button className="w-full">
                                      <Send className="h-4 w-4 mr-2" />
                                      发送请求
                                    </Button>
                                  </div>

                                  <div>
                                    <h4 className="text-sm font-medium mb-2">
                                      响应
                                    </h4>
                                    <div className="border rounded-md p-3 bg-muted font-mono text-sm h-48 overflow-auto">
                                      <div className="flex items-center justify-between mb-2">
                                        <Badge className="bg-green-100 text-green-800">
                                          200 OK
                                        </Badge>
                                        <span className="text-xs text-muted-foreground">
                                          200ms
                                        </span>
                                      </div>
                                      <div className="text-xs">
                                        {JSON.stringify(
                                          {
                                            // 示例响应
                                            success: true,
                                            data: {
                                              items: [
                                                { id: '1', name: '示例数据1' },
                                                { id: '2', name: '示例数据2' },
                                              ],
                                              total: 2,
                                              page: 1,
                                              pageSize: 10,
                                            },
                                          },
                                          null,
                                          2
                                        )}
                                      </div>
                                    </div>
                                  </div>
                                </div>
                              </TabsContent>
                            </Tabs>
                          </div>
                        )}
                      </div>
                    ))}
                  </div>
                )}
              </CardContent>
            </Card>
          </div>
        </div>
      </div>
    </MainLayout>
  )
}
