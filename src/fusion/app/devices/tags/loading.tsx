import { MainLayout } from "@/components/layout/main-layout"
import { <PERSON>, CardContent, CardHeader } from "@/components/ui/card"
import { Skeleton } from "@/components/ui/skeleton"

export default function AllTagsLoading() {
  return (
    <MainLayout>
      <div className="w-full px-6 py-6">
        <div className="flex items-center justify-between mb-6">
          <div>
            <Skeleton className="h-8 w-48 mb-2" />
            <Skeleton className="h-4 w-64" />
          </div>
          <div className="flex items-center gap-2">
            <Skeleton className="h-10 w-24" />
          </div>
        </div>

        <div className="flex items-center justify-between mb-6">
          <div className="flex items-center space-x-2">
            <Skeleton className="h-10 w-[300px]" />
            <Skeleton className="h-10 w-10" />
          </div>
          <div className="flex items-center gap-2">
            <Skeleton className="h-10 w-[180px]" />
            <Skeleton className="h-10 w-[180px]" />
          </div>
        </div>

        <Card>
          <CardHeader>
            <Skeleton className="h-6 w-32" />
          </CardHeader>
          <CardContent>
            <div className="space-y-4">
              <div className="flex gap-2 mb-4">
                <Skeleton className="h-10 w-24" />
                <Skeleton className="h-10 w-24" />
                <Skeleton className="h-10 w-24" />
                <Skeleton className="h-10 w-24" />
              </div>

              <div className="overflow-x-auto">
                <table className="w-full">
                  <thead>
                    <tr className="border-b">
                      <th className="px-4 py-3">
                        <Skeleton className="h-4 w-20" />
                      </th>
                      <th className="px-4 py-3">
                        <Skeleton className="h-4 w-20" />
                      </th>
                      <th className="px-4 py-3">
                        <Skeleton className="h-4 w-20" />
                      </th>
                      <th className="px-4 py-3">
                        <Skeleton className="h-4 w-20" />
                      </th>
                      <th className="px-4 py-3">
                        <Skeleton className="h-4 w-20" />
                      </th>
                      <th className="px-4 py-3">
                        <Skeleton className="h-4 w-20" />
                      </th>
                      <th className="px-4 py-3">
                        <Skeleton className="h-4 w-20" />
                      </th>
                    </tr>
                  </thead>
                  <tbody>
                    {Array(5)
                      .fill(0)
                      .map((_, i) => (
                        <tr key={i} className="border-b">
                          <td className="px-4 py-3">
                            <Skeleton className="h-6 w-24 mb-1" />
                            <Skeleton className="h-4 w-16" />
                          </td>
                          <td className="px-4 py-3">
                            <Skeleton className="h-6 w-32" />
                          </td>
                          <td className="px-4 py-3">
                            <Skeleton className="h-6 w-16" />
                          </td>
                          <td className="px-4 py-3">
                            <Skeleton className="h-6 w-12" />
                          </td>
                          <td className="px-4 py-3">
                            <Skeleton className="h-6 w-16" />
                          </td>
                          <td className="px-4 py-3">
                            <Skeleton className="h-6 w-24" />
                          </td>
                          <td className="px-4 py-3">
                            <div className="flex gap-1">
                              <Skeleton className="h-8 w-8" />
                              <Skeleton className="h-8 w-8" />
                              <Skeleton className="h-8 w-8" />
                            </div>
                          </td>
                        </tr>
                      ))}
                  </tbody>
                </table>
              </div>
            </div>
          </CardContent>
        </Card>
      </div>
    </MainLayout>
  )
}
