import { MainLayout } from "@/components/layout/main-layout"
import { Card, CardContent } from "@/components/ui/card"

export default function DeviceGroupsPage() {
  return (
    <MainLayout>
      <div className="w-full px-6 py-6">
        <div className="mb-6">
          <h1 className="text-2xl font-bold">设备分组</h1>
          <p className="text-gray-500 text-sm">管理设备分组和组织结构</p>
        </div>

        <Card>
          <CardContent className="p-6 flex items-center justify-center min-h-[400px]">
            <div className="text-center">
              <h3 className="text-lg font-medium mb-2">设备分组功能开发中</h3>
              <p className="text-gray-500">此功能将在后续版本中推出</p>
            </div>
          </CardContent>
        </Card>
      </div>
    </MainLayout>
  )
}
