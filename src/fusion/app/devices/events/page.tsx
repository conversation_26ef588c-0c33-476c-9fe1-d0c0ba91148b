import { useState } from 'react'
import { MainLayout } from '@/components/layout/main-layout'
import { Button } from '@/components/ui/button'
import { Input } from '@/components/ui/input'
import {
  Card,
  CardContent,
  CardHeader,
  CardTitle,
  CardDescription,
} from '@/components/ui/card'
import { Badge } from '@/components/ui/badge'
import {
  Select,
  SelectContent,
  SelectItem,
  SelectTrigger,
  SelectValue,
} from '@/components/ui/select'
import {
  DropdownMenu,
  DropdownMenuContent,
  DropdownMenuItem,
  DropdownMenuTrigger,
} from '@/components/ui/dropdown-menu'
import {
  Filter,
  Search,
  MoreVertical,
  Clock,
  Power,
  RefreshCw,
  Settings,
  Download,
} from 'lucide-react'
import {
  Dialog,
  DialogContent,
  DialogDescription,
  DialogFooter,
  DialogHeader,
  DialogTitle,
} from '@/components/ui/dialog'
import { Checkbox } from '@/components/ui/checkbox'
import { Separator } from '@/components/ui/separator'

export default function DeviceEventsPage() {
  const [searchQuery, setSearchQuery] = useState('')
  const [showSettingsDialog, setShowSettingsDialog] = useState(false)

  // 模拟设备事件数据
  const events = [
    {
      id: 'event-001',
      deviceId: 'dev-001',
      deviceName: '温度传感器 A1',
      type: 'status',
      message: '设备上线',
      timestamp: '2025-04-04 08:15:22',
      details: '设备成功连接到网络',
      severity: 'info',
    },
    {
      id: 'event-002',
      deviceId: 'dev-002',
      deviceName: '湿度传感器 B2',
      type: 'config',
      message: '配置更新',
      timestamp: '2025-04-04 09:30:45',
      details: '采样频率从 60s 更新为 30s',
      severity: 'info',
    },
    {
      id: 'event-003',
      deviceId: 'dev-003',
      deviceName: '压力传感器 C3',
      type: 'data',
      message: '数据异常',
      timestamp: '2025-04-04 10:22:18',
      details: '数据波动超出正常范围',
      severity: 'warning',
    },
    {
      id: 'event-004',
      deviceId: 'dev-004',
      deviceName: '流量传感器 D4',
      type: 'status',
      message: '设备离线',
      timestamp: '2025-04-04 11:05:33',
      details: '设备连接中断',
      severity: 'error',
    },
    {
      id: 'event-005',
      deviceId: 'dev-001',
      deviceName: '温度传感器 A1',
      type: 'maintenance',
      message: '固件更新',
      timestamp: '2025-04-04 12:45:10',
      details: '固件版本从 v2.1.0 更新到 v2.3.1',
      severity: 'info',
    },
    {
      id: 'event-006',
      deviceId: 'dev-005',
      deviceName: '光照传感器 E5',
      type: 'status',
      message: '设备重启',
      timestamp: '2025-04-04 13:20:05',
      details: '设备自动重启完成',
      severity: 'info',
    },
    {
      id: 'event-007',
      deviceId: 'dev-002',
      deviceName: '湿度传感器 B2',
      type: 'data',
      message: '数据上传',
      timestamp: '2025-04-04 14:10:30',
      details: '成功上传 1.2MB 历史数据',
      severity: 'info',
    },
  ]

  const getEventSeverityBadge = (severity) => {
    switch (severity) {
      case 'error':
        return (
          <Badge className="bg-red-100 text-red-800 hover:bg-red-100">
            错误
          </Badge>
        )
      case 'warning':
        return (
          <Badge className="bg-amber-100 text-amber-800 hover:bg-amber-100">
            警告
          </Badge>
        )
      case 'info':
        return (
          <Badge className="bg-blue-100 text-blue-800 hover:bg-blue-100">
            信息
          </Badge>
        )
      default:
        return <Badge variant="outline">{severity}</Badge>
    }
  }

  const getEventIcon = (type) => {
    switch (type) {
      case 'status':
        return <Power className="h-5 w-5 text-blue-500" />
      case 'config':
        return <Settings className="h-5 w-5 text-purple-500" />
      case 'data':
        return <RefreshCw className="h-5 w-5 text-green-500" />
      case 'maintenance':
        return <RefreshCw className="h-5 w-5 text-amber-500" />
      default:
        return <Clock className="h-5 w-5 text-gray-500" />
    }
  }

  const getEventTypeLabel = (type) => {
    switch (type) {
      case 'status':
        return '状态变更'
      case 'config':
        return '配置变更'
      case 'data':
        return '数据事件'
      case 'maintenance':
        return '维护事件'
      default:
        return type
    }
  }

  return (
    <MainLayout>
      <div className="w-full px-6 py-6">
        <div className="flex justify-between items-center mb-4">
          <div>
            <h1 className="text-2xl font-bold">设备事件</h1>
            <p className="text-gray-500 text-sm">查看设备活动和事件历史</p>
          </div>
          <div className="flex gap-2">
            <Button variant="outline">
              <Download className="mr-2 h-4 w-4" /> 导出事件
            </Button>
            <Button
              className="bg-black text-white hover:bg-gray-800"
              onClick={() => setShowSettingsDialog(true)}>
              <Settings className="mr-2 h-4 w-4" /> 事件设置
            </Button>
          </div>
        </div>

        <div className="flex items-center gap-4 mb-6">
          <div className="relative flex-1 max-w-md">
            <Search className="absolute left-3 top-1/2 transform -translate-y-1/2 h-4 w-4 text-gray-400" />
            <Input
              placeholder="搜索事件..."
              className="pl-10"
              value={searchQuery}
              onChange={(e) => setSearchQuery(e.target.value)}
            />
          </div>
          <Button variant="outline" size="icon">
            <Filter className="h-4 w-4" />
          </Button>
          <Select defaultValue="all">
            <SelectTrigger className="w-[160px]">
              <SelectValue placeholder="所有类型" />
            </SelectTrigger>
            <SelectContent>
              <SelectItem value="all">所有类型</SelectItem>
              <SelectItem value="status">状态变更</SelectItem>
              <SelectItem value="config">配置变更</SelectItem>
              <SelectItem value="data">数据事件</SelectItem>
              <SelectItem value="maintenance">维护事件</SelectItem>
            </SelectContent>
          </Select>
          <Select defaultValue="all">
            <SelectTrigger className="w-[160px]">
              <SelectValue placeholder="所有级别" />
            </SelectTrigger>
            <SelectContent>
              <SelectItem value="all">所有级别</SelectItem>
              <SelectItem value="error">错误</SelectItem>
              <SelectItem value="warning">警告</SelectItem>
              <SelectItem value="info">信息</SelectItem>
            </SelectContent>
          </Select>
        </div>

        <div className="grid gap-4 md:grid-cols-2 lg:grid-cols-4 mb-6">
          <Card>
            <CardContent className="p-4 flex flex-col">
              <div className="text-sm text-gray-500 mb-1">今日事件总数</div>
              <div className="text-2xl font-bold">42</div>
              <div className="text-xs text-gray-500">较昨日 +8</div>
            </CardContent>
          </Card>
          <Card>
            <CardContent className="p-4 flex flex-col">
              <div className="text-sm text-gray-500 mb-1">状态变更</div>
              <div className="text-2xl font-bold text-blue-600">15</div>
              <div className="text-xs text-gray-500">设备上下线、重启等</div>
            </CardContent>
          </Card>
          <Card>
            <CardContent className="p-4 flex flex-col">
              <div className="text-sm text-gray-500 mb-1">数据事件</div>
              <div className="text-2xl font-bold text-green-600">18</div>
              <div className="text-xs text-gray-500">数据上传、异常等</div>
            </CardContent>
          </Card>
          <Card>
            <CardContent className="p-4 flex flex-col">
              <div className="text-sm text-gray-500 mb-1">配置变更</div>
              <div className="text-2xl font-bold text-purple-600">9</div>
              <div className="text-xs text-gray-500">参数调整、固件更新等</div>
            </CardContent>
          </Card>
        </div>

        <Card>
          <CardHeader>
            <CardTitle>事件列表</CardTitle>
            <CardDescription>设备活动和事件历史记录</CardDescription>
          </CardHeader>
          <CardContent>
            <div className="overflow-x-auto">
              <table className="w-full">
                <thead>
                  <tr className="bg-gray-50 text-left text-sm text-gray-500 border-b">
                    <th className="px-6 py-3 font-medium">事件ID</th>
                    <th className="px-6 py-3 font-medium">设备</th>
                    <th className="px-6 py-3 font-medium">类型</th>
                    <th className="px-6 py-3 font-medium">事件内容</th>
                    <th className="px-6 py-3 font-medium">级别</th>
                    <th className="px-6 py-3 font-medium">时间</th>
                    <th className="px-6 py-3 font-medium">操作</th>
                  </tr>
                </thead>
                <tbody>
                  {events.map((event) => (
                    <tr key={event.id} className="border-b hover:bg-gray-50">
                      <td className="px-6 py-4 font-medium">{event.id}</td>
                      <td className="px-6 py-4">{event.deviceName}</td>
                      <td className="px-6 py-4">
                        <div className="flex items-center gap-2">
                          {getEventIcon(event.type)}
                          <span>{getEventTypeLabel(event.type)}</span>
                        </div>
                      </td>
                      <td className="px-6 py-4">
                        <div>
                          <div className="font-medium">{event.message}</div>
                          <div className="text-xs text-gray-500">
                            {event.details}
                          </div>
                        </div>
                      </td>
                      <td className="px-6 py-4">
                        {getEventSeverityBadge(event.severity)}
                      </td>
                      <td className="px-6 py-4">
                        <div className="flex items-center gap-1">
                          <Clock className="h-4 w-4 text-gray-400" />
                          <span>{event.timestamp}</span>
                        </div>
                      </td>
                      <td className="px-6 py-4">
                        <DropdownMenu>
                          <DropdownMenuTrigger asChild>
                            <Button variant="ghost" size="icon">
                              <MoreVertical className="h-4 w-4" />
                            </Button>
                          </DropdownMenuTrigger>
                          <DropdownMenuContent align="end">
                            <DropdownMenuItem>查看详情</DropdownMenuItem>
                            <DropdownMenuItem>查看设备</DropdownMenuItem>
                            <DropdownMenuItem>导出事件</DropdownMenuItem>
                          </DropdownMenuContent>
                        </DropdownMenu>
                      </td>
                    </tr>
                  ))}
                </tbody>
              </table>
            </div>
          </CardContent>
        </Card>
      </div>
      <Dialog open={showSettingsDialog} onOpenChange={setShowSettingsDialog}>
        <DialogContent className="sm:max-w-[500px]">
          <DialogHeader>
            <DialogTitle>事件设置</DialogTitle>
            <DialogDescription>配置事件记录和通知的相关设置</DialogDescription>
          </DialogHeader>

          <div className="py-4 space-y-4">
            <div className="space-y-4">
              <h4 className="text-sm font-medium">事件记录设置</h4>
              <Separator />
              <div className="flex items-center space-x-2">
                <Checkbox id="record-status" defaultChecked />
                <label
                  htmlFor="record-status"
                  className="text-sm font-medium leading-none peer-disabled:cursor-not-allowed peer-disabled:opacity-70">
                  记录状态变更事件
                </label>
              </div>
              <div className="flex items-center space-x-2">
                <Checkbox id="record-config" defaultChecked />
                <label
                  htmlFor="record-config"
                  className="text-sm font-medium leading-none peer-disabled:cursor-not-allowed peer-disabled:opacity-70">
                  记录配置变更事件
                </label>
              </div>
              <div className="flex items-center space-x-2">
                <Checkbox id="record-data" defaultChecked />
                <label
                  htmlFor="record-data"
                  className="text-sm font-medium leading-none peer-disabled:cursor-not-allowed peer-disabled:opacity-70">
                  记录数据事件
                </label>
              </div>
              <div className="flex items-center space-x-2">
                <Checkbox id="record-maintenance" defaultChecked />
                <label
                  htmlFor="record-maintenance"
                  className="text-sm font-medium leading-none peer-disabled:cursor-not-allowed peer-disabled:opacity-70">
                  记录维护事件
                </label>
              </div>
            </div>

            <div className="space-y-4">
              <h4 className="text-sm font-medium">事件保留设置</h4>
              <Separator />
              <div className="grid grid-cols-2 gap-4">
                <div className="space-y-2">
                  <label
                    htmlFor="retention-days"
                    className="text-sm font-medium">
                    保留天数
                  </label>
                  <Select defaultValue="30">
                    <SelectTrigger id="retention-days">
                      <SelectValue placeholder="选择天数" />
                    </SelectTrigger>
                    <SelectContent>
                      <SelectItem value="7">7天</SelectItem>
                      <SelectItem value="14">14天</SelectItem>
                      <SelectItem value="30">30天</SelectItem>
                      <SelectItem value="60">60天</SelectItem>
                      <SelectItem value="90">90天</SelectItem>
                    </SelectContent>
                  </Select>
                </div>
                <div className="space-y-2">
                  <label htmlFor="max-events" className="text-sm font-medium">
                    最大事件数
                  </label>
                  <Select defaultValue="1000">
                    <SelectTrigger id="max-events">
                      <SelectValue placeholder="选择数量" />
                    </SelectTrigger>
                    <SelectContent>
                      <SelectItem value="500">500条</SelectItem>
                      <SelectItem value="1000">1000条</SelectItem>
                      <SelectItem value="5000">5000条</SelectItem>
                      <SelectItem value="10000">10000条</SelectItem>
                    </SelectContent>
                  </Select>
                </div>
              </div>
            </div>

            <div className="space-y-4">
              <h4 className="text-sm font-medium">通知设置</h4>
              <Separator />
              <div className="flex items-center space-x-2">
                <Checkbox id="notify-error" defaultChecked />
                <label
                  htmlFor="notify-error"
                  className="text-sm font-medium leading-none peer-disabled:cursor-not-allowed peer-disabled:opacity-70">
                  错误级别事件通知
                </label>
              </div>
              <div className="flex items-center space-x-2">
                <Checkbox id="notify-warning" defaultChecked />
                <label
                  htmlFor="notify-warning"
                  className="text-sm font-medium leading-none peer-disabled:cursor-not-allowed peer-disabled:opacity-70">
                  警告级别事件通知
                </label>
              </div>
              <div className="flex items-center space-x-2">
                <Checkbox id="notify-info" />
                <label
                  htmlFor="notify-info"
                  className="text-sm font-medium leading-none peer-disabled:cursor-not-allowed peer-disabled:opacity-70">
                  信息级别事件通知
                </label>
              </div>
            </div>
          </div>

          <DialogFooter>
            <Button
              variant="outline"
              onClick={() => setShowSettingsDialog(false)}>
              取消
            </Button>
            <Button onClick={() => setShowSettingsDialog(false)}>
              保存设置
            </Button>
          </DialogFooter>
        </DialogContent>
      </Dialog>
    </MainLayout>
  )
}
