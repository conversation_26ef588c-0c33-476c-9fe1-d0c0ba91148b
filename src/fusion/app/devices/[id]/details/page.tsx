import {
  Dialog,
  DialogContent,
  DialogHeader,
  DialogTitle,
  DialogDescription,
} from '@/components/ui/dialog'
// 首先，在文件顶部添加必要的导入
import { Plus } from 'lucide-react'
import { useState, useEffect, useCallback, useMemo, useRef } from 'react'
import { useParams, useNavigate } from 'react-router-dom'
import { Link } from 'react-router-dom'
import * as XLSX from 'xlsx'
import { MainLayout } from '@/components/layout/main-layout'
import { Button } from '@/components/ui/button'
import { Tabs, TabsContent, TabsList, TabsTrigger } from '@/components/ui/tabs'
import { Card, CardContent, CardHeader, CardTitle } from '@/components/ui/card'
import { Badge } from '@/components/ui/badge'
import {
  ChevronLeft,
  Edit,
  RefreshCw,
  Power,
  Upload,
  PenSquare,
  FolderPlus,
  Bell,
  Gauge,
  FileCode,
  FileSpreadsheet,
  Download,
  Wand2,
} from 'lucide-react'
import { DeviceService, type Device } from '@/lib/api/device-api'
import { TagService, type DeviceTag } from '@/lib/api/tag-api'
import { toast } from '@/components/ui/use-toast'
import { TagList } from '@/components/devices/tag-list'
import { TagValueWriter } from '@/components/devices/tag-value-writer'
import ExcelImport from '@/components/common/excel-import'
import { TagGroupManager } from '@/components/devices/tag-group-manager'
import { TagAlarmConfig } from '@/components/devices/tag-alarm-config'
import { DeviceDashboard } from '@/components/devices/device-dashboard'
import { FirmwareUpdate } from '@/components/devices/firmware-update'
import { DeviceDiagnostics } from '@/components/devices/device-diagnostics'
import { DeviceTemplateManager } from '@/components/devices/device-template-manager'
import { AddDevicePointDialog } from '@/components/devices/add-device-point-dialog'
// 使用正确的脚本编辑器组件
import ScriptEditorDialog from '@/components/ui/script-editor-dialog'
// 导入批量编辑对话框组件
import { BatchTagDialog } from '@/components/devices/batch-tag-dialog'
// 导入历史数据查看器组件
import { DeviceHistoryDataViewer } from '@/components/devices/history-data/DeviceHistoryDataViewer'
// 导入AI辅助批量点位创建组件
// 迁移注释：从Next.js迁移到Vite+React，集成AI辅助批量点位创建功能
import { AIBatchLabelCreator } from '@/components/devices/ai-batch-label-creator'

// 修改接口名称，避免命名冲突
interface PageTag extends DeviceTag {
  value: any // 确保value是必选属性
  address: string // 确保address是必选属性
  accessRight?: string
  uploadMethod?: string
  decimalPlaces?: number
  min?: number
  max?: number
}

// 添加扩展的DeviceTag接口，包含协议特定字段
interface ExtendedTag extends DeviceTag {
  functionCode?: string | number
  registerType?: string
  registerAddress?: string
  driverDataType?: string
  protocolDataType?: string // 添加协议数据类型字段
  tags?: string[]
}

// 扩展Device类型以匹配API返回的数据结构
type ExtendedDevice = Device & {
  identifier?: string
  channelId?: number
  driverName?: string
  isConnect?: boolean
  status?: string
  allowWrite?: boolean
  enabled?: boolean
  createTime?: string
  updateTime?: string
  deviceInfo?: {
    minPeriod?: number
    waitTime?: number
    reConnTime?: number
    reportType?: string
    storeHistoryData?: boolean
  }
}

// 转换函数：将DeviceTag转换为页面所需的PageTag类型
function convertToPageTag(tag: DeviceTag): PageTag {
  return {
    ...tag,
    value: tag.value ?? null, // 确保value有值
    address: tag.address ?? '', // 确保address有值
    accessRight:
      tag.readWrite === 'R' ? '只读' : tag.readWrite === 'W' ? '只写' : '读写',
  }
}

// 转换DeviceTag为历史数据组件所需的AvailableTag格式
function convertToAvailableTag(tag: DeviceTag): {
  id: string
  name: string
  identifier: string
  displayName: string
  dataType: string
  unit?: string
} {
  return {
    id: tag.id.toString(),
    name: tag.name,
    identifier: tag.identifier,
    displayName: tag.alias || tag.name,
    dataType: tag.dataType,
    unit: tag.unit,
  }
}

// 添加防抖函数，增加时间至500ms
function debounce<T extends (...args: any[]) => any>(
  func: T,
  wait: number
): (...args: Parameters<T>) => void {
  let timeout: NodeJS.Timeout | null = null

  return function (...args: Parameters<T>) {
    if (timeout) clearTimeout(timeout)
    timeout = setTimeout(() => func(...args), wait)
  }
}

// 添加上报方式转换函数
const getReportTypeInChinese = (reportType?: string) => {
  switch (reportType) {
    case 'Always':
      return '始终上报'
    case 'OnChange':
      return '变化上报'
    case 'Periodic':
      return '周期上报'
    case 'OnDemand':
      return '按需上报'
    default:
      return reportType || '-'
  }
}

// 获取状态徽章
const getStatusBadge = (status?: string) => {
  switch (status) {
    case 'Good':
      return <Badge className="bg-green-100 text-green-800">正常</Badge>
    case 'Warning':
      return <Badge className="bg-amber-100 text-amber-800">警告</Badge>
    case 'Bad':
      return <Badge className="bg-red-100 text-red-800">异常</Badge>
    case 'online':
      return <Badge className="bg-green-100 text-green-800">在线</Badge>
    case 'warning':
      return <Badge className="bg-amber-100 text-amber-800">警告</Badge>
    case 'offline':
      return <Badge className="bg-red-100 text-red-800">离线</Badge>
    default:
      return <Badge variant="outline">未知</Badge>
  }
}

// 获取连接状态徽章，使其更加明显
const getConnectionBadge = (isConnected?: boolean) => {
  if (isConnected) {
    return (
      <Badge className="bg-green-100 text-green-800 px-3 py-1 text-sm">
        已连接
      </Badge>
    )
  } else {
    return (
      <Badge className="bg-red-100 text-red-800 px-3 py-1 text-sm">
        未连接
      </Badge>
    )
  }
}

// 将主要内容移到单独的组件中
function DeviceDetailContent() {
  const params = useParams()
  const navigate = useNavigate()
  const deviceId = params.id as string

  // 添加请求跟踪状态，避免重复请求
  const loadingDeviceRef = useRef(false)
  const loadingTagsRef = useRef(false)
  const initialLoadCompletedRef = useRef(false)

  if (!deviceId || deviceId === 'NaN' || deviceId === 'undefined') {
    console.warn('无效deviceId:', deviceId, 'params:', params)
    return (
      <MainLayout>
        <div className="flex justify-center items-center py-12 bg-white rounded-lg border">
          <div className="text-center">
            <p className="text-gray-500 mb-4">设备不存在或已被删除</p>
            <Button variant="outline" asChild>
              <Link to="/devices">返回设备列表</Link>
            </Button>
          </div>
        </div>
      </MainLayout>
    )
  }

  const [device, setDevice] = useState<ExtendedDevice | null>(null)
  const [tags, setTags] = useState<DeviceTag[]>([])
  const [loading, setLoading] = useState(true)
  const [tagsLoading, setTagsLoading] = useState(true)
  const [refreshTrigger, setRefreshTrigger] = useState(0)

  // 对话框状态
  const [showWriteDialog, setShowWriteDialog] = useState(false)
  const [showImportExportDialog, setShowImportExportDialog] = useState(false)
  const [showGroupManagerDialog, setShowGroupManagerDialog] = useState(false)
  const [showAlarmConfigDialog, setShowAlarmConfigDialog] = useState(false)
  const [showFirmwareUpdateDialog, setShowFirmwareUpdateDialog] =
    useState(false)
  const [showDiagnosticsDialog, setShowDiagnosticsDialog] = useState(false)
  const [showTemplateManagerDialog, setShowTemplateManagerDialog] =
    useState(false)
  const [showExportDialog, setShowExportDialog] = useState(false)
  const [showAddPointDialog, setShowAddPointDialog] = useState(false)
  const [selectedTag, setSelectedTag] = useState<DeviceTag | null>(null)

  // AI辅助批量点位创建对话框状态
  // 迁移注释：从Next.js迁移到Vite+React，添加AI批量创建状态管理
  const [showAIBatchCreateDialog, setShowAIBatchCreateDialog] = useState(false)

  // 批量编辑对话框状态
  const [showBatchEditDialog, setShowBatchEditDialog] = useState(false)
  const [selectedTagsForBatchEdit, setSelectedTagsForBatchEdit] = useState<
    DeviceTag[]
  >([])

  const [showScriptEditorDialog, setShowScriptEditorDialog] = useState(false)
  const [scriptContent, setScriptContent] = useState('')

  // 新增点位状态管理
  const [newPointFormState, setNewPointFormState] = useState({
    name: '',
    displayName: '',
    dataSource: 'device',
    dataType: 'Number',
    decimalPlaces: 2,
    unit: '',
    description: '',
    uploadMethod: '总是上报',
    uploadInterval: 60,
    collectionInterval: 0,
    deadband: 0,
    min: '',
    max: '',
    accessRight: '只读',
    address: '',
    functionCode: '3',
    registerType: 'HoldingRegister',
    registerAddress: 0,
    driverDataType: 'Float',
    tags: [] as string[],
    processingScript: '',
    protocolDataType: '', // 添加缺少的协议数据类型字段
  })

  // 新增点位脚本编辑器内容
  const [newPointScriptContent, setNewPointScriptContent] = useState('')

  // 新增点位表单验证
  const [formErrors, setFormErrors] = useState<Record<string, string>>({})
  const [isSubmitting, setIsSubmitting] = useState(false)

  // 加载设备详情 - 优化防抖
  const debouncedLoadDevice = useCallback(
    debounce(async () => {
      // 检查是否正在加载，避免重复请求
      if (loadingDeviceRef.current) return
      if (!deviceId || deviceId === 'NaN' || deviceId === 'undefined') return

      try {
        loadingDeviceRef.current = true
        setLoading(true)

        // 使用DeviceService获取真实设备数据
        const response = await DeviceService.getDevice(deviceId)
        if (response.code === 200 && response.data) {
          // 处理API返回的数据结构，确保类型兼容
          const deviceData = response.data
          setDevice(deviceData as ExtendedDevice)
        } else {
          console.error('加载设备失败:', response.msg, 'deviceId:', deviceId)
          toast({
            title: '加载失败',
            description: '无法加载设备详情: ' + response.msg,
            variant: 'destructive',
          })
        }
      } catch (error) {
        console.error('加载设备失败:', error, 'deviceId:', deviceId)
        toast({
          title: '加载失败',
          description: '无法加载设备详情',
          variant: 'destructive',
        })
      } finally {
        setLoading(false)
        loadingDeviceRef.current = false
      }
    }, 500), // 增加防抖时间至500ms
    [deviceId]
  )

  // 加载点位列表 - 优化防抖
  const debouncedLoadTags = useCallback(
    debounce(async () => {
      // 检查是否正在加载，避免重复请求
      if (loadingTagsRef.current) return
      if (!deviceId || deviceId === 'NaN' || deviceId === 'undefined') return

      try {
        loadingTagsRef.current = true
        setTagsLoading(true)

        // 使用TagService获取真实点位数据
        const tagsData = await TagService.getTagsByDeviceId(deviceId)

        // 同时更新状态和引用存储
        setTags(tagsData)
      } catch (error) {
        console.error('加载点位失败:', error, 'deviceId:', deviceId)
        toast({
          title: '加载失败',
          description: '无法加载设备点位',
          variant: 'destructive',
        })
      } finally {
        setTagsLoading(false)
        loadingTagsRef.current = false
      }
    }, 500), // 增加防抖时间至500ms
    [deviceId]
  )

  // 使用 useMemo 优化转换的标签数据
  const convertedTags = useMemo(
    () => {
      return tags.map(convertToPageTag) as any[]
    },
    [tags, refreshTrigger] // 添加refreshTrigger依赖项，确保在数据刷新时重新计算
  )

  // 使用 useCallback 包装处理函数
  const handleTagAdded = useCallback(() => {
    // 触发点位列表刷新
    setRefreshTrigger((prev) => prev + 1)
    debouncedLoadTags()
  }, [debouncedLoadTags])

  // 处理模板应用
  const handleTemplateApplied = useCallback(
    (templateId: number) => {
      // 在实际应用中，这里会重新加载点位数据
      debouncedLoadTags()
    },
    [debouncedLoadTags]
  )

  // 初始加载 - 优化依赖项和初始化逻辑
  useEffect(() => {
    if (
      deviceId &&
      deviceId !== 'NaN' &&
      deviceId !== 'undefined' &&
      !initialLoadCompletedRef.current
    ) {
      initialLoadCompletedRef.current = true
      debouncedLoadDevice()
      debouncedLoadTags()
    }
  }, [deviceId, debouncedLoadDevice, debouncedLoadTags])

  // 重启设备
  const handleRestartDevice = async () => {
    if (!confirm('确定要重启此设备吗？')) return

    try {
      // 在实际应用中使用: await DeviceService.restartDevice(deviceId)
      await new Promise((resolve) => setTimeout(resolve, 1500))
      toast({
        title: '重启成功',
        description: '设备重启命令已发送',
      })
    } catch (error) {
      console.error('重启设备失败:', error)
      toast({
        title: '重启失败',
        description: '无法重启设备',
        variant: 'destructive',
      })
    }
  }

  // 修改这部分实现，使Promise类型匹配
  const asyncNoop = async () => {
    /* 空的异步函数，返回Promise */
  }
  
  // 处理新点位表单字段变更
  const handleNewPointFormChange = (field: string, value: any) => {
    setNewPointFormState((prev) => ({
      ...prev,
      [field]: value,
    }))

    // 清除该字段的错误（如果有）
    if (formErrors[field]) {
      setFormErrors((prev) => {
        const newErrors = { ...prev }
        delete newErrors[field]
        return newErrors
      })
    }
  }

  // 添加处理批量编辑的回调函数
  const handleBatchEdit = useCallback((tagsToEdit: DeviceTag[]) => {
    setSelectedTagsForBatchEdit(tagsToEdit)
    setShowBatchEditDialog(true)
  }, [])

  // 处理批量编辑完成
  const handleBatchTagsUpdated = useCallback((updatedTags: DeviceTag[]) => {
    // 更新本地标签列表
    setTags((prevTags) => {
      return prevTags.map((tag) => {
        const updatedTag = updatedTags.find((t) => t.id === tag.id)
        return updatedTag || tag
      })
    })

    // 触发列表刷新以确保数据同步
    debouncedLoadTags()

    toast({
      title: '批量更新成功',
      description: `已成功更新 ${updatedTags.length} 个点位`,
    })
  }, [debouncedLoadTags])

  // 处理点位删除后的回调
  const handleTagDeleted = useCallback(() => {
    // 刷新点位列表
    debouncedLoadTags()
  }, [debouncedLoadTags])

  // 处理AI批量创建点位结果
  // 迁移注释：从Next.js迁移到Vite+React，添加AI批量创建结果处理逻辑
  const handleAILabelsCreated = useCallback((labels: any[]) => {
    // 显示成功提示
    toast({
      title: '批量创建成功',
      description: `成功创建 ${labels.length} 个点位，正在刷新列表...`,
    })

    // 刷新点位列表
    debouncedLoadTags()

    // 关闭对话框
    setShowAIBatchCreateDialog(false)
  }, [debouncedLoadTags])

  // 处理复制点位到新增
  const handleCopyTag = (tagToCopy: DeviceTag) => {
    // 使用类型断言告诉TypeScript编译器这可能是一个扩展标签
    const tagWithExtras = tagToCopy as ExtendedTag

    // 将点位数据转换为新增点位对话框所需的格式，确保结构与newPointFormState一致
    const pointFormData = {
      name: tagWithExtras.name + '_copy', // 添加_copy后缀以区分
      displayName: (tagWithExtras.alias || tagWithExtras.name) + '_copy',
      dataSource: tagWithExtras.dataSourceType || 'device',
      dataType: tagWithExtras.dataType || 'Number',
      decimalPlaces:
        tagWithExtras.decimalPlaces !== undefined
          ? tagWithExtras.decimalPlaces
          : 2,
      unit: tagWithExtras.unit || '',
      description: tagWithExtras.description || '',
      uploadMethod: tagWithExtras.uploadMethod || '变化上报',
      uploadInterval: 0,
      collectionInterval: 1000,
      deadband: 0,
      min: tagWithExtras.min !== undefined ? String(tagWithExtras.min) : '',
      max: tagWithExtras.max !== undefined ? String(tagWithExtras.max) : '',
      accessRight:
        tagWithExtras.readWrite === 'R'
          ? '只读'
          : tagWithExtras.readWrite === 'W'
          ? '只写'
          : '读写',
      address: tagWithExtras.address || '',
      // 处理协议特定字段
      functionCode: tagWithExtras.functionCode
        ? String(tagWithExtras.functionCode)
        : '3',
      registerType: tagWithExtras.registerType || 'HoldingRegister',
      registerAddress:
        tagWithExtras.registerAddress !== undefined
          ? Number(tagWithExtras.registerAddress)
          : 0,
      driverDataType: tagWithExtras.driverDataType || 'Float',
      protocolDataType: tagWithExtras.protocolDataType || '', // 使用扩展接口中的字段
      processingScript: tagWithExtras.processingScript || '',
      tags: Array.isArray(tagWithExtras.tags) ? tagWithExtras.tags : [],
    }

    // 设置新点位表单状态
    setNewPointFormState(pointFormData)

    // 打开新增点位对话框
    setShowAddPointDialog(true)
  }

  return (
    <div className="w-full px-6 py-6">
      <div className="flex items-center mb-6">
        <Button variant="ghost" asChild className="mr-4">
          <Link to="/devices">
            <ChevronLeft className="mr-2 h-4 w-4" />
            返回设备列表
          </Link>
        </Button>
        <div className="flex-1">
          {loading ? (
            <div className="h-8 w-48 bg-gray-200 animate-pulse rounded"></div>
          ) : (
            <h1 className="text-2xl font-bold">{device?.name}</h1>
          )}
        </div>
        <div className="flex items-center gap-2">
          <Button
            variant="outline"
            onClick={() => {
              // 仅在未加载状态下触发刷新
              if (!loading && !tagsLoading) {
                debouncedLoadDevice()
                debouncedLoadTags()
              }
            }}
            disabled={loading || tagsLoading}>
            <RefreshCw
              className={`mr-2 h-4 w-4 ${
                loading || tagsLoading ? 'animate-spin' : ''
              }`}
            />
            刷新
          </Button>
          <Button
            variant="outline"
            onClick={() => navigate(`/devices/edit/${deviceId}`)}>
            <Edit className="mr-2 h-4 w-4" />
            编辑
          </Button>
        </div>
      </div>

      {loading ? (
        <div className="grid gap-6 md:grid-cols-3 mb-6">
          {[1, 2, 3].map((i) => (
            <Card key={i} className="overflow-hidden">
              <CardHeader className="pb-2">
                <div className="h-5 w-24 bg-gray-200 animate-pulse rounded"></div>
              </CardHeader>
              <CardContent>
                <div className="space-y-2">
                  <div className="h-4 w-full bg-gray-200 animate-pulse rounded"></div>
                  <div className="h-4 w-3/4 bg-gray-200 animate-pulse rounded"></div>
                </div>
              </CardContent>
            </Card>
          ))}
        </div>
      ) : device ? (
        <>
          <div className="grid gap-6 md:grid-cols-4 mb-6">
            <Card className="overflow-hidden">
              <CardHeader className="pb-2">
                <CardTitle className="text-base">设备信息</CardTitle>
              </CardHeader>
              <CardContent>
                <div className="space-y-2">
                  <div className="flex justify-between">
                    <span className="text-sm text-gray-500">标识符:</span>
                    <span className="font-medium">{device.identifier}</span>
                  </div>
                  <div className="flex justify-between">
                    <span className="text-sm text-gray-500">状态:</span>
                    {getStatusBadge(device.status)}
                  </div>
                  <div className="flex justify-between">
                    <span className="text-sm text-gray-500">创建时间:</span>
                    <span className="font-medium">{device.createTime}</span>
                  </div>
                  <div className="flex justify-between">
                    <span className="text-sm text-gray-500">设备ID:</span>
                    <span className="font-medium">{device.id}</span>
                  </div>
                </div>
              </CardContent>
            </Card>

            <Card className="overflow-hidden">
              <CardHeader className="pb-2">
                <CardTitle className="text-base">连接信息</CardTitle>
              </CardHeader>
              <CardContent>
                <div className="space-y-2">
                  <div className="flex justify-between">
                    <span className="text-sm text-gray-500">驱动类型:</span>
                    <span className="font-medium">
                      {device.driverName || '-'}
                    </span>
                  </div>
                  <div className="flex justify-between">
                    <span className="text-sm text-gray-500">连接状态:</span>
                    {getConnectionBadge(device.isConnect)}
                  </div>
                  <div className="flex justify-between">
                    <span className="text-sm text-gray-500">启用状态:</span>
                    <Badge variant={device.enabled ? 'default' : 'secondary'}>
                      {device.enabled ? '已启用' : '已禁用'}
                    </Badge>
                  </div>
                  <div className="flex justify-between">
                    <span className="text-sm text-gray-500">写入权限:</span>
                    <Badge
                      variant={device.allowWrite ? 'default' : 'secondary'}>
                      {device.allowWrite ? '允许' : '禁止'}
                    </Badge>
                  </div>
                </div>
              </CardContent>
            </Card>

            <Card className="overflow-hidden">
              <CardHeader className="pb-2">
                <CardTitle className="text-base">通信参数</CardTitle>
              </CardHeader>
              <CardContent>
                <div className="space-y-2">
                  <div className="flex justify-between">
                    <span className="text-sm text-gray-500">最小周期:</span>
                    <span className="font-medium">
                      {device.deviceInfo?.minPeriod || '-'} 毫秒
                    </span>
                  </div>
                  <div className="flex justify-between">
                    <span className="text-sm text-gray-500">重连时间:</span>
                    <span className="font-medium">
                      {device.deviceInfo?.reConnTime || '-'} 毫秒
                    </span>
                  </div>
                  <div className="flex justify-between">
                    <span className="text-sm text-gray-500">上报方式:</span>
                    <span className="font-medium">
                      {getReportTypeInChinese(device.deviceInfo?.reportType)}
                    </span>
                  </div>
                  <div className="flex justify-between">
                    <span className="text-sm text-gray-500">历史存储:</span>
                    <Badge
                      variant={
                        device.deviceInfo?.storeHistoryData
                          ? 'default'
                          : 'secondary'
                      }>
                      {device.deviceInfo?.storeHistoryData ? '开启' : '关闭'}
                    </Badge>
                  </div>
                </div>
              </CardContent>
            </Card>

            <Card className="overflow-hidden">
              <CardHeader className="pb-2">
                <CardTitle className="text-base">设备管理</CardTitle>
              </CardHeader>
              <CardContent className="py-6">
                <div className="grid grid-cols-2 sm:grid-cols-3 md:grid-cols-4 lg:grid-cols-6 gap-4">
                  {/* 管理项数组渲染 */}
                  {[
                    {
                      key: 'diagnostics',
                      icon: <Gauge className="h-7 w-7 mb-1" />,
                      label: '设备诊断',
                      onClick: () => setShowDiagnosticsDialog(true),
                      disabled: false,
                    },
                    {
                      key: 'template',
                      icon: <FileCode className="h-7 w-7 mb-1" />,
                      label: '模板管理',
                      onClick: () => setShowTemplateManagerDialog(true),
                      disabled: false,
                    },
                    {
                      key: 'restart',
                      icon: <Power className="h-7 w-7 mb-1" />,
                      label: '重启设备',
                      onClick: handleRestartDevice,
                      disabled: !device.isConnect,
                    },
                  ].map((item) => (
                    <button
                      key={item.key}
                      type="button"
                      className={`flex flex-col items-center justify-center rounded-lg border border-gray-200 bg-white hover:border-primary/60 hover:shadow transition-all py-6 px-2 focus:outline-none disabled:opacity-50 disabled:cursor-not-allowed`}
                      onClick={item.onClick}
                      disabled={item.disabled}>
                      {item.icon}
                      <span className="font-medium text-sm mt-1">
                        {item.label}
                      </span>
                    </button>
                  ))}
                </div>
              </CardContent>
            </Card>
          </div>

          <div className="grid gap-6 md:grid-cols-3 mb-6">
            <Card className="overflow-hidden md:col-span-3">
              <CardHeader className="pb-2">
                <CardTitle className="text-base">点位管理</CardTitle>
              </CardHeader>
              <CardContent>
                <div className="grid grid-cols-1 md:grid-cols-6 gap-2">
                  <Button
                    variant="outline"
                    onClick={() => setShowAddPointDialog(true)}>
                    <PenSquare className="mr-2 h-4 w-4" />
                    新增点位
                  </Button>
                  <Button
                    variant="outline"
                    onClick={() => setShowAIBatchCreateDialog(true)}
                    className="bg-gradient-to-r from-blue-50 to-purple-50 border-blue-200 hover:from-blue-100 hover:to-purple-100">
                    <Wand2 className="mr-2 h-4 w-4 text-blue-600" />
                    <span className="text-blue-700">AI批量创建</span>
                  </Button>
                  <Button
                    variant="outline"
                    onClick={() => setShowImportExportDialog(true)}>
                    <Upload className="mr-2 h-4 w-4" />
                    导入点位
                  </Button>
                  <Button
                    variant="outline"
                    onClick={() => setShowExportDialog(true)}>
                    <Download className="mr-2 h-4 w-4" />
                    导出点位
                  </Button>
                  <Button
                    variant="outline"
                    onClick={() => setShowGroupManagerDialog(true)}>
                    <FolderPlus className="mr-2 h-4 w-4" />
                    点位分组管理
                  </Button>
                  <Button
                    variant="outline"
                    onClick={() => setShowAlarmConfigDialog(true)}>
                    <Bell className="mr-2 h-4 w-4" />
                    报警配置
                  </Button>
                  <Button
                    variant="outline"
                    className="relative group"
                    asChild
                    disabled={!device.enabled}>
                    <Link to={`/devices/${deviceId}/write-tags`}>
                      <div className="flex items-center">
                        <PenSquare className="mr-2 h-4 w-4 text-red-500" />
                        <span>批量写入</span>
                      </div>
                      <div className="absolute hidden group-hover:block bg-black text-white text-xs rounded py-1 px-2 -bottom-8 left-0 w-48 z-10">
                        谨慎操作！写入可能影响设备运行
                      </div>
                    </Link>
                  </Button>
                </div>
              </CardContent>
            </Card>
          </div>

          <Tabs defaultValue="dashboard" className="space-y-4">
            <TabsList>
              <TabsTrigger value="dashboard">仪表盘</TabsTrigger>
              <TabsTrigger value="data">实时数据</TabsTrigger>
              <TabsTrigger value="history">历史数据</TabsTrigger>
              <TabsTrigger value="alarms">报警记录</TabsTrigger>
            </TabsList>
            <TabsContent value="dashboard" className="pt-5">
              <DeviceDashboard
                deviceId={deviceId}
                tags={convertedTags}
                onRefresh={asyncNoop}
              />
            </TabsContent>
            <TabsContent value="data" className="pt-5">
              <div className="flex justify-between mb-4">
                <h2 className="text-xl font-bold">点位列表</h2>
                <div className="flex space-x-2">
                  <Button
                    size="sm"
                    variant="outline"
                    className="bg-blue-50 border-blue-200 hover:bg-blue-100"
                    onClick={() => {
                      // 获取TagList组件的引用并调用批量模式
                      const tagListElement =
                        document.getElementById('tag-list-component')
                      if (tagListElement) {
                        // 触发自定义事件来激活批量模式
                        const event = new CustomEvent('activate-batch-mode')
                        tagListElement.dispatchEvent(event)
                      } else {
                        // 如果无法获取引用，则显示提示
                        toast({
                          title: '批量管理',
                          description:
                            '请点击点位列表中的"批量管理"按钮开始批量操作',
                        })
                      }
                    }}>
                    <Edit className="mr-2 h-4 w-4" />
                    批量管理
                  </Button>
                  <Button size="sm" onClick={() => setShowAddPointDialog(true)}>
                    <Plus className="mr-2 h-4 w-4" />
                    添加点位
                  </Button>
                </div>
              </div>
              <div
                className="bg-blue-50 p-3 mb-4 rounded-md border border-blue-200"
                style={{ display: 'flex', alignItems: 'center' }}>
                <div className="text-blue-700 mr-2">
                  <svg
                    xmlns="http://www.w3.org/2000/svg"
                    width="20"
                    height="20"
                    viewBox="0 0 24 24"
                    fill="none"
                    stroke="currentColor"
                    strokeWidth="2"
                    strokeLinecap="round"
                    strokeLinejoin="round">
                    <circle cx="12" cy="12" r="10"></circle>
                    <path d="M12 16v-4"></path>
                    <path d="M12 8h.01"></path>
                  </svg>
                </div>
                <div className="text-sm text-blue-700">
                  新功能：现在您可以使用<strong>批量管理</strong>
                  功能同时编辑、删除或启用/禁用多个点位。点击上方的
                  <strong>批量管理</strong>按钮开始使用。
                </div>
              </div>
              <TagList
                id="tag-list-component"
                deviceId={deviceId}
                refreshTrigger={refreshTrigger}
                externalTags={tags}
                isLoading={tagsLoading}
                onBatchEdit={handleBatchEdit}
                onCopyTag={handleCopyTag}
                onTagDeleted={handleTagDeleted}
                protocol={device?.protocol}
                deviceName={device?.name}
                deviceIdentifier={device?.identifier}
              />
            </TabsContent>
            <TabsContent value="history">
              {device ? (
                <DeviceHistoryDataViewer
                  deviceId={device.identifier || deviceId}
                  deviceName={device.name}
                  availableTags={tags.map(convertToAvailableTag)}
                />
              ) : (
                <Card>
                  <CardHeader>
                    <CardTitle>历史数据</CardTitle>
                  </CardHeader>
                  <CardContent>
                    <p className="text-center text-gray-500 py-8">
                      设备信息加载中...
                    </p>
                  </CardContent>
                </Card>
              )}
            </TabsContent>
            <TabsContent value="alarms">
              <Card>
                <CardHeader>
                  <CardTitle>报警记录</CardTitle>
                </CardHeader>
                <CardContent>
                  <p className="text-center text-gray-500 py-8">
                    报警记录功能将在后续版本中提供
                  </p>
                </CardContent>
              </Card>
            </TabsContent>
          </Tabs>
        </>
      ) : (
        <div className="flex justify-center items-center py-12 bg-white rounded-lg border">
          <div className="text-center">
            <p className="text-gray-500 mb-4">设备不存在或已被删除</p>
            <Button variant="outline" asChild>
              <Link to="/devices">返回设备列表</Link>
            </Button>
          </div>
        </div>
      )}

      {/* 点位写入对话框 */}
      <TagValueWriter
        open={showWriteDialog}
        onOpenChange={setShowWriteDialog}
        tag={selectedTag ? (convertToPageTag(selectedTag) as any) : null}
      />

      {/* 点位Excel导入对话框 */}
      <ExcelImport
        open={showImportExportDialog}
        onOpenChange={setShowImportExportDialog}
        title="点位Excel导入"
        description="导入设备点位数据"
        systemFields={[
          { id: 'name', label: '点位名称', required: true },
          { id: 'address', label: '点位地址', required: true },
          { id: 'dataType', label: '数据类型', required: true },
          { id: 'readWrite', label: '读写权限', required: false },
          { id: 'uploadMethod', label: '上报方式', required: false },
          { id: 'description', label: '描述', required: false },
          { id: 'unit', label: '单位', required: false },
          { id: 'min', label: '最小值', required: false },
          { id: 'max', label: '最大值', required: false },
          { id: 'decimalPlaces', label: '小数位数', required: false },
          { id: 'alias', label: '别名', required: false },
        ]}
        onTemplateDownload={async () => {
          try {
            const url = '/api/label/export-template'
            window.open(url, '_blank')

            toast({
              title: '模板下载',
              description: '开始下载点位导入模板',
            })
          } catch (error) {
            console.error('下载模板失败:', error)
            toast({
              title: '下载失败',
              description: '无法下载点位导入模板',
              variant: 'destructive',
            })
          }
        }}
        onImport={async (data, config) => {
          if (data.length === 0) {
            toast({
              title: '导入失败',
              description: '没有可导入的数据',
              variant: 'destructive',
            })
            return
          }

          // 显示加载中反馈
          toast({
            title: '导入中',
            description: `正在导入 ${data.length} 个点位...`,
          })

          try {
            setTagsLoading(true)

            // 将导入数据转换为Excel文件
            const workbook = XLSX.utils.book_new()

            // 添加表头
            const headers = Object.keys(data[0] || {})
            const rows = data.map((item) =>
              headers.map((header) => item[header])
            )

            // 创建工作表
            const worksheet = XLSX.utils.aoa_to_sheet([headers, ...rows])
            XLSX.utils.book_append_sheet(workbook, worksheet, 'Points')

            // 转换为Blob
            const excelBuffer = XLSX.write(workbook, {
              bookType: 'xlsx',
              type: 'array',
            })
            const blob = new Blob([excelBuffer], {
              type: 'application/vnd.openxmlformats-officedocument.spreadsheetml.sheet',
            })

            // 导入模式：1=覆盖, 2=追加, 3=仅新增
            const importMode = config.fieldMappings.length > 0 ? 2 : 1

            // 使用TagService.importTags方法导入
            const result = await TagService.importTags(
              deviceId,
              blob,
              importMode
            )

            if (result) {
              toast({
                title: '导入成功',
                description: `已成功导入 ${data.length} 个点位`,
              })

              // 关闭导入对话框
              setShowImportExportDialog(false)

              // 重新加载点位列表
              debouncedLoadTags()
            } else {
              throw new Error('导入失败，请检查数据格式是否正确')
            }
          } catch (error) {
            console.error('导入点位失败:', error)
            toast({
              title: '导入失败',
              description:
                error instanceof Error ? error.message : '无法导入点位数据',
              variant: 'destructive',
            })
            throw error
          } finally {
            setTagsLoading(false)
          }
        }}
      />

      {/* 点位Excel导出对话框 */}
      <Dialog open={showExportDialog} onOpenChange={setShowExportDialog}>
        <DialogContent
          className="max-w-lg bg-white border-2 border-gray-300 shadow-xl"
          backgroundOpacity={0.9}>
          <DialogHeader>
            <DialogTitle>导出点位Excel</DialogTitle>
            <DialogDescription>
              将设备点位数据导出为完整Excel文件
            </DialogDescription>
          </DialogHeader>
          <div className="flex flex-col gap-4 py-4">
            <Button
              onClick={async () => {
                try {
                  setTagsLoading(true)

                  // 创建标准化的文件名
                  const timestamp = new Date().toISOString().slice(0, 10)
                  const deviceName = device?.name
                    ? device.name.replace(/[^\w\s]/gi, '_')
                    : deviceId
                  const fileName = `设备点位_${deviceName}_${timestamp}`

                  // 使用TagService导出 - 完整格式
                  const success = await TagService.exportTags(deviceId, {
                    exportMethod: 'complete',
                    fileName: fileName,
                  })

                  if (success) {
                    toast({
                      title: '导出成功',
                      description: '文件下载已开始',
                    })

                    setShowExportDialog(false)
                  } else {
                    throw new Error('导出失败，请稍后重试')
                  }
                } catch (error) {
                  console.error('导出点位失败:', error)
                  toast({
                    title: '导出失败',
                    description:
                      error instanceof Error
                        ? error.message
                        : '无法导出点位数据',
                    variant: 'destructive',
                  })
                } finally {
                  setTagsLoading(false)
                }
              }}
              className="w-full"
              disabled={tagsLoading}>
              <FileSpreadsheet className="mr-2 h-4 w-4" />
              导出Excel
              {tagsLoading && (
                <RefreshCw className="ml-2 h-3 w-3 animate-spin" />
              )}
            </Button>

            <div className="mt-2">
              <p className="text-sm text-muted-foreground">
                导出包含所有点位字段的完整Excel文件，适合备份或迁移到其他设备。通过调用后端API自动处理导出流程。
              </p>
            </div>
          </div>
        </DialogContent>
      </Dialog>

      {/* 点位分组管理对话框 */}
      <TagGroupManager
        open={showGroupManagerDialog}
        onOpenChange={setShowGroupManagerDialog}
        deviceId={deviceId}
        tags={convertedTags}
        onGroupsUpdated={() => debouncedLoadTags()}
      />

      {/* 点位报警配置对话框 */}
      <TagAlarmConfig
        open={showAlarmConfigDialog}
        onOpenChange={setShowAlarmConfigDialog}
        deviceId={deviceId}
        tags={convertedTags}
        onAlarmsUpdated={() => debouncedLoadTags()}
      />

      {/* 固件更新对话框 */}
      {device && (
        <FirmwareUpdate
          open={showFirmwareUpdateDialog}
          onOpenChange={setShowFirmwareUpdateDialog}
          device={device}
          onUpdateComplete={() => debouncedLoadDevice()}
        />
      )}

      {/* 设备诊断对话框 */}
      {device && (
        <DeviceDiagnostics
          open={showDiagnosticsDialog}
          onOpenChange={setShowDiagnosticsDialog}
          device={device}
        />
      )}

      {/* 设备模板管理对话框 */}
      {device && (
        <DeviceTemplateManager
          open={showTemplateManagerDialog}
          onOpenChange={setShowTemplateManagerDialog}
          device={device}
          tags={convertedTags}
          onTemplateApplied={handleTemplateApplied}
        />
      )}

      {/* 新增点位对话框 */}
      {device && (
        <AddDevicePointDialog
          open={showAddPointDialog}
          onOpenChange={setShowAddPointDialog}
          deviceId={deviceId}
          deviceIdentifier={device.identifier}
          deviceName={device.name}
          protocol={device.protocol}
          defaultValues={{
            ...newPointFormState,
            processingScript: newPointScriptContent,
          }}
          onPointAdded={() => {
            debouncedLoadTags() // 重新加载点位列表
          }}
          onSubmitting={setIsSubmitting}
          onScriptOpen={(scriptContent, mode) => {
            setNewPointScriptContent(scriptContent)
            setShowScriptEditorDialog(true)
          }}
          onScriptSave={(scriptContent) => {
            // 脚本保存后更新点位的脚本内容
            setNewPointScriptContent(scriptContent)
            handleNewPointFormChange('processingScript', scriptContent)
          }}
        />
      )}

      {/* 使用简化版脚本编辑器对话框 */}
      <ScriptEditorDialog
        open={showScriptEditorDialog}
        onOpenChange={(open) => {
          setShowScriptEditorDialog(open)
          // 当关闭对话框时，如果在新增点位模式下，更新点位的脚本内容
          if (!open && showAddPointDialog) {
            handleNewPointFormChange('processingScript', newPointScriptContent)
          }
        }}
        initialCode={showAddPointDialog ? newPointScriptContent : scriptContent}
        onSave={(code) => {
          if (showAddPointDialog) {
            // 当在新增点位模式下，保存到点位特定的脚本状态
            setNewPointScriptContent(code)
            handleNewPointFormChange('processingScript', code)

            // 关闭脚本编辑器对话框
            setShowScriptEditorDialog(false)
          } else {
            // 设备级别的脚本
            setScriptContent(code)
          }
        }}
        title={showAddPointDialog ? '点位数据处理脚本' : '设备脚本编辑器'}
        mode="script"
        availableTags={tags}
        deviceId={deviceId}
        deviceIdentifier={device?.identifier}
        onError={(error) => {
          console.error('脚本编辑器错误:', error)
          toast({
            title: '脚本调试失败',
            description: `错误信息: ${error.message || JSON.stringify(error)}`,
            variant: 'destructive',
          })
        }}
        showDebugInfo={true}
      />

      {/* 批量编辑点位对话框 */}
      {device && (
        <BatchTagDialog
          open={showBatchEditDialog}
          onOpenChange={setShowBatchEditDialog}
          deviceId={deviceId}
          tagsToEdit={selectedTagsForBatchEdit}
          onTagsUpdated={handleBatchTagsUpdated}
        />
      )}

      {/* AI辅助批量点位创建对话框 */}
      {/* 迁移注释：从Next.js迁移到Vite+React，集成AI辅助批量点位创建组件 */}
      {device && (
        <AIBatchLabelCreator
          open={showAIBatchCreateDialog}
          onOpenChange={setShowAIBatchCreateDialog}
          deviceId={deviceId}
          deviceName={device.name}
          protocol={device.driverName}
          onLabelsCreated={handleAILabelsCreated}
        />
      )}
    </div>
  )
}

// 主页面组件
export default function DeviceDetailPage() {
  return (
    <MainLayout>
      <DeviceDetailContent />
    </MainLayout>
  )
}
