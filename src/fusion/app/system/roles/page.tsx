/**
 * 角色管理页面 - 系统角色和权限管理
 *
 * 菜单位置：系统管理 > 角色管理
 * 路由地址：/system/roles
 * 页面功能：管理系统角色，配置角色权限和菜单访问控制
 */

import { useState, useEffect } from 'react'
import { MainLayout } from '@/components/layout/main-layout'
import {
  Card,
  CardContent,
  CardDescription,
  CardHeader,
  CardTitle,
} from '@/components/ui/card'
import { Button } from '@/components/ui/button'
import { Input } from '@/components/ui/input'
import {
  Table,
  TableBody,
  TableCell,
  TableHead,
  TableHeader,
  TableRow,
} from '@/components/ui/table'
import {
  Dialog,
  DialogContent,
  DialogDescription,
  DialogFooter,
  DialogHeader,
  DialogTitle,
} from '@/components/ui/dialog'
import { Label } from '@/components/ui/label'
import { Textarea } from '@/components/ui/textarea'
import { Badge } from '@/components/ui/badge'
import { Checkbox } from '@/components/ui/checkbox'
import {
  Plus,
  Search,
  Edit,
  Trash2,
  Shield,
  RefreshCw,
  Settings,
} from 'lucide-react'
import { toast } from '@/hooks/use-toast'

// API相关导入
import { feature, LoadingManager, getAPI } from '@/lib/api-services/axios-utils'
import { SysRoleApi } from '@/lib/api-services/apis/sys-role-api'
import { SysMenuApi } from '@/lib/api-services/apis/sys-menu-api'
import {
  RoleOutput,
  RoleCreateInput,
  RoleUpdateInput,
  AccountTypeEnum,
  MenuTreeOutput
} from '@/lib/api-services/models'

// 加载状态键
const LOADING_KEY = {
  ROLE_LIST: 'roles.list',
  ROLE_CREATE: 'roles.create',
  ROLE_UPDATE: 'roles.update',
  ROLE_DELETE: 'roles.delete',
  ROLE_MENUS: 'roles.menus',
  ASSIGN_MENUS: 'roles.assignMenus',
  MENU_TREE: 'menu.tree',
}

// 角色数据类型（基于后端API模型）
interface Role extends RoleOutput {
  userCount?: number // 前端扩展字段，用于显示用户数量
}

// 菜单权限数据类型
interface MenuPermission {
  id: number
  name: string
  code: string
  parentId?: number
  children?: MenuPermission[]
  checked: boolean
}

export default function RolesPage() {
  const [roles, setRoles] = useState<Role[]>([])
  const [loading, setLoading] = useState(true)
  const [searchTerm, setSearchTerm] = useState('')
  const [isAddDialogOpen, setIsAddDialogOpen] = useState(false)
  const [isPermissionDialogOpen, setIsPermissionDialogOpen] = useState(false)
  const [editingRole, setEditingRole] = useState<Role | null>(null)
  const [selectedRole, setSelectedRole] = useState<Role | null>(null)
  const [menuPermissions, setMenuPermissions] = useState<MenuPermission[]>([])
  const [allMenus, setAllMenus] = useState<MenuTreeOutput[]>([]) // 存储完整的菜单树
  const [currentPage, setCurrentPage] = useState(1)
  const [pageSize] = useState(20)
  const [total, setTotal] = useState(0)

  // 表单状态
  const [formData, setFormData] = useState({
    name: '',
    code: '',
    description: '',
    sort: 0,
    status: true,
  })

  // 加载角色列表
  const loadRoles = async () => {
    try {
      LoadingManager.setLoading(LOADING_KEY.ROLE_LIST, true)

      const [error, response] = await feature(
        getAPI(SysRoleApi).apiSysRolePageGet(
          currentPage,
          pageSize,
          searchTerm || undefined,
          undefined, // code
          undefined, // status
          undefined  // accountType
        )
      )

      if (error) {
        console.error('获取角色列表失败:', error)
        toast({
          title: '加载失败',
          description: '无法获取角色列表，请稍后重试',
          variant: 'destructive',
        })
        return
      }

      if (response?.data?.data?.items) {
        const roleList = response.data.data.items.map((role: RoleOutput) => ({
          ...role,
          userCount: 0, // TODO: 从用户API获取实际用户数量
        }))
        setRoles(roleList)
        setTotal(response.data.data.total || 0)
      }
    } catch (error) {
      console.error('获取角色列表异常:', error)
      toast({
        title: '系统错误',
        description: '获取角色列表时发生异常',
        variant: 'destructive',
      })
    } finally {
      LoadingManager.setLoading(LOADING_KEY.ROLE_LIST, false)
      setLoading(false)
    }
  }

  // 加载完整的菜单树
  const loadMenuTree = async () => {
    try {
      LoadingManager.setLoading(LOADING_KEY.MENU_TREE, true)

      const [error, response] = await feature(
        getAPI(SysMenuApi).apiSysMenuTreeGet()
      )

      if (error) {
        console.error('获取菜单树失败:', error)
        toast({
          title: '加载失败',
          description: '无法获取菜单树结构',
          variant: 'destructive',
        })
        return
      }

      if (response?.data?.data) {
        setAllMenus(response.data.data)
      }
    } catch (error) {
      console.error('获取菜单树异常:', error)
    } finally {
      LoadingManager.setLoading(LOADING_KEY.MENU_TREE, false)
    }
  }

  // 将菜单树转换为权限结构
  const convertMenuTreeToPermissions = (
    menus: MenuTreeOutput[],
    selectedMenuIds: number[]
  ): MenuPermission[] => {
    return menus.map(menu => ({
      id: menu.id!,
      name: menu.name || '',
      code: menu.code || '',
      parentId: menu.parentId || undefined,
      checked: selectedMenuIds.includes(menu.id!),
      children: menu.children && menu.children.length > 0
        ? convertMenuTreeToPermissions(menu.children, selectedMenuIds)
        : undefined,
    }))
  }

  // 加载菜单权限数据
  const loadMenuPermissions = async (roleId: number) => {
    try {
      LoadingManager.setLoading(LOADING_KEY.ROLE_MENUS, true)

      // 并行加载菜单树和角色菜单权限
      const [menuTreeError, menuTreeResponse] = await feature(
        getAPI(SysMenuApi).apiSysMenuTreeGet()
      )

      const [roleMenuError, roleMenuResponse] = await feature(
        getAPI(SysRoleApi).apiSysRoleIdMenusGet(roleId)
      )

      if (menuTreeError) {
        console.error('获取菜单树失败:', menuTreeError)
        toast({
          title: '加载失败',
          description: '无法获取菜单树结构',
          variant: 'destructive',
        })
        return
      }

      if (roleMenuError) {
        console.error('获取角色菜单失败:', roleMenuError)
        toast({
          title: '加载失败',
          description: '无法获取角色菜单权限',
          variant: 'destructive',
        })
        return
      }

      // 获取角色已分配的菜单ID列表
      const selectedMenuIds = roleMenuResponse?.data?.data || []

      // 获取完整的菜单树
      const menuTree = menuTreeResponse?.data?.data || []

      // 转换为权限结构
      const permissions = convertMenuTreeToPermissions(menuTree, selectedMenuIds)

      setMenuPermissions(permissions)
      setAllMenus(menuTree)
    } catch (error) {
      console.error('获取角色菜单异常:', error)
      toast({
        title: '系统错误',
        description: '获取菜单权限时发生异常',
        variant: 'destructive',
      })
    } finally {
      LoadingManager.setLoading(LOADING_KEY.ROLE_MENUS, false)
    }
  }

  // 初始化加载
  useEffect(() => {
    loadRoles()
    loadMenuTree() // 初始化时加载菜单树
  }, [currentPage])

  // 搜索防抖
  useEffect(() => {
    const timer = setTimeout(() => {
      if (currentPage === 1) {
        loadRoles()
      } else {
        setCurrentPage(1)
      }
    }, 500)

    return () => clearTimeout(timer)
  }, [searchTerm])

  const filteredRoles = roles.filter(
    (role) =>
      role.name?.toLowerCase().includes(searchTerm.toLowerCase()) ||
      role.code?.toLowerCase().includes(searchTerm.toLowerCase())
  )

  const handleAddRole = () => {
    setEditingRole(null)
    setFormData({
      name: '',
      code: '',
      description: '',
      sort: 0,
      status: true,
    })
    setIsAddDialogOpen(true)
  }

  const handleEditRole = (role: Role) => {
    setEditingRole(role)
    setFormData({
      name: role.name || '',
      code: role.code || '',
      description: role.description || '',
      sort: role.sort || 0,
      status: role.status || true,
    })
    setIsAddDialogOpen(true)
  }

  // 保存角色（创建或更新）
  const handleSaveRole = async () => {
    if (!formData.name.trim() || !formData.code.trim()) {
      toast({
        title: '验证失败',
        description: '角色名称和编码不能为空',
        variant: 'destructive',
      })
      return
    }

    try {
      if (editingRole) {
        // 更新角色
        LoadingManager.setLoading(LOADING_KEY.ROLE_UPDATE, true)

        const updateData: RoleUpdateInput = {
          id: editingRole.id!,
          name: formData.name,
          code: formData.code,
          description: formData.description,
          status: formData.status,
          sort: formData.sort,
        }

        const [error] = await feature(
          getAPI(SysRoleApi).apiSysRoleRolePut(updateData)
        )

        if (error) {
          console.error('更新角色失败:', error)
          toast({
            title: '更新失败',
            description: '无法更新角色，请稍后重试',
            variant: 'destructive',
          })
          return
        }

        toast({
          title: '更新成功',
          description: `角色 "${formData.name}" 已更新`,
        })
      } else {
        // 创建角色
        LoadingManager.setLoading(LOADING_KEY.ROLE_CREATE, true)

        const createData: RoleCreateInput = {
          name: formData.name,
          code: formData.code,
          description: formData.description,
          status: formData.status,
          sort: formData.sort,
        }

        const [error] = await feature(
          getAPI(SysRoleApi).apiSysRoleRolePost(createData)
        )

        if (error) {
          console.error('创建角色失败:', error)
          toast({
            title: '创建失败',
            description: '无法创建角色，请稍后重试',
            variant: 'destructive',
          })
          return
        }

        toast({
          title: '创建成功',
          description: `角色 "${formData.name}" 已创建`,
        })
      }

      setIsAddDialogOpen(false)
      loadRoles() // 重新加载角色列表
    } catch (error) {
      console.error('保存角色异常:', error)
      toast({
        title: '系统错误',
        description: '保存角色时发生异常',
        variant: 'destructive',
      })
    } finally {
      LoadingManager.setLoading(editingRole ? LOADING_KEY.ROLE_UPDATE : LOADING_KEY.ROLE_CREATE, false)
    }
  }

  const handleDeleteRole = async (role: Role) => {
    if (role.isDefault) {
      toast({
        title: '无法删除',
        description: '默认角色不能删除',
        variant: 'destructive',
      })
      return
    }

    if (role.userCount && role.userCount > 0) {
      toast({
        title: '无法删除',
        description: '该角色下还有用户，请先移除用户',
        variant: 'destructive',
      })
      return
    }

    if (!confirm(`确定要删除角色 "${role.name}" 吗？`)) {
      return
    }

    try {
      LoadingManager.setLoading(LOADING_KEY.ROLE_DELETE, true)

      const [error] = await feature(
        getAPI(SysRoleApi).apiSysRoleIdDelete(role.id!)
      )

      if (error) {
        console.error('删除角色失败:', error)
        toast({
          title: '删除失败',
          description: '无法删除角色，请稍后重试',
          variant: 'destructive',
        })
        return
      }

      toast({
        title: '删除成功',
        description: `角色 "${role.name}" 已被删除`,
      })

      // 重新加载角色列表
      loadRoles()
    } catch (error) {
      console.error('删除角色异常:', error)
      toast({
        title: '系统错误',
        description: '删除角色时发生异常',
        variant: 'destructive',
      })
    } finally {
      LoadingManager.setLoading(LOADING_KEY.ROLE_DELETE, false)
    }
  }

  const handleToggleStatus = async (role: Role) => {
    if (role.isDefault) {
      toast({
        title: '无法修改',
        description: '默认角色状态不能修改',
        variant: 'destructive',
      })
      return
    }

    try {
      LoadingManager.setLoading(LOADING_KEY.ROLE_UPDATE, true)

      const updateData: RoleUpdateInput = {
        id: role.id!,
        name: role.name!,
        code: role.code!,
        description: role.description,
        status: !role.status,
        accountType: role.accountType as AccountTypeEnum,
        sort: role.sort,
      }

      const [error] = await feature(
        getAPI(SysRoleApi).apiSysRoleRolePut(updateData)
      )

      if (error) {
        console.error('更新角色状态失败:', error)
        toast({
          title: '更新失败',
          description: '无法更新角色状态，请稍后重试',
          variant: 'destructive',
        })
        return
      }

      toast({
        title: role.status ? '角色已禁用' : '角色已启用',
        description: `角色 "${role.name}" 状态已更新`,
      })

      // 重新加载角色列表
      loadRoles()
    } catch (error) {
      console.error('更新角色状态异常:', error)
      toast({
        title: '系统错误',
        description: '更新角色状态时发生异常',
        variant: 'destructive',
      })
    } finally {
      LoadingManager.setLoading(LOADING_KEY.ROLE_UPDATE, false)
    }
  }

  const handleConfigurePermissions = async (role: Role) => {
    setSelectedRole(role)
    await loadMenuPermissions(role.id!)
    setIsPermissionDialogOpen(true)
  }

  const getAccountTypeLabel = (type?: string) => {
    switch (type) {
      case 'SuperAdmin':
        return '超级管理员'
      case 'SysAdmin':
        return '系统管理员'
      case 'NormalUser':
        return '普通用户'
      default:
        return '自定义'
    }
  }

  // 更新菜单权限状态
  const updateMenuPermission = (menuId: number, checked: boolean) => {
    const updateMenus = (menus: MenuPermission[]): MenuPermission[] => {
      return menus.map(menu => {
        if (menu.id === menuId) {
          return { ...menu, checked }
        }
        if (menu.children) {
          return { ...menu, children: updateMenus(menu.children) }
        }
        return menu
      })
    }
    setMenuPermissions(updateMenus(menuPermissions))
  }

  // 获取所有选中的菜单ID
  const getSelectedMenuIds = (menus: MenuPermission[]): number[] => {
    const ids: number[] = []
    menus.forEach(menu => {
      if (menu.checked) {
        ids.push(menu.id)
      }
      if (menu.children) {
        ids.push(...getSelectedMenuIds(menu.children))
      }
    })
    return ids
  }

  // 保存角色权限
  const handleSavePermissions = async () => {
    if (!selectedRole?.id) return

    try {
      LoadingManager.setLoading(LOADING_KEY.ASSIGN_MENUS, true)

      const selectedMenuIds = getSelectedMenuIds(menuPermissions)

      const [error] = await feature(
        getAPI(SysRoleApi).apiSysRoleIdMenusPost(selectedMenuIds, selectedRole.id)
      )

      if (error) {
        console.error('保存角色权限失败:', error)
        toast({
          title: '保存失败',
          description: '无法保存角色权限，请稍后重试',
          variant: 'destructive',
        })
        return
      }

      toast({
        title: '保存成功',
        description: `角色 "${selectedRole.name}" 的权限已更新`,
      })

      setIsPermissionDialogOpen(false)
    } catch (error) {
      console.error('保存角色权限异常:', error)
      toast({
        title: '系统错误',
        description: '保存角色权限时发生异常',
        variant: 'destructive',
      })
    } finally {
      LoadingManager.setLoading(LOADING_KEY.ASSIGN_MENUS, false)
    }
  }

  const renderMenuPermissions = (menus: MenuPermission[], level = 0) => {
    return menus.map((menu) => (
      <div key={menu.id} style={{ marginLeft: level * 20 }}>
        <div className="flex items-center space-x-2 py-1">
          <Checkbox
            id={`menu-${menu.id}`}
            checked={menu.checked}
            onCheckedChange={(checked) => {
              updateMenuPermission(menu.id, checked as boolean)
            }}
          />
          <Label htmlFor={`menu-${menu.id}`} className="text-sm">
            {menu.name}
          </Label>
        </div>
        {menu.children && renderMenuPermissions(menu.children, level + 1)}
      </div>
    ))
  }

  return (
    <MainLayout>
      <div className="container mx-auto py-6 space-y-6">
        <div className="flex flex-col space-y-2">
          <h1 className="text-3xl font-bold tracking-tight">角色管理</h1>
          <p className="text-muted-foreground">
            管理系统角色和权限配置
          </p>
        </div>

        <Card>
          <CardHeader>
            <CardTitle>角色列表</CardTitle>
            <CardDescription>
              系统中所有角色的管理界面
            </CardDescription>
          </CardHeader>
          <CardContent>
            {/* 操作栏 */}
            <div className="flex items-center justify-between mb-4">
              <div className="flex items-center space-x-2">
                <div className="relative">
                  <Search className="absolute left-2 top-2.5 h-4 w-4 text-muted-foreground" />
                  <Input
                    placeholder="搜索角色..."
                    value={searchTerm}
                    onChange={(e) => setSearchTerm(e.target.value)}
                    className="pl-8 w-64"
                  />
                </div>
                <Button
                  variant="outline"
                  size="sm"
                  onClick={() => {
                    setLoading(true)
                    loadRoles()
                  }}
                  disabled={LoadingManager.isLoading(LOADING_KEY.ROLE_LIST)}
                >
                  <RefreshCw className={`h-4 w-4 mr-2 ${LoadingManager.isLoading(LOADING_KEY.ROLE_LIST) ? 'animate-spin' : ''}`} />
                  刷新
                </Button>
              </div>
              <Button onClick={handleAddRole}>
                <Plus className="h-4 w-4 mr-2" />
                添加角色
              </Button>
            </div>

            {/* 角色表格 */}
            {loading ? (
              <div className="flex items-center justify-center h-32">
                <RefreshCw className="h-6 w-6 animate-spin" />
                <span className="ml-2">加载中...</span>
              </div>
            ) : (
              <Table>
                <TableHeader>
                  <TableRow>
                    <TableHead>角色名称</TableHead>
                    <TableHead>角色编码</TableHead>
                    <TableHead>描述</TableHead>
                    <TableHead>类型</TableHead>
                    <TableHead>状态</TableHead>
                    <TableHead>用户数</TableHead>
                    <TableHead>排序</TableHead>
                    <TableHead>创建时间</TableHead>
                    <TableHead>操作</TableHead>
                  </TableRow>
                </TableHeader>
                <TableBody>
                  {filteredRoles.map((role) => (
                    <TableRow key={role.id}>
                      <TableCell className="font-medium">
                        <div className="flex items-center space-x-2">
                          <span>{role.name}</span>
                          {role.isDefault && (
                            <Badge variant="outline" className="text-xs">
                              默认
                            </Badge>
                          )}
                        </div>
                      </TableCell>
                      <TableCell>
                        <code className="text-xs bg-muted px-1 py-0.5 rounded">
                          {role.code}
                        </code>
                      </TableCell>
                      <TableCell>
                        <span className="text-sm text-muted-foreground">
                          {role.description || '-'}
                        </span>
                      </TableCell>
                      <TableCell>
                        <Badge variant="secondary">
                          {getAccountTypeLabel(role.accountType?.toString())}
                        </Badge>
                      </TableCell>
                      <TableCell>
                        <Badge variant={role.status ? 'default' : 'secondary'}>
                          {role.status ? '启用' : '禁用'}
                        </Badge>
                      </TableCell>
                      <TableCell>{role.userCount || 0}</TableCell>
                      <TableCell>{role.sort || 0}</TableCell>
                      <TableCell>
                        {role.createTime ? new Date(role.createTime).toLocaleString() : '-'}
                      </TableCell>
                      <TableCell>
                        <div className="flex items-center space-x-2">
                          <Button
                            variant="outline"
                            size="sm"
                            onClick={() => handleEditRole(role)}
                          >
                            <Edit className="h-4 w-4" />
                          </Button>
                          <Button
                            variant="outline"
                            size="sm"
                            onClick={() => handleConfigurePermissions(role)}
                          >
                            <Settings className="h-4 w-4" />
                          </Button>
                          {!role.isDefault && (
                            <>
                              <Button
                                variant="outline"
                                size="sm"
                                onClick={() => handleToggleStatus(role)}
                              >
                                <Shield className="h-4 w-4" />
                              </Button>
                              <Button
                                variant="outline"
                                size="sm"
                                onClick={() => handleDeleteRole(role)}
                              >
                                <Trash2 className="h-4 w-4" />
                              </Button>
                            </>
                          )}
                        </div>
                      </TableCell>
                    </TableRow>
                  ))}
                </TableBody>
              </Table>
            )}
          </CardContent>
        </Card>

        {/* 添加/编辑角色对话框 */}
        <Dialog open={isAddDialogOpen} onOpenChange={setIsAddDialogOpen}>
          <DialogContent>
            <DialogHeader>
              <DialogTitle>
                {editingRole ? '编辑角色' : '添加角色'}
              </DialogTitle>
              <DialogDescription>
                {editingRole ? '修改角色信息' : '创建新的系统角色'}
              </DialogDescription>
            </DialogHeader>
            <div className="grid gap-4 py-4">
              <div className="grid grid-cols-4 items-center gap-4">
                <Label htmlFor="roleName" className="text-right">
                  角色名称
                </Label>
                <Input
                  id="roleName"
                  value={formData.name}
                  onChange={(e) => setFormData({ ...formData, name: e.target.value })}
                  className="col-span-3"
                  placeholder="请输入角色名称"
                />
              </div>
              <div className="grid grid-cols-4 items-center gap-4">
                <Label htmlFor="roleCode" className="text-right">
                  角色编码
                </Label>
                <Input
                  id="roleCode"
                  value={formData.code}
                  onChange={(e) => setFormData({ ...formData, code: e.target.value })}
                  className="col-span-3"
                  placeholder="请输入角色编码"
                />
              </div>
              <div className="grid grid-cols-4 items-center gap-4">
                <Label htmlFor="roleDescription" className="text-right">
                  描述
                </Label>
                <Textarea
                  id="roleDescription"
                  value={formData.description}
                  onChange={(e) => setFormData({ ...formData, description: e.target.value })}
                  className="col-span-3"
                  rows={3}
                  placeholder="请输入角色描述"
                />
              </div>
              <div className="grid grid-cols-4 items-center gap-4">
                <Label htmlFor="roleSort" className="text-right">
                  排序
                </Label>
                <Input
                  id="roleSort"
                  type="number"
                  value={formData.sort}
                  onChange={(e) => setFormData({ ...formData, sort: parseInt(e.target.value) || 0 })}
                  className="col-span-3"
                  placeholder="请输入排序值"
                />
              </div>
            </div>
            <DialogFooter>
              <Button variant="outline" onClick={() => setIsAddDialogOpen(false)}>
                取消
              </Button>
              <Button
                onClick={handleSaveRole}
                disabled={LoadingManager.isLoading(LOADING_KEY.ROLE_CREATE) || LoadingManager.isLoading(LOADING_KEY.ROLE_UPDATE)}
              >
                {LoadingManager.isLoading(LOADING_KEY.ROLE_CREATE) || LoadingManager.isLoading(LOADING_KEY.ROLE_UPDATE)
                  ? '保存中...'
                  : (editingRole ? '保存' : '创建')
                }
              </Button>
            </DialogFooter>
          </DialogContent>
        </Dialog>

        {/* 权限配置对话框 */}
        <Dialog open={isPermissionDialogOpen} onOpenChange={setIsPermissionDialogOpen}>
          <DialogContent className="max-w-2xl">
            <DialogHeader>
              <DialogTitle>配置权限 - {selectedRole?.name}</DialogTitle>
              <DialogDescription>
                为角色配置菜单访问权限
              </DialogDescription>
            </DialogHeader>
            <div className="max-h-96 overflow-y-auto py-4">
              {LoadingManager.isLoading(LOADING_KEY.ROLE_MENUS) ? (
                <div className="flex items-center justify-center py-8">
                  <RefreshCw className="h-6 w-6 animate-spin mr-2" />
                  <span>加载菜单权限中...</span>
                </div>
              ) : menuPermissions.length > 0 ? (
                renderMenuPermissions(menuPermissions)
              ) : (
                <div className="text-center py-8 text-gray-500">
                  暂无菜单权限数据
                </div>
              )}
            </div>
            <DialogFooter>
              <Button variant="outline" onClick={() => setIsPermissionDialogOpen(false)}>
                取消
              </Button>
              <Button
                onClick={handleSavePermissions}
                disabled={LoadingManager.isLoading(LOADING_KEY.ASSIGN_MENUS)}
              >
                {LoadingManager.isLoading(LOADING_KEY.ASSIGN_MENUS) ? '保存中...' : '保存权限'}
              </Button>
            </DialogFooter>
          </DialogContent>
        </Dialog>
      </div>
    </MainLayout>
  )
}
