'use client'

import React, { useState, useEffect } from 'react'
import { But<PERSON> } from '@/components/ui/button'
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from '@/components/ui/card'
import { Badge } from '@/components/ui/badge'
import { useToast } from '@/hooks/use-toast'
import { MainLayout } from '@/components/layout/main-layout'
import { permissionManagementApi } from '@/lib/api/permission-management-api'
import { <PERSON><PERSON><PERSON><PERSON><PERSON>, API_ENDPOINTS } from '@/lib/api/api-checker'
import { RefreshCw, CheckCircle, XCircle, AlertCircle } from 'lucide-react'

interface ApiTestResult {
  endpoint: string
  status: 'success' | 'error' | 'pending'
  message: string
  data?: any
}

export default function PermissionsTestPage() {
  const { toast } = useToast()
  const [testResults, setTestResults] = useState<ApiTestResult[]>([])
  const [isLoading, setIsLoading] = useState(false)

  const testEndpoints = [
    {
      name: '权限树API',
      endpoint: API_ENDPOINTS.PERMISSION_TREE,
      test: () => permissionManagementApi.getPermissionTree()
    },
    {
      name: '权限列表API',
      endpoint: API_ENDPOINTS.PERMISSION_LIST,
      test: () => permissionManagementApi.getPermissionList()
    },
    {
      name: '权限模板API',
      endpoint: API_ENDPOINTS.PERMISSION_TEMPLATES,
      test: () => permissionManagementApi.getPermissionTemplates()
    }
  ]

  const runTests = async () => {
    setIsLoading(true)
    const results: ApiTestResult[] = []

    for (const testCase of testEndpoints) {
      try {
        // 首先检查API是否可用
        const isAvailable = await ApiChecker.checkApi(testCase.endpoint)
        
        if (!isAvailable) {
          results.push({
            endpoint: testCase.name,
            status: 'error',
            message: 'API端点不可用 (404 Not Found)'
          })
          continue
        }

        // 尝试调用API
        const response = await testCase.test()
        results.push({
          endpoint: testCase.name,
          status: 'success',
          message: `API调用成功，返回 ${Array.isArray(response.data) ? response.data.length : '1'} 条数据`,
          data: response.data
        })
      } catch (error: any) {
        results.push({
          endpoint: testCase.name,
          status: 'error',
          message: error.message || '未知错误'
        })
      }
    }

    setTestResults(results)
    setIsLoading(false)

    // 显示测试结果摘要
    const successCount = results.filter(r => r.status === 'success').length
    const totalCount = results.length

    if (successCount === totalCount) {
      toast({
        title: '测试完成',
        description: `所有 ${totalCount} 个API测试通过`,
      })
    } else {
      toast({
        title: '测试完成',
        description: `${successCount}/${totalCount} 个API测试通过`,
        variant: 'destructive',
      })
    }
  }

  const getStatusIcon = (status: string) => {
    switch (status) {
      case 'success':
        return <CheckCircle className="h-5 w-5 text-green-500" />
      case 'error':
        return <XCircle className="h-5 w-5 text-red-500" />
      default:
        return <AlertCircle className="h-5 w-5 text-yellow-500" />
    }
  }

  const getStatusBadge = (status: string) => {
    switch (status) {
      case 'success':
        return <Badge variant="default" className="bg-green-500">成功</Badge>
      case 'error':
        return <Badge variant="destructive">失败</Badge>
      default:
        return <Badge variant="secondary">待测试</Badge>
    }
  }

  useEffect(() => {
    // 页面加载时自动运行测试
    runTests()
  }, [])

  return (
    <MainLayout>
      <div className="container mx-auto py-6 space-y-6">
        <div className="flex flex-col space-y-2">
          <h1 className="text-3xl font-bold tracking-tight">权限API测试</h1>
          <p className="text-muted-foreground">
            测试权限管理相关API的可用性和功能
          </p>
        </div>

        <div className="flex justify-between items-center">
          <div className="text-sm text-muted-foreground">
            测试结果：{testResults.filter(r => r.status === 'success').length}/{testResults.length} 通过
          </div>
          <Button 
            onClick={runTests} 
            disabled={isLoading}
            className="flex items-center gap-2"
          >
            <RefreshCw className={`h-4 w-4 ${isLoading ? 'animate-spin' : ''}`} />
            {isLoading ? '测试中...' : '重新测试'}
          </Button>
        </div>

        <div className="grid gap-4">
          {testResults.map((result, index) => (
            <Card key={index}>
              <CardHeader className="pb-3">
                <div className="flex items-center justify-between">
                  <div className="flex items-center gap-3">
                    {getStatusIcon(result.status)}
                    <CardTitle className="text-lg">{result.endpoint}</CardTitle>
                  </div>
                  {getStatusBadge(result.status)}
                </div>
              </CardHeader>
              <CardContent>
                <CardDescription className="mb-3">
                  {result.message}
                </CardDescription>
                
                {result.data && (
                  <div className="mt-4">
                    <h4 className="text-sm font-medium mb-2">返回数据预览：</h4>
                    <pre className="bg-muted p-3 rounded-md text-xs overflow-auto max-h-40">
                      {JSON.stringify(result.data, null, 2)}
                    </pre>
                  </div>
                )}
              </CardContent>
            </Card>
          ))}
        </div>

        {testResults.length === 0 && !isLoading && (
          <Card>
            <CardContent className="text-center py-8">
              <p className="text-muted-foreground">点击"重新测试"开始API测试</p>
            </CardContent>
          </Card>
        )}

        <Card>
          <CardHeader>
            <CardTitle>API端点信息</CardTitle>
            <CardDescription>当前配置的权限管理API端点</CardDescription>
          </CardHeader>
          <CardContent>
            <div className="space-y-2">
              <div className="flex justify-between">
                <span className="font-medium">权限树:</span>
                <code className="text-sm bg-muted px-2 py-1 rounded">{API_ENDPOINTS.PERMISSION_TREE}</code>
              </div>
              <div className="flex justify-between">
                <span className="font-medium">权限列表:</span>
                <code className="text-sm bg-muted px-2 py-1 rounded">{API_ENDPOINTS.PERMISSION_LIST}</code>
              </div>
              <div className="flex justify-between">
                <span className="font-medium">权限模板:</span>
                <code className="text-sm bg-muted px-2 py-1 rounded">{API_ENDPOINTS.PERMISSION_TEMPLATES}</code>
              </div>
            </div>
          </CardContent>
        </Card>
      </div>
    </MainLayout>
  )
}
