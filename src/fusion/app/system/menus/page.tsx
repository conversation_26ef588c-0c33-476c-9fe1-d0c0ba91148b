/**
 * 菜单管理页面 - 系统菜单结构管理
 *
 * 菜单位置：系统管理 > 菜单管理
 * 路由地址：/system/menus
 * 页面功能：管理系统菜单结构，配置菜单层级和访问权限
 */

import { useState, useEffect } from 'react'
import { MainLayout } from '@/components/layout/main-layout'
import {
  Card,
  CardContent,
  CardDescription,
  CardHeader,
  CardTitle,
} from '@/components/ui/card'
import { Button } from '@/components/ui/button'
import { Input } from '@/components/ui/input'
import {
  Table,
  TableBody,
  TableCell,
  TableHead,
  TableHeader,
  TableRow,
} from '@/components/ui/table'
import {
  Dialog,
  DialogContent,
  DialogDescription,
  DialogFooter,
  DialogHeader,
  DialogTitle,
} from '@/components/ui/dialog'
import { Label } from '@/components/ui/label'
import {
  Select,
  SelectContent,
  SelectItem,
  SelectTrigger,
  SelectValue,
} from '@/components/ui/select'
import { Badge } from '@/components/ui/badge'
import { Switch } from '@/components/ui/switch'
import {
  Plus,
  Search,
  Edit,
  Trash2,
  Menu,
  RefreshCw,
  Eye,
  EyeOff,
  ChevronRight,
  ChevronDown,
} from 'lucide-react'
import { toast } from '@/hooks/use-toast'

// API相关导入
import { feature, LoadingManager, getAPI } from '@/lib/api-services/axios-utils'
import { SysMenuApi } from '@/lib/api-services/apis/sys-menu-api'
import {
  MenuTreeOutput,
  MenuCreateInput,
  MenuUpdateInput,
  MenuTypeEnum,
  MenuOutput
} from '@/lib/api-services/models'

// 加载状态键
const LOADING_KEY = {
  MENU_LIST: 'menu.list',
  MENU_TREE: 'menu.tree',
  MENU_CREATE: 'menu.create',
  MENU_UPDATE: 'menu.update',
  MENU_DELETE: 'menu.delete',
}

// 菜单数据类型（基于后端API模型）
interface MenuItem extends MenuTreeOutput {
  level?: number // 前端计算的层级
  expanded?: boolean // 前端展开状态
  children?: MenuItem[]
}

export default function MenusPage() {
  const [menus, setMenus] = useState<MenuItem[]>([])
  const [flatMenus, setFlatMenus] = useState<MenuItem[]>([])
  const [loading, setLoading] = useState(true)
  const [searchTerm, setSearchTerm] = useState('')
  const [isAddDialogOpen, setIsAddDialogOpen] = useState(false)
  const [editingMenu, setEditingMenu] = useState<MenuItem | null>(null)

  // 表单状态
  const [formData, setFormData] = useState({
    name: '',
    code: '',
    path: '',
    icon: '',
    parentId: null as number | null,
    menuType: MenuTypeEnum.NUMBER_2, // 默认为菜单类型
    sort: 0,
    status: true,
    hidden: false,
  })

  // 菜单类型转换函数
  const getMenuTypeLabel = (menuType?: MenuTypeEnum) => {
    switch (menuType) {
      case MenuTypeEnum.NUMBER_1:
        return '模块'
      case MenuTypeEnum.NUMBER_2:
        return '菜单'
      default:
        return '未知'
    }
  }

  // 计算菜单层级
  const calculateLevel = (menus: MenuTreeOutput[], parentLevel = 0): MenuItem[] => {
    return menus.map(menu => ({
      ...menu,
      level: parentLevel + 1,
      expanded: true,
      children: menu.children ? calculateLevel(menu.children, parentLevel + 1) : undefined,
    }))
  }

  // 展平菜单数据用于表格显示
  const flattenMenus = (menus: MenuItem[], result: MenuItem[] = []): MenuItem[] => {
    menus.forEach(menu => {
      result.push({ ...menu })
      if (menu.children && menu.children.length > 0) {
        flattenMenus(menu.children, result)
      }
    })
    return result
  }

  // 加载菜单树数据
  const loadMenus = async () => {
    try {
      LoadingManager.setLoading(LOADING_KEY.MENU_TREE, true)

      const [error, response] = await feature(
        getAPI(SysMenuApi).apiSysMenuTreeGet()
      )

      if (error) {
        console.error('获取菜单树失败:', error)
        toast({
          title: '加载失败',
          description: '无法获取菜单列表，请稍后重试',
          variant: 'destructive',
        })
        return
      }

      if (response?.data?.data) {
        const menuTree = calculateLevel(response.data.data)
        setMenus(menuTree)
        setFlatMenus(flattenMenus(menuTree))
      }
    } catch (error) {
      console.error('获取菜单树异常:', error)
      toast({
        title: '系统错误',
        description: '获取菜单列表时发生异常',
        variant: 'destructive',
      })
    } finally {
      LoadingManager.setLoading(LOADING_KEY.MENU_TREE, false)
      setLoading(false)
    }
  }

  // 初始化加载
  useEffect(() => {
    loadMenus()
  }, [])

  const filteredMenus = flatMenus.filter(
    (menu) =>
      menu.name?.toLowerCase().includes(searchTerm.toLowerCase()) ||
      menu.code?.toLowerCase().includes(searchTerm.toLowerCase())
  )

  const handleAddMenu = () => {
    setEditingMenu(null)
    setFormData({
      name: '',
      code: '',
      path: '',
      icon: '',
      parentId: null,
      menuType: MenuTypeEnum.NUMBER_2,
      sort: 0,
      status: true,
      hidden: false,
    })
    setIsAddDialogOpen(true)
  }

  const handleEditMenu = (menu: MenuItem) => {
    setEditingMenu(menu)
    setFormData({
      name: menu.name || '',
      code: menu.code || '',
      path: menu.path || '',
      icon: menu.icon || '',
      parentId: menu.parentId || null,
      menuType: menu.menuType || MenuTypeEnum.NUMBER_2,
      sort: menu.sort || 0,
      status: menu.status || true,
      hidden: menu.hidden || false,
    })
    setIsAddDialogOpen(true)
  }

  const handleDeleteMenu = async (menu: MenuItem) => {
    if (menu.children && menu.children.length > 0) {
      toast({
        title: '无法删除',
        description: '该菜单下还有子菜单，请先删除子菜单',
        variant: 'destructive',
      })
      return
    }

    if (!confirm(`确定要删除菜单 "${menu.name}" 吗？`)) {
      return
    }

    try {
      LoadingManager.setLoading(LOADING_KEY.MENU_DELETE, true)

      const [error] = await feature(
        getAPI(SysMenuApi).apiSysMenuIdDelete(menu.id!)
      )

      if (error) {
        console.error('删除菜单失败:', error)
        toast({
          title: '删除失败',
          description: '无法删除菜单，请稍后重试',
          variant: 'destructive',
        })
        return
      }

      toast({
        title: '删除成功',
        description: `菜单 "${menu.name}" 已被删除`,
      })

      // 重新加载菜单列表
      loadMenus()
    } catch (error) {
      console.error('删除菜单异常:', error)
      toast({
        title: '系统错误',
        description: '删除菜单时发生异常',
        variant: 'destructive',
      })
    } finally {
      LoadingManager.setLoading(LOADING_KEY.MENU_DELETE, false)
    }
  }

  // 保存菜单（创建或更新）
  const handleSaveMenu = async () => {
    if (!formData.name.trim() || !formData.code.trim()) {
      toast({
        title: '验证失败',
        description: '菜单名称和编码不能为空',
        variant: 'destructive',
      })
      return
    }

    try {
      if (editingMenu) {
        // 更新菜单
        LoadingManager.setLoading(LOADING_KEY.MENU_UPDATE, true)

        const updateData: MenuUpdateInput = {
          id: editingMenu.id!,
          name: formData.name,
          code: formData.code,
          path: formData.path || null,
          icon: formData.icon || null,
          parentId: formData.parentId,
          menuType: formData.menuType,
          sort: formData.sort,
          status: formData.status,
          hidden: formData.hidden,
        }

        const [error] = await feature(
          getAPI(SysMenuApi).apiSysMenuMenuPut(updateData)
        )

        if (error) {
          console.error('更新菜单失败:', error)
          toast({
            title: '更新失败',
            description: '无法更新菜单，请稍后重试',
            variant: 'destructive',
          })
          return
        }

        toast({
          title: '更新成功',
          description: `菜单 "${formData.name}" 已更新`,
        })
      } else {
        // 创建菜单
        LoadingManager.setLoading(LOADING_KEY.MENU_CREATE, true)

        const createData: MenuCreateInput = {
          name: formData.name,
          code: formData.code,
          path: formData.path || null,
          icon: formData.icon || null,
          parentId: formData.parentId,
          menuType: formData.menuType,
          sort: formData.sort,
          status: formData.status,
          hidden: formData.hidden,
        }

        const [error] = await feature(
          getAPI(SysMenuApi).apiSysMenuMenuPost(createData)
        )

        if (error) {
          console.error('创建菜单失败:', error)
          toast({
            title: '创建失败',
            description: '无法创建菜单，请稍后重试',
            variant: 'destructive',
          })
          return
        }

        toast({
          title: '创建成功',
          description: `菜单 "${formData.name}" 已创建`,
        })
      }

      setIsAddDialogOpen(false)
      loadMenus() // 重新加载菜单列表
    } catch (error) {
      console.error('保存菜单异常:', error)
      toast({
        title: '系统错误',
        description: '保存菜单时发生异常',
        variant: 'destructive',
      })
    } finally {
      LoadingManager.setLoading(editingMenu ? LOADING_KEY.MENU_UPDATE : LOADING_KEY.MENU_CREATE, false)
    }
  }

  const handleToggleStatus = async (menu: MenuItem) => {
    try {
      LoadingManager.setLoading(LOADING_KEY.MENU_UPDATE, true)

      const updateData: MenuUpdateInput = {
        id: menu.id!,
        name: menu.name!,
        code: menu.code!,
        path: menu.path,
        icon: menu.icon,
        parentId: menu.parentId,
        menuType: menu.menuType,
        sort: menu.sort,
        status: !menu.status,
        hidden: menu.hidden,
      }

      const [error] = await feature(
        getAPI(SysMenuApi).apiSysMenuMenuPut(updateData)
      )

      if (error) {
        console.error('更新菜单状态失败:', error)
        toast({
          title: '更新失败',
          description: '无法更新菜单状态，请稍后重试',
          variant: 'destructive',
        })
        return
      }

      toast({
        title: menu.status ? '菜单已禁用' : '菜单已启用',
        description: `菜单 "${menu.name}" 状态已更新`,
      })

      // 重新加载菜单列表
      loadMenus()
    } catch (error) {
      console.error('更新菜单状态异常:', error)
      toast({
        title: '系统错误',
        description: '更新菜单状态时发生异常',
        variant: 'destructive',
      })
    } finally {
      LoadingManager.setLoading(LOADING_KEY.MENU_UPDATE, false)
    }
  }

  const handleToggleHidden = async (menu: MenuItem) => {
    try {
      LoadingManager.setLoading(LOADING_KEY.MENU_UPDATE, true)

      const updateData: MenuUpdateInput = {
        id: menu.id!,
        name: menu.name!,
        code: menu.code!,
        path: menu.path,
        icon: menu.icon,
        parentId: menu.parentId,
        menuType: menu.menuType,
        sort: menu.sort,
        status: menu.status,
        hidden: !menu.hidden,
      }

      const [error] = await feature(
        getAPI(SysMenuApi).apiSysMenuMenuPut(updateData)
      )

      if (error) {
        console.error('更新菜单可见性失败:', error)
        toast({
          title: '更新失败',
          description: '无法更新菜单可见性，请稍后重试',
          variant: 'destructive',
        })
        return
      }

      toast({
        title: menu.hidden ? '菜单已显示' : '菜单已隐藏',
        description: `菜单 "${menu.name}" 可见性已更新`,
      })

      // 重新加载菜单列表
      loadMenus()
    } catch (error) {
      console.error('更新菜单可见性异常:', error)
      toast({
        title: '系统错误',
        description: '更新菜单可见性时发生异常',
        variant: 'destructive',
      })
    } finally {
      LoadingManager.setLoading(LOADING_KEY.MENU_UPDATE, false)
    }
  }

  const getMenuTypeBadge = (menuType?: MenuTypeEnum) => {
    switch (menuType) {
      case MenuTypeEnum.NUMBER_1:
        return <Badge variant="outline">模块</Badge>
      case MenuTypeEnum.NUMBER_2:
        return <Badge variant="default">菜单</Badge>
      default:
        return <Badge variant="outline">未知</Badge>
    }
  }

  const renderMenuName = (menu: MenuItem) => {
    const indent = ((menu.level || 1) - 1) * 20
    return (
      <div className="flex items-center" style={{ marginLeft: indent }}>
        {menu.children && menu.children.length > 0 ? (
          menu.expanded ? (
            <ChevronDown className="h-4 w-4 mr-1" />
          ) : (
            <ChevronRight className="h-4 w-4 mr-1" />
          )
        ) : (
          <div className="w-5" />
        )}
        <span>{menu.name}</span>
      </div>
    )
  }

  return (
    <MainLayout>
      <div className="container mx-auto py-6 space-y-6">
        <div className="flex flex-col space-y-2">
          <h1 className="text-3xl font-bold tracking-tight">菜单管理</h1>
          <p className="text-muted-foreground">
            管理系统菜单结构和权限配置
          </p>
        </div>

        <Card>
          <CardHeader>
            <CardTitle>菜单列表</CardTitle>
            <CardDescription>
              系统菜单的层级结构管理
            </CardDescription>
          </CardHeader>
          <CardContent>
            {/* 操作栏 */}
            <div className="flex items-center justify-between mb-4">
              <div className="flex items-center space-x-2">
                <div className="relative">
                  <Search className="absolute left-2 top-2.5 h-4 w-4 text-muted-foreground" />
                  <Input
                    placeholder="搜索菜单..."
                    value={searchTerm}
                    onChange={(e) => setSearchTerm(e.target.value)}
                    className="pl-8 w-64"
                  />
                </div>
                <Button
                  variant="outline"
                  size="sm"
                  onClick={() => {
                    setLoading(true)
                    loadMenus()
                  }}
                  disabled={LoadingManager.isLoading(LOADING_KEY.MENU_TREE)}
                >
                  <RefreshCw className={`h-4 w-4 mr-2 ${LoadingManager.isLoading(LOADING_KEY.MENU_TREE) ? 'animate-spin' : ''}`} />
                  刷新
                </Button>
              </div>
              <Button onClick={handleAddMenu}>
                <Plus className="h-4 w-4 mr-2" />
                添加菜单
              </Button>
            </div>

            {/* 菜单表格 */}
            {loading ? (
              <div className="flex items-center justify-center h-32">
                <RefreshCw className="h-6 w-6 animate-spin" />
                <span className="ml-2">加载中...</span>
              </div>
            ) : (
              <Table>
                <TableHeader>
                  <TableRow>
                    <TableHead>菜单名称</TableHead>
                    <TableHead>菜单编码</TableHead>
                    <TableHead>路径</TableHead>
                    <TableHead>类型</TableHead>
                    <TableHead>图标</TableHead>
                    <TableHead>排序</TableHead>
                    <TableHead>状态</TableHead>
                    <TableHead>可见</TableHead>
                    <TableHead>创建时间</TableHead>
                    <TableHead>操作</TableHead>
                  </TableRow>
                </TableHeader>
                <TableBody>
                  {filteredMenus.map((menu) => (
                    <TableRow key={menu.id}>
                      <TableCell className="font-medium">
                        {renderMenuName(menu)}
                      </TableCell>
                      <TableCell>
                        <code className="text-xs bg-muted px-1 py-0.5 rounded">
                          {menu.code}
                        </code>
                      </TableCell>
                      <TableCell>
                        <span className="text-sm text-muted-foreground">
                          {menu.path || '-'}
                        </span>
                      </TableCell>
                      <TableCell>{getMenuTypeBadge(menu.menuType)}</TableCell>
                      <TableCell>
                        <span className="text-sm text-muted-foreground">
                          {menu.icon || '-'}
                        </span>
                      </TableCell>
                      <TableCell>{menu.sort || 0}</TableCell>
                      <TableCell>
                        <Badge variant={menu.status ? 'default' : 'secondary'}>
                          {menu.status ? '启用' : '禁用'}
                        </Badge>
                      </TableCell>
                      <TableCell>
                        <Badge variant={menu.hidden ? 'secondary' : 'default'}>
                          {menu.hidden ? '隐藏' : '显示'}
                        </Badge>
                      </TableCell>
                      <TableCell>
                        {menu.createTime ? new Date(menu.createTime).toLocaleString() : '-'}
                      </TableCell>
                      <TableCell>
                        <div className="flex items-center space-x-2">
                          <Button
                            variant="outline"
                            size="sm"
                            onClick={() => handleEditMenu(menu)}
                          >
                            <Edit className="h-4 w-4" />
                          </Button>
                          <Button
                            variant="outline"
                            size="sm"
                            onClick={() => handleToggleHidden(menu)}
                          >
                            {menu.hidden ? (
                              <Eye className="h-4 w-4" />
                            ) : (
                              <EyeOff className="h-4 w-4" />
                            )}
                          </Button>
                          <Button
                            variant="outline"
                            size="sm"
                            onClick={() => handleDeleteMenu(menu)}
                          >
                            <Trash2 className="h-4 w-4" />
                          </Button>
                        </div>
                      </TableCell>
                    </TableRow>
                  ))}
                </TableBody>
              </Table>
            )}
          </CardContent>
        </Card>

        {/* 添加/编辑菜单对话框 */}
        <Dialog open={isAddDialogOpen} onOpenChange={setIsAddDialogOpen}>
          <DialogContent className="max-w-2xl">
            <DialogHeader>
              <DialogTitle>
                {editingMenu ? '编辑菜单' : '添加菜单'}
              </DialogTitle>
              <DialogDescription>
                {editingMenu ? '修改菜单信息' : '创建新的系统菜单'}
              </DialogDescription>
            </DialogHeader>
            <div className="grid gap-4 py-4">
              <div className="grid grid-cols-4 items-center gap-4">
                <Label htmlFor="menuName" className="text-right">
                  菜单名称
                </Label>
                <Input
                  id="menuName"
                  value={formData.name}
                  onChange={(e) => setFormData({ ...formData, name: e.target.value })}
                  className="col-span-3"
                  placeholder="请输入菜单名称"
                />
              </div>
              <div className="grid grid-cols-4 items-center gap-4">
                <Label htmlFor="menuCode" className="text-right">
                  菜单编码
                </Label>
                <Input
                  id="menuCode"
                  value={formData.code}
                  onChange={(e) => setFormData({ ...formData, code: e.target.value })}
                  className="col-span-3"
                  placeholder="请输入菜单编码"
                />
              </div>
              <div className="grid grid-cols-4 items-center gap-4">
                <Label htmlFor="menuPath" className="text-right">
                  路径
                </Label>
                <Input
                  id="menuPath"
                  value={formData.path}
                  onChange={(e) => setFormData({ ...formData, path: e.target.value })}
                  className="col-span-3"
                  placeholder="请输入菜单路径"
                />
              </div>
              <div className="grid grid-cols-4 items-center gap-4">
                <Label htmlFor="menuType" className="text-right">
                  菜单类型
                </Label>
                <Select
                  value={formData.menuType?.toString()}
                  onValueChange={(value) => setFormData({ ...formData, menuType: parseInt(value) as MenuTypeEnum })}
                >
                  <SelectTrigger className="col-span-3">
                    <SelectValue placeholder="选择菜单类型" />
                  </SelectTrigger>
                  <SelectContent>
                    <SelectItem value={MenuTypeEnum.NUMBER_1.toString()}>模块</SelectItem>
                    <SelectItem value={MenuTypeEnum.NUMBER_2.toString()}>菜单</SelectItem>
                  </SelectContent>
                </Select>
              </div>
              <div className="grid grid-cols-4 items-center gap-4">
                <Label htmlFor="parentMenu" className="text-right">
                  父级菜单
                </Label>
                <Select
                  value={formData.parentId?.toString() || ''}
                  onValueChange={(value) => setFormData({ ...formData, parentId: value ? parseInt(value) : null })}
                >
                  <SelectTrigger className="col-span-3">
                    <SelectValue placeholder="选择父级菜单（可选）" />
                  </SelectTrigger>
                  <SelectContent>
                    <SelectItem value="">根目录</SelectItem>
                    {flatMenus
                      .filter(menu => menu.menuType === MenuTypeEnum.NUMBER_1) // 只显示模块类型作为父级
                      .map(menu => (
                        <SelectItem key={menu.id} value={menu.id!.toString()}>
                          {menu.name}
                        </SelectItem>
                      ))
                    }
                  </SelectContent>
                </Select>
              </div>
              <div className="grid grid-cols-4 items-center gap-4">
                <Label htmlFor="menuIcon" className="text-right">
                  图标
                </Label>
                <Input
                  id="menuIcon"
                  value={formData.icon}
                  onChange={(e) => setFormData({ ...formData, icon: e.target.value })}
                  className="col-span-3"
                  placeholder="Lucide图标名称"
                />
              </div>
              <div className="grid grid-cols-4 items-center gap-4">
                <Label htmlFor="menuSort" className="text-right">
                  排序
                </Label>
                <Input
                  id="menuSort"
                  type="number"
                  value={formData.sort}
                  onChange={(e) => setFormData({ ...formData, sort: parseInt(e.target.value) || 0 })}
                  className="col-span-3"
                  placeholder="请输入排序值"
                />
              </div>
              <div className="grid grid-cols-4 items-center gap-4">
                <Label htmlFor="menuStatus" className="text-right">
                  状态
                </Label>
                <div className="col-span-3 flex items-center space-x-2">
                  <Switch
                    id="menuStatus"
                    checked={formData.status}
                    onCheckedChange={(checked) => setFormData({ ...formData, status: checked })}
                  />
                  <Label htmlFor="menuStatus">启用</Label>
                </div>
              </div>
              <div className="grid grid-cols-4 items-center gap-4">
                <Label htmlFor="menuHidden" className="text-right">
                  可见性
                </Label>
                <div className="col-span-3 flex items-center space-x-2">
                  <Switch
                    id="menuHidden"
                    checked={!formData.hidden}
                    onCheckedChange={(checked) => setFormData({ ...formData, hidden: !checked })}
                  />
                  <Label htmlFor="menuHidden">显示</Label>
                </div>
              </div>
            </div>
            <DialogFooter>
              <Button variant="outline" onClick={() => setIsAddDialogOpen(false)}>
                取消
              </Button>
              <Button
                onClick={handleSaveMenu}
                disabled={LoadingManager.isLoading(LOADING_KEY.MENU_CREATE) || LoadingManager.isLoading(LOADING_KEY.MENU_UPDATE)}
              >
                {LoadingManager.isLoading(LOADING_KEY.MENU_CREATE) || LoadingManager.isLoading(LOADING_KEY.MENU_UPDATE)
                  ? '保存中...'
                  : (editingMenu ? '保存' : '创建')
                }
              </Button>
            </DialogFooter>
          </DialogContent>
        </Dialog>
      </div>
    </MainLayout>
  )
}
