/**
 * 数据监控页面 - 监控仪表盘和实时状态监控
 *
 * 菜单位置：侧边栏主导航 > 数据监控 > 监控仪表盘
 * 路由地址：/monitoring
 * 页面功能：工作流编排平台的监控中心，提供实时系统监控和工作流执行状态跟踪
 *
 *
 * 业务价值：
 * - 实时掌握系统运行状态和健康度
 * - 快速识别和响应执行异常
 * - 优化工作流性能和资源配置
 * - 提供运维决策的数据支持
 * - 确保业务流程的稳定运行
 * - 支持故障排查和问题诊断
 */

import { Button } from '@/components/ui/button'
import {
  Card,
  CardContent,
  CardDescription,
  CardHeader,
  CardTitle,
} from '@/components/ui/card'
import { Input } from '@/components/ui/input'
import { Badge } from '@/components/ui/badge'
import {
  Calendar,
  Filter,
  Search,
  ArrowUpDown,
  CheckCircle2,
  AlertCircle,
  AlertTriangle,
  Play,
  Pause,
} from 'lucide-react'
import MainLayout from '@/components/layout/main-layout'
import { MonitoringCharts } from '@/components/monitoring/monitoring-charts'

export default function MonitoringPage() {
  const executionData = [
    { name: '周一', success: 42, failed: 3, pending: 5 },
    { name: '周二', success: 38, failed: 2, pending: 4 },
    { name: '周三', success: 45, failed: 1, pending: 3 },
    { name: '周四', success: 39, failed: 4, pending: 2 },
    { name: '周五', success: 48, failed: 2, pending: 6 },
    { name: '周六', success: 30, failed: 1, pending: 2 },
    { name: '周日', success: 27, failed: 0, pending: 1 },
  ]

  const durationData = [
    { name: '客户数据同步', value: 1.2 },
    { name: '订单处理', value: 3.5 },
    { name: '库存报告', value: 0.8 },
    { name: '用户入职', value: 0.5 },
    { name: '支付对账', value: 2.1 },
  ]

  const executions = [
    {
      id: 'exec-001',
      workflow: '客户数据同步',
      status: 'success',
      startTime: '2025-04-03 09:15:22',
      duration: '1分12秒',
      trigger: '定时',
    },
    {
      id: 'exec-002',
      workflow: '订单处理流程',
      status: 'failed',
      startTime: '2025-04-03 08:45:10',
      duration: '3分30秒',
      trigger: 'API',
    },
    {
      id: 'exec-003',
      workflow: '每日库存报告',
      status: 'success',
      startTime: '2025-04-03 07:00:00',
      duration: '45秒',
      trigger: '定时',
    },
    {
      id: 'exec-004',
      workflow: '支付对账',
      status: 'running',
      startTime: '2025-04-03 09:30:15',
      duration: '2分05秒',
      trigger: '手动',
    },
    {
      id: 'exec-005',
      workflow: '用户入职流程',
      status: 'warning',
      startTime: '2025-04-03 08:15:30',
      duration: '30秒',
      trigger: 'Webhook',
    },
  ]

  const getStatusBadge = (status: string) => {
    switch (status) {
      case 'success':
        return (
          <Badge className="bg-green-100 text-green-800 hover:bg-green-100">
            成功
          </Badge>
        )
      case 'failed':
        return (
          <Badge className="bg-red-100 text-red-800 hover:bg-red-100">
            失败
          </Badge>
        )
      case 'running':
        return (
          <Badge className="bg-blue-100 text-blue-800 hover:bg-blue-100">
            运行中
          </Badge>
        )
      case 'warning':
        return (
          <Badge className="bg-amber-100 text-amber-800 hover:bg-amber-100">
            警告
          </Badge>
        )
      default:
        return <Badge variant="outline">{status}</Badge>
    }
  }

  const getStatusIcon = (status: string) => {
    switch (status) {
      case 'success':
        return <CheckCircle2 className="h-5 w-5 text-green-500" />
      case 'failed':
        return <AlertCircle className="h-5 w-5 text-red-500" />
      case 'warning':
        return <AlertTriangle className="h-5 w-5 text-amber-500" />
      case 'running':
        return <Play className="h-5 w-5 text-blue-500" />
      default:
        return <Pause className="h-5 w-5 text-gray-500" />
    }
  }

  return (
    <MainLayout>
      <div className="container mx-auto py-6 space-y-6 min-h-full">
        <div className="flex items-center justify-between">
          <h1 className="text-3xl font-bold tracking-tight">监控仪表盘</h1>
          <div className="flex items-center gap-2">
            <Button variant="outline" size="sm" className="gap-2">
              <Calendar className="h-4 w-4" />
              最近7天
            </Button>
            <Button variant="outline" size="sm">
              <ArrowUpDown className="mr-2 h-4 w-4" />
              排序
            </Button>
            <div className="relative ml-4">
              <Search className="absolute left-2.5 top-2.5 h-4 w-4 text-gray-500 dark:text-gray-400" />
              <Input
                type="search"
                placeholder="搜索执行记录..."
                className="w-[200px] pl-8 md:w-[300px] lg:w-[300px]"
              />
            </div>
            <Button variant="ghost" size="icon">
              <Filter className="h-4 w-4" />
              <span className="sr-only">筛选</span>
            </Button>
          </div>
        </div>

        <div className="grid gap-6 md:grid-cols-2 lg:grid-cols-4">
          <Card>
            <CardHeader className="pb-2">
              <CardTitle className="text-sm font-medium">总执行次数</CardTitle>
            </CardHeader>
            <CardContent>
              <div className="text-2xl font-bold">235</div>
              <p className="text-xs text-gray-500">较上周 +12%</p>
            </CardContent>
          </Card>
          <Card>
            <CardHeader className="pb-2">
              <CardTitle className="text-sm font-medium">成功率</CardTitle>
            </CardHeader>
            <CardContent>
              <div className="text-2xl font-bold text-green-600">94.5%</div>
              <p className="text-xs text-gray-500">较上周 +2.3%</p>
            </CardContent>
          </Card>
          <Card>
            <CardHeader className="pb-2">
              <CardTitle className="text-sm font-medium">
                平均执行时间
              </CardTitle>
            </CardHeader>
            <CardContent>
              <div className="text-2xl font-bold">1分45秒</div>
              <p className="text-xs text-gray-500">较上周 -15秒</p>
            </CardContent>
          </Card>
          <Card>
            <CardHeader className="pb-2">
              <CardTitle className="text-sm font-medium">活跃工作流</CardTitle>
            </CardHeader>
            <CardContent>
              <div className="text-2xl font-bold">12</div>
              <p className="text-xs text-gray-500">较上周 +3</p>
            </CardContent>
          </Card>
        </div>

        {/* Use the client component for charts */}
        <MonitoringCharts
          executionData={executionData}
          durationData={durationData}
        />

        <div>
          <Card>
            <CardHeader>
              <CardTitle>最近执行记录</CardTitle>
              <CardDescription>最新工作流运行及其状态</CardDescription>
            </CardHeader>
            <CardContent>
              <div className="overflow-x-auto">
                <table className="w-full">
                  <thead>
                    <tr className="border-b text-left text-sm font-medium text-gray-500">
                      <th className="pb-3 pl-4">ID</th>
                      <th className="pb-3">工作流</th>
                      <th className="pb-3">状态</th>
                      <th className="pb-3">开始时间</th>
                      <th className="pb-3">执行时间</th>
                      <th className="pb-3">触发方式</th>
                      <th className="pb-3 pr-4">操作</th>
                    </tr>
                  </thead>
                  <tbody>
                    {executions.map((execution) => (
                      <tr
                        key={execution.id}
                        className="border-b hover:bg-gray-50">
                        <td className="py-3 pl-4 text-sm font-medium">
                          {execution.id}
                        </td>
                        <td className="py-3 text-sm">{execution.workflow}</td>
                        <td className="py-3">
                          <div className="flex items-center gap-2">
                            {getStatusIcon(execution.status)}
                            {getStatusBadge(execution.status)}
                          </div>
                        </td>
                        <td className="py-3 text-sm">{execution.startTime}</td>
                        <td className="py-3 text-sm">{execution.duration}</td>
                        <td className="py-3 text-sm">{execution.trigger}</td>
                        <td className="py-3 pr-4">
                          <Button variant="ghost" size="sm">
                            查看详情
                          </Button>
                        </td>
                      </tr>
                    ))}
                  </tbody>
                </table>
              </div>
            </CardContent>
          </Card>
        </div>
      </div>
    </MainLayout>
  )
}
