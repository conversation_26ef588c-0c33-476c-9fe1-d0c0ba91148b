import { Skeleton } from "@/components/ui/skeleton"
import MainLayout from "@/components/layout/main-layout"

export default function DatabaseManagementLoading() {
  return (
    <MainLayout>
      <div className="p-6">
        <div className="container">
          <div className="flex items-center justify-between">
            <Skeleton className="h-10 w-[200px]" />
            <div className="flex items-center gap-2">
              <Skeleton className="h-9 w-[100px]" />
              <Skeleton className="h-9 w-[100px]" />
              <Skeleton className="h-9 w-[100px]" />
            </div>
          </div>

          <div className="mt-6">
            <Skeleton className="h-10 w-full mb-4" />
            <Skeleton className="h-[500px] w-full rounded-md" />
          </div>
        </div>
      </div>
    </MainLayout>
  )
}
