import { useEffect, useState, useRef } from 'react'
import { useNavigate } from 'react-router-dom'
import { Button } from '@/components/ui/button'
import { Progress } from '@/components/ui/progress'
import {
  AlertCircle,
  RefreshCw,
  WifiOff,
  Server,
  CheckCircle2,
  XCircle,
  ArrowLeft,
  ClipboardCopy,
  Settings,
} from 'lucide-react'
import { toast } from '@/components/ui/use-toast'
import { SysAuthApi } from '@/lib/api-services/apis/sys-auth-api'
import { getAPI, getRequestConfig } from '@/lib/api-services/axios-utils'
import { cn } from '@/lib/utils'
import { log } from '@/lib/utils/logger'

// 常量定义 - 用于拦截和限制框架请求
const NEXTJS_STACK_FRAME_URL_PATTERN = /__nextjs_original-stack-frame/

export default function NetworkErrorPage() {
  const navigate = useNavigate()
  const [isReconnecting, setIsReconnecting] = useState(false)
  const [reconnectAttempts, setReconnectAttempts] = useState(0)
  const [reconnectProgress, setReconnectProgress] = useState(0)
  const [lastHeartbeatTime, setLastHeartbeatTime] = useState<Date | null>(null)
  const [connectionStatus, setConnectionStatus] = useState<
    'disconnected' | 'connecting' | 'connected'
  >('disconnected')
  const [errorDetails, setErrorDetails] = useState<string | null>(null)
  const [services, setServices] = useState([
    { name: 'API 服务', status: 'unknown' },
    { name: 'WebSocket 服务', status: 'unknown' },
    { name: '数据库服务', status: 'unknown' },
  ])

  // 添加诊断模式状态
  const [diagnosticMode, setDiagnosticMode] = useState(false)
  const [diagnosticLogs, setDiagnosticLogs] = useState<string[]>([])

  // 添加诊断日志的函数
  const addDiagnosticLog = (message: string) => {
    // 使用统一的Logger系统替代console.log
    const timestamp = new Date().toLocaleTimeString()
    const logEntry = `[${timestamp}] ${message}`

    // 使用Logger记录诊断信息
    log.debug(`网络诊断: ${logEntry}`)

    // 只有在诊断模式且组件仍然挂载时才更新UI
    if (diagnosticMode && isMountedRef.current) {
      setDiagnosticLogs((prev) => [...prev, logEntry])
    }
  }

  // 创建SysAuthApi实例
  const sysAuthApi = getAPI(SysAuthApi)

  // Maximum number of reconnection attempts
  const MAX_RECONNECT_ATTEMPTS = 10 // 增加到10次
  // Delay between reconnection attempts in milliseconds (increases with each attempt)
  const BASE_RECONNECT_DELAY = 2000 // 从3000减少到2000ms初始延迟
  // Maximum delay cap to prevent very long delays
  const MAX_RECONNECT_DELAY = 30000 // 设置最大延迟为30秒

  // 添加组件挂载状态标记
  const isMountedRef = useRef(true)

  // 添加定时器引用跟踪数组
  const timeoutRefsRef = useRef<NodeJS.Timeout[]>([])

  // 记录上次重连尝试时间，用于避免过于频繁的重连
  const lastReconnectTimeRef = useRef<number>(0)

  // 添加定时器管理辅助函数
  const addTimeout = (callback: () => void, delay: number): NodeJS.Timeout => {
    // 创建包装的回调函数，确保只在组件挂载时执行
    const wrappedCallback = () => {
      if (isMountedRef.current) {
        callback()
      }
    }

    // 设置定时器并保存引用
    const timeoutId = setTimeout(wrappedCallback, delay)
    timeoutRefsRef.current.push(timeoutId)
    return timeoutId
  }

  // 清理所有定时器
  const clearAllTimeouts = () => {
    timeoutRefsRef.current.forEach((timeoutId) => {
      clearTimeout(timeoutId)
    })
    timeoutRefsRef.current = []
  }

  // Function to check server connection
  const checkConnection = async () => {
    try {
      // 使用心跳接口检查连接状态
      setConnectionStatus('connecting')

      // 尝试3次，增加检测可靠性
      for (let attempt = 1; attempt <= 3; attempt++) {
        try {
          console.log(`心跳检测尝试 ${attempt}/3...`)

          // 使用较短的超时时间
          const response = await sysAuthApi.heartBeat(
            getRequestConfig({
              timeout: 4000, // 4秒超时
              silentError: true, // 不显示错误提示
            })
          )

          // 检查响应状态和数据
          if (response && response.status === 200 && response.data) {
            // 验证响应数据的有效性 - 通常RESTfulResultInt32应该有data字段
            if (response.data.hasOwnProperty('data')) {
              console.log('心跳API响应成功:', response.data)
              setConnectionStatus('connected')
              setLastHeartbeatTime(new Date())
              setErrorDetails(null)
              return true
            } else {
              console.warn('心跳API响应缺少预期的data字段:', response.data)
              // 继续尝试下一次
            }
          } else {
            console.warn('心跳API响应无效:', response)
            // 继续尝试下一次
          }
        } catch (heartbeatError) {
          console.warn(`心跳API尝试${attempt}失败:`, heartbeatError)

          // 如果不是最后一次尝试，等待一小段时间后继续
          if (attempt < 3) {
            await new Promise((resolve) => setTimeout(resolve, 1000))
            continue
          }

          // 记录详细的错误信息
          const errorMessage =
            heartbeatError instanceof Error
              ? heartbeatError.message
              : '未知心跳检测错误'

          setErrorDetails(errorMessage)
        }
      }

      // 所有尝试都失败了
      console.error('所有心跳检测尝试均失败')
      setConnectionStatus('disconnected')
      return false
    } catch (error) {
      console.error('连接检查失败:', error)
      setConnectionStatus('disconnected')
      setErrorDetails(error instanceof Error ? error.message : '未知错误')
      return false
    }
  }

  // Function to check individual service status
  const checkServices = async () => {
    // 尝试检查API服务状态 - 使用心跳API
    let apiServiceOnline = false
    try {
      const response = await sysAuthApi.heartBeat(
        getRequestConfig({
          timeout: 3000,
          silentError: true,
        })
      )
      // 确保响应不仅状态码正确，而且数据也符合预期
      apiServiceOnline =
        response?.status === 200 && response?.data?.hasOwnProperty('data')
      console.log(
        'API service check result:',
        apiServiceOnline ? 'online' : 'offline'
      )
    } catch (error) {
      console.error('API service check failed:', error)
      apiServiceOnline = false
    }

    // WebSocket服务检查 - 在实际应用中应实现真正的WebSocket检查
    let websocketServiceOnline = false
    try {
      // 这里仍使用API状态作为参考，但在生产环境中应该实现真正的WebSocket检查
      // 例如尝试创建WebSocket连接并发送ping/pong消息
      websocketServiceOnline = apiServiceOnline
    } catch (wsError) {
      console.error('WebSocket service check failed:', wsError)
      websocketServiceOnline = false
    }

    // 数据库服务状态 - 通常需要通过API间接检查
    let dbServiceOnline = false
    try {
      // 在实际应用中，可以通过特定的数据库健康检查API来确认数据库状态
      // 这里仍使用API服务状态作为参考
      dbServiceOnline = apiServiceOnline
    } catch (dbError) {
      console.error('Database service check failed:', dbError)
      dbServiceOnline = false
    }

    // 更新服务状态
    const updatedServices = [
      { name: 'API 服务', status: apiServiceOnline ? 'online' : 'offline' },
      {
        name: 'WebSocket 服务',
        status: websocketServiceOnline ? 'online' : 'offline',
      },
      { name: '数据库服务', status: dbServiceOnline ? 'online' : 'offline' },
    ]

    console.log('Updated service statuses:', updatedServices)
    setServices(updatedServices)

    // 返回整体服务状态 - 如果关键服务在线则认为服务可用
    return apiServiceOnline
  }

  // Function to handle reconnection attempts
  const attemptReconnection = async () => {
    // 如果组件已卸载，不执行重连
    if (!isMountedRef.current) {
      console.log('组件已卸载，取消重连尝试')
      return
    }

    // 检查是否距离上次重连尝试时间太短
    const now = Date.now()
    const minReconnectInterval = 2000 // 最小重连间隔2秒
    if (now - lastReconnectTimeRef.current < minReconnectInterval) {
      console.log(
        `重连尝试间隔太短，等待至少 ${minReconnectInterval}ms 后再尝试`
      )
      return // 如果间隔太短，不执行重连
    }

    // 更新上次重连尝试时间
    lastReconnectTimeRef.current = now

    setIsReconnecting(true)
    setReconnectAttempts((prev) => prev + 1)
    setReconnectProgress(0)

    // 计算当前重试延迟时间（使用指数退避但设置上限）
    // 添加一个小的随机延迟(0-1000ms)以避免多个客户端同时重试
    const randomJitter = Math.floor(Math.random() * 1000)
    const currentDelay = Math.min(
      BASE_RECONNECT_DELAY * Math.pow(1.8, reconnectAttempts) + randomJitter,
      MAX_RECONNECT_DELAY
    )

    // 计算进度更新间隔（保证进度条平滑）
    const progressUpdateInterval = Math.max(currentDelay / 30, 50)

    // 显示当前尝试信息
    console.log(
      `尝试第 ${reconnectAttempts}/${MAX_RECONNECT_ATTEMPTS} 次重连，延迟: ${currentDelay}ms (包含随机抖动: ${randomJitter}ms)`
    )

    // 模拟进度
    const progressInterval = setInterval(() => {
      // 如果组件已卸载，清理定时器并返回
      if (!isMountedRef.current) {
        clearInterval(progressInterval)
        return
      }

      setReconnectProgress((prev) => {
        if (prev >= 90) {
          // 最多到90%，剩余10%留给实际连接检查
          clearInterval(progressInterval)
          return 90
        }
        return prev + 100 / (currentDelay / progressUpdateInterval)
      })
    }, progressUpdateInterval)

    // 等待重连延迟
    await new Promise((resolve) => setTimeout(resolve, currentDelay))

    // 如果组件已卸载，清理资源并返回
    if (!isMountedRef.current) {
      clearInterval(progressInterval)
      return
    }

    // 检查连接
    let isConnected = false
    try {
      isConnected = await checkConnection()
    } catch (error) {
      console.error('Connection check error during reconnection:', error)
      isConnected = false
    }

    // 如果组件已卸载，不继续执行
    if (!isMountedRef.current) {
      clearInterval(progressInterval)
      return
    }

    clearInterval(progressInterval)
    setReconnectProgress(100)

    if (isConnected) {
      // 连接看起来已恢复，但需要二次确认
      console.log('连接看起来已恢复，进行二次确认...')

      // 稍等一会再进行二次确认，确保连接稳定
      await new Promise((resolve) => setTimeout(resolve, 2000))

      // 如果组件已卸载，不继续执行
      if (!isMountedRef.current) return

      // 二次确认连接
      let secondConfirmationSuccess = false
      try {
        secondConfirmationSuccess = await checkConnection()
      } catch (error) {
        console.error('二次确认失败:', error)
        secondConfirmationSuccess = false
      }

      // 如果组件已卸载，不继续执行
      if (!isMountedRef.current) return

      if (!secondConfirmationSuccess) {
        console.warn('二次确认失败，连接可能不稳定，继续重连尝试')
        setIsReconnecting(false)

        // 短暂延迟后尝试下一次重连
        addTimeout(() => {
          attemptReconnection()
        }, 3000)

        return
      }

      // 连接确实恢复
      console.log('连接已确认恢复')

      toast({
        title: '连接已恢复',
        description: '已成功重新连接到服务器',
        variant: 'default',
      })

      // 检查服务状态
      try {
        const servicesOnline = await checkServices()

        // 如果组件已卸载，不继续执行
        if (!isMountedRef.current) return

        // 只有当服务状态检查也成功时才返回首页
        if (servicesOnline) {
          // 短暂延迟后返回上一页
          console.log('服务状态正常，准备返回首页...')
          addTimeout(() => {
            if (isMountedRef.current) {
              navigate(-1)
            }
          }, 2000)
        } else {
          console.warn('服务状态异常，虽然连接已恢复，但不返回首页')
          setIsReconnecting(false)
          toast({
            title: '部分服务异常',
            description: '已连接到服务器，但部分服务可能不可用',
            variant: 'warning',
          })
        }
      } catch (error) {
        console.warn('Service check failed after reconnection:', error)
        setIsReconnecting(false)
      }
    } else {
      setIsReconnecting(false)

      // 如果达到最大尝试次数，停止尝试
      if (reconnectAttempts >= MAX_RECONNECT_ATTEMPTS) {
        toast({
          title: '重连失败',
          description: `已达到最大重试次数 (${MAX_RECONNECT_ATTEMPTS})`,
          variant: 'destructive',
        })
      } else {
        // 否则，短暂延迟后自动再次尝试
        // 延迟逐渐增加，避免频繁重试
        const nextRetryDelay = Math.min(1000 * reconnectAttempts, 5000)
        console.log(`将在 ${nextRetryDelay}ms 后进行下一次重连尝试`)

        addTimeout(() => {
          attemptReconnection()
        }, nextRetryDelay)
      }
    }
  }

  // Start reconnection on page load
  useEffect(() => {
    console.log('网络错误页面已加载，开始连接检查流程')

    // 设置组件挂载状态为true
    isMountedRef.current = true

    // 初始化连接状态
    setConnectionStatus('disconnected')

    // 设置一个短暂延迟，确保UI渲染完成
    const initialTimeoutId = addTimeout(() => {
      attemptReconnection()
    }, 500)

    // Setup heartbeat interval
    const heartbeatInterval = setInterval(async () => {
      // 如果组件已卸载，不执行操作
      if (!isMountedRef.current) return

      // 定期检查连接状态，但不自动返回首页
      try {
        const isConnected = await checkConnection()
        console.log(
          `定期心跳检查结果: ${isConnected ? '连接正常' : '连接断开'}`
        )

        // 如果连接已恢复但页面还在网络错误页面，提示用户可以返回
        if (isConnected && !isReconnecting) {
          toast({
            title: '连接已恢复',
            description: '您可以点击"返回上一页"按钮继续操作',
            variant: 'default',
          })
        }
      } catch (error) {
        console.error('定期心跳检查失败:', error)
      }
    }, 30000) // Check every 30 seconds

    // 组件卸载时的清理函数
    return () => {
      log.debug('网络错误页面卸载，清理所有资源')

      // 标记组件已卸载
      isMountedRef.current = false

      // 清理心跳检测定时器
      clearInterval(heartbeatInterval)

      // 清理所有setTimeout
      clearAllTimeouts()

      // 取消所有进行中的网络请求
      try {
        // 尝试取消所有包含heartbeat的请求
        import('@/lib/api-services/axios-utils')
          .then(({ cancelRequestsByUrlPattern }) => {
            cancelRequestsByUrlPattern(/heartbeat/i, '页面已卸载，取消心跳请求')
          })
          .catch((err) => {
            log.error('取消请求失败:', err)
          })
      } catch (error) {
        log.error('取消请求操作失败:', error)
      }
    }
  }, [])

  // Handle manual reconnection
  const handleManualReconnect = () => {
    setReconnectAttempts(0)
    attemptReconnection()
  }

  // Handle go back
  const handleGoBack = () => {
    navigate(-1)
  }

  // 前往服务器配置页面
  const goToServerConfig = () => {
    navigate('/settings/config?tab=api')
  }

  // 切换诊断模式
  const toggleDiagnosticMode = () => {
    setDiagnosticMode((prev) => !prev)
    if (!diagnosticMode) {
      // 开启诊断模式时添加初始日志
      addDiagnosticLog('诊断模式已开启')
      addDiagnosticLog(`当前连接状态: ${connectionStatus}`)
      addDiagnosticLog(`已尝试重连次数: ${reconnectAttempts}`)
      addDiagnosticLog(
        `组件挂载状态: ${isMountedRef.current ? '已挂载' : '已卸载'}`
      )
      addDiagnosticLog(`活动定时器数量: ${timeoutRefsRef.current.length}`)

      // 添加API基础URL信息
      try {
        const apiBaseUrl =
          import.meta.env.VITE_API_BASE_URL ||
          import.meta.env.NEXT_PUBLIC_API_BASE_URL ||
          '未设置'
        addDiagnosticLog(`API基础URL: ${apiBaseUrl}`)
      } catch (e) {
        addDiagnosticLog('无法获取API基础URL')
      }
    } else {
      addDiagnosticLog('诊断模式已关闭')
    }
  }

  // 执行诊断测试
  const runDiagnosticTest = async () => {
    if (!diagnosticMode) return

    addDiagnosticLog('开始运行诊断测试...')
    addDiagnosticLog(
      `组件挂载状态: ${isMountedRef.current ? '已挂载' : '已卸载'}`
    )
    addDiagnosticLog(`活动定时器数量: ${timeoutRefsRef.current.length}`)

    // 测试API心跳
    try {
      addDiagnosticLog('测试心跳接口...')
      const response = await sysAuthApi.heartBeat(
        getRequestConfig({
          timeout: 5000,
          silentError: true,
        })
      )
      addDiagnosticLog(`心跳接口响应状态: ${response?.status || 'unknown'}`)
      if (response?.data) {
        addDiagnosticLog(`心跳接口响应数据: ${JSON.stringify(response.data)}`)
      }
    } catch (error) {
      addDiagnosticLog(
        `心跳接口测试失败: ${
          error instanceof Error ? error.message : '未知错误'
        }`
      )
    }

    // 测试网络连接
    try {
      addDiagnosticLog('测试基本网络连接...')
      const response = await fetch('https://www.baidu.com', {
        method: 'HEAD',
        cache: 'no-store',
        signal: AbortSignal.timeout(5000),
      })
      addDiagnosticLog(`公共网站连接测试结果: ${response.ok ? '成功' : '失败'}`)
    } catch (error) {
      addDiagnosticLog(
        `公共网站连接测试失败: ${
          error instanceof Error ? error.message : '未知错误'
        }`
      )
    }

    addDiagnosticLog('诊断测试完成')
  }

  return (
    <div className="min-h-screen bg-gradient-to-b from-gray-50 to-gray-100 flex flex-col items-center justify-center p-4 sm:p-6">
      <div className="w-full max-w-4xl bg-white rounded-xl shadow-xl overflow-hidden transition-all duration-300 border border-gray-100">
        {/* Header */}
        <div className="bg-gradient-to-r from-red-50 to-rose-50 p-6 border-b border-red-100">
          <div className="flex items-center">
            <div className="h-14 w-14 rounded-full bg-red-100 flex items-center justify-center mr-4 shadow-sm">
              <WifiOff className="h-7 w-7 text-red-600" />
            </div>
            <div className="flex-1">
              <h1 className="text-xl sm:text-2xl font-bold text-red-800">
                网络连接中断
              </h1>
              <p className="text-red-600 text-sm sm:text-base mt-1">
                无法连接到服务器，正在尝试重新连接...
              </p>
            </div>
          </div>
        </div>

        {/* Connection Status */}
        <div className="p-6 border-b border-gray-100 bg-white">
          <div className="flex items-center justify-between mb-4">
            <h2 className="text-lg font-semibold text-gray-800">连接状态</h2>
            <div className="flex items-center">
              {connectionStatus === 'connected' ? (
                <span className="flex items-center text-green-600 bg-green-50 px-3 py-1 rounded-full text-sm font-medium animate-pulse">
                  <CheckCircle2 className="h-4 w-4 mr-1.5" /> 已连接
                </span>
              ) : connectionStatus === 'connecting' ? (
                <span className="flex items-center text-amber-600 bg-amber-50 px-3 py-1 rounded-full text-sm font-medium">
                  <RefreshCw className="h-4 w-4 mr-1.5 animate-spin" /> 连接中
                </span>
              ) : (
                <span className="flex items-center text-red-600 bg-red-50 px-3 py-1 rounded-full text-sm font-medium">
                  <XCircle className="h-4 w-4 mr-1.5" /> 已断开
                </span>
              )}
            </div>
          </div>

          {lastHeartbeatTime && (
            <p className="text-sm text-gray-500 mb-3 flex items-center">
              <span className="inline-block w-3 h-3 bg-green-400 rounded-full mr-2 animate-pulse"></span>
              上次心跳时间: {lastHeartbeatTime.toLocaleTimeString()}
            </p>
          )}

          {errorDetails && (
            <div className="bg-red-50 p-4 rounded-lg mb-4 text-sm text-red-700 border border-red-100 shadow-sm">
              <div className="font-semibold flex items-center mb-1">
                <AlertCircle className="h-4 w-4 mr-1.5" /> 错误详情:
              </div>
              <div className="ml-6">{errorDetails}</div>
            </div>
          )}

          {/* Reconnection Progress */}
          {isReconnecting && (
            <div className="mt-5 bg-white p-4 rounded-lg border border-gray-100 shadow-sm">
              <div className="flex justify-between text-sm mb-2">
                <span className="font-medium text-gray-700">重连进度</span>
                <span className="font-medium text-gray-700">
                  尝试 {reconnectAttempts}/{MAX_RECONNECT_ATTEMPTS}
                </span>
              </div>
              <Progress
                value={reconnectProgress}
                className="h-2.5 bg-gray-100 [&>div]:bg-gradient-to-r [&>div]:from-blue-500 [&>div]:to-indigo-500"
              />
            </div>
          )}
        </div>

        {/* Service Status */}
        <div className="p-6 border-b border-gray-100 bg-white">
          <h2 className="text-lg font-semibold text-gray-800 mb-4">服务状态</h2>
          <div className="grid gap-3 sm:grid-cols-3">
            {services.map((service, index) => (
              <div
                key={index}
                className={cn(
                  'flex flex-col p-4 rounded-lg border transition-all duration-200 shadow-sm',
                  service.status === 'online'
                    ? 'bg-green-50 border-green-100'
                    : service.status === 'offline'
                    ? 'bg-red-50 border-red-100'
                    : 'bg-gray-50 border-gray-200'
                )}>
                <div className="flex items-center justify-between mb-2">
                  <div className="flex items-center">
                    <Server className="h-4 w-4 mr-2 text-gray-500" />
                    <span className="font-medium text-gray-700">
                      {service.name}
                    </span>
                  </div>
                  {service.status === 'online' ? (
                    <span className="flex h-6 items-center justify-center px-2.5 py-0.5 rounded-full text-xs font-medium bg-green-100 text-green-800">
                      在线
                    </span>
                  ) : service.status === 'offline' ? (
                    <span className="flex h-6 items-center justify-center px-2.5 py-0.5 rounded-full text-xs font-medium bg-red-100 text-red-800">
                      离线
                    </span>
                  ) : (
                    <span className="flex h-6 items-center justify-center px-2.5 py-0.5 rounded-full text-xs font-medium bg-gray-100 text-gray-600">
                      未知
                    </span>
                  )}
                </div>
                <div className="mt-1">
                  <div className="h-1.5 w-full rounded-full bg-gray-200 overflow-hidden">
                    <div
                      className={cn(
                        'h-full rounded-full',
                        service.status === 'online'
                          ? 'bg-green-500'
                          : service.status === 'offline'
                          ? 'bg-red-500 w-[15%]'
                          : 'bg-gray-400 w-[5%]'
                      )}
                      style={{
                        width:
                          service.status === 'online'
                            ? '100%'
                            : service.status === 'offline'
                            ? '15%'
                            : '5%',
                      }}
                    />
                  </div>
                </div>
              </div>
            ))}
          </div>
        </div>

        {/* 服务器配置提示 */}
        <div className="p-6 border-b border-gray-100 bg-gradient-to-r from-indigo-50 to-blue-50">
          <div className="flex items-start">
            <div className="h-12 w-12 rounded-full bg-indigo-100 flex items-center justify-center mr-4 shadow-sm flex-shrink-0">
              <Settings className="h-6 w-6 text-indigo-600" />
            </div>
            <div>
              <h2 className="text-lg font-semibold text-indigo-800 mb-2">
                服务器配置可能需要更新
              </h2>
              <p className="text-indigo-700 text-sm mb-3">
                如果您确认网络连接正常但仍无法连接到服务器，可能是服务器地址配置不正确。
                您可以点击下方的"配置服务器"按钮，检查并更新服务器IP地址和端口。
              </p>
            </div>
          </div>
        </div>

        {/* Actions */}
        <div className="p-6 flex flex-wrap gap-3 bg-gray-50 border-t border-gray-100">
          <Button
            variant="outline"
            className="flex items-center shadow-sm hover:bg-gray-100 transition-all duration-200 hover:scale-105"
            onClick={handleGoBack}>
            <ArrowLeft className="h-4 w-4 mr-2" />
            返回上一页
          </Button>

          <Button
            onClick={handleManualReconnect}
            disabled={isReconnecting}
            className={cn(
              'flex items-center transition-all duration-200 hover:scale-105 shadow-sm',
              isReconnecting
                ? 'opacity-70 cursor-not-allowed'
                : 'hover:bg-blue-600'
            )}>
            <RefreshCw
              className={`h-4 w-4 mr-2 ${isReconnecting ? 'animate-spin' : ''}`}
            />
            {isReconnecting ? '重连中...' : '手动重连'}
          </Button>

          <Button
            variant="outline"
            className="flex items-center ml-auto shadow-sm hover:bg-gray-100 transition-all duration-200 hover:scale-105"
            onClick={() => (window.location.href = '/')}>
            返回首页
          </Button>

          <Button
            variant="default"
            className="flex items-center shadow-sm bg-purple-600 hover:bg-purple-700 transition-all duration-200 hover:scale-105"
            onClick={goToServerConfig}>
            <Settings className="h-4 w-4 mr-2" />
            配置服务器
          </Button>

          <Button
            variant={diagnosticMode ? 'default' : 'outline'}
            className={cn(
              'flex items-center shadow-sm transition-all duration-200 hover:scale-105',
              diagnosticMode
                ? 'bg-amber-600 hover:bg-amber-700 text-white'
                : 'hover:bg-amber-50 hover:text-amber-700 hover:border-amber-200'
            )}
            onClick={toggleDiagnosticMode}>
            <AlertCircle className="h-4 w-4 mr-2" />
            {diagnosticMode ? '关闭诊断' : '开启诊断'}
          </Button>

          {diagnosticMode && (
            <Button
              variant="outline"
              className="flex items-center shadow-sm hover:bg-blue-50 hover:text-blue-700 hover:border-blue-200 transition-all duration-200 hover:scale-105"
              onClick={runDiagnosticTest}>
              <Server className="h-4 w-4 mr-2" />
              运行诊断测试
            </Button>
          )}
        </div>

        {/* 诊断日志 */}
        {diagnosticMode && (
          <div className="p-6 border-t border-gray-100 bg-gradient-to-b from-gray-50 to-gray-100">
            <div className="flex items-center justify-between mb-3">
              <h3 className="text-md font-semibold text-gray-800 flex items-center">
                <span className="inline-block w-2 h-2 bg-amber-500 rounded-full mr-2 animate-pulse"></span>
                诊断日志
              </h3>
              <div className="flex gap-2">
                <Button
                  variant="outline"
                  size="sm"
                  onClick={() => setDiagnosticLogs([])}
                  className="h-8 px-3 text-xs shadow-sm hover:bg-red-50 hover:text-red-600 hover:border-red-200 transition-all">
                  <XCircle className="h-3.5 w-3.5 mr-1" />
                  清除日志
                </Button>
                <Button
                  variant="outline"
                  size="sm"
                  onClick={() => {
                    // 复制日志到剪贴板
                    const logText = diagnosticLogs.join('\n')
                    navigator.clipboard.writeText(logText)
                    toast({
                      title: '已复制',
                      description: '诊断日志已复制到剪贴板',
                      duration: 2000,
                    })
                  }}
                  className="h-8 px-3 text-xs shadow-sm hover:bg-blue-50 hover:text-blue-600 hover:border-blue-200 transition-all">
                  <ClipboardCopy className="h-3.5 w-3.5 mr-1" />
                  复制日志
                </Button>
              </div>
            </div>
            <div className="bg-gray-900 p-4 rounded-lg border border-gray-700 max-h-60 overflow-y-auto text-xs font-mono shadow-inner">
              {diagnosticLogs.length > 0 ? (
                <ul className="space-y-1 text-gray-300">
                  {diagnosticLogs.map((log, index) => {
                    // 根据日志内容添加不同的颜色
                    let textColor = 'text-gray-300'
                    if (log.includes('成功') || log.includes('online')) {
                      textColor = 'text-green-400'
                    } else if (
                      log.includes('失败') ||
                      log.includes('错误') ||
                      log.includes('Error')
                    ) {
                      textColor = 'text-red-400'
                    } else if (log.includes('警告') || log.includes('测试')) {
                      textColor = 'text-amber-400'
                    } else if (log.includes('API') || log.includes('接口')) {
                      textColor = 'text-blue-400'
                    }

                    return (
                      <li
                        key={index}
                        className={`break-all ${textColor} border-l-2 border-gray-700 pl-3 hover:bg-gray-800 py-1 rounded transition-colors`}>
                        {log}
                      </li>
                    )
                  })}
                </ul>
              ) : (
                <p className="text-gray-500 italic">
                  尚无诊断日志，点击"运行诊断测试"生成日志
                </p>
              )}
            </div>
          </div>
        )}
      </div>

      {/* Troubleshooting Tips */}
      <div className="w-full max-w-4xl bg-white rounded-xl shadow-xl p-6 mt-6 border border-gray-100 transition-all duration-300 hover:shadow-lg">
        <div className="flex items-center mb-4">
          <div className="h-10 w-10 rounded-full bg-amber-100 flex items-center justify-center mr-3 shadow-sm">
            <AlertCircle className="h-5 w-5 text-amber-600" />
          </div>
          <h2 className="text-lg font-bold text-gray-800">故障排除提示</h2>
        </div>
        <div className="grid sm:grid-cols-2 md:grid-cols-4 gap-4 mt-4">
          <div className="bg-gray-50 p-4 rounded-lg border border-gray-100 shadow-sm">
            <h3 className="font-medium text-gray-700 mb-2 flex items-center">
              <RefreshCw className="h-4 w-4 mr-2 text-blue-500" />
              连接问题
            </h3>
            <ul className="space-y-2 text-sm text-gray-600 ml-6 list-disc">
              <li>检查您的网络连接是否正常</li>
              <li>尝试刷新浏览器或重新启动应用</li>
              <li>检查网络设备（如路由器、交换机）是否工作正常</li>
            </ul>
          </div>

          <div className="bg-gray-50 p-4 rounded-lg border border-gray-100 shadow-sm">
            <h3 className="font-medium text-gray-700 mb-2 flex items-center">
              <Server className="h-4 w-4 mr-2 text-purple-500" />
              服务器问题
            </h3>
            <ul className="space-y-2 text-sm text-gray-600 ml-6 list-disc">
              <li>确认服务器是否在运行</li>
              <li>检查防火墙设置是否阻止了连接</li>
              <li>可能是服务器临时维护或更新</li>
            </ul>
          </div>

          <div className="bg-gray-50 p-4 rounded-lg border border-gray-100 shadow-sm">
            <h3 className="font-medium text-gray-700 mb-2 flex items-center">
              <AlertCircle className="h-4 w-4 mr-2 text-red-500" />
              客户端问题
            </h3>
            <ul className="space-y-2 text-sm text-gray-600 ml-6 list-disc">
              <li>尝试清除浏览器缓存并刷新页面</li>
              <li>检查浏览器扩展是否干扰连接</li>
              <li>尝试使用不同的浏览器或设备</li>
            </ul>
          </div>

          <div className="bg-gray-50 p-4 rounded-lg border border-gray-100 shadow-sm">
            <h3 className="font-medium text-gray-700 mb-2 flex items-center">
              <CheckCircle2 className="h-4 w-4 mr-2 text-green-500" />
              获取帮助
            </h3>
            <ul className="space-y-2 text-sm text-gray-600 ml-6 list-disc">
              <li>打开诊断模式获取详细错误信息</li>
              <li>复制诊断日志以便提供给技术支持</li>
              <li>如果问题持续存在，请联系系统管理员</li>
            </ul>
          </div>
        </div>
      </div>
    </div>
  )
}
