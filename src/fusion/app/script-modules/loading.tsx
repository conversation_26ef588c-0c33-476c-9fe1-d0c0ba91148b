import { Skeleton } from '@/components/ui/skeleton'
import { MainLayout } from '@/components/layout/main-layout'
import { NavSidebar } from '@/components/layout/nav-sidebar'

export default function ScriptModulesLoading() {
  // 侧边栏配置
  const sidebarItems = [
    { title: '脚本模块', href: '/script-modules', icon: 'Code' },
    {
      title: '模块分类',
      href: '/script-modules/categories',
      icon: 'FolderTree',
    },
    {
      title: '导入导出',
      href: '/script-modules/import-export',
      icon: 'FileImport',
    },
    { title: '使用统计', href: '/script-modules/stats', icon: 'BarChart' },
  ]

  return (
    <MainLayout>
      <div className="flex h-screen overflow-hidden">
        <NavSidebar items={sidebarItems} />

        <div className="flex-1 flex flex-col overflow-hidden">
          <div className="flex items-center justify-between p-4 border-b">
            <Skeleton className="h-8 w-48" />
            <Skeleton className="h-10 w-32" />
          </div>

          <div className="flex-1 overflow-auto p-4">
            <div className="mb-4 flex items-center justify-between">
              <Skeleton className="h-10 w-64" />
              <div className="flex items-center space-x-2">
                <Skeleton className="h-10 w-24" />
                <Skeleton className="h-10 w-24" />
              </div>
            </div>

            <Skeleton className="h-12 w-full mb-4" />

            <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-4">
              {Array.from({ length: 6 }).map((_, i) => (
                <div key={i} className="border rounded-lg p-4 space-y-3">
                  <div className="flex justify-between">
                    <Skeleton className="h-6 w-32" />
                    <Skeleton className="h-6 w-20" />
                  </div>
                  <Skeleton className="h-4 w-full" />
                  <div className="flex gap-2">
                    <Skeleton className="h-6 w-16" />
                    <Skeleton className="h-6 w-16" />
                  </div>
                  <Skeleton className="h-20 w-full" />
                  <div className="flex justify-between">
                    <div className="flex gap-2">
                      <Skeleton className="h-8 w-20" />
                      <Skeleton className="h-8 w-20" />
                    </div>
                    <div className="flex gap-2">
                      <Skeleton className="h-8 w-20" />
                      <Skeleton className="h-8 w-20" />
                    </div>
                  </div>
                </div>
              ))}
            </div>
          </div>
        </div>
      </div>
    </MainLayout>
  )
}
