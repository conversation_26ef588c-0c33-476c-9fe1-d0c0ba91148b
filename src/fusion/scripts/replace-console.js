#!/usr/bin/env node

/**
 * 批量替换console调用脚本
 * 将项目中的console.log/error/warn/info替换为Logger调用
 */

const fs = require('fs')
const path = require('path')
const glob = require('glob')

// 颜色输出
const colors = {
  reset: '\x1b[0m',
  bright: '\x1b[1m',
  red: '\x1b[31m',
  green: '\x1b[32m',
  yellow: '\x1b[33m',
  blue: '\x1b[34m',
  cyan: '\x1b[36m'
}

function log(message, color = 'reset') {
  console.log(`${colors[color]}${message}${colors.reset}`)
}

// 需要排除的文件和目录
const excludePatterns = [
  '**/node_modules/**',
  '**/dist/**',
  '**/build/**',
  '**/.next/**',
  '**/console-override.ts',
  '**/logger.ts',
  '**/safe-execute.ts',
  '**/scripts/**',
  '**/*.js' // 排除JS文件，只处理TS/TSX文件
]

// 替换规则
const replacementRules = [
  {
    pattern: /console\.log\(/g,
    replacement: 'log.info(',
    import: "import { log } from '@/lib/utils/logger'"
  },
  {
    pattern: /console\.info\(/g,
    replacement: 'log.info(',
    import: "import { log } from '@/lib/utils/logger'"
  },
  {
    pattern: /console\.warn\(/g,
    replacement: 'log.warn(',
    import: "import { log } from '@/lib/utils/logger'"
  },
  {
    pattern: /console\.error\(/g,
    replacement: 'log.error(',
    import: "import { log } from '@/lib/utils/logger'"
  },
  {
    pattern: /console\.debug\(/g,
    replacement: 'log.debug(',
    import: "import { log } from '@/lib/utils/logger'"
  }
]

/**
 * 检查文件是否需要添加import
 */
function needsLoggerImport(content) {
  return replacementRules.some(rule => rule.pattern.test(content)) &&
         !content.includes("import { log } from '@/lib/utils/logger'") &&
         !content.includes("from '@/lib/utils/logger'")
}

/**
 * 添加Logger import
 */
function addLoggerImport(content) {
  // 查找最后一个import语句的位置
  const importRegex = /^import\s+.*?from\s+['"][^'"]+['"];?\s*$/gm
  const imports = content.match(importRegex) || []
  
  if (imports.length === 0) {
    // 如果没有import语句，在文件开头添加
    return "import { log } from '@/lib/utils/logger'\n\n" + content
  }
  
  // 在最后一个import语句后添加
  const lastImport = imports[imports.length - 1]
  const lastImportIndex = content.lastIndexOf(lastImport)
  const insertPosition = lastImportIndex + lastImport.length
  
  return content.slice(0, insertPosition) + 
         "\nimport { log } from '@/lib/utils/logger'" +
         content.slice(insertPosition)
}

/**
 * 处理单个文件
 */
function processFile(filePath) {
  try {
    const content = fs.readFileSync(filePath, 'utf8')
    let newContent = content
    let hasChanges = false
    
    // 应用替换规则
    replacementRules.forEach(rule => {
      if (rule.pattern.test(newContent)) {
        newContent = newContent.replace(rule.pattern, rule.replacement)
        hasChanges = true
      }
    })
    
    // 如果有变化且需要添加import
    if (hasChanges && needsLoggerImport(content)) {
      newContent = addLoggerImport(newContent)
    }
    
    // 写回文件
    if (hasChanges) {
      fs.writeFileSync(filePath, newContent, 'utf8')
      return true
    }
    
    return false
  } catch (error) {
    log(`处理文件失败 ${filePath}: ${error.message}`, 'red')
    return false
  }
}

/**
 * 获取需要处理的文件列表
 */
function getFilesToProcess() {
  const patterns = [
    'src/**/*.ts',
    'src/**/*.tsx'
  ]
  
  let files = []
  patterns.forEach(pattern => {
    const matches = glob.sync(pattern, {
      ignore: excludePatterns,
      absolute: true
    })
    files = files.concat(matches)
  })
  
  // 去重
  return [...new Set(files)]
}

/**
 * 主函数
 */
function main() {
  log('🚀 开始批量替换console调用...', 'cyan')
  
  const files = getFilesToProcess()
  log(`📁 找到 ${files.length} 个文件需要检查`, 'blue')
  
  let processedCount = 0
  let changedCount = 0
  
  files.forEach(file => {
    const relativePath = path.relative(process.cwd(), file)
    
    try {
      const hasChanges = processFile(file)
      processedCount++
      
      if (hasChanges) {
        changedCount++
        log(`✅ ${relativePath}`, 'green')
      } else {
        log(`⏭️  ${relativePath} (无需更改)`, 'yellow')
      }
    } catch (error) {
      log(`❌ ${relativePath}: ${error.message}`, 'red')
    }
  })
  
  log('\n📊 处理完成统计:', 'cyan')
  log(`   总文件数: ${files.length}`, 'blue')
  log(`   已处理: ${processedCount}`, 'blue')
  log(`   已修改: ${changedCount}`, 'green')
  log(`   跳过: ${files.length - processedCount}`, 'yellow')
  
  if (changedCount > 0) {
    log('\n🎉 console调用替换完成！', 'green')
    log('💡 建议运行以下命令检查结果:', 'cyan')
    log('   npm run type-check  # 检查类型错误', 'blue')
    log('   npm run lint        # 检查代码规范', 'blue')
  } else {
    log('\n✨ 没有找到需要替换的console调用', 'green')
  }
}

// 检查是否安装了glob
try {
  require('glob')
} catch (error) {
  log('❌ 缺少依赖: glob', 'red')
  log('请运行: npm install glob', 'yellow')
  process.exit(1)
}

// 运行主函数
if (require.main === module) {
  main()
}

module.exports = {
  processFile,
  getFilesToProcess,
  replacementRules
}
