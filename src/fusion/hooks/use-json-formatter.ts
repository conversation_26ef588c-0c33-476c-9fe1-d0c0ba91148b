/**
 * 迁移自: components/debug-tools/json-formatter.tsx
 * 迁移时间: 2025-01-18T11:30:00.000Z
 *
 * 迁移说明:
 * - 提取JSON格式化核心逻辑为可复用的Hook
 * - 支持美化、压缩、验证等功能
 * - 保持与debug-tools组件的功能一致性
 * - 为floating-assistant组件提供高级JSON处理能力
 *
 * 注意: 本Hook整合了debug-tools的核心JSON处理功能，实现工具复用
 */

import { useState, useCallback } from 'react'

export interface JsonFormatterState {
  input: string
  formattedJson: string
  compactJson: string
  error: string | null
  isValid: boolean
}

export interface JsonFormatterActions {
  setInput: (input: string) => void
  formatJson: () => void
  clearJson: () => void
  validateJson: (input: string) => boolean
  getFormattedOutput: (format: 'beautify' | 'minify') => string
}

// 去除JSON注释的辅助函数
const removeJsonComments = (json: string): string => {
  // 去除单行注释
  let result = json.replace(/\/\/.*$/gm, '')
  // 去除多行注释
  result = result.replace(/\/\*[\s\S]*?\*\//g, '')
  return result
}

export function useJsonFormatter(): JsonFormatterState & JsonFormatterActions {
  const [input, setInputState] = useState('')
  const [formattedJson, setFormattedJson] = useState('')
  const [compactJson, setCompactJson] = useState('')
  const [error, setError] = useState<string | null>(null)
  const [isValid, setIsValid] = useState(false)

  const setInput = useCallback((newInput: string) => {
    setInputState(newInput)
    // 自动验证输入
    if (newInput.trim()) {
      validateJson(newInput)
    } else {
      setError(null)
      setIsValid(false)
      setFormattedJson('')
      setCompactJson('')
    }
  }, [])

  const validateJson = useCallback((jsonInput: string): boolean => {
    try {
      if (!jsonInput.trim()) {
        setError(null)
        setIsValid(false)
        return false
      }

      // 去除注释
      const jsonWithoutComments = removeJsonComments(jsonInput)
      JSON.parse(jsonWithoutComments)
      
      setError(null)
      setIsValid(true)
      return true
    } catch (e) {
      const errorMessage = e instanceof Error ? e.message : '未知错误'
      setError(`JSON格式错误: ${errorMessage}`)
      setIsValid(false)
      return false
    }
  }, [])

  const formatJson = useCallback(() => {
    try {
      if (!input.trim()) {
        setFormattedJson('')
        setCompactJson('')
        setError(null)
        setIsValid(false)
        return
      }

      // 去除注释
      const jsonWithoutComments = removeJsonComments(input)
      const parsedJson = JSON.parse(jsonWithoutComments)

      const beautified = JSON.stringify(parsedJson, null, 2)
      const minified = JSON.stringify(parsedJson)

      setFormattedJson(beautified)
      setCompactJson(minified)
      setError(null)
      setIsValid(true)
    } catch (e) {
      const errorMessage = e instanceof Error ? e.message : '未知错误'
      setError(`JSON格式错误: ${errorMessage}`)
      setIsValid(false)
      setFormattedJson('')
      setCompactJson('')
    }
  }, [input])

  const clearJson = useCallback(() => {
    setInputState('')
    setFormattedJson('')
    setCompactJson('')
    setError(null)
    setIsValid(false)
  }, [])

  const getFormattedOutput = useCallback((format: 'beautify' | 'minify'): string => {
    return format === 'beautify' ? formattedJson : compactJson
  }, [formattedJson, compactJson])

  return {
    // State
    input,
    formattedJson,
    compactJson,
    error,
    isValid,
    // Actions
    setInput,
    formatJson,
    clearJson,
    validateJson,
    getFormattedOutput,
  }
}
