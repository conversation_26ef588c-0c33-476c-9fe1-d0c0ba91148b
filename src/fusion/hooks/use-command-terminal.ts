/**
 * 命令终端工具核心逻辑Hook
 *
 * 提供命令终端的核心功能
 * 支持常见的系统命令模拟
 */

import { useState, useRef, useCallback } from 'react'

export interface CommandOutputItem {
  type: 'input' | 'output' | 'error'
  content: string
}

export function useCommandTerminal() {
  const [commandInput, setCommandInput] = useState('')
  const [commandHistory, setCommandHistory] = useState<string[]>([])
  const [commandOutput, setCommandOutput] = useState<CommandOutputItem[]>([
    { type: 'output', content: "欢迎使用命令终端! 输入 'help' 查看可用命令。" },
  ])
  const [historyIndex, setHistoryIndex] = useState(-1)

  // 执行命令
  const executeCommand = useCallback((cmd: string) => {
    // 添加命令到历史
    setCommandHistory((prev) => [...prev, cmd])
    setHistoryIndex(-1)

    // 添加命令到输出
    setCommandOutput((prev) => [
      ...prev,
      { type: 'input', content: `> ${cmd}` },
    ])

    // 处理命令
    const args = cmd.trim().split(/\s+/)
    const command = args[0].toLowerCase()

    switch (command) {
      case 'help':
        if (args.length > 1) {
          // 显示特定命令或类别的帮助
          const helpTopic = args[1].toLowerCase()
          switch (helpTopic) {
            case 'system':
              setCommandOutput((prev) => [
                ...prev,
                {
                  type: 'output',
                  content: `系统管理命令:
service [start|stop|restart|status] [name] - 管理系统服务
process [list|kill] [id] - 管理系统进程
memory - 显示内存使用情况
cpu - 显示CPU使用情况
disk - 显示磁盘使用情况
uptime - 显示系统运行时间
reboot - 重启系统（模拟）
shutdown - 关闭系统（模拟）`,
                },
              ])
              break
            case 'network':
              setCommandOutput((prev) => [
                ...prev,
                {
                  type: 'output',
                  content: `网络命令:
ping [host] - 测试网络连接
ifconfig - 显示网络接口信息
netstat - 显示网络连接状态
route - 显示路由表
dns [lookup|flush] [domain] - DNS操作`,
                },
              ])
              break
            case 'file':
              setCommandOutput((prev) => [
                ...prev,
                {
                  type: 'output',
                  content: `文件操作命令:
ls [path] - 列出目录内容
cat [file] - 显示文件内容
mkdir [dir] - 创建目录
rm [file] - 删除文件
cp [source] [dest] - 复制文件
mv [source] [dest] - 移动文件`,
                },
              ])
              break
            case 'config':
              setCommandOutput((prev) => [
                ...prev,
                {
                  type: 'output',
                  content: `配置命令:
config list - 列出所有配置
config get [key] - 获取配置值
config set [key] [value] - 设置配置值
config reset [key] - 重置配置值
config export - 导出配置
config import - 导入配置`,
                },
              ])
              break
            case 'workflow':
              setCommandOutput((prev) => [
                ...prev,
                {
                  type: 'output',
                  content: `工作流命令:
workflow list - 列出所有工作流
workflow start [id] - 启动工作流
workflow stop [id] - 停止工作流
workflow status [id] - 查看工作流状态
workflow history [id] - 查看工作流历史
workflow export [id] - 导出工作流
workflow import - 导入工作流`,
                },
              ])
              break
            case 'device':
              setCommandOutput((prev) => [
                ...prev,
                {
                  type: 'output',
                  content: `设备命令:
device list - 列出所有设备
device info [id] - 查看设备详情
device connect [id] - 连接设备
device disconnect [id] - 断开设备
device reboot [id] - 重启设备
device tags [id] - 查看设备标签
device write [id] [tag] [value] - 写入标签值`,
                },
              ])
              break
            case 'log':
              setCommandOutput((prev) => [
                ...prev,
                {
                  type: 'output',
                  content: `日志命令:
log show [type] [lines] - 显示日志
log clear [type] - 清除日志
log export [type] - 导出日志
log level [set|get] [level] - 设置/获取日志级别`,
                },
              ])
              break
            case 'user':
              setCommandOutput((prev) => [
                ...prev,
                {
                  type: 'output',
                  content: `用户命令:
user list - 列出所有用户
user add [name] - 添加用户
user remove [name] - 删除用户
user info [name] - 查看用户信息
user role [name] [role] - 设置用户角色
user password [name] - 修改用户密码`,
                },
              ])
              break
            default:
              setCommandOutput((prev) => [
                ...prev,
                {
                  type: 'error',
                  content: `未知帮助主题: ${helpTopic}。可用主题: system, network, file, config, workflow, device, log, user`,
                },
              ])
          }
        } else {
          setCommandOutput((prev) => [
            ...prev,
            {
              type: 'output',
              content: `可用命令类别:
help [topic] - 显示帮助信息，可选主题: system, network, file, config, workflow, device, log, user
clear - 清除终端

基本命令:
echo [text] - 显示文本
date - 显示当前日期和时间
version - 显示系统版本
status - 显示系统状态
exit - 关闭终端

系统管理:
service, process, memory, cpu, disk, uptime, reboot, shutdown

网络工具:
ping, ifconfig, netstat, route, dns

文件操作:
ls, cat, mkdir, rm, cp, mv

配置管理:
config list, config get, config set, config reset, config export, config import

工作流管理:
workflow list, workflow start, workflow stop, workflow status, workflow history

设备管理:
device list, device info, device connect, device disconnect, device tags, device write

日志管理:
log show, log clear, log export, log level

用户管理:
user list, user add, user remove, user info, user role, user password

输入 'help [类别]' 获取详细信息`,
            },
          ])
        }
        break

      case 'clear':
        setCommandOutput([{ type: 'output', content: '终端已清除。' }])
        break

      case 'echo':
        const text = args.slice(1).join(' ') || ''
        setCommandOutput((prev) => [...prev, { type: 'output', content: text }])
        break

      case 'ping':
        const host = args[1] || 'localhost'
        setCommandOutput((prev) => [
          ...prev,
          { type: 'output', content: `正在 ping ${host}...` },
        ])

        // 模拟 ping 响应
        setTimeout(() => {
          setCommandOutput((prev) => [
            ...prev,
            {
              type: 'output',
              content: `来自 ${host} 的回复: 时间=20ms
来自 ${host} 的回复: 时间=18ms
来自 ${host} 的回复: 时间=22ms
来自 ${host} 的回复: 时间=19ms

${host} 的 Ping 统计信息:
    数据包: 已发送 = 4，已接收 = 4，丢失 = 0 (0% 丢失)，
往返行程的估计时间(以毫秒为单位):
    最短 = 18ms，最长 = 22ms，平均 = 19.75ms`,
            },
          ])
        }, 1000)
        break

      case 'date':
        setCommandOutput((prev) => [
          ...prev,
          {
            type: 'output',
            content: new Date().toLocaleString(),
          },
        ])
        break

      case 'version':
        setCommandOutput((prev) => [
          ...prev,
          {
            type: 'output',
            content: '工作流自动化工具 v1.0.0',
          },
        ])
        break

      case 'status':
        setCommandOutput((prev) => [
          ...prev,
          {
            type: 'output',
            content:
              '系统状态: 正常运行中\n服务器负载: 32%\n内存使用: 1.2GB/4GB\n存储空间: 45GB/100GB\n活跃连接: 12',
          },
        ])
        break

      case 'list':
        if (args[1] === 'devices') {
          setCommandOutput((prev) => [
            ...prev,
            {
              type: 'output',
              content: `设备列表:
1. PLC-Main (已连接)
2. Sensor-Temp-01 (已连接)
3. Sensor-Pressure-01 (已连接)
4. Gateway-Edge-01 (已连接)
5. HMI-Panel-01 (离线)
6. Robot-Arm-01 (已连接)`,
            },
          ])
        } else if (args[1] === 'tags') {
          setCommandOutput((prev) => [
            ...prev,
            {
              type: 'output',
              content: `标签列表:
1. PLC-Main.Temperature (REAL, 24.5)
2. PLC-Main.Pressure (REAL, 101.3)
3. PLC-Main.Status (INT, 1)
4. Sensor-Temp-01.Value (REAL, 25.2)
5. Sensor-Pressure-01.Value (REAL, 98.7)
6. Robot-Arm-01.Position (STRING, "HOME")`,
            },
          ])
        } else if (args[1] === 'workflows') {
          setCommandOutput((prev) => [
            ...prev,
            {
              type: 'output',
              content: `工作流列表:
1. 数据采集 (运行中)
2. 报警监控 (运行中)
3. 数据转发 (运行中)
4. 批处理 (已停止)
5. 报表生成 (已停止)`,
            },
          ])
        } else {
          setCommandOutput((prev) => [
            ...prev,
            {
              type: 'error',
              content: `未知列表类型: ${
                args[1] || ''
              }。可用选项: devices, tags, workflows`,
            },
          ])
        }
        break

      case 'exit':
        return { exit: true }

      case '':
        // 空命令，仅添加新行
        break

      default:
        setCommandOutput((prev) => [
          ...prev,
          {
            type: 'error',
            content: `未知命令: ${command}。输入 'help' 查看可用命令。`,
          },
        ])
    }

    return { exit: false }
  }, [])

  // 处理命令输入按键
  const handleCommandKeyDown = useCallback(
    (e: React.KeyboardEvent<HTMLInputElement>) => {
      if (e.key === 'Enter') {
        const result = executeCommand(commandInput)
        if (!result.exit) {
          setCommandInput('')
        }
        return result
      } else if (e.key === 'ArrowUp') {
        e.preventDefault()
        if (commandHistory.length > 0) {
          const newIndex =
            historyIndex < commandHistory.length - 1
              ? historyIndex + 1
              : historyIndex
          setHistoryIndex(newIndex)
          setCommandInput(commandHistory[commandHistory.length - 1 - newIndex])
        }
      } else if (e.key === 'ArrowDown') {
        e.preventDefault()
        if (historyIndex > 0) {
          const newIndex = historyIndex - 1
          setHistoryIndex(newIndex)
          setCommandInput(commandHistory[commandHistory.length - 1 - newIndex])
        } else if (historyIndex === 0) {
          setHistoryIndex(-1)
          setCommandInput('')
        }
      } else if (e.key === 'Tab') {
        e.preventDefault()
        // 简单的命令补全
        const partialCmd = commandInput.toLowerCase()

        const commands = [
          'help',
          'clear',
          'echo',
          'ping',
          'date',
          'version',
          'status',
          'list',
          'exit',
          'service',
          'ls',
        ]
        const matchingCmd = commands.find((cmd) => cmd.startsWith(partialCmd))

        if (matchingCmd) {
          setCommandInput(matchingCmd)
        }
      }

      return { exit: false }
    },
    [commandInput, commandHistory, historyIndex, executeCommand]
  )

  // 滚动到底部
  const scrollToBottom = useCallback(() => {
    // 在这里我们不直接操作DOM，而是返回一个函数，让使用者调用
    return () => {
      setTimeout(() => {
        const outputElement = document.querySelector('#command-output')
        if (outputElement) {
          outputElement.scrollTop = outputElement.scrollHeight
        }
      }, 0)
    }
  }, [])

  return {
    // 状态
    commandInput,
    setCommandInput,
    commandHistory,
    commandOutput,
    historyIndex,

    // 方法
    executeCommand,
    handleCommandKeyDown,
    scrollToBottom,
  }
}
