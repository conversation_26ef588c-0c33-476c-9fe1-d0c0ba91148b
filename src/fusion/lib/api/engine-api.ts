import {
  feature,
  LoadingManager,
  getAPI,
  serveConfig,
} from '../api-services/axios-utils'
import { EngineApi } from '../api-services/apis/engine-api'
import { ExecuteScriptDto } from '../api-services/models'
import { Configuration } from '../api-services/configuration'
import { AxiosResponse } from 'axios'

// 加载状态键
const LOADING_KEY = {
  EXECUTE_SCRIPT: 'engine.executeScript',
  LOAD_METHODS: 'engine.loadMethods',
}

// 定义API方法接口
export interface ApiMethod {
  category: string
  name: string
  description: string
  example: string
  value?: string // 方法值/表达式
  sampleCode?: string // 示例代码
}

// 引擎服务类
export class EngineService {
  /**
   * 获取API方法列表
   * @returns API方法列表
   */
  static async getMethodList(): Promise<ApiMethod[]> {
    try {
      LoadingManager.setLoading(LOADING_KEY.LOAD_METHODS, true)

      // 使用serveConfig而不是创建新的空basePath配置
      const apiConfig = new Configuration({
        basePath: serveConfig.basePath,
      })

      const [error, response] = await feature<AxiosResponse<any>>(
        getAPI(EngineApi, apiConfig).getList()
      )

      if (error) {
        console.error('获取API方法列表失败:', error)
        return [] // 出错时返回空数组
      }

      if (response?.data?.succeeded && response.data.data) {
        // 将API返回的方法转换为我们需要的格式
        const methods: ApiMethod[] = []

        // 处理常用方法
        if (response.data.data.commonlyUsedMethods) {
          Object.entries(response.data.data.commonlyUsedMethods).forEach(
            ([category, methodList]) => {
              if (Array.isArray(methodList)) {
                methodList.forEach((method) => {
                  if (method && method.name) {
                    methods.push({
                      category: category,
                      name: method.name,
                      description: method.desc || '',
                      // 优先使用sampleCode作为示例，如果没有则使用value
                      example:
                        method.sampleCode || method.value || `${method.name}()`,
                      // 保存原始value和sampleCode
                      value: method.value || '',
                      sampleCode: method.sampleCode || '',
                    })
                  }
                })
              }
            }
          )
        }

        // 处理其他方法
        if (response.data.data.methods) {
          Object.entries(response.data.data.methods).forEach(
            ([category, methodList]) => {
              if (Array.isArray(methodList)) {
                methodList.forEach((method) => {
                  if (method && method.name) {
                    methods.push({
                      category: category,
                      name: method.name,
                      description: method.desc || '',
                      // 优先使用sampleCode作为示例，如果没有则使用value
                      example:
                        method.sampleCode || method.value || `${method.name}()`,
                      // 保存原始value和sampleCode
                      value: method.value || '',
                      sampleCode: method.sampleCode || '',
                    })
                  }
                })
              }
            }
          )
        }
        return methods
      }

      return [] // 未获取到数据时返回空数组
    } catch (error) {
      console.error('获取API方法列表发生异常:', error)
      return [] // 异常时返回空数组
    } finally {
      LoadingManager.setLoading(LOADING_KEY.LOAD_METHODS, false)
    }
  }

  /**
   * 执行脚本
   * @param script 脚本内容
   * @param data 脚本数据
   * @param deviceId 设备ID
   * @returns 执行结果
   */
  static async executeScript(
    script: string,
    data: any = { value: null },
    deviceId: number | null = null
  ): Promise<any> {
    try {
      LoadingManager.setLoading(LOADING_KEY.EXECUTE_SCRIPT, true)

      // 使用serveConfig而不是创建新的空basePath配置
      const apiConfig = new Configuration({
        basePath: serveConfig.basePath,
      })

      const scriptDto: ExecuteScriptDto = {
        script,
        data,
        deviceId,
      }

      const [error, response] = await feature(
        getAPI(EngineApi, apiConfig).execute(scriptDto)
      )

      if (error) {
        console.error('脚本执行失败:', error)
        throw error
      }

      if (response?.data?.succeeded) {
        return response.data.data
      } else {
        throw new Error(response?.data?.errors || '脚本执行失败')
      }
    } catch (error) {
      console.error('执行脚本发生异常:', error)
      throw error
    } finally {
      LoadingManager.setLoading(LOADING_KEY.EXECUTE_SCRIPT, false)
    }
  }

  /**
   * 获取指定API的加载状态
   * @param key 加载状态键
   * @returns 是否加载中
   */
  static isLoading(key: string): boolean {
    return LoadingManager.isLoading(key)
  }
}
