import { feature, LoadingManager, getAPI } from '../api-services/axios-utils'
import { DriverApi } from '../api-services/apis/driver-api'
import {
  RESTfulResultDriver,
  RESTfulResultObject,
} from '../api-services/models'
import { Driver, DriverConfigs } from '../api-services/models'
import { AxiosResponse } from 'axios'

// 定义API响应通用格式
export interface ApiResponse<T = any> {
  code: number
  msg: string
  data?: T
}

// 简化的协议信息
export interface ProtocolInfo {
  name: string
  label: string
  version?: string
  description?: string
  manufacturer?: string
}

// 协议类型
export type ProtocolType = string

// 串口通道
export interface SerialChannel {
  id: string
  name: string
  port: string
  baudRate: number
  dataBits: number
  stopBits: number
  parity: string
  status: 'available' | 'inUse' | string
}

// 加载状态键
const LOADING_KEY = {
  GET_PROTOCOLS: 'driver.getProtocols',
  GET_PROTOCOL_DETAIL: 'driver.getProtocolDetail',
}

/**
 * 驱动/协议服务
 */
export class DriverService {
  /**
   * 获取所有协议列表
   * @returns 协议列表
   */
  static async getProtocols(): Promise<ApiResponse<ProtocolInfo[]>> {
    try {
      LoadingManager.setLoading(LOADING_KEY.GET_PROTOCOLS, true)

      const [error, response] = await feature<
        AxiosResponse<RESTfulResultObject>
      >(getAPI(DriverApi).getList())

      if (error) {
        console.error('获取协议列表失败:', error)
        return {
          code: 500,
          msg: error.message || '获取协议列表失败',
          data: [],
        }
      }

      const protocols: ProtocolInfo[] = []

      // 转换协议列表格式
      if (response.data.succeeded && Array.isArray(response.data.data)) {
        response.data.data.forEach((item: any) => {
          protocols.push({
            name: item.driverName || '',
            label: item.driverName || '',
            version: item.version || '',
            description: item.description || '',
            manufacturer: item.manufacturer || item.vendor || '其他',
          })
        })
      }

      return {
        code: response.data.statusCode || 200,
        msg: response.data.succeeded ? '获取协议列表成功' : '获取协议列表失败',
        data: protocols,
      }
    } finally {
      LoadingManager.setLoading(LOADING_KEY.GET_PROTOCOLS, false)
    }
  }

  /**
   * 获取协议详情，包括具体配置信息
   * @param name 协议名称
   * @returns 协议详情，包括配置项
   */
  static async getProtocolDetail(name: string): Promise<ApiResponse<Driver>> {
    try {
      LoadingManager.setLoading(LOADING_KEY.GET_PROTOCOL_DETAIL, true)

      const [error, response] = await feature<
        AxiosResponse<RESTfulResultDriver>
      >(getAPI(DriverApi).getDetail(name))

      if (error) {
        console.error(`获取协议 ${name} 详情失败:`, error)
        return {
          code: 500,
          msg: error.message || `获取协议 ${name} 详情失败`,
        }
      }

      return {
        code: response.data.statusCode || 200,
        msg: response.data.succeeded ? '获取协议详情成功' : '获取协议详情失败',
        data: response.data.data,
      }
    } finally {
      LoadingManager.setLoading(LOADING_KEY.GET_PROTOCOL_DETAIL, false)
    }
  }

  /**
   * 检查是否正在加载
   * @param key 加载状态键
   * @returns 是否正在加载
   */
  static isLoading(key: string): boolean {
    return LoadingManager.isLoading(key)
  }
}
