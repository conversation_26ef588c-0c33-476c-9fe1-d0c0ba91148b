// 通知 API 客户端封装 - 基于自动生成的 Swagger API 客户端

import { NotificationApi } from '../api-services/apis/notification-api'
import { Configuration } from '../api-services/configuration'
import { type NotificationResponseDto } from '../api-services/models/notification-response-dto'
import { type CreateNotificationDto } from '../api-services/models/create-notification-dto'
import { type NotificationTypeEnum } from '../api-services/models/notification-type-enum'
import { type NotificationSourceEnum } from '../api-services/models/notification-source-enum'

// 基础类型定义
type NotificationTypeMap = 1 | 2 | 3 | 4
type NotificationSourceMap = 1 | 2 | 3 | 4 | 5 | 6 | 7

// 保持向后兼容的类型定义
export interface NotificationResponse {
  id: number
  title: string
  message: string
  type: 'info' | 'warning' | 'critical' | 'success'
  source: string
  actionUrl?: string
  details?: string
  isRead: boolean
  createdTime: string
  relatedEntityId?: string
  relatedEntityType?: string
}

export interface NotificationQueryParams {
  page?: number
  pageSize?: number
  type?: string
  source?: string
  isRead?: boolean
  keyword?: string
  startTime?: Date
  endTime?: Date
  relatedEntityId?: string
  relatedEntityType?: string
}

export interface PaginatedNotificationResponse {
  items: NotificationResponse[]
  total: number
  page: number
  pageSize: number
  totalPages: number
}

export interface CreateNotificationRequest {
  title: string
  message: string
  type: 'info' | 'warning' | 'critical' | 'success'
  source: string
  actionUrl?: string
  details?: string
  relatedEntityId?: string
  relatedEntityType?: string
}

// API 配置实例
const createApiConfiguration = (): Configuration => {
  const baseURL = import.meta.env.VITE_API_BASE_URL || ''

  return new Configuration({
    basePath: baseURL,
    // 可以在这里添加认证配置
    accessToken: () => {
      // 这里可以从存储中获取访问令牌
      if (typeof window !== 'undefined') {
        return localStorage.getItem('access_token') || ''
      }
      return ''
    },
  })
}

// 创建 API 实例
const apiConfig = createApiConfiguration()
const notificationApiInstance = new NotificationApi(apiConfig)

// 类型转换函数
const mapNotificationTypeToEnum = (type: string): NotificationTypeEnum => {
  const typeMap: Record<string, NotificationTypeMap> = {
    info: 1,
    warning: 2,
    critical: 3,
    success: 4,
  }
  return (typeMap[type] || 1) as NotificationTypeEnum
}

const mapNotificationSourceToEnum = (
  source: string
): NotificationSourceEnum => {
  const sourceMap: Record<string, NotificationSourceMap> = {
    DeviceManagement: 1,
    DataForwarding: 2,
    SystemManagement: 3,
    DataCollection: 4,
    WorkflowEngine: 5,
    SecurityMonitoring: 6,
    DeviceMonitoring: 7,
  }
  return (sourceMap[source] || 3) as NotificationSourceEnum
}

const mapEnumToNotificationType = (
  enumValue?: string | null
): 'info' | 'warning' | 'critical' | 'success' => {
  if (!enumValue) return 'info'
  const enumMap: Record<string, 'info' | 'warning' | 'critical' | 'success'> = {
    '1': 'info',
    '2': 'warning',
    '3': 'critical',
    '4': 'success',
    Info: 'info',
    Warning: 'warning',
    Critical: 'critical',
    Success: 'success',
  }
  return enumMap[enumValue] || 'info'
}

// 响应转换函数
const convertNotificationResponseDto = (
  dto: NotificationResponseDto
): NotificationResponse => {
  return {
    id: dto.id || 0,
    title: dto.title || '',
    message: dto.message || '',
    type: mapEnumToNotificationType(dto.type),
    source: dto.source || '',
    actionUrl: dto.actionUrl || undefined,
    details: dto.details || undefined,
    isRead: dto.read || false,
    createdTime: new Date().toISOString(), // 后端如果没有提供时间字段，使用当前时间
    relatedEntityId: dto.relatedEntityId || undefined,
    relatedEntityType: dto.relatedEntityType || undefined,
  }
}

// 错误处理函数
const handleApiError = (error: any): never => {
  console.error('API Error:', error)

  if (error.response) {
    // HTTP 错误响应
    throw new Error(
      `API请求失败: ${error.response.status} ${error.response.statusText}`
    )
  } else if (error.request) {
    // 网络错误
    throw new Error('网络连接失败，请检查网络设置')
  } else {
    // 其他错误
    throw new Error(error.message || '未知错误')
  }
}

class NotificationAPI {
  // 分页查询通知
  async getNotifications(
    params: NotificationQueryParams = {}
  ): Promise<PaginatedNotificationResponse> {
    try {
      const typeEnum = params.type
        ? mapNotificationTypeToEnum(params.type)
        : undefined
      const sourceEnum = params.source
        ? mapNotificationSourceToEnum(params.source)
        : undefined

      const response = await notificationApiInstance.getNotificationsAsync(
        params.page,
        params.pageSize,
        typeEnum,
        sourceEnum,
        params.isRead,
        params.keyword,
        params.startTime,
        params.endTime,
        params.relatedEntityId,
        params.relatedEntityType
      )

      const data = response.data.data
      if (!data) {
        throw new Error('响应数据为空')
      }

      const items = (data.items || []).map(convertNotificationResponseDto)

      return {
        items,
        total: data.total || 0,
        page: data.page || 1,
        pageSize: data.pageSize || 20,
        totalPages: data.totalPages || 1,
      }
    } catch (error) {
      return handleApiError(error)
    }
  }

  // 获取通知详情
  async getNotification(id: number): Promise<NotificationResponse> {
    try {
      const response = await notificationApiInstance.getNotificationByIdAsync(
        id
      )

      const data = response.data.data
      if (!data) {
        throw new Error('通知不存在')
      }

      return convertNotificationResponseDto(data)
    } catch (error) {
      return handleApiError(error)
    }
  }

  // 标记通知为已读
  async markAsRead(id: number): Promise<void> {
    try {
      await notificationApiInstance.markAsReadAsync(id)
    } catch (error) {
      handleApiError(error)
    }
  }

  // 标记所有通知为已读
  async markAllAsRead(): Promise<void> {
    try {
      await notificationApiInstance.markAllAsReadAsync()
    } catch (error) {
      handleApiError(error)
    }
  }

  // 删除通知
  async deleteNotification(id: number): Promise<void> {
    try {
      await notificationApiInstance.deleteNotificationAsync(id)
    } catch (error) {
      handleApiError(error)
    }
  }

  // 清除已读通知
  async clearReadNotifications(): Promise<void> {
    try {
      await notificationApiInstance.clearReadNotificationsAsync()
    } catch (error) {
      handleApiError(error)
    }
  }

  // 获取未读数量
  async getUnreadCount(): Promise<{ count: number }> {
    try {
      const response = await notificationApiInstance.getUnreadCountAsync()

      return {
        count: response.data.data || 0,
      }
    } catch (error) {
      return handleApiError(error)
    }
  }

  // 创建通知（内部使用）
  async createNotification(
    data: CreateNotificationRequest
  ): Promise<{ id: number }> {
    try {
      const createDto: CreateNotificationDto = {
        title: data.title,
        message: data.message,
        type: mapNotificationTypeToEnum(data.type),
        source: mapNotificationSourceToEnum(data.source),
        actionUrl: data.actionUrl,
        details: data.details,
        relatedEntityId: data.relatedEntityId,
        relatedEntityType: data.relatedEntityType,
      }

      const response = await notificationApiInstance.createNotificationAsync(
        createDto
      )

      return {
        id: Number(response.data.data || 0),
      }
    } catch (error) {
      return handleApiError(error)
    }
  }
}

// 导出单例实例
export const notificationAPI = new NotificationAPI()

// 工具函数：格式化通知时间
export function formatNotificationTime(isoString: string): {
  time: string
  date: string
} {
  const date = new Date(isoString)

  const time = date.toLocaleTimeString('zh-CN', {
    hour: '2-digit',
    minute: '2-digit',
    hour12: false,
  })

  const dateStr = date.toISOString().split('T')[0]

  return { time, date: dateStr }
}

// 工具函数：将后端通知转换为前端格式
export function transformNotificationResponse(
  apiResponse: NotificationResponse
) {
  const { time, date } = formatNotificationTime(apiResponse.createdTime)

  return {
    id: apiResponse.id,
    title: apiResponse.title,
    message: apiResponse.message,
    time,
    date,
    read: apiResponse.isRead,
    type: apiResponse.type,
    source: apiResponse.source,
    details: apiResponse.details,
    actionUrl: apiResponse.actionUrl,
  }
}

// 工具函数：映射通知来源中文名称
export function getSourceDisplayName(source: string): string {
  const sourceMap: Record<string, string> = {
    DeviceManagement: '设备管理',
    DataForwarding: '数据转发',
    SystemManagement: '系统管理',
    DataCollection: '数据采集',
    WorkflowEngine: '工作流编排',
    SecurityMonitoring: '安全监控',
    DeviceMonitoring: '设备监控',
  }

  return sourceMap[source] || source
}
