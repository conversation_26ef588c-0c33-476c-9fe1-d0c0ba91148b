import { feature, LoadingManager, getAPI } from '../api-services/axios-utils'
import { DataForwardApi } from '../api-services/apis/data-forward-api'
import {
  ForwardConfigInput,
  ForwardConfigUpdateInput,
  TemplateTypeEnum,
  ForwardTypeEnum,
  ForwardConfig,
  OfflineStorageConfig as ApiOfflineStorageConfig,
  RetryPolicy,
} from '../api-services/models'
import { AxiosResponse } from 'axios'
import type {
  ForwardingConfig,
  ForwardingProtocol,
  ForwardMode,
  CustomConfig,
  OfflineStorageConfig,
  RetryConfig,
} from '@/components/data-forwarding/types'

// 加载状态键
const LOADING_KEY = {
  GET_DETAIL: 'dataForward.getDetail',
  CREATE: 'dataForward.create',
  UPDATE: 'dataForward.update',
  TEST_CONNECTION: 'dataForward.testConnection',
  GET_TEMPLATES: 'dataForward.getTemplates',
}

// 转发类型映射
const forwardTypeMap: Record<string, number> = {
  MQTT: 1,
  HTTP: 2,
  WebSocket: 3,
  TCP: 4,
  UDP: 5,
  MySQL: 6,
  PostgreSQL: 7,
  MongoDB: 8,
  InfluxDB: 9,
  Redis: 10,
  SQLServer: 11,
  ModbusServer: 12,
  OPCUA: 13,
  SiemensS7: 14,
  EthernetIP: 15,
}

// 转发类型映射函数
const mapForwardType = (type: string): ForwardTypeEnum => {
  // 先查找预定义映射
  const mappedType = forwardTypeMap[type]
  if (mappedType !== undefined) {
    return mappedType as ForwardTypeEnum
  }

  // 尝试转换数字类型
  const numType = parseInt(type, 10)
  if (!isNaN(numType) && numType >= 1 && numType <= 15) {
    return numType as ForwardTypeEnum
  }

  // 兜底返回HTTP类型
  console.warn(`未识别的转发类型: ${type}，默认使用HTTP(2)`)
  return ForwardTypeEnum.NUMBER_2 // HTTP
}

// 转发类型到字符串映射函数
const mapForwardTypeToString = (
  type: ForwardTypeEnum | string | undefined
): ForwardingProtocol => {
  if (type === undefined) {
    return 'HTTP' // 默认返回HTTP
  }

  // 如果是字符串类型，直接进行字符串映射
  if (typeof type === 'string') {
    const upperType = type.toUpperCase()
    switch (upperType) {
      case 'MQTT':
        return 'MQTT'
      case 'HTTP':
      case 'HTTPS':
        return 'HTTP'
      case 'WEBSOCKET':
      case 'WS':
        return 'WebSocket'
      case 'TCP':
        return 'TCP'
      case 'UDP':
        return 'UDP'
      case 'MYSQL':
        return 'MySQL'
      case 'POSTGRESQL':
      case 'POSTGRES':
        return 'PostgreSQL'
      case 'MONGODB':
      case 'MONGO':
        return 'MongoDB'
      case 'INFLUXDB':
        return 'InfluxDB'
      case 'REDIS':
        return 'Redis'
      case 'SQLSERVER':
      case 'SQL_SERVER':
        return 'SQLServer'
      case 'MODBUSSERVER':
      case 'MODBUS_SERVER':
      case 'MODBUS':
        return 'ModbusServer'
      case 'OPCUA':
      case 'OPC_UA':
        return 'OPCUA'
      case 'SIEMENSS7':
      case 'SIEMENS_S7':
      case 'S7':
        return 'SiemensS7'
      case 'ETHERNETIP':
      case 'ETHERNET_IP':
        return 'EthernetIP'
      default:
        console.warn(`未识别的转发类型字符串: ${type}，默认使用HTTP`)
        return 'HTTP'
    }
  }

  // 如果是枚举类型，使用原有的枚举映射逻辑（只有3个有效值）
  switch (type) {
    case ForwardTypeEnum.NUMBER_1:
      return 'MQTT'
    case ForwardTypeEnum.NUMBER_2:
      return 'HTTP'
    case ForwardTypeEnum.NUMBER_3:
      return 'WebSocket'
    default:
      console.warn(`未识别的转发类型枚举值: ${type}，默认使用HTTP`)
      return 'HTTP'
  }
}

// 将API OfflineStorageConfig转换为前端OfflineStorageConfig
const convertApiOfflineStorageConfig = (
  apiConfig?: ApiOfflineStorageConfig
): OfflineStorageConfig => {
  return {
    offlineStorage: apiConfig?.offlineStorage ?? true,
    batchSize: apiConfig?.batchSize ?? 100,
    storageLimit: apiConfig?.storageLimit ?? 10000,
    storageMode: (apiConfig?.storageMode as any) ?? 'file',
    expireDays: apiConfig?.expireDays ?? 7,
    cleanExpireHours: apiConfig?.cleanExpireHours ?? 1,
  }
}

// 将API RetryPolicy转换为前端RetryConfig
const convertApiRetryPolicy = (apiPolicy?: RetryPolicy): RetryConfig => {
  return {
    enable: apiPolicy?.enable ?? true,
    maxRetries: apiPolicy?.maxRetries ?? 3,
    retryInterval: apiPolicy?.retryInterval ?? 5000,
    initialDelay: apiPolicy?.initialDelay ?? 1000,
    maxDelay: apiPolicy?.maxDelay ?? 60000,
    backoffMultiplier: apiPolicy?.backoffMultiplier ?? 2,
    enableDeadLetter: apiPolicy?.enableDeadLetter ?? false,
    enableAutoRetry: apiPolicy?.enableAutoRetry ?? true,
  }
}

// 数据转发API服务类
class DataForwardService {
  async getDetail(id: number) {
    try {
      LoadingManager.setLoading(LOADING_KEY.GET_DETAIL, true)
      const response = await getAPI(DataForwardApi).detail(id)
      return {
        ...response,
        data:
          response.data.succeeded && response.data.data
            ? this.fromApiConfig(response.data.data)
            : undefined,
      }
    } finally {
      LoadingManager.setLoading(LOADING_KEY.GET_DETAIL, false)
    }
  }

  async create(data: ForwardConfigInput) {
    try {
      LoadingManager.setLoading(LOADING_KEY.CREATE, true)
      return await getAPI(DataForwardApi).create(data)
    } finally {
      LoadingManager.setLoading(LOADING_KEY.CREATE, false)
    }
  }

  async update(data: ForwardConfigUpdateInput) {
    try {
      LoadingManager.setLoading(LOADING_KEY.UPDATE, true)
      return await getAPI(DataForwardApi).update(data)
    } finally {
      LoadingManager.setLoading(LOADING_KEY.UPDATE, false)
    }
  }

  async getTemplateList(
    type?: TemplateTypeEnum,
    forwardType?: ForwardTypeEnum
  ) {
    try {
      LoadingManager.setLoading(LOADING_KEY.GET_TEMPLATES, true)
      const api = getAPI(DataForwardApi)
      return await api.getTemplateList(type, forwardType)
    } finally {
      LoadingManager.setLoading(LOADING_KEY.GET_TEMPLATES, false)
    }
  }

  async testConnection(config: any) {
    try {
      LoadingManager.setLoading(LOADING_KEY.TEST_CONNECTION, true)
      // 这里假设有一个测试连接的API
      // 实际项目中需要替换为真实的API调用
      await new Promise((resolve) => setTimeout(resolve, 1000))
      return {
        success: true,
        message: '连接成功',
      }
    } catch (error) {
      return {
        success: false,
        message: error instanceof Error ? error.message : '连接失败',
      }
    } finally {
      LoadingManager.setLoading(LOADING_KEY.TEST_CONNECTION, false)
    }
  }

  isLoading(key: string): boolean {
    return LoadingManager.isLoading(key)
  }

  // 将前端配置转换为API请求格式
  toApiInput(config: ForwardingConfig): ForwardConfigInput {
    return {
      name: config.name,
      enable: config.enable,
      type: mapForwardType(config.type),
      url: config.url,
      username: config.username,
      password: config.password,
      timeout: config.timeout,
      forwardMode: config.forwardMode as any, // 兼容API格式
      connectionTimeout: config.connectionTimeout,
      reconnectInterval: config.reconnectInterval,
      customConfig: config.customConfig || {},
      offlineStorageConfig: config.offlineStorageConfig,
      retryConfig: config.retryConfig,
    }
  }

  // 将前端配置转换为API更新请求格式
  toApiUpdateInput(config: ForwardingConfig): ForwardConfigUpdateInput {
    return {
      id: config.id as number, // 确保id存在
      name: config.name,
      enable: config.enable,
      type: mapForwardType(config.type),
      url: config.url,
      username: config.username,
      password: config.password,
      timeout: config.timeout,
      forwardMode: config.forwardMode as any,
      connectionTimeout: config.connectionTimeout,
      reconnectInterval: config.reconnectInterval,
      customConfig: config.customConfig || {},
      offlineStorageConfig: config.offlineStorageConfig,
      retryConfig: config.retryConfig,
    }
  }

  // 将API响应转换为前端配置格式
  fromApiConfig(apiConfig: ForwardConfig): ForwardingConfig {
    // 确保类型安全的转换
    const protocolType = mapForwardTypeToString(apiConfig.type)

    // 确保forwardMode是有效的ForwardMode类型
    let forwardMode: ForwardMode = 'realtime'
    if (
      apiConfig.forwardMode === 'batch' ||
      apiConfig.forwardMode === 'scheduled' ||
      apiConfig.forwardMode === 'event'
    ) {
      forwardMode = apiConfig.forwardMode as ForwardMode
    }

    // 使用转换函数处理复杂类型
    const offlineStorageConfig = convertApiOfflineStorageConfig(
      apiConfig.offlineStorageConfig
    )
    const retryConfig = convertApiRetryPolicy(apiConfig.retryConfig)

    return {
      id: apiConfig.id,
      name: apiConfig.name || '',
      enable: apiConfig.enable ?? false,
      type: protocolType,
      url: apiConfig.url || '',
      username: apiConfig.username || '',
      password: apiConfig.password || '',
      timeout: apiConfig.timeout,
      forwardMode: forwardMode,
      customConfig: apiConfig.customConfig || {},
      connectionTimeout: apiConfig.connectionTimeout,
      reconnectInterval: apiConfig.reconnectInterval,
      isConnected: apiConfig.isConnected ?? false,
      lastActivityTime: apiConfig.lastActivityTime
        ? String(apiConfig.lastActivityTime)
        : undefined,
      createTime: apiConfig.createTime
        ? String(apiConfig.createTime)
        : undefined,
      offlineStorageConfig,
      retryConfig,
      description: undefined,
    }
  }
}

// 使用feature函数创建服务实例
const dataForwardService = new DataForwardService()
export default dataForwardService
