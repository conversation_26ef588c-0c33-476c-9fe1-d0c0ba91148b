/**
 * AG Grid配置文件
 * 用于注册AG Grid模块和配置
 */
import {
  ModuleRegistry,
  AllCommunityModule,
  provideGlobalGridOptions,
} from 'ag-grid-community'

// 注册所有AG Grid社区模块
ModuleRegistry.registerModules([AllCommunityModule])

// 配置全局网格选项，使用legacy主题模式
provideGlobalGridOptions({
  theme: 'legacy', // 使用旧版主题系统，避免与CSS文件冲突
})

// 导出一个空函数，确保文件被导入时执行注册代码
export const ensureModulesRegistered = () => {
  // 模块已在文件顶层注册，此函数仅用于确保导入
}

// 默认导出，方便导入
export default { ensureModulesRegistered }
