/**
 * 应用初始化配置
 * 用于在应用启动时初始化各种配置和监听器
 */

import { initializeApiConfig, setupApiConfigListener } from './setup-api'
import { initializeLoggerConfig, setupLoggerConfigListener } from './logger-config'
import { overrideConsole } from '../utils/console-override'
import { initializeDevToolsBlocker } from '../utils/dev-tools-blocker'

/**
 * 初始化应用配置
 * 在应用启动时调用一次
 */
export function initializeApp() {
  // 首先保存原始console，防止循环调用
  if (typeof window !== 'undefined' && !((window as any).__originalConsole__)) {
    ;(window as any).__originalConsole__ = {
      log: console.log.bind(console),
      info: console.info.bind(console),
      warn: console.warn.bind(console),
      error: console.error.bind(console),
      debug: console.debug.bind(console),
      trace: console.trace.bind(console),
      group: console.group.bind(console),
      groupCollapsed: console.groupCollapsed.bind(console),
      groupEnd: console.groupEnd.bind(console),
      table: console.table.bind(console),
      time: console.time.bind(console),
      timeEnd: console.timeEnd.bind(console),
      count: console.count.bind(console),
      clear: console.clear.bind(console),
      assert: console.assert.bind(console)
    }
  }

  // 初始化API配置
  initializeApiConfig()

  // 初始化Logger配置
  initializeLoggerConfig()

  // 重写全局console
  overrideConsole({
    enabled: true,
    preserveOriginal: import.meta.env.DEV, // 开发环境保留原始console
    addConsolePrefix: true,
    ignorePatterns: [
      /node_modules/,
      /__webpack/,
      /webpack-internal/,
      /react-dom/,
      /react-refresh/,
      /vite/
    ]
  })

  // 初始化开发者工具阻止器
  initializeDevToolsBlocker()
}

/**
 * 设置配置监听器
 * 在应用启动时调用，返回清理函数
 */
export function setupConfigListeners() {
  const cleanupFunctions: (() => void)[] = []
  
  // 设置API配置监听器
  const apiCleanup = setupApiConfigListener()
  cleanupFunctions.push(apiCleanup)
  
  // 设置Logger配置监听器
  const loggerCleanup = setupLoggerConfigListener()
  cleanupFunctions.push(loggerCleanup)
  
  // 返回清理所有监听器的函数
  return () => {
    cleanupFunctions.forEach(cleanup => cleanup())
  }
}

/**
 * 完整的应用初始化
 * 包括配置初始化和监听器设置
 */
export function initializeAppWithListeners() {
  // 初始化配置
  initializeApp()
  
  // 设置监听器
  const cleanup = setupConfigListeners()
  
  return cleanup
}
