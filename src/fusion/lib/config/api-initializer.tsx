import { useEffect } from 'react'
import { initializeApiConfig, setupApiConfigListener } from './setup-api'
import {
  initializeSignalRConfig,
  setupSignalRConfigListener,
} from '../signalr/setup-signalr'

/**
 * API和SignalR配置初始化组件
 * 在应用启动时初始化配置，并在配置变更时更新
 */
export function ApiInitializer() {
  useEffect(() => {
    // 初始化API配置
    initializeApiConfig()

    // 初始化SignalR配置
    initializeSignalRConfig()

    // 设置配置变更监听器
    const unsubscribeApi = setupApiConfigListener()
    const unsubscribeSignalR = setupSignalRConfigListener()

    // 清理函数
    return () => {
      unsubscribeApi()
      unsubscribeSignalR()
    }
  }, [])

  // 这是一个无渲染组件
  return null
}
