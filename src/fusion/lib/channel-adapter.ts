import { EdgeChannel } from './api-services/models'
import { StopBits } from './api-services/models/stop-bits'
import { Parity } from './api-services/models/parity'

// UI展示用的通道数据结构
export interface SerialChannelDisplay {
  id: string
  name: string
  port: string
  baudRate: number
  dataBits: number
  stopBits: number
  parity: string
  isInUse: boolean
}

// 将字符串形式的stop位映射为数字
export const mapStopToNumber = (stop: string): number => {
  switch (stop) {
    case 'One':
      return 1
    case 'Two':
      return 2
    case 'OnePointFive':
      return 1.5
    default:
      return 1
  }
}

// 将字符串形式的checkout映射为parity字符串
export const mapCheckoutToParity = (checkout: string): string => {
  switch (checkout) {
    case 'None':
      return 'none'
    case 'Odd':
      return 'odd'
    case 'Even':
      return 'even'
    case 'Mark':
      return 'mark'
    case 'Space':
      return 'space'
    default:
      return 'none'
  }
}

// 将StopBits枚举映射为字符串
export const mapStopBitsToString = (
  stopBits: StopBits | number | string | undefined
): string => {
  if (typeof stopBits === 'string') {
    return stopBits
  }

  if (stopBits === undefined) {
    return 'One'
  }

  switch (stopBits) {
    case StopBits.NUMBER_0:
      return 'One'
    case StopBits.NUMBER_1:
      return 'Two'
    case StopBits.NUMBER_2:
      return 'OnePointFive'
    case 0:
      return 'One'
    case 1:
      return 'Two'
    case 2:
      return 'OnePointFive'
    default:
      return 'One'
  }
}

// 将Parity枚举映射为字符串
export const mapParityToString = (
  parity: Parity | number | string | undefined
): string => {
  if (typeof parity === 'string') {
    return parity
  }

  if (parity === undefined) {
    return 'None'
  }

  switch (parity) {
    case Parity.NUMBER_0:
      return 'None'
    case Parity.NUMBER_1:
      return 'Odd'
    case Parity.NUMBER_2:
      return 'Even'
    case Parity.NUMBER_3:
      return 'Mark'
    case Parity.NUMBER_4:
      return 'Space'
    case 0:
      return 'None'
    case 1:
      return 'Odd'
    case 2:
      return 'Even'
    case 3:
      return 'Mark'
    case 4:
      return 'Space'
    default:
      return 'None'
  }
}

// 工具函数：转换EdgeChannel为UI使用的格式
export const convertChannelToDisplay = (
  channel: EdgeChannel
): SerialChannelDisplay => {
  const stopStr = mapStopBitsToString(channel.stop)
  const checkoutStr = mapParityToString(channel.checkout)

  return {
    id: channel.id?.toString() || '',
    name: channel.channelName || '',
    port: channel.serial || '',
    baudRate: channel.baudRate || 9600,
    dataBits: channel.dataBits || 8,
    stopBits: mapStopToNumber(stopStr),
    parity: mapCheckoutToParity(checkoutStr),
    isInUse: channel.deviceCount !== undefined && channel.deviceCount > 0,
  }
}
