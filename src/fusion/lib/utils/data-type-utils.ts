/**
 * 数据类型工具函数
 * 只使用 transitionType 字段进行数据类型判断
 */

export interface TagWithDataType {
  transitionType?: string | null
  [key: string]: any
}

/**
 * 获取点位的数据类型
 * 只使用 transitionType 字段
 */
export function getTagDataType(tag: TagWithDataType): string {
  // 只使用 transitionType（API返回的字段）
  if (tag.transitionType && tag.transitionType !== null) {
    return tag.transitionType
  }

  // 如果没有 transitionType，返回未知类型
  return 'Unknown'
}

/**
 * 检查点位是否为数值类型
 * 支持的数值类型：Number, Integer, Float, Double 等
 */
export function isNumericTag(tag: TagWithDataType): boolean {
  const dataType = getTagDataType(tag)

  // 数值类型的可能值
  const numericTypes = [
    'Number',
    'Integer',
    'JSON',
    'String',
    'Array',
    'Boolean',
    'Object',
  ]

  return numericTypes.some((type) =>
    dataType.toLowerCase().includes(type.toLowerCase())
  )
}

/**
 * 检查点位是否为字符串类型
 */
export function isStringTag(tag: TagWithDataType): boolean {
  const dataType = getTagDataType(tag)
  return dataType.toLowerCase().includes('string')
}

/**
 * 检查点位是否为布尔类型
 */
export function isBooleanTag(tag: TagWithDataType): boolean {
  const dataType = getTagDataType(tag)
  return dataType.toLowerCase().includes('bool')
}

/**
 * 获取数据类型的显示名称
 * 将英文类型名转换为中文显示
 */
export function getDataTypeDisplayName(tag: TagWithDataType): string {
  const dataType = getTagDataType(tag)

  const typeMap: Record<string, string> = {
    Number: '数值',
    Integer: '整数',
    String: '字符串',
    Boolean: '布尔值',
    Array: '数组',
    Object: '对象',
    Binary: '二进制',
    JSON: 'JSON',
  }

  // 查找完全匹配
  if (typeMap[dataType]) {
    return typeMap[dataType]
  }

  // 模糊匹配
  for (const [key, value] of Object.entries(typeMap)) {
    if (dataType.toLowerCase().includes(key.toLowerCase())) {
      return value
    }
  }

  // 如果没有匹配，返回原始值
  return dataType
}

/**
 * 验证点位数据类型是否有效
 */
export function isValidDataType(tag: TagWithDataType): boolean {
  const dataType = getTagDataType(tag)
  return dataType !== 'Unknown' && dataType.trim() !== ''
}

/**
 * 获取数据类型的图标类名或组件
 */
export function getDataTypeIcon(tag: TagWithDataType): string {
  const dataType = getTagDataType(tag)

  if (isNumericTag(tag)) {
    return 'hash' // 数值图标
  } else if (isStringTag(tag)) {
    return 'type' // 文本图标
  } else if (isBooleanTag(tag)) {
    return 'toggle-left' // 开关图标
  } else {
    return 'help-circle' // 未知类型图标
  }
}
