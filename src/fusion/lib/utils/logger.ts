/**
 * 统一日志管理系统
 * 提供分级日志控制、性能优化和环境感知功能
 */

import { storage } from './storage-manager'

export enum LogLevel {
  DEBUG = 0,
  INFO = 1,
  WARN = 2,
  ERROR = 3,
  NONE = 4
}

export interface LogEntry {
  level: LogLevel
  message: string
  args: any[]
  timestamp: number
  source?: string
}

class Logger {
  private static instance: Logger
  private currentLevel: LogLevel = LogLevel.INFO
  private isEnabled: boolean = true
  private isDevelopment: boolean = import.meta.env.DEV
  private logs: LogEntry[] = []
  private maxLogs: number = 1000
  private shouldPersist: boolean = false

  private constructor() {
    // 从localStorage恢复日志设置
    this.loadSettings()
  }

  static getInstance(): Logger {
    if (!Logger.instance) {
      Logger.instance = new Logger()
    }
    return Logger.instance
  }

  /**
   * 设置日志级别
   */
  setLevel(level: LogLevel) {
    this.currentLevel = level
    this.saveSettings()
  }

  /**
   * 启用/禁用日志
   */
  setEnabled(enabled: boolean) {
    this.isEnabled = enabled
    this.saveSettings()
  }

  /**
   * 启用/禁用日志持久化
   */
  setPersist(persist: boolean) {
    this.shouldPersist = persist
    this.saveSettings()
  }

  /**
   * 设置最大日志条数
   */
  setMaxLogs(maxLogs: number) {
    this.maxLogs = maxLogs
    this.trimLogs()
  }

  /**
   * 调试级别日志
   */
  debug(message: any, ...args: any[]) {
    this.log(LogLevel.DEBUG, 'debug', message, ...args)
  }

  /**
   * 信息级别日志
   */
  info(message: any, ...args: any[]) {
    this.log(LogLevel.INFO, 'info', message, ...args)
  }

  /**
   * 警告级别日志
   */
  warn(message: any, ...args: any[]) {
    this.log(LogLevel.WARN, 'warn', message, ...args)
  }

  /**
   * 错误级别日志
   */
  error(message: any, ...args: any[]) {
    this.log(LogLevel.ERROR, 'error', message, ...args)
  }

  /**
   * 核心日志方法
   */
  private log(level: LogLevel, method: keyof Console, message: any, ...args: any[]) {
    // 性能优化：如果不满足条件，直接返回
    if (!this.isEnabled || level < this.currentLevel) {
      return
    }

    // 生产环境只显示 WARN 和 ERROR
    if (!this.isDevelopment && level < LogLevel.WARN) {
      return
    }

    const timestamp = Date.now()
    const logEntry: LogEntry = {
      level,
      message: String(message),
      args,
      timestamp,
      source: this.getCallerInfo()
    }

    // 持久化日志
    if (this.shouldPersist) {
      this.persistLog(logEntry)
    }

    // 输出到控制台
    this.outputToConsole(level, method, message, ...args)
  }

  /**
   * 输出到控制台
   */
  private outputToConsole(level: LogLevel, method: keyof Console, message: any, ...args: any[]) {
    const timestamp = new Date().toISOString()
    const levelName = LogLevel[level]
    const prefix = `[${timestamp}] [${levelName}]`

    // 使用原始console方法避免循环调用
    try {
      // 标记为Logger内部调用
      ;(window as any).__loggerInternalCall__ = true

      // 获取原始console方法
      const originalConsole = (window as any).__originalConsole__
      if (originalConsole && originalConsole[method]) {
        originalConsole[method](prefix, message, ...args)
      } else {
        // 如果没有原始console，使用当前console
        const consoleMethod = console[method] as any
        if (consoleMethod) {
          consoleMethod(prefix, message, ...args)
        }
      }
    } catch (error) {
      // 输出失败时静默处理，避免无限递归
    } finally {
      // 清除内部调用标记
      ;(window as any).__loggerInternalCall__ = false
    }
  }

  /**
   * 获取调用者信息
   */
  private getCallerInfo(): string {
    try {
      const stack = new Error().stack
      if (stack) {
        const lines = stack.split('\n')
        // 跳过当前方法和log方法，获取实际调用者
        const callerLine = lines[4] || lines[3] || ''
        const match = callerLine.match(/at\s+(.+?)\s+\((.+?):(\d+):(\d+)\)/)
        if (match) {
          const [, functionName, fileName, lineNumber] = match
          const shortFileName = fileName.split('/').pop() || fileName
          return `${functionName} (${shortFileName}:${lineNumber})`
        }
      }
    } catch (e) {
      // 忽略错误
    }
    return 'unknown'
  }

  /**
   * 持久化日志
   */
  private persistLog(entry: LogEntry) {
    this.logs.push(entry)
    this.trimLogs()

    const logsData = JSON.stringify(this.logs)
    const success = storage.setItem('app-logs', logsData)

    if (!success) {
      // 存储失败，尝试减少日志数量
      this.handleStorageQuotaExceeded()
    }
  }

  /**
   * 处理存储空间不足的情况
   */
  private handleStorageQuotaExceeded() {
    const originalConsole = (window as any).__originalConsole__ || console

    // 策略1: 减少日志数量到原来的1/4
    const reducedLogs = this.logs.slice(-Math.floor(this.maxLogs / 4))
    this.logs = reducedLogs

    let success = storage.setItem('app-logs', JSON.stringify(this.logs))

    if (success) {
      originalConsole.warn('Logger: localStorage空间不足，已自动清理旧日志')
      return
    }

    // 策略2: 只保留最近的50条日志
    this.logs = this.logs.slice(-50)
    success = storage.setItem('app-logs', JSON.stringify(this.logs))

    if (success) {
      originalConsole.warn('Logger: localStorage空间严重不足，只保留最近50条日志')
      return
    }

    // 策略3: 完全禁用持久化
    this.shouldPersist = false
    this.logs = []
    storage.removeItem('app-logs')

    originalConsole.error('Logger: localStorage空间不足，已禁用日志持久化功能')
  }

  /**
   * 修剪日志数量
   */
  private trimLogs() {
    if (this.logs.length > this.maxLogs) {
      this.logs = this.logs.slice(-this.maxLogs)
    }
  }

  /**
   * 获取历史日志
   */
  getLogs(): LogEntry[] {
    return [...this.logs]
  }

  /**
   * 获取指定级别的日志
   */
  getLogsByLevel(level: LogLevel): LogEntry[] {
    return this.logs.filter(log => log.level === level)
  }

  /**
   * 清空日志
   */
  clearLogs() {
    this.logs = []
    storage.removeItem('app-logs')
  }

  /**
   * 导出日志
   */
  exportLogs(): string {
    const exportData = {
      timestamp: new Date().toISOString(),
      environment: this.isDevelopment ? 'development' : 'production',
      settings: {
        level: LogLevel[this.currentLevel],
        enabled: this.isEnabled,
        persist: this.shouldPersist
      },
      logs: this.logs.map(log => ({
        level: LogLevel[log.level],
        message: log.message,
        args: log.args,
        timestamp: new Date(log.timestamp).toISOString(),
        source: log.source
      }))
    }
    return JSON.stringify(exportData, null, 2)
  }

  /**
   * 保存设置到localStorage
   */
  private saveSettings() {
    const settings = {
      level: this.currentLevel,
      enabled: this.isEnabled,
      persist: this.shouldPersist
    }
    storage.setItem('logger-settings', JSON.stringify(settings))
  }

  /**
   * 从localStorage加载设置
   */
  private loadSettings() {
    try {
      const settingsStr = storage.getItem('logger-settings')
      if (settingsStr) {
        const settings = JSON.parse(settingsStr)
        this.currentLevel = settings.level ?? LogLevel.INFO
        this.isEnabled = settings.enabled ?? true
        this.shouldPersist = settings.persist ?? false
      }

      // 加载历史日志
      if (this.shouldPersist) {
        const logsStr = storage.getItem('app-logs')
        if (logsStr) {
          this.logs = JSON.parse(logsStr)
          this.trimLogs()
        }
      }
    } catch (e) {
      // 如果加载失败，使用默认设置
      this.currentLevel = LogLevel.INFO
      this.isEnabled = true
      this.shouldPersist = false
    }
  }

  /**
   * 获取当前设置
   */
  getSettings() {
    return {
      level: this.currentLevel,
      levelName: LogLevel[this.currentLevel],
      enabled: this.isEnabled,
      persist: this.shouldPersist,
      isDevelopment: this.isDevelopment,
      logCount: this.logs.length,
      maxLogs: this.maxLogs
    }
  }
}

// 导出单例实例
export const logger = Logger.getInstance()

// 导出便捷方法
export const log = {
  debug: (message: any, ...args: any[]) => logger.debug(message, ...args),
  info: (message: any, ...args: any[]) => logger.info(message, ...args),
  warn: (message: any, ...args: any[]) => logger.warn(message, ...args),
  error: (message: any, ...args: any[]) => logger.error(message, ...args),
}

// 默认导出
export default logger
