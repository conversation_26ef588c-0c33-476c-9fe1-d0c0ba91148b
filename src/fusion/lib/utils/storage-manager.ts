/**
 * localStorage管理工具
 * 提供存储空间监控、清理和优化功能
 */

export interface StorageInfo {
  used: number
  available: number
  total: number
  usagePercentage: number
  items: Array<{
    key: string
    size: number
    sizeFormatted: string
  }>
}

export class StorageManager {
  private static instance: StorageManager

  static getInstance(): StorageManager {
    if (!StorageManager.instance) {
      StorageManager.instance = new StorageManager()
    }
    return StorageManager.instance
  }

  /**
   * 获取localStorage使用情况
   */
  getStorageInfo(): StorageInfo {
    const items: Array<{ key: string; size: number; sizeFormatted: string }> = []
    let totalUsed = 0

    // 遍历所有localStorage项目
    for (let i = 0; i < localStorage.length; i++) {
      const key = localStorage.key(i)
      if (key) {
        const value = localStorage.getItem(key) || ''
        const size = new Blob([value]).size
        totalUsed += size
        
        items.push({
          key,
          size,
          sizeFormatted: this.formatBytes(size)
        })
      }
    }

    // 按大小排序
    items.sort((a, b) => b.size - a.size)

    // 估算总可用空间（通常为5-10MB）
    const estimatedTotal = 5 * 1024 * 1024 // 5MB
    const available = Math.max(0, estimatedTotal - totalUsed)
    const usagePercentage = (totalUsed / estimatedTotal) * 100

    return {
      used: totalUsed,
      available,
      total: estimatedTotal,
      usagePercentage,
      items
    }
  }

  /**
   * 检查存储空间是否充足
   */
  hasEnoughSpace(requiredBytes: number = 1024): boolean {
    const info = this.getStorageInfo()
    return info.available >= requiredBytes
  }

  /**
   * 清理指定前缀的存储项
   */
  clearByPrefix(prefix: string): number {
    const keysToRemove: string[] = []
    
    for (let i = 0; i < localStorage.length; i++) {
      const key = localStorage.key(i)
      if (key && key.startsWith(prefix)) {
        keysToRemove.push(key)
      }
    }

    keysToRemove.forEach(key => {
      try {
        localStorage.removeItem(key)
      } catch (e) {
        console.warn(`Failed to remove localStorage item: ${key}`)
      }
    })

    return keysToRemove.length
  }

  /**
   * 清理最大的存储项
   */
  clearLargestItems(count: number = 1): number {
    const info = this.getStorageInfo()
    const itemsToRemove = info.items
      .slice(0, count)
      .filter(item => !this.isProtectedKey(item.key))

    itemsToRemove.forEach(item => {
      try {
        localStorage.removeItem(item.key)
      } catch (e) {
        console.warn(`Failed to remove localStorage item: ${item.key}`)
      }
    })

    return itemsToRemove.length
  }

  /**
   * 智能清理存储空间
   */
  smartCleanup(): { cleaned: number; freedBytes: number } {
    const beforeInfo = this.getStorageInfo()
    let cleanedItems = 0

    // 1. 清理过期的缓存数据
    cleanedItems += this.clearExpiredCache()

    // 2. 清理临时数据
    cleanedItems += this.clearByPrefix('temp:')
    cleanedItems += this.clearByPrefix('cache:')

    // 3. 如果空间仍然不足，清理日志数据
    if (!this.hasEnoughSpace(1024 * 100)) { // 100KB
      cleanedItems += this.clearByPrefix('app-logs')
      cleanedItems += this.clearByPrefix('logger-')
    }

    // 4. 如果还是不够，清理最大的非保护项
    if (!this.hasEnoughSpace(1024 * 50)) { // 50KB
      cleanedItems += this.clearLargestItems(3)
    }

    const afterInfo = this.getStorageInfo()
    const freedBytes = beforeInfo.used - afterInfo.used

    return { cleaned: cleanedItems, freedBytes }
  }

  /**
   * 清理过期的缓存数据
   */
  private clearExpiredCache(): number {
    const now = Date.now()
    const keysToRemove: string[] = []

    for (let i = 0; i < localStorage.length; i++) {
      const key = localStorage.key(i)
      if (key && key.includes(':expires:')) {
        try {
          const data = JSON.parse(localStorage.getItem(key) || '{}')
          if (data.expires && data.expires < now) {
            keysToRemove.push(key)
          }
        } catch (e) {
          // 如果解析失败，也删除这个项
          keysToRemove.push(key)
        }
      }
    }

    keysToRemove.forEach(key => {
      try {
        localStorage.removeItem(key)
      } catch (e) {
        console.warn(`Failed to remove expired cache item: ${key}`)
      }
    })

    return keysToRemove.length
  }

  /**
   * 检查是否为受保护的键
   */
  private isProtectedKey(key: string): boolean {
    const protectedPrefixes = [
      'fusion:auth:',
      'fusion:user:',
      'fusion:config:',
      'fusion:settings:',
      'logger-settings'
    ]

    return protectedPrefixes.some(prefix => key.startsWith(prefix))
  }

  /**
   * 格式化字节数
   */
  private formatBytes(bytes: number): string {
    if (bytes === 0) return '0 B'

    const k = 1024
    const sizes = ['B', 'KB', 'MB', 'GB']
    const i = Math.floor(Math.log(bytes) / Math.log(k))

    return parseFloat((bytes / Math.pow(k, i)).toFixed(2)) + ' ' + sizes[i]
  }

  /**
   * 安全的localStorage设置
   */
  safeSetItem(key: string, value: string): boolean {
    try {
      localStorage.setItem(key, value)
      return true
    } catch (e) {
      if (e instanceof DOMException && e.code === DOMException.QUOTA_EXCEEDED_ERR) {
        // 尝试清理空间
        const { cleaned, freedBytes } = this.smartCleanup()
        
        console.warn(`localStorage quota exceeded. Cleaned ${cleaned} items, freed ${this.formatBytes(freedBytes)}`)
        
        // 再次尝试设置
        try {
          localStorage.setItem(key, value)
          return true
        } catch (e2) {
          console.error('Failed to set localStorage item even after cleanup:', key)
          return false
        }
      } else {
        console.error('Failed to set localStorage item:', key, e)
        return false
      }
    }
  }

  /**
   * 安全的localStorage获取
   */
  safeGetItem(key: string): string | null {
    try {
      return localStorage.getItem(key)
    } catch (e) {
      console.error('Failed to get localStorage item:', key, e)
      return null
    }
  }

  /**
   * 安全的localStorage删除
   */
  safeRemoveItem(key: string): boolean {
    try {
      localStorage.removeItem(key)
      return true
    } catch (e) {
      console.error('Failed to remove localStorage item:', key, e)
      return false
    }
  }

  /**
   * 获取存储使用报告
   */
  getStorageReport(): string {
    const info = this.getStorageInfo()
    
    let report = `=== localStorage 使用报告 ===\n`
    report += `总使用量: ${this.formatBytes(info.used)} / ${this.formatBytes(info.total)} (${info.usagePercentage.toFixed(1)}%)\n`
    report += `可用空间: ${this.formatBytes(info.available)}\n\n`
    
    report += `=== 存储项详情 ===\n`
    info.items.forEach((item, index) => {
      const isProtected = this.isProtectedKey(item.key) ? ' [受保护]' : ''
      report += `${index + 1}. ${item.key}: ${item.sizeFormatted}${isProtected}\n`
    })

    return report
  }
}

// 导出单例实例
export const storageManager = StorageManager.getInstance()

// 导出便捷方法
export const storage = {
  setItem: (key: string, value: string) => storageManager.safeSetItem(key, value),
  getItem: (key: string) => storageManager.safeGetItem(key),
  removeItem: (key: string) => storageManager.safeRemoveItem(key),
  cleanup: () => storageManager.smartCleanup(),
  getInfo: () => storageManager.getStorageInfo(),
  getReport: () => storageManager.getStorageReport()
}
