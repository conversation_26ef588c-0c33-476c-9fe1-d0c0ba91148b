import { feature, LoadingManager, getAPI } from '../api-services/axios-utils'
import { HttpDebugApi } from '../api-services/apis/http-debug-api'
import { HttpRequestConfig, HttpResponseInfo } from '../api-services/models'
import { AxiosResponse } from 'axios'
import {
  RESTfulResultHttpResponseInfo,
  RESTfulResultListHttpRequestLog,
} from '../api-services/models'
import { stringToHttpMethod } from '../utils/http-method-mapping'

// 加载状态键
const LOADING_KEY = {
  SEND_REQUEST: 'http-tester.sendRequest',
  GET_HISTORY: 'http-tester.getHistory',
  CLEAR_HISTORY: 'http-tester.clearHistory',
}

// 前端Header格式
export interface Header {
  key: string
  value: string
}

// 前端Parameter格式
export interface Parameter {
  key: string
  value: string
}

// 前端响应格式
export interface HttpResponse {
  status: number
  statusText: string
  headers: Record<string, string>
  body: string
  time: number
}

/**
 * HTTP测试工具服务类
 */
class HttpTesterService {
  /**
   * 发送HTTP请求
   * @param method HTTP方法
   * @param url 请求URL
   * @param headers 请求头
   * @param parameters 请求参数
   * @param body 请求体
   * @returns 响应数据
   */
  async sendRequest(
    method: string,
    url: string,
    headers: Header[],
    parameters: Parameter[],
    body?: string
  ): Promise<HttpResponse | null> {
    try {
      LoadingManager.setLoading(LOADING_KEY.SEND_REQUEST, true)

      // 转换请求头格式
      const headersObj: Record<string, string> = {}
      headers.forEach((header) => {
        if (header.key && header.value) {
          headersObj[header.key] = header.value
        }
      })

      // 转换请求参数格式
      const parametersObj: Record<string, string> = {}
      parameters.forEach((param) => {
        if (param.key && param.value) {
          parametersObj[param.key] = param.value
        }
      })

      // 构建请求配置
      const requestConfig: HttpRequestConfig = {
        url,
        method: stringToHttpMethod(method),
        headers: headersObj,
        parameters: parametersObj,
        body: body || null,
      }

      // 发送请求
      const [error, response] = await feature<
        AxiosResponse<RESTfulResultHttpResponseInfo>
      >(getAPI(HttpDebugApi).sendRequest(requestConfig))

      if (error) {
        console.error('HTTP请求失败:', error)
        throw error
      }

      // 检查API响应是否成功
      if (!response?.data?.succeeded) {
        const errorMsg = response?.data?.errors || '请求失败'
        throw new Error(
          typeof errorMsg === 'string' ? errorMsg : JSON.stringify(errorMsg)
        )
      }

      // 获取响应数据
      const responseData = response.data.data

      if (!responseData) {
        throw new Error('未收到有效响应')
      }

      // 转换为前端响应格式
      return {
        status: responseData.statusCode || 0,
        statusText: responseData.error || '',
        headers: responseData.headers || {},
        body: responseData.body || '',
        time: responseData.responseTime || 0,
      }
    } catch (err) {
      const errorMessage = err instanceof Error ? err.message : '请求失败'
      throw new Error(errorMessage)
    } finally {
      LoadingManager.setLoading(LOADING_KEY.SEND_REQUEST, false)
    }
  }

  /**
   * 获取请求历史
   * @returns 请求历史列表
   */
  async getRequestHistory() {
    try {
      LoadingManager.setLoading(LOADING_KEY.GET_HISTORY, true)

      const [error, response] = await feature<
        AxiosResponse<RESTfulResultListHttpRequestLog>
      >(getAPI(HttpDebugApi).getRequestHistory())

      if (error) {
        console.error('获取请求历史失败:', error)
        return []
      }

      // 检查API响应是否成功
      if (!response?.data?.succeeded) {
        return []
      }

      return response.data.data || []
    } finally {
      LoadingManager.setLoading(LOADING_KEY.GET_HISTORY, false)
    }
  }

  /**
   * 清除请求历史
   * @returns 是否成功
   */
  async clearRequestHistory(): Promise<boolean> {
    try {
      LoadingManager.setLoading(LOADING_KEY.CLEAR_HISTORY, true)

      const [error, response] = await feature(
        getAPI(HttpDebugApi).clearRequestHistory()
      )

      if (error) {
        console.error('清除请求历史失败:', error)
        return false
      }

      return true
    } finally {
      LoadingManager.setLoading(LOADING_KEY.CLEAR_HISTORY, false)
    }
  }

  /**
   * 检查是否正在加载
   * @param key 加载状态键
   * @returns 是否正在加载
   */
  isLoading(key: string): boolean {
    return LoadingManager.isLoading(key)
  }
}

// 导出单例实例
export const httpTesterService = new HttpTesterService()
export { LOADING_KEY }
