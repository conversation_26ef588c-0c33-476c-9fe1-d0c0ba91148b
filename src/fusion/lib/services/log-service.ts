/**
 * 日志服务 - 封装日志API调用和数据转换
 * 提供统一的日志管理接口，包括文件列表获取、内容查看、文件操作等功能
 */

import { LogApi } from '../api-services/apis/log-api'
import { getAPI, getAxiosInstance } from '../api-services/axios-utils'
import {
  LogContentInput,
  LogFileDeleteInput,
  LogFileGroupDto,
  LogFileDto,
  LogContentDto,
  StreamLogContentDto,
} from '../api-services/models'
import { DownloadDiagnostics } from '../utils/download-diagnostics'
import { log, safeExecuteAsync, safeExecuteWithResult } from '../utils/safe-execute'

// UI层使用的数据结构
export interface LogFile {
  id: string
  name: string
  type: 'file' | 'folder'
  size?: string
  sizeBytes?: number
  fileSize?: number // 文件大小(KB) - 与后端API保持一致
  lastModified: string
  children?: LogFile[]
  content?: string
  level?: 'info' | 'warn' | 'error' | 'debug'
  lineCount?: number
  preview?: string
  // 新增字段用于API集成
  logLevel?: string
  fileName?: string
}

export interface LogLine {
  lineNumber: number
  content: string
  level?: 'info' | 'warn' | 'error' | 'debug'
}

export interface LogContentResult {
  lines: LogLine[]
  totalLines: number
  hasMore: boolean
}

/**
 * 日志服务类
 */
export class LogService {
  private logApi: LogApi

  constructor() {
    this.logApi = getAPI(LogApi)
  }

  /**
   * 获取日志文件树结构
   * 将API返回的分组数据转换为树形结构
   */
  async getLogFileTree(): Promise<LogFile[]> {
    try {
      const response = await this.logApi.getLogFiles()
      
      if (!response.data.succeeded || !response.data.data) {
        throw new Error(response.data.errors || '获取日志文件列表失败')
      }

      return this.transformToFileTree(response.data.data)
    } catch (error) {
      console.error('获取日志文件树失败:', error)
      throw error
    }
  }

  /**
   * 获取日志内容（分页）
   */
  async getLogContent(
    logLevel: string,
    fileName: string,
    options: {
      lines?: number
      startLine?: number
    } = {}
  ): Promise<LogContentResult> {
    try {
      const input: LogContentInput = {
        logLevel,
        fileName,
        lines: options.lines || 1000,
        startLine: options.startLine,
      }

      const response = await this.logApi.getLogContentAsync(input)

      if (!response.data.succeeded || !response.data.data) {
        throw new Error(response.data.errors || '获取日志内容失败')
      }

      return this.parseLogContent(response.data.data, options.startLine || 1)
    } catch (error) {
      console.error('获取日志内容失败:', error)
      throw error
    }
  }

  /**
   * 高性能批量获取日志内容（支持大数据集）
   */
  async getLogContentBulk(
    logLevel: string,
    fileName: string,
    options: {
      lines?: number
      startLine?: number
    } = {}
  ): Promise<LogContentResult> {
    try {
      const input: LogContentInput = {
        logLevel,
        fileName,
        lines: options.lines || 10000, // 默认更高的行数
        startLine: options.startLine,
      }

      const response = await this.logApi.getLogBulkAsync(input)

      if (!response.data.succeeded || !response.data.data) {
        throw new Error(response.data.errors || '获取日志内容失败')
      }

      return this.parseLogContent(response.data.data, options.startLine || 1)
    } catch (error) {
      console.error('批量获取日志内容失败:', error)
      throw error
    }
  }

  /**
   * 高性能获取完整日志文件内容
   * 使用批量模式获取大量日志内容
   */
  async getLogStreamFull(
    logLevel: string,
    fileName: string
  ): Promise<LogContentResult> {
    try {
      const input: LogContentInput = {
        logLevel,
        fileName,
        lines: 50000, // 获取大量内容（50K行）
        startLine: 1,
      }

      console.log('使用批量模式加载完整日志内容')
      const response = await this.logApi.getLogBulkAsync(input)

      if (!response.data.succeeded || !response.data.data) {
        throw new Error(response.data.errors || '获取日志内容失败')
      }

      return this.parseLogContent(response.data.data, 1)
    } catch (error) {
      console.error('获取日志内容失败:', error)
      throw error
    }
  }



  /**
   * 流式获取日志内容
   */
  async streamLogContent(
    logLevel: string,
    fileName: string,
    options: {
      lines?: number
      keyword?: string
    } = {}
  ): Promise<StreamLogContentDto[]> {
    try {
      const input: LogContentInput = {
        logLevel,
        fileName,
        lines: options.lines || 1000,
      }

      const response = await this.logApi.streamLogContentAsync(input)
      
      if (!response.data) {
        throw new Error('流式获取日志内容失败')
      }

      return response.data
    } catch (error) {
      console.error('流式获取日志内容失败:', error)
      throw error
    }
  }

  /**
   * 下载日志文件
   */
  async downloadLogFile(
    logLevel: string,
    fileName: string,
    onProgress?: (progress: number) => void
  ): Promise<void> {
    let retryCount = 0
    const maxRetries = 3

    while (retryCount <= maxRetries) {
      try {
        // 直接使用axios调用下载接口，绕过生成的API类
        // 因为生成的API类不支持blob响应类型
        const axios = getAxiosInstance()

        const response = await axios.get('/api/log/download', {
          params: {
            logLevel,
            fileName
          },
          responseType: 'blob', // 重要：指定响应类型为blob
          timeout: 300000, // 5分钟超时
          onDownloadProgress: (progressEvent) => {
            if (onProgress) {
              if (progressEvent.total) {
                const progress = Math.round((progressEvent.loaded * 100) / progressEvent.total)
                onProgress(progress)
              } else {
                // 如果没有total，根据已下载的字节数估算进度
                const estimatedProgress = Math.min(90, Math.floor(progressEvent.loaded / 10000))
                onProgress(estimatedProgress)
              }
            }
          }
        })

        // 验证响应数据
        if (!response.data || response.data.size === 0) {
          throw new Error('下载的文件为空')
        }

        // 创建blob URL并触发下载
        const blob = new Blob([response.data], {
          type: 'application/octet-stream'
        })

        // 验证blob大小
        console.log(`下载完成，文件大小: ${blob.size} 字节`)

        const url = window.URL.createObjectURL(blob)
        const link = document.createElement('a')
        link.href = url
        link.download = fileName

        // 添加到DOM并触发点击
        document.body.appendChild(link)
        link.click()

        // 延迟清理，确保下载开始
        setTimeout(() => {
          document.body.removeChild(link)
          window.URL.revokeObjectURL(url)
        }, 100)

        // 下载成功，退出重试循环
        return

      } catch (error: any) {
        console.error(`下载尝试 ${retryCount + 1} 失败:`, error)

        // 检查是否是内容长度不匹配错误
        if (error.code === 'ERR_CONTENT_LENGTH_MISMATCH' ||
            error.message?.includes('content-length mismatch')) {

          retryCount++
          if (retryCount <= maxRetries) {
            console.log(`检测到内容长度不匹配，正在进行第 ${retryCount} 次重试...`)
            // 等待一段时间后重试
            await new Promise(resolve => setTimeout(resolve, 1000 * retryCount))
            continue
          }
        }

        // 如果是其他错误或重试次数用完，抛出错误
        if (retryCount >= maxRetries) {
          throw new Error(`下载失败，已重试 ${maxRetries} 次。错误: ${error.message || '网络错误'}`)
        } else {
          throw error
        }
      }
    }
  }

  /**
   * 备用下载方法 - 使用隐藏链接方式避免HEAD请求
   * 当axios下载失败时使用此方法
   */
  async downloadLogFileAlternative(
    logLevel: string,
    fileName: string
  ): Promise<void> {
    try {
      // 构建下载URL
      const axios = getAxiosInstance()
      const baseURL = axios.defaults.baseURL || 'http://nervecube.cn:5005'
      const downloadUrl = `${baseURL}/api/log/download?logLevel=${encodeURIComponent(logLevel)}&fileName=${encodeURIComponent(fileName)}`

      // 获取访问令牌
      const accessToken = window.localStorage.getItem('access-token')

      // 创建隐藏的下载链接，避免使用window.open导致的HEAD请求
      const link = document.createElement('a')
      link.href = downloadUrl
      link.download = fileName
      link.style.display = 'none'

      // 如果有token，需要通过其他方式传递（注意：a标签无法设置请求头）
      if (accessToken) {
        // 对于需要认证的下载，仍然使用fetch方法
        console.warn('备用下载方法：有token时建议使用fetch方法')
      }

      // 添加到DOM并触发点击
      document.body.appendChild(link)
      link.click()

      // 立即移除链接
      setTimeout(() => {
        document.body.removeChild(link)
      }, 100)

    } catch (error) {
      console.error('备用下载方法失败:', error)
      throw error
    }
  }

  /**
   * 使用Fetch API的下载方法 - 更好地处理流式下载
   */
  async downloadLogFileWithFetch(
    logLevel: string,
    fileName: string,
    onProgress?: (progress: number) => void
  ): Promise<void> {
    try {
      // 获取axios实例的配置
      const axios = getAxiosInstance()
      const baseURL = axios.defaults.baseURL || 'http://nervecube.cn:5005'
      const accessToken = window.localStorage.getItem('access-token')

      // 构建请求URL
      const url = new URL('/api/log/download', baseURL)
      url.searchParams.set('logLevel', logLevel)
      url.searchParams.set('fileName', fileName)

      // 构建请求头
      const headers: HeadersInit = {
        'Accept': 'application/octet-stream',
      }

      if (accessToken) {
        headers['Authorization'] = `Bearer ${accessToken}`
      }

      // 发起fetch请求
      const response = await fetch(url.toString(), {
        method: 'GET',
        headers,
      })

      if (!response.ok) {
        throw new Error(`HTTP ${response.status}: ${response.statusText}`)
      }

      // 获取内容长度
      const contentLength = response.headers.get('Content-Length')
      const total = contentLength ? parseInt(contentLength, 10) : 0

      // 读取响应流
      const reader = response.body?.getReader()
      if (!reader) {
        throw new Error('无法获取响应流')
      }

      const chunks: Uint8Array[] = []
      let loaded = 0

      try {
        while (true) {
          const { done, value } = await reader.read()

          if (done) break

          if (value) {
            chunks.push(value)
            loaded += value.length

            // 更新进度
            if (onProgress && total > 0) {
              const progress = Math.round((loaded * 100) / total)
              onProgress(progress)
            }
          }
        }
      } finally {
        reader.releaseLock()
      }

      // 合并所有数据块
      const totalLength = chunks.reduce((acc, chunk) => acc + chunk.length, 0)
      const result = new Uint8Array(totalLength)
      let offset = 0

      for (const chunk of chunks) {
        result.set(chunk, offset)
        offset += chunk.length
      }

      // 创建blob并触发下载
      const blob = new Blob([result], { type: 'application/octet-stream' })
      console.log(`Fetch下载完成，文件大小: ${blob.size} 字节`)

      const url_blob = window.URL.createObjectURL(blob)
      const link = document.createElement('a')
      link.href = url_blob
      link.download = fileName

      document.body.appendChild(link)
      link.click()

      setTimeout(() => {
        document.body.removeChild(link)
        window.URL.revokeObjectURL(url_blob)
      }, 100)

    } catch (error) {
      console.error('Fetch下载失败:', error)
      throw error
    }
  }

  /**
   * 智能下载方法 - 自动选择最佳下载方式
   */
  async smartDownloadLogFile(
    logLevel: string,
    fileName: string,
    onProgress?: (progress: number) => void
  ): Promise<void> {
    const axios = getAxiosInstance()
    const baseURL = axios.defaults.baseURL || 'http://nervecube.cn:5005'
    const downloadUrl = `${baseURL}/api/log/download?logLevel=${encodeURIComponent(logLevel)}&fileName=${encodeURIComponent(fileName)}`

    // 记录开始下载
    DownloadDiagnostics.record({
      url: downloadUrl,
      method: 'GET',
      headers: {
        'Authorization': 'Bearer ***',
        'Accept': 'application/octet-stream',
      },
    })

    try {
      // 直接开始下载，不进行HEAD请求测试
      log.info('开始下载日志文件', { logLevel, fileName })

      // 首先尝试Fetch API方法
      await this.downloadLogFileWithFetch(logLevel, fileName, onProgress)

      // 记录成功
      DownloadDiagnostics.record({
        url: downloadUrl,
        method: 'GET',
        headers: { 'Authorization': 'Bearer ***' },
      })

    } catch (fetchError: any) {
      log.warn('Fetch下载方法失败，尝试axios方法', {
        error: fetchError.message,
        logLevel,
        fileName
      })

      // 记录Fetch错误
      DownloadDiagnostics.record({
        url: downloadUrl,
        method: 'GET (Fetch)',
        headers: { 'Authorization': 'Bearer ***' },
        error: fetchError.message,
      })

      try {
        // 尝试标准axios下载方法
        await this.downloadLogFile(logLevel, fileName, onProgress)

        // 记录axios成功
        DownloadDiagnostics.record({
          url: downloadUrl,
          method: 'GET (Axios)',
          headers: { 'Authorization': 'Bearer ***' },
        })

      } catch (axiosError: any) {
        console.warn('Axios下载方法失败，尝试备用方法:', axiosError.message)

        // 记录axios错误
        DownloadDiagnostics.record({
          url: downloadUrl,
          method: 'GET (Axios)',
          headers: { 'Authorization': 'Bearer ***' },
          error: axiosError.message,
        })

        // 如果是内容长度不匹配错误，尝试备用方法
        if (axiosError.code === 'ERR_CONTENT_LENGTH_MISMATCH' ||
            axiosError.message?.includes('content-length mismatch') ||
            axiosError.message?.includes('Network Error')) {

          try {
            await this.downloadLogFileAlternative(logLevel, fileName)
            // 备用方法成功，更新进度为100%
            if (onProgress) {
              onProgress(100)
            }

            // 记录备用方法成功
            DownloadDiagnostics.record({
              url: downloadUrl,
              method: 'GET (Alternative)',
              headers: { 'Authorization': 'Bearer ***' },
            })

          } catch (alternativeError: any) {
            console.error('所有下载方法都失败了')

            // 记录最终失败
            DownloadDiagnostics.record({
              url: downloadUrl,
              method: 'GET (Alternative)',
              headers: { 'Authorization': 'Bearer ***' },
              error: alternativeError.message,
            })

            // 生成诊断报告
            const analysis = DownloadDiagnostics.analyzeProblem(axiosError)
            console.error('下载问题分析:', analysis)

            throw new Error(`下载失败: 尝试了多种方法都无法下载文件。\n\n问题分析:\n${analysis}`)
          }
        } else {
          // 其他错误直接抛出
          const analysis = DownloadDiagnostics.analyzeProblem(axiosError)
          console.error('下载问题分析:', analysis)
          throw new Error(`${axiosError.message}\n\n问题分析:\n${analysis}`)
        }
      }
    }
  }

  /**
   * 删除单个日志文件
   */
  async deleteLogFile(logLevel: string, fileName: string): Promise<void> {
    try {
      const response = await this.logApi.deleteLogFile(logLevel, fileName)
      
      if (!response.data.succeeded) {
        throw new Error(response.data.errors || '删除日志文件失败')
      }
    } catch (error) {
      console.error('删除日志文件失败:', error)
      throw error
    }
  }

  /**
   * 批量删除日志文件
   */
  async batchDeleteLogFiles(files: Array<{ logLevel: string; fileName: string }>): Promise<void> {
    try {
      const input: LogFileDeleteInput[] = files.map(file => ({
        logLevel: file.logLevel,
        fileName: file.fileName,
      }))

      const response = await this.logApi.batchDeleteLogFiles(input)
      
      if (!response.data.succeeded) {
        throw new Error(response.data.errors || '批量删除日志文件失败')
      }
    } catch (error) {
      console.error('批量删除日志文件失败:', error)
      throw error
    }
  }

  /**
   * 将API返回的分组数据转换为树形结构
   */
  private transformToFileTree(groups: LogFileGroupDto[]): LogFile[] {
    return groups.map(group => ({
      id: `folder-${group.logLevel}`,
      name: this.getLogLevelDisplayName(group.logLevel || ''),
      type: 'folder' as const,
      lastModified: new Date().toISOString().split('T')[0],
      level: this.mapLogLevel(group.logLevel || ''),
      children: group.files?.map(file => this.transformFileDto(file, group.logLevel || '')) || [],
    }))
  }

  /**
   * 转换单个文件DTO
   */
  private transformFileDto(file: LogFileDto, logLevel: string): LogFile {
    return {
      id: `file-${logLevel}-${file.fileName}`,
      name: file.fileName || '',
      type: 'file' as const,
      size: this.formatFileSize(file.fileSize || 0),
      sizeBytes: (file.fileSize || 0) * 1024, // API返回的是KB，转换为字节
      fileSize: file.fileSize || 0, // 保持原始KB值
      lastModified: this.formatDate(file.lastWriteTime),
      level: this.mapLogLevel(file.logLevel || logLevel),
      logLevel: file.logLevel || logLevel,
      fileName: file.fileName || '',
      lineCount: file.lineCount || 0,
      preview: `${file.fileName} - ${this.formatFileSize(file.fileSize || 0)}`,
    }
  }

  /**
   * 解析日志内容
   */
  private parseLogContent(content: LogContentDto, fallbackStartLine: number): LogContentResult {
    const lines: LogLine[] = []

    if (content.content) {
      const contentLines = content.content.split('\n')
      const actualStartLine = content.startLine || fallbackStartLine

      contentLines.forEach((line, index) => {
        if (line.trim()) {
          lines.push({
            lineNumber: actualStartLine + index,
            content: line,
            level: this.detectLogLevel(line),
          })
        }
      })
    }

    return {
      lines,
      totalLines: content.totalLines || lines.length,
      hasMore: content.hasMore || false,
    }
  }

  /**
   * 格式化文件大小
   */
  private formatFileSize(sizeKB: number): string {
    if (sizeKB < 1024) {
      return `${sizeKB.toFixed(1)} KB`
    } else if (sizeKB < 1024 * 1024) {
      return `${(sizeKB / 1024).toFixed(1)} MB`
    } else {
      return `${(sizeKB / (1024 * 1024)).toFixed(1)} GB`
    }
  }

  /**
   * 格式化日期
   */
  private formatDate(date: any): string {
    if (!date) return new Date().toISOString().split('T')[0]
    
    try {
      return new Date(date).toLocaleString('zh-CN', {
        year: 'numeric',
        month: '2-digit',
        day: '2-digit',
        hour: '2-digit',
        minute: '2-digit',
      })
    } catch {
      return new Date().toISOString().split('T')[0]
    }
  }

  /**
   * 映射日志级别
   */
  private mapLogLevel(level: string): 'info' | 'warn' | 'error' | 'debug' {
    const lowerLevel = level.toLowerCase()
    if (lowerLevel.includes('error')) return 'error'
    if (lowerLevel.includes('warn')) return 'warn'
    if (lowerLevel.includes('debug')) return 'debug'
    return 'info'
  }

  /**
   * 获取日志级别显示名称
   */
  private getLogLevelDisplayName(level: string): string {
    const levelMap: Record<string, string> = {
      'Information': '信息日志',
      'Warning': '警告日志',
      'Error': '错误日志',
      'Debug': '调试日志',
      'Trace': '跟踪日志',
    }
    return levelMap[level] || level || '未知日志'
  }

  /**
   * 从日志行内容检测日志级别
   */
  private detectLogLevel(content: string): 'info' | 'warn' | 'error' | 'debug' {
    const lowerContent = content.toLowerCase()
    if (lowerContent.includes('error') || lowerContent.includes('exception')) return 'error'
    if (lowerContent.includes('warn')) return 'warn'
    if (lowerContent.includes('debug')) return 'debug'
    return 'info'
  }
}

// 导出单例实例
export const logService = new LogService()
