/**
 * 真实日志数据源 - 替换模拟的VirtualLogDataSource
 * 使用真实的API调用来获取日志数据，支持分页和搜索
 */

import { logService, LogLine, LogFile } from './log-service'

// 数据块结构
export interface DataChunk {
  startLine: number
  endLine: number
  lines: LogLine[]
  loaded: boolean
}

/**
 * 真实日志数据源类
 * 使用LogService来获取真实的日志数据
 */
export class RealLogDataSource {
  private file: LogFile
  private chunkSize = 100 // 每个数据块100行
  private chunks: Map<number, DataChunk> = new Map()
  private totalLines: number = 0
  private useBulkMode: boolean = false
  private useStreamMode: boolean = false
  private fullContentLoaded: boolean = false
  private allLines: LogLine[] = []

  constructor(file: LogFile, useBulkMode: boolean = false, useStreamMode: boolean = false) {
    this.file = file
    this.useBulkMode = useBulkMode
    this.useStreamMode = useStreamMode
    this.totalLines = file.lineCount || 0

    // 根据文件大小和模式选择策略
    const fileSizeKB = file.fileSize || 0

    if (useStreamMode || fileSizeKB < 10240) { // 小于10MB的文件使用流式加载
      this.useStreamMode = true
      this.chunkSize = 10000 // 流式模式使用更大的块
    } else if (useBulkMode) {
      this.chunkSize = 1000
    }
  }

  /**
   * 获取总行数
   */
  getTotalLines(): number {
    return this.totalLines
  }

  /**
   * 设置批量模式
   */
  setBulkMode(useBulkMode: boolean) {
    if (this.useBulkMode !== useBulkMode) {
      this.useBulkMode = useBulkMode

      // 调整块大小
      this.chunkSize = useBulkMode ? 1000 : 100

      // 清除缓存，重新加载数据
      this.chunks.clear()
    }
  }

  /**
   * 异步加载数据块
   */
  async loadChunk(chunkIndex: number): Promise<DataChunk> {
    // 如果使用流式模式且是第一次加载，尝试加载完整文件
    if (this.useStreamMode && chunkIndex === 0 && !this.fullContentLoaded) {
      return await this.loadFullContentStream()
    }

    // 检查缓存
    if (this.chunks.has(chunkIndex)) {
      return this.chunks.get(chunkIndex)!
    }

    // 如果已经加载了完整内容，从内存中返回块
    if (this.fullContentLoaded && this.allLines.length > 0) {
      return this.getChunkFromMemory(chunkIndex)
    }

    try {
      const startLine = chunkIndex * this.chunkSize + 1
      const lines = this.chunkSize

      // 选择使用批量模式还是普通模式
      const useLogService = this.useBulkMode ?
        logService.getLogContentBulk.bind(logService) :
        logService.getLogContent.bind(logService)

      // 调用真实API获取日志内容
      const result = await useLogService(
        this.file.logLevel || '',
        this.file.fileName || '',
        {
          lines,
          startLine,
        }
      )

      // 更新总行数（如果API返回了新的总行数）
      if (result.totalLines > 0 && result.totalLines !== this.totalLines) {
        this.totalLines = result.totalLines
      }

      const chunk: DataChunk = {
        startLine: startLine - 1, // 转换为0基索引
        endLine: Math.min(startLine - 1 + result.lines.length, this.totalLines),
        lines: result.lines,
        loaded: true,
      }

      // 缓存数据块
      this.chunks.set(chunkIndex, chunk)
      return chunk
    } catch (error) {
      console.error('加载日志数据块失败:', error)

      // 返回空数据块
      const chunk: DataChunk = {
        startLine: chunkIndex * this.chunkSize,
        endLine: chunkIndex * this.chunkSize,
        lines: [],
        loaded: false,
      }

      return chunk
    }
  }

  /**
   * 使用流式模式加载完整文件内容
   */
  private async loadFullContentStream(): Promise<DataChunk> {
    try {
      console.log('Loading full content using stream mode...')

      const result = await logService.getLogStreamFull(
        this.file.logLevel || '',
        this.file.fileName || ''
      )

      // 存储所有行到内存
      this.allLines = result.lines
      this.totalLines = result.totalLines || result.lines.length
      this.fullContentLoaded = true

      console.log(`Stream mode loaded ${this.allLines.length} lines`)

      // 返回第一个块
      return this.getChunkFromMemory(0)
    } catch (error) {
      console.error('Failed to load full content stream:', error)
      // 回退到普通模式
      this.useStreamMode = false
      return await this.loadChunk(0)
    }
  }

  /**
   * 从内存中获取指定块
   */
  private getChunkFromMemory(chunkIndex: number): DataChunk {
    const startIndex = chunkIndex * this.chunkSize
    const endIndex = Math.min(startIndex + this.chunkSize, this.allLines.length)
    const chunkLines = this.allLines.slice(startIndex, endIndex)

    const chunk: DataChunk = {
      startLine: startIndex,
      endLine: endIndex - 1,
      lines: chunkLines,
      loaded: true,
    }

    // 缓存块
    this.chunks.set(chunkIndex, chunk)
    return chunk
  }

  /**
   * 获取所有已加载的日志行（用于前端搜索）
   */
  getAllLoadedLines(): LogLine[] {
    // 如果已经加载了完整内容，直接返回
    if (this.fullContentLoaded && this.allLines.length > 0) {
      return this.allLines
    }

    // 否则从数据块中收集
    const allLines: LogLine[] = []

    // 按顺序收集所有已加载的数据块
    const sortedChunks = Array.from(this.chunks.entries())
      .sort(([a], [b]) => a - b)

    for (const [, chunk] of sortedChunks) {
      if (chunk.loaded) {
        allLines.push(...chunk.lines)
      }
    }

    return allLines
  }

  /**
   * 预加载相邻的数据块
   * 提高用户体验
   */
  async preloadAdjacentChunks(currentChunkIndex: number) {
    const totalChunks = Math.ceil(this.totalLines / this.chunkSize)
    const preloadPromises: Promise<DataChunk>[] = []

    // 预加载前一个和后一个数据块
    if (currentChunkIndex > 0) {
      preloadPromises.push(this.loadChunk(currentChunkIndex - 1))
    }
    if (currentChunkIndex < totalChunks - 1) {
      preloadPromises.push(this.loadChunk(currentChunkIndex + 1))
    }

    // 并行预加载，但不等待结果
    Promise.all(preloadPromises).catch(error => {
      console.warn('预加载数据块失败:', error)
    })
  }

  /**
   * 清除缓存
   */
  clearCache() {
    this.chunks.clear()
  }

  /**
   * 获取缓存统计信息
   */
  getCacheStats() {
    return {
      cachedChunks: this.chunks.size,
      totalChunks: Math.ceil(this.totalLines / this.chunkSize),
      cacheHitRate: this.chunks.size / Math.ceil(this.totalLines / this.chunkSize),
    }
  }

  /**
   * 估算文件行数（如果API没有提供）
   */
  async estimateLineCount(): Promise<number> {
    try {
      // 获取一小部分内容来估算总行数
      const sampleResult = await logService.getLogContent(
        this.file.logLevel || '',
        this.file.fileName || '',
        {
          lines: 100,
        }
      )

      if (sampleResult.lines.length > 0 && this.file.sizeBytes) {
        // 基于文件大小和样本行数估算总行数
        const avgLineSize = this.file.sizeBytes / sampleResult.lines.length
        const estimatedLines = Math.floor(this.file.sizeBytes / avgLineSize)
        
        this.totalLines = estimatedLines
        return estimatedLines
      }

      return sampleResult.lines.length
    } catch (error) {
      console.error('估算文件行数失败:', error)
      return 0
    }
  }

  /**
   * 获取文件预览内容
   */
  async getPreview(maxLines: number = 10): Promise<LogLine[]> {
    try {
      const result = await logService.getLogContent(
        this.file.logLevel || '',
        this.file.fileName || '',
        {
          lines: maxLines,
        }
      )

      return result.lines
    } catch (error) {
      console.error('获取文件预览失败:', error)
      return []
    }
  }

  
}
