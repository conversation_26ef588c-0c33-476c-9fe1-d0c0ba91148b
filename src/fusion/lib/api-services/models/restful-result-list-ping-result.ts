/* tslint:disable */
/* eslint-disable */
/**
 * All Groups
 * No description provided (generated by Swagger Codegen https://github.com/swagger-api/swagger-codegen)
 *
 * OpenAPI spec version: 1.0.0
 * 
 *
 * NOTE: This class is auto generated by the swagger code generator program.
 * https://github.com/swagger-api/swagger-codegen.git
 * Do not edit the class manually.
 */

import { PingResult } from './ping-result';
 /**
 * 
 *
 * @export
 * @interface RESTfulResultListPingResult
 */
export interface RESTfulResultListPingResult {

    /**
     * @type {number}
     * @memberof RESTfulResultListPingResult
     */
    statusCode?: number | null;

    /**
     * @type {Array<PingResult>}
     * @memberof RESTfulResultListPingResult
     */
    data?: Array<PingResult> | null;

    /**
     * @type {boolean}
     * @memberof RESTfulResultListPingResult
     */
    succeeded?: boolean;

    /**
     * @type {any}
     * @memberof RESTfulResultListPingResult
     */
    errors?: any | null;

    /**
     * @type {any}
     * @memberof RESTfulResultListPingResult
     */
    extras?: any | null;

    /**
     * @type {number}
     * @memberof RESTfulResultListPingResult
     */
    timestamp?: number;
}
