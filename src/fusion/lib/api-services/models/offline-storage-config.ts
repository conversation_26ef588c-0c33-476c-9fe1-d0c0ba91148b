/* tslint:disable */
/* eslint-disable */
/**
 * All Groups
 * No description provided (generated by Swagger Codegen https://github.com/swagger-api/swagger-codegen)
 *
 * OpenAPI spec version: 1.0.0
 * 
 *
 * NOTE: This class is auto generated by the swagger code generator program.
 * https://github.com/swagger-api/swagger-codegen.git
 * Do not edit the class manually.
 */

 /**
 * 
 *
 * @export
 * @interface OfflineStorageConfig
 */
export interface OfflineStorageConfig {

    /**
     * @type {boolean}
     * @memberof OfflineStorageConfig
     */
    offlineStorage?: boolean;

    /**
     * @type {number}
     * @memberof OfflineStorageConfig
     */
    batchSize?: number;

    /**
     * @type {number}
     * @memberof OfflineStorageConfig
     */
    storageLimit?: number;

    /**
     * @type {string}
     * @memberof OfflineStorageConfig
     */
    storageMode?: string | null;

    /**
     * @type {number}
     * @memberof OfflineStorageConfig
     */
    expireDays?: number;

    /**
     * @type {number}
     * @memberof OfflineStorageConfig
     */
    cleanExpireHours?: number;
}
