/* tslint:disable */
/* eslint-disable */
/**
 * All Groups
 * No description provided (generated by Swagger Codegen https://github.com/swagger-api/swagger-codegen)
 *
 * OpenAPI spec version: 1.0.0
 * 
 *
 * NOTE: This class is auto generated by the swagger code generator program.
 * https://github.com/swagger-api/swagger-codegen.git
 * Do not edit the class manually.
 */

import { IActionResult } from './iaction-result';
 /**
 * 
 *
 * @export
 * @interface RESTfulResultIActionResult
 */
export interface RESTfulResultIActionResult {

    /**
     * @type {number}
     * @memberof RESTfulResultIActionResult
     */
    statusCode?: number | null;

    /**
     * @type {IActionResult}
     * @memberof RESTfulResultIActionResult
     */
    data?: IActionResult;

    /**
     * @type {boolean}
     * @memberof RESTfulResultIActionResult
     */
    succeeded?: boolean;

    /**
     * @type {any}
     * @memberof RESTfulResultIActionResult
     */
    errors?: any | null;

    /**
     * @type {any}
     * @memberof RESTfulResultIActionResult
     */
    extras?: any | null;

    /**
     * @type {number}
     * @memberof RESTfulResultIActionResult
     */
    timestamp?: number;
}
