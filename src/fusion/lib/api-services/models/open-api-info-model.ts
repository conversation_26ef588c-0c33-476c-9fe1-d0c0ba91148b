/* tslint:disable */
/* eslint-disable */
/**
 * All Groups
 * No description provided (generated by Swagger Codegen https://github.com/swagger-api/swagger-codegen)
 *
 * OpenAPI spec version: 1.0.0
 * 
 *
 * NOTE: This class is auto generated by the swagger code generator program.
 * https://github.com/swagger-api/swagger-codegen.git
 * Do not edit the class manually.
 */

import { OpenApiParameterModel } from './open-api-parameter-model';
 /**
 * 
 *
 * @export
 * @interface OpenApiInfoModel
 */
export interface OpenApiInfoModel {

    /**
     * @type {string}
     * @memberof OpenApiInfoModel
     */
    path?: string | null;

    /**
     * @type {string}
     * @memberof OpenApiInfoModel
     */
    httpMethod?: string | null;

    /**
     * @type {string}
     * @memberof OpenApiInfoModel
     */
    name?: string | null;

    /**
     * @type {string}
     * @memberof OpenApiInfoModel
     */
    description?: string | null;

    /**
     * @type {string}
     * @memberof OpenApiInfoModel
     */
    groupName?: string | null;

    /**
     * @type {boolean}
     * @memberof OpenApiInfoModel
     */
    requireAuthentication?: boolean;

    /**
     * @type {number}
     * @memberof OpenApiInfoModel
     */
    rateLimit?: number;

    /**
     * @type {boolean}
     * @memberof OpenApiInfoModel
     */
    allowCache?: boolean;

    /**
     * @type {number}
     * @memberof OpenApiInfoModel
     */
    cacheExpiration?: number;

    /**
     * @type {Array<OpenApiParameterModel>}
     * @memberof OpenApiInfoModel
     */
    parameters?: Array<OpenApiParameterModel> | null;
}
