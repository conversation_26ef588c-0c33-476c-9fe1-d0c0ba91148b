/* tslint:disable */
/* eslint-disable */
/**
 * All Groups
 * No description provided (generated by Swagger Codegen https://github.com/swagger-api/swagger-codegen)
 *
 * OpenAPI spec version: 1.0.0
 * 
 *
 * NOTE: This class is auto generated by the swagger code generator program.
 * https://github.com/swagger-api/swagger-codegen.git
 * Do not edit the class manually.
 */

import { NetworkLinkInfo } from './network-link-info';
import { NetworkNodeInfo } from './network-node-info';
 /**
 * ??????
 *
 * @export
 * @interface NetworkDiscoveryResult
 */
export interface NetworkDiscoveryResult {

    /**
     * ???????
     *
     * @type {Array<NetworkNodeInfo>}
     * @memberof NetworkDiscoveryResult
     */
    discoveredNodes?: Array<NetworkNodeInfo> | null;

    /**
     * ???????
     *
     * @type {Array<NetworkLinkInfo>}
     * @memberof NetworkDiscoveryResult
     */
    discoveredLinks?: Array<NetworkLinkInfo> | null;

    /**
     * ????
     *
     * @type {Date}
     * @memberof NetworkDiscoveryResult
     */
    discoveryTime?: Date;
}
