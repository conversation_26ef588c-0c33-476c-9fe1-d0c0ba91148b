/* tslint:disable */
/* eslint-disable */
/**
 * All Groups
 * No description provided (generated by Swagger Codegen https://github.com/swagger-api/swagger-codegen)
 *
 * OpenAPI spec version: 1.0.0
 * 
 *
 * NOTE: This class is auto generated by the swagger code generator program.
 * https://github.com/swagger-api/swagger-codegen.git
 * Do not edit the class manually.
 */

import { MenuTreeOutput } from './menu-tree-output';
import { MenuTypeEnum } from './menu-type-enum';
 /**
 * ?????
 *
 * @export
 * @interface MenuTreeOutput
 */
export interface MenuTreeOutput {

    /**
     * ??ID
     *
     * @type {number}
     * @memberof MenuTreeOutput
     */
    id?: number;

    /**
     * ????
     *
     * @type {string}
     * @memberof MenuTreeOutput
     */
    name?: string | null;

    /**
     * ????
     *
     * @type {string}
     * @memberof MenuTreeOutput
     */
    code?: string | null;

    /**
     * ????
     *
     * @type {string}
     * @memberof MenuTreeOutput
     */
    path?: string | null;

    /**
     * ??
     *
     * @type {string}
     * @memberof MenuTreeOutput
     */
    icon?: string | null;

    /**
     * ????ID
     *
     * @type {number}
     * @memberof MenuTreeOutput
     */
    parentId?: number | null;

    /**
     * @type {MenuTypeEnum}
     * @memberof MenuTreeOutput
     */
    menuType?: MenuTypeEnum;

    /**
     * ??????
     *
     * @type {string}
     * @memberof MenuTreeOutput
     */
    menuTypeDesc?: string | null;

    /**
     * ??
     *
     * @type {number}
     * @memberof MenuTreeOutput
     */
    sort?: number;

    /**
     * ??
     *
     * @type {boolean}
     * @memberof MenuTreeOutput
     */
    status?: boolean;

    /**
     * ????
     *
     * @type {boolean}
     * @memberof MenuTreeOutput
     */
    hidden?: boolean;

    /**
     * ????
     *
     * @type {Date}
     * @memberof MenuTreeOutput
     */
    createTime?: Date | null;

    /**
     * ?????
     *
     * @type {Array<MenuTreeOutput>}
     * @memberof MenuTreeOutput
     */
    children?: Array<MenuTreeOutput> | null;
}
