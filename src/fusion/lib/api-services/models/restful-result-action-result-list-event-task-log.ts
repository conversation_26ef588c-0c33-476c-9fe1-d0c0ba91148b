/* tslint:disable */
/* eslint-disable */
/**
 * All Groups
 * No description provided (generated by Swagger Codegen https://github.com/swagger-api/swagger-codegen)
 *
 * OpenAPI spec version: 1.0.0
 * 
 *
 * NOTE: This class is auto generated by the swagger code generator program.
 * https://github.com/swagger-api/swagger-codegen.git
 * Do not edit the class manually.
 */

import { ActionResultListEventTaskLog } from './action-result-list-event-task-log';
 /**
 * 
 *
 * @export
 * @interface RESTfulResultActionResultListEventTaskLog
 */
export interface RESTfulResultActionResultListEventTaskLog {

    /**
     * @type {number}
     * @memberof RESTfulResultActionResultListEventTaskLog
     */
    statusCode?: number | null;

    /**
     * @type {ActionResultListEventTaskLog}
     * @memberof RESTfulResultActionResultListEventTaskLog
     */
    data?: ActionResultListEventTaskLog;

    /**
     * @type {boolean}
     * @memberof RESTfulResultActionResultListEventTaskLog
     */
    succeeded?: boolean;

    /**
     * @type {any}
     * @memberof RESTfulResultActionResultListEventTaskLog
     */
    errors?: any | null;

    /**
     * @type {any}
     * @memberof RESTfulResultActionResultListEventTaskLog
     */
    extras?: any | null;

    /**
     * @type {number}
     * @memberof RESTfulResultActionResultListEventTaskLog
     */
    timestamp?: number;
}
