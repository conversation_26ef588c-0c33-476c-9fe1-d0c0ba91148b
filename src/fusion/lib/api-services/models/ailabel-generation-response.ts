/* tslint:disable */
/* eslint-disable */
/**
 * All Groups
 * No description provided (generated by Swagger Codegen https://github.com/swagger-api/swagger-codegen)
 *
 * OpenAPI spec version: 1.0.0
 * 
 *
 * NOTE: This class is auto generated by the swagger code generator program.
 * https://github.com/swagger-api/swagger-codegen.git
 * Do not edit the class manually.
 */

import { DeviceVariableAddInput } from './device-variable-add-input';
import { LabelFieldMetadata } from './label-field-metadata';
 /**
 * AI????????
 *
 * @export
 * @interface AILabelGenerationResponse
 */
export interface AILabelGenerationResponse {

    /**
     * ????
     *
     * @type {boolean}
     * @memberof AILabelGenerationResponse
     */
    success?: boolean;

    /**
     * ????
     *
     * @type {string}
     * @memberof AILabelGenerationResponse
     */
    errorMessage?: string | null;

    /**
     * ?????????
     *
     * @type {Array<DeviceVariableAddInput>}
     * @memberof AILabelGenerationResponse
     */
    labels?: Array<DeviceVariableAddInput> | null;

    /**
     * @type {LabelFieldMetadata}
     * @memberof AILabelGenerationResponse
     */
    metadata?: LabelFieldMetadata;

    /**
     * ?????
     *
     * @type {Date}
     * @memberof AILabelGenerationResponse
     */
    generatedAt?: Date;

    /**
     * ????
     *
     * @type {string}
     * @memberof AILabelGenerationResponse
     */
    protocolType?: string | null;

    /**
     * ??ID
     *
     * @type {number}
     * @memberof AILabelGenerationResponse
     */
    deviceId?: number;
}
