/* tslint:disable */
/* eslint-disable */
/**
 * All Groups
 * No description provided (generated by Swagger Codegen https://github.com/swagger-api/swagger-codegen)
 *
 * OpenAPI spec version: 1.0.0
 * 
 *
 * NOTE: This class is auto generated by the swagger code generator program.
 * https://github.com/swagger-api/swagger-codegen.git
 * Do not edit the class manually.
 */

import { ActionResultBoolean } from './action-result-boolean';
 /**
 * 
 *
 * @export
 * @interface RESTfulResultActionResultBoolean
 */
export interface RESTfulResultActionResultBoolean {

    /**
     * @type {number}
     * @memberof RESTfulResultActionResultBoolean
     */
    statusCode?: number | null;

    /**
     * @type {ActionResultBoolean}
     * @memberof RESTfulResultActionResultBoolean
     */
    data?: ActionResultBoolean;

    /**
     * @type {boolean}
     * @memberof RESTfulResultActionResultBoolean
     */
    succeeded?: boolean;

    /**
     * @type {any}
     * @memberof RESTfulResultActionResultBoolean
     */
    errors?: any | null;

    /**
     * @type {any}
     * @memberof RESTfulResultActionResultBoolean
     */
    extras?: any | null;

    /**
     * @type {number}
     * @memberof RESTfulResultActionResultBoolean
     */
    timestamp?: number;
}
