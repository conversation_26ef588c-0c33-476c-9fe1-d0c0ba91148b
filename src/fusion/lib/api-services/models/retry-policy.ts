/* tslint:disable */
/* eslint-disable */
/**
 * All Groups
 * No description provided (generated by Swagger Codegen https://github.com/swagger-api/swagger-codegen)
 *
 * OpenAPI spec version: 1.0.0
 * 
 *
 * NOTE: This class is auto generated by the swagger code generator program.
 * https://github.com/swagger-api/swagger-codegen.git
 * Do not edit the class manually.
 */

 /**
 * 
 *
 * @export
 * @interface RetryPolicy
 */
export interface RetryPolicy {

    /**
     * @type {boolean}
     * @memberof RetryPolicy
     */
    enable?: boolean;

    /**
     * @type {number}
     * @memberof RetryPolicy
     */
    maxRetries?: number;

    /**
     * @type {number}
     * @memberof RetryPolicy
     */
    retryInterval?: number;

    /**
     * @type {number}
     * @memberof RetryPolicy
     */
    initialDelay?: number;

    /**
     * @type {number}
     * @memberof RetryPolicy
     */
    maxDelay?: number;

    /**
     * @type {number}
     * @memberof RetryPolicy
     */
    backoffMultiplier?: number;

    /**
     * @type {boolean}
     * @memberof RetryPolicy
     */
    enableDeadLetter?: boolean;

    /**
     * @type {boolean}
     * @memberof RetryPolicy
     */
    enableAutoRetry?: boolean;
}
