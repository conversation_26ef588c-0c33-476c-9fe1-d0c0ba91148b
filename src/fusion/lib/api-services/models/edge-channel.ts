/* tslint:disable */
/* eslint-disable */
/**
 * All Groups
 * No description provided (generated by Swagger Codegen https://github.com/swagger-api/swagger-codegen)
 *
 * OpenAPI spec version: 1.0.0
 * 
 *
 * NOTE: This class is auto generated by the swagger code generator program.
 * https://github.com/swagger-api/swagger-codegen.git
 * Do not edit the class manually.
 */

import { Parity } from './parity';
import { StopBits } from './stop-bits';
 /**
 * ????
 *
 * @export
 * @interface EdgeChannel
 */
export interface EdgeChannel {

    /**
     * @type {number}
     * @memberof EdgeChannel
     */
    id?: number;

    /**
     * @type {Date}
     * @memberof EdgeChannel
     */
    createTime?: Date | null;

    /**
     * @type {Date}
     * @memberof EdgeChannel
     */
    updateTime?: Date | null;

    /**
     * @type {number}
     * @memberof EdgeChannel
     */
    createUserId?: number | null;

    /**
     * @type {string}
     * @memberof EdgeChannel
     */
    createUserName?: string | null;

    /**
     * @type {number}
     * @memberof EdgeChannel
     */
    updateUserId?: number | null;

    /**
     * @type {string}
     * @memberof EdgeChannel
     */
    updateUserName?: string | null;

    /**
     * ????
     *
     * @type {string}
     * @memberof EdgeChannel
     */
    channelName?: string | null;

    /**
     * ???
     *
     * @type {string}
     * @memberof EdgeChannel
     */
    serial?: string | null;

    /**
     * ???
     *
     * @type {number}
     * @memberof EdgeChannel
     */
    baudRate?: number;

    /**
     * ???
     *
     * @type {number}
     * @memberof EdgeChannel
     */
    dataBits?: number;

    /**
     * @type {StopBits}
     * @memberof EdgeChannel
     */
    stop?: StopBits;

    /**
     * @type {Parity}
     * @memberof EdgeChannel
     */
    checkout?: Parity;

    /**
     * ????
     *
     * @type {number}
     * @memberof EdgeChannel
     */
    threadCount?: number;

    /**
     * ?? :??;??
     *
     * @type {boolean}
     * @memberof EdgeChannel
     */
    enable?: boolean;

    /**
     * ????
     *
     * @type {number}
     * @memberof EdgeChannel
     */
    deviceCount?: number;
}
