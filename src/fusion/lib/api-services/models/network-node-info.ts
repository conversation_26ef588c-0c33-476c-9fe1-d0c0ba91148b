/* tslint:disable */
/* eslint-disable */
/**
 * All Groups
 * No description provided (generated by Swagger Codegen https://github.com/swagger-api/swagger-codegen)
 *
 * OpenAPI spec version: 1.0.0
 * 
 *
 * NOTE: This class is auto generated by the swagger code generator program.
 * https://github.com/swagger-api/swagger-codegen.git
 * Do not edit the class manually.
 */

import { NodeStatus } from './node-status';
import { NodeType } from './node-type';
 /**
 * ??????
 *
 * @export
 * @interface NetworkNodeInfo
 */
export interface NetworkNodeInfo {

    /**
     * ??ID
     *
     * @type {string}
     * @memberof NetworkNodeInfo
     */
    id?: string | null;

    /**
     * ????
     *
     * @type {string}
     * @memberof NetworkNodeInfo
     */
    name?: string | null;

    /**
     * IP??
     *
     * @type {string}
     * @memberof NetworkNodeInfo
     */
    ipAddress?: string | null;

    /**
     * MAC??
     *
     * @type {string}
     * @memberof NetworkNodeInfo
     */
    macAddress?: string | null;

    /**
     * @type {NodeType}
     * @memberof NetworkNodeInfo
     */
    type?: NodeType;

    /**
     * @type {NodeStatus}
     * @memberof NetworkNodeInfo
     */
    status?: NodeStatus;

    /**
     * @type {NodeStatus}
     * @memberof NetworkNodeInfo
     */
    lastStatus?: NodeStatus;

    /**
     * ??(ms)
     *
     * @type {number}
     * @memberof NetworkNodeInfo
     */
    latency?: number;

    /**
     * ??????
     *
     * @type {Date}
     * @memberof NetworkNodeInfo
     */
    lastChecked?: Date;

    /**
     * ??????
     *
     * @type {boolean}
     * @memberof NetworkNodeInfo
     */
    isManual?: boolean;

    /**
     * ????
     *
     * @type {{ [key: string]: string; }}
     * @memberof NetworkNodeInfo
     */
    properties?: { [key: string]: string; } | null;
}
