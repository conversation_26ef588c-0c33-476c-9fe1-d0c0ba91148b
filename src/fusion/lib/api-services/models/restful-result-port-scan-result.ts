/* tslint:disable */
/* eslint-disable */
/**
 * All Groups
 * No description provided (generated by Swagger Codegen https://github.com/swagger-api/swagger-codegen)
 *
 * OpenAPI spec version: 1.0.0
 * 
 *
 * NOTE: This class is auto generated by the swagger code generator program.
 * https://github.com/swagger-api/swagger-codegen.git
 * Do not edit the class manually.
 */

import { PortScanResult } from './port-scan-result';
 /**
 * 
 *
 * @export
 * @interface RESTfulResultPortScanResult
 */
export interface RESTfulResultPortScanResult {

    /**
     * @type {number}
     * @memberof RESTfulResultPortScanResult
     */
    statusCode?: number | null;

    /**
     * @type {PortScanResult}
     * @memberof RESTfulResultPortScanResult
     */
    data?: PortScanResult;

    /**
     * @type {boolean}
     * @memberof RESTfulResultPortScanResult
     */
    succeeded?: boolean;

    /**
     * @type {any}
     * @memberof RESTfulResultPortScanResult
     */
    errors?: any | null;

    /**
     * @type {any}
     * @memberof RESTfulResultPortScanResult
     */
    extras?: any | null;

    /**
     * @type {number}
     * @memberof RESTfulResultPortScanResult
     */
    timestamp?: number;
}
