/* tslint:disable */
/* eslint-disable */
/**
 * All Groups
 * No description provided (generated by Swagger Codegen https://github.com/swagger-api/swagger-codegen)
 *
 * OpenAPI spec version: 1.0.0
 * 
 *
 * NOTE: This class is auto generated by the swagger code generator program.
 * https://github.com/swagger-api/swagger-codegen.git
 * Do not edit the class manually.
 */

import { DeviceSelectDto } from './device-select-dto';
 /**
 * 
 *
 * @export
 * @interface RESTfulResultListDeviceSelectDto
 */
export interface RESTfulResultListDeviceSelectDto {

    /**
     * @type {number}
     * @memberof RESTfulResultListDeviceSelectDto
     */
    statusCode?: number | null;

    /**
     * @type {Array<DeviceSelectDto>}
     * @memberof RESTfulResultListDeviceSelectDto
     */
    data?: Array<DeviceSelectDto> | null;

    /**
     * @type {boolean}
     * @memberof RESTfulResultListDeviceSelectDto
     */
    succeeded?: boolean;

    /**
     * @type {any}
     * @memberof RESTfulResultListDeviceSelectDto
     */
    errors?: any | null;

    /**
     * @type {any}
     * @memberof RESTfulResultListDeviceSelectDto
     */
    extras?: any | null;

    /**
     * @type {number}
     * @memberof RESTfulResultListDeviceSelectDto
     */
    timestamp?: number;
}
