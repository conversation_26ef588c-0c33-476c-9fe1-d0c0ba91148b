/* tslint:disable */
/* eslint-disable */
/**
 * All Groups
 * No description provided (generated by Swagger Codegen https://github.com/swagger-api/swagger-codegen)
 *
 * OpenAPI spec version: 1.0.0
 * 
 *
 * NOTE: This class is auto generated by the swagger code generator program.
 * https://github.com/swagger-api/swagger-codegen.git
 * Do not edit the class manually.
 */

import { SqlSugarPagedListMenuOutput } from './sql-sugar-paged-list-menu-output';
 /**
 * 
 *
 * @export
 * @interface RESTfulResultSqlSugarPagedListMenuOutput
 */
export interface RESTfulResultSqlSugarPagedListMenuOutput {

    /**
     * @type {number}
     * @memberof RESTfulResultSqlSugarPagedListMenuOutput
     */
    statusCode?: number | null;

    /**
     * @type {SqlSugarPagedListMenuOutput}
     * @memberof RESTfulResultSqlSugarPagedListMenuOutput
     */
    data?: SqlSugarPagedListMenuOutput;

    /**
     * @type {boolean}
     * @memberof RESTfulResultSqlSugarPagedListMenuOutput
     */
    succeeded?: boolean;

    /**
     * @type {any}
     * @memberof RESTfulResultSqlSugarPagedListMenuOutput
     */
    errors?: any | null;

    /**
     * @type {any}
     * @memberof RESTfulResultSqlSugarPagedListMenuOutput
     */
    extras?: any | null;

    /**
     * @type {number}
     * @memberof RESTfulResultSqlSugarPagedListMenuOutput
     */
    timestamp?: number;
}
