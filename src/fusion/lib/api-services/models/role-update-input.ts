/* tslint:disable */
/* eslint-disable */
/**
 * All Groups
 * No description provided (generated by Swagger Codegen https://github.com/swagger-api/swagger-codegen)
 *
 * OpenAPI spec version: 1.0.0
 * 
 *
 * NOTE: This class is auto generated by the swagger code generator program.
 * https://github.com/swagger-api/swagger-codegen.git
 * Do not edit the class manually.
 */

import { AccountTypeEnum } from './account-type-enum';
 /**
 * ??????
 *
 * @export
 * @interface RoleUpdateInput
 */
export interface RoleUpdateInput {

    /**
     * ??ID
     *
     * @type {number}
     * @memberof RoleUpdateInput
     */
    id: number;

    /**
     * ????
     *
     * @type {string}
     * @memberof RoleUpdateInput
     */
    name: string;

    /**
     * ????
     *
     * @type {string}
     * @memberof RoleUpdateInput
     */
    code: string;

    /**
     * ????
     *
     * @type {string}
     * @memberof RoleUpdateInput
     */
    description?: string | null;

    /**
     * ??
     *
     * @type {boolean}
     * @memberof RoleUpdateInput
     */
    status?: boolean;

    /**
     * @type {AccountTypeEnum}
     * @memberof RoleUpdateInput
     */
    accountType?: AccountTypeEnum;

    /**
     * ??
     *
     * @type {number}
     * @memberof RoleUpdateInput
     */
    sort?: number;
}
