/* tslint:disable */
/* eslint-disable */
/**
 * All Groups
 * No description provided (generated by Swagger Codegen https://github.com/swagger-api/swagger-codegen)
 *
 * OpenAPI spec version: 1.0.0
 * 
 *
 * NOTE: This class is auto generated by the swagger code generator program.
 * https://github.com/swagger-api/swagger-codegen.git
 * Do not edit the class manually.
 */

import { AccountTypeEnum } from './account-type-enum';
 /**
 * ??????
 *
 * @export
 * @interface UserUpdateInput
 */
export interface UserUpdateInput {

    /**
     * ??ID
     *
     * @type {number}
     * @memberof UserUpdateInput
     */
    id: number;

    /**
     * ??
     *
     * @type {string}
     * @memberof UserUpdateInput
     */
    account: string;

    /**
     * ??
     *
     * @type {string}
     * @memberof UserUpdateInput
     */
    name: string;

    /**
     * @type {AccountTypeEnum}
     * @memberof UserUpdateInput
     */
    accountType: AccountTypeEnum;

    /**
     * ??
     *
     * @type {boolean}
     * @memberof UserUpdateInput
     */
    status?: boolean;
}
