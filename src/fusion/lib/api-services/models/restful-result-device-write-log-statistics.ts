/* tslint:disable */
/* eslint-disable */
/**
 * All Groups
 * No description provided (generated by Swagger Codegen https://github.com/swagger-api/swagger-codegen)
 *
 * OpenAPI spec version: 1.0.0
 * 
 *
 * NOTE: This class is auto generated by the swagger code generator program.
 * https://github.com/swagger-api/swagger-codegen.git
 * Do not edit the class manually.
 */

import { DeviceWriteLogStatistics } from './device-write-log-statistics';
 /**
 * 
 *
 * @export
 * @interface RESTfulResultDeviceWriteLogStatistics
 */
export interface RESTfulResultDeviceWriteLogStatistics {

    /**
     * @type {number}
     * @memberof RESTfulResultDeviceWriteLogStatistics
     */
    statusCode?: number | null;

    /**
     * @type {DeviceWriteLogStatistics}
     * @memberof RESTfulResultDeviceWriteLogStatistics
     */
    data?: DeviceWriteLogStatistics;

    /**
     * @type {boolean}
     * @memberof RESTfulResultDeviceWriteLogStatistics
     */
    succeeded?: boolean;

    /**
     * @type {any}
     * @memberof RESTfulResultDeviceWriteLogStatistics
     */
    errors?: any | null;

    /**
     * @type {any}
     * @memberof RESTfulResultDeviceWriteLogStatistics
     */
    extras?: any | null;

    /**
     * @type {number}
     * @memberof RESTfulResultDeviceWriteLogStatistics
     */
    timestamp?: number;
}
