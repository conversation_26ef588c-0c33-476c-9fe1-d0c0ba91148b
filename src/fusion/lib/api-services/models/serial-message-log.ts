/* tslint:disable */
/* eslint-disable */
/**
 * All Groups
 * No description provided (generated by Swagger Codegen https://github.com/swagger-api/swagger-codegen)
 *
 * OpenAPI spec version: 1.0.0
 * 
 *
 * NOTE: This class is auto generated by the swagger code generator program.
 * https://github.com/swagger-api/swagger-codegen.git
 * Do not edit the class manually.
 */

import { MessageType } from './message-type';
 /**
 * ??????
 *
 * @export
 * @interface SerialMessageLog
 */
export interface SerialMessageLog {

    /**
     * @type {MessageType}
     * @memberof SerialMessageLog
     */
    type?: MessageType;

    /**
     * ????
     *
     * @type {string}
     * @memberof SerialMessageLog
     */
    content?: string | null;

    /**
     * ????
     *
     * @type {string}
     * @memberof SerialMessageLog
     */
    format?: string | null;

    /**
     * ???
     *
     * @type {Date}
     * @memberof SerialMessageLog
     */
    timestamp?: Date;
}
