/* tslint:disable */
/* eslint-disable */
/**
 * All Groups
 * No description provided (generated by Swagger Codegen https://github.com/swagger-api/swagger-codegen)
 *
 * OpenAPI spec version: 1.0.0
 * 
 *
 * NOTE: This class is auto generated by the swagger code generator program.
 * https://github.com/swagger-api/swagger-codegen.git
 * Do not edit the class manually.
 */

import { ApiPermissionModel } from './api-permission-model';
 /**
 * 
 *
 * @export
 * @interface RESTfulResultListApiPermissionModel
 */
export interface RESTfulResultListApiPermissionModel {

    /**
     * @type {number}
     * @memberof RESTfulResultListApiPermissionModel
     */
    statusCode?: number | null;

    /**
     * @type {Array<ApiPermissionModel>}
     * @memberof RESTfulResultListApiPermissionModel
     */
    data?: Array<ApiPermissionModel> | null;

    /**
     * @type {boolean}
     * @memberof RESTfulResultListApiPermissionModel
     */
    succeeded?: boolean;

    /**
     * @type {any}
     * @memberof RESTfulResultListApiPermissionModel
     */
    errors?: any | null;

    /**
     * @type {any}
     * @memberof RESTfulResultListApiPermissionModel
     */
    extras?: any | null;

    /**
     * @type {number}
     * @memberof RESTfulResultListApiPermissionModel
     */
    timestamp?: number;
}
