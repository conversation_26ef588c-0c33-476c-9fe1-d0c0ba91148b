/* tslint:disable */
/* eslint-disable */
/**
 * All Groups
 * No description provided (generated by Swagger Codegen https://github.com/swagger-api/swagger-codegen)
 *
 * OpenAPI spec version: 1.0.0
 * 
 *
 * NOTE: This class is auto generated by the swagger code generator program.
 * https://github.com/swagger-api/swagger-codegen.git
 * Do not edit the class manually.
 */

 /**
 * 
 *
 * @export
 * @interface RESTfulResultListInt64
 */
export interface RESTfulResultListInt64 {

    /**
     * @type {number}
     * @memberof RESTfulResultListInt64
     */
    statusCode?: number | null;

    /**
     * @type {Array<number>}
     * @memberof RESTfulResultListInt64
     */
    data?: Array<number> | null;

    /**
     * @type {boolean}
     * @memberof RESTfulResultListInt64
     */
    succeeded?: boolean;

    /**
     * @type {any}
     * @memberof RESTfulResultListInt64
     */
    errors?: any | null;

    /**
     * @type {any}
     * @memberof RESTfulResultListInt64
     */
    extras?: any | null;

    /**
     * @type {number}
     * @memberof RESTfulResultListInt64
     */
    timestamp?: number;
}
