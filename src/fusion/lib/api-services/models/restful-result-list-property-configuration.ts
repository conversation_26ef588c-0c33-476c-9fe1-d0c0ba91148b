/* tslint:disable */
/* eslint-disable */
/**
 * All Groups
 * No description provided (generated by Swagger Codegen https://github.com/swagger-api/swagger-codegen)
 *
 * OpenAPI spec version: 1.0.0
 * 
 *
 * NOTE: This class is auto generated by the swagger code generator program.
 * https://github.com/swagger-api/swagger-codegen.git
 * Do not edit the class manually.
 */

import { PropertyConfiguration } from './property-configuration';
 /**
 * 
 *
 * @export
 * @interface RESTfulResultListPropertyConfiguration
 */
export interface RESTfulResultListPropertyConfiguration {

    /**
     * @type {number}
     * @memberof RESTfulResultListPropertyConfiguration
     */
    statusCode?: number | null;

    /**
     * @type {Array<PropertyConfiguration>}
     * @memberof RESTfulResultListPropertyConfiguration
     */
    data?: Array<PropertyConfiguration> | null;

    /**
     * @type {boolean}
     * @memberof RESTfulResultListPropertyConfiguration
     */
    succeeded?: boolean;

    /**
     * @type {any}
     * @memberof RESTfulResultListPropertyConfiguration
     */
    errors?: any | null;

    /**
     * @type {any}
     * @memberof RESTfulResultListPropertyConfiguration
     */
    extras?: any | null;

    /**
     * @type {number}
     * @memberof RESTfulResultListPropertyConfiguration
     */
    timestamp?: number;
}
