/* tslint:disable */
/* eslint-disable */
/**
 * All Groups
 * No description provided (generated by Swagger Codegen https://github.com/swagger-api/swagger-codegen)
 *
 * OpenAPI spec version: 1.0.0
 * 
 *
 * NOTE: This class is auto generated by the swagger code generator program.
 * https://github.com/swagger-api/swagger-codegen.git
 * Do not edit the class manually.
 */

import { WriteSourceType } from './write-source-type';
 /**
 * ??????
 *
 * @export
 * @interface DeviceWriteLog
 */
export interface DeviceWriteLog {

    /**
     * @type {number}
     * @memberof DeviceWriteLog
     */
    id?: number;

    /**
     * ??ID
     *
     * @type {number}
     * @memberof DeviceWriteLog
     */
    deviceId?: number;

    /**
     * ?????
     *
     * @type {string}
     * @memberof DeviceWriteLog
     */
    labelIdentifier?: string | null;

    /**
     * ????
     *
     * @type {string}
     * @memberof DeviceWriteLog
     */
    address?: string | null;

    /**
     * ???
     *
     * @type {string}
     * @memberof DeviceWriteLog
     */
    value?: string | null;

    /**
     * ????
     *
     * @type {string}
     * @memberof DeviceWriteLog
     */
    dataType?: string | null;

    /**
     * ????
     *
     * @type {number}
     * @memberof DeviceWriteLog
     */
    length?: number;

    /**
     * ????
     *
     * @type {string}
     * @memberof DeviceWriteLog
     */
    encoding?: string | null;

    /**
     * @type {WriteSourceType}
     * @memberof DeviceWriteLog
     */
    sourceType?: WriteSourceType;

    /**
     * ????
     *
     * @type {Date}
     * @memberof DeviceWriteLog
     */
    writeTime?: Date;

    /**
     * ????
     *
     * @type {boolean}
     * @memberof DeviceWriteLog
     */
    success?: boolean;

    /**
     * ????
     *
     * @type {string}
     * @memberof DeviceWriteLog
     */
    errorMessage?: string | null;
}
