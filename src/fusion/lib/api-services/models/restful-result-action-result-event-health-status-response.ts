/* tslint:disable */
/* eslint-disable */
/**
 * All Groups
 * No description provided (generated by Swagger Codegen https://github.com/swagger-api/swagger-codegen)
 *
 * OpenAPI spec version: 1.0.0
 * 
 *
 * NOTE: This class is auto generated by the swagger code generator program.
 * https://github.com/swagger-api/swagger-codegen.git
 * Do not edit the class manually.
 */

import { ActionResultEventHealthStatusResponse } from './action-result-event-health-status-response';
 /**
 * 
 *
 * @export
 * @interface RESTfulResultActionResultEventHealthStatusResponse
 */
export interface RESTfulResultActionResultEventHealthStatusResponse {

    /**
     * @type {number}
     * @memberof RESTfulResultActionResultEventHealthStatusResponse
     */
    statusCode?: number | null;

    /**
     * @type {ActionResultEventHealthStatusResponse}
     * @memberof RESTfulResultActionResultEventHealthStatusResponse
     */
    data?: ActionResultEventHealthStatusResponse;

    /**
     * @type {boolean}
     * @memberof RESTfulResultActionResultEventHealthStatusResponse
     */
    succeeded?: boolean;

    /**
     * @type {any}
     * @memberof RESTfulResultActionResultEventHealthStatusResponse
     */
    errors?: any | null;

    /**
     * @type {any}
     * @memberof RESTfulResultActionResultEventHealthStatusResponse
     */
    extras?: any | null;

    /**
     * @type {number}
     * @memberof RESTfulResultActionResultEventHealthStatusResponse
     */
    timestamp?: number;
}
