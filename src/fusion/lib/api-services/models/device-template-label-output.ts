/* tslint:disable */
/* eslint-disable */
/**
 * All Groups
 * No description provided (generated by Swagger Codegen https://github.com/swagger-api/swagger-codegen)
 *
 * OpenAPI spec version: 1.0.0
 * 
 *
 * NOTE: This class is auto generated by the swagger code generator program.
 * https://github.com/swagger-api/swagger-codegen.git
 * Do not edit the class manually.
 */

import { ProtectTypeEnum } from './protect-type-enum';
import { SendTypeEnum } from './send-type-enum';
import { ValueSourceEnum } from './value-source-enum';
 /**
 * ????????
 *
 * @export
 * @interface DeviceTemplateLabelOutput
 */
export interface DeviceTemplateLabelOutput {

    /**
     * ??ID
     *
     * @type {number}
     * @memberof DeviceTemplateLabelOutput
     */
    id?: number;

    /**
     * ???
     *
     * @type {string}
     * @memberof DeviceTemplateLabelOutput
     */
    identifier?: string | null;

    /**
     * ???
     *
     * @type {string}
     * @memberof DeviceTemplateLabelOutput
     */
    name?: string | null;

    /**
     * ??????
     *
     * @type {string}
     * @memberof DeviceTemplateLabelOutput
     */
    transitionType?: string | null;

    /**
     * @type {ValueSourceEnum}
     * @memberof DeviceTemplateLabelOutput
     */
    valueSource?: ValueSourceEnum;

    /**
     * ?????
     *
     * @type {number}
     * @memberof DeviceTemplateLabelOutput
     */
    actionOrder?: number;

    /**
     * ???/????/???
     *
     * @type {string}
     * @memberof DeviceTemplateLabelOutput
     */
    content?: string | null;

    /**
     * @type {SendTypeEnum}
     * @memberof DeviceTemplateLabelOutput
     */
    sendType?: SendTypeEnum;

    /**
     * ????
     *
     * @type {number}
     * @memberof DeviceTemplateLabelOutput
     */
    period?: number;

    /**
     * ????
     *
     * @type {boolean}
     * @memberof DeviceTemplateLabelOutput
     */
    enable?: boolean;

    /**
     * @type {ProtectTypeEnum}
     * @memberof DeviceTemplateLabelOutput
     */
    protectType?: ProtectTypeEnum;

    /**
     * ??????
     *
     * @type {string}
     * @memberof DeviceTemplateLabelOutput
     */
    dataType?: string | null;

    /**
     * ????
     *
     * @type {string}
     * @memberof DeviceTemplateLabelOutput
     */
    registerAddress?: string | null;

    /**
     * ??
     *
     * @type {string}
     * @memberof DeviceTemplateLabelOutput
     */
    description?: string | null;
}
