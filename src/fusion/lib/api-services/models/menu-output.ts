/* tslint:disable */
/* eslint-disable */
/**
 * All Groups
 * No description provided (generated by Swagger Codegen https://github.com/swagger-api/swagger-codegen)
 *
 * OpenAPI spec version: 1.0.0
 * 
 *
 * NOTE: This class is auto generated by the swagger code generator program.
 * https://github.com/swagger-api/swagger-codegen.git
 * Do not edit the class manually.
 */

import { MenuTypeEnum } from './menu-type-enum';
 /**
 * ????
 *
 * @export
 * @interface MenuOutput
 */
export interface MenuOutput {

    /**
     * ??ID
     *
     * @type {number}
     * @memberof MenuOutput
     */
    id?: number;

    /**
     * ????
     *
     * @type {string}
     * @memberof MenuOutput
     */
    name?: string | null;

    /**
     * ????
     *
     * @type {string}
     * @memberof MenuOutput
     */
    code?: string | null;

    /**
     * ????
     *
     * @type {string}
     * @memberof MenuOutput
     */
    path?: string | null;

    /**
     * ??
     *
     * @type {string}
     * @memberof MenuOutput
     */
    icon?: string | null;

    /**
     * ????ID
     *
     * @type {number}
     * @memberof MenuOutput
     */
    parentId?: number | null;

    /**
     * ??????
     *
     * @type {string}
     * @memberof MenuOutput
     */
    parentName?: string | null;

    /**
     * @type {MenuTypeEnum}
     * @memberof MenuOutput
     */
    menuType?: MenuTypeEnum;

    /**
     * ??????
     *
     * @type {string}
     * @memberof MenuOutput
     */
    menuTypeDesc?: string | null;

    /**
     * ??
     *
     * @type {number}
     * @memberof MenuOutput
     */
    sort?: number;

    /**
     * ??
     *
     * @type {boolean}
     * @memberof MenuOutput
     */
    status?: boolean;

    /**
     * ????
     *
     * @type {boolean}
     * @memberof MenuOutput
     */
    hidden?: boolean;

    /**
     * ????
     *
     * @type {Date}
     * @memberof MenuOutput
     */
    createTime?: Date | null;

    /**
     * ????
     *
     * @type {Date}
     * @memberof MenuOutput
     */
    updateTime?: Date | null;
}
