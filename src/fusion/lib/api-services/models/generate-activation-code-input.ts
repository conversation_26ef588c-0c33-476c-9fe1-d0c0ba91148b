/* tslint:disable */
/* eslint-disable */
/**
 * All Groups
 * No description provided (generated by Swagger Codegen https://github.com/swagger-api/swagger-codegen)
 *
 * OpenAPI spec version: 1.0.0
 * 
 *
 * NOTE: This class is auto generated by the swagger code generator program.
 * https://github.com/swagger-api/swagger-codegen.git
 * Do not edit the class manually.
 */

 /**
 * ???????
 *
 * @export
 * @interface GenerateActivationCodeInput
 */
export interface GenerateActivationCodeInput {

    /**
     * ???
     *
     * @type {string}
     * @memberof GenerateActivationCodeInput
     */
    machineCode?: string | null;

    /**
     * ??????
     *
     * @type {number}
     * @memberof GenerateActivationCodeInput
     */
    deviceLimit?: number;

    /**
     * ????????
     *
     * @type {number}
     * @memberof GenerateActivationCodeInput
     */
    tagLimit?: number;

    /**
     * ??????
     *
     * @type {Date}
     * @memberof GenerateActivationCodeInput
     */
    startTime?: Date | null;

    /**
     * ??????
     *
     * @type {Date}
     * @memberof GenerateActivationCodeInput
     */
    expireTime?: Date | null;

    /**
     * ????
     *
     * @type {string}
     * @memberof GenerateActivationCodeInput
     */
    edition?: string | null;

    /**
     * ????
     *
     * @type {string}
     * @memberof GenerateActivationCodeInput
     */
    customer?: string | null;
}
