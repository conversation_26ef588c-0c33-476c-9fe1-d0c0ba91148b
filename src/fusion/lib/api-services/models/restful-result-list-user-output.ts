/* tslint:disable */
/* eslint-disable */
/**
 * All Groups
 * No description provided (generated by Swagger Codegen https://github.com/swagger-api/swagger-codegen)
 *
 * OpenAPI spec version: 1.0.0
 * 
 *
 * NOTE: This class is auto generated by the swagger code generator program.
 * https://github.com/swagger-api/swagger-codegen.git
 * Do not edit the class manually.
 */

import { UserOutput } from './user-output';
 /**
 * 
 *
 * @export
 * @interface RESTfulResultListUserOutput
 */
export interface RESTfulResultListUserOutput {

    /**
     * @type {number}
     * @memberof RESTfulResultListUserOutput
     */
    statusCode?: number | null;

    /**
     * @type {Array<UserOutput>}
     * @memberof RESTfulResultListUserOutput
     */
    data?: Array<UserOutput> | null;

    /**
     * @type {boolean}
     * @memberof RESTfulResultListUserOutput
     */
    succeeded?: boolean;

    /**
     * @type {any}
     * @memberof RESTfulResultListUserOutput
     */
    errors?: any | null;

    /**
     * @type {any}
     * @memberof RESTfulResultListUserOutput
     */
    extras?: any | null;

    /**
     * @type {number}
     * @memberof RESTfulResultListUserOutput
     */
    timestamp?: number;
}
