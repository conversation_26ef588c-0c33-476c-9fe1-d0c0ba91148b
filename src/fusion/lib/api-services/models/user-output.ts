/* tslint:disable */
/* eslint-disable */
/**
 * All Groups
 * No description provided (generated by Swagger Codegen https://github.com/swagger-api/swagger-codegen)
 *
 * OpenAPI spec version: 1.0.0
 * 
 *
 * NOTE: This class is auto generated by the swagger code generator program.
 * https://github.com/swagger-api/swagger-codegen.git
 * Do not edit the class manually.
 */

import { AccountTypeEnum } from './account-type-enum';
 /**
 * ????
 *
 * @export
 * @interface UserOutput
 */
export interface UserOutput {

    /**
     * ??ID
     *
     * @type {number}
     * @memberof UserOutput
     */
    id?: number;

    /**
     * ??
     *
     * @type {string}
     * @memberof UserOutput
     */
    account?: string | null;

    /**
     * ??
     *
     * @type {string}
     * @memberof UserOutput
     */
    name?: string | null;

    /**
     * @type {AccountTypeEnum}
     * @memberof UserOutput
     */
    accountType?: AccountTypeEnum;

    /**
     * ??
     *
     * @type {boolean}
     * @memberof UserOutput
     */
    status?: boolean;

    /**
     * ??????
     *
     * @type {Date}
     * @memberof UserOutput
     */
    lastLoginTime?: Date | null;

    /**
     * ????IP
     *
     * @type {string}
     * @memberof UserOutput
     */
    lastLoginIp?: string | null;
}
