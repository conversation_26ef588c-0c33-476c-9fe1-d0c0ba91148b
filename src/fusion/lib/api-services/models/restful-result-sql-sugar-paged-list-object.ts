/* tslint:disable */
/* eslint-disable */
/**
 * All Groups
 * No description provided (generated by Swagger Codegen https://github.com/swagger-api/swagger-codegen)
 *
 * OpenAPI spec version: 1.0.0
 * 
 *
 * NOTE: This class is auto generated by the swagger code generator program.
 * https://github.com/swagger-api/swagger-codegen.git
 * Do not edit the class manually.
 */

import { SqlSugarPagedListObject } from './sql-sugar-paged-list-object';
 /**
 * 
 *
 * @export
 * @interface RESTfulResultSqlSugarPagedListObject
 */
export interface RESTfulResultSqlSugarPagedListObject {

    /**
     * @type {number}
     * @memberof RESTfulResultSqlSugarPagedListObject
     */
    statusCode?: number | null;

    /**
     * @type {SqlSugarPagedListObject}
     * @memberof RESTfulResultSqlSugarPagedListObject
     */
    data?: SqlSugarPagedListObject;

    /**
     * @type {boolean}
     * @memberof RESTfulResultSqlSugarPagedListObject
     */
    succeeded?: boolean;

    /**
     * @type {any}
     * @memberof RESTfulResultSqlSugarPagedListObject
     */
    errors?: any | null;

    /**
     * @type {any}
     * @memberof RESTfulResultSqlSugarPagedListObject
     */
    extras?: any | null;

    /**
     * @type {number}
     * @memberof RESTfulResultSqlSugarPagedListObject
     */
    timestamp?: number;
}
