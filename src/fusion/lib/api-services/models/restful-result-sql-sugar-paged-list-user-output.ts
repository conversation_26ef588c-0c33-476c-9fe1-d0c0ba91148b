/* tslint:disable */
/* eslint-disable */
/**
 * All Groups
 * No description provided (generated by Swagger Codegen https://github.com/swagger-api/swagger-codegen)
 *
 * OpenAPI spec version: 1.0.0
 * 
 *
 * NOTE: This class is auto generated by the swagger code generator program.
 * https://github.com/swagger-api/swagger-codegen.git
 * Do not edit the class manually.
 */

import { SqlSugarPagedListUserOutput } from './sql-sugar-paged-list-user-output';
 /**
 * 
 *
 * @export
 * @interface RESTfulResultSqlSugarPagedListUserOutput
 */
export interface RESTfulResultSqlSugarPagedListUserOutput {

    /**
     * @type {number}
     * @memberof RESTfulResultSqlSugarPagedListUserOutput
     */
    statusCode?: number | null;

    /**
     * @type {SqlSugarPagedListUserOutput}
     * @memberof RESTfulResultSqlSugarPagedListUserOutput
     */
    data?: SqlSugarPagedListUserOutput;

    /**
     * @type {boolean}
     * @memberof RESTfulResultSqlSugarPagedListUserOutput
     */
    succeeded?: boolean;

    /**
     * @type {any}
     * @memberof RESTfulResultSqlSugarPagedListUserOutput
     */
    errors?: any | null;

    /**
     * @type {any}
     * @memberof RESTfulResultSqlSugarPagedListUserOutput
     */
    extras?: any | null;

    /**
     * @type {number}
     * @memberof RESTfulResultSqlSugarPagedListUserOutput
     */
    timestamp?: number;
}
