/* tslint:disable */
/* eslint-disable */
/**
 * All Groups
 * No description provided (generated by Swagger Codegen https://github.com/swagger-api/swagger-codegen)
 *
 * OpenAPI spec version: 1.0.0
 * 
 *
 * NOTE: This class is auto generated by the swagger code generator program.
 * https://github.com/swagger-api/swagger-codegen.git
 * Do not edit the class manually.
 */

import { TimeSeriesData } from './time-series-data';
 /**
 * 
 *
 * @export
 * @interface PagedResultTimeSeriesData
 */
export interface PagedResultTimeSeriesData {

    /**
     * @type {Array<TimeSeriesData>}
     * @memberof PagedResultTimeSeriesData
     */
    items?: Array<TimeSeriesData> | null;

    /**
     * @type {number}
     * @memberof PagedResultTimeSeriesData
     */
    totalCount?: number;

    /**
     * @type {number}
     * @memberof PagedResultTimeSeriesData
     */
    pageNumber?: number;

    /**
     * @type {number}
     * @memberof PagedResultTimeSeriesData
     */
    pageSize?: number;

    /**
     * @type {number}
     * @memberof PagedResultTimeSeriesData
     */
    totalPages?: number;
}
