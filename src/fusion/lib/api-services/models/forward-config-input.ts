/* tslint:disable */
/* eslint-disable */
/**
 * All Groups
 * No description provided (generated by Swagger Codegen https://github.com/swagger-api/swagger-codegen)
 *
 * OpenAPI spec version: 1.0.0
 * 
 *
 * NOTE: This class is auto generated by the swagger code generator program.
 * https://github.com/swagger-api/swagger-codegen.git
 * Do not edit the class manually.
 */

import { ForwardTypeEnum } from './forward-type-enum';
import { OfflineStorageConfig } from './offline-storage-config';
import { RetryPolicy } from './retry-policy';
 /**
 * 
 *
 * @export
 * @interface ForwardConfigInput
 */
export interface ForwardConfigInput {

    /**
     * @type {string}
     * @memberof ForwardConfigInput
     */
    name: string;

    /**
     * @type {boolean}
     * @memberof ForwardConfigInput
     */
    enable?: boolean;

    /**
     * @type {ForwardTypeEnum}
     * @memberof ForwardConfigInput
     */
    type?: ForwardTypeEnum;

    /**
     * @type {string}
     * @memberof ForwardConfigInput
     */
    url: string;

    /**
     * @type {string}
     * @memberof ForwardConfigInput
     */
    username?: string | null;

    /**
     * @type {string}
     * @memberof ForwardConfigInput
     */
    password?: string | null;

    /**
     * @type {number}
     * @memberof ForwardConfigInput
     */
    timeout?: number;

    /**
     * @type {string}
     * @memberof ForwardConfigInput
     */
    forwardMode?: string | null;

    /**
     * @type {any}
     * @memberof ForwardConfigInput
     */
    customConfig?: any | null;

    /**
     * @type {number}
     * @memberof ForwardConfigInput
     */
    connectionTimeout?: number;

    /**
     * @type {number}
     * @memberof ForwardConfigInput
     */
    reconnectInterval?: number;

    /**
     * @type {OfflineStorageConfig}
     * @memberof ForwardConfigInput
     */
    offlineStorageConfig?: OfflineStorageConfig;

    /**
     * @type {RetryPolicy}
     * @memberof ForwardConfigInput
     */
    retryConfig?: RetryPolicy;
}
