/* tslint:disable */
/* eslint-disable */
/**
 * All Groups
 * No description provided (generated by Swagger Codegen https://github.com/swagger-api/swagger-codegen)
 *
 * OpenAPI spec version: 1.0.0
 * 
 *
 * NOTE: This class is auto generated by the swagger code generator program.
 * https://github.com/swagger-api/swagger-codegen.git
 * Do not edit the class manually.
 */

import { DatabaseBackupConfig } from './database-backup-config';
 /**
 * 
 *
 * @export
 * @interface RESTfulResultDatabaseBackupConfig
 */
export interface RESTfulResultDatabaseBackupConfig {

    /**
     * @type {number}
     * @memberof RESTfulResultDatabaseBackupConfig
     */
    statusCode?: number | null;

    /**
     * @type {DatabaseBackupConfig}
     * @memberof RESTfulResultDatabaseBackupConfig
     */
    data?: DatabaseBackupConfig;

    /**
     * @type {boolean}
     * @memberof RESTfulResultDatabaseBackupConfig
     */
    succeeded?: boolean;

    /**
     * @type {any}
     * @memberof RESTfulResultDatabaseBackupConfig
     */
    errors?: any | null;

    /**
     * @type {any}
     * @memberof RESTfulResultDatabaseBackupConfig
     */
    extras?: any | null;

    /**
     * @type {number}
     * @memberof RESTfulResultDatabaseBackupConfig
     */
    timestamp?: number;
}
