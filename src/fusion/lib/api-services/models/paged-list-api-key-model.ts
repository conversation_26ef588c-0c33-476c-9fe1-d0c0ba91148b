/* tslint:disable */
/* eslint-disable */
/**
 * All Groups
 * No description provided (generated by Swagger Codegen https://github.com/swagger-api/swagger-codegen)
 *
 * OpenAPI spec version: 1.0.0
 * 
 *
 * NOTE: This class is auto generated by the swagger code generator program.
 * https://github.com/swagger-api/swagger-codegen.git
 * Do not edit the class manually.
 */

import { ApiKeyModel } from './api-key-model';
 /**
 * 
 *
 * @export
 * @interface PagedListApiKeyModel
 */
export interface PagedListApiKeyModel {

    /**
     * @type {number}
     * @memberof PagedListApiKeyModel
     */
    pageIndex?: number;

    /**
     * @type {number}
     * @memberof PagedListApiKeyModel
     */
    pageSize?: number;

    /**
     * @type {number}
     * @memberof PagedListApiKeyModel
     */
    totalCount?: number;

    /**
     * @type {number}
     * @memberof PagedListApiKeyModel
     */
    totalPages?: number;

    /**
     * @type {boolean}
     * @memberof PagedListApiKeyModel
     */
    hasPreviousPage?: boolean;

    /**
     * @type {boolean}
     * @memberof PagedListApiKeyModel
     */
    hasNextPage?: boolean;

    /**
     * @type {Array<ApiKeyModel>}
     * @memberof PagedListApiKeyModel
     */
    items?: Array<ApiKeyModel> | null;
}
