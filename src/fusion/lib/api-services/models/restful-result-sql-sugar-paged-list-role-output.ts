/* tslint:disable */
/* eslint-disable */
/**
 * All Groups
 * No description provided (generated by Swagger Codegen https://github.com/swagger-api/swagger-codegen)
 *
 * OpenAPI spec version: 1.0.0
 * 
 *
 * NOTE: This class is auto generated by the swagger code generator program.
 * https://github.com/swagger-api/swagger-codegen.git
 * Do not edit the class manually.
 */

import { SqlSugarPagedListRoleOutput } from './sql-sugar-paged-list-role-output';
 /**
 * 
 *
 * @export
 * @interface RESTfulResultSqlSugarPagedListRoleOutput
 */
export interface RESTfulResultSqlSugarPagedListRoleOutput {

    /**
     * @type {number}
     * @memberof RESTfulResultSqlSugarPagedListRoleOutput
     */
    statusCode?: number | null;

    /**
     * @type {SqlSugarPagedListRoleOutput}
     * @memberof RESTfulResultSqlSugarPagedListRoleOutput
     */
    data?: SqlSugarPagedListRoleOutput;

    /**
     * @type {boolean}
     * @memberof RESTfulResultSqlSugarPagedListRoleOutput
     */
    succeeded?: boolean;

    /**
     * @type {any}
     * @memberof RESTfulResultSqlSugarPagedListRoleOutput
     */
    errors?: any | null;

    /**
     * @type {any}
     * @memberof RESTfulResultSqlSugarPagedListRoleOutput
     */
    extras?: any | null;

    /**
     * @type {number}
     * @memberof RESTfulResultSqlSugarPagedListRoleOutput
     */
    timestamp?: number;
}
