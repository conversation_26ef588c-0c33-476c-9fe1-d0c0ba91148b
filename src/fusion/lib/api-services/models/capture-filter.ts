/* tslint:disable */
/* eslint-disable */
/**
 * All Groups
 * No description provided (generated by Swagger Codegen https://github.com/swagger-api/swagger-codegen)
 *
 * OpenAPI spec version: 1.0.0
 * 
 *
 * NOTE: This class is auto generated by the swagger code generator program.
 * https://github.com/swagger-api/swagger-codegen.git
 * Do not edit the class manually.
 */

import { ProtocolType } from './protocol-type';
 /**
 * ??????
 *
 * @export
 * @interface CaptureFilter
 */
export interface CaptureFilter {

    /**
     * ??????
     *
     * @type {string}
     * @memberof CaptureFilter
     */
    interfaceName?: string | null;

    /**
     * ?IP??
     *
     * @type {string}
     * @memberof CaptureFilter
     */
    sourceIp?: string | null;

    /**
     * ??IP??
     *
     * @type {string}
     * @memberof CaptureFilter
     */
    destinationIp?: string | null;

    /**
     * ???
     *
     * @type {number}
     * @memberof CaptureFilter
     */
    sourcePort?: number | null;

    /**
     * ????
     *
     * @type {number}
     * @memberof CaptureFilter
     */
    destinationPort?: number | null;

    /**
     * @type {ProtocolType}
     * @memberof CaptureFilter
     */
    protocol?: ProtocolType;

    /**
     * ???
     *
     * @type {string}
     * @memberof CaptureFilter
     */
    keyword?: string | null;

    /**
     * ???????
     *
     * @type {number}
     * @memberof CaptureFilter
     */
    maxPackets?: number;

    /**
     * ??????(ms)
     *
     * @type {number}
     * @memberof CaptureFilter
     */
    captureTimeout?: number;
}
