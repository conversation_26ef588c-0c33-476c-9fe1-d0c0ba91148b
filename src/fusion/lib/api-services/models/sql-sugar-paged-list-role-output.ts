/* tslint:disable */
/* eslint-disable */
/**
 * All Groups
 * No description provided (generated by Swagger Codegen https://github.com/swagger-api/swagger-codegen)
 *
 * OpenAPI spec version: 1.0.0
 * 
 *
 * NOTE: This class is auto generated by the swagger code generator program.
 * https://github.com/swagger-api/swagger-codegen.git
 * Do not edit the class manually.
 */

import { RoleOutput } from './role-output';
 /**
 * 
 *
 * @export
 * @interface SqlSugarPagedListRoleOutput
 */
export interface SqlSugarPagedListRoleOutput {

    /**
     * @type {number}
     * @memberof SqlSugarPagedListRoleOutput
     */
    page?: number;

    /**
     * @type {number}
     * @memberof SqlSugarPagedListRoleOutput
     */
    pageSize?: number;

    /**
     * @type {number}
     * @memberof SqlSugarPagedListRoleOutput
     */
    total?: number;

    /**
     * @type {number}
     * @memberof SqlSugarPagedListRoleOutput
     */
    totalPages?: number;

    /**
     * @type {Array<RoleOutput>}
     * @memberof SqlSugarPagedListRoleOutput
     */
    items?: Array<RoleOutput> | null;

    /**
     * @type {boolean}
     * @memberof SqlSugarPagedListRoleOutput
     */
    hasPrevPage?: boolean;

    /**
     * @type {boolean}
     * @memberof SqlSugarPagedListRoleOutput
     */
    hasNextPage?: boolean;
}
