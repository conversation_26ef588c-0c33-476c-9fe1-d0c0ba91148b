/* tslint:disable */
/* eslint-disable */
/**
 * All Groups
 * No description provided (generated by Swagger Codegen https://github.com/swagger-api/swagger-codegen)
 *
 * OpenAPI spec version: 1.0.0
 * 
 *
 * NOTE: This class is auto generated by the swagger code generator program.
 * https://github.com/swagger-api/swagger-codegen.git
 * Do not edit the class manually.
 */

import { MenuTreeOutput } from './menu-tree-output';
 /**
 * 
 *
 * @export
 * @interface RESTfulResultListMenuTreeOutput
 */
export interface RESTfulResultListMenuTreeOutput {

    /**
     * @type {number}
     * @memberof RESTfulResultListMenuTreeOutput
     */
    statusCode?: number | null;

    /**
     * @type {Array<MenuTreeOutput>}
     * @memberof RESTfulResultListMenuTreeOutput
     */
    data?: Array<MenuTreeOutput> | null;

    /**
     * @type {boolean}
     * @memberof RESTfulResultListMenuTreeOutput
     */
    succeeded?: boolean;

    /**
     * @type {any}
     * @memberof RESTfulResultListMenuTreeOutput
     */
    errors?: any | null;

    /**
     * @type {any}
     * @memberof RESTfulResultListMenuTreeOutput
     */
    extras?: any | null;

    /**
     * @type {number}
     * @memberof RESTfulResultListMenuTreeOutput
     */
    timestamp?: number;
}
