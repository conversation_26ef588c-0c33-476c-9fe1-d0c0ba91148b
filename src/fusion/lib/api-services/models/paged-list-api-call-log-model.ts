/* tslint:disable */
/* eslint-disable */
/**
 * All Groups
 * No description provided (generated by Swagger Codegen https://github.com/swagger-api/swagger-codegen)
 *
 * OpenAPI spec version: 1.0.0
 * 
 *
 * NOTE: This class is auto generated by the swagger code generator program.
 * https://github.com/swagger-api/swagger-codegen.git
 * Do not edit the class manually.
 */

import { ApiCallLogModel } from './api-call-log-model';
 /**
 * 
 *
 * @export
 * @interface PagedListApiCallLogModel
 */
export interface PagedListApiCallLogModel {

    /**
     * @type {number}
     * @memberof PagedListApiCallLogModel
     */
    pageIndex?: number;

    /**
     * @type {number}
     * @memberof PagedListApiCallLogModel
     */
    pageSize?: number;

    /**
     * @type {number}
     * @memberof PagedListApiCallLogModel
     */
    totalCount?: number;

    /**
     * @type {number}
     * @memberof PagedListApiCallLogModel
     */
    totalPages?: number;

    /**
     * @type {boolean}
     * @memberof PagedListApiCallLogModel
     */
    hasPreviousPage?: boolean;

    /**
     * @type {boolean}
     * @memberof PagedListApiCallLogModel
     */
    hasNextPage?: boolean;

    /**
     * @type {Array<ApiCallLogModel>}
     * @memberof PagedListApiCallLogModel
     */
    items?: Array<ApiCallLogModel> | null;
}
