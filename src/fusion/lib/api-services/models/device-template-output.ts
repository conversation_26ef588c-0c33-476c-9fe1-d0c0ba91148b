/* tslint:disable */
/* eslint-disable */
/**
 * All Groups
 * No description provided (generated by Swagger Codegen https://github.com/swagger-api/swagger-codegen)
 *
 * OpenAPI spec version: 1.0.0
 * 
 *
 * NOTE: This class is auto generated by the swagger code generator program.
 * https://github.com/swagger-api/swagger-codegen.git
 * Do not edit the class manually.
 */

import { DeviceTemplateLabelOutput } from './device-template-label-output';
 /**
 * ??????
 *
 * @export
 * @interface DeviceTemplateOutput
 */
export interface DeviceTemplateOutput {

    /**
     * ??ID
     *
     * @type {number}
     * @memberof DeviceTemplateOutput
     */
    id?: number;

    /**
     * ????
     *
     * @type {string}
     * @memberof DeviceTemplateOutput
     */
    name?: string | null;

    /**
     * ????
     *
     * @type {string}
     * @memberof DeviceTemplateOutput
     */
    description?: string | null;

    /**
     * ????
     *
     * @type {string}
     * @memberof DeviceTemplateOutput
     */
    driverName?: string | null;

    /**
     * ????
     *
     * @type {Date}
     * @memberof DeviceTemplateOutput
     */
    createTime?: Date;

    /**
     * ????
     *
     * @type {number}
     * @memberof DeviceTemplateOutput
     */
    labelCount?: number;

    /**
     * ??????(?????)
     *
     * @type {Array<DeviceTemplateLabelOutput>}
     * @memberof DeviceTemplateOutput
     */
    labels?: Array<DeviceTemplateLabelOutput> | null;
}
