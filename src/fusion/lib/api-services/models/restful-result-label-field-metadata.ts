/* tslint:disable */
/* eslint-disable */
/**
 * All Groups
 * No description provided (generated by Swagger Codegen https://github.com/swagger-api/swagger-codegen)
 *
 * OpenAPI spec version: 1.0.0
 * 
 *
 * NOTE: This class is auto generated by the swagger code generator program.
 * https://github.com/swagger-api/swagger-codegen.git
 * Do not edit the class manually.
 */

import { LabelFieldMetadata } from './label-field-metadata';
 /**
 * 
 *
 * @export
 * @interface RESTfulResultLabelFieldMetadata
 */
export interface RESTfulResultLabelFieldMetadata {

    /**
     * @type {number}
     * @memberof RESTfulResultLabelFieldMetadata
     */
    statusCode?: number | null;

    /**
     * @type {LabelFieldMetadata}
     * @memberof RESTfulResultLabelFieldMetadata
     */
    data?: LabelFieldMetadata;

    /**
     * @type {boolean}
     * @memberof RESTfulResultLabelFieldMetadata
     */
    succeeded?: boolean;

    /**
     * @type {any}
     * @memberof RESTfulResultLabelFieldMetadata
     */
    errors?: any | null;

    /**
     * @type {any}
     * @memberof RESTfulResultLabelFieldMetadata
     */
    extras?: any | null;

    /**
     * @type {number}
     * @memberof RESTfulResultLabelFieldMetadata
     */
    timestamp?: number;
}
