/* tslint:disable */
/* eslint-disable */
/**
 * All Groups
 * No description provided (generated by Swagger Codegen https://github.com/swagger-api/swagger-codegen)
 *
 * OpenAPI spec version: 1.0.0
 * 
 *
 * NOTE: This class is auto generated by the swagger code generator program.
 * https://github.com/swagger-api/swagger-codegen.git
 * Do not edit the class manually.
 */

import { EdgeChannel } from './edge-channel';
 /**
 * 
 *
 * @export
 * @interface SqlSugarPagedListEdgeChannel
 */
export interface SqlSugarPagedListEdgeChannel {

    /**
     * @type {number}
     * @memberof SqlSugarPagedListEdgeChannel
     */
    page?: number;

    /**
     * @type {number}
     * @memberof SqlSugarPagedListEdgeChannel
     */
    pageSize?: number;

    /**
     * @type {number}
     * @memberof SqlSugarPagedListEdgeChannel
     */
    total?: number;

    /**
     * @type {number}
     * @memberof SqlSugarPagedListEdgeChannel
     */
    totalPages?: number;

    /**
     * @type {Array<EdgeChannel>}
     * @memberof SqlSugarPagedListEdgeChannel
     */
    items?: Array<EdgeChannel> | null;

    /**
     * @type {boolean}
     * @memberof SqlSugarPagedListEdgeChannel
     */
    hasPrevPage?: boolean;

    /**
     * @type {boolean}
     * @memberof SqlSugarPagedListEdgeChannel
     */
    hasNextPage?: boolean;
}
