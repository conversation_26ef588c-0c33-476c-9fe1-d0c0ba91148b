/* tslint:disable */
/* eslint-disable */
/**
 * All Groups
 * No description provided (generated by Swagger Codegen https://github.com/swagger-api/swagger-codegen)
 *
 * OpenAPI spec version: 1.0.0
 * 
 *
 * NOTE: This class is auto generated by the swagger code generator program.
 * https://github.com/swagger-api/swagger-codegen.git
 * Do not edit the class manually.
 */

import { MenuTypeEnum } from './menu-type-enum';
 /**
 * ??????
 *
 * @export
 * @interface MenuUpdateInput
 */
export interface MenuUpdateInput {

    /**
     * ??ID
     *
     * @type {number}
     * @memberof MenuUpdateInput
     */
    id: number;

    /**
     * ????
     *
     * @type {string}
     * @memberof MenuUpdateInput
     */
    name: string;

    /**
     * ????
     *
     * @type {string}
     * @memberof MenuUpdateInput
     */
    code: string;

    /**
     * ????
     *
     * @type {string}
     * @memberof MenuUpdateInput
     */
    path?: string | null;

    /**
     * ??
     *
     * @type {string}
     * @memberof MenuUpdateInput
     */
    icon?: string | null;

    /**
     * ????ID
     *
     * @type {number}
     * @memberof MenuUpdateInput
     */
    parentId?: number | null;

    /**
     * @type {MenuTypeEnum}
     * @memberof MenuUpdateInput
     */
    menuType?: MenuTypeEnum;

    /**
     * ??
     *
     * @type {number}
     * @memberof MenuUpdateInput
     */
    sort?: number;

    /**
     * ??
     *
     * @type {boolean}
     * @memberof MenuUpdateInput
     */
    status?: boolean;

    /**
     * ????
     *
     * @type {boolean}
     * @memberof MenuUpdateInput
     */
    hidden?: boolean;
}
