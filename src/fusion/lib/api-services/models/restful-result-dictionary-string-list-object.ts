/* tslint:disable */
/* eslint-disable */
/**
 * All Groups
 * No description provided (generated by Swagger Codegen https://github.com/swagger-api/swagger-codegen)
 *
 * OpenAPI spec version: 1.0.0
 * 
 *
 * NOTE: This class is auto generated by the swagger code generator program.
 * https://github.com/swagger-api/swagger-codegen.git
 * Do not edit the class manually.
 */

 /**
 * 
 *
 * @export
 * @interface RESTfulResultDictionaryStringListObject
 */
export interface RESTfulResultDictionaryStringListObject {

    /**
     * @type {number}
     * @memberof RESTfulResultDictionaryStringListObject
     */
    statusCode?: number | null;

    /**
     * @type {{ [key: string]: Array<any>; }}
     * @memberof RESTfulResultDictionaryStringListObject
     */
    data?: { [key: string]: Array<any>; } | null;

    /**
     * @type {boolean}
     * @memberof RESTfulResultDictionaryStringListObject
     */
    succeeded?: boolean;

    /**
     * @type {any}
     * @memberof RESTfulResultDictionaryStringListObject
     */
    errors?: any | null;

    /**
     * @type {any}
     * @memberof RESTfulResultDictionaryStringListObject
     */
    extras?: any | null;

    /**
     * @type {number}
     * @memberof RESTfulResultDictionaryStringListObject
     */
    timestamp?: number;
}
