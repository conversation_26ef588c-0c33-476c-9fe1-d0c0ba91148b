/* tslint:disable */
/* eslint-disable */
/**
 * All Groups
 * No description provided (generated by Swagger Codegen https://github.com/swagger-api/swagger-codegen)
 *
 * OpenAPI spec version: 1.0.0
 * 
 *
 * NOTE: This class is auto generated by the swagger code generator program.
 * https://github.com/swagger-api/swagger-codegen.git
 * Do not edit the class manually.
 */

import { NetworkTopology } from './network-topology';
 /**
 * 
 *
 * @export
 * @interface RESTfulResultNetworkTopology
 */
export interface RESTfulResultNetworkTopology {

    /**
     * @type {number}
     * @memberof RESTfulResultNetworkTopology
     */
    statusCode?: number | null;

    /**
     * @type {NetworkTopology}
     * @memberof RESTfulResultNetworkTopology
     */
    data?: NetworkTopology;

    /**
     * @type {boolean}
     * @memberof RESTfulResultNetworkTopology
     */
    succeeded?: boolean;

    /**
     * @type {any}
     * @memberof RESTfulResultNetworkTopology
     */
    errors?: any | null;

    /**
     * @type {any}
     * @memberof RESTfulResultNetworkTopology
     */
    extras?: any | null;

    /**
     * @type {number}
     * @memberof RESTfulResultNetworkTopology
     */
    timestamp?: number;
}
