/* tslint:disable */
/* eslint-disable */
/**
 * All Groups
 * No description provided (generated by Swagger Codegen https://github.com/swagger-api/swagger-codegen)
 *
 * OpenAPI spec version: 1.0.0
 * 
 *
 * NOTE: This class is auto generated by the swagger code generator program.
 * https://github.com/swagger-api/swagger-codegen.git
 * Do not edit the class manually.
 */

import { RetryStatistics } from './retry-statistics';
 /**
 * 
 *
 * @export
 * @interface RESTfulResultRetryStatistics
 */
export interface RESTfulResultRetryStatistics {

    /**
     * @type {number}
     * @memberof RESTfulResultRetryStatistics
     */
    statusCode?: number | null;

    /**
     * @type {RetryStatistics}
     * @memberof RESTfulResultRetryStatistics
     */
    data?: RetryStatistics;

    /**
     * @type {boolean}
     * @memberof RESTfulResultRetryStatistics
     */
    succeeded?: boolean;

    /**
     * @type {any}
     * @memberof RESTfulResultRetryStatistics
     */
    errors?: any | null;

    /**
     * @type {any}
     * @memberof RESTfulResultRetryStatistics
     */
    extras?: any | null;

    /**
     * @type {number}
     * @memberof RESTfulResultRetryStatistics
     */
    timestamp?: number;
}
