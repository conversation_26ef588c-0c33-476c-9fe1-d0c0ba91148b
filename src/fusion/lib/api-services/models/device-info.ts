/* tslint:disable */
/* eslint-disable */
/**
 * All Groups
 * No description provided (generated by Swagger Codegen https://github.com/swagger-api/swagger-codegen)
 *
 * OpenAPI spec version: 1.0.0
 * 
 *
 * NOTE: This class is auto generated by the swagger code generator program.
 * https://github.com/swagger-api/swagger-codegen.git
 * Do not edit the class manually.
 */

import { DataReportTypeEnum } from './data-report-type-enum';
 /**
 * ????????
 *
 * @export
 * @interface DeviceInfo
 */
export interface DeviceInfo {

    /**
     * ?????????
     *
     * @type {number}
     * @memberof DeviceInfo
     */
    minPeriod?: number;

    /**
     * ????????
     *
     * @type {number}
     * @memberof DeviceInfo
     */
    waitTime?: number;

    /**
     * ????
     *
     * @type {number}
     * @memberof DeviceInfo
     */
    reConnTime?: number;

    /**
     * @type {DataReportTypeEnum}
     * @memberof DeviceInfo
     */
    reportType?: DataReportTypeEnum;

    /**
     * ??????
     *
     * @type {boolean}
     * @memberof DeviceInfo
     */
    storeHistoryData?: boolean;
}
