/* tslint:disable */
/* eslint-disable */
/**
 * All Groups
 * No description provided (generated by Swagger Codegen https://github.com/swagger-api/swagger-codegen)
 *
 * OpenAPI spec version: 1.0.0
 * 
 *
 * NOTE: This class is auto generated by the swagger code generator program.
 * https://github.com/swagger-api/swagger-codegen.git
 * Do not edit the class manually.
 */

import { AccountTypeEnum } from './account-type-enum';
 /**
 * ????
 *
 * @export
 * @interface RoleOutput
 */
export interface RoleOutput {

    /**
     * ??ID
     *
     * @type {number}
     * @memberof RoleOutput
     */
    id?: number;

    /**
     * ????
     *
     * @type {string}
     * @memberof RoleOutput
     */
    name?: string | null;

    /**
     * ????
     *
     * @type {string}
     * @memberof RoleOutput
     */
    code?: string | null;

    /**
     * ????
     *
     * @type {string}
     * @memberof RoleOutput
     */
    description?: string | null;

    /**
     * ??
     *
     * @type {boolean}
     * @memberof RoleOutput
     */
    status?: boolean;

    /**
     * ??????
     *
     * @type {boolean}
     * @memberof RoleOutput
     */
    isDefault?: boolean;

    /**
     * @type {AccountTypeEnum}
     * @memberof RoleOutput
     */
    accountType?: AccountTypeEnum;

    /**
     * ??????
     *
     * @type {string}
     * @memberof RoleOutput
     */
    accountTypeDesc?: string | null;

    /**
     * ??
     *
     * @type {number}
     * @memberof RoleOutput
     */
    sort?: number;

    /**
     * ????
     *
     * @type {Date}
     * @memberof RoleOutput
     */
    createTime?: Date | null;

    /**
     * ????
     *
     * @type {Date}
     * @memberof RoleOutput
     */
    updateTime?: Date | null;
}
