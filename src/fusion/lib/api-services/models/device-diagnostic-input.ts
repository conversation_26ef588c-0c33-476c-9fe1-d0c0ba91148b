/* tslint:disable */
/* eslint-disable */
/**
 * All Groups
 * No description provided (generated by Swagger Codegen https://github.com/swagger-api/swagger-codegen)
 *
 * OpenAPI spec version: 1.0.0
 * 
 *
 * NOTE: This class is auto generated by the swagger code generator program.
 * https://github.com/swagger-api/swagger-codegen.git
 * Do not edit the class manually.
 */

 /**
 * ????????
 *
 * @export
 * @interface DeviceDiagnosticInput
 */
export interface DeviceDiagnosticInput {

    /**
     * ??ID
     *
     * @type {number}
     * @memberof DeviceDiagnosticInput
     */
    deviceId?: number;

    /**
     * ????????
     *
     * @type {boolean}
     * @memberof DeviceDiagnosticInput
     */
    includeConnection?: boolean;

    /**
     * ????????
     *
     * @type {boolean}
     * @memberof DeviceDiagnosticInput
     */
    includePerformance?: boolean;

    /**
     * ????????
     *
     * @type {boolean}
     * @memberof DeviceDiagnosticInput
     */
    includeHealth?: boolean;

    /**
     * ????????
     *
     * @type {boolean}
     * @memberof DeviceDiagnosticInput
     */
    includeConfiguration?: boolean;

    /**
     * ????????
     *
     * @type {boolean}
     * @memberof DeviceDiagnosticInput
     */
    includeData?: boolean;

    /**
     * ??????????
     *
     * @type {boolean}
     * @memberof DeviceDiagnosticInput
     */
    includeHistory?: boolean;

    /**
     * ????????(??7?)
     *
     * @type {number}
     * @memberof DeviceDiagnosticInput
     */
    historyDays?: number;

    /**
     * ??????
     *
     * @type {number}
     * @memberof DeviceDiagnosticInput
     */
    historyPage?: number;

    /**
     * ???????
     *
     * @type {number}
     * @memberof DeviceDiagnosticInput
     */
    historyPageSize?: number;
}
