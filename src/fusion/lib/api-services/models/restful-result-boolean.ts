/* tslint:disable */
/* eslint-disable */
/**
 * All Groups
 * No description provided (generated by Swagger Codegen https://github.com/swagger-api/swagger-codegen)
 *
 * OpenAPI spec version: 1.0.0
 * 
 *
 * NOTE: This class is auto generated by the swagger code generator program.
 * https://github.com/swagger-api/swagger-codegen.git
 * Do not edit the class manually.
 */

 /**
 * 
 *
 * @export
 * @interface RESTfulResultBoolean
 */
export interface RESTfulResultBoolean {

    /**
     * @type {number}
     * @memberof RESTfulResultBoolean
     */
    statusCode?: number | null;

    /**
     * @type {boolean}
     * @memberof RESTfulResultBoolean
     */
    data?: boolean;

    /**
     * @type {boolean}
     * @memberof RESTfulResultBoolean
     */
    succeeded?: boolean;

    /**
     * @type {any}
     * @memberof RESTfulResultBoolean
     */
    errors?: any | null;

    /**
     * @type {any}
     * @memberof RESTfulResultBoolean
     */
    extras?: any | null;

    /**
     * @type {number}
     * @memberof RESTfulResultBoolean
     */
    timestamp?: number;
}
