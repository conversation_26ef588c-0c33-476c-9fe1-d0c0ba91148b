/* tslint:disable */
/* eslint-disable */
/**
 * All Groups
 * No description provided (generated by Swagger Codegen https://github.com/swagger-api/swagger-codegen)
 *
 * OpenAPI spec version: 1.0.0
 * 
 *
 * NOTE: This class is auto generated by the swagger code generator program.
 * https://github.com/swagger-api/swagger-codegen.git
 * Do not edit the class manually.
 */

 /**
 * 
 *
 * @export
 * @interface StorageStatus
 */
export interface StorageStatus {

    /**
     * @type {number}
     * @memberof StorageStatus
     */
    totalSpace?: number;

    /**
     * @type {number}
     * @memberof StorageStatus
     */
    usedSpace?: number;

    /**
     * @type {number}
     * @memberof StorageStatus
     */
    freeSpace?: number;

    /**
     * @type {number}
     * @memberof StorageStatus
     */
    deviceCount?: number;

    /**
     * @type {number}
     * @memberof StorageStatus
     */
    dataPointCount?: number;

    /**
     * @type {Date}
     * @memberof StorageStatus
     */
    earliestDataTime?: Date | null;

    /**
     * @type {Date}
     * @memberof StorageStatus
     */
    latestDataTime?: Date | null;
}
