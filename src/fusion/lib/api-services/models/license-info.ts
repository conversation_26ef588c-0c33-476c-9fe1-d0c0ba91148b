/* tslint:disable */
/* eslint-disable */
/**
 * All Groups
 * No description provided (generated by Swagger Codegen https://github.com/swagger-api/swagger-codegen)
 *
 * OpenAPI spec version: 1.0.0
 * 
 *
 * NOTE: This class is auto generated by the swagger code generator program.
 * https://github.com/swagger-api/swagger-codegen.git
 * Do not edit the class manually.
 */

 /**
 * ????
 *
 * @export
 * @interface LicenseInfo
 */
export interface LicenseInfo {

    /**
     * ???
     *
     * @type {string}
     * @memberof LicenseInfo
     */
    machineCode?: string | null;

    /**
     * ???
     *
     * @type {string}
     * @memberof LicenseInfo
     */
    activationCode?: string | null;

    /**
     * ????
     *
     * @type {boolean}
     * @memberof LicenseInfo
     */
    isActivated?: boolean;

    /**
     * ??????
     *
     * @type {number}
     * @memberof LicenseInfo
     */
    deviceLimit?: number;

    /**
     * ????????
     *
     * @type {number}
     * @memberof LicenseInfo
     */
    tagLimit?: number;

    /**
     * ??????
     *
     * @type {Date}
     * @memberof LicenseInfo
     */
    startTime?: Date;

    /**
     * ??????
     *
     * @type {Date}
     * @memberof LicenseInfo
     */
    expireTime?: Date;

    /**
     * ????
     *
     * @type {string}
     * @memberof LicenseInfo
     */
    edition?: string | null;

    /**
     * ????
     *
     * @type {string}
     * @memberof LicenseInfo
     */
    customer?: string | null;
}
