/* tslint:disable */
/* eslint-disable */
/**
 * All Groups
 * No description provided (generated by Swagger Codegen https://github.com/swagger-api/swagger-codegen)
 *
 * OpenAPI spec version: 1.0.0
 * 
 *
 * NOTE: This class is auto generated by the swagger code generator program.
 * https://github.com/swagger-api/swagger-codegen.git
 * Do not edit the class manually.
 */

import { RoleOutput } from './role-output';
 /**
 * 
 *
 * @export
 * @interface RESTfulResultListRoleOutput
 */
export interface RESTfulResultListRoleOutput {

    /**
     * @type {number}
     * @memberof RESTfulResultListRoleOutput
     */
    statusCode?: number | null;

    /**
     * @type {Array<RoleOutput>}
     * @memberof RESTfulResultListRoleOutput
     */
    data?: Array<RoleOutput> | null;

    /**
     * @type {boolean}
     * @memberof RESTfulResultListRoleOutput
     */
    succeeded?: boolean;

    /**
     * @type {any}
     * @memberof RESTfulResultListRoleOutput
     */
    errors?: any | null;

    /**
     * @type {any}
     * @memberof RESTfulResultListRoleOutput
     */
    extras?: any | null;

    /**
     * @type {number}
     * @memberof RESTfulResultListRoleOutput
     */
    timestamp?: number;
}
