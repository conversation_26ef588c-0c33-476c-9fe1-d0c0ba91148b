/* tslint:disable */
/* eslint-disable */
/**
 * All Groups
 * No description provided (generated by Swagger Codegen https://github.com/swagger-api/swagger-codegen)
 *
 * OpenAPI spec version: 1.0.0
 * 
 *
 * NOTE: This class is auto generated by the swagger code generator program.
 * https://github.com/swagger-api/swagger-codegen.git
 * Do not edit the class manually.
 */

 /**
 * Ping????
 *
 * @export
 * @interface PingRequest
 */
export interface PingRequest {

    /**
     * ??????  IP?????
     *
     * @type {Array<string>}
     * @memberof PingRequest
     */
    targets: Array<string>;

    /**
     * ????(??)  ??1000ms
     *
     * @type {number}
     * @memberof PingRequest
     */
    timeout?: number;

    /**
     * TTL?  ??64
     *
     * @type {number}
     * @memberof PingRequest
     */
    ttl?: number;
}
