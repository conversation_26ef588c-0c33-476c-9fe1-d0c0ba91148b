/* tslint:disable */
/* eslint-disable */
/**
 * All Groups
 * No description provided (generated by Swagger Codegen https://github.com/swagger-api/swagger-codegen)
 *
 * OpenAPI spec version: 1.0.0
 * 
 *
 * NOTE: This class is auto generated by the swagger code generator program.
 * https://github.com/swagger-api/swagger-codegen.git
 * Do not edit the class manually.
 */

import { GatewayConfiguration } from './gateway-configuration';
 /**
 * 
 *
 * @export
 * @interface RESTfulResultGatewayConfiguration
 */
export interface RESTfulResultGatewayConfiguration {

    /**
     * @type {number}
     * @memberof RESTfulResultGatewayConfiguration
     */
    statusCode?: number | null;

    /**
     * @type {GatewayConfiguration}
     * @memberof RESTfulResultGatewayConfiguration
     */
    data?: GatewayConfiguration;

    /**
     * @type {boolean}
     * @memberof RESTfulResultGatewayConfiguration
     */
    succeeded?: boolean;

    /**
     * @type {any}
     * @memberof RESTfulResultGatewayConfiguration
     */
    errors?: any | null;

    /**
     * @type {any}
     * @memberof RESTfulResultGatewayConfiguration
     */
    extras?: any | null;

    /**
     * @type {number}
     * @memberof RESTfulResultGatewayConfiguration
     */
    timestamp?: number;
}
