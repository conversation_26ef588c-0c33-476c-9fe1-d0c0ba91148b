/* tslint:disable */
/* eslint-disable */
/**
 * All Groups
 * No description provided (generated by Swagger Codegen https://github.com/swagger-api/swagger-codegen)
 *
 * OpenAPI spec version: 1.0.0
 * 
 *
 * NOTE: This class is auto generated by the swagger code generator program.
 * https://github.com/swagger-api/swagger-codegen.git
 * Do not edit the class manually.
 */

import { MenuOutput } from './menu-output';
 /**
 * 
 *
 * @export
 * @interface SqlSugarPagedListMenuOutput
 */
export interface SqlSugarPagedListMenuOutput {

    /**
     * @type {number}
     * @memberof SqlSugarPagedListMenuOutput
     */
    page?: number;

    /**
     * @type {number}
     * @memberof SqlSugarPagedListMenuOutput
     */
    pageSize?: number;

    /**
     * @type {number}
     * @memberof SqlSugarPagedListMenuOutput
     */
    total?: number;

    /**
     * @type {number}
     * @memberof SqlSugarPagedListMenuOutput
     */
    totalPages?: number;

    /**
     * @type {Array<MenuOutput>}
     * @memberof SqlSugarPagedListMenuOutput
     */
    items?: Array<MenuOutput> | null;

    /**
     * @type {boolean}
     * @memberof SqlSugarPagedListMenuOutput
     */
    hasPrevPage?: boolean;

    /**
     * @type {boolean}
     * @memberof SqlSugarPagedListMenuOutput
     */
    hasNextPage?: boolean;
}
