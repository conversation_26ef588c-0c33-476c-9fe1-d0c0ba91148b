/* tslint:disable */
/* eslint-disable */
/**
 * All Groups
 * No description provided (generated by Swagger Codegen https://github.com/swagger-api/swagger-codegen)
 *
 * OpenAPI spec version: 1.0.0
 * 
 *
 * NOTE: This class is auto generated by the swagger code generator program.
 * https://github.com/swagger-api/swagger-codegen.git
 * Do not edit the class manually.
 */

import { EventScheduleTask } from './event-schedule-task';
 /**
 * ?????? - ????????
 *
 * @export
 * @interface EventTaskLog
 */
export interface EventTaskLog {

    /**
     * @type {number}
     * @memberof EventTaskLog
     */
    id?: number;

    /**
     * @type {Date}
     * @memberof EventTaskLog
     */
    createTime?: Date | null;

    /**
     * @type {Date}
     * @memberof EventTaskLog
     */
    updateTime?: Date | null;

    /**
     * @type {number}
     * @memberof EventTaskLog
     */
    createUserId?: number | null;

    /**
     * @type {string}
     * @memberof EventTaskLog
     */
    createUserName?: string | null;

    /**
     * @type {number}
     * @memberof EventTaskLog
     */
    updateUserId?: number | null;

    /**
     * @type {string}
     * @memberof EventTaskLog
     */
    updateUserName?: string | null;

    /**
     * ??ID
     *
     * @type {number}
     * @memberof EventTaskLog
     */
    taskId?: number;

    /**
     * ????ID
     *
     * @type {number}
     * @memberof EventTaskLog
     */
    deviceEventId?: number;

    /**
     * ????
     *
     * @type {Date}
     * @memberof EventTaskLog
     */
    executeTime?: Date;

    /**
     * ????
     *
     * @type {boolean}
     * @memberof EventTaskLog
     */
    isSuccess?: boolean;

    /**
     * ??????
     *
     * @type {string}
     * @memberof EventTaskLog
     */
    resultMessage?: string | null;

    /**
     * ????(??)
     *
     * @type {number}
     * @memberof EventTaskLog
     */
    executionTime?: number;

    /**
     * ????
     *
     * @type {string}
     * @memberof EventTaskLog
     */
    errorMessage?: string | null;

    /**
     * ????
     *
     * @type {string}
     * @memberof EventTaskLog
     */
    stackTrace?: string | null;

    /**
     * ????
     *
     * @type {string}
     * @memberof EventTaskLog
     */
    executionNode?: string | null;

    /**
     * ???? - JSON???????????
     *
     * @type {{ [key: string]: any; }}
     * @memberof EventTaskLog
     */
    detailData?: { [key: string]: any; } | null;

    /**
     * @type {EventScheduleTask}
     * @memberof EventTaskLog
     */
    task?: EventScheduleTask;
}
