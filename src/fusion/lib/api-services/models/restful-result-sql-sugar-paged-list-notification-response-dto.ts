/* tslint:disable */
/* eslint-disable */
/**
 * All Groups
 * No description provided (generated by Swagger Codegen https://github.com/swagger-api/swagger-codegen)
 *
 * OpenAPI spec version: 1.0.0
 * 
 *
 * NOTE: This class is auto generated by the swagger code generator program.
 * https://github.com/swagger-api/swagger-codegen.git
 * Do not edit the class manually.
 */

import { SqlSugarPagedListNotificationResponseDto } from './sql-sugar-paged-list-notification-response-dto';
 /**
 * 
 *
 * @export
 * @interface RESTfulResultSqlSugarPagedListNotificationResponseDto
 */
export interface RESTfulResultSqlSugarPagedListNotificationResponseDto {

    /**
     * @type {number}
     * @memberof RESTfulResultSqlSugarPagedListNotificationResponseDto
     */
    statusCode?: number | null;

    /**
     * @type {SqlSugarPagedListNotificationResponseDto}
     * @memberof RESTfulResultSqlSugarPagedListNotificationResponseDto
     */
    data?: SqlSugarPagedListNotificationResponseDto;

    /**
     * @type {boolean}
     * @memberof RESTfulResultSqlSugarPagedListNotificationResponseDto
     */
    succeeded?: boolean;

    /**
     * @type {any}
     * @memberof RESTfulResultSqlSugarPagedListNotificationResponseDto
     */
    errors?: any | null;

    /**
     * @type {any}
     * @memberof RESTfulResultSqlSugarPagedListNotificationResponseDto
     */
    extras?: any | null;

    /**
     * @type {number}
     * @memberof RESTfulResultSqlSugarPagedListNotificationResponseDto
     */
    timestamp?: number;
}
