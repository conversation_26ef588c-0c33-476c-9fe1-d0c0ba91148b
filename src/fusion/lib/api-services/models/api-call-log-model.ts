/* tslint:disable */
/* eslint-disable */
/**
 * All Groups
 * No description provided (generated by Swagger Codegen https://github.com/swagger-api/swagger-codegen)
 *
 * OpenAPI spec version: 1.0.0
 * 
 *
 * NOTE: This class is auto generated by the swagger code generator program.
 * https://github.com/swagger-api/swagger-codegen.git
 * Do not edit the class manually.
 */

 /**
 * 
 *
 * @export
 * @interface ApiCallLogModel
 */
export interface ApiCallLogModel {

    /**
     * @type {number}
     * @memberof ApiCallLogModel
     */
    id?: number;

    /**
     * @type {number}
     * @memberof ApiCallLogModel
     */
    apiKeyId?: number;

    /**
     * @type {string}
     * @memberof ApiCallLogModel
     */
    apiKeyName?: string | null;

    /**
     * @type {string}
     * @memberof ApiCallLogModel
     */
    callerIp?: string | null;

    /**
     * @type {string}
     * @memberof ApiCallLogModel
     */
    requestPath?: string | null;

    /**
     * @type {string}
     * @memberof ApiCallLogModel
     */
    httpMethod?: string | null;

    /**
     * @type {number}
     * @memberof ApiCallLogModel
     */
    statusCode?: number;

    /**
     * @type {number}
     * @memberof ApiCallLogModel
     */
    executionTime?: number;

    /**
     * @type {Date}
     * @memberof ApiCallLogModel
     */
    callTime?: Date;

    /**
     * @type {boolean}
     * @memberof ApiCallLogModel
     */
    isSuccess?: boolean;

    /**
     * @type {string}
     * @memberof ApiCallLogModel
     */
    errorMessage?: string | null;

    /**
     * @type {string}
     * @memberof ApiCallLogModel
     */
    requestParams?: string | null;

    /**
     * @type {string}
     * @memberof ApiCallLogModel
     */
    requestHeaders?: string | null;

    /**
     * @type {string}
     * @memberof ApiCallLogModel
     */
    responseContent?: string | null;
}
