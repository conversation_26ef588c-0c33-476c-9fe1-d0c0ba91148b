/* tslint:disable */
/* eslint-disable */
/**
 * All Groups
 * No description provided (generated by Swagger Codegen https://github.com/swagger-api/swagger-codegen)
 *
 * OpenAPI spec version: 1.0.0
 * 
 *
 * NOTE: This class is auto generated by the swagger code generator program.
 * https://github.com/swagger-api/swagger-codegen.git
 * Do not edit the class manually.
 */

import { AccountTypeEnum } from './account-type-enum';
 /**
 * ??????
 *
 * @export
 * @interface RoleCreateInput
 */
export interface RoleCreateInput {

    /**
     * ????
     *
     * @type {string}
     * @memberof RoleCreateInput
     */
    name: string;

    /**
     * ????
     *
     * @type {string}
     * @memberof RoleCreateInput
     */
    code: string;

    /**
     * ????
     *
     * @type {string}
     * @memberof RoleCreateInput
     */
    description?: string | null;

    /**
     * ??
     *
     * @type {boolean}
     * @memberof RoleCreateInput
     */
    status?: boolean;

    /**
     * @type {AccountTypeEnum}
     * @memberof RoleCreateInput
     */
    accountType?: AccountTypeEnum;

    /**
     * ??
     *
     * @type {number}
     * @memberof RoleCreateInput
     */
    sort?: number;
}
