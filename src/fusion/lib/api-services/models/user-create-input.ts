/* tslint:disable */
/* eslint-disable */
/**
 * All Groups
 * No description provided (generated by Swagger Codegen https://github.com/swagger-api/swagger-codegen)
 *
 * OpenAPI spec version: 1.0.0
 * 
 *
 * NOTE: This class is auto generated by the swagger code generator program.
 * https://github.com/swagger-api/swagger-codegen.git
 * Do not edit the class manually.
 */

import { AccountTypeEnum } from './account-type-enum';
 /**
 * ??????
 *
 * @export
 * @interface UserCreateInput
 */
export interface UserCreateInput {

    /**
     * ??
     *
     * @type {string}
     * @memberof UserCreateInput
     */
    account: string;

    /**
     * ??
     *
     * @type {string}
     * @memberof UserCreateInput
     */
    name: string;

    /**
     * ??
     *
     * @type {string}
     * @memberof UserCreateInput
     */
    password: string;

    /**
     * @type {AccountTypeEnum}
     * @memberof UserCreateInput
     */
    accountType: AccountTypeEnum;

    /**
     * ??
     *
     * @type {boolean}
     * @memberof UserCreateInput
     */
    status?: boolean;
}
