/* tslint:disable */
/* eslint-disable */
/**
 * All Groups
 * No description provided (generated by Swagger Codegen https://github.com/swagger-api/swagger-codegen)
 *
 * OpenAPI spec version: 1.0.0
 * 
 *
 * NOTE: This class is auto generated by the swagger code generator program.
 * https://github.com/swagger-api/swagger-codegen.git
 * Do not edit the class manually.
 */

import { MenuTypeEnum } from './menu-type-enum';
 /**
 * ??????
 *
 * @export
 * @interface MenuCreateInput
 */
export interface MenuCreateInput {

    /**
     * ????
     *
     * @type {string}
     * @memberof MenuCreateInput
     */
    name: string;

    /**
     * ????
     *
     * @type {string}
     * @memberof MenuCreateInput
     */
    code: string;

    /**
     * ????
     *
     * @type {string}
     * @memberof MenuCreateInput
     */
    path?: string | null;

    /**
     * ??
     *
     * @type {string}
     * @memberof MenuCreateInput
     */
    icon?: string | null;

    /**
     * ????ID
     *
     * @type {number}
     * @memberof MenuCreateInput
     */
    parentId?: number | null;

    /**
     * @type {MenuTypeEnum}
     * @memberof MenuCreateInput
     */
    menuType?: MenuTypeEnum;

    /**
     * ??
     *
     * @type {number}
     * @memberof MenuCreateInput
     */
    sort?: number;

    /**
     * ??
     *
     * @type {boolean}
     * @memberof MenuCreateInput
     */
    status?: boolean;

    /**
     * ????
     *
     * @type {boolean}
     * @memberof MenuCreateInput
     */
    hidden?: boolean;
}
