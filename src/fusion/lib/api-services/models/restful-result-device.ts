/* tslint:disable */
/* eslint-disable */
/**
 * All Groups
 * No description provided (generated by Swagger Codegen https://github.com/swagger-api/swagger-codegen)
 *
 * OpenAPI spec version: 1.0.0
 * 
 *
 * NOTE: This class is auto generated by the swagger code generator program.
 * https://github.com/swagger-api/swagger-codegen.git
 * Do not edit the class manually.
 */

import { Device } from './device';
 /**
 * 
 *
 * @export
 * @interface RESTfulResultDevice
 */
export interface RESTfulResultDevice {

    /**
     * @type {number}
     * @memberof RESTfulResultDevice
     */
    statusCode?: number | null;

    /**
     * @type {Device}
     * @memberof RESTfulResultDevice
     */
    data?: Device;

    /**
     * @type {boolean}
     * @memberof RESTfulResultDevice
     */
    succeeded?: boolean;

    /**
     * @type {any}
     * @memberof RESTfulResultDevice
     */
    errors?: any | null;

    /**
     * @type {any}
     * @memberof RESTfulResultDevice
     */
    extras?: any | null;

    /**
     * @type {number}
     * @memberof RESTfulResultDevice
     */
    timestamp?: number;
}
