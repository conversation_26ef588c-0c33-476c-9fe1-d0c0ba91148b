/* tslint:disable */
/* eslint-disable */
/**
 * All Groups
 * No description provided (generated by Swagger Codegen https://github.com/swagger-api/swagger-codegen)
 *
 * OpenAPI spec version: 1.0.0
 * 
 *
 * NOTE: This class is auto generated by the swagger code generator program.
 * https://github.com/swagger-api/swagger-codegen.git
 * Do not edit the class manually.
 */

import { ConditionEvaluationDetail } from './condition-evaluation-detail';
import { DiagnosticLevelEnum } from './diagnostic-level-enum';
import { DiagnosticTypeEnum } from './diagnostic-type-enum';
import { EventPerformanceMetrics } from './event-performance-metrics';
 /**
 * ??????????
 *
 * @export
 * @interface EventDiagnosticInfoResponse
 */
export interface EventDiagnosticInfoResponse {

    /**
     * ????ID
     *
     * @type {number}
     * @memberof EventDiagnosticInfoResponse
     */
    id?: number;

    /**
     * ????ID
     *
     * @type {number}
     * @memberof EventDiagnosticInfoResponse
     */
    deviceEventId?: number;

    /**
     * ??ID
     *
     * @type {number}
     * @memberof EventDiagnosticInfoResponse
     */
    deviceId?: number;

    /**
     * ????ID
     *
     * @type {string}
     * @memberof EventDiagnosticInfoResponse
     */
    sessionId?: string | null;

    /**
     * @type {DiagnosticTypeEnum}
     * @memberof EventDiagnosticInfoResponse
     */
    diagnosticType?: DiagnosticTypeEnum;

    /**
     * @type {DiagnosticLevelEnum}
     * @memberof EventDiagnosticInfoResponse
     */
    level?: DiagnosticLevelEnum;

    /**
     * ????
     *
     * @type {string}
     * @memberof EventDiagnosticInfoResponse
     */
    title?: string | null;

    /**
     * ????
     *
     * @type {string}
     * @memberof EventDiagnosticInfoResponse
     */
    message?: string | null;

    /**
     * ??????
     *
     * @type {Array<ConditionEvaluationDetail>}
     * @memberof EventDiagnosticInfoResponse
     */
    conditionDetails?: Array<ConditionEvaluationDetail> | null;

    /**
     * @type {EventPerformanceMetrics}
     * @memberof EventDiagnosticInfoResponse
     */
    performanceMetrics?: EventPerformanceMetrics;

    /**
     * ????
     *
     * @type {{ [key: string]: any; }}
     * @memberof EventDiagnosticInfoResponse
     */
    environmentInfo?: { [key: string]: any; } | null;

    /**
     * ????
     *
     * @type {string}
     * @memberof EventDiagnosticInfoResponse
     */
    diagnosticCode?: string | null;

    /**
     * ????
     *
     * @type {string}
     * @memberof EventDiagnosticInfoResponse
     */
    suggestedAction?: string | null;

    /**
     * ???????
     *
     * @type {boolean}
     * @memberof EventDiagnosticInfoResponse
     */
    isResolved?: boolean;

    /**
     * ????
     *
     * @type {Date}
     * @memberof EventDiagnosticInfoResponse
     */
    resolvedTime?: Date | null;

    /**
     * ????
     *
     * @type {Date}
     * @memberof EventDiagnosticInfoResponse
     */
    createTime?: Date;
}
