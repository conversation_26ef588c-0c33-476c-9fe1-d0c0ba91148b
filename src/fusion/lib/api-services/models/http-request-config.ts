/* tslint:disable */
/* eslint-disable */
/**
 * All Groups
 * No description provided (generated by Swagger Codegen https://github.com/swagger-api/swagger-codegen)
 *
 * OpenAPI spec version: 1.0.0
 * 
 *
 * NOTE: This class is auto generated by the swagger code generator program.
 * https://github.com/swagger-api/swagger-codegen.git
 * Do not edit the class manually.
 */

import { HttpMethod } from './http-method';
 /**
 * HTTP????
 *
 * @export
 * @interface HttpRequestConfig
 */
export interface HttpRequestConfig {

    /**
     * ??URL
     *
     * @type {string}
     * @memberof HttpRequestConfig
     */
    url: string;

    /**
     * @type {HttpMethod}
     * @memberof HttpRequestConfig
     */
    method: HttpMethod;

    /**
     * ???
     *
     * @type {{ [key: string]: string; }}
     * @memberof HttpRequestConfig
     */
    headers?: { [key: string]: string; } | null;

    /**
     * ????
     *
     * @type {{ [key: string]: string; }}
     * @memberof HttpRequestConfig
     */
    parameters?: { [key: string]: string; } | null;

    /**
     * ???
     *
     * @type {string}
     * @memberof HttpRequestConfig
     */
    body?: string | null;

    /**
     * ????
     *
     * @type {string}
     * @memberof HttpRequestConfig
     */
    contentType?: string | null;

    /**
     * ????(ms)
     *
     * @type {number}
     * @memberof HttpRequestConfig
     */
    timeout?: number;

    /**
     * ???????
     *
     * @type {boolean}
     * @memberof HttpRequestConfig
     */
    allowRedirect?: boolean;
}
