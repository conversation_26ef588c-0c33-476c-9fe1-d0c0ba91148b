/* tslint:disable */
/* eslint-disable */
/**
 * All Groups
 * No description provided (generated by Swagger Codegen https://github.com/swagger-api/swagger-codegen)
 *
 * OpenAPI spec version: 1.0.0
 * 
 *
 * NOTE: This class is auto generated by the swagger code generator program.
 * https://github.com/swagger-api/swagger-codegen.git
 * Do not edit the class manually.
 */

import { DeviceWriteLog } from './device-write-log';
 /**
 * 
 *
 * @export
 * @interface SqlSugarPagedListDeviceWriteLog
 */
export interface SqlSugarPagedListDeviceWriteLog {

    /**
     * @type {number}
     * @memberof SqlSugarPagedListDeviceWriteLog
     */
    page?: number;

    /**
     * @type {number}
     * @memberof SqlSugarPagedListDeviceWriteLog
     */
    pageSize?: number;

    /**
     * @type {number}
     * @memberof SqlSugarPagedListDeviceWriteLog
     */
    total?: number;

    /**
     * @type {number}
     * @memberof SqlSugarPagedListDeviceWriteLog
     */
    totalPages?: number;

    /**
     * @type {Array<DeviceWriteLog>}
     * @memberof SqlSugarPagedListDeviceWriteLog
     */
    items?: Array<DeviceWriteLog> | null;

    /**
     * @type {boolean}
     * @memberof SqlSugarPagedListDeviceWriteLog
     */
    hasPrevPage?: boolean;

    /**
     * @type {boolean}
     * @memberof SqlSugarPagedListDeviceWriteLog
     */
    hasNextPage?: boolean;
}
