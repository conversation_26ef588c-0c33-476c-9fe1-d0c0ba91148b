/* tslint:disable */
/* eslint-disable */
/**
 * All Groups
 * No description provided (generated by Swagger Codegen https://github.com/swagger-api/swagger-codegen)
 *
 * OpenAPI spec version: 1.0.0
 * 
 *
 * NOTE: This class is auto generated by the swagger code generator program.
 * https://github.com/swagger-api/swagger-codegen.git
 * Do not edit the class manually.
 */

import { ConfigHistoryInfo } from './config-history-info';
 /**
 * 
 *
 * @export
 * @interface RESTfulResultListConfigHistoryInfo
 */
export interface RESTfulResultListConfigHistoryInfo {

    /**
     * @type {number}
     * @memberof RESTfulResultListConfigHistoryInfo
     */
    statusCode?: number | null;

    /**
     * @type {Array<ConfigHistoryInfo>}
     * @memberof RESTfulResultListConfigHistoryInfo
     */
    data?: Array<ConfigHistoryInfo> | null;

    /**
     * @type {boolean}
     * @memberof RESTfulResultListConfigHistoryInfo
     */
    succeeded?: boolean;

    /**
     * @type {any}
     * @memberof RESTfulResultListConfigHistoryInfo
     */
    errors?: any | null;

    /**
     * @type {any}
     * @memberof RESTfulResultListConfigHistoryInfo
     */
    extras?: any | null;

    /**
     * @type {number}
     * @memberof RESTfulResultListConfigHistoryInfo
     */
    timestamp?: number;
}
