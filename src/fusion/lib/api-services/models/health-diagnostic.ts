/* tslint:disable */
/* eslint-disable */
/**
 * All Groups
 * No description provided (generated by Swagger Codegen https://github.com/swagger-api/swagger-codegen)
 *
 * OpenAPI spec version: 1.0.0
 * 
 *
 * NOTE: This class is auto generated by the swagger code generator program.
 * https://github.com/swagger-api/swagger-codegen.git
 * Do not edit the class manually.
 */

import { EventHealthStatusEnum } from './event-health-status-enum';
 /**
 * ??????
 *
 * @export
 * @interface HealthDiagnostic
 */
export interface HealthDiagnostic {

    /**
     * @type {EventHealthStatusEnum}
     * @memberof HealthDiagnostic
     */
    healthStatus?: EventHealthStatusEnum;

    /**
     * ????(0-100)
     *
     * @type {number}
     * @memberof HealthDiagnostic
     */
    healthScore?: number;

    /**
     * ?????(%)
     *
     * @type {number}
     * @memberof HealthDiagnostic
     */
    triggerSuccessRate?: number;

    /**
     * ?????
     *
     * @type {number}
     * @memberof HealthDiagnostic
     */
    totalTriggerCount?: number;

    /**
     * ????
     *
     * @type {number}
     * @memberof HealthDiagnostic
     */
    failureCount?: number;

    /**
     * ??????
     *
     * @type {string}
     * @memberof HealthDiagnostic
     */
    lastErrorMessage?: string | null;

    /**
     * ??????
     *
     * @type {Date}
     * @memberof HealthDiagnostic
     */
    lastErrorTime?: Date | null;

    /**
     * ?????(??7?)
     *
     * @type {number}
     * @memberof HealthDiagnostic
     */
    availabilityScore?: number;
}
