/* tslint:disable */
/* eslint-disable */
/**
 * All Groups
 * No description provided (generated by Swagger Codegen https://github.com/swagger-api/swagger-codegen)
 *
 * OpenAPI spec version: 1.0.0
 * 
 *
 * NOTE: This class is auto generated by the swagger code generator program.
 * https://github.com/swagger-api/swagger-codegen.git
 * Do not edit the class manually.
 */

import { DeviceDiagnosticDto } from './device-diagnostic-dto';
 /**
 * 
 *
 * @export
 * @interface RESTfulResultDeviceDiagnosticDto
 */
export interface RESTfulResultDeviceDiagnosticDto {

    /**
     * @type {number}
     * @memberof RESTfulResultDeviceDiagnosticDto
     */
    statusCode?: number | null;

    /**
     * @type {DeviceDiagnosticDto}
     * @memberof RESTfulResultDeviceDiagnosticDto
     */
    data?: DeviceDiagnosticDto;

    /**
     * @type {boolean}
     * @memberof RESTfulResultDeviceDiagnosticDto
     */
    succeeded?: boolean;

    /**
     * @type {any}
     * @memberof RESTfulResultDeviceDiagnosticDto
     */
    errors?: any | null;

    /**
     * @type {any}
     * @memberof RESTfulResultDeviceDiagnosticDto
     */
    extras?: any | null;

    /**
     * @type {number}
     * @memberof RESTfulResultDeviceDiagnosticDto
     */
    timestamp?: number;
}
