/* tslint:disable */
/* eslint-disable */
/**
 * All Groups
 * No description provided (generated by Swagger Codegen https://github.com/swagger-api/swagger-codegen)
 *
 * OpenAPI spec version: 1.0.0
 * 
 *
 * NOTE: This class is auto generated by the swagger code generator program.
 * https://github.com/swagger-api/swagger-codegen.git
 * Do not edit the class manually.
 */

import { DriverConfigs } from './driver-configs';
import { PropertyConfiguration } from './property-configuration';
 /**
 * ????
 *
 * @export
 * @interface Driver
 */
export interface Driver {

    /**
     * ????
     *
     * @type {string}
     * @memberof Driver
     */
    driverName?: string | null;

    /**
     * ????
     *
     * @type {string}
     * @memberof Driver
     */
    fileName?: string | null;

    /**
     * ????
     *
     * @type {string}
     * @memberof Driver
     */
    version?: string | null;

    /**
     * ????
     *
     * @type {string}
     * @memberof Driver
     */
    manufacturer?: string | null;

    /**
     * ????:PLC;CNC
     *
     * @type {string}
     * @memberof Driver
     */
    driverType?: string | null;

    /**
     * ????
     *
     * @type {boolean}
     * @memberof Driver
     */
    createVariables?: boolean;

    /**
     * ???????
     *
     * @type {boolean}
     * @memberof Driver
     */
    isNet?: boolean;

    /**
     * ????
     *
     * @type {string}
     * @memberof Driver
     */
    description?: string | null;

    /**
     * ????,ip,port...
     *
     * @type {Array<DriverConfigs>}
     * @memberof Driver
     */
    connectionConfig?: Array<DriverConfigs> | null;

    /**
     * ????,??,??,??,????...
     *
     * @type {Array<PropertyConfiguration>}
     * @memberof Driver
     */
    propertyConfiguration?: Array<PropertyConfiguration> | null;
}
