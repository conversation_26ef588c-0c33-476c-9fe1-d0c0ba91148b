/* tslint:disable */
/* eslint-disable */
/**
 * All Groups
 * No description provided (generated by Swagger Codegen https://github.com/swagger-api/swagger-codegen)
 *
 * OpenAPI spec version: 1.0.0
 * 
 *
 * NOTE: This class is auto generated by the swagger code generator program.
 * https://github.com/swagger-api/swagger-codegen.git
 * Do not edit the class manually.
 */

import { SqlSugarPagedListDeviceLabel } from './sql-sugar-paged-list-device-label';
 /**
 * 
 *
 * @export
 * @interface RESTfulResultSqlSugarPagedListDeviceLabel
 */
export interface RESTfulResultSqlSugarPagedListDeviceLabel {

    /**
     * @type {number}
     * @memberof RESTfulResultSqlSugarPagedListDeviceLabel
     */
    statusCode?: number | null;

    /**
     * @type {SqlSugarPagedListDeviceLabel}
     * @memberof RESTfulResultSqlSugarPagedListDeviceLabel
     */
    data?: SqlSugarPagedListDeviceLabel;

    /**
     * @type {boolean}
     * @memberof RESTfulResultSqlSugarPagedListDeviceLabel
     */
    succeeded?: boolean;

    /**
     * @type {any}
     * @memberof RESTfulResultSqlSugarPagedListDeviceLabel
     */
    errors?: any | null;

    /**
     * @type {any}
     * @memberof RESTfulResultSqlSugarPagedListDeviceLabel
     */
    extras?: any | null;

    /**
     * @type {number}
     * @memberof RESTfulResultSqlSugarPagedListDeviceLabel
     */
    timestamp?: number;
}
