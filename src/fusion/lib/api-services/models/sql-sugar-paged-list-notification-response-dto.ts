/* tslint:disable */
/* eslint-disable */
/**
 * All Groups
 * No description provided (generated by Swagger Codegen https://github.com/swagger-api/swagger-codegen)
 *
 * OpenAPI spec version: 1.0.0
 * 
 *
 * NOTE: This class is auto generated by the swagger code generator program.
 * https://github.com/swagger-api/swagger-codegen.git
 * Do not edit the class manually.
 */

import { NotificationResponseDto } from './notification-response-dto';
 /**
 * 
 *
 * @export
 * @interface SqlSugarPagedListNotificationResponseDto
 */
export interface SqlSugarPagedListNotificationResponseDto {

    /**
     * @type {number}
     * @memberof SqlSugarPagedListNotificationResponseDto
     */
    page?: number;

    /**
     * @type {number}
     * @memberof SqlSugarPagedListNotificationResponseDto
     */
    pageSize?: number;

    /**
     * @type {number}
     * @memberof SqlSugarPagedListNotificationResponseDto
     */
    total?: number;

    /**
     * @type {number}
     * @memberof SqlSugarPagedListNotificationResponseDto
     */
    totalPages?: number;

    /**
     * @type {Array<NotificationResponseDto>}
     * @memberof SqlSugarPagedListNotificationResponseDto
     */
    items?: Array<NotificationResponseDto> | null;

    /**
     * @type {boolean}
     * @memberof SqlSugarPagedListNotificationResponseDto
     */
    hasPrevPage?: boolean;

    /**
     * @type {boolean}
     * @memberof SqlSugarPagedListNotificationResponseDto
     */
    hasNextPage?: boolean;
}
