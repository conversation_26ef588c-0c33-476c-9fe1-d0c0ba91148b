/* tslint:disable */
/* eslint-disable */
/**
 * All Groups
 * No description provided (generated by Swagger Codegen https://github.com/swagger-api/swagger-codegen)
 *
 * OpenAPI spec version: 1.0.0
 * 
 *
 * NOTE: This class is auto generated by the swagger code generator program.
 * https://github.com/swagger-api/swagger-codegen.git
 * Do not edit the class manually.
 */

import { StorageStatus } from './storage-status';
 /**
 * 
 *
 * @export
 * @interface RESTfulResultStorageStatus
 */
export interface RESTfulResultStorageStatus {

    /**
     * @type {number}
     * @memberof RESTfulResultStorageStatus
     */
    statusCode?: number | null;

    /**
     * @type {StorageStatus}
     * @memberof RESTfulResultStorageStatus
     */
    data?: StorageStatus;

    /**
     * @type {boolean}
     * @memberof RESTfulResultStorageStatus
     */
    succeeded?: boolean;

    /**
     * @type {any}
     * @memberof RESTfulResultStorageStatus
     */
    errors?: any | null;

    /**
     * @type {any}
     * @memberof RESTfulResultStorageStatus
     */
    extras?: any | null;

    /**
     * @type {number}
     * @memberof RESTfulResultStorageStatus
     */
    timestamp?: number;
}
