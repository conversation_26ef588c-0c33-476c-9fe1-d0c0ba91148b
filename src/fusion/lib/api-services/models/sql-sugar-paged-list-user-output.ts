/* tslint:disable */
/* eslint-disable */
/**
 * All Groups
 * No description provided (generated by Swagger Codegen https://github.com/swagger-api/swagger-codegen)
 *
 * OpenAPI spec version: 1.0.0
 * 
 *
 * NOTE: This class is auto generated by the swagger code generator program.
 * https://github.com/swagger-api/swagger-codegen.git
 * Do not edit the class manually.
 */

import { UserOutput } from './user-output';
 /**
 * 
 *
 * @export
 * @interface SqlSugarPagedListUserOutput
 */
export interface SqlSugarPagedListUserOutput {

    /**
     * @type {number}
     * @memberof SqlSugarPagedListUserOutput
     */
    page?: number;

    /**
     * @type {number}
     * @memberof SqlSugarPagedListUserOutput
     */
    pageSize?: number;

    /**
     * @type {number}
     * @memberof SqlSugarPagedListUserOutput
     */
    total?: number;

    /**
     * @type {number}
     * @memberof SqlSugarPagedListUserOutput
     */
    totalPages?: number;

    /**
     * @type {Array<UserOutput>}
     * @memberof SqlSugarPagedListUserOutput
     */
    items?: Array<UserOutput> | null;

    /**
     * @type {boolean}
     * @memberof SqlSugarPagedListUserOutput
     */
    hasPrevPage?: boolean;

    /**
     * @type {boolean}
     * @memberof SqlSugarPagedListUserOutput
     */
    hasNextPage?: boolean;
}
