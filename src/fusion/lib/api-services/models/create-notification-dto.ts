/* tslint:disable */
/* eslint-disable */
/**
 * All Groups
 * No description provided (generated by Swagger Codegen https://github.com/swagger-api/swagger-codegen)
 *
 * OpenAPI spec version: 1.0.0
 * 
 *
 * NOTE: This class is auto generated by the swagger code generator program.
 * https://github.com/swagger-api/swagger-codegen.git
 * Do not edit the class manually.
 */

import { NotificationSourceEnum } from './notification-source-enum';
import { NotificationTypeEnum } from './notification-type-enum';
 /**
 * 
 *
 * @export
 * @interface CreateNotificationDto
 */
export interface CreateNotificationDto {

    /**
     * @type {string}
     * @memberof CreateNotificationDto
     */
    title: string;

    /**
     * @type {string}
     * @memberof CreateNotificationDto
     */
    message: string;

    /**
     * @type {NotificationTypeEnum}
     * @memberof CreateNotificationDto
     */
    type?: NotificationTypeEnum;

    /**
     * @type {NotificationSourceEnum}
     * @memberof CreateNotificationDto
     */
    source?: NotificationSourceEnum;

    /**
     * @type {string}
     * @memberof CreateNotificationDto
     */
    actionUrl?: string | null;

    /**
     * @type {string}
     * @memberof CreateNotificationDto
     */
    details?: string | null;

    /**
     * @type {string}
     * @memberof CreateNotificationDto
     */
    relatedEntityId?: string | null;

    /**
     * @type {string}
     * @memberof CreateNotificationDto
     */
    relatedEntityType?: string | null;
}
