/* tslint:disable */
/* eslint-disable */
/**
 * All Groups
 * No description provided (generated by Swagger Codegen https://github.com/swagger-api/swagger-codegen)
 *
 * OpenAPI spec version: 1.0.0
 * 
 *
 * NOTE: This class is auto generated by the swagger code generator program.
 * https://github.com/swagger-api/swagger-codegen.git
 * Do not edit the class manually.
 */

import { OperationStatus } from './operation-status';
 /**
 * ????
 *
 * @export
 * @interface ConnectionRecord
 */
export interface ConnectionRecord {

    /**
     * ???
     *
     * @type {Date}
     * @memberof ConnectionRecord
     */
    timestamp?: Date;

    /**
     * ????
     *
     * @type {string}
     * @memberof ConnectionRecord
     */
    operation?: string | null;

    /**
     * ????
     *
     * @type {string}
     * @memberof ConnectionRecord
     */
    details?: string | null;

    /**
     * @type {OperationStatus}
     * @memberof ConnectionRecord
     */
    status?: OperationStatus;
}
