/* tslint:disable */
/* eslint-disable */
/**
 * All Groups
 * No description provided (generated by Swagger Codegen https://github.com/swagger-api/swagger-codegen)
 *
 * OpenAPI spec version: 1.0.0
 * 
 *
 * NOTE: This class is auto generated by the swagger code generator program.
 * https://github.com/swagger-api/swagger-codegen.git
 * Do not edit the class manually.
 */

import { SqlSugarPagedListDeviceLabelSelectOutput } from './sql-sugar-paged-list-device-label-select-output';
 /**
 * 
 *
 * @export
 * @interface RESTfulResultSqlSugarPagedListDeviceLabelSelectOutput
 */
export interface RESTfulResultSqlSugarPagedListDeviceLabelSelectOutput {

    /**
     * @type {number}
     * @memberof RESTfulResultSqlSugarPagedListDeviceLabelSelectOutput
     */
    statusCode?: number | null;

    /**
     * @type {SqlSugarPagedListDeviceLabelSelectOutput}
     * @memberof RESTfulResultSqlSugarPagedListDeviceLabelSelectOutput
     */
    data?: SqlSugarPagedListDeviceLabelSelectOutput;

    /**
     * @type {boolean}
     * @memberof RESTfulResultSqlSugarPagedListDeviceLabelSelectOutput
     */
    succeeded?: boolean;

    /**
     * @type {any}
     * @memberof RESTfulResultSqlSugarPagedListDeviceLabelSelectOutput
     */
    errors?: any | null;

    /**
     * @type {any}
     * @memberof RESTfulResultSqlSugarPagedListDeviceLabelSelectOutput
     */
    extras?: any | null;

    /**
     * @type {number}
     * @memberof RESTfulResultSqlSugarPagedListDeviceLabelSelectOutput
     */
    timestamp?: number;
}
