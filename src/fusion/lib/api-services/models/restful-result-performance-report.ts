/* tslint:disable */
/* eslint-disable */
/**
 * All Groups
 * No description provided (generated by Swagger Codegen https://github.com/swagger-api/swagger-codegen)
 *
 * OpenAPI spec version: 1.0.0
 * 
 *
 * NOTE: This class is auto generated by the swagger code generator program.
 * https://github.com/swagger-api/swagger-codegen.git
 * Do not edit the class manually.
 */

import { PerformanceReport } from './performance-report';
 /**
 * 
 *
 * @export
 * @interface RESTfulResultPerformanceReport
 */
export interface RESTfulResultPerformanceReport {

    /**
     * @type {number}
     * @memberof RESTfulResultPerformanceReport
     */
    statusCode?: number | null;

    /**
     * @type {PerformanceReport}
     * @memberof RESTfulResultPerformanceReport
     */
    data?: PerformanceReport;

    /**
     * @type {boolean}
     * @memberof RESTfulResultPerformanceReport
     */
    succeeded?: boolean;

    /**
     * @type {any}
     * @memberof RESTfulResultPerformanceReport
     */
    errors?: any | null;

    /**
     * @type {any}
     * @memberof RESTfulResultPerformanceReport
     */
    extras?: any | null;

    /**
     * @type {number}
     * @memberof RESTfulResultPerformanceReport
     */
    timestamp?: number;
}
