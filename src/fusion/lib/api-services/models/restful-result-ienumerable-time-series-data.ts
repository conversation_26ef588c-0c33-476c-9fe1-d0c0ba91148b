/* tslint:disable */
/* eslint-disable */
/**
 * All Groups
 * No description provided (generated by Swagger Codegen https://github.com/swagger-api/swagger-codegen)
 *
 * OpenAPI spec version: 1.0.0
 * 
 *
 * NOTE: This class is auto generated by the swagger code generator program.
 * https://github.com/swagger-api/swagger-codegen.git
 * Do not edit the class manually.
 */

import { TimeSeriesData } from './time-series-data';
 /**
 * 
 *
 * @export
 * @interface RESTfulResultIEnumerableTimeSeriesData
 */
export interface RESTfulResultIEnumerableTimeSeriesData {

    /**
     * @type {number}
     * @memberof RESTfulResultIEnumerableTimeSeriesData
     */
    statusCode?: number | null;

    /**
     * @type {Array<TimeSeriesData>}
     * @memberof RESTfulResultIEnumerableTimeSeriesData
     */
    data?: Array<TimeSeriesData> | null;

    /**
     * @type {boolean}
     * @memberof RESTfulResultIEnumerableTimeSeriesData
     */
    succeeded?: boolean;

    /**
     * @type {any}
     * @memberof RESTfulResultIEnumerableTimeSeriesData
     */
    errors?: any | null;

    /**
     * @type {any}
     * @memberof RESTfulResultIEnumerableTimeSeriesData
     */
    extras?: any | null;

    /**
     * @type {number}
     * @memberof RESTfulResultIEnumerableTimeSeriesData
     */
    timestamp?: number;
}
