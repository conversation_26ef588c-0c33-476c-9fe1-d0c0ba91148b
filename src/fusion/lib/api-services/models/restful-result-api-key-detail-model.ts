/* tslint:disable */
/* eslint-disable */
/**
 * All Groups
 * No description provided (generated by Swagger Codegen https://github.com/swagger-api/swagger-codegen)
 *
 * OpenAPI spec version: 1.0.0
 * 
 *
 * NOTE: This class is auto generated by the swagger code generator program.
 * https://github.com/swagger-api/swagger-codegen.git
 * Do not edit the class manually.
 */

import { ApiKeyDetailModel } from './api-key-detail-model';
 /**
 * 
 *
 * @export
 * @interface RESTfulResultApiKeyDetailModel
 */
export interface RESTfulResultApiKeyDetailModel {

    /**
     * @type {number}
     * @memberof RESTfulResultApiKeyDetailModel
     */
    statusCode?: number | null;

    /**
     * @type {ApiKeyDetailModel}
     * @memberof RESTfulResultApiKeyDetailModel
     */
    data?: ApiKeyDetailModel;

    /**
     * @type {boolean}
     * @memberof RESTfulResultApiKeyDetailModel
     */
    succeeded?: boolean;

    /**
     * @type {any}
     * @memberof RESTfulResultApiKeyDetailModel
     */
    errors?: any | null;

    /**
     * @type {any}
     * @memberof RESTfulResultApiKeyDetailModel
     */
    extras?: any | null;

    /**
     * @type {number}
     * @memberof RESTfulResultApiKeyDetailModel
     */
    timestamp?: number;
}
