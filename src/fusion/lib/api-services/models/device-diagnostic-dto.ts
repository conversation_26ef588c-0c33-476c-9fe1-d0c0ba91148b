/* tslint:disable */
/* eslint-disable */
/**
 * All Groups
 * No description provided (generated by Swagger Codegen https://github.com/swagger-api/swagger-codegen)
 *
 * OpenAPI spec version: 1.0.0
 * 
 *
 * NOTE: This class is auto generated by the swagger code generator program.
 * https://github.com/swagger-api/swagger-codegen.git
 * Do not edit the class manually.
 */

import { ConfigurationDiagnostic } from './configuration-diagnostic';
import { ConnectionDiagnostic } from './connection-diagnostic';
import { DataDiagnostic } from './data-diagnostic';
import { DeviceDiagnosticStatus } from './device-diagnostic-status';
import { DiagnosticRecommendation } from './diagnostic-recommendation';
import { HealthDiagnostic } from './health-diagnostic';
import { HistoryDiagnosticData } from './history-diagnostic-data';
import { PerformanceDiagnostic } from './performance-diagnostic';
 /**
 * ??????
 *
 * @export
 * @interface DeviceDiagnosticDto
 */
export interface DeviceDiagnosticDto {

    /**
     * ??ID
     *
     * @type {number}
     * @memberof DeviceDiagnosticDto
     */
    deviceId?: number;

    /**
     * ?????
     *
     * @type {string}
     * @memberof DeviceDiagnosticDto
     */
    deviceIdentifier?: string | null;

    /**
     * ????
     *
     * @type {string}
     * @memberof DeviceDiagnosticDto
     */
    deviceName?: string | null;

    /**
     * ????
     *
     * @type {Date}
     * @memberof DeviceDiagnosticDto
     */
    diagnosticTime?: Date;

    /**
     * ??????(0-100)
     *
     * @type {number}
     * @memberof DeviceDiagnosticDto
     */
    overallHealthScore?: number;

    /**
     * @type {DeviceDiagnosticStatus}
     * @memberof DeviceDiagnosticDto
     */
    overallStatus?: DeviceDiagnosticStatus;

    /**
     * @type {ConnectionDiagnostic}
     * @memberof DeviceDiagnosticDto
     */
    connection?: ConnectionDiagnostic;

    /**
     * @type {PerformanceDiagnostic}
     * @memberof DeviceDiagnosticDto
     */
    performance?: PerformanceDiagnostic;

    /**
     * @type {HealthDiagnostic}
     * @memberof DeviceDiagnosticDto
     */
    health?: HealthDiagnostic;

    /**
     * @type {ConfigurationDiagnostic}
     * @memberof DeviceDiagnosticDto
     */
    configuration?: ConfigurationDiagnostic;

    /**
     * @type {DataDiagnostic}
     * @memberof DeviceDiagnosticDto
     */
    data?: DataDiagnostic;

    /**
     * ??????
     *
     * @type {Array<DiagnosticRecommendation>}
     * @memberof DeviceDiagnosticDto
     */
    recommendations?: Array<DiagnosticRecommendation> | null;

    /**
     * @type {HistoryDiagnosticData}
     * @memberof DeviceDiagnosticDto
     */
    history?: HistoryDiagnosticData;
}
