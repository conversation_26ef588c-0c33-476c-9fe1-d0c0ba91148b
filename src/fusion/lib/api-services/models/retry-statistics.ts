/* tslint:disable */
/* eslint-disable */
/**
 * All Groups
 * No description provided (generated by Swagger Codegen https://github.com/swagger-api/swagger-codegen)
 *
 * OpenAPI spec version: 1.0.0
 * 
 *
 * NOTE: This class is auto generated by the swagger code generator program.
 * https://github.com/swagger-api/swagger-codegen.git
 * Do not edit the class manually.
 */

 /**
 * 
 *
 * @export
 * @interface RetryStatistics
 */
export interface RetryStatistics {

    /**
     * @type {number}
     * @memberof RetryStatistics
     */
    totalCount?: number;

    /**
     * @type {number}
     * @memberof RetryStatistics
     */
    pendingCount?: number;

    /**
     * @type {number}
     * @memberof RetryStatistics
     */
    processingCount?: number;

    /**
     * @type {number}
     * @memberof RetryStatistics
     */
    completedCount?: number;

    /**
     * @type {number}
     * @memberof RetryStatistics
     */
    storageSize?: number;
}
