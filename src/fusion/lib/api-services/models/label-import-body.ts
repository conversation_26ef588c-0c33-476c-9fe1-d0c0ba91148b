/* tslint:disable */
/* eslint-disable */
/**
 * All Groups
 * No description provided (generated by Swagger Codegen https://github.com/swagger-api/swagger-codegen)
 *
 * OpenAPI spec version: 1.0.0
 * 
 *
 * NOTE: This class is auto generated by the swagger code generator program.
 * https://github.com/swagger-api/swagger-codegen.git
 * Do not edit the class manually.
 */

import { ImportModeEnum } from './import-mode-enum';
 /**
 * 
 *
 * @export
 * @interface LabelImportBody
 */
export interface LabelImportBody {

    /**
     * ??ID
     *
     * @type {number}
     * @memberof LabelImportBody
     */
    deviceId?: number;

    /**
     * Excel??
     *
     * @type {Blob}
     * @memberof LabelImportBody
     */
    file?: Blob;

    /**
     * @type {ImportModeEnum}
     * @memberof LabelImportBody
     */
    importMode?: ImportModeEnum;

    /**
     * ?????
     *
     * @type {Array}
     * @memberof LabelImportBody
     */
    ignoreFields?: Array;
}
