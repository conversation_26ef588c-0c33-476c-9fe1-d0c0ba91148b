/* tslint:disable */
/* eslint-disable */
/**
 * All Groups
 * No description provided (generated by Swagger Codegen https://github.com/swagger-api/swagger-codegen)
 *
 * OpenAPI spec version: 1.0.0
 * 
 *
 * NOTE: This class is auto generated by the swagger code generator program.
 * https://github.com/swagger-api/swagger-codegen.git
 * Do not edit the class manually.
 */

import { PagedListApiKeyModel } from './paged-list-api-key-model';
 /**
 * 
 *
 * @export
 * @interface RESTfulResultPagedListApiKeyModel
 */
export interface RESTfulResultPagedListApiKeyModel {

    /**
     * @type {number}
     * @memberof RESTfulResultPagedListApiKeyModel
     */
    statusCode?: number | null;

    /**
     * @type {PagedListApiKeyModel}
     * @memberof RESTfulResultPagedListApiKeyModel
     */
    data?: PagedListApiKeyModel;

    /**
     * @type {boolean}
     * @memberof RESTfulResultPagedListApiKeyModel
     */
    succeeded?: boolean;

    /**
     * @type {any}
     * @memberof RESTfulResultPagedListApiKeyModel
     */
    errors?: any | null;

    /**
     * @type {any}
     * @memberof RESTfulResultPagedListApiKeyModel
     */
    extras?: any | null;

    /**
     * @type {number}
     * @memberof RESTfulResultPagedListApiKeyModel
     */
    timestamp?: number;
}
