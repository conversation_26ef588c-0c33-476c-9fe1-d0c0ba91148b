/* tslint:disable */
/* eslint-disable */
/**
 * All Groups
 * No description provided (generated by Swagger Codegen https://github.com/swagger-api/swagger-codegen)
 *
 * OpenAPI spec version: 1.0.0
 * 
 *
 * NOTE: This class is auto generated by the swagger code generator program.
 * https://github.com/swagger-api/swagger-codegen.git
 * Do not edit the class manually.
 */

import { MqttMessageLog } from './mqtt-message-log';
 /**
 * 
 *
 * @export
 * @interface RESTfulResultListMqttMessageLog
 */
export interface RESTfulResultListMqttMessageLog {

    /**
     * @type {number}
     * @memberof RESTfulResultListMqttMessageLog
     */
    statusCode?: number | null;

    /**
     * @type {Array<MqttMessageLog>}
     * @memberof RESTfulResultListMqttMessageLog
     */
    data?: Array<MqttMessageLog> | null;

    /**
     * @type {boolean}
     * @memberof RESTfulResultListMqttMessageLog
     */
    succeeded?: boolean;

    /**
     * @type {any}
     * @memberof RESTfulResultListMqttMessageLog
     */
    errors?: any | null;

    /**
     * @type {any}
     * @memberof RESTfulResultListMqttMessageLog
     */
    extras?: any | null;

    /**
     * @type {number}
     * @memberof RESTfulResultListMqttMessageLog
     */
    timestamp?: number;
}
