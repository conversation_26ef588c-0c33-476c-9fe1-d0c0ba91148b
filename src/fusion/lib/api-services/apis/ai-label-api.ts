/* tslint:disable */
/* eslint-disable */
/**
 * All Groups
 * No description provided (generated by Swagger Codegen https://github.com/swagger-api/swagger-codegen)
 *
 * OpenAPI spec version: 1.0.0
 * 
 *
 * NOTE: This class is auto generated by the swagger code generator program.
 * https://github.com/swagger-api/swagger-codegen.git
 * Do not edit the class manually.
 */

import globalAxios, { AxiosResponse, AxiosInstance, AxiosRequestConfig, AxiosPromise } from 'axios';
import { Configuration } from '../configuration';
// Some imports not used depending on template conditions
// @ts-ignore
import { BASE_PATH, COLLECTION_FORMATS, RequestArgs, BaseAPI, RequiredError } from '../base';
import { AILabelGenerationRequest } from '../models/ai-label-generation-request';
import { AILabelGenerationResponse } from '../models/ai-label-generation-response';
import { LabelFieldMetadata } from '../models/label-field-metadata';
import { PropertyConfiguration } from '../models/property-configuration';

// 迁移注释：从Next.js迁移到Vite+React，保持API服务的结构和调用方式

/**
 * AILabelApi - axios parameter creator
 * @export
 */
export const AILabelApiAxiosParamCreator = function (configuration?: Configuration) {
    return {
        /**
         * AI生成标签配置
         * @summary AI生成标签配置
         * @param {AILabelGenerationRequest} [body] 
         * @param {*} [options] Override http request option.
         * @throws {RequiredError}
         */
        generateLabels: async (body?: AILabelGenerationRequest, options: AxiosRequestConfig = {}): Promise<RequestArgs> => {
            const localVarPath = `/api/label/ai/generate`;
            // use dummy base URL string because the URL constructor only accepts absolute URLs.
            const localVarUrlObj = new URL(localVarPath, 'https://example.com');
            let baseOptions;
            if (configuration) {
                baseOptions = configuration.baseOptions;
            }
            const localVarRequestOptions = { method: 'POST', ...baseOptions, ...options};
            const localVarHeaderParameter = {} as any;
            const localVarQueryParameter = {} as any;

            localVarHeaderParameter['Content-Type'] = 'application/json';

            const query = new URLSearchParams(localVarUrlObj.search);
            for (const key in localVarQueryParameter) {
                query.set(key, localVarQueryParameter[key]);
            }
            for (const key in options.params) {
                query.set(key, options.params[key]);
            }
            localVarUrlObj.search = (new URLSearchParams(query)).toString();
            let headersFromBaseOptions = baseOptions && baseOptions.headers ? baseOptions.headers : {};
            localVarRequestOptions.headers = {...localVarHeaderParameter, ...headersFromBaseOptions, ...options.headers};
            const needsSerialization = (typeof body !== "string") || localVarRequestOptions.headers['Content-Type'] === 'application/json';
            localVarRequestOptions.data =  needsSerialization ? JSON.stringify(body !== undefined ? body : {}) : (body || "");

            return {
                url: localVarUrlObj.pathname + localVarUrlObj.search + localVarUrlObj.hash,
                options: localVarRequestOptions,
            };
        },
        /**
         * 获取协议配置信息
         * @summary 获取协议配置信息
         * @param {string} protocolType 协议类型
         * @param {*} [options] Override http request option.
         * @throws {RequiredError}
         */
        getProtocolConfig: async (protocolType: string, options: AxiosRequestConfig = {}): Promise<RequestArgs> => {
            // verify required parameter 'protocolType' is not null or undefined
            if (protocolType === null || protocolType === undefined) {
                throw new RequiredError('protocolType','Required parameter protocolType was null or undefined when calling getProtocolConfig.');
            }
            const localVarPath = `/api/label/ai/protocol-config/{protocolType}`
                .replace(`{${"protocolType"}}`, encodeURIComponent(String(protocolType)));
            // use dummy base URL string because the URL constructor only accepts absolute URLs.
            const localVarUrlObj = new URL(localVarPath, 'https://example.com');
            let baseOptions;
            if (configuration) {
                baseOptions = configuration.baseOptions;
            }
            const localVarRequestOptions = { method: 'GET', ...baseOptions, ...options};
            const localVarHeaderParameter = {} as any;
            const localVarQueryParameter = {} as any;

            const query = new URLSearchParams(localVarUrlObj.search);
            for (const key in localVarQueryParameter) {
                query.set(key, localVarQueryParameter[key]);
            }
            for (const key in options.params) {
                query.set(key, options.params[key]);
            }
            localVarUrlObj.search = (new URLSearchParams(query)).toString();
            let headersFromBaseOptions = baseOptions && baseOptions.headers ? baseOptions.headers : {};
            localVarRequestOptions.headers = {...localVarHeaderParameter, ...headersFromBaseOptions, ...options.headers};

            return {
                url: localVarUrlObj.pathname + localVarUrlObj.search + localVarUrlObj.hash,
                options: localVarRequestOptions,
            };
        },
        /**
         * 获取标签字段元数据
         * @summary 获取标签字段元数据
         * @param {*} [options] Override http request option.
         * @throws {RequiredError}
         */
        getFieldMetadata: async (options: AxiosRequestConfig = {}): Promise<RequestArgs> => {
            const localVarPath = `/api/label/ai/field-metadata`;
            // use dummy base URL string because the URL constructor only accepts absolute URLs.
            const localVarUrlObj = new URL(localVarPath, 'https://example.com');
            let baseOptions;
            if (configuration) {
                baseOptions = configuration.baseOptions;
            }
            const localVarRequestOptions = { method: 'GET', ...baseOptions, ...options};
            const localVarHeaderParameter = {} as any;
            const localVarQueryParameter = {} as any;

            const query = new URLSearchParams(localVarUrlObj.search);
            for (const key in localVarQueryParameter) {
                query.set(key, localVarQueryParameter[key]);
            }
            for (const key in options.params) {
                query.set(key, options.params[key]);
            }
            localVarUrlObj.search = (new URLSearchParams(query)).toString();
            let headersFromBaseOptions = baseOptions && baseOptions.headers ? baseOptions.headers : {};
            localVarRequestOptions.headers = {...localVarHeaderParameter, ...headersFromBaseOptions, ...options.headers};

            return {
                url: localVarUrlObj.pathname + localVarUrlObj.search + localVarUrlObj.hash,
                options: localVarRequestOptions,
            };
        },
    }
};

/**
 * AILabelApi - functional programming interface
 * @export
 */
export const AILabelApiFp = function(configuration?: Configuration) {
    return {
        /**
         * AI生成标签配置
         * @summary AI生成标签配置
         * @param {AILabelGenerationRequest} [body] 
         * @param {*} [options] Override http request option.
         * @throws {RequiredError}
         */
        async generateLabels(body?: AILabelGenerationRequest, options?: AxiosRequestConfig): Promise<(axios?: AxiosInstance, basePath?: string) => AxiosPromise<AILabelGenerationResponse>> {
            const localVarAxiosArgs = await AILabelApiAxiosParamCreator(configuration).generateLabels(body, options);
            return (axios: AxiosInstance = globalAxios, basePath: string = BASE_PATH) => {
                const axiosRequestArgs = {...localVarAxiosArgs.options, url: basePath + localVarAxiosArgs.url};
                return axios.request(axiosRequestArgs);
            };
        },
        /**
         * 获取协议配置信息
         * @summary 获取协议配置信息
         * @param {string} protocolType 协议类型
         * @param {*} [options] Override http request option.
         * @throws {RequiredError}
         */
        async getProtocolConfig(protocolType: string, options?: AxiosRequestConfig): Promise<(axios?: AxiosInstance, basePath?: string) => AxiosPromise<Array<PropertyConfiguration>>> {
            const localVarAxiosArgs = await AILabelApiAxiosParamCreator(configuration).getProtocolConfig(protocolType, options);
            return (axios: AxiosInstance = globalAxios, basePath: string = BASE_PATH) => {
                const axiosRequestArgs = {...localVarAxiosArgs.options, url: basePath + localVarAxiosArgs.url};
                return axios.request(axiosRequestArgs);
            };
        },
        /**
         * 获取标签字段元数据
         * @summary 获取标签字段元数据
         * @param {*} [options] Override http request option.
         * @throws {RequiredError}
         */
        async getFieldMetadata(options?: AxiosRequestConfig): Promise<(axios?: AxiosInstance, basePath?: string) => AxiosPromise<LabelFieldMetadata>> {
            const localVarAxiosArgs = await AILabelApiAxiosParamCreator(configuration).getFieldMetadata(options);
            return (axios: AxiosInstance = globalAxios, basePath: string = BASE_PATH) => {
                const axiosRequestArgs = {...localVarAxiosArgs.options, url: basePath + localVarAxiosArgs.url};
                return axios.request(axiosRequestArgs);
            };
        },
    }
};

/**
 * AILabelApi - factory interface
 * @export
 */
export const AILabelApiFactory = function (configuration?: Configuration, basePath?: string, axios?: AxiosInstance) {
    return {
        /**
         * AI生成标签配置
         * @summary AI生成标签配置
         * @param {AILabelGenerationRequest} [body] 
         * @param {*} [options] Override http request option.
         * @throws {RequiredError}
         */
        generateLabels(body?: AILabelGenerationRequest, options?: any): AxiosPromise<AILabelGenerationResponse> {
            return AILabelApiFp(configuration).generateLabels(body, options).then((request) => request(axios, basePath));
        },
        /**
         * 获取协议配置信息
         * @summary 获取协议配置信息
         * @param {string} protocolType 协议类型
         * @param {*} [options] Override http request option.
         * @throws {RequiredError}
         */
        getProtocolConfig(protocolType: string, options?: any): AxiosPromise<Array<PropertyConfiguration>> {
            return AILabelApiFp(configuration).getProtocolConfig(protocolType, options).then((request) => request(axios, basePath));
        },
        /**
         * 获取标签字段元数据
         * @summary 获取标签字段元数据
         * @param {*} [options] Override http request option.
         * @throws {RequiredError}
         */
        getFieldMetadata(options?: any): AxiosPromise<LabelFieldMetadata> {
            return AILabelApiFp(configuration).getFieldMetadata(options).then((request) => request(axios, basePath));
        },
    };
};

/**
 * AILabelApi - object-oriented interface
 * @export
 * @class AILabelApi
 * @extends {BaseAPI}
 */
export class AILabelApi extends BaseAPI {
    /**
     * AI生成标签配置
     * @summary AI生成标签配置
     * @param {AILabelGenerationRequest} [body] 
     * @param {*} [options] Override http request option.
     * @throws {RequiredError}
     * @memberof AILabelApi
     */
    public generateLabels(body?: AILabelGenerationRequest, options?: AxiosRequestConfig) {
        return AILabelApiFp(this.configuration).generateLabels(body, options).then((request) => request(this.axios, this.basePath));
    }
    /**
     * 获取协议配置信息
     * @summary 获取协议配置信息
     * @param {string} protocolType 协议类型
     * @param {*} [options] Override http request option.
     * @throws {RequiredError}
     * @memberof AILabelApi
     */
    public getProtocolConfig(protocolType: string, options?: AxiosRequestConfig) {
        return AILabelApiFp(this.configuration).getProtocolConfig(protocolType, options).then((request) => request(this.axios, this.basePath));
    }
    /**
     * 获取标签字段元数据
     * @summary 获取标签字段元数据
     * @param {*} [options] Override http request option.
     * @throws {RequiredError}
     * @memberof AILabelApi
     */
    public getFieldMetadata(options?: AxiosRequestConfig) {
        return AILabelApiFp(this.configuration).getFieldMetadata(options).then((request) => request(this.axios, this.basePath));
    }
}
