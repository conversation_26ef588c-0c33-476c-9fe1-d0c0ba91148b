/* tslint:disable */
/* eslint-disable */
/**
 * All Groups
 * No description provided (generated by Swagger Codegen https://github.com/swagger-api/swagger-codegen)
 *
 * OpenAPI spec version: 1.0.0
 * 
 *
 * NOTE: This class is auto generated by the swagger code generator program.
 * https://github.com/swagger-api/swagger-codegen.git
 * Do not edit the class manually.
 */

import globalAxios, { AxiosResponse, AxiosInstance, AxiosRequestConfig } from 'axios';
import { Configuration } from '../configuration';
// Some imports not used depending on template conditions
// @ts-ignore
import { BASE_PATH, COLLECTION_FORMATS, RequestArgs, BaseAPI, RequiredError } from '../base';
import { GatewayConfiguration } from '../models';
import { RESTfulResultGatewayConfiguration } from '../models';
/**
 * GatewayConfigurationApi - axios parameter creator
 * @export
 */
export const GatewayConfigurationApiAxiosParamCreator = function (configuration?: Configuration) {
    return {
        /**
         * 
         * @param {*} [options] Override http request option.
         * @throws {RequiredError}
         */
        getConfigurationAsync: async (options: AxiosRequestConfig = {}): Promise<RequestArgs> => {
            const localVarPath = `/api/system/config/gateway/get`;
            // use dummy base URL string because the URL constructor only accepts absolute URLs.
            const localVarUrlObj = new URL(localVarPath, 'https://example.com');
            let baseOptions;
            if (configuration) {
                baseOptions = configuration.baseOptions;
            }
            const localVarRequestOptions :AxiosRequestConfig = { method: 'GET', ...baseOptions, ...options};
            const localVarHeaderParameter = {} as any;
            const localVarQueryParameter = {} as any;

            // authentication Bearer required
            // http bearer authentication required
            if (configuration && configuration.accessToken) {
                const accessToken = typeof configuration.accessToken === 'function'
                    ? await configuration.accessToken()
                    : await configuration.accessToken;
                localVarHeaderParameter["Authorization"] = "Bearer " + accessToken;
            }

            const query = new URLSearchParams(localVarUrlObj.search);
            for (const key in localVarQueryParameter) {
                query.set(key, localVarQueryParameter[key]);
            }
            for (const key in options.params) {
                query.set(key, options.params[key]);
            }
            localVarUrlObj.search = (new URLSearchParams(query)).toString();
            let headersFromBaseOptions = baseOptions && baseOptions.headers ? baseOptions.headers : {};
            localVarRequestOptions.headers = {...localVarHeaderParameter, ...headersFromBaseOptions, ...options.headers};

            return {
                url: localVarUrlObj.pathname + localVarUrlObj.search + localVarUrlObj.hash,
                options: localVarRequestOptions,
            };
        },
        /**
         * 
         * @param {GatewayConfiguration} [body] 
         * @param {*} [options] Override http request option.
         * @throws {RequiredError}
         */
        saveConfigurationAsync: async (body?: GatewayConfiguration, options: AxiosRequestConfig = {}): Promise<RequestArgs> => {
            const localVarPath = `/api/system/config/gateway/save`;
            // use dummy base URL string because the URL constructor only accepts absolute URLs.
            const localVarUrlObj = new URL(localVarPath, 'https://example.com');
            let baseOptions;
            if (configuration) {
                baseOptions = configuration.baseOptions;
            }
            const localVarRequestOptions :AxiosRequestConfig = { method: 'POST', ...baseOptions, ...options};
            const localVarHeaderParameter = {} as any;
            const localVarQueryParameter = {} as any;

            // authentication Bearer required
            // http bearer authentication required
            if (configuration && configuration.accessToken) {
                const accessToken = typeof configuration.accessToken === 'function'
                    ? await configuration.accessToken()
                    : await configuration.accessToken;
                localVarHeaderParameter["Authorization"] = "Bearer " + accessToken;
            }

            localVarHeaderParameter['Content-Type'] = 'application/json-patch+json';

            const query = new URLSearchParams(localVarUrlObj.search);
            for (const key in localVarQueryParameter) {
                query.set(key, localVarQueryParameter[key]);
            }
            for (const key in options.params) {
                query.set(key, options.params[key]);
            }
            localVarUrlObj.search = (new URLSearchParams(query)).toString();
            let headersFromBaseOptions = baseOptions && baseOptions.headers ? baseOptions.headers : {};
            localVarRequestOptions.headers = {...localVarHeaderParameter, ...headersFromBaseOptions, ...options.headers};
            const needsSerialization = (typeof body !== "string") || localVarRequestOptions.headers['Content-Type'] === 'application/json';
            localVarRequestOptions.data =  needsSerialization ? JSON.stringify(body !== undefined ? body : {}) : (body || "");

            return {
                url: localVarUrlObj.pathname + localVarUrlObj.search + localVarUrlObj.hash,
                options: localVarRequestOptions,
            };
        },
    }
};

/**
 * GatewayConfigurationApi - functional programming interface
 * @export
 */
export const GatewayConfigurationApiFp = function(configuration?: Configuration) {
    return {
        /**
         * 
         * @param {*} [options] Override http request option.
         * @throws {RequiredError}
         */
        async getConfigurationAsync(options?: AxiosRequestConfig): Promise<(axios?: AxiosInstance, basePath?: string) => Promise<AxiosResponse<RESTfulResultGatewayConfiguration>>> {
            const localVarAxiosArgs = await GatewayConfigurationApiAxiosParamCreator(configuration).getConfigurationAsync(options);
            return (axios: AxiosInstance = globalAxios, basePath: string = BASE_PATH) => {
                const axiosRequestArgs :AxiosRequestConfig = {...localVarAxiosArgs.options, url: basePath + localVarAxiosArgs.url};
                return axios.request(axiosRequestArgs);
            };
        },
        /**
         * 
         * @param {GatewayConfiguration} [body] 
         * @param {*} [options] Override http request option.
         * @throws {RequiredError}
         */
        async saveConfigurationAsync(body?: GatewayConfiguration, options?: AxiosRequestConfig): Promise<(axios?: AxiosInstance, basePath?: string) => Promise<AxiosResponse<void>>> {
            const localVarAxiosArgs = await GatewayConfigurationApiAxiosParamCreator(configuration).saveConfigurationAsync(body, options);
            return (axios: AxiosInstance = globalAxios, basePath: string = BASE_PATH) => {
                const axiosRequestArgs :AxiosRequestConfig = {...localVarAxiosArgs.options, url: basePath + localVarAxiosArgs.url};
                return axios.request(axiosRequestArgs);
            };
        },
    }
};

/**
 * GatewayConfigurationApi - factory interface
 * @export
 */
export const GatewayConfigurationApiFactory = function (configuration?: Configuration, basePath?: string, axios?: AxiosInstance) {
    return {
        /**
         * 
         * @param {*} [options] Override http request option.
         * @throws {RequiredError}
         */
        async getConfigurationAsync(options?: AxiosRequestConfig): Promise<AxiosResponse<RESTfulResultGatewayConfiguration>> {
            return GatewayConfigurationApiFp(configuration).getConfigurationAsync(options).then((request) => request(axios, basePath));
        },
        /**
         * 
         * @param {GatewayConfiguration} [body] 
         * @param {*} [options] Override http request option.
         * @throws {RequiredError}
         */
        async saveConfigurationAsync(body?: GatewayConfiguration, options?: AxiosRequestConfig): Promise<AxiosResponse<void>> {
            return GatewayConfigurationApiFp(configuration).saveConfigurationAsync(body, options).then((request) => request(axios, basePath));
        },
    };
};

/**
 * GatewayConfigurationApi - object-oriented interface
 * @export
 * @class GatewayConfigurationApi
 * @extends {BaseAPI}
 */
export class GatewayConfigurationApi extends BaseAPI {
    /**
     * 
     * @param {*} [options] Override http request option.
     * @throws {RequiredError}
     * @memberof GatewayConfigurationApi
     */
    public async getConfigurationAsync(options?: AxiosRequestConfig) : Promise<AxiosResponse<RESTfulResultGatewayConfiguration>> {
        return GatewayConfigurationApiFp(this.configuration).getConfigurationAsync(options).then((request) => request(this.axios, this.basePath));
    }
    /**
     * 
     * @param {GatewayConfiguration} [body] 
     * @param {*} [options] Override http request option.
     * @throws {RequiredError}
     * @memberof GatewayConfigurationApi
     */
    public async saveConfigurationAsync(body?: GatewayConfiguration, options?: AxiosRequestConfig) : Promise<AxiosResponse<void>> {
        return GatewayConfigurationApiFp(this.configuration).saveConfigurationAsync(body, options).then((request) => request(this.axios, this.basePath));
    }
}
