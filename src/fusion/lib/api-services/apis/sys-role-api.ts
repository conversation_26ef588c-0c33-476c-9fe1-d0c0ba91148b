/* tslint:disable */
/* eslint-disable */
/**
 * All Groups
 * No description provided (generated by Swagger Codegen https://github.com/swagger-api/swagger-codegen)
 *
 * OpenAPI spec version: 1.0.0
 * 
 *
 * NOTE: This class is auto generated by the swagger code generator program.
 * https://github.com/swagger-api/swagger-codegen.git
 * Do not edit the class manually.
 */

import globalAxios, { AxiosResponse, AxiosInstance, AxiosRequestConfig } from 'axios';
import { Configuration } from '../configuration';
// Some imports not used depending on template conditions
// @ts-ignore
import { BASE_PATH, COLLECTION_FORMATS, RequestArgs, BaseAPI, RequiredError } from '../base';
import { AccountTypeEnum } from '../models';
import { RESTfulResultInt64 } from '../models';
import { RESTfulResultListInt64 } from '../models';
import { RESTfulResultListRoleOutput } from '../models';
import { RESTfulResultSqlSugarPagedListRoleOutput } from '../models';
import { RoleCreateInput } from '../models';
import { RoleUpdateInput } from '../models';
/**
 * SysRoleApi - axios parameter creator
 * @export
 */
export const SysRoleApiAxiosParamCreator = function (configuration?: Configuration) {
    return {
        /**
         * 
         * @summary ????
         * @param {number} id 
         * @param {*} [options] Override http request option.
         * @throws {RequiredError}
         */
        apiSysRoleIdDelete: async (id: number, options: AxiosRequestConfig = {}): Promise<RequestArgs> => {
            // verify required parameter 'id' is not null or undefined
            if (id === null || id === undefined) {
                throw new RequiredError('id','Required parameter id was null or undefined when calling apiSysRoleIdDelete.');
            }
            const localVarPath = `/api/sysRole/{id}`
                .replace(`{${"id"}}`, encodeURIComponent(String(id)));
            // use dummy base URL string because the URL constructor only accepts absolute URLs.
            const localVarUrlObj = new URL(localVarPath, 'https://example.com');
            let baseOptions;
            if (configuration) {
                baseOptions = configuration.baseOptions;
            }
            const localVarRequestOptions :AxiosRequestConfig = { method: 'DELETE', ...baseOptions, ...options};
            const localVarHeaderParameter = {} as any;
            const localVarQueryParameter = {} as any;

            // authentication Bearer required
            // http bearer authentication required
            if (configuration && configuration.accessToken) {
                const accessToken = typeof configuration.accessToken === 'function'
                    ? await configuration.accessToken()
                    : await configuration.accessToken;
                localVarHeaderParameter["Authorization"] = "Bearer " + accessToken;
            }

            const query = new URLSearchParams(localVarUrlObj.search);
            for (const key in localVarQueryParameter) {
                query.set(key, localVarQueryParameter[key]);
            }
            for (const key in options.params) {
                query.set(key, options.params[key]);
            }
            localVarUrlObj.search = (new URLSearchParams(query)).toString();
            let headersFromBaseOptions = baseOptions && baseOptions.headers ? baseOptions.headers : {};
            localVarRequestOptions.headers = {...localVarHeaderParameter, ...headersFromBaseOptions, ...options.headers};

            return {
                url: localVarUrlObj.pathname + localVarUrlObj.search + localVarUrlObj.hash,
                options: localVarRequestOptions,
            };
        },
        /**
         * 
         * @summary ??????
         * @param {number} id 
         * @param {*} [options] Override http request option.
         * @throws {RequiredError}
         */
        apiSysRoleIdMenusGet: async (id: number, options: AxiosRequestConfig = {}): Promise<RequestArgs> => {
            // verify required parameter 'id' is not null or undefined
            if (id === null || id === undefined) {
                throw new RequiredError('id','Required parameter id was null or undefined when calling apiSysRoleIdMenusGet.');
            }
            const localVarPath = `/api/sysRole/{id}/menus`
                .replace(`{${"id"}}`, encodeURIComponent(String(id)));
            // use dummy base URL string because the URL constructor only accepts absolute URLs.
            const localVarUrlObj = new URL(localVarPath, 'https://example.com');
            let baseOptions;
            if (configuration) {
                baseOptions = configuration.baseOptions;
            }
            const localVarRequestOptions :AxiosRequestConfig = { method: 'GET', ...baseOptions, ...options};
            const localVarHeaderParameter = {} as any;
            const localVarQueryParameter = {} as any;

            // authentication Bearer required
            // http bearer authentication required
            if (configuration && configuration.accessToken) {
                const accessToken = typeof configuration.accessToken === 'function'
                    ? await configuration.accessToken()
                    : await configuration.accessToken;
                localVarHeaderParameter["Authorization"] = "Bearer " + accessToken;
            }

            const query = new URLSearchParams(localVarUrlObj.search);
            for (const key in localVarQueryParameter) {
                query.set(key, localVarQueryParameter[key]);
            }
            for (const key in options.params) {
                query.set(key, options.params[key]);
            }
            localVarUrlObj.search = (new URLSearchParams(query)).toString();
            let headersFromBaseOptions = baseOptions && baseOptions.headers ? baseOptions.headers : {};
            localVarRequestOptions.headers = {...localVarHeaderParameter, ...headersFromBaseOptions, ...options.headers};

            return {
                url: localVarUrlObj.pathname + localVarUrlObj.search + localVarUrlObj.hash,
                options: localVarRequestOptions,
            };
        },
        /**
         * 
         * @summary ??????
         * @param {Array<number>} body 
         * @param {number} id 
         * @param {*} [options] Override http request option.
         * @throws {RequiredError}
         */
        apiSysRoleIdMenusPost: async (body: Array<number>, id: number, options: AxiosRequestConfig = {}): Promise<RequestArgs> => {
            // verify required parameter 'body' is not null or undefined
            if (body === null || body === undefined) {
                throw new RequiredError('body','Required parameter body was null or undefined when calling apiSysRoleIdMenusPost.');
            }
            // verify required parameter 'id' is not null or undefined
            if (id === null || id === undefined) {
                throw new RequiredError('id','Required parameter id was null or undefined when calling apiSysRoleIdMenusPost.');
            }
            const localVarPath = `/api/sysRole/{id}/menus`
                .replace(`{${"id"}}`, encodeURIComponent(String(id)));
            // use dummy base URL string because the URL constructor only accepts absolute URLs.
            const localVarUrlObj = new URL(localVarPath, 'https://example.com');
            let baseOptions;
            if (configuration) {
                baseOptions = configuration.baseOptions;
            }
            const localVarRequestOptions :AxiosRequestConfig = { method: 'POST', ...baseOptions, ...options};
            const localVarHeaderParameter = {} as any;
            const localVarQueryParameter = {} as any;

            // authentication Bearer required
            // http bearer authentication required
            if (configuration && configuration.accessToken) {
                const accessToken = typeof configuration.accessToken === 'function'
                    ? await configuration.accessToken()
                    : await configuration.accessToken;
                localVarHeaderParameter["Authorization"] = "Bearer " + accessToken;
            }

            localVarHeaderParameter['Content-Type'] = 'application/json-patch+json';

            const query = new URLSearchParams(localVarUrlObj.search);
            for (const key in localVarQueryParameter) {
                query.set(key, localVarQueryParameter[key]);
            }
            for (const key in options.params) {
                query.set(key, options.params[key]);
            }
            localVarUrlObj.search = (new URLSearchParams(query)).toString();
            let headersFromBaseOptions = baseOptions && baseOptions.headers ? baseOptions.headers : {};
            localVarRequestOptions.headers = {...localVarHeaderParameter, ...headersFromBaseOptions, ...options.headers};
            const needsSerialization = (typeof body !== "string") || localVarRequestOptions.headers['Content-Type'] === 'application/json';
            localVarRequestOptions.data =  needsSerialization ? JSON.stringify(body !== undefined ? body : {}) : (body || "");

            return {
                url: localVarUrlObj.pathname + localVarUrlObj.search + localVarUrlObj.hash,
                options: localVarRequestOptions,
            };
        },
        /**
         * 
         * @summary ????????
         * @param {*} [options] Override http request option.
         * @throws {RequiredError}
         */
        apiSysRoleListGet: async (options: AxiosRequestConfig = {}): Promise<RequestArgs> => {
            const localVarPath = `/api/sysRole/list`;
            // use dummy base URL string because the URL constructor only accepts absolute URLs.
            const localVarUrlObj = new URL(localVarPath, 'https://example.com');
            let baseOptions;
            if (configuration) {
                baseOptions = configuration.baseOptions;
            }
            const localVarRequestOptions :AxiosRequestConfig = { method: 'GET', ...baseOptions, ...options};
            const localVarHeaderParameter = {} as any;
            const localVarQueryParameter = {} as any;

            // authentication Bearer required
            // http bearer authentication required
            if (configuration && configuration.accessToken) {
                const accessToken = typeof configuration.accessToken === 'function'
                    ? await configuration.accessToken()
                    : await configuration.accessToken;
                localVarHeaderParameter["Authorization"] = "Bearer " + accessToken;
            }

            const query = new URLSearchParams(localVarUrlObj.search);
            for (const key in localVarQueryParameter) {
                query.set(key, localVarQueryParameter[key]);
            }
            for (const key in options.params) {
                query.set(key, options.params[key]);
            }
            localVarUrlObj.search = (new URLSearchParams(query)).toString();
            let headersFromBaseOptions = baseOptions && baseOptions.headers ? baseOptions.headers : {};
            localVarRequestOptions.headers = {...localVarHeaderParameter, ...headersFromBaseOptions, ...options.headers};

            return {
                url: localVarUrlObj.pathname + localVarUrlObj.search + localVarUrlObj.hash,
                options: localVarRequestOptions,
            };
        },
        /**
         * 
         * @summary ????????
         * @param {number} [page] ??
         * @param {number} [pageSize] ???
         * @param {string} [name] ????
         * @param {string} [code] ????
         * @param {boolean} [status] ??
         * @param {AccountTypeEnum} [accountType] ????
         * @param {*} [options] Override http request option.
         * @throws {RequiredError}
         */
        apiSysRolePageGet: async (page?: number, pageSize?: number, name?: string, code?: string, status?: boolean, accountType?: AccountTypeEnum, options: AxiosRequestConfig = {}): Promise<RequestArgs> => {
            const localVarPath = `/api/sysRole/page`;
            // use dummy base URL string because the URL constructor only accepts absolute URLs.
            const localVarUrlObj = new URL(localVarPath, 'https://example.com');
            let baseOptions;
            if (configuration) {
                baseOptions = configuration.baseOptions;
            }
            const localVarRequestOptions :AxiosRequestConfig = { method: 'GET', ...baseOptions, ...options};
            const localVarHeaderParameter = {} as any;
            const localVarQueryParameter = {} as any;

            // authentication Bearer required
            // http bearer authentication required
            if (configuration && configuration.accessToken) {
                const accessToken = typeof configuration.accessToken === 'function'
                    ? await configuration.accessToken()
                    : await configuration.accessToken;
                localVarHeaderParameter["Authorization"] = "Bearer " + accessToken;
            }

            if (page !== undefined) {
                localVarQueryParameter['Page'] = page;
            }

            if (pageSize !== undefined) {
                localVarQueryParameter['PageSize'] = pageSize;
            }

            if (name !== undefined) {
                localVarQueryParameter['Name'] = name;
            }

            if (code !== undefined) {
                localVarQueryParameter['Code'] = code;
            }

            if (status !== undefined) {
                localVarQueryParameter['Status'] = status;
            }

            if (accountType !== undefined) {
                localVarQueryParameter['AccountType'] = accountType;
            }

            const query = new URLSearchParams(localVarUrlObj.search);
            for (const key in localVarQueryParameter) {
                query.set(key, localVarQueryParameter[key]);
            }
            for (const key in options.params) {
                query.set(key, options.params[key]);
            }
            localVarUrlObj.search = (new URLSearchParams(query)).toString();
            let headersFromBaseOptions = baseOptions && baseOptions.headers ? baseOptions.headers : {};
            localVarRequestOptions.headers = {...localVarHeaderParameter, ...headersFromBaseOptions, ...options.headers};

            return {
                url: localVarUrlObj.pathname + localVarUrlObj.search + localVarUrlObj.hash,
                options: localVarRequestOptions,
            };
        },
        /**
         * 
         * @summary ????
         * @param {RoleCreateInput} body 
         * @param {*} [options] Override http request option.
         * @throws {RequiredError}
         */
        apiSysRoleRolePost: async (body: RoleCreateInput, options: AxiosRequestConfig = {}): Promise<RequestArgs> => {
            // verify required parameter 'body' is not null or undefined
            if (body === null || body === undefined) {
                throw new RequiredError('body','Required parameter body was null or undefined when calling apiSysRoleRolePost.');
            }
            const localVarPath = `/api/sysRole/role`;
            // use dummy base URL string because the URL constructor only accepts absolute URLs.
            const localVarUrlObj = new URL(localVarPath, 'https://example.com');
            let baseOptions;
            if (configuration) {
                baseOptions = configuration.baseOptions;
            }
            const localVarRequestOptions :AxiosRequestConfig = { method: 'POST', ...baseOptions, ...options};
            const localVarHeaderParameter = {} as any;
            const localVarQueryParameter = {} as any;

            // authentication Bearer required
            // http bearer authentication required
            if (configuration && configuration.accessToken) {
                const accessToken = typeof configuration.accessToken === 'function'
                    ? await configuration.accessToken()
                    : await configuration.accessToken;
                localVarHeaderParameter["Authorization"] = "Bearer " + accessToken;
            }

            localVarHeaderParameter['Content-Type'] = 'application/json-patch+json';

            const query = new URLSearchParams(localVarUrlObj.search);
            for (const key in localVarQueryParameter) {
                query.set(key, localVarQueryParameter[key]);
            }
            for (const key in options.params) {
                query.set(key, options.params[key]);
            }
            localVarUrlObj.search = (new URLSearchParams(query)).toString();
            let headersFromBaseOptions = baseOptions && baseOptions.headers ? baseOptions.headers : {};
            localVarRequestOptions.headers = {...localVarHeaderParameter, ...headersFromBaseOptions, ...options.headers};
            const needsSerialization = (typeof body !== "string") || localVarRequestOptions.headers['Content-Type'] === 'application/json';
            localVarRequestOptions.data =  needsSerialization ? JSON.stringify(body !== undefined ? body : {}) : (body || "");

            return {
                url: localVarUrlObj.pathname + localVarUrlObj.search + localVarUrlObj.hash,
                options: localVarRequestOptions,
            };
        },
        /**
         * 
         * @summary ????
         * @param {RoleUpdateInput} body 
         * @param {*} [options] Override http request option.
         * @throws {RequiredError}
         */
        apiSysRoleRolePut: async (body: RoleUpdateInput, options: AxiosRequestConfig = {}): Promise<RequestArgs> => {
            // verify required parameter 'body' is not null or undefined
            if (body === null || body === undefined) {
                throw new RequiredError('body','Required parameter body was null or undefined when calling apiSysRoleRolePut.');
            }
            const localVarPath = `/api/sysRole/role`;
            // use dummy base URL string because the URL constructor only accepts absolute URLs.
            const localVarUrlObj = new URL(localVarPath, 'https://example.com');
            let baseOptions;
            if (configuration) {
                baseOptions = configuration.baseOptions;
            }
            const localVarRequestOptions :AxiosRequestConfig = { method: 'PUT', ...baseOptions, ...options};
            const localVarHeaderParameter = {} as any;
            const localVarQueryParameter = {} as any;

            // authentication Bearer required
            // http bearer authentication required
            if (configuration && configuration.accessToken) {
                const accessToken = typeof configuration.accessToken === 'function'
                    ? await configuration.accessToken()
                    : await configuration.accessToken;
                localVarHeaderParameter["Authorization"] = "Bearer " + accessToken;
            }

            localVarHeaderParameter['Content-Type'] = 'application/json-patch+json';

            const query = new URLSearchParams(localVarUrlObj.search);
            for (const key in localVarQueryParameter) {
                query.set(key, localVarQueryParameter[key]);
            }
            for (const key in options.params) {
                query.set(key, options.params[key]);
            }
            localVarUrlObj.search = (new URLSearchParams(query)).toString();
            let headersFromBaseOptions = baseOptions && baseOptions.headers ? baseOptions.headers : {};
            localVarRequestOptions.headers = {...localVarHeaderParameter, ...headersFromBaseOptions, ...options.headers};
            const needsSerialization = (typeof body !== "string") || localVarRequestOptions.headers['Content-Type'] === 'application/json';
            localVarRequestOptions.data =  needsSerialization ? JSON.stringify(body !== undefined ? body : {}) : (body || "");

            return {
                url: localVarUrlObj.pathname + localVarUrlObj.search + localVarUrlObj.hash,
                options: localVarRequestOptions,
            };
        },
    }
};

/**
 * SysRoleApi - functional programming interface
 * @export
 */
export const SysRoleApiFp = function(configuration?: Configuration) {
    return {
        /**
         * 
         * @summary ????
         * @param {number} id 
         * @param {*} [options] Override http request option.
         * @throws {RequiredError}
         */
        async apiSysRoleIdDelete(id: number, options?: AxiosRequestConfig): Promise<(axios?: AxiosInstance, basePath?: string) => Promise<AxiosResponse<void>>> {
            const localVarAxiosArgs = await SysRoleApiAxiosParamCreator(configuration).apiSysRoleIdDelete(id, options);
            return (axios: AxiosInstance = globalAxios, basePath: string = BASE_PATH) => {
                const axiosRequestArgs :AxiosRequestConfig = {...localVarAxiosArgs.options, url: basePath + localVarAxiosArgs.url};
                return axios.request(axiosRequestArgs);
            };
        },
        /**
         * 
         * @summary ??????
         * @param {number} id 
         * @param {*} [options] Override http request option.
         * @throws {RequiredError}
         */
        async apiSysRoleIdMenusGet(id: number, options?: AxiosRequestConfig): Promise<(axios?: AxiosInstance, basePath?: string) => Promise<AxiosResponse<RESTfulResultListInt64>>> {
            const localVarAxiosArgs = await SysRoleApiAxiosParamCreator(configuration).apiSysRoleIdMenusGet(id, options);
            return (axios: AxiosInstance = globalAxios, basePath: string = BASE_PATH) => {
                const axiosRequestArgs :AxiosRequestConfig = {...localVarAxiosArgs.options, url: basePath + localVarAxiosArgs.url};
                return axios.request(axiosRequestArgs);
            };
        },
        /**
         * 
         * @summary ??????
         * @param {Array<number>} body 
         * @param {number} id 
         * @param {*} [options] Override http request option.
         * @throws {RequiredError}
         */
        async apiSysRoleIdMenusPost(body: Array<number>, id: number, options?: AxiosRequestConfig): Promise<(axios?: AxiosInstance, basePath?: string) => Promise<AxiosResponse<void>>> {
            const localVarAxiosArgs = await SysRoleApiAxiosParamCreator(configuration).apiSysRoleIdMenusPost(body, id, options);
            return (axios: AxiosInstance = globalAxios, basePath: string = BASE_PATH) => {
                const axiosRequestArgs :AxiosRequestConfig = {...localVarAxiosArgs.options, url: basePath + localVarAxiosArgs.url};
                return axios.request(axiosRequestArgs);
            };
        },
        /**
         * 
         * @summary ????????
         * @param {*} [options] Override http request option.
         * @throws {RequiredError}
         */
        async apiSysRoleListGet(options?: AxiosRequestConfig): Promise<(axios?: AxiosInstance, basePath?: string) => Promise<AxiosResponse<RESTfulResultListRoleOutput>>> {
            const localVarAxiosArgs = await SysRoleApiAxiosParamCreator(configuration).apiSysRoleListGet(options);
            return (axios: AxiosInstance = globalAxios, basePath: string = BASE_PATH) => {
                const axiosRequestArgs :AxiosRequestConfig = {...localVarAxiosArgs.options, url: basePath + localVarAxiosArgs.url};
                return axios.request(axiosRequestArgs);
            };
        },
        /**
         * 
         * @summary ????????
         * @param {number} [page] ??
         * @param {number} [pageSize] ???
         * @param {string} [name] ????
         * @param {string} [code] ????
         * @param {boolean} [status] ??
         * @param {AccountTypeEnum} [accountType] ????
         * @param {*} [options] Override http request option.
         * @throws {RequiredError}
         */
        async apiSysRolePageGet(page?: number, pageSize?: number, name?: string, code?: string, status?: boolean, accountType?: AccountTypeEnum, options?: AxiosRequestConfig): Promise<(axios?: AxiosInstance, basePath?: string) => Promise<AxiosResponse<RESTfulResultSqlSugarPagedListRoleOutput>>> {
            const localVarAxiosArgs = await SysRoleApiAxiosParamCreator(configuration).apiSysRolePageGet(page, pageSize, name, code, status, accountType, options);
            return (axios: AxiosInstance = globalAxios, basePath: string = BASE_PATH) => {
                const axiosRequestArgs :AxiosRequestConfig = {...localVarAxiosArgs.options, url: basePath + localVarAxiosArgs.url};
                return axios.request(axiosRequestArgs);
            };
        },
        /**
         * 
         * @summary ????
         * @param {RoleCreateInput} body 
         * @param {*} [options] Override http request option.
         * @throws {RequiredError}
         */
        async apiSysRoleRolePost(body: RoleCreateInput, options?: AxiosRequestConfig): Promise<(axios?: AxiosInstance, basePath?: string) => Promise<AxiosResponse<RESTfulResultInt64>>> {
            const localVarAxiosArgs = await SysRoleApiAxiosParamCreator(configuration).apiSysRoleRolePost(body, options);
            return (axios: AxiosInstance = globalAxios, basePath: string = BASE_PATH) => {
                const axiosRequestArgs :AxiosRequestConfig = {...localVarAxiosArgs.options, url: basePath + localVarAxiosArgs.url};
                return axios.request(axiosRequestArgs);
            };
        },
        /**
         * 
         * @summary ????
         * @param {RoleUpdateInput} body 
         * @param {*} [options] Override http request option.
         * @throws {RequiredError}
         */
        async apiSysRoleRolePut(body: RoleUpdateInput, options?: AxiosRequestConfig): Promise<(axios?: AxiosInstance, basePath?: string) => Promise<AxiosResponse<void>>> {
            const localVarAxiosArgs = await SysRoleApiAxiosParamCreator(configuration).apiSysRoleRolePut(body, options);
            return (axios: AxiosInstance = globalAxios, basePath: string = BASE_PATH) => {
                const axiosRequestArgs :AxiosRequestConfig = {...localVarAxiosArgs.options, url: basePath + localVarAxiosArgs.url};
                return axios.request(axiosRequestArgs);
            };
        },
    }
};

/**
 * SysRoleApi - factory interface
 * @export
 */
export const SysRoleApiFactory = function (configuration?: Configuration, basePath?: string, axios?: AxiosInstance) {
    return {
        /**
         * 
         * @summary ????
         * @param {number} id 
         * @param {*} [options] Override http request option.
         * @throws {RequiredError}
         */
        async apiSysRoleIdDelete(id: number, options?: AxiosRequestConfig): Promise<AxiosResponse<void>> {
            return SysRoleApiFp(configuration).apiSysRoleIdDelete(id, options).then((request) => request(axios, basePath));
        },
        /**
         * 
         * @summary ??????
         * @param {number} id 
         * @param {*} [options] Override http request option.
         * @throws {RequiredError}
         */
        async apiSysRoleIdMenusGet(id: number, options?: AxiosRequestConfig): Promise<AxiosResponse<RESTfulResultListInt64>> {
            return SysRoleApiFp(configuration).apiSysRoleIdMenusGet(id, options).then((request) => request(axios, basePath));
        },
        /**
         * 
         * @summary ??????
         * @param {Array<number>} body 
         * @param {number} id 
         * @param {*} [options] Override http request option.
         * @throws {RequiredError}
         */
        async apiSysRoleIdMenusPost(body: Array<number>, id: number, options?: AxiosRequestConfig): Promise<AxiosResponse<void>> {
            return SysRoleApiFp(configuration).apiSysRoleIdMenusPost(body, id, options).then((request) => request(axios, basePath));
        },
        /**
         * 
         * @summary ????????
         * @param {*} [options] Override http request option.
         * @throws {RequiredError}
         */
        async apiSysRoleListGet(options?: AxiosRequestConfig): Promise<AxiosResponse<RESTfulResultListRoleOutput>> {
            return SysRoleApiFp(configuration).apiSysRoleListGet(options).then((request) => request(axios, basePath));
        },
        /**
         * 
         * @summary ????????
         * @param {number} [page] ??
         * @param {number} [pageSize] ???
         * @param {string} [name] ????
         * @param {string} [code] ????
         * @param {boolean} [status] ??
         * @param {AccountTypeEnum} [accountType] ????
         * @param {*} [options] Override http request option.
         * @throws {RequiredError}
         */
        async apiSysRolePageGet(page?: number, pageSize?: number, name?: string, code?: string, status?: boolean, accountType?: AccountTypeEnum, options?: AxiosRequestConfig): Promise<AxiosResponse<RESTfulResultSqlSugarPagedListRoleOutput>> {
            return SysRoleApiFp(configuration).apiSysRolePageGet(page, pageSize, name, code, status, accountType, options).then((request) => request(axios, basePath));
        },
        /**
         * 
         * @summary ????
         * @param {RoleCreateInput} body 
         * @param {*} [options] Override http request option.
         * @throws {RequiredError}
         */
        async apiSysRoleRolePost(body: RoleCreateInput, options?: AxiosRequestConfig): Promise<AxiosResponse<RESTfulResultInt64>> {
            return SysRoleApiFp(configuration).apiSysRoleRolePost(body, options).then((request) => request(axios, basePath));
        },
        /**
         * 
         * @summary ????
         * @param {RoleUpdateInput} body 
         * @param {*} [options] Override http request option.
         * @throws {RequiredError}
         */
        async apiSysRoleRolePut(body: RoleUpdateInput, options?: AxiosRequestConfig): Promise<AxiosResponse<void>> {
            return SysRoleApiFp(configuration).apiSysRoleRolePut(body, options).then((request) => request(axios, basePath));
        },
    };
};

/**
 * SysRoleApi - object-oriented interface
 * @export
 * @class SysRoleApi
 * @extends {BaseAPI}
 */
export class SysRoleApi extends BaseAPI {
    /**
     * 
     * @summary ????
     * @param {number} id 
     * @param {*} [options] Override http request option.
     * @throws {RequiredError}
     * @memberof SysRoleApi
     */
    public async apiSysRoleIdDelete(id: number, options?: AxiosRequestConfig) : Promise<AxiosResponse<void>> {
        return SysRoleApiFp(this.configuration).apiSysRoleIdDelete(id, options).then((request) => request(this.axios, this.basePath));
    }
    /**
     * 
     * @summary ??????
     * @param {number} id 
     * @param {*} [options] Override http request option.
     * @throws {RequiredError}
     * @memberof SysRoleApi
     */
    public async apiSysRoleIdMenusGet(id: number, options?: AxiosRequestConfig) : Promise<AxiosResponse<RESTfulResultListInt64>> {
        return SysRoleApiFp(this.configuration).apiSysRoleIdMenusGet(id, options).then((request) => request(this.axios, this.basePath));
    }
    /**
     * 
     * @summary ??????
     * @param {Array<number>} body 
     * @param {number} id 
     * @param {*} [options] Override http request option.
     * @throws {RequiredError}
     * @memberof SysRoleApi
     */
    public async apiSysRoleIdMenusPost(body: Array<number>, id: number, options?: AxiosRequestConfig) : Promise<AxiosResponse<void>> {
        return SysRoleApiFp(this.configuration).apiSysRoleIdMenusPost(body, id, options).then((request) => request(this.axios, this.basePath));
    }
    /**
     * 
     * @summary ????????
     * @param {*} [options] Override http request option.
     * @throws {RequiredError}
     * @memberof SysRoleApi
     */
    public async apiSysRoleListGet(options?: AxiosRequestConfig) : Promise<AxiosResponse<RESTfulResultListRoleOutput>> {
        return SysRoleApiFp(this.configuration).apiSysRoleListGet(options).then((request) => request(this.axios, this.basePath));
    }
    /**
     * 
     * @summary ????????
     * @param {number} [page] ??
     * @param {number} [pageSize] ???
     * @param {string} [name] ????
     * @param {string} [code] ????
     * @param {boolean} [status] ??
     * @param {AccountTypeEnum} [accountType] ????
     * @param {*} [options] Override http request option.
     * @throws {RequiredError}
     * @memberof SysRoleApi
     */
    public async apiSysRolePageGet(page?: number, pageSize?: number, name?: string, code?: string, status?: boolean, accountType?: AccountTypeEnum, options?: AxiosRequestConfig) : Promise<AxiosResponse<RESTfulResultSqlSugarPagedListRoleOutput>> {
        return SysRoleApiFp(this.configuration).apiSysRolePageGet(page, pageSize, name, code, status, accountType, options).then((request) => request(this.axios, this.basePath));
    }
    /**
     * 
     * @summary ????
     * @param {RoleCreateInput} body 
     * @param {*} [options] Override http request option.
     * @throws {RequiredError}
     * @memberof SysRoleApi
     */
    public async apiSysRoleRolePost(body: RoleCreateInput, options?: AxiosRequestConfig) : Promise<AxiosResponse<RESTfulResultInt64>> {
        return SysRoleApiFp(this.configuration).apiSysRoleRolePost(body, options).then((request) => request(this.axios, this.basePath));
    }
    /**
     * 
     * @summary ????
     * @param {RoleUpdateInput} body 
     * @param {*} [options] Override http request option.
     * @throws {RequiredError}
     * @memberof SysRoleApi
     */
    public async apiSysRoleRolePut(body: RoleUpdateInput, options?: AxiosRequestConfig) : Promise<AxiosResponse<void>> {
        return SysRoleApiFp(this.configuration).apiSysRoleRolePut(body, options).then((request) => request(this.axios, this.basePath));
    }
}
