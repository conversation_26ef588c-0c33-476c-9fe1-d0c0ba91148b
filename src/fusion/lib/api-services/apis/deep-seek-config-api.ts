/* tslint:disable */
/* eslint-disable */
/**
 * All Groups
 * No description provided (generated by Swagger Codegen https://github.com/swagger-api/swagger-codegen)
 *
 * OpenAPI spec version: 1.0.0
 * 
 *
 * NOTE: This class is auto generated by the swagger code generator program.
 * https://github.com/swagger-api/swagger-codegen.git
 * Do not edit the class manually.
 */

import globalAxios, { AxiosResponse, AxiosInstance, AxiosRequestConfig } from 'axios';
import { Configuration } from '../configuration';
// Some imports not used depending on template conditions
// @ts-ignore
import { BASE_PATH, COLLECTION_FORMATS, RequestArgs, BaseAPI, RequiredError } from '../base';
import { DeepSeekOptions } from '../models';
import { RESTfulResultObject } from '../models';
/**
 * DeepSeekConfigApi - axios parameter creator
 * @export
 */
export const DeepSeekConfigApiAxiosParamCreator = function (configuration?: Configuration) {
    return {
        /**
         * 
         * @summary ?? DeepSeek ????
         * @param {*} [options] Override http request option.
         * @throws {RequiredError}
         */
        getConfig: async (options: AxiosRequestConfig = {}): Promise<RequestArgs> => {
            const localVarPath = `/api/ai/config/info`;
            // use dummy base URL string because the URL constructor only accepts absolute URLs.
            const localVarUrlObj = new URL(localVarPath, 'https://example.com');
            let baseOptions;
            if (configuration) {
                baseOptions = configuration.baseOptions;
            }
            const localVarRequestOptions :AxiosRequestConfig = { method: 'GET', ...baseOptions, ...options};
            const localVarHeaderParameter = {} as any;
            const localVarQueryParameter = {} as any;

            // authentication Bearer required
            // http bearer authentication required
            if (configuration && configuration.accessToken) {
                const accessToken = typeof configuration.accessToken === 'function'
                    ? await configuration.accessToken()
                    : await configuration.accessToken;
                localVarHeaderParameter["Authorization"] = "Bearer " + accessToken;
            }

            const query = new URLSearchParams(localVarUrlObj.search);
            for (const key in localVarQueryParameter) {
                query.set(key, localVarQueryParameter[key]);
            }
            for (const key in options.params) {
                query.set(key, options.params[key]);
            }
            localVarUrlObj.search = (new URLSearchParams(query)).toString();
            let headersFromBaseOptions = baseOptions && baseOptions.headers ? baseOptions.headers : {};
            localVarRequestOptions.headers = {...localVarHeaderParameter, ...headersFromBaseOptions, ...options.headers};

            return {
                url: localVarUrlObj.pathname + localVarUrlObj.search + localVarUrlObj.hash,
                options: localVarRequestOptions,
            };
        },
        /**
         * 
         * @summary ?? DeepSeek ??????
         * @param {*} [options] Override http request option.
         * @throws {RequiredError}
         */
        resetConfig: async (options: AxiosRequestConfig = {}): Promise<RequestArgs> => {
            const localVarPath = `/api/ai/config/reset`;
            // use dummy base URL string because the URL constructor only accepts absolute URLs.
            const localVarUrlObj = new URL(localVarPath, 'https://example.com');
            let baseOptions;
            if (configuration) {
                baseOptions = configuration.baseOptions;
            }
            const localVarRequestOptions :AxiosRequestConfig = { method: 'POST', ...baseOptions, ...options};
            const localVarHeaderParameter = {} as any;
            const localVarQueryParameter = {} as any;

            // authentication Bearer required
            // http bearer authentication required
            if (configuration && configuration.accessToken) {
                const accessToken = typeof configuration.accessToken === 'function'
                    ? await configuration.accessToken()
                    : await configuration.accessToken;
                localVarHeaderParameter["Authorization"] = "Bearer " + accessToken;
            }

            const query = new URLSearchParams(localVarUrlObj.search);
            for (const key in localVarQueryParameter) {
                query.set(key, localVarQueryParameter[key]);
            }
            for (const key in options.params) {
                query.set(key, options.params[key]);
            }
            localVarUrlObj.search = (new URLSearchParams(query)).toString();
            let headersFromBaseOptions = baseOptions && baseOptions.headers ? baseOptions.headers : {};
            localVarRequestOptions.headers = {...localVarHeaderParameter, ...headersFromBaseOptions, ...options.headers};

            return {
                url: localVarUrlObj.pathname + localVarUrlObj.search + localVarUrlObj.hash,
                options: localVarRequestOptions,
            };
        },
        /**
         * 
         * @summary ?? DeepSeek ????
         * @param {DeepSeekOptions} [body] ????
         * @param {*} [options] Override http request option.
         * @throws {RequiredError}
         */
        updateConfig: async (body?: DeepSeekOptions, options: AxiosRequestConfig = {}): Promise<RequestArgs> => {
            const localVarPath = `/api/ai/config/update`;
            // use dummy base URL string because the URL constructor only accepts absolute URLs.
            const localVarUrlObj = new URL(localVarPath, 'https://example.com');
            let baseOptions;
            if (configuration) {
                baseOptions = configuration.baseOptions;
            }
            const localVarRequestOptions :AxiosRequestConfig = { method: 'PUT', ...baseOptions, ...options};
            const localVarHeaderParameter = {} as any;
            const localVarQueryParameter = {} as any;

            // authentication Bearer required
            // http bearer authentication required
            if (configuration && configuration.accessToken) {
                const accessToken = typeof configuration.accessToken === 'function'
                    ? await configuration.accessToken()
                    : await configuration.accessToken;
                localVarHeaderParameter["Authorization"] = "Bearer " + accessToken;
            }

            localVarHeaderParameter['Content-Type'] = 'application/json-patch+json';

            const query = new URLSearchParams(localVarUrlObj.search);
            for (const key in localVarQueryParameter) {
                query.set(key, localVarQueryParameter[key]);
            }
            for (const key in options.params) {
                query.set(key, options.params[key]);
            }
            localVarUrlObj.search = (new URLSearchParams(query)).toString();
            let headersFromBaseOptions = baseOptions && baseOptions.headers ? baseOptions.headers : {};
            localVarRequestOptions.headers = {...localVarHeaderParameter, ...headersFromBaseOptions, ...options.headers};
            const needsSerialization = (typeof body !== "string") || localVarRequestOptions.headers['Content-Type'] === 'application/json';
            localVarRequestOptions.data =  needsSerialization ? JSON.stringify(body !== undefined ? body : {}) : (body || "");

            return {
                url: localVarUrlObj.pathname + localVarUrlObj.search + localVarUrlObj.hash,
                options: localVarRequestOptions,
            };
        },
        /**
         * 
         * @summary ?? DeepSeek ??
         * @param {DeepSeekOptions} [body] ????
         * @param {*} [options] Override http request option.
         * @throws {RequiredError}
         */
        validateConfig: async (body?: DeepSeekOptions, options: AxiosRequestConfig = {}): Promise<RequestArgs> => {
            const localVarPath = `/api/ai/config/validate`;
            // use dummy base URL string because the URL constructor only accepts absolute URLs.
            const localVarUrlObj = new URL(localVarPath, 'https://example.com');
            let baseOptions;
            if (configuration) {
                baseOptions = configuration.baseOptions;
            }
            const localVarRequestOptions :AxiosRequestConfig = { method: 'POST', ...baseOptions, ...options};
            const localVarHeaderParameter = {} as any;
            const localVarQueryParameter = {} as any;

            // authentication Bearer required
            // http bearer authentication required
            if (configuration && configuration.accessToken) {
                const accessToken = typeof configuration.accessToken === 'function'
                    ? await configuration.accessToken()
                    : await configuration.accessToken;
                localVarHeaderParameter["Authorization"] = "Bearer " + accessToken;
            }

            localVarHeaderParameter['Content-Type'] = 'application/json-patch+json';

            const query = new URLSearchParams(localVarUrlObj.search);
            for (const key in localVarQueryParameter) {
                query.set(key, localVarQueryParameter[key]);
            }
            for (const key in options.params) {
                query.set(key, options.params[key]);
            }
            localVarUrlObj.search = (new URLSearchParams(query)).toString();
            let headersFromBaseOptions = baseOptions && baseOptions.headers ? baseOptions.headers : {};
            localVarRequestOptions.headers = {...localVarHeaderParameter, ...headersFromBaseOptions, ...options.headers};
            const needsSerialization = (typeof body !== "string") || localVarRequestOptions.headers['Content-Type'] === 'application/json';
            localVarRequestOptions.data =  needsSerialization ? JSON.stringify(body !== undefined ? body : {}) : (body || "");

            return {
                url: localVarUrlObj.pathname + localVarUrlObj.search + localVarUrlObj.hash,
                options: localVarRequestOptions,
            };
        },
    }
};

/**
 * DeepSeekConfigApi - functional programming interface
 * @export
 */
export const DeepSeekConfigApiFp = function(configuration?: Configuration) {
    return {
        /**
         * 
         * @summary ?? DeepSeek ????
         * @param {*} [options] Override http request option.
         * @throws {RequiredError}
         */
        async getConfig(options?: AxiosRequestConfig): Promise<(axios?: AxiosInstance, basePath?: string) => Promise<AxiosResponse<RESTfulResultObject>>> {
            const localVarAxiosArgs = await DeepSeekConfigApiAxiosParamCreator(configuration).getConfig(options);
            return (axios: AxiosInstance = globalAxios, basePath: string = BASE_PATH) => {
                const axiosRequestArgs :AxiosRequestConfig = {...localVarAxiosArgs.options, url: basePath + localVarAxiosArgs.url};
                return axios.request(axiosRequestArgs);
            };
        },
        /**
         * 
         * @summary ?? DeepSeek ??????
         * @param {*} [options] Override http request option.
         * @throws {RequiredError}
         */
        async resetConfig(options?: AxiosRequestConfig): Promise<(axios?: AxiosInstance, basePath?: string) => Promise<AxiosResponse<RESTfulResultObject>>> {
            const localVarAxiosArgs = await DeepSeekConfigApiAxiosParamCreator(configuration).resetConfig(options);
            return (axios: AxiosInstance = globalAxios, basePath: string = BASE_PATH) => {
                const axiosRequestArgs :AxiosRequestConfig = {...localVarAxiosArgs.options, url: basePath + localVarAxiosArgs.url};
                return axios.request(axiosRequestArgs);
            };
        },
        /**
         * 
         * @summary ?? DeepSeek ????
         * @param {DeepSeekOptions} [body] ????
         * @param {*} [options] Override http request option.
         * @throws {RequiredError}
         */
        async updateConfig(body?: DeepSeekOptions, options?: AxiosRequestConfig): Promise<(axios?: AxiosInstance, basePath?: string) => Promise<AxiosResponse<RESTfulResultObject>>> {
            const localVarAxiosArgs = await DeepSeekConfigApiAxiosParamCreator(configuration).updateConfig(body, options);
            return (axios: AxiosInstance = globalAxios, basePath: string = BASE_PATH) => {
                const axiosRequestArgs :AxiosRequestConfig = {...localVarAxiosArgs.options, url: basePath + localVarAxiosArgs.url};
                return axios.request(axiosRequestArgs);
            };
        },
        /**
         * 
         * @summary ?? DeepSeek ??
         * @param {DeepSeekOptions} [body] ????
         * @param {*} [options] Override http request option.
         * @throws {RequiredError}
         */
        async validateConfig(body?: DeepSeekOptions, options?: AxiosRequestConfig): Promise<(axios?: AxiosInstance, basePath?: string) => Promise<AxiosResponse<RESTfulResultObject>>> {
            const localVarAxiosArgs = await DeepSeekConfigApiAxiosParamCreator(configuration).validateConfig(body, options);
            return (axios: AxiosInstance = globalAxios, basePath: string = BASE_PATH) => {
                const axiosRequestArgs :AxiosRequestConfig = {...localVarAxiosArgs.options, url: basePath + localVarAxiosArgs.url};
                return axios.request(axiosRequestArgs);
            };
        },
    }
};

/**
 * DeepSeekConfigApi - factory interface
 * @export
 */
export const DeepSeekConfigApiFactory = function (configuration?: Configuration, basePath?: string, axios?: AxiosInstance) {
    return {
        /**
         * 
         * @summary ?? DeepSeek ????
         * @param {*} [options] Override http request option.
         * @throws {RequiredError}
         */
        async getConfig(options?: AxiosRequestConfig): Promise<AxiosResponse<RESTfulResultObject>> {
            return DeepSeekConfigApiFp(configuration).getConfig(options).then((request) => request(axios, basePath));
        },
        /**
         * 
         * @summary ?? DeepSeek ??????
         * @param {*} [options] Override http request option.
         * @throws {RequiredError}
         */
        async resetConfig(options?: AxiosRequestConfig): Promise<AxiosResponse<RESTfulResultObject>> {
            return DeepSeekConfigApiFp(configuration).resetConfig(options).then((request) => request(axios, basePath));
        },
        /**
         * 
         * @summary ?? DeepSeek ????
         * @param {DeepSeekOptions} [body] ????
         * @param {*} [options] Override http request option.
         * @throws {RequiredError}
         */
        async updateConfig(body?: DeepSeekOptions, options?: AxiosRequestConfig): Promise<AxiosResponse<RESTfulResultObject>> {
            return DeepSeekConfigApiFp(configuration).updateConfig(body, options).then((request) => request(axios, basePath));
        },
        /**
         * 
         * @summary ?? DeepSeek ??
         * @param {DeepSeekOptions} [body] ????
         * @param {*} [options] Override http request option.
         * @throws {RequiredError}
         */
        async validateConfig(body?: DeepSeekOptions, options?: AxiosRequestConfig): Promise<AxiosResponse<RESTfulResultObject>> {
            return DeepSeekConfigApiFp(configuration).validateConfig(body, options).then((request) => request(axios, basePath));
        },
    };
};

/**
 * DeepSeekConfigApi - object-oriented interface
 * @export
 * @class DeepSeekConfigApi
 * @extends {BaseAPI}
 */
export class DeepSeekConfigApi extends BaseAPI {
    /**
     * 
     * @summary ?? DeepSeek ????
     * @param {*} [options] Override http request option.
     * @throws {RequiredError}
     * @memberof DeepSeekConfigApi
     */
    public async getConfig(options?: AxiosRequestConfig) : Promise<AxiosResponse<RESTfulResultObject>> {
        return DeepSeekConfigApiFp(this.configuration).getConfig(options).then((request) => request(this.axios, this.basePath));
    }
    /**
     * 
     * @summary ?? DeepSeek ??????
     * @param {*} [options] Override http request option.
     * @throws {RequiredError}
     * @memberof DeepSeekConfigApi
     */
    public async resetConfig(options?: AxiosRequestConfig) : Promise<AxiosResponse<RESTfulResultObject>> {
        return DeepSeekConfigApiFp(this.configuration).resetConfig(options).then((request) => request(this.axios, this.basePath));
    }
    /**
     * 
     * @summary ?? DeepSeek ????
     * @param {DeepSeekOptions} [body] ????
     * @param {*} [options] Override http request option.
     * @throws {RequiredError}
     * @memberof DeepSeekConfigApi
     */
    public async updateConfig(body?: DeepSeekOptions, options?: AxiosRequestConfig) : Promise<AxiosResponse<RESTfulResultObject>> {
        return DeepSeekConfigApiFp(this.configuration).updateConfig(body, options).then((request) => request(this.axios, this.basePath));
    }
    /**
     * 
     * @summary ?? DeepSeek ??
     * @param {DeepSeekOptions} [body] ????
     * @param {*} [options] Override http request option.
     * @throws {RequiredError}
     * @memberof DeepSeekConfigApi
     */
    public async validateConfig(body?: DeepSeekOptions, options?: AxiosRequestConfig) : Promise<AxiosResponse<RESTfulResultObject>> {
        return DeepSeekConfigApiFp(this.configuration).validateConfig(body, options).then((request) => request(this.axios, this.basePath));
    }
}
