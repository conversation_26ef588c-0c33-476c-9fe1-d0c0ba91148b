/* tslint:disable */
/* eslint-disable */
/**
 * All Groups
 * No description provided (generated by Swagger Codegen https://github.com/swagger-api/swagger-codegen)
 *
 * OpenAPI spec version: 1.0.0
 * 
 *
 * NOTE: This class is auto generated by the swagger code generator program.
 * https://github.com/swagger-api/swagger-codegen.git
 * Do not edit the class manually.
 */

import globalAxios, { AxiosResponse, AxiosInstance, AxiosRequestConfig } from 'axios';
import { Configuration } from '../configuration';
// Some imports not used depending on template conditions
// @ts-ignore
import { BASE_PATH, COLLECTION_FORMATS, RequestArgs, BaseAPI, RequiredError } from '../base';
import { CaptureFilter } from '../models';
import { RESTfulResultBoolean } from '../models';
import { RESTfulResultListObject } from '../models';
import { RESTfulResultListPacketInfo } from '../models';
import { RESTfulResultObject } from '../models';
/**
 * PacketCaptureApi - axios parameter creator
 * @export
 */
export const PacketCaptureApiAxiosParamCreator = function (configuration?: Configuration) {
    return {
        /**
         * 
         * @summary ??????
         * @param {*} [options] Override http request option.
         * @throws {RequiredError}
         */
        clearPackets: async (options: AxiosRequestConfig = {}): Promise<RequestArgs> => {
            const localVarPath = `/api/system/network/capture/clear`;
            // use dummy base URL string because the URL constructor only accepts absolute URLs.
            const localVarUrlObj = new URL(localVarPath, 'https://example.com');
            let baseOptions;
            if (configuration) {
                baseOptions = configuration.baseOptions;
            }
            const localVarRequestOptions :AxiosRequestConfig = { method: 'POST', ...baseOptions, ...options};
            const localVarHeaderParameter = {} as any;
            const localVarQueryParameter = {} as any;

            // authentication Bearer required
            // http bearer authentication required
            if (configuration && configuration.accessToken) {
                const accessToken = typeof configuration.accessToken === 'function'
                    ? await configuration.accessToken()
                    : await configuration.accessToken;
                localVarHeaderParameter["Authorization"] = "Bearer " + accessToken;
            }

            const query = new URLSearchParams(localVarUrlObj.search);
            for (const key in localVarQueryParameter) {
                query.set(key, localVarQueryParameter[key]);
            }
            for (const key in options.params) {
                query.set(key, options.params[key]);
            }
            localVarUrlObj.search = (new URLSearchParams(query)).toString();
            let headersFromBaseOptions = baseOptions && baseOptions.headers ? baseOptions.headers : {};
            localVarRequestOptions.headers = {...localVarHeaderParameter, ...headersFromBaseOptions, ...options.headers};

            return {
                url: localVarUrlObj.pathname + localVarUrlObj.search + localVarUrlObj.hash,
                options: localVarRequestOptions,
            };
        },
        /**
         * 
         * @summary ????????
         * @param {*} [options] Override http request option.
         * @throws {RequiredError}
         */
        getInterfaces: async (options: AxiosRequestConfig = {}): Promise<RequestArgs> => {
            const localVarPath = `/api/system/network/capture/interfaces`;
            // use dummy base URL string because the URL constructor only accepts absolute URLs.
            const localVarUrlObj = new URL(localVarPath, 'https://example.com');
            let baseOptions;
            if (configuration) {
                baseOptions = configuration.baseOptions;
            }
            const localVarRequestOptions :AxiosRequestConfig = { method: 'GET', ...baseOptions, ...options};
            const localVarHeaderParameter = {} as any;
            const localVarQueryParameter = {} as any;

            // authentication Bearer required
            // http bearer authentication required
            if (configuration && configuration.accessToken) {
                const accessToken = typeof configuration.accessToken === 'function'
                    ? await configuration.accessToken()
                    : await configuration.accessToken;
                localVarHeaderParameter["Authorization"] = "Bearer " + accessToken;
            }

            const query = new URLSearchParams(localVarUrlObj.search);
            for (const key in localVarQueryParameter) {
                query.set(key, localVarQueryParameter[key]);
            }
            for (const key in options.params) {
                query.set(key, options.params[key]);
            }
            localVarUrlObj.search = (new URLSearchParams(query)).toString();
            let headersFromBaseOptions = baseOptions && baseOptions.headers ? baseOptions.headers : {};
            localVarRequestOptions.headers = {...localVarHeaderParameter, ...headersFromBaseOptions, ...options.headers};

            return {
                url: localVarUrlObj.pathname + localVarUrlObj.search + localVarUrlObj.hash,
                options: localVarRequestOptions,
            };
        },
        /**
         * 
         * @summary ??????
         * @param {number} [start] 
         * @param {number} [count] 
         * @param {*} [options] Override http request option.
         * @throws {RequiredError}
         */
        getPackets: async (start?: number, count?: number, options: AxiosRequestConfig = {}): Promise<RequestArgs> => {
            const localVarPath = `/api/system/network/capture/packets`;
            // use dummy base URL string because the URL constructor only accepts absolute URLs.
            const localVarUrlObj = new URL(localVarPath, 'https://example.com');
            let baseOptions;
            if (configuration) {
                baseOptions = configuration.baseOptions;
            }
            const localVarRequestOptions :AxiosRequestConfig = { method: 'GET', ...baseOptions, ...options};
            const localVarHeaderParameter = {} as any;
            const localVarQueryParameter = {} as any;

            // authentication Bearer required
            // http bearer authentication required
            if (configuration && configuration.accessToken) {
                const accessToken = typeof configuration.accessToken === 'function'
                    ? await configuration.accessToken()
                    : await configuration.accessToken;
                localVarHeaderParameter["Authorization"] = "Bearer " + accessToken;
            }

            if (start !== undefined) {
                localVarQueryParameter['start'] = start;
            }

            if (count !== undefined) {
                localVarQueryParameter['count'] = count;
            }

            const query = new URLSearchParams(localVarUrlObj.search);
            for (const key in localVarQueryParameter) {
                query.set(key, localVarQueryParameter[key]);
            }
            for (const key in options.params) {
                query.set(key, options.params[key]);
            }
            localVarUrlObj.search = (new URLSearchParams(query)).toString();
            let headersFromBaseOptions = baseOptions && baseOptions.headers ? baseOptions.headers : {};
            localVarRequestOptions.headers = {...localVarHeaderParameter, ...headersFromBaseOptions, ...options.headers};

            return {
                url: localVarUrlObj.pathname + localVarUrlObj.search + localVarUrlObj.hash,
                options: localVarRequestOptions,
            };
        },
        /**
         * 
         * @summary ??????
         * @param {*} [options] Override http request option.
         * @throws {RequiredError}
         */
        getStatus: async (options: AxiosRequestConfig = {}): Promise<RequestArgs> => {
            const localVarPath = `/api/system/network/capture/status`;
            // use dummy base URL string because the URL constructor only accepts absolute URLs.
            const localVarUrlObj = new URL(localVarPath, 'https://example.com');
            let baseOptions;
            if (configuration) {
                baseOptions = configuration.baseOptions;
            }
            const localVarRequestOptions :AxiosRequestConfig = { method: 'GET', ...baseOptions, ...options};
            const localVarHeaderParameter = {} as any;
            const localVarQueryParameter = {} as any;

            // authentication Bearer required
            // http bearer authentication required
            if (configuration && configuration.accessToken) {
                const accessToken = typeof configuration.accessToken === 'function'
                    ? await configuration.accessToken()
                    : await configuration.accessToken;
                localVarHeaderParameter["Authorization"] = "Bearer " + accessToken;
            }

            const query = new URLSearchParams(localVarUrlObj.search);
            for (const key in localVarQueryParameter) {
                query.set(key, localVarQueryParameter[key]);
            }
            for (const key in options.params) {
                query.set(key, options.params[key]);
            }
            localVarUrlObj.search = (new URLSearchParams(query)).toString();
            let headersFromBaseOptions = baseOptions && baseOptions.headers ? baseOptions.headers : {};
            localVarRequestOptions.headers = {...localVarHeaderParameter, ...headersFromBaseOptions, ...options.headers};

            return {
                url: localVarUrlObj.pathname + localVarUrlObj.search + localVarUrlObj.hash,
                options: localVarRequestOptions,
            };
        },
        /**
         * 
         * @summary ????
         * @param {CaptureFilter} [body] 
         * @param {*} [options] Override http request option.
         * @throws {RequiredError}
         */
        startCapture: async (body?: CaptureFilter, options: AxiosRequestConfig = {}): Promise<RequestArgs> => {
            const localVarPath = `/api/system/network/capture/start`;
            // use dummy base URL string because the URL constructor only accepts absolute URLs.
            const localVarUrlObj = new URL(localVarPath, 'https://example.com');
            let baseOptions;
            if (configuration) {
                baseOptions = configuration.baseOptions;
            }
            const localVarRequestOptions :AxiosRequestConfig = { method: 'POST', ...baseOptions, ...options};
            const localVarHeaderParameter = {} as any;
            const localVarQueryParameter = {} as any;

            // authentication Bearer required
            // http bearer authentication required
            if (configuration && configuration.accessToken) {
                const accessToken = typeof configuration.accessToken === 'function'
                    ? await configuration.accessToken()
                    : await configuration.accessToken;
                localVarHeaderParameter["Authorization"] = "Bearer " + accessToken;
            }

            localVarHeaderParameter['Content-Type'] = 'application/json-patch+json';

            const query = new URLSearchParams(localVarUrlObj.search);
            for (const key in localVarQueryParameter) {
                query.set(key, localVarQueryParameter[key]);
            }
            for (const key in options.params) {
                query.set(key, options.params[key]);
            }
            localVarUrlObj.search = (new URLSearchParams(query)).toString();
            let headersFromBaseOptions = baseOptions && baseOptions.headers ? baseOptions.headers : {};
            localVarRequestOptions.headers = {...localVarHeaderParameter, ...headersFromBaseOptions, ...options.headers};
            const needsSerialization = (typeof body !== "string") || localVarRequestOptions.headers['Content-Type'] === 'application/json';
            localVarRequestOptions.data =  needsSerialization ? JSON.stringify(body !== undefined ? body : {}) : (body || "");

            return {
                url: localVarUrlObj.pathname + localVarUrlObj.search + localVarUrlObj.hash,
                options: localVarRequestOptions,
            };
        },
        /**
         * 
         * @summary ????
         * @param {*} [options] Override http request option.
         * @throws {RequiredError}
         */
        stopCapture: async (options: AxiosRequestConfig = {}): Promise<RequestArgs> => {
            const localVarPath = `/api/system/network/capture/stop`;
            // use dummy base URL string because the URL constructor only accepts absolute URLs.
            const localVarUrlObj = new URL(localVarPath, 'https://example.com');
            let baseOptions;
            if (configuration) {
                baseOptions = configuration.baseOptions;
            }
            const localVarRequestOptions :AxiosRequestConfig = { method: 'POST', ...baseOptions, ...options};
            const localVarHeaderParameter = {} as any;
            const localVarQueryParameter = {} as any;

            // authentication Bearer required
            // http bearer authentication required
            if (configuration && configuration.accessToken) {
                const accessToken = typeof configuration.accessToken === 'function'
                    ? await configuration.accessToken()
                    : await configuration.accessToken;
                localVarHeaderParameter["Authorization"] = "Bearer " + accessToken;
            }

            const query = new URLSearchParams(localVarUrlObj.search);
            for (const key in localVarQueryParameter) {
                query.set(key, localVarQueryParameter[key]);
            }
            for (const key in options.params) {
                query.set(key, options.params[key]);
            }
            localVarUrlObj.search = (new URLSearchParams(query)).toString();
            let headersFromBaseOptions = baseOptions && baseOptions.headers ? baseOptions.headers : {};
            localVarRequestOptions.headers = {...localVarHeaderParameter, ...headersFromBaseOptions, ...options.headers};

            return {
                url: localVarUrlObj.pathname + localVarUrlObj.search + localVarUrlObj.hash,
                options: localVarRequestOptions,
            };
        },
    }
};

/**
 * PacketCaptureApi - functional programming interface
 * @export
 */
export const PacketCaptureApiFp = function(configuration?: Configuration) {
    return {
        /**
         * 
         * @summary ??????
         * @param {*} [options] Override http request option.
         * @throws {RequiredError}
         */
        async clearPackets(options?: AxiosRequestConfig): Promise<(axios?: AxiosInstance, basePath?: string) => Promise<AxiosResponse<void>>> {
            const localVarAxiosArgs = await PacketCaptureApiAxiosParamCreator(configuration).clearPackets(options);
            return (axios: AxiosInstance = globalAxios, basePath: string = BASE_PATH) => {
                const axiosRequestArgs :AxiosRequestConfig = {...localVarAxiosArgs.options, url: basePath + localVarAxiosArgs.url};
                return axios.request(axiosRequestArgs);
            };
        },
        /**
         * 
         * @summary ????????
         * @param {*} [options] Override http request option.
         * @throws {RequiredError}
         */
        async getInterfaces(options?: AxiosRequestConfig): Promise<(axios?: AxiosInstance, basePath?: string) => Promise<AxiosResponse<RESTfulResultListObject>>> {
            const localVarAxiosArgs = await PacketCaptureApiAxiosParamCreator(configuration).getInterfaces(options);
            return (axios: AxiosInstance = globalAxios, basePath: string = BASE_PATH) => {
                const axiosRequestArgs :AxiosRequestConfig = {...localVarAxiosArgs.options, url: basePath + localVarAxiosArgs.url};
                return axios.request(axiosRequestArgs);
            };
        },
        /**
         * 
         * @summary ??????
         * @param {number} [start] 
         * @param {number} [count] 
         * @param {*} [options] Override http request option.
         * @throws {RequiredError}
         */
        async getPackets(start?: number, count?: number, options?: AxiosRequestConfig): Promise<(axios?: AxiosInstance, basePath?: string) => Promise<AxiosResponse<RESTfulResultListPacketInfo>>> {
            const localVarAxiosArgs = await PacketCaptureApiAxiosParamCreator(configuration).getPackets(start, count, options);
            return (axios: AxiosInstance = globalAxios, basePath: string = BASE_PATH) => {
                const axiosRequestArgs :AxiosRequestConfig = {...localVarAxiosArgs.options, url: basePath + localVarAxiosArgs.url};
                return axios.request(axiosRequestArgs);
            };
        },
        /**
         * 
         * @summary ??????
         * @param {*} [options] Override http request option.
         * @throws {RequiredError}
         */
        async getStatus(options?: AxiosRequestConfig): Promise<(axios?: AxiosInstance, basePath?: string) => Promise<AxiosResponse<RESTfulResultObject>>> {
            const localVarAxiosArgs = await PacketCaptureApiAxiosParamCreator(configuration).getStatus(options);
            return (axios: AxiosInstance = globalAxios, basePath: string = BASE_PATH) => {
                const axiosRequestArgs :AxiosRequestConfig = {...localVarAxiosArgs.options, url: basePath + localVarAxiosArgs.url};
                return axios.request(axiosRequestArgs);
            };
        },
        /**
         * 
         * @summary ????
         * @param {CaptureFilter} [body] 
         * @param {*} [options] Override http request option.
         * @throws {RequiredError}
         */
        async startCapture(body?: CaptureFilter, options?: AxiosRequestConfig): Promise<(axios?: AxiosInstance, basePath?: string) => Promise<AxiosResponse<RESTfulResultBoolean>>> {
            const localVarAxiosArgs = await PacketCaptureApiAxiosParamCreator(configuration).startCapture(body, options);
            return (axios: AxiosInstance = globalAxios, basePath: string = BASE_PATH) => {
                const axiosRequestArgs :AxiosRequestConfig = {...localVarAxiosArgs.options, url: basePath + localVarAxiosArgs.url};
                return axios.request(axiosRequestArgs);
            };
        },
        /**
         * 
         * @summary ????
         * @param {*} [options] Override http request option.
         * @throws {RequiredError}
         */
        async stopCapture(options?: AxiosRequestConfig): Promise<(axios?: AxiosInstance, basePath?: string) => Promise<AxiosResponse<void>>> {
            const localVarAxiosArgs = await PacketCaptureApiAxiosParamCreator(configuration).stopCapture(options);
            return (axios: AxiosInstance = globalAxios, basePath: string = BASE_PATH) => {
                const axiosRequestArgs :AxiosRequestConfig = {...localVarAxiosArgs.options, url: basePath + localVarAxiosArgs.url};
                return axios.request(axiosRequestArgs);
            };
        },
    }
};

/**
 * PacketCaptureApi - factory interface
 * @export
 */
export const PacketCaptureApiFactory = function (configuration?: Configuration, basePath?: string, axios?: AxiosInstance) {
    return {
        /**
         * 
         * @summary ??????
         * @param {*} [options] Override http request option.
         * @throws {RequiredError}
         */
        async clearPackets(options?: AxiosRequestConfig): Promise<AxiosResponse<void>> {
            return PacketCaptureApiFp(configuration).clearPackets(options).then((request) => request(axios, basePath));
        },
        /**
         * 
         * @summary ????????
         * @param {*} [options] Override http request option.
         * @throws {RequiredError}
         */
        async getInterfaces(options?: AxiosRequestConfig): Promise<AxiosResponse<RESTfulResultListObject>> {
            return PacketCaptureApiFp(configuration).getInterfaces(options).then((request) => request(axios, basePath));
        },
        /**
         * 
         * @summary ??????
         * @param {number} [start] 
         * @param {number} [count] 
         * @param {*} [options] Override http request option.
         * @throws {RequiredError}
         */
        async getPackets(start?: number, count?: number, options?: AxiosRequestConfig): Promise<AxiosResponse<RESTfulResultListPacketInfo>> {
            return PacketCaptureApiFp(configuration).getPackets(start, count, options).then((request) => request(axios, basePath));
        },
        /**
         * 
         * @summary ??????
         * @param {*} [options] Override http request option.
         * @throws {RequiredError}
         */
        async getStatus(options?: AxiosRequestConfig): Promise<AxiosResponse<RESTfulResultObject>> {
            return PacketCaptureApiFp(configuration).getStatus(options).then((request) => request(axios, basePath));
        },
        /**
         * 
         * @summary ????
         * @param {CaptureFilter} [body] 
         * @param {*} [options] Override http request option.
         * @throws {RequiredError}
         */
        async startCapture(body?: CaptureFilter, options?: AxiosRequestConfig): Promise<AxiosResponse<RESTfulResultBoolean>> {
            return PacketCaptureApiFp(configuration).startCapture(body, options).then((request) => request(axios, basePath));
        },
        /**
         * 
         * @summary ????
         * @param {*} [options] Override http request option.
         * @throws {RequiredError}
         */
        async stopCapture(options?: AxiosRequestConfig): Promise<AxiosResponse<void>> {
            return PacketCaptureApiFp(configuration).stopCapture(options).then((request) => request(axios, basePath));
        },
    };
};

/**
 * PacketCaptureApi - object-oriented interface
 * @export
 * @class PacketCaptureApi
 * @extends {BaseAPI}
 */
export class PacketCaptureApi extends BaseAPI {
    /**
     * 
     * @summary ??????
     * @param {*} [options] Override http request option.
     * @throws {RequiredError}
     * @memberof PacketCaptureApi
     */
    public async clearPackets(options?: AxiosRequestConfig) : Promise<AxiosResponse<void>> {
        return PacketCaptureApiFp(this.configuration).clearPackets(options).then((request) => request(this.axios, this.basePath));
    }
    /**
     * 
     * @summary ????????
     * @param {*} [options] Override http request option.
     * @throws {RequiredError}
     * @memberof PacketCaptureApi
     */
    public async getInterfaces(options?: AxiosRequestConfig) : Promise<AxiosResponse<RESTfulResultListObject>> {
        return PacketCaptureApiFp(this.configuration).getInterfaces(options).then((request) => request(this.axios, this.basePath));
    }
    /**
     * 
     * @summary ??????
     * @param {number} [start] 
     * @param {number} [count] 
     * @param {*} [options] Override http request option.
     * @throws {RequiredError}
     * @memberof PacketCaptureApi
     */
    public async getPackets(start?: number, count?: number, options?: AxiosRequestConfig) : Promise<AxiosResponse<RESTfulResultListPacketInfo>> {
        return PacketCaptureApiFp(this.configuration).getPackets(start, count, options).then((request) => request(this.axios, this.basePath));
    }
    /**
     * 
     * @summary ??????
     * @param {*} [options] Override http request option.
     * @throws {RequiredError}
     * @memberof PacketCaptureApi
     */
    public async getStatus(options?: AxiosRequestConfig) : Promise<AxiosResponse<RESTfulResultObject>> {
        return PacketCaptureApiFp(this.configuration).getStatus(options).then((request) => request(this.axios, this.basePath));
    }
    /**
     * 
     * @summary ????
     * @param {CaptureFilter} [body] 
     * @param {*} [options] Override http request option.
     * @throws {RequiredError}
     * @memberof PacketCaptureApi
     */
    public async startCapture(body?: CaptureFilter, options?: AxiosRequestConfig) : Promise<AxiosResponse<RESTfulResultBoolean>> {
        return PacketCaptureApiFp(this.configuration).startCapture(body, options).then((request) => request(this.axios, this.basePath));
    }
    /**
     * 
     * @summary ????
     * @param {*} [options] Override http request option.
     * @throws {RequiredError}
     * @memberof PacketCaptureApi
     */
    public async stopCapture(options?: AxiosRequestConfig) : Promise<AxiosResponse<void>> {
        return PacketCaptureApiFp(this.configuration).stopCapture(options).then((request) => request(this.axios, this.basePath));
    }
}
