/* tslint:disable */
/* eslint-disable */
/**
 * All Groups
 * No description provided (generated by Swagger Codegen https://github.com/swagger-api/swagger-codegen)
 *
 * OpenAPI spec version: 1.0.0
 * 
 *
 * NOTE: This class is auto generated by the swagger code generator program.
 * https://github.com/swagger-api/swagger-codegen.git
 * Do not edit the class manually.
 */

import globalAxios, { AxiosResponse, AxiosInstance, AxiosRequestConfig } from 'axios';
import { Configuration } from '../configuration';
// Some imports not used depending on template conditions
// @ts-ignore
import { BASE_PATH, COLLECTION_FORMATS, RequestArgs, BaseAPI, RequiredError } from '../base';
import { GetLatestBatchInput } from '../models';
import { GetLatestInput } from '../models';
import { GetQuerySuggestionsInput } from '../models';
import { RESTfulResultBoolean } from '../models';
import { RESTfulResultIDictionaryStringTimeSeriesData } from '../models';
import { RESTfulResultIEnumerableTimeSeriesData } from '../models';
import { RESTfulResultPagedResultTimeSeriesData } from '../models';
import { RESTfulResultPerformanceReport } from '../models';
import { RESTfulResultQuerySuggestion } from '../models';
import { RESTfulResultStorageStatus } from '../models';
import { RESTfulResultTimeSeriesData } from '../models';
import { RESTfulResultTimeSeriesStatistics } from '../models';
import { TimeSeriesDeleteRequest } from '../models';
import { TimeSeriesPagedQueryRequest } from '../models';
import { TimeSeriesQueryRequest } from '../models';
import { TimeSeriesStatisticsRequest } from '../models';
/**
 * TimeSeriesApi - axios parameter creator
 * @export
 */
export const TimeSeriesApiAxiosParamCreator = function (configuration?: Configuration) {
    return {
        /**
         * 
         * @summary ??????
         * @param {TimeSeriesDeleteRequest} [body] 
         * @param {*} [options] Override http request option.
         * @throws {RequiredError}
         */
        deleteTimeSeries: async (body?: TimeSeriesDeleteRequest, options: AxiosRequestConfig = {}): Promise<RequestArgs> => {
            const localVarPath = `/api/timeSeries/timeSeries`;
            // use dummy base URL string because the URL constructor only accepts absolute URLs.
            const localVarUrlObj = new URL(localVarPath, 'https://example.com');
            let baseOptions;
            if (configuration) {
                baseOptions = configuration.baseOptions;
            }
            const localVarRequestOptions :AxiosRequestConfig = { method: 'DELETE', ...baseOptions, ...options};
            const localVarHeaderParameter = {} as any;
            const localVarQueryParameter = {} as any;

            // authentication Bearer required
            // http bearer authentication required
            if (configuration && configuration.accessToken) {
                const accessToken = typeof configuration.accessToken === 'function'
                    ? await configuration.accessToken()
                    : await configuration.accessToken;
                localVarHeaderParameter["Authorization"] = "Bearer " + accessToken;
            }

            localVarHeaderParameter['Content-Type'] = 'application/json-patch+json';

            const query = new URLSearchParams(localVarUrlObj.search);
            for (const key in localVarQueryParameter) {
                query.set(key, localVarQueryParameter[key]);
            }
            for (const key in options.params) {
                query.set(key, options.params[key]);
            }
            localVarUrlObj.search = (new URLSearchParams(query)).toString();
            let headersFromBaseOptions = baseOptions && baseOptions.headers ? baseOptions.headers : {};
            localVarRequestOptions.headers = {...localVarHeaderParameter, ...headersFromBaseOptions, ...options.headers};
            const needsSerialization = (typeof body !== "string") || localVarRequestOptions.headers['Content-Type'] === 'application/json';
            localVarRequestOptions.data =  needsSerialization ? JSON.stringify(body !== undefined ? body : {}) : (body || "");

            return {
                url: localVarUrlObj.pathname + localVarUrlObj.search + localVarUrlObj.hash,
                options: localVarRequestOptions,
            };
        },
        /**
         * 
         * @summary ??????
         * @param {string} deviceId 
         * @param {GetLatestInput} [body] 
         * @param {*} [options] Override http request option.
         * @throws {RequiredError}
         */
        getLatest: async (deviceId: string, body?: GetLatestInput, options: AxiosRequestConfig = {}): Promise<RequestArgs> => {
            // verify required parameter 'deviceId' is not null or undefined
            if (deviceId === null || deviceId === undefined) {
                throw new RequiredError('deviceId','Required parameter deviceId was null or undefined when calling getLatest.');
            }
            const localVarPath = `/api/timeSeries/latest/{deviceId}`
                .replace(`{${"deviceId"}}`, encodeURIComponent(String(deviceId)));
            // use dummy base URL string because the URL constructor only accepts absolute URLs.
            const localVarUrlObj = new URL(localVarPath, 'https://example.com');
            let baseOptions;
            if (configuration) {
                baseOptions = configuration.baseOptions;
            }
            const localVarRequestOptions :AxiosRequestConfig = { method: 'GET', ...baseOptions, ...options};
            const localVarHeaderParameter = {} as any;
            const localVarQueryParameter = {} as any;

            // authentication Bearer required
            // http bearer authentication required
            if (configuration && configuration.accessToken) {
                const accessToken = typeof configuration.accessToken === 'function'
                    ? await configuration.accessToken()
                    : await configuration.accessToken;
                localVarHeaderParameter["Authorization"] = "Bearer " + accessToken;
            }

            localVarHeaderParameter['Content-Type'] = 'application/json-patch+json';

            const query = new URLSearchParams(localVarUrlObj.search);
            for (const key in localVarQueryParameter) {
                query.set(key, localVarQueryParameter[key]);
            }
            for (const key in options.params) {
                query.set(key, options.params[key]);
            }
            localVarUrlObj.search = (new URLSearchParams(query)).toString();
            let headersFromBaseOptions = baseOptions && baseOptions.headers ? baseOptions.headers : {};
            localVarRequestOptions.headers = {...localVarHeaderParameter, ...headersFromBaseOptions, ...options.headers};
            const needsSerialization = (typeof body !== "string") || localVarRequestOptions.headers['Content-Type'] === 'application/json';
            localVarRequestOptions.data =  needsSerialization ? JSON.stringify(body !== undefined ? body : {}) : (body || "");

            return {
                url: localVarUrlObj.pathname + localVarUrlObj.search + localVarUrlObj.hash,
                options: localVarRequestOptions,
            };
        },
        /**
         * 
         * @summary ????????
         * @param {GetLatestBatchInput} [body] 
         * @param {*} [options] Override http request option.
         * @throws {RequiredError}
         */
        getLatestBatch: async (body?: GetLatestBatchInput, options: AxiosRequestConfig = {}): Promise<RequestArgs> => {
            const localVarPath = `/api/timeSeries/latest/batch`;
            // use dummy base URL string because the URL constructor only accepts absolute URLs.
            const localVarUrlObj = new URL(localVarPath, 'https://example.com');
            let baseOptions;
            if (configuration) {
                baseOptions = configuration.baseOptions;
            }
            const localVarRequestOptions :AxiosRequestConfig = { method: 'POST', ...baseOptions, ...options};
            const localVarHeaderParameter = {} as any;
            const localVarQueryParameter = {} as any;

            // authentication Bearer required
            // http bearer authentication required
            if (configuration && configuration.accessToken) {
                const accessToken = typeof configuration.accessToken === 'function'
                    ? await configuration.accessToken()
                    : await configuration.accessToken;
                localVarHeaderParameter["Authorization"] = "Bearer " + accessToken;
            }

            localVarHeaderParameter['Content-Type'] = 'application/json-patch+json';

            const query = new URLSearchParams(localVarUrlObj.search);
            for (const key in localVarQueryParameter) {
                query.set(key, localVarQueryParameter[key]);
            }
            for (const key in options.params) {
                query.set(key, options.params[key]);
            }
            localVarUrlObj.search = (new URLSearchParams(query)).toString();
            let headersFromBaseOptions = baseOptions && baseOptions.headers ? baseOptions.headers : {};
            localVarRequestOptions.headers = {...localVarHeaderParameter, ...headersFromBaseOptions, ...options.headers};
            const needsSerialization = (typeof body !== "string") || localVarRequestOptions.headers['Content-Type'] === 'application/json';
            localVarRequestOptions.data =  needsSerialization ? JSON.stringify(body !== undefined ? body : {}) : (body || "");

            return {
                url: localVarUrlObj.pathname + localVarUrlObj.search + localVarUrlObj.hash,
                options: localVarRequestOptions,
            };
        },
        /**
         * 
         * @summary ??????
         * @param {*} [options] Override http request option.
         * @throws {RequiredError}
         */
        getPerformanceReport: async (options: AxiosRequestConfig = {}): Promise<RequestArgs> => {
            const localVarPath = `/api/timeSeries/monitor/performance`;
            // use dummy base URL string because the URL constructor only accepts absolute URLs.
            const localVarUrlObj = new URL(localVarPath, 'https://example.com');
            let baseOptions;
            if (configuration) {
                baseOptions = configuration.baseOptions;
            }
            const localVarRequestOptions :AxiosRequestConfig = { method: 'GET', ...baseOptions, ...options};
            const localVarHeaderParameter = {} as any;
            const localVarQueryParameter = {} as any;

            // authentication Bearer required
            // http bearer authentication required
            if (configuration && configuration.accessToken) {
                const accessToken = typeof configuration.accessToken === 'function'
                    ? await configuration.accessToken()
                    : await configuration.accessToken;
                localVarHeaderParameter["Authorization"] = "Bearer " + accessToken;
            }

            const query = new URLSearchParams(localVarUrlObj.search);
            for (const key in localVarQueryParameter) {
                query.set(key, localVarQueryParameter[key]);
            }
            for (const key in options.params) {
                query.set(key, options.params[key]);
            }
            localVarUrlObj.search = (new URLSearchParams(query)).toString();
            let headersFromBaseOptions = baseOptions && baseOptions.headers ? baseOptions.headers : {};
            localVarRequestOptions.headers = {...localVarHeaderParameter, ...headersFromBaseOptions, ...options.headers};

            return {
                url: localVarUrlObj.pathname + localVarUrlObj.search + localVarUrlObj.hash,
                options: localVarRequestOptions,
            };
        },
        /**
         * 
         * @summary ??????
         * @param {string} deviceId 
         * @param {GetQuerySuggestionsInput} [body] 
         * @param {*} [options] Override http request option.
         * @throws {RequiredError}
         */
        getQuerySuggestions: async (deviceId: string, body?: GetQuerySuggestionsInput, options: AxiosRequestConfig = {}): Promise<RequestArgs> => {
            // verify required parameter 'deviceId' is not null or undefined
            if (deviceId === null || deviceId === undefined) {
                throw new RequiredError('deviceId','Required parameter deviceId was null or undefined when calling getQuerySuggestions.');
            }
            const localVarPath = `/api/timeSeries/monitor/suggestions/{deviceId}`
                .replace(`{${"deviceId"}}`, encodeURIComponent(String(deviceId)));
            // use dummy base URL string because the URL constructor only accepts absolute URLs.
            const localVarUrlObj = new URL(localVarPath, 'https://example.com');
            let baseOptions;
            if (configuration) {
                baseOptions = configuration.baseOptions;
            }
            const localVarRequestOptions :AxiosRequestConfig = { method: 'GET', ...baseOptions, ...options};
            const localVarHeaderParameter = {} as any;
            const localVarQueryParameter = {} as any;

            // authentication Bearer required
            // http bearer authentication required
            if (configuration && configuration.accessToken) {
                const accessToken = typeof configuration.accessToken === 'function'
                    ? await configuration.accessToken()
                    : await configuration.accessToken;
                localVarHeaderParameter["Authorization"] = "Bearer " + accessToken;
            }

            localVarHeaderParameter['Content-Type'] = 'application/json-patch+json';

            const query = new URLSearchParams(localVarUrlObj.search);
            for (const key in localVarQueryParameter) {
                query.set(key, localVarQueryParameter[key]);
            }
            for (const key in options.params) {
                query.set(key, options.params[key]);
            }
            localVarUrlObj.search = (new URLSearchParams(query)).toString();
            let headersFromBaseOptions = baseOptions && baseOptions.headers ? baseOptions.headers : {};
            localVarRequestOptions.headers = {...localVarHeaderParameter, ...headersFromBaseOptions, ...options.headers};
            const needsSerialization = (typeof body !== "string") || localVarRequestOptions.headers['Content-Type'] === 'application/json';
            localVarRequestOptions.data =  needsSerialization ? JSON.stringify(body !== undefined ? body : {}) : (body || "");

            return {
                url: localVarUrlObj.pathname + localVarUrlObj.search + localVarUrlObj.hash,
                options: localVarRequestOptions,
            };
        },
        /**
         * 
         * @summary ????????
         * @param {TimeSeriesStatisticsRequest} [body] 
         * @param {*} [options] Override http request option.
         * @throws {RequiredError}
         */
        getStatistics: async (body?: TimeSeriesStatisticsRequest, options: AxiosRequestConfig = {}): Promise<RequestArgs> => {
            const localVarPath = `/api/timeSeries/statistics`;
            // use dummy base URL string because the URL constructor only accepts absolute URLs.
            const localVarUrlObj = new URL(localVarPath, 'https://example.com');
            let baseOptions;
            if (configuration) {
                baseOptions = configuration.baseOptions;
            }
            const localVarRequestOptions :AxiosRequestConfig = { method: 'POST', ...baseOptions, ...options};
            const localVarHeaderParameter = {} as any;
            const localVarQueryParameter = {} as any;

            // authentication Bearer required
            // http bearer authentication required
            if (configuration && configuration.accessToken) {
                const accessToken = typeof configuration.accessToken === 'function'
                    ? await configuration.accessToken()
                    : await configuration.accessToken;
                localVarHeaderParameter["Authorization"] = "Bearer " + accessToken;
            }

            localVarHeaderParameter['Content-Type'] = 'application/json-patch+json';

            const query = new URLSearchParams(localVarUrlObj.search);
            for (const key in localVarQueryParameter) {
                query.set(key, localVarQueryParameter[key]);
            }
            for (const key in options.params) {
                query.set(key, options.params[key]);
            }
            localVarUrlObj.search = (new URLSearchParams(query)).toString();
            let headersFromBaseOptions = baseOptions && baseOptions.headers ? baseOptions.headers : {};
            localVarRequestOptions.headers = {...localVarHeaderParameter, ...headersFromBaseOptions, ...options.headers};
            const needsSerialization = (typeof body !== "string") || localVarRequestOptions.headers['Content-Type'] === 'application/json';
            localVarRequestOptions.data =  needsSerialization ? JSON.stringify(body !== undefined ? body : {}) : (body || "");

            return {
                url: localVarUrlObj.pathname + localVarUrlObj.search + localVarUrlObj.hash,
                options: localVarRequestOptions,
            };
        },
        /**
         * 
         * @summary ??????
         * @param {*} [options] Override http request option.
         * @throws {RequiredError}
         */
        getStorageStatus: async (options: AxiosRequestConfig = {}): Promise<RequestArgs> => {
            const localVarPath = `/api/timeSeries/status`;
            // use dummy base URL string because the URL constructor only accepts absolute URLs.
            const localVarUrlObj = new URL(localVarPath, 'https://example.com');
            let baseOptions;
            if (configuration) {
                baseOptions = configuration.baseOptions;
            }
            const localVarRequestOptions :AxiosRequestConfig = { method: 'GET', ...baseOptions, ...options};
            const localVarHeaderParameter = {} as any;
            const localVarQueryParameter = {} as any;

            // authentication Bearer required
            // http bearer authentication required
            if (configuration && configuration.accessToken) {
                const accessToken = typeof configuration.accessToken === 'function'
                    ? await configuration.accessToken()
                    : await configuration.accessToken;
                localVarHeaderParameter["Authorization"] = "Bearer " + accessToken;
            }

            const query = new URLSearchParams(localVarUrlObj.search);
            for (const key in localVarQueryParameter) {
                query.set(key, localVarQueryParameter[key]);
            }
            for (const key in options.params) {
                query.set(key, options.params[key]);
            }
            localVarUrlObj.search = (new URLSearchParams(query)).toString();
            let headersFromBaseOptions = baseOptions && baseOptions.headers ? baseOptions.headers : {};
            localVarRequestOptions.headers = {...localVarHeaderParameter, ...headersFromBaseOptions, ...options.headers};

            return {
                url: localVarUrlObj.pathname + localVarUrlObj.search + localVarUrlObj.hash,
                options: localVarRequestOptions,
            };
        },
        /**
         * 
         * @summary ????????
         * @param {TimeSeriesPagedQueryRequest} [body] 
         * @param {*} [options] Override http request option.
         * @throws {RequiredError}
         */
        getTimeSeriesPage: async (body?: TimeSeriesPagedQueryRequest, options: AxiosRequestConfig = {}): Promise<RequestArgs> => {
            const localVarPath = `/api/timeSeries/page`;
            // use dummy base URL string because the URL constructor only accepts absolute URLs.
            const localVarUrlObj = new URL(localVarPath, 'https://example.com');
            let baseOptions;
            if (configuration) {
                baseOptions = configuration.baseOptions;
            }
            const localVarRequestOptions :AxiosRequestConfig = { method: 'POST', ...baseOptions, ...options};
            const localVarHeaderParameter = {} as any;
            const localVarQueryParameter = {} as any;

            // authentication Bearer required
            // http bearer authentication required
            if (configuration && configuration.accessToken) {
                const accessToken = typeof configuration.accessToken === 'function'
                    ? await configuration.accessToken()
                    : await configuration.accessToken;
                localVarHeaderParameter["Authorization"] = "Bearer " + accessToken;
            }

            localVarHeaderParameter['Content-Type'] = 'application/json-patch+json';

            const query = new URLSearchParams(localVarUrlObj.search);
            for (const key in localVarQueryParameter) {
                query.set(key, localVarQueryParameter[key]);
            }
            for (const key in options.params) {
                query.set(key, options.params[key]);
            }
            localVarUrlObj.search = (new URLSearchParams(query)).toString();
            let headersFromBaseOptions = baseOptions && baseOptions.headers ? baseOptions.headers : {};
            localVarRequestOptions.headers = {...localVarHeaderParameter, ...headersFromBaseOptions, ...options.headers};
            const needsSerialization = (typeof body !== "string") || localVarRequestOptions.headers['Content-Type'] === 'application/json';
            localVarRequestOptions.data =  needsSerialization ? JSON.stringify(body !== undefined ? body : {}) : (body || "");

            return {
                url: localVarUrlObj.pathname + localVarUrlObj.search + localVarUrlObj.hash,
                options: localVarRequestOptions,
            };
        },
        /**
         * 
         * @summary ??????
         * @param {TimeSeriesQueryRequest} [body] 
         * @param {*} [options] Override http request option.
         * @throws {RequiredError}
         */
        queryTimeSeries: async (body?: TimeSeriesQueryRequest, options: AxiosRequestConfig = {}): Promise<RequestArgs> => {
            const localVarPath = `/api/timeSeries/query`;
            // use dummy base URL string because the URL constructor only accepts absolute URLs.
            const localVarUrlObj = new URL(localVarPath, 'https://example.com');
            let baseOptions;
            if (configuration) {
                baseOptions = configuration.baseOptions;
            }
            const localVarRequestOptions :AxiosRequestConfig = { method: 'POST', ...baseOptions, ...options};
            const localVarHeaderParameter = {} as any;
            const localVarQueryParameter = {} as any;

            // authentication Bearer required
            // http bearer authentication required
            if (configuration && configuration.accessToken) {
                const accessToken = typeof configuration.accessToken === 'function'
                    ? await configuration.accessToken()
                    : await configuration.accessToken;
                localVarHeaderParameter["Authorization"] = "Bearer " + accessToken;
            }

            localVarHeaderParameter['Content-Type'] = 'application/json-patch+json';

            const query = new URLSearchParams(localVarUrlObj.search);
            for (const key in localVarQueryParameter) {
                query.set(key, localVarQueryParameter[key]);
            }
            for (const key in options.params) {
                query.set(key, options.params[key]);
            }
            localVarUrlObj.search = (new URLSearchParams(query)).toString();
            let headersFromBaseOptions = baseOptions && baseOptions.headers ? baseOptions.headers : {};
            localVarRequestOptions.headers = {...localVarHeaderParameter, ...headersFromBaseOptions, ...options.headers};
            const needsSerialization = (typeof body !== "string") || localVarRequestOptions.headers['Content-Type'] === 'application/json';
            localVarRequestOptions.data =  needsSerialization ? JSON.stringify(body !== undefined ? body : {}) : (body || "");

            return {
                url: localVarUrlObj.pathname + localVarUrlObj.search + localVarUrlObj.hash,
                options: localVarRequestOptions,
            };
        },
    }
};

/**
 * TimeSeriesApi - functional programming interface
 * @export
 */
export const TimeSeriesApiFp = function(configuration?: Configuration) {
    return {
        /**
         * 
         * @summary ??????
         * @param {TimeSeriesDeleteRequest} [body] 
         * @param {*} [options] Override http request option.
         * @throws {RequiredError}
         */
        async deleteTimeSeries(body?: TimeSeriesDeleteRequest, options?: AxiosRequestConfig): Promise<(axios?: AxiosInstance, basePath?: string) => Promise<AxiosResponse<RESTfulResultBoolean>>> {
            const localVarAxiosArgs = await TimeSeriesApiAxiosParamCreator(configuration).deleteTimeSeries(body, options);
            return (axios: AxiosInstance = globalAxios, basePath: string = BASE_PATH) => {
                const axiosRequestArgs :AxiosRequestConfig = {...localVarAxiosArgs.options, url: basePath + localVarAxiosArgs.url};
                return axios.request(axiosRequestArgs);
            };
        },
        /**
         * 
         * @summary ??????
         * @param {string} deviceId 
         * @param {GetLatestInput} [body] 
         * @param {*} [options] Override http request option.
         * @throws {RequiredError}
         */
        async getLatest(deviceId: string, body?: GetLatestInput, options?: AxiosRequestConfig): Promise<(axios?: AxiosInstance, basePath?: string) => Promise<AxiosResponse<RESTfulResultTimeSeriesData>>> {
            const localVarAxiosArgs = await TimeSeriesApiAxiosParamCreator(configuration).getLatest(deviceId, body, options);
            return (axios: AxiosInstance = globalAxios, basePath: string = BASE_PATH) => {
                const axiosRequestArgs :AxiosRequestConfig = {...localVarAxiosArgs.options, url: basePath + localVarAxiosArgs.url};
                return axios.request(axiosRequestArgs);
            };
        },
        /**
         * 
         * @summary ????????
         * @param {GetLatestBatchInput} [body] 
         * @param {*} [options] Override http request option.
         * @throws {RequiredError}
         */
        async getLatestBatch(body?: GetLatestBatchInput, options?: AxiosRequestConfig): Promise<(axios?: AxiosInstance, basePath?: string) => Promise<AxiosResponse<RESTfulResultIDictionaryStringTimeSeriesData>>> {
            const localVarAxiosArgs = await TimeSeriesApiAxiosParamCreator(configuration).getLatestBatch(body, options);
            return (axios: AxiosInstance = globalAxios, basePath: string = BASE_PATH) => {
                const axiosRequestArgs :AxiosRequestConfig = {...localVarAxiosArgs.options, url: basePath + localVarAxiosArgs.url};
                return axios.request(axiosRequestArgs);
            };
        },
        /**
         * 
         * @summary ??????
         * @param {*} [options] Override http request option.
         * @throws {RequiredError}
         */
        async getPerformanceReport(options?: AxiosRequestConfig): Promise<(axios?: AxiosInstance, basePath?: string) => Promise<AxiosResponse<RESTfulResultPerformanceReport>>> {
            const localVarAxiosArgs = await TimeSeriesApiAxiosParamCreator(configuration).getPerformanceReport(options);
            return (axios: AxiosInstance = globalAxios, basePath: string = BASE_PATH) => {
                const axiosRequestArgs :AxiosRequestConfig = {...localVarAxiosArgs.options, url: basePath + localVarAxiosArgs.url};
                return axios.request(axiosRequestArgs);
            };
        },
        /**
         * 
         * @summary ??????
         * @param {string} deviceId 
         * @param {GetQuerySuggestionsInput} [body] 
         * @param {*} [options] Override http request option.
         * @throws {RequiredError}
         */
        async getQuerySuggestions(deviceId: string, body?: GetQuerySuggestionsInput, options?: AxiosRequestConfig): Promise<(axios?: AxiosInstance, basePath?: string) => Promise<AxiosResponse<RESTfulResultQuerySuggestion>>> {
            const localVarAxiosArgs = await TimeSeriesApiAxiosParamCreator(configuration).getQuerySuggestions(deviceId, body, options);
            return (axios: AxiosInstance = globalAxios, basePath: string = BASE_PATH) => {
                const axiosRequestArgs :AxiosRequestConfig = {...localVarAxiosArgs.options, url: basePath + localVarAxiosArgs.url};
                return axios.request(axiosRequestArgs);
            };
        },
        /**
         * 
         * @summary ????????
         * @param {TimeSeriesStatisticsRequest} [body] 
         * @param {*} [options] Override http request option.
         * @throws {RequiredError}
         */
        async getStatistics(body?: TimeSeriesStatisticsRequest, options?: AxiosRequestConfig): Promise<(axios?: AxiosInstance, basePath?: string) => Promise<AxiosResponse<RESTfulResultTimeSeriesStatistics>>> {
            const localVarAxiosArgs = await TimeSeriesApiAxiosParamCreator(configuration).getStatistics(body, options);
            return (axios: AxiosInstance = globalAxios, basePath: string = BASE_PATH) => {
                const axiosRequestArgs :AxiosRequestConfig = {...localVarAxiosArgs.options, url: basePath + localVarAxiosArgs.url};
                return axios.request(axiosRequestArgs);
            };
        },
        /**
         * 
         * @summary ??????
         * @param {*} [options] Override http request option.
         * @throws {RequiredError}
         */
        async getStorageStatus(options?: AxiosRequestConfig): Promise<(axios?: AxiosInstance, basePath?: string) => Promise<AxiosResponse<RESTfulResultStorageStatus>>> {
            const localVarAxiosArgs = await TimeSeriesApiAxiosParamCreator(configuration).getStorageStatus(options);
            return (axios: AxiosInstance = globalAxios, basePath: string = BASE_PATH) => {
                const axiosRequestArgs :AxiosRequestConfig = {...localVarAxiosArgs.options, url: basePath + localVarAxiosArgs.url};
                return axios.request(axiosRequestArgs);
            };
        },
        /**
         * 
         * @summary ????????
         * @param {TimeSeriesPagedQueryRequest} [body] 
         * @param {*} [options] Override http request option.
         * @throws {RequiredError}
         */
        async getTimeSeriesPage(body?: TimeSeriesPagedQueryRequest, options?: AxiosRequestConfig): Promise<(axios?: AxiosInstance, basePath?: string) => Promise<AxiosResponse<RESTfulResultPagedResultTimeSeriesData>>> {
            const localVarAxiosArgs = await TimeSeriesApiAxiosParamCreator(configuration).getTimeSeriesPage(body, options);
            return (axios: AxiosInstance = globalAxios, basePath: string = BASE_PATH) => {
                const axiosRequestArgs :AxiosRequestConfig = {...localVarAxiosArgs.options, url: basePath + localVarAxiosArgs.url};
                return axios.request(axiosRequestArgs);
            };
        },
        /**
         * 
         * @summary ??????
         * @param {TimeSeriesQueryRequest} [body] 
         * @param {*} [options] Override http request option.
         * @throws {RequiredError}
         */
        async queryTimeSeries(body?: TimeSeriesQueryRequest, options?: AxiosRequestConfig): Promise<(axios?: AxiosInstance, basePath?: string) => Promise<AxiosResponse<RESTfulResultIEnumerableTimeSeriesData>>> {
            const localVarAxiosArgs = await TimeSeriesApiAxiosParamCreator(configuration).queryTimeSeries(body, options);
            return (axios: AxiosInstance = globalAxios, basePath: string = BASE_PATH) => {
                const axiosRequestArgs :AxiosRequestConfig = {...localVarAxiosArgs.options, url: basePath + localVarAxiosArgs.url};
                return axios.request(axiosRequestArgs);
            };
        },
    }
};

/**
 * TimeSeriesApi - factory interface
 * @export
 */
export const TimeSeriesApiFactory = function (configuration?: Configuration, basePath?: string, axios?: AxiosInstance) {
    return {
        /**
         * 
         * @summary ??????
         * @param {TimeSeriesDeleteRequest} [body] 
         * @param {*} [options] Override http request option.
         * @throws {RequiredError}
         */
        async deleteTimeSeries(body?: TimeSeriesDeleteRequest, options?: AxiosRequestConfig): Promise<AxiosResponse<RESTfulResultBoolean>> {
            return TimeSeriesApiFp(configuration).deleteTimeSeries(body, options).then((request) => request(axios, basePath));
        },
        /**
         * 
         * @summary ??????
         * @param {string} deviceId 
         * @param {GetLatestInput} [body] 
         * @param {*} [options] Override http request option.
         * @throws {RequiredError}
         */
        async getLatest(deviceId: string, body?: GetLatestInput, options?: AxiosRequestConfig): Promise<AxiosResponse<RESTfulResultTimeSeriesData>> {
            return TimeSeriesApiFp(configuration).getLatest(deviceId, body, options).then((request) => request(axios, basePath));
        },
        /**
         * 
         * @summary ????????
         * @param {GetLatestBatchInput} [body] 
         * @param {*} [options] Override http request option.
         * @throws {RequiredError}
         */
        async getLatestBatch(body?: GetLatestBatchInput, options?: AxiosRequestConfig): Promise<AxiosResponse<RESTfulResultIDictionaryStringTimeSeriesData>> {
            return TimeSeriesApiFp(configuration).getLatestBatch(body, options).then((request) => request(axios, basePath));
        },
        /**
         * 
         * @summary ??????
         * @param {*} [options] Override http request option.
         * @throws {RequiredError}
         */
        async getPerformanceReport(options?: AxiosRequestConfig): Promise<AxiosResponse<RESTfulResultPerformanceReport>> {
            return TimeSeriesApiFp(configuration).getPerformanceReport(options).then((request) => request(axios, basePath));
        },
        /**
         * 
         * @summary ??????
         * @param {string} deviceId 
         * @param {GetQuerySuggestionsInput} [body] 
         * @param {*} [options] Override http request option.
         * @throws {RequiredError}
         */
        async getQuerySuggestions(deviceId: string, body?: GetQuerySuggestionsInput, options?: AxiosRequestConfig): Promise<AxiosResponse<RESTfulResultQuerySuggestion>> {
            return TimeSeriesApiFp(configuration).getQuerySuggestions(deviceId, body, options).then((request) => request(axios, basePath));
        },
        /**
         * 
         * @summary ????????
         * @param {TimeSeriesStatisticsRequest} [body] 
         * @param {*} [options] Override http request option.
         * @throws {RequiredError}
         */
        async getStatistics(body?: TimeSeriesStatisticsRequest, options?: AxiosRequestConfig): Promise<AxiosResponse<RESTfulResultTimeSeriesStatistics>> {
            return TimeSeriesApiFp(configuration).getStatistics(body, options).then((request) => request(axios, basePath));
        },
        /**
         * 
         * @summary ??????
         * @param {*} [options] Override http request option.
         * @throws {RequiredError}
         */
        async getStorageStatus(options?: AxiosRequestConfig): Promise<AxiosResponse<RESTfulResultStorageStatus>> {
            return TimeSeriesApiFp(configuration).getStorageStatus(options).then((request) => request(axios, basePath));
        },
        /**
         * 
         * @summary ????????
         * @param {TimeSeriesPagedQueryRequest} [body] 
         * @param {*} [options] Override http request option.
         * @throws {RequiredError}
         */
        async getTimeSeriesPage(body?: TimeSeriesPagedQueryRequest, options?: AxiosRequestConfig): Promise<AxiosResponse<RESTfulResultPagedResultTimeSeriesData>> {
            return TimeSeriesApiFp(configuration).getTimeSeriesPage(body, options).then((request) => request(axios, basePath));
        },
        /**
         * 
         * @summary ??????
         * @param {TimeSeriesQueryRequest} [body] 
         * @param {*} [options] Override http request option.
         * @throws {RequiredError}
         */
        async queryTimeSeries(body?: TimeSeriesQueryRequest, options?: AxiosRequestConfig): Promise<AxiosResponse<RESTfulResultIEnumerableTimeSeriesData>> {
            return TimeSeriesApiFp(configuration).queryTimeSeries(body, options).then((request) => request(axios, basePath));
        },
    };
};

/**
 * TimeSeriesApi - object-oriented interface
 * @export
 * @class TimeSeriesApi
 * @extends {BaseAPI}
 */
export class TimeSeriesApi extends BaseAPI {
    /**
     * 
     * @summary ??????
     * @param {TimeSeriesDeleteRequest} [body] 
     * @param {*} [options] Override http request option.
     * @throws {RequiredError}
     * @memberof TimeSeriesApi
     */
    public async deleteTimeSeries(body?: TimeSeriesDeleteRequest, options?: AxiosRequestConfig) : Promise<AxiosResponse<RESTfulResultBoolean>> {
        return TimeSeriesApiFp(this.configuration).deleteTimeSeries(body, options).then((request) => request(this.axios, this.basePath));
    }
    /**
     * 
     * @summary ??????
     * @param {string} deviceId 
     * @param {GetLatestInput} [body] 
     * @param {*} [options] Override http request option.
     * @throws {RequiredError}
     * @memberof TimeSeriesApi
     */
    public async getLatest(deviceId: string, body?: GetLatestInput, options?: AxiosRequestConfig) : Promise<AxiosResponse<RESTfulResultTimeSeriesData>> {
        return TimeSeriesApiFp(this.configuration).getLatest(deviceId, body, options).then((request) => request(this.axios, this.basePath));
    }
    /**
     * 
     * @summary ????????
     * @param {GetLatestBatchInput} [body] 
     * @param {*} [options] Override http request option.
     * @throws {RequiredError}
     * @memberof TimeSeriesApi
     */
    public async getLatestBatch(body?: GetLatestBatchInput, options?: AxiosRequestConfig) : Promise<AxiosResponse<RESTfulResultIDictionaryStringTimeSeriesData>> {
        return TimeSeriesApiFp(this.configuration).getLatestBatch(body, options).then((request) => request(this.axios, this.basePath));
    }
    /**
     * 
     * @summary ??????
     * @param {*} [options] Override http request option.
     * @throws {RequiredError}
     * @memberof TimeSeriesApi
     */
    public async getPerformanceReport(options?: AxiosRequestConfig) : Promise<AxiosResponse<RESTfulResultPerformanceReport>> {
        return TimeSeriesApiFp(this.configuration).getPerformanceReport(options).then((request) => request(this.axios, this.basePath));
    }
    /**
     * 
     * @summary ??????
     * @param {string} deviceId 
     * @param {GetQuerySuggestionsInput} [body] 
     * @param {*} [options] Override http request option.
     * @throws {RequiredError}
     * @memberof TimeSeriesApi
     */
    public async getQuerySuggestions(deviceId: string, body?: GetQuerySuggestionsInput, options?: AxiosRequestConfig) : Promise<AxiosResponse<RESTfulResultQuerySuggestion>> {
        return TimeSeriesApiFp(this.configuration).getQuerySuggestions(deviceId, body, options).then((request) => request(this.axios, this.basePath));
    }
    /**
     * 
     * @summary ????????
     * @param {TimeSeriesStatisticsRequest} [body] 
     * @param {*} [options] Override http request option.
     * @throws {RequiredError}
     * @memberof TimeSeriesApi
     */
    public async getStatistics(body?: TimeSeriesStatisticsRequest, options?: AxiosRequestConfig) : Promise<AxiosResponse<RESTfulResultTimeSeriesStatistics>> {
        return TimeSeriesApiFp(this.configuration).getStatistics(body, options).then((request) => request(this.axios, this.basePath));
    }
    /**
     * 
     * @summary ??????
     * @param {*} [options] Override http request option.
     * @throws {RequiredError}
     * @memberof TimeSeriesApi
     */
    public async getStorageStatus(options?: AxiosRequestConfig) : Promise<AxiosResponse<RESTfulResultStorageStatus>> {
        return TimeSeriesApiFp(this.configuration).getStorageStatus(options).then((request) => request(this.axios, this.basePath));
    }
    /**
     * 
     * @summary ????????
     * @param {TimeSeriesPagedQueryRequest} [body] 
     * @param {*} [options] Override http request option.
     * @throws {RequiredError}
     * @memberof TimeSeriesApi
     */
    public async getTimeSeriesPage(body?: TimeSeriesPagedQueryRequest, options?: AxiosRequestConfig) : Promise<AxiosResponse<RESTfulResultPagedResultTimeSeriesData>> {
        return TimeSeriesApiFp(this.configuration).getTimeSeriesPage(body, options).then((request) => request(this.axios, this.basePath));
    }
    /**
     * 
     * @summary ??????
     * @param {TimeSeriesQueryRequest} [body] 
     * @param {*} [options] Override http request option.
     * @throws {RequiredError}
     * @memberof TimeSeriesApi
     */
    public async queryTimeSeries(body?: TimeSeriesQueryRequest, options?: AxiosRequestConfig) : Promise<AxiosResponse<RESTfulResultIEnumerableTimeSeriesData>> {
        return TimeSeriesApiFp(this.configuration).queryTimeSeries(body, options).then((request) => request(this.axios, this.basePath));
    }
}
