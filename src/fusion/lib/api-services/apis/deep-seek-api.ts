/* tslint:disable */
/* eslint-disable */
/**
 * All Groups
 * No description provided (generated by Swagger Codegen https://github.com/swagger-api/swagger-codegen)
 *
 * OpenAPI spec version: 1.0.0
 * 
 *
 * NOTE: This class is auto generated by the swagger code generator program.
 * https://github.com/swagger-api/swagger-codegen.git
 * Do not edit the class manually.
 */

import globalAxios, { AxiosResponse, AxiosInstance, AxiosRequestConfig } from 'axios';
import { Configuration } from '../configuration';
// Some imports not used depending on template conditions
// @ts-ignore
import { BASE_PATH, COLLECTION_FORMATS, RequestArgs, BaseAPI, RequiredError } from '../base';
import { ChatRequest } from '../models';
import { ClearSessionInput } from '../models';
import { PayLoadTransformRequest } from '../models';
import { RESTfulResultObject } from '../models';
/**
 * DeepSeekApi - axios parameter creator
 * @export
 */
export const DeepSeekApiAxiosParamCreator = function (configuration?: Configuration) {
    return {
        /**
         * 
         * @summary ????Ai??
         * @param {ChatRequest} [body] ????
         * @param {*} [options] Override http request option.
         * @throws {RequiredError}
         */
        chat: async (body?: ChatRequest, options: AxiosRequestConfig = {}): Promise<RequestArgs> => {
            const localVarPath = `/api/ai/chat`;
            // use dummy base URL string because the URL constructor only accepts absolute URLs.
            const localVarUrlObj = new URL(localVarPath, 'https://example.com');
            let baseOptions;
            if (configuration) {
                baseOptions = configuration.baseOptions;
            }
            const localVarRequestOptions :AxiosRequestConfig = { method: 'POST', ...baseOptions, ...options};
            const localVarHeaderParameter = {} as any;
            const localVarQueryParameter = {} as any;

            // authentication Bearer required
            // http bearer authentication required
            if (configuration && configuration.accessToken) {
                const accessToken = typeof configuration.accessToken === 'function'
                    ? await configuration.accessToken()
                    : await configuration.accessToken;
                localVarHeaderParameter["Authorization"] = "Bearer " + accessToken;
            }

            localVarHeaderParameter['Content-Type'] = 'application/json-patch+json';

            const query = new URLSearchParams(localVarUrlObj.search);
            for (const key in localVarQueryParameter) {
                query.set(key, localVarQueryParameter[key]);
            }
            for (const key in options.params) {
                query.set(key, options.params[key]);
            }
            localVarUrlObj.search = (new URLSearchParams(query)).toString();
            let headersFromBaseOptions = baseOptions && baseOptions.headers ? baseOptions.headers : {};
            localVarRequestOptions.headers = {...localVarHeaderParameter, ...headersFromBaseOptions, ...options.headers};
            const needsSerialization = (typeof body !== "string") || localVarRequestOptions.headers['Content-Type'] === 'application/json';
            localVarRequestOptions.data =  needsSerialization ? JSON.stringify(body !== undefined ? body : {}) : (body || "");

            return {
                url: localVarUrlObj.pathname + localVarUrlObj.search + localVarUrlObj.hash,
                options: localVarRequestOptions,
            };
        },
        /**
         * 
         * @summary ????????
         * @param {ChatRequest} [body] ????
         * @param {*} [options] Override http request option.
         * @throws {RequiredError}
         */
        chatStream: async (body?: ChatRequest, options: AxiosRequestConfig = {}): Promise<RequestArgs> => {
            const localVarPath = `/api/ai/chat/stream`;
            // use dummy base URL string because the URL constructor only accepts absolute URLs.
            const localVarUrlObj = new URL(localVarPath, 'https://example.com');
            let baseOptions;
            if (configuration) {
                baseOptions = configuration.baseOptions;
            }
            const localVarRequestOptions :AxiosRequestConfig = { method: 'POST', ...baseOptions, ...options};
            const localVarHeaderParameter = {} as any;
            const localVarQueryParameter = {} as any;

            // authentication Bearer required
            // http bearer authentication required
            if (configuration && configuration.accessToken) {
                const accessToken = typeof configuration.accessToken === 'function'
                    ? await configuration.accessToken()
                    : await configuration.accessToken;
                localVarHeaderParameter["Authorization"] = "Bearer " + accessToken;
            }

            localVarHeaderParameter['Content-Type'] = 'application/json-patch+json';

            const query = new URLSearchParams(localVarUrlObj.search);
            for (const key in localVarQueryParameter) {
                query.set(key, localVarQueryParameter[key]);
            }
            for (const key in options.params) {
                query.set(key, options.params[key]);
            }
            localVarUrlObj.search = (new URLSearchParams(query)).toString();
            let headersFromBaseOptions = baseOptions && baseOptions.headers ? baseOptions.headers : {};
            localVarRequestOptions.headers = {...localVarHeaderParameter, ...headersFromBaseOptions, ...options.headers};
            const needsSerialization = (typeof body !== "string") || localVarRequestOptions.headers['Content-Type'] === 'application/json';
            localVarRequestOptions.data =  needsSerialization ? JSON.stringify(body !== undefined ? body : {}) : (body || "");

            return {
                url: localVarUrlObj.pathname + localVarUrlObj.search + localVarUrlObj.hash,
                options: localVarRequestOptions,
            };
        },
        /**
         * 
         * @summary ??????
         * @param {ClearSessionInput} [body] ????????
         * @param {*} [options] Override http request option.
         * @throws {RequiredError}
         */
        clearSession: async (body?: ClearSessionInput, options: AxiosRequestConfig = {}): Promise<RequestArgs> => {
            const localVarPath = `/api/ai/chat/clear`;
            // use dummy base URL string because the URL constructor only accepts absolute URLs.
            const localVarUrlObj = new URL(localVarPath, 'https://example.com');
            let baseOptions;
            if (configuration) {
                baseOptions = configuration.baseOptions;
            }
            const localVarRequestOptions :AxiosRequestConfig = { method: 'POST', ...baseOptions, ...options};
            const localVarHeaderParameter = {} as any;
            const localVarQueryParameter = {} as any;

            // authentication Bearer required
            // http bearer authentication required
            if (configuration && configuration.accessToken) {
                const accessToken = typeof configuration.accessToken === 'function'
                    ? await configuration.accessToken()
                    : await configuration.accessToken;
                localVarHeaderParameter["Authorization"] = "Bearer " + accessToken;
            }

            localVarHeaderParameter['Content-Type'] = 'application/json-patch+json';

            const query = new URLSearchParams(localVarUrlObj.search);
            for (const key in localVarQueryParameter) {
                query.set(key, localVarQueryParameter[key]);
            }
            for (const key in options.params) {
                query.set(key, options.params[key]);
            }
            localVarUrlObj.search = (new URLSearchParams(query)).toString();
            let headersFromBaseOptions = baseOptions && baseOptions.headers ? baseOptions.headers : {};
            localVarRequestOptions.headers = {...localVarHeaderParameter, ...headersFromBaseOptions, ...options.headers};
            const needsSerialization = (typeof body !== "string") || localVarRequestOptions.headers['Content-Type'] === 'application/json';
            localVarRequestOptions.data =  needsSerialization ? JSON.stringify(body !== undefined ? body : {}) : (body || "");

            return {
                url: localVarUrlObj.pathname + localVarUrlObj.search + localVarUrlObj.hash,
                options: localVarRequestOptions,
            };
        },
        /**
         * 
         * @summary PayLoad ????????
         * @param {PayLoadTransformRequest} [body] ??????
         * @param {*} [options] Override http request option.
         * @throws {RequiredError}
         */
        payLoadTransform: async (body?: PayLoadTransformRequest, options: AxiosRequestConfig = {}): Promise<RequestArgs> => {
            const localVarPath = `/api/ai/payload-transform`;
            // use dummy base URL string because the URL constructor only accepts absolute URLs.
            const localVarUrlObj = new URL(localVarPath, 'https://example.com');
            let baseOptions;
            if (configuration) {
                baseOptions = configuration.baseOptions;
            }
            const localVarRequestOptions :AxiosRequestConfig = { method: 'POST', ...baseOptions, ...options};
            const localVarHeaderParameter = {} as any;
            const localVarQueryParameter = {} as any;

            // authentication Bearer required
            // http bearer authentication required
            if (configuration && configuration.accessToken) {
                const accessToken = typeof configuration.accessToken === 'function'
                    ? await configuration.accessToken()
                    : await configuration.accessToken;
                localVarHeaderParameter["Authorization"] = "Bearer " + accessToken;
            }

            localVarHeaderParameter['Content-Type'] = 'application/json-patch+json';

            const query = new URLSearchParams(localVarUrlObj.search);
            for (const key in localVarQueryParameter) {
                query.set(key, localVarQueryParameter[key]);
            }
            for (const key in options.params) {
                query.set(key, options.params[key]);
            }
            localVarUrlObj.search = (new URLSearchParams(query)).toString();
            let headersFromBaseOptions = baseOptions && baseOptions.headers ? baseOptions.headers : {};
            localVarRequestOptions.headers = {...localVarHeaderParameter, ...headersFromBaseOptions, ...options.headers};
            const needsSerialization = (typeof body !== "string") || localVarRequestOptions.headers['Content-Type'] === 'application/json';
            localVarRequestOptions.data =  needsSerialization ? JSON.stringify(body !== undefined ? body : {}) : (body || "");

            return {
                url: localVarUrlObj.pathname + localVarUrlObj.search + localVarUrlObj.hash,
                options: localVarRequestOptions,
            };
        },
    }
};

/**
 * DeepSeekApi - functional programming interface
 * @export
 */
export const DeepSeekApiFp = function(configuration?: Configuration) {
    return {
        /**
         * 
         * @summary ????Ai??
         * @param {ChatRequest} [body] ????
         * @param {*} [options] Override http request option.
         * @throws {RequiredError}
         */
        async chat(body?: ChatRequest, options?: AxiosRequestConfig): Promise<(axios?: AxiosInstance, basePath?: string) => Promise<AxiosResponse<RESTfulResultObject>>> {
            const localVarAxiosArgs = await DeepSeekApiAxiosParamCreator(configuration).chat(body, options);
            return (axios: AxiosInstance = globalAxios, basePath: string = BASE_PATH) => {
                const axiosRequestArgs :AxiosRequestConfig = {...localVarAxiosArgs.options, url: basePath + localVarAxiosArgs.url};
                return axios.request(axiosRequestArgs);
            };
        },
        /**
         * 
         * @summary ????????
         * @param {ChatRequest} [body] ????
         * @param {*} [options] Override http request option.
         * @throws {RequiredError}
         */
        async chatStream(body?: ChatRequest, options?: AxiosRequestConfig): Promise<(axios?: AxiosInstance, basePath?: string) => Promise<AxiosResponse<Array<any>>>> {
            const localVarAxiosArgs = await DeepSeekApiAxiosParamCreator(configuration).chatStream(body, options);
            return (axios: AxiosInstance = globalAxios, basePath: string = BASE_PATH) => {
                const axiosRequestArgs :AxiosRequestConfig = {...localVarAxiosArgs.options, url: basePath + localVarAxiosArgs.url};
                return axios.request(axiosRequestArgs);
            };
        },
        /**
         * 
         * @summary ??????
         * @param {ClearSessionInput} [body] ????????
         * @param {*} [options] Override http request option.
         * @throws {RequiredError}
         */
        async clearSession(body?: ClearSessionInput, options?: AxiosRequestConfig): Promise<(axios?: AxiosInstance, basePath?: string) => Promise<AxiosResponse<RESTfulResultObject>>> {
            const localVarAxiosArgs = await DeepSeekApiAxiosParamCreator(configuration).clearSession(body, options);
            return (axios: AxiosInstance = globalAxios, basePath: string = BASE_PATH) => {
                const axiosRequestArgs :AxiosRequestConfig = {...localVarAxiosArgs.options, url: basePath + localVarAxiosArgs.url};
                return axios.request(axiosRequestArgs);
            };
        },
        /**
         * 
         * @summary PayLoad ????????
         * @param {PayLoadTransformRequest} [body] ??????
         * @param {*} [options] Override http request option.
         * @throws {RequiredError}
         */
        async payLoadTransform(body?: PayLoadTransformRequest, options?: AxiosRequestConfig): Promise<(axios?: AxiosInstance, basePath?: string) => Promise<AxiosResponse<RESTfulResultObject>>> {
            const localVarAxiosArgs = await DeepSeekApiAxiosParamCreator(configuration).payLoadTransform(body, options);
            return (axios: AxiosInstance = globalAxios, basePath: string = BASE_PATH) => {
                const axiosRequestArgs :AxiosRequestConfig = {...localVarAxiosArgs.options, url: basePath + localVarAxiosArgs.url};
                return axios.request(axiosRequestArgs);
            };
        },
    }
};

/**
 * DeepSeekApi - factory interface
 * @export
 */
export const DeepSeekApiFactory = function (configuration?: Configuration, basePath?: string, axios?: AxiosInstance) {
    return {
        /**
         * 
         * @summary ????Ai??
         * @param {ChatRequest} [body] ????
         * @param {*} [options] Override http request option.
         * @throws {RequiredError}
         */
        async chat(body?: ChatRequest, options?: AxiosRequestConfig): Promise<AxiosResponse<RESTfulResultObject>> {
            return DeepSeekApiFp(configuration).chat(body, options).then((request) => request(axios, basePath));
        },
        /**
         * 
         * @summary ????????
         * @param {ChatRequest} [body] ????
         * @param {*} [options] Override http request option.
         * @throws {RequiredError}
         */
        async chatStream(body?: ChatRequest, options?: AxiosRequestConfig): Promise<AxiosResponse<Array<any>>> {
            return DeepSeekApiFp(configuration).chatStream(body, options).then((request) => request(axios, basePath));
        },
        /**
         * 
         * @summary ??????
         * @param {ClearSessionInput} [body] ????????
         * @param {*} [options] Override http request option.
         * @throws {RequiredError}
         */
        async clearSession(body?: ClearSessionInput, options?: AxiosRequestConfig): Promise<AxiosResponse<RESTfulResultObject>> {
            return DeepSeekApiFp(configuration).clearSession(body, options).then((request) => request(axios, basePath));
        },
        /**
         * 
         * @summary PayLoad ????????
         * @param {PayLoadTransformRequest} [body] ??????
         * @param {*} [options] Override http request option.
         * @throws {RequiredError}
         */
        async payLoadTransform(body?: PayLoadTransformRequest, options?: AxiosRequestConfig): Promise<AxiosResponse<RESTfulResultObject>> {
            return DeepSeekApiFp(configuration).payLoadTransform(body, options).then((request) => request(axios, basePath));
        },
    };
};

/**
 * DeepSeekApi - object-oriented interface
 * @export
 * @class DeepSeekApi
 * @extends {BaseAPI}
 */
export class DeepSeekApi extends BaseAPI {
    /**
     * 
     * @summary ????Ai??
     * @param {ChatRequest} [body] ????
     * @param {*} [options] Override http request option.
     * @throws {RequiredError}
     * @memberof DeepSeekApi
     */
    public async chat(body?: ChatRequest, options?: AxiosRequestConfig) : Promise<AxiosResponse<RESTfulResultObject>> {
        return DeepSeekApiFp(this.configuration).chat(body, options).then((request) => request(this.axios, this.basePath));
    }
    /**
     * 
     * @summary ????????
     * @param {ChatRequest} [body] ????
     * @param {*} [options] Override http request option.
     * @throws {RequiredError}
     * @memberof DeepSeekApi
     */
    public async chatStream(body?: ChatRequest, options?: AxiosRequestConfig) : Promise<AxiosResponse<Array<any>>> {
        return DeepSeekApiFp(this.configuration).chatStream(body, options).then((request) => request(this.axios, this.basePath));
    }
    /**
     * 
     * @summary ??????
     * @param {ClearSessionInput} [body] ????????
     * @param {*} [options] Override http request option.
     * @throws {RequiredError}
     * @memberof DeepSeekApi
     */
    public async clearSession(body?: ClearSessionInput, options?: AxiosRequestConfig) : Promise<AxiosResponse<RESTfulResultObject>> {
        return DeepSeekApiFp(this.configuration).clearSession(body, options).then((request) => request(this.axios, this.basePath));
    }
    /**
     * 
     * @summary PayLoad ????????
     * @param {PayLoadTransformRequest} [body] ??????
     * @param {*} [options] Override http request option.
     * @throws {RequiredError}
     * @memberof DeepSeekApi
     */
    public async payLoadTransform(body?: PayLoadTransformRequest, options?: AxiosRequestConfig) : Promise<AxiosResponse<RESTfulResultObject>> {
        return DeepSeekApiFp(this.configuration).payLoadTransform(body, options).then((request) => request(this.axios, this.basePath));
    }
}
