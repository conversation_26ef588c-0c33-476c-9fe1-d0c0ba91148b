/* tslint:disable */
/* eslint-disable */
/**
 * All Groups
 * No description provided (generated by Swagger Codegen https://github.com/swagger-api/swagger-codegen)
 *
 * OpenAPI spec version: 1.0.0
 * 
 *
 * NOTE: This class is auto generated by the swagger code generator program.
 * https://github.com/swagger-api/swagger-codegen.git
 * Do not edit the class manually.
 */

import globalAxios, { AxiosResponse, AxiosInstance, AxiosRequestConfig } from 'axios';
import { Configuration } from '../configuration';
// Some imports not used depending on template conditions
// @ts-ignore
import { BASE_PATH, COLLECTION_FORMATS, RequestArgs, BaseAPI, RequiredError } from '../base';
import { RESTfulResultBoolean } from '../models';
import { RESTfulResultIActionResult } from '../models';
import { RESTfulResultObject } from '../models';
/**
 * SysLogApi - axios parameter creator
 * @export
 */
export const SysLogApiAxiosParamCreator = function (configuration?: Configuration) {
    return {
        /**
         * 
         * @param {any} [body] 
         * @param {*} [options] Override http request option.
         * @throws {RequiredError}
         */
        cleanLogs: async (body?: any, options: AxiosRequestConfig = {}): Promise<RequestArgs> => {
            const localVarPath = `/api/sysLog/clean`;
            // use dummy base URL string because the URL constructor only accepts absolute URLs.
            const localVarUrlObj = new URL(localVarPath, 'https://example.com');
            let baseOptions;
            if (configuration) {
                baseOptions = configuration.baseOptions;
            }
            const localVarRequestOptions :AxiosRequestConfig = { method: 'POST', ...baseOptions, ...options};
            const localVarHeaderParameter = {} as any;
            const localVarQueryParameter = {} as any;

            // authentication Bearer required
            // http bearer authentication required
            if (configuration && configuration.accessToken) {
                const accessToken = typeof configuration.accessToken === 'function'
                    ? await configuration.accessToken()
                    : await configuration.accessToken;
                localVarHeaderParameter["Authorization"] = "Bearer " + accessToken;
            }

            localVarHeaderParameter['Content-Type'] = 'application/json-patch+json';

            const query = new URLSearchParams(localVarUrlObj.search);
            for (const key in localVarQueryParameter) {
                query.set(key, localVarQueryParameter[key]);
            }
            for (const key in options.params) {
                query.set(key, options.params[key]);
            }
            localVarUrlObj.search = (new URLSearchParams(query)).toString();
            let headersFromBaseOptions = baseOptions && baseOptions.headers ? baseOptions.headers : {};
            localVarRequestOptions.headers = {...localVarHeaderParameter, ...headersFromBaseOptions, ...options.headers};
            const needsSerialization = (typeof body !== "string") || localVarRequestOptions.headers['Content-Type'] === 'application/json';
            localVarRequestOptions.data =  needsSerialization ? JSON.stringify(body !== undefined ? body : {}) : (body || "");

            return {
                url: localVarUrlObj.pathname + localVarUrlObj.search + localVarUrlObj.hash,
                options: localVarRequestOptions,
            };
        },
        /**
         * 
         * @param {string} [format] 
         * @param {string} [level] 
         * @param {string} [category] 
         * @param {string} [startTime] 
         * @param {string} [endTime] 
         * @param {number} [maxRecords] 
         * @param {*} [options] Override http request option.
         * @throws {RequiredError}
         */
        exportLogs: async (format?: string, level?: string, category?: string, startTime?: string, endTime?: string, maxRecords?: number, options: AxiosRequestConfig = {}): Promise<RequestArgs> => {
            const localVarPath = `/api/sysLog/export`;
            // use dummy base URL string because the URL constructor only accepts absolute URLs.
            const localVarUrlObj = new URL(localVarPath, 'https://example.com');
            let baseOptions;
            if (configuration) {
                baseOptions = configuration.baseOptions;
            }
            const localVarRequestOptions :AxiosRequestConfig = { method: 'GET', ...baseOptions, ...options};
            const localVarHeaderParameter = {} as any;
            const localVarQueryParameter = {} as any;

            // authentication Bearer required
            // http bearer authentication required
            if (configuration && configuration.accessToken) {
                const accessToken = typeof configuration.accessToken === 'function'
                    ? await configuration.accessToken()
                    : await configuration.accessToken;
                localVarHeaderParameter["Authorization"] = "Bearer " + accessToken;
            }

            if (format !== undefined) {
                localVarQueryParameter['format'] = format;
            }

            if (level !== undefined) {
                localVarQueryParameter['level'] = level;
            }

            if (category !== undefined) {
                localVarQueryParameter['category'] = category;
            }

            if (startTime !== undefined) {
                localVarQueryParameter['startTime'] = startTime;
            }

            if (endTime !== undefined) {
                localVarQueryParameter['endTime'] = endTime;
            }

            if (maxRecords !== undefined) {
                localVarQueryParameter['maxRecords'] = maxRecords;
            }

            const query = new URLSearchParams(localVarUrlObj.search);
            for (const key in localVarQueryParameter) {
                query.set(key, localVarQueryParameter[key]);
            }
            for (const key in options.params) {
                query.set(key, options.params[key]);
            }
            localVarUrlObj.search = (new URLSearchParams(query)).toString();
            let headersFromBaseOptions = baseOptions && baseOptions.headers ? baseOptions.headers : {};
            localVarRequestOptions.headers = {...localVarHeaderParameter, ...headersFromBaseOptions, ...options.headers};

            return {
                url: localVarUrlObj.pathname + localVarUrlObj.search + localVarUrlObj.hash,
                options: localVarRequestOptions,
            };
        },
        /**
         * 
         * @param {number} [page] 
         * @param {number} [pageSize] 
         * @param {string} [level] 
         * @param {string} [category] 
         * @param {string} [action] 
         * @param {string} [module] 
         * @param {number} [userId] 
         * @param {string} [username] 
         * @param {string} [ipAddress] 
         * @param {string} [status] 
         * @param {string} [startTime] 
         * @param {string} [endTime] 
         * @param {string} [keyword] 
         * @param {*} [options] Override http request option.
         * @throws {RequiredError}
         */
        getPage: async (page?: number, pageSize?: number, level?: string, category?: string, action?: string, module?: string, userId?: number, username?: string, ipAddress?: string, status?: string, startTime?: string, endTime?: string, keyword?: string, options: AxiosRequestConfig = {}): Promise<RequestArgs> => {
            const localVarPath = `/api/sysLog/page`;
            // use dummy base URL string because the URL constructor only accepts absolute URLs.
            const localVarUrlObj = new URL(localVarPath, 'https://example.com');
            let baseOptions;
            if (configuration) {
                baseOptions = configuration.baseOptions;
            }
            const localVarRequestOptions :AxiosRequestConfig = { method: 'GET', ...baseOptions, ...options};
            const localVarHeaderParameter = {} as any;
            const localVarQueryParameter = {} as any;

            // authentication Bearer required
            // http bearer authentication required
            if (configuration && configuration.accessToken) {
                const accessToken = typeof configuration.accessToken === 'function'
                    ? await configuration.accessToken()
                    : await configuration.accessToken;
                localVarHeaderParameter["Authorization"] = "Bearer " + accessToken;
            }

            if (page !== undefined) {
                localVarQueryParameter['page'] = page;
            }

            if (pageSize !== undefined) {
                localVarQueryParameter['pageSize'] = pageSize;
            }

            if (level !== undefined) {
                localVarQueryParameter['level'] = level;
            }

            if (category !== undefined) {
                localVarQueryParameter['category'] = category;
            }

            if (action !== undefined) {
                localVarQueryParameter['action'] = action;
            }

            if (module !== undefined) {
                localVarQueryParameter['module'] = module;
            }

            if (userId !== undefined) {
                localVarQueryParameter['userId'] = userId;
            }

            if (username !== undefined) {
                localVarQueryParameter['username'] = username;
            }

            if (ipAddress !== undefined) {
                localVarQueryParameter['ipAddress'] = ipAddress;
            }

            if (status !== undefined) {
                localVarQueryParameter['status'] = status;
            }

            if (startTime !== undefined) {
                localVarQueryParameter['startTime'] = startTime;
            }

            if (endTime !== undefined) {
                localVarQueryParameter['endTime'] = endTime;
            }

            if (keyword !== undefined) {
                localVarQueryParameter['keyword'] = keyword;
            }

            const query = new URLSearchParams(localVarUrlObj.search);
            for (const key in localVarQueryParameter) {
                query.set(key, localVarQueryParameter[key]);
            }
            for (const key in options.params) {
                query.set(key, options.params[key]);
            }
            localVarUrlObj.search = (new URLSearchParams(query)).toString();
            let headersFromBaseOptions = baseOptions && baseOptions.headers ? baseOptions.headers : {};
            localVarRequestOptions.headers = {...localVarHeaderParameter, ...headersFromBaseOptions, ...options.headers};

            return {
                url: localVarUrlObj.pathname + localVarUrlObj.search + localVarUrlObj.hash,
                options: localVarRequestOptions,
            };
        },
        /**
         * 
         * @param {string} [startTime] 
         * @param {string} [endTime] 
         * @param {*} [options] Override http request option.
         * @throws {RequiredError}
         */
        getStats: async (startTime?: string, endTime?: string, options: AxiosRequestConfig = {}): Promise<RequestArgs> => {
            const localVarPath = `/api/sysLog/stats`;
            // use dummy base URL string because the URL constructor only accepts absolute URLs.
            const localVarUrlObj = new URL(localVarPath, 'https://example.com');
            let baseOptions;
            if (configuration) {
                baseOptions = configuration.baseOptions;
            }
            const localVarRequestOptions :AxiosRequestConfig = { method: 'GET', ...baseOptions, ...options};
            const localVarHeaderParameter = {} as any;
            const localVarQueryParameter = {} as any;

            // authentication Bearer required
            // http bearer authentication required
            if (configuration && configuration.accessToken) {
                const accessToken = typeof configuration.accessToken === 'function'
                    ? await configuration.accessToken()
                    : await configuration.accessToken;
                localVarHeaderParameter["Authorization"] = "Bearer " + accessToken;
            }

            if (startTime !== undefined) {
                localVarQueryParameter['startTime'] = startTime;
            }

            if (endTime !== undefined) {
                localVarQueryParameter['endTime'] = endTime;
            }

            const query = new URLSearchParams(localVarUrlObj.search);
            for (const key in localVarQueryParameter) {
                query.set(key, localVarQueryParameter[key]);
            }
            for (const key in options.params) {
                query.set(key, options.params[key]);
            }
            localVarUrlObj.search = (new URLSearchParams(query)).toString();
            let headersFromBaseOptions = baseOptions && baseOptions.headers ? baseOptions.headers : {};
            localVarRequestOptions.headers = {...localVarHeaderParameter, ...headersFromBaseOptions, ...options.headers};

            return {
                url: localVarUrlObj.pathname + localVarUrlObj.search + localVarUrlObj.hash,
                options: localVarRequestOptions,
            };
        },
    }
};

/**
 * SysLogApi - functional programming interface
 * @export
 */
export const SysLogApiFp = function(configuration?: Configuration) {
    return {
        /**
         * 
         * @param {any} [body] 
         * @param {*} [options] Override http request option.
         * @throws {RequiredError}
         */
        async cleanLogs(body?: any, options?: AxiosRequestConfig): Promise<(axios?: AxiosInstance, basePath?: string) => Promise<AxiosResponse<RESTfulResultBoolean>>> {
            const localVarAxiosArgs = await SysLogApiAxiosParamCreator(configuration).cleanLogs(body, options);
            return (axios: AxiosInstance = globalAxios, basePath: string = BASE_PATH) => {
                const axiosRequestArgs :AxiosRequestConfig = {...localVarAxiosArgs.options, url: basePath + localVarAxiosArgs.url};
                return axios.request(axiosRequestArgs);
            };
        },
        /**
         * 
         * @param {string} [format] 
         * @param {string} [level] 
         * @param {string} [category] 
         * @param {string} [startTime] 
         * @param {string} [endTime] 
         * @param {number} [maxRecords] 
         * @param {*} [options] Override http request option.
         * @throws {RequiredError}
         */
        async exportLogs(format?: string, level?: string, category?: string, startTime?: string, endTime?: string, maxRecords?: number, options?: AxiosRequestConfig): Promise<(axios?: AxiosInstance, basePath?: string) => Promise<AxiosResponse<RESTfulResultIActionResult>>> {
            const localVarAxiosArgs = await SysLogApiAxiosParamCreator(configuration).exportLogs(format, level, category, startTime, endTime, maxRecords, options);
            return (axios: AxiosInstance = globalAxios, basePath: string = BASE_PATH) => {
                const axiosRequestArgs :AxiosRequestConfig = {...localVarAxiosArgs.options, url: basePath + localVarAxiosArgs.url};
                return axios.request(axiosRequestArgs);
            };
        },
        /**
         * 
         * @param {number} [page] 
         * @param {number} [pageSize] 
         * @param {string} [level] 
         * @param {string} [category] 
         * @param {string} [action] 
         * @param {string} [module] 
         * @param {number} [userId] 
         * @param {string} [username] 
         * @param {string} [ipAddress] 
         * @param {string} [status] 
         * @param {string} [startTime] 
         * @param {string} [endTime] 
         * @param {string} [keyword] 
         * @param {*} [options] Override http request option.
         * @throws {RequiredError}
         */
        async getPage(page?: number, pageSize?: number, level?: string, category?: string, action?: string, module?: string, userId?: number, username?: string, ipAddress?: string, status?: string, startTime?: string, endTime?: string, keyword?: string, options?: AxiosRequestConfig): Promise<(axios?: AxiosInstance, basePath?: string) => Promise<AxiosResponse<RESTfulResultObject>>> {
            const localVarAxiosArgs = await SysLogApiAxiosParamCreator(configuration).getPage(page, pageSize, level, category, action, module, userId, username, ipAddress, status, startTime, endTime, keyword, options);
            return (axios: AxiosInstance = globalAxios, basePath: string = BASE_PATH) => {
                const axiosRequestArgs :AxiosRequestConfig = {...localVarAxiosArgs.options, url: basePath + localVarAxiosArgs.url};
                return axios.request(axiosRequestArgs);
            };
        },
        /**
         * 
         * @param {string} [startTime] 
         * @param {string} [endTime] 
         * @param {*} [options] Override http request option.
         * @throws {RequiredError}
         */
        async getStats(startTime?: string, endTime?: string, options?: AxiosRequestConfig): Promise<(axios?: AxiosInstance, basePath?: string) => Promise<AxiosResponse<RESTfulResultObject>>> {
            const localVarAxiosArgs = await SysLogApiAxiosParamCreator(configuration).getStats(startTime, endTime, options);
            return (axios: AxiosInstance = globalAxios, basePath: string = BASE_PATH) => {
                const axiosRequestArgs :AxiosRequestConfig = {...localVarAxiosArgs.options, url: basePath + localVarAxiosArgs.url};
                return axios.request(axiosRequestArgs);
            };
        },
    }
};

/**
 * SysLogApi - factory interface
 * @export
 */
export const SysLogApiFactory = function (configuration?: Configuration, basePath?: string, axios?: AxiosInstance) {
    return {
        /**
         * 
         * @param {any} [body] 
         * @param {*} [options] Override http request option.
         * @throws {RequiredError}
         */
        async cleanLogs(body?: any, options?: AxiosRequestConfig): Promise<AxiosResponse<RESTfulResultBoolean>> {
            return SysLogApiFp(configuration).cleanLogs(body, options).then((request) => request(axios, basePath));
        },
        /**
         * 
         * @param {string} [format] 
         * @param {string} [level] 
         * @param {string} [category] 
         * @param {string} [startTime] 
         * @param {string} [endTime] 
         * @param {number} [maxRecords] 
         * @param {*} [options] Override http request option.
         * @throws {RequiredError}
         */
        async exportLogs(format?: string, level?: string, category?: string, startTime?: string, endTime?: string, maxRecords?: number, options?: AxiosRequestConfig): Promise<AxiosResponse<RESTfulResultIActionResult>> {
            return SysLogApiFp(configuration).exportLogs(format, level, category, startTime, endTime, maxRecords, options).then((request) => request(axios, basePath));
        },
        /**
         * 
         * @param {number} [page] 
         * @param {number} [pageSize] 
         * @param {string} [level] 
         * @param {string} [category] 
         * @param {string} [action] 
         * @param {string} [module] 
         * @param {number} [userId] 
         * @param {string} [username] 
         * @param {string} [ipAddress] 
         * @param {string} [status] 
         * @param {string} [startTime] 
         * @param {string} [endTime] 
         * @param {string} [keyword] 
         * @param {*} [options] Override http request option.
         * @throws {RequiredError}
         */
        async getPage(page?: number, pageSize?: number, level?: string, category?: string, action?: string, module?: string, userId?: number, username?: string, ipAddress?: string, status?: string, startTime?: string, endTime?: string, keyword?: string, options?: AxiosRequestConfig): Promise<AxiosResponse<RESTfulResultObject>> {
            return SysLogApiFp(configuration).getPage(page, pageSize, level, category, action, module, userId, username, ipAddress, status, startTime, endTime, keyword, options).then((request) => request(axios, basePath));
        },
        /**
         * 
         * @param {string} [startTime] 
         * @param {string} [endTime] 
         * @param {*} [options] Override http request option.
         * @throws {RequiredError}
         */
        async getStats(startTime?: string, endTime?: string, options?: AxiosRequestConfig): Promise<AxiosResponse<RESTfulResultObject>> {
            return SysLogApiFp(configuration).getStats(startTime, endTime, options).then((request) => request(axios, basePath));
        },
    };
};

/**
 * SysLogApi - object-oriented interface
 * @export
 * @class SysLogApi
 * @extends {BaseAPI}
 */
export class SysLogApi extends BaseAPI {
    /**
     * 
     * @param {any} [body] 
     * @param {*} [options] Override http request option.
     * @throws {RequiredError}
     * @memberof SysLogApi
     */
    public async cleanLogs(body?: any, options?: AxiosRequestConfig) : Promise<AxiosResponse<RESTfulResultBoolean>> {
        return SysLogApiFp(this.configuration).cleanLogs(body, options).then((request) => request(this.axios, this.basePath));
    }
    /**
     * 
     * @param {string} [format] 
     * @param {string} [level] 
     * @param {string} [category] 
     * @param {string} [startTime] 
     * @param {string} [endTime] 
     * @param {number} [maxRecords] 
     * @param {*} [options] Override http request option.
     * @throws {RequiredError}
     * @memberof SysLogApi
     */
    public async exportLogs(format?: string, level?: string, category?: string, startTime?: string, endTime?: string, maxRecords?: number, options?: AxiosRequestConfig) : Promise<AxiosResponse<RESTfulResultIActionResult>> {
        return SysLogApiFp(this.configuration).exportLogs(format, level, category, startTime, endTime, maxRecords, options).then((request) => request(this.axios, this.basePath));
    }
    /**
     * 
     * @param {number} [page] 
     * @param {number} [pageSize] 
     * @param {string} [level] 
     * @param {string} [category] 
     * @param {string} [action] 
     * @param {string} [module] 
     * @param {number} [userId] 
     * @param {string} [username] 
     * @param {string} [ipAddress] 
     * @param {string} [status] 
     * @param {string} [startTime] 
     * @param {string} [endTime] 
     * @param {string} [keyword] 
     * @param {*} [options] Override http request option.
     * @throws {RequiredError}
     * @memberof SysLogApi
     */
    public async getPage(page?: number, pageSize?: number, level?: string, category?: string, action?: string, module?: string, userId?: number, username?: string, ipAddress?: string, status?: string, startTime?: string, endTime?: string, keyword?: string, options?: AxiosRequestConfig) : Promise<AxiosResponse<RESTfulResultObject>> {
        return SysLogApiFp(this.configuration).getPage(page, pageSize, level, category, action, module, userId, username, ipAddress, status, startTime, endTime, keyword, options).then((request) => request(this.axios, this.basePath));
    }
    /**
     * 
     * @param {string} [startTime] 
     * @param {string} [endTime] 
     * @param {*} [options] Override http request option.
     * @throws {RequiredError}
     * @memberof SysLogApi
     */
    public async getStats(startTime?: string, endTime?: string, options?: AxiosRequestConfig) : Promise<AxiosResponse<RESTfulResultObject>> {
        return SysLogApiFp(this.configuration).getStats(startTime, endTime, options).then((request) => request(this.axios, this.basePath));
    }
}
