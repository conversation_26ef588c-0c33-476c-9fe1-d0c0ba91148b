/* tslint:disable */
/* eslint-disable */
/**
 * All Groups
 * No description provided (generated by Swagger Codegen https://github.com/swagger-api/swagger-codegen)
 *
 * OpenAPI spec version: 1.0.0
 * 
 *
 * NOTE: This class is auto generated by the swagger code generator program.
 * https://github.com/swagger-api/swagger-codegen.git
 * Do not edit the class manually.
 */

import globalAxios, { AxiosResponse, AxiosInstance, AxiosRequestConfig } from 'axios';
import { Configuration } from '../configuration';
// Some imports not used depending on template conditions
// @ts-ignore
import { BASE_PATH, COLLECTION_FORMATS, RequestArgs, BaseAPI, RequiredError } from '../base';
import { CreateNotificationDto } from '../models';
import { NotificationSourceEnum } from '../models';
import { NotificationTypeEnum } from '../models';
import { RESTfulResultBoolean } from '../models';
import { RESTfulResultInt32 } from '../models';
import { RESTfulResultInt64 } from '../models';
import { RESTfulResultNotificationResponseDto } from '../models';
import { RESTfulResultSqlSugarPagedListNotificationResponseDto } from '../models';
/**
 * NotificationApi - axios parameter creator
 * @export
 */
export const NotificationApiAxiosParamCreator = function (configuration?: Configuration) {
    return {
        /**
         * 
         * @param {CreateNotificationDto} [body] 
         * @param {*} [options] Override http request option.
         * @throws {RequiredError}
         */
        apiNotificationPushNotificationPost: async (body?: CreateNotificationDto, options: AxiosRequestConfig = {}): Promise<RequestArgs> => {
            const localVarPath = `/api/notification/pushNotification`;
            // use dummy base URL string because the URL constructor only accepts absolute URLs.
            const localVarUrlObj = new URL(localVarPath, 'https://example.com');
            let baseOptions;
            if (configuration) {
                baseOptions = configuration.baseOptions;
            }
            const localVarRequestOptions :AxiosRequestConfig = { method: 'POST', ...baseOptions, ...options};
            const localVarHeaderParameter = {} as any;
            const localVarQueryParameter = {} as any;

            // authentication Bearer required
            // http bearer authentication required
            if (configuration && configuration.accessToken) {
                const accessToken = typeof configuration.accessToken === 'function'
                    ? await configuration.accessToken()
                    : await configuration.accessToken;
                localVarHeaderParameter["Authorization"] = "Bearer " + accessToken;
            }

            localVarHeaderParameter['Content-Type'] = 'application/json-patch+json';

            const query = new URLSearchParams(localVarUrlObj.search);
            for (const key in localVarQueryParameter) {
                query.set(key, localVarQueryParameter[key]);
            }
            for (const key in options.params) {
                query.set(key, options.params[key]);
            }
            localVarUrlObj.search = (new URLSearchParams(query)).toString();
            let headersFromBaseOptions = baseOptions && baseOptions.headers ? baseOptions.headers : {};
            localVarRequestOptions.headers = {...localVarHeaderParameter, ...headersFromBaseOptions, ...options.headers};
            const needsSerialization = (typeof body !== "string") || localVarRequestOptions.headers['Content-Type'] === 'application/json';
            localVarRequestOptions.data =  needsSerialization ? JSON.stringify(body !== undefined ? body : {}) : (body || "");

            return {
                url: localVarUrlObj.pathname + localVarUrlObj.search + localVarUrlObj.hash,
                options: localVarRequestOptions,
            };
        },
        /**
         * 
         * @param {*} [options] Override http request option.
         * @throws {RequiredError}
         */
        clearReadNotificationsAsync: async (options: AxiosRequestConfig = {}): Promise<RequestArgs> => {
            const localVarPath = `/api/notification/clear-read`;
            // use dummy base URL string because the URL constructor only accepts absolute URLs.
            const localVarUrlObj = new URL(localVarPath, 'https://example.com');
            let baseOptions;
            if (configuration) {
                baseOptions = configuration.baseOptions;
            }
            const localVarRequestOptions :AxiosRequestConfig = { method: 'DELETE', ...baseOptions, ...options};
            const localVarHeaderParameter = {} as any;
            const localVarQueryParameter = {} as any;

            // authentication Bearer required
            // http bearer authentication required
            if (configuration && configuration.accessToken) {
                const accessToken = typeof configuration.accessToken === 'function'
                    ? await configuration.accessToken()
                    : await configuration.accessToken;
                localVarHeaderParameter["Authorization"] = "Bearer " + accessToken;
            }

            const query = new URLSearchParams(localVarUrlObj.search);
            for (const key in localVarQueryParameter) {
                query.set(key, localVarQueryParameter[key]);
            }
            for (const key in options.params) {
                query.set(key, options.params[key]);
            }
            localVarUrlObj.search = (new URLSearchParams(query)).toString();
            let headersFromBaseOptions = baseOptions && baseOptions.headers ? baseOptions.headers : {};
            localVarRequestOptions.headers = {...localVarHeaderParameter, ...headersFromBaseOptions, ...options.headers};

            return {
                url: localVarUrlObj.pathname + localVarUrlObj.search + localVarUrlObj.hash,
                options: localVarRequestOptions,
            };
        },
        /**
         * 
         * @param {CreateNotificationDto} [body] 
         * @param {*} [options] Override http request option.
         * @throws {RequiredError}
         */
        createNotificationAsync: async (body?: CreateNotificationDto, options: AxiosRequestConfig = {}): Promise<RequestArgs> => {
            const localVarPath = `/api/notification/create`;
            // use dummy base URL string because the URL constructor only accepts absolute URLs.
            const localVarUrlObj = new URL(localVarPath, 'https://example.com');
            let baseOptions;
            if (configuration) {
                baseOptions = configuration.baseOptions;
            }
            const localVarRequestOptions :AxiosRequestConfig = { method: 'POST', ...baseOptions, ...options};
            const localVarHeaderParameter = {} as any;
            const localVarQueryParameter = {} as any;

            // authentication Bearer required
            // http bearer authentication required
            if (configuration && configuration.accessToken) {
                const accessToken = typeof configuration.accessToken === 'function'
                    ? await configuration.accessToken()
                    : await configuration.accessToken;
                localVarHeaderParameter["Authorization"] = "Bearer " + accessToken;
            }

            localVarHeaderParameter['Content-Type'] = 'application/json-patch+json';

            const query = new URLSearchParams(localVarUrlObj.search);
            for (const key in localVarQueryParameter) {
                query.set(key, localVarQueryParameter[key]);
            }
            for (const key in options.params) {
                query.set(key, options.params[key]);
            }
            localVarUrlObj.search = (new URLSearchParams(query)).toString();
            let headersFromBaseOptions = baseOptions && baseOptions.headers ? baseOptions.headers : {};
            localVarRequestOptions.headers = {...localVarHeaderParameter, ...headersFromBaseOptions, ...options.headers};
            const needsSerialization = (typeof body !== "string") || localVarRequestOptions.headers['Content-Type'] === 'application/json';
            localVarRequestOptions.data =  needsSerialization ? JSON.stringify(body !== undefined ? body : {}) : (body || "");

            return {
                url: localVarUrlObj.pathname + localVarUrlObj.search + localVarUrlObj.hash,
                options: localVarRequestOptions,
            };
        },
        /**
         * 
         * @param {number} id 
         * @param {*} [options] Override http request option.
         * @throws {RequiredError}
         */
        deleteNotificationAsync: async (id: number, options: AxiosRequestConfig = {}): Promise<RequestArgs> => {
            // verify required parameter 'id' is not null or undefined
            if (id === null || id === undefined) {
                throw new RequiredError('id','Required parameter id was null or undefined when calling deleteNotificationAsync.');
            }
            const localVarPath = `/api/notification/{id}`
                .replace(`{${"id"}}`, encodeURIComponent(String(id)));
            // use dummy base URL string because the URL constructor only accepts absolute URLs.
            const localVarUrlObj = new URL(localVarPath, 'https://example.com');
            let baseOptions;
            if (configuration) {
                baseOptions = configuration.baseOptions;
            }
            const localVarRequestOptions :AxiosRequestConfig = { method: 'DELETE', ...baseOptions, ...options};
            const localVarHeaderParameter = {} as any;
            const localVarQueryParameter = {} as any;

            // authentication Bearer required
            // http bearer authentication required
            if (configuration && configuration.accessToken) {
                const accessToken = typeof configuration.accessToken === 'function'
                    ? await configuration.accessToken()
                    : await configuration.accessToken;
                localVarHeaderParameter["Authorization"] = "Bearer " + accessToken;
            }

            const query = new URLSearchParams(localVarUrlObj.search);
            for (const key in localVarQueryParameter) {
                query.set(key, localVarQueryParameter[key]);
            }
            for (const key in options.params) {
                query.set(key, options.params[key]);
            }
            localVarUrlObj.search = (new URLSearchParams(query)).toString();
            let headersFromBaseOptions = baseOptions && baseOptions.headers ? baseOptions.headers : {};
            localVarRequestOptions.headers = {...localVarHeaderParameter, ...headersFromBaseOptions, ...options.headers};

            return {
                url: localVarUrlObj.pathname + localVarUrlObj.search + localVarUrlObj.hash,
                options: localVarRequestOptions,
            };
        },
        /**
         * 
         * @param {number} id 
         * @param {*} [options] Override http request option.
         * @throws {RequiredError}
         */
        getNotificationByIdAsync: async (id: number, options: AxiosRequestConfig = {}): Promise<RequestArgs> => {
            // verify required parameter 'id' is not null or undefined
            if (id === null || id === undefined) {
                throw new RequiredError('id','Required parameter id was null or undefined when calling getNotificationByIdAsync.');
            }
            const localVarPath = `/api/notification/{id}`
                .replace(`{${"id"}}`, encodeURIComponent(String(id)));
            // use dummy base URL string because the URL constructor only accepts absolute URLs.
            const localVarUrlObj = new URL(localVarPath, 'https://example.com');
            let baseOptions;
            if (configuration) {
                baseOptions = configuration.baseOptions;
            }
            const localVarRequestOptions :AxiosRequestConfig = { method: 'GET', ...baseOptions, ...options};
            const localVarHeaderParameter = {} as any;
            const localVarQueryParameter = {} as any;

            // authentication Bearer required
            // http bearer authentication required
            if (configuration && configuration.accessToken) {
                const accessToken = typeof configuration.accessToken === 'function'
                    ? await configuration.accessToken()
                    : await configuration.accessToken;
                localVarHeaderParameter["Authorization"] = "Bearer " + accessToken;
            }

            const query = new URLSearchParams(localVarUrlObj.search);
            for (const key in localVarQueryParameter) {
                query.set(key, localVarQueryParameter[key]);
            }
            for (const key in options.params) {
                query.set(key, options.params[key]);
            }
            localVarUrlObj.search = (new URLSearchParams(query)).toString();
            let headersFromBaseOptions = baseOptions && baseOptions.headers ? baseOptions.headers : {};
            localVarRequestOptions.headers = {...localVarHeaderParameter, ...headersFromBaseOptions, ...options.headers};

            return {
                url: localVarUrlObj.pathname + localVarUrlObj.search + localVarUrlObj.hash,
                options: localVarRequestOptions,
            };
        },
        /**
         * 
         * @param {number} [page] 
         * @param {number} [pageSize] 
         * @param {NotificationTypeEnum} [type] &lt;br /&gt;&amp;nbsp;?? Info &#x3D; 1&lt;br /&gt;&amp;nbsp;?? Warning &#x3D; 2&lt;br /&gt;&amp;nbsp;?? Critical &#x3D; 3&lt;br /&gt;&amp;nbsp;?? Success &#x3D; 4&lt;br /&gt;
         * @param {NotificationSourceEnum} [source] &lt;br /&gt;&amp;nbsp;???? DeviceManagement &#x3D; 1&lt;br /&gt;&amp;nbsp;???? DataForwarding &#x3D; 2&lt;br /&gt;&amp;nbsp;???? SystemManagement &#x3D; 3&lt;br /&gt;&amp;nbsp;???? DataCollection &#x3D; 4&lt;br /&gt;&amp;nbsp;????? WorkflowEngine &#x3D; 5&lt;br /&gt;&amp;nbsp;???? SecurityMonitoring &#x3D; 6&lt;br /&gt;&amp;nbsp;???? DeviceMonitoring &#x3D; 7&lt;br /&gt;
         * @param {boolean} [isRead] 
         * @param {string} [keyword] 
         * @param {Date} [startTime] 
         * @param {Date} [endTime] 
         * @param {string} [relatedEntityId] 
         * @param {string} [relatedEntityType] 
         * @param {*} [options] Override http request option.
         * @throws {RequiredError}
         */
        getNotificationsAsync: async (page?: number, pageSize?: number, type?: NotificationTypeEnum, source?: NotificationSourceEnum, isRead?: boolean, keyword?: string, startTime?: Date, endTime?: Date, relatedEntityId?: string, relatedEntityType?: string, options: AxiosRequestConfig = {}): Promise<RequestArgs> => {
            const localVarPath = `/api/notification/page`;
            // use dummy base URL string because the URL constructor only accepts absolute URLs.
            const localVarUrlObj = new URL(localVarPath, 'https://example.com');
            let baseOptions;
            if (configuration) {
                baseOptions = configuration.baseOptions;
            }
            const localVarRequestOptions :AxiosRequestConfig = { method: 'GET', ...baseOptions, ...options};
            const localVarHeaderParameter = {} as any;
            const localVarQueryParameter = {} as any;

            // authentication Bearer required
            // http bearer authentication required
            if (configuration && configuration.accessToken) {
                const accessToken = typeof configuration.accessToken === 'function'
                    ? await configuration.accessToken()
                    : await configuration.accessToken;
                localVarHeaderParameter["Authorization"] = "Bearer " + accessToken;
            }

            if (page !== undefined) {
                localVarQueryParameter['Page'] = page;
            }

            if (pageSize !== undefined) {
                localVarQueryParameter['PageSize'] = pageSize;
            }

            if (type !== undefined) {
                localVarQueryParameter['Type'] = type;
            }

            if (source !== undefined) {
                localVarQueryParameter['Source'] = source;
            }

            if (isRead !== undefined) {
                localVarQueryParameter['IsRead'] = isRead;
            }

            if (keyword !== undefined) {
                localVarQueryParameter['Keyword'] = keyword;
            }

            if (startTime !== undefined) {
                localVarQueryParameter['StartTime'] = (startTime as any instanceof Date) ?
                    (startTime as any).toISOString() :
                    startTime;
            }

            if (endTime !== undefined) {
                localVarQueryParameter['EndTime'] = (endTime as any instanceof Date) ?
                    (endTime as any).toISOString() :
                    endTime;
            }

            if (relatedEntityId !== undefined) {
                localVarQueryParameter['RelatedEntityId'] = relatedEntityId;
            }

            if (relatedEntityType !== undefined) {
                localVarQueryParameter['RelatedEntityType'] = relatedEntityType;
            }

            const query = new URLSearchParams(localVarUrlObj.search);
            for (const key in localVarQueryParameter) {
                query.set(key, localVarQueryParameter[key]);
            }
            for (const key in options.params) {
                query.set(key, options.params[key]);
            }
            localVarUrlObj.search = (new URLSearchParams(query)).toString();
            let headersFromBaseOptions = baseOptions && baseOptions.headers ? baseOptions.headers : {};
            localVarRequestOptions.headers = {...localVarHeaderParameter, ...headersFromBaseOptions, ...options.headers};

            return {
                url: localVarUrlObj.pathname + localVarUrlObj.search + localVarUrlObj.hash,
                options: localVarRequestOptions,
            };
        },
        /**
         * 
         * @param {*} [options] Override http request option.
         * @throws {RequiredError}
         */
        getUnreadCountAsync: async (options: AxiosRequestConfig = {}): Promise<RequestArgs> => {
            const localVarPath = `/api/notification/unread-count`;
            // use dummy base URL string because the URL constructor only accepts absolute URLs.
            const localVarUrlObj = new URL(localVarPath, 'https://example.com');
            let baseOptions;
            if (configuration) {
                baseOptions = configuration.baseOptions;
            }
            const localVarRequestOptions :AxiosRequestConfig = { method: 'GET', ...baseOptions, ...options};
            const localVarHeaderParameter = {} as any;
            const localVarQueryParameter = {} as any;

            // authentication Bearer required
            // http bearer authentication required
            if (configuration && configuration.accessToken) {
                const accessToken = typeof configuration.accessToken === 'function'
                    ? await configuration.accessToken()
                    : await configuration.accessToken;
                localVarHeaderParameter["Authorization"] = "Bearer " + accessToken;
            }

            const query = new URLSearchParams(localVarUrlObj.search);
            for (const key in localVarQueryParameter) {
                query.set(key, localVarQueryParameter[key]);
            }
            for (const key in options.params) {
                query.set(key, options.params[key]);
            }
            localVarUrlObj.search = (new URLSearchParams(query)).toString();
            let headersFromBaseOptions = baseOptions && baseOptions.headers ? baseOptions.headers : {};
            localVarRequestOptions.headers = {...localVarHeaderParameter, ...headersFromBaseOptions, ...options.headers};

            return {
                url: localVarUrlObj.pathname + localVarUrlObj.search + localVarUrlObj.hash,
                options: localVarRequestOptions,
            };
        },
        /**
         * 
         * @param {*} [options] Override http request option.
         * @throws {RequiredError}
         */
        markAllAsReadAsync: async (options: AxiosRequestConfig = {}): Promise<RequestArgs> => {
            const localVarPath = `/api/notification/read-all`;
            // use dummy base URL string because the URL constructor only accepts absolute URLs.
            const localVarUrlObj = new URL(localVarPath, 'https://example.com');
            let baseOptions;
            if (configuration) {
                baseOptions = configuration.baseOptions;
            }
            const localVarRequestOptions :AxiosRequestConfig = { method: 'PUT', ...baseOptions, ...options};
            const localVarHeaderParameter = {} as any;
            const localVarQueryParameter = {} as any;

            // authentication Bearer required
            // http bearer authentication required
            if (configuration && configuration.accessToken) {
                const accessToken = typeof configuration.accessToken === 'function'
                    ? await configuration.accessToken()
                    : await configuration.accessToken;
                localVarHeaderParameter["Authorization"] = "Bearer " + accessToken;
            }

            const query = new URLSearchParams(localVarUrlObj.search);
            for (const key in localVarQueryParameter) {
                query.set(key, localVarQueryParameter[key]);
            }
            for (const key in options.params) {
                query.set(key, options.params[key]);
            }
            localVarUrlObj.search = (new URLSearchParams(query)).toString();
            let headersFromBaseOptions = baseOptions && baseOptions.headers ? baseOptions.headers : {};
            localVarRequestOptions.headers = {...localVarHeaderParameter, ...headersFromBaseOptions, ...options.headers};

            return {
                url: localVarUrlObj.pathname + localVarUrlObj.search + localVarUrlObj.hash,
                options: localVarRequestOptions,
            };
        },
        /**
         * 
         * @param {number} id 
         * @param {*} [options] Override http request option.
         * @throws {RequiredError}
         */
        markAsReadAsync: async (id: number, options: AxiosRequestConfig = {}): Promise<RequestArgs> => {
            // verify required parameter 'id' is not null or undefined
            if (id === null || id === undefined) {
                throw new RequiredError('id','Required parameter id was null or undefined when calling markAsReadAsync.');
            }
            const localVarPath = `/api/notification/{id}/read`
                .replace(`{${"id"}}`, encodeURIComponent(String(id)));
            // use dummy base URL string because the URL constructor only accepts absolute URLs.
            const localVarUrlObj = new URL(localVarPath, 'https://example.com');
            let baseOptions;
            if (configuration) {
                baseOptions = configuration.baseOptions;
            }
            const localVarRequestOptions :AxiosRequestConfig = { method: 'PUT', ...baseOptions, ...options};
            const localVarHeaderParameter = {} as any;
            const localVarQueryParameter = {} as any;

            // authentication Bearer required
            // http bearer authentication required
            if (configuration && configuration.accessToken) {
                const accessToken = typeof configuration.accessToken === 'function'
                    ? await configuration.accessToken()
                    : await configuration.accessToken;
                localVarHeaderParameter["Authorization"] = "Bearer " + accessToken;
            }

            const query = new URLSearchParams(localVarUrlObj.search);
            for (const key in localVarQueryParameter) {
                query.set(key, localVarQueryParameter[key]);
            }
            for (const key in options.params) {
                query.set(key, options.params[key]);
            }
            localVarUrlObj.search = (new URLSearchParams(query)).toString();
            let headersFromBaseOptions = baseOptions && baseOptions.headers ? baseOptions.headers : {};
            localVarRequestOptions.headers = {...localVarHeaderParameter, ...headersFromBaseOptions, ...options.headers};

            return {
                url: localVarUrlObj.pathname + localVarUrlObj.search + localVarUrlObj.hash,
                options: localVarRequestOptions,
            };
        },
    }
};

/**
 * NotificationApi - functional programming interface
 * @export
 */
export const NotificationApiFp = function(configuration?: Configuration) {
    return {
        /**
         * 
         * @param {CreateNotificationDto} [body] 
         * @param {*} [options] Override http request option.
         * @throws {RequiredError}
         */
        async apiNotificationPushNotificationPost(body?: CreateNotificationDto, options?: AxiosRequestConfig): Promise<(axios?: AxiosInstance, basePath?: string) => Promise<AxiosResponse<RESTfulResultInt64>>> {
            const localVarAxiosArgs = await NotificationApiAxiosParamCreator(configuration).apiNotificationPushNotificationPost(body, options);
            return (axios: AxiosInstance = globalAxios, basePath: string = BASE_PATH) => {
                const axiosRequestArgs :AxiosRequestConfig = {...localVarAxiosArgs.options, url: basePath + localVarAxiosArgs.url};
                return axios.request(axiosRequestArgs);
            };
        },
        /**
         * 
         * @param {*} [options] Override http request option.
         * @throws {RequiredError}
         */
        async clearReadNotificationsAsync(options?: AxiosRequestConfig): Promise<(axios?: AxiosInstance, basePath?: string) => Promise<AxiosResponse<RESTfulResultInt32>>> {
            const localVarAxiosArgs = await NotificationApiAxiosParamCreator(configuration).clearReadNotificationsAsync(options);
            return (axios: AxiosInstance = globalAxios, basePath: string = BASE_PATH) => {
                const axiosRequestArgs :AxiosRequestConfig = {...localVarAxiosArgs.options, url: basePath + localVarAxiosArgs.url};
                return axios.request(axiosRequestArgs);
            };
        },
        /**
         * 
         * @param {CreateNotificationDto} [body] 
         * @param {*} [options] Override http request option.
         * @throws {RequiredError}
         */
        async createNotificationAsync(body?: CreateNotificationDto, options?: AxiosRequestConfig): Promise<(axios?: AxiosInstance, basePath?: string) => Promise<AxiosResponse<RESTfulResultInt64>>> {
            const localVarAxiosArgs = await NotificationApiAxiosParamCreator(configuration).createNotificationAsync(body, options);
            return (axios: AxiosInstance = globalAxios, basePath: string = BASE_PATH) => {
                const axiosRequestArgs :AxiosRequestConfig = {...localVarAxiosArgs.options, url: basePath + localVarAxiosArgs.url};
                return axios.request(axiosRequestArgs);
            };
        },
        /**
         * 
         * @param {number} id 
         * @param {*} [options] Override http request option.
         * @throws {RequiredError}
         */
        async deleteNotificationAsync(id: number, options?: AxiosRequestConfig): Promise<(axios?: AxiosInstance, basePath?: string) => Promise<AxiosResponse<RESTfulResultBoolean>>> {
            const localVarAxiosArgs = await NotificationApiAxiosParamCreator(configuration).deleteNotificationAsync(id, options);
            return (axios: AxiosInstance = globalAxios, basePath: string = BASE_PATH) => {
                const axiosRequestArgs :AxiosRequestConfig = {...localVarAxiosArgs.options, url: basePath + localVarAxiosArgs.url};
                return axios.request(axiosRequestArgs);
            };
        },
        /**
         * 
         * @param {number} id 
         * @param {*} [options] Override http request option.
         * @throws {RequiredError}
         */
        async getNotificationByIdAsync(id: number, options?: AxiosRequestConfig): Promise<(axios?: AxiosInstance, basePath?: string) => Promise<AxiosResponse<RESTfulResultNotificationResponseDto>>> {
            const localVarAxiosArgs = await NotificationApiAxiosParamCreator(configuration).getNotificationByIdAsync(id, options);
            return (axios: AxiosInstance = globalAxios, basePath: string = BASE_PATH) => {
                const axiosRequestArgs :AxiosRequestConfig = {...localVarAxiosArgs.options, url: basePath + localVarAxiosArgs.url};
                return axios.request(axiosRequestArgs);
            };
        },
        /**
         * 
         * @param {number} [page] 
         * @param {number} [pageSize] 
         * @param {NotificationTypeEnum} [type] &lt;br /&gt;&amp;nbsp;?? Info &#x3D; 1&lt;br /&gt;&amp;nbsp;?? Warning &#x3D; 2&lt;br /&gt;&amp;nbsp;?? Critical &#x3D; 3&lt;br /&gt;&amp;nbsp;?? Success &#x3D; 4&lt;br /&gt;
         * @param {NotificationSourceEnum} [source] &lt;br /&gt;&amp;nbsp;???? DeviceManagement &#x3D; 1&lt;br /&gt;&amp;nbsp;???? DataForwarding &#x3D; 2&lt;br /&gt;&amp;nbsp;???? SystemManagement &#x3D; 3&lt;br /&gt;&amp;nbsp;???? DataCollection &#x3D; 4&lt;br /&gt;&amp;nbsp;????? WorkflowEngine &#x3D; 5&lt;br /&gt;&amp;nbsp;???? SecurityMonitoring &#x3D; 6&lt;br /&gt;&amp;nbsp;???? DeviceMonitoring &#x3D; 7&lt;br /&gt;
         * @param {boolean} [isRead] 
         * @param {string} [keyword] 
         * @param {Date} [startTime] 
         * @param {Date} [endTime] 
         * @param {string} [relatedEntityId] 
         * @param {string} [relatedEntityType] 
         * @param {*} [options] Override http request option.
         * @throws {RequiredError}
         */
        async getNotificationsAsync(page?: number, pageSize?: number, type?: NotificationTypeEnum, source?: NotificationSourceEnum, isRead?: boolean, keyword?: string, startTime?: Date, endTime?: Date, relatedEntityId?: string, relatedEntityType?: string, options?: AxiosRequestConfig): Promise<(axios?: AxiosInstance, basePath?: string) => Promise<AxiosResponse<RESTfulResultSqlSugarPagedListNotificationResponseDto>>> {
            const localVarAxiosArgs = await NotificationApiAxiosParamCreator(configuration).getNotificationsAsync(page, pageSize, type, source, isRead, keyword, startTime, endTime, relatedEntityId, relatedEntityType, options);
            return (axios: AxiosInstance = globalAxios, basePath: string = BASE_PATH) => {
                const axiosRequestArgs :AxiosRequestConfig = {...localVarAxiosArgs.options, url: basePath + localVarAxiosArgs.url};
                return axios.request(axiosRequestArgs);
            };
        },
        /**
         * 
         * @param {*} [options] Override http request option.
         * @throws {RequiredError}
         */
        async getUnreadCountAsync(options?: AxiosRequestConfig): Promise<(axios?: AxiosInstance, basePath?: string) => Promise<AxiosResponse<RESTfulResultInt32>>> {
            const localVarAxiosArgs = await NotificationApiAxiosParamCreator(configuration).getUnreadCountAsync(options);
            return (axios: AxiosInstance = globalAxios, basePath: string = BASE_PATH) => {
                const axiosRequestArgs :AxiosRequestConfig = {...localVarAxiosArgs.options, url: basePath + localVarAxiosArgs.url};
                return axios.request(axiosRequestArgs);
            };
        },
        /**
         * 
         * @param {*} [options] Override http request option.
         * @throws {RequiredError}
         */
        async markAllAsReadAsync(options?: AxiosRequestConfig): Promise<(axios?: AxiosInstance, basePath?: string) => Promise<AxiosResponse<RESTfulResultInt32>>> {
            const localVarAxiosArgs = await NotificationApiAxiosParamCreator(configuration).markAllAsReadAsync(options);
            return (axios: AxiosInstance = globalAxios, basePath: string = BASE_PATH) => {
                const axiosRequestArgs :AxiosRequestConfig = {...localVarAxiosArgs.options, url: basePath + localVarAxiosArgs.url};
                return axios.request(axiosRequestArgs);
            };
        },
        /**
         * 
         * @param {number} id 
         * @param {*} [options] Override http request option.
         * @throws {RequiredError}
         */
        async markAsReadAsync(id: number, options?: AxiosRequestConfig): Promise<(axios?: AxiosInstance, basePath?: string) => Promise<AxiosResponse<RESTfulResultBoolean>>> {
            const localVarAxiosArgs = await NotificationApiAxiosParamCreator(configuration).markAsReadAsync(id, options);
            return (axios: AxiosInstance = globalAxios, basePath: string = BASE_PATH) => {
                const axiosRequestArgs :AxiosRequestConfig = {...localVarAxiosArgs.options, url: basePath + localVarAxiosArgs.url};
                return axios.request(axiosRequestArgs);
            };
        },
    }
};

/**
 * NotificationApi - factory interface
 * @export
 */
export const NotificationApiFactory = function (configuration?: Configuration, basePath?: string, axios?: AxiosInstance) {
    return {
        /**
         * 
         * @param {CreateNotificationDto} [body] 
         * @param {*} [options] Override http request option.
         * @throws {RequiredError}
         */
        async apiNotificationPushNotificationPost(body?: CreateNotificationDto, options?: AxiosRequestConfig): Promise<AxiosResponse<RESTfulResultInt64>> {
            return NotificationApiFp(configuration).apiNotificationPushNotificationPost(body, options).then((request) => request(axios, basePath));
        },
        /**
         * 
         * @param {*} [options] Override http request option.
         * @throws {RequiredError}
         */
        async clearReadNotificationsAsync(options?: AxiosRequestConfig): Promise<AxiosResponse<RESTfulResultInt32>> {
            return NotificationApiFp(configuration).clearReadNotificationsAsync(options).then((request) => request(axios, basePath));
        },
        /**
         * 
         * @param {CreateNotificationDto} [body] 
         * @param {*} [options] Override http request option.
         * @throws {RequiredError}
         */
        async createNotificationAsync(body?: CreateNotificationDto, options?: AxiosRequestConfig): Promise<AxiosResponse<RESTfulResultInt64>> {
            return NotificationApiFp(configuration).createNotificationAsync(body, options).then((request) => request(axios, basePath));
        },
        /**
         * 
         * @param {number} id 
         * @param {*} [options] Override http request option.
         * @throws {RequiredError}
         */
        async deleteNotificationAsync(id: number, options?: AxiosRequestConfig): Promise<AxiosResponse<RESTfulResultBoolean>> {
            return NotificationApiFp(configuration).deleteNotificationAsync(id, options).then((request) => request(axios, basePath));
        },
        /**
         * 
         * @param {number} id 
         * @param {*} [options] Override http request option.
         * @throws {RequiredError}
         */
        async getNotificationByIdAsync(id: number, options?: AxiosRequestConfig): Promise<AxiosResponse<RESTfulResultNotificationResponseDto>> {
            return NotificationApiFp(configuration).getNotificationByIdAsync(id, options).then((request) => request(axios, basePath));
        },
        /**
         * 
         * @param {number} [page] 
         * @param {number} [pageSize] 
         * @param {NotificationTypeEnum} [type] &lt;br /&gt;&amp;nbsp;?? Info &#x3D; 1&lt;br /&gt;&amp;nbsp;?? Warning &#x3D; 2&lt;br /&gt;&amp;nbsp;?? Critical &#x3D; 3&lt;br /&gt;&amp;nbsp;?? Success &#x3D; 4&lt;br /&gt;
         * @param {NotificationSourceEnum} [source] &lt;br /&gt;&amp;nbsp;???? DeviceManagement &#x3D; 1&lt;br /&gt;&amp;nbsp;???? DataForwarding &#x3D; 2&lt;br /&gt;&amp;nbsp;???? SystemManagement &#x3D; 3&lt;br /&gt;&amp;nbsp;???? DataCollection &#x3D; 4&lt;br /&gt;&amp;nbsp;????? WorkflowEngine &#x3D; 5&lt;br /&gt;&amp;nbsp;???? SecurityMonitoring &#x3D; 6&lt;br /&gt;&amp;nbsp;???? DeviceMonitoring &#x3D; 7&lt;br /&gt;
         * @param {boolean} [isRead] 
         * @param {string} [keyword] 
         * @param {Date} [startTime] 
         * @param {Date} [endTime] 
         * @param {string} [relatedEntityId] 
         * @param {string} [relatedEntityType] 
         * @param {*} [options] Override http request option.
         * @throws {RequiredError}
         */
        async getNotificationsAsync(page?: number, pageSize?: number, type?: NotificationTypeEnum, source?: NotificationSourceEnum, isRead?: boolean, keyword?: string, startTime?: Date, endTime?: Date, relatedEntityId?: string, relatedEntityType?: string, options?: AxiosRequestConfig): Promise<AxiosResponse<RESTfulResultSqlSugarPagedListNotificationResponseDto>> {
            return NotificationApiFp(configuration).getNotificationsAsync(page, pageSize, type, source, isRead, keyword, startTime, endTime, relatedEntityId, relatedEntityType, options).then((request) => request(axios, basePath));
        },
        /**
         * 
         * @param {*} [options] Override http request option.
         * @throws {RequiredError}
         */
        async getUnreadCountAsync(options?: AxiosRequestConfig): Promise<AxiosResponse<RESTfulResultInt32>> {
            return NotificationApiFp(configuration).getUnreadCountAsync(options).then((request) => request(axios, basePath));
        },
        /**
         * 
         * @param {*} [options] Override http request option.
         * @throws {RequiredError}
         */
        async markAllAsReadAsync(options?: AxiosRequestConfig): Promise<AxiosResponse<RESTfulResultInt32>> {
            return NotificationApiFp(configuration).markAllAsReadAsync(options).then((request) => request(axios, basePath));
        },
        /**
         * 
         * @param {number} id 
         * @param {*} [options] Override http request option.
         * @throws {RequiredError}
         */
        async markAsReadAsync(id: number, options?: AxiosRequestConfig): Promise<AxiosResponse<RESTfulResultBoolean>> {
            return NotificationApiFp(configuration).markAsReadAsync(id, options).then((request) => request(axios, basePath));
        },
    };
};

/**
 * NotificationApi - object-oriented interface
 * @export
 * @class NotificationApi
 * @extends {BaseAPI}
 */
export class NotificationApi extends BaseAPI {
    /**
     * 
     * @param {CreateNotificationDto} [body] 
     * @param {*} [options] Override http request option.
     * @throws {RequiredError}
     * @memberof NotificationApi
     */
    public async apiNotificationPushNotificationPost(body?: CreateNotificationDto, options?: AxiosRequestConfig) : Promise<AxiosResponse<RESTfulResultInt64>> {
        return NotificationApiFp(this.configuration).apiNotificationPushNotificationPost(body, options).then((request) => request(this.axios, this.basePath));
    }
    /**
     * 
     * @param {*} [options] Override http request option.
     * @throws {RequiredError}
     * @memberof NotificationApi
     */
    public async clearReadNotificationsAsync(options?: AxiosRequestConfig) : Promise<AxiosResponse<RESTfulResultInt32>> {
        return NotificationApiFp(this.configuration).clearReadNotificationsAsync(options).then((request) => request(this.axios, this.basePath));
    }
    /**
     * 
     * @param {CreateNotificationDto} [body] 
     * @param {*} [options] Override http request option.
     * @throws {RequiredError}
     * @memberof NotificationApi
     */
    public async createNotificationAsync(body?: CreateNotificationDto, options?: AxiosRequestConfig) : Promise<AxiosResponse<RESTfulResultInt64>> {
        return NotificationApiFp(this.configuration).createNotificationAsync(body, options).then((request) => request(this.axios, this.basePath));
    }
    /**
     * 
     * @param {number} id 
     * @param {*} [options] Override http request option.
     * @throws {RequiredError}
     * @memberof NotificationApi
     */
    public async deleteNotificationAsync(id: number, options?: AxiosRequestConfig) : Promise<AxiosResponse<RESTfulResultBoolean>> {
        return NotificationApiFp(this.configuration).deleteNotificationAsync(id, options).then((request) => request(this.axios, this.basePath));
    }
    /**
     * 
     * @param {number} id 
     * @param {*} [options] Override http request option.
     * @throws {RequiredError}
     * @memberof NotificationApi
     */
    public async getNotificationByIdAsync(id: number, options?: AxiosRequestConfig) : Promise<AxiosResponse<RESTfulResultNotificationResponseDto>> {
        return NotificationApiFp(this.configuration).getNotificationByIdAsync(id, options).then((request) => request(this.axios, this.basePath));
    }
    /**
     * 
     * @param {number} [page] 
     * @param {number} [pageSize] 
     * @param {NotificationTypeEnum} [type] &lt;br /&gt;&amp;nbsp;?? Info &#x3D; 1&lt;br /&gt;&amp;nbsp;?? Warning &#x3D; 2&lt;br /&gt;&amp;nbsp;?? Critical &#x3D; 3&lt;br /&gt;&amp;nbsp;?? Success &#x3D; 4&lt;br /&gt;
     * @param {NotificationSourceEnum} [source] &lt;br /&gt;&amp;nbsp;???? DeviceManagement &#x3D; 1&lt;br /&gt;&amp;nbsp;???? DataForwarding &#x3D; 2&lt;br /&gt;&amp;nbsp;???? SystemManagement &#x3D; 3&lt;br /&gt;&amp;nbsp;???? DataCollection &#x3D; 4&lt;br /&gt;&amp;nbsp;????? WorkflowEngine &#x3D; 5&lt;br /&gt;&amp;nbsp;???? SecurityMonitoring &#x3D; 6&lt;br /&gt;&amp;nbsp;???? DeviceMonitoring &#x3D; 7&lt;br /&gt;
     * @param {boolean} [isRead] 
     * @param {string} [keyword] 
     * @param {Date} [startTime] 
     * @param {Date} [endTime] 
     * @param {string} [relatedEntityId] 
     * @param {string} [relatedEntityType] 
     * @param {*} [options] Override http request option.
     * @throws {RequiredError}
     * @memberof NotificationApi
     */
    public async getNotificationsAsync(page?: number, pageSize?: number, type?: NotificationTypeEnum, source?: NotificationSourceEnum, isRead?: boolean, keyword?: string, startTime?: Date, endTime?: Date, relatedEntityId?: string, relatedEntityType?: string, options?: AxiosRequestConfig) : Promise<AxiosResponse<RESTfulResultSqlSugarPagedListNotificationResponseDto>> {
        return NotificationApiFp(this.configuration).getNotificationsAsync(page, pageSize, type, source, isRead, keyword, startTime, endTime, relatedEntityId, relatedEntityType, options).then((request) => request(this.axios, this.basePath));
    }
    /**
     * 
     * @param {*} [options] Override http request option.
     * @throws {RequiredError}
     * @memberof NotificationApi
     */
    public async getUnreadCountAsync(options?: AxiosRequestConfig) : Promise<AxiosResponse<RESTfulResultInt32>> {
        return NotificationApiFp(this.configuration).getUnreadCountAsync(options).then((request) => request(this.axios, this.basePath));
    }
    /**
     * 
     * @param {*} [options] Override http request option.
     * @throws {RequiredError}
     * @memberof NotificationApi
     */
    public async markAllAsReadAsync(options?: AxiosRequestConfig) : Promise<AxiosResponse<RESTfulResultInt32>> {
        return NotificationApiFp(this.configuration).markAllAsReadAsync(options).then((request) => request(this.axios, this.basePath));
    }
    /**
     * 
     * @param {number} id 
     * @param {*} [options] Override http request option.
     * @throws {RequiredError}
     * @memberof NotificationApi
     */
    public async markAsReadAsync(id: number, options?: AxiosRequestConfig) : Promise<AxiosResponse<RESTfulResultBoolean>> {
        return NotificationApiFp(this.configuration).markAsReadAsync(id, options).then((request) => request(this.axios, this.basePath));
    }
}
