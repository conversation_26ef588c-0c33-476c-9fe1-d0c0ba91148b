/* tslint:disable */
/* eslint-disable */
/**
 * All Groups
 * No description provided (generated by Swagger Codegen https://github.com/swagger-api/swagger-codegen)
 *
 * OpenAPI spec version: 1.0.0
 * 
 *
 * NOTE: This class is auto generated by the swagger code generator program.
 * https://github.com/swagger-api/swagger-codegen.git
 * Do not edit the class manually.
 */

import globalAxios, { AxiosResponse, AxiosInstance, AxiosRequestConfig } from 'axios';
import { Configuration } from '../configuration';
// Some imports not used depending on template conditions
// @ts-ignore
import { BASE_PATH, COLLECTION_FORMATS, RequestArgs, BaseAPI, RequiredError } from '../base';
import { DeviceInfo } from '../models';
import { RESTfulResultListObject } from '../models';
import { RESTfulResultObject } from '../models';
/**
 * SampleOpenApi - axios parameter creator
 * @export
 */
export const SampleOpenApiAxiosParamCreator = function (configuration?: Configuration) {
    return {
        /**
         * 
         * @summary ????
         * @param {DeviceInfo} [body] 
         * @param {*} [options] Override http request option.
         * @throws {RequiredError}
         */
        createDevice: async (body?: DeviceInfo, options: AxiosRequestConfig = {}): Promise<RequestArgs> => {
            const localVarPath = `/openapi/sample/devices`;
            // use dummy base URL string because the URL constructor only accepts absolute URLs.
            const localVarUrlObj = new URL(localVarPath, 'https://example.com');
            let baseOptions;
            if (configuration) {
                baseOptions = configuration.baseOptions;
            }
            const localVarRequestOptions :AxiosRequestConfig = { method: 'POST', ...baseOptions, ...options};
            const localVarHeaderParameter = {} as any;
            const localVarQueryParameter = {} as any;

            // authentication Bearer required
            // http bearer authentication required
            if (configuration && configuration.accessToken) {
                const accessToken = typeof configuration.accessToken === 'function'
                    ? await configuration.accessToken()
                    : await configuration.accessToken;
                localVarHeaderParameter["Authorization"] = "Bearer " + accessToken;
            }

            localVarHeaderParameter['Content-Type'] = 'application/json-patch+json';

            const query = new URLSearchParams(localVarUrlObj.search);
            for (const key in localVarQueryParameter) {
                query.set(key, localVarQueryParameter[key]);
            }
            for (const key in options.params) {
                query.set(key, options.params[key]);
            }
            localVarUrlObj.search = (new URLSearchParams(query)).toString();
            let headersFromBaseOptions = baseOptions && baseOptions.headers ? baseOptions.headers : {};
            localVarRequestOptions.headers = {...localVarHeaderParameter, ...headersFromBaseOptions, ...options.headers};
            const needsSerialization = (typeof body !== "string") || localVarRequestOptions.headers['Content-Type'] === 'application/json';
            localVarRequestOptions.data =  needsSerialization ? JSON.stringify(body !== undefined ? body : {}) : (body || "");

            return {
                url: localVarUrlObj.pathname + localVarUrlObj.search + localVarUrlObj.hash,
                options: localVarRequestOptions,
            };
        },
        /**
         * 
         * @summary ??????
         * @param {number} id 
         * @param {*} [options] Override http request option.
         * @throws {RequiredError}
         */
        getDeviceById: async (id: number, options: AxiosRequestConfig = {}): Promise<RequestArgs> => {
            // verify required parameter 'id' is not null or undefined
            if (id === null || id === undefined) {
                throw new RequiredError('id','Required parameter id was null or undefined when calling getDeviceById.');
            }
            const localVarPath = `/openapi/sample/devices/{id}`
                .replace(`{${"id"}}`, encodeURIComponent(String(id)));
            // use dummy base URL string because the URL constructor only accepts absolute URLs.
            const localVarUrlObj = new URL(localVarPath, 'https://example.com');
            let baseOptions;
            if (configuration) {
                baseOptions = configuration.baseOptions;
            }
            const localVarRequestOptions :AxiosRequestConfig = { method: 'GET', ...baseOptions, ...options};
            const localVarHeaderParameter = {} as any;
            const localVarQueryParameter = {} as any;

            // authentication Bearer required
            // http bearer authentication required
            if (configuration && configuration.accessToken) {
                const accessToken = typeof configuration.accessToken === 'function'
                    ? await configuration.accessToken()
                    : await configuration.accessToken;
                localVarHeaderParameter["Authorization"] = "Bearer " + accessToken;
            }

            const query = new URLSearchParams(localVarUrlObj.search);
            for (const key in localVarQueryParameter) {
                query.set(key, localVarQueryParameter[key]);
            }
            for (const key in options.params) {
                query.set(key, options.params[key]);
            }
            localVarUrlObj.search = (new URLSearchParams(query)).toString();
            let headersFromBaseOptions = baseOptions && baseOptions.headers ? baseOptions.headers : {};
            localVarRequestOptions.headers = {...localVarHeaderParameter, ...headersFromBaseOptions, ...options.headers};

            return {
                url: localVarUrlObj.pathname + localVarUrlObj.search + localVarUrlObj.hash,
                options: localVarRequestOptions,
            };
        },
        /**
         * 
         * @summary ??????
         * @param {*} [options] Override http request option.
         * @throws {RequiredError}
         */
        getDevices: async (options: AxiosRequestConfig = {}): Promise<RequestArgs> => {
            const localVarPath = `/openapi/sample/devices`;
            // use dummy base URL string because the URL constructor only accepts absolute URLs.
            const localVarUrlObj = new URL(localVarPath, 'https://example.com');
            let baseOptions;
            if (configuration) {
                baseOptions = configuration.baseOptions;
            }
            const localVarRequestOptions :AxiosRequestConfig = { method: 'GET', ...baseOptions, ...options};
            const localVarHeaderParameter = {} as any;
            const localVarQueryParameter = {} as any;

            // authentication Bearer required
            // http bearer authentication required
            if (configuration && configuration.accessToken) {
                const accessToken = typeof configuration.accessToken === 'function'
                    ? await configuration.accessToken()
                    : await configuration.accessToken;
                localVarHeaderParameter["Authorization"] = "Bearer " + accessToken;
            }

            const query = new URLSearchParams(localVarUrlObj.search);
            for (const key in localVarQueryParameter) {
                query.set(key, localVarQueryParameter[key]);
            }
            for (const key in options.params) {
                query.set(key, options.params[key]);
            }
            localVarUrlObj.search = (new URLSearchParams(query)).toString();
            let headersFromBaseOptions = baseOptions && baseOptions.headers ? baseOptions.headers : {};
            localVarRequestOptions.headers = {...localVarHeaderParameter, ...headersFromBaseOptions, ...options.headers};

            return {
                url: localVarUrlObj.pathname + localVarUrlObj.search + localVarUrlObj.hash,
                options: localVarRequestOptions,
            };
        },
        /**
         * 
         * @summary ??????
         * @param {*} [options] Override http request option.
         * @throws {RequiredError}
         */
        getSystemInfo: async (options: AxiosRequestConfig = {}): Promise<RequestArgs> => {
            const localVarPath = `/openapi/sample/system-info`;
            // use dummy base URL string because the URL constructor only accepts absolute URLs.
            const localVarUrlObj = new URL(localVarPath, 'https://example.com');
            let baseOptions;
            if (configuration) {
                baseOptions = configuration.baseOptions;
            }
            const localVarRequestOptions :AxiosRequestConfig = { method: 'GET', ...baseOptions, ...options};
            const localVarHeaderParameter = {} as any;
            const localVarQueryParameter = {} as any;

            // authentication Bearer required
            // http bearer authentication required
            if (configuration && configuration.accessToken) {
                const accessToken = typeof configuration.accessToken === 'function'
                    ? await configuration.accessToken()
                    : await configuration.accessToken;
                localVarHeaderParameter["Authorization"] = "Bearer " + accessToken;
            }

            const query = new URLSearchParams(localVarUrlObj.search);
            for (const key in localVarQueryParameter) {
                query.set(key, localVarQueryParameter[key]);
            }
            for (const key in options.params) {
                query.set(key, options.params[key]);
            }
            localVarUrlObj.search = (new URLSearchParams(query)).toString();
            let headersFromBaseOptions = baseOptions && baseOptions.headers ? baseOptions.headers : {};
            localVarRequestOptions.headers = {...localVarHeaderParameter, ...headersFromBaseOptions, ...options.headers};

            return {
                url: localVarUrlObj.pathname + localVarUrlObj.search + localVarUrlObj.hash,
                options: localVarRequestOptions,
            };
        },
    }
};

/**
 * SampleOpenApi - functional programming interface
 * @export
 */
export const SampleOpenApiFp = function(configuration?: Configuration) {
    return {
        /**
         * 
         * @summary ????
         * @param {DeviceInfo} [body] 
         * @param {*} [options] Override http request option.
         * @throws {RequiredError}
         */
        async createDevice(body?: DeviceInfo, options?: AxiosRequestConfig): Promise<(axios?: AxiosInstance, basePath?: string) => Promise<AxiosResponse<RESTfulResultObject>>> {
            const localVarAxiosArgs = await SampleOpenApiAxiosParamCreator(configuration).createDevice(body, options);
            return (axios: AxiosInstance = globalAxios, basePath: string = BASE_PATH) => {
                const axiosRequestArgs :AxiosRequestConfig = {...localVarAxiosArgs.options, url: basePath + localVarAxiosArgs.url};
                return axios.request(axiosRequestArgs);
            };
        },
        /**
         * 
         * @summary ??????
         * @param {number} id 
         * @param {*} [options] Override http request option.
         * @throws {RequiredError}
         */
        async getDeviceById(id: number, options?: AxiosRequestConfig): Promise<(axios?: AxiosInstance, basePath?: string) => Promise<AxiosResponse<RESTfulResultObject>>> {
            const localVarAxiosArgs = await SampleOpenApiAxiosParamCreator(configuration).getDeviceById(id, options);
            return (axios: AxiosInstance = globalAxios, basePath: string = BASE_PATH) => {
                const axiosRequestArgs :AxiosRequestConfig = {...localVarAxiosArgs.options, url: basePath + localVarAxiosArgs.url};
                return axios.request(axiosRequestArgs);
            };
        },
        /**
         * 
         * @summary ??????
         * @param {*} [options] Override http request option.
         * @throws {RequiredError}
         */
        async getDevices(options?: AxiosRequestConfig): Promise<(axios?: AxiosInstance, basePath?: string) => Promise<AxiosResponse<RESTfulResultListObject>>> {
            const localVarAxiosArgs = await SampleOpenApiAxiosParamCreator(configuration).getDevices(options);
            return (axios: AxiosInstance = globalAxios, basePath: string = BASE_PATH) => {
                const axiosRequestArgs :AxiosRequestConfig = {...localVarAxiosArgs.options, url: basePath + localVarAxiosArgs.url};
                return axios.request(axiosRequestArgs);
            };
        },
        /**
         * 
         * @summary ??????
         * @param {*} [options] Override http request option.
         * @throws {RequiredError}
         */
        async getSystemInfo(options?: AxiosRequestConfig): Promise<(axios?: AxiosInstance, basePath?: string) => Promise<AxiosResponse<RESTfulResultObject>>> {
            const localVarAxiosArgs = await SampleOpenApiAxiosParamCreator(configuration).getSystemInfo(options);
            return (axios: AxiosInstance = globalAxios, basePath: string = BASE_PATH) => {
                const axiosRequestArgs :AxiosRequestConfig = {...localVarAxiosArgs.options, url: basePath + localVarAxiosArgs.url};
                return axios.request(axiosRequestArgs);
            };
        },
    }
};

/**
 * SampleOpenApi - factory interface
 * @export
 */
export const SampleOpenApiFactory = function (configuration?: Configuration, basePath?: string, axios?: AxiosInstance) {
    return {
        /**
         * 
         * @summary ????
         * @param {DeviceInfo} [body] 
         * @param {*} [options] Override http request option.
         * @throws {RequiredError}
         */
        async createDevice(body?: DeviceInfo, options?: AxiosRequestConfig): Promise<AxiosResponse<RESTfulResultObject>> {
            return SampleOpenApiFp(configuration).createDevice(body, options).then((request) => request(axios, basePath));
        },
        /**
         * 
         * @summary ??????
         * @param {number} id 
         * @param {*} [options] Override http request option.
         * @throws {RequiredError}
         */
        async getDeviceById(id: number, options?: AxiosRequestConfig): Promise<AxiosResponse<RESTfulResultObject>> {
            return SampleOpenApiFp(configuration).getDeviceById(id, options).then((request) => request(axios, basePath));
        },
        /**
         * 
         * @summary ??????
         * @param {*} [options] Override http request option.
         * @throws {RequiredError}
         */
        async getDevices(options?: AxiosRequestConfig): Promise<AxiosResponse<RESTfulResultListObject>> {
            return SampleOpenApiFp(configuration).getDevices(options).then((request) => request(axios, basePath));
        },
        /**
         * 
         * @summary ??????
         * @param {*} [options] Override http request option.
         * @throws {RequiredError}
         */
        async getSystemInfo(options?: AxiosRequestConfig): Promise<AxiosResponse<RESTfulResultObject>> {
            return SampleOpenApiFp(configuration).getSystemInfo(options).then((request) => request(axios, basePath));
        },
    };
};

/**
 * SampleOpenApi - object-oriented interface
 * @export
 * @class SampleOpenApi
 * @extends {BaseAPI}
 */
export class SampleOpenApi extends BaseAPI {
    /**
     * 
     * @summary ????
     * @param {DeviceInfo} [body] 
     * @param {*} [options] Override http request option.
     * @throws {RequiredError}
     * @memberof SampleOpenApi
     */
    public async createDevice(body?: DeviceInfo, options?: AxiosRequestConfig) : Promise<AxiosResponse<RESTfulResultObject>> {
        return SampleOpenApiFp(this.configuration).createDevice(body, options).then((request) => request(this.axios, this.basePath));
    }
    /**
     * 
     * @summary ??????
     * @param {number} id 
     * @param {*} [options] Override http request option.
     * @throws {RequiredError}
     * @memberof SampleOpenApi
     */
    public async getDeviceById(id: number, options?: AxiosRequestConfig) : Promise<AxiosResponse<RESTfulResultObject>> {
        return SampleOpenApiFp(this.configuration).getDeviceById(id, options).then((request) => request(this.axios, this.basePath));
    }
    /**
     * 
     * @summary ??????
     * @param {*} [options] Override http request option.
     * @throws {RequiredError}
     * @memberof SampleOpenApi
     */
    public async getDevices(options?: AxiosRequestConfig) : Promise<AxiosResponse<RESTfulResultListObject>> {
        return SampleOpenApiFp(this.configuration).getDevices(options).then((request) => request(this.axios, this.basePath));
    }
    /**
     * 
     * @summary ??????
     * @param {*} [options] Override http request option.
     * @throws {RequiredError}
     * @memberof SampleOpenApi
     */
    public async getSystemInfo(options?: AxiosRequestConfig) : Promise<AxiosResponse<RESTfulResultObject>> {
        return SampleOpenApiFp(this.configuration).getSystemInfo(options).then((request) => request(this.axios, this.basePath));
    }
}
