/* tslint:disable */
/* eslint-disable */
/**
 * All Groups
 * No description provided (generated by Swagger Codegen https://github.com/swagger-api/swagger-codegen)
 *
 * OpenAPI spec version: 1.0.0
 * 
 *
 * NOTE: This class is auto generated by the swagger code generator program.
 * https://github.com/swagger-api/swagger-codegen.git
 * Do not edit the class manually.
 */

import globalAxios, { AxiosResponse, AxiosInstance, AxiosRequestConfig } from 'axios';
import { Configuration } from '../configuration';
// Some imports not used depending on template conditions
// @ts-ignore
import { BASE_PATH, COLLECTION_FORMATS, RequestArgs, BaseAPI, RequiredError } from '../base';
import { MenuCreateInput } from '../models';
import { MenuTypeEnum } from '../models';
import { MenuUpdateInput } from '../models';
import { RESTfulResultInt64 } from '../models';
import { RESTfulResultListMenuTreeOutput } from '../models';
import { RESTfulResultSqlSugarPagedListMenuOutput } from '../models';
/**
 * SysMenuApi - axios parameter creator
 * @export
 */
export const SysMenuApiAxiosParamCreator = function (configuration?: Configuration) {
    return {
        /**
         * 
         * @summary ????
         * @param {number} id 
         * @param {*} [options] Override http request option.
         * @throws {RequiredError}
         */
        apiSysMenuIdDelete: async (id: number, options: AxiosRequestConfig = {}): Promise<RequestArgs> => {
            // verify required parameter 'id' is not null or undefined
            if (id === null || id === undefined) {
                throw new RequiredError('id','Required parameter id was null or undefined when calling apiSysMenuIdDelete.');
            }
            const localVarPath = `/api/sysMenu/{id}`
                .replace(`{${"id"}}`, encodeURIComponent(String(id)));
            // use dummy base URL string because the URL constructor only accepts absolute URLs.
            const localVarUrlObj = new URL(localVarPath, 'https://example.com');
            let baseOptions;
            if (configuration) {
                baseOptions = configuration.baseOptions;
            }
            const localVarRequestOptions :AxiosRequestConfig = { method: 'DELETE', ...baseOptions, ...options};
            const localVarHeaderParameter = {} as any;
            const localVarQueryParameter = {} as any;

            // authentication Bearer required
            // http bearer authentication required
            if (configuration && configuration.accessToken) {
                const accessToken = typeof configuration.accessToken === 'function'
                    ? await configuration.accessToken()
                    : await configuration.accessToken;
                localVarHeaderParameter["Authorization"] = "Bearer " + accessToken;
            }

            const query = new URLSearchParams(localVarUrlObj.search);
            for (const key in localVarQueryParameter) {
                query.set(key, localVarQueryParameter[key]);
            }
            for (const key in options.params) {
                query.set(key, options.params[key]);
            }
            localVarUrlObj.search = (new URLSearchParams(query)).toString();
            let headersFromBaseOptions = baseOptions && baseOptions.headers ? baseOptions.headers : {};
            localVarRequestOptions.headers = {...localVarHeaderParameter, ...headersFromBaseOptions, ...options.headers};

            return {
                url: localVarUrlObj.pathname + localVarUrlObj.search + localVarUrlObj.hash,
                options: localVarRequestOptions,
            };
        },
        /**
         * 
         * @summary ????
         * @param {MenuCreateInput} body 
         * @param {*} [options] Override http request option.
         * @throws {RequiredError}
         */
        apiSysMenuMenuPost: async (body: MenuCreateInput, options: AxiosRequestConfig = {}): Promise<RequestArgs> => {
            // verify required parameter 'body' is not null or undefined
            if (body === null || body === undefined) {
                throw new RequiredError('body','Required parameter body was null or undefined when calling apiSysMenuMenuPost.');
            }
            const localVarPath = `/api/sysMenu/menu`;
            // use dummy base URL string because the URL constructor only accepts absolute URLs.
            const localVarUrlObj = new URL(localVarPath, 'https://example.com');
            let baseOptions;
            if (configuration) {
                baseOptions = configuration.baseOptions;
            }
            const localVarRequestOptions :AxiosRequestConfig = { method: 'POST', ...baseOptions, ...options};
            const localVarHeaderParameter = {} as any;
            const localVarQueryParameter = {} as any;

            // authentication Bearer required
            // http bearer authentication required
            if (configuration && configuration.accessToken) {
                const accessToken = typeof configuration.accessToken === 'function'
                    ? await configuration.accessToken()
                    : await configuration.accessToken;
                localVarHeaderParameter["Authorization"] = "Bearer " + accessToken;
            }

            localVarHeaderParameter['Content-Type'] = 'application/json-patch+json';

            const query = new URLSearchParams(localVarUrlObj.search);
            for (const key in localVarQueryParameter) {
                query.set(key, localVarQueryParameter[key]);
            }
            for (const key in options.params) {
                query.set(key, options.params[key]);
            }
            localVarUrlObj.search = (new URLSearchParams(query)).toString();
            let headersFromBaseOptions = baseOptions && baseOptions.headers ? baseOptions.headers : {};
            localVarRequestOptions.headers = {...localVarHeaderParameter, ...headersFromBaseOptions, ...options.headers};
            const needsSerialization = (typeof body !== "string") || localVarRequestOptions.headers['Content-Type'] === 'application/json';
            localVarRequestOptions.data =  needsSerialization ? JSON.stringify(body !== undefined ? body : {}) : (body || "");

            return {
                url: localVarUrlObj.pathname + localVarUrlObj.search + localVarUrlObj.hash,
                options: localVarRequestOptions,
            };
        },
        /**
         * 
         * @summary ????
         * @param {MenuUpdateInput} body 
         * @param {*} [options] Override http request option.
         * @throws {RequiredError}
         */
        apiSysMenuMenuPut: async (body: MenuUpdateInput, options: AxiosRequestConfig = {}): Promise<RequestArgs> => {
            // verify required parameter 'body' is not null or undefined
            if (body === null || body === undefined) {
                throw new RequiredError('body','Required parameter body was null or undefined when calling apiSysMenuMenuPut.');
            }
            const localVarPath = `/api/sysMenu/menu`;
            // use dummy base URL string because the URL constructor only accepts absolute URLs.
            const localVarUrlObj = new URL(localVarPath, 'https://example.com');
            let baseOptions;
            if (configuration) {
                baseOptions = configuration.baseOptions;
            }
            const localVarRequestOptions :AxiosRequestConfig = { method: 'PUT', ...baseOptions, ...options};
            const localVarHeaderParameter = {} as any;
            const localVarQueryParameter = {} as any;

            // authentication Bearer required
            // http bearer authentication required
            if (configuration && configuration.accessToken) {
                const accessToken = typeof configuration.accessToken === 'function'
                    ? await configuration.accessToken()
                    : await configuration.accessToken;
                localVarHeaderParameter["Authorization"] = "Bearer " + accessToken;
            }

            localVarHeaderParameter['Content-Type'] = 'application/json-patch+json';

            const query = new URLSearchParams(localVarUrlObj.search);
            for (const key in localVarQueryParameter) {
                query.set(key, localVarQueryParameter[key]);
            }
            for (const key in options.params) {
                query.set(key, options.params[key]);
            }
            localVarUrlObj.search = (new URLSearchParams(query)).toString();
            let headersFromBaseOptions = baseOptions && baseOptions.headers ? baseOptions.headers : {};
            localVarRequestOptions.headers = {...localVarHeaderParameter, ...headersFromBaseOptions, ...options.headers};
            const needsSerialization = (typeof body !== "string") || localVarRequestOptions.headers['Content-Type'] === 'application/json';
            localVarRequestOptions.data =  needsSerialization ? JSON.stringify(body !== undefined ? body : {}) : (body || "");

            return {
                url: localVarUrlObj.pathname + localVarUrlObj.search + localVarUrlObj.hash,
                options: localVarRequestOptions,
            };
        },
        /**
         * 
         * @summary ????????
         * @param {number} [page] ??
         * @param {number} [pageSize] ???
         * @param {string} [name] ????
         * @param {string} [code] ????
         * @param {number} [parentId] ????ID
         * @param {MenuTypeEnum} [menuType] ????
         * @param {boolean} [status] ??
         * @param {*} [options] Override http request option.
         * @throws {RequiredError}
         */
        apiSysMenuPageGet: async (page?: number, pageSize?: number, name?: string, code?: string, parentId?: number, menuType?: MenuTypeEnum, status?: boolean, options: AxiosRequestConfig = {}): Promise<RequestArgs> => {
            const localVarPath = `/api/sysMenu/page`;
            // use dummy base URL string because the URL constructor only accepts absolute URLs.
            const localVarUrlObj = new URL(localVarPath, 'https://example.com');
            let baseOptions;
            if (configuration) {
                baseOptions = configuration.baseOptions;
            }
            const localVarRequestOptions :AxiosRequestConfig = { method: 'GET', ...baseOptions, ...options};
            const localVarHeaderParameter = {} as any;
            const localVarQueryParameter = {} as any;

            // authentication Bearer required
            // http bearer authentication required
            if (configuration && configuration.accessToken) {
                const accessToken = typeof configuration.accessToken === 'function'
                    ? await configuration.accessToken()
                    : await configuration.accessToken;
                localVarHeaderParameter["Authorization"] = "Bearer " + accessToken;
            }

            if (page !== undefined) {
                localVarQueryParameter['Page'] = page;
            }

            if (pageSize !== undefined) {
                localVarQueryParameter['PageSize'] = pageSize;
            }

            if (name !== undefined) {
                localVarQueryParameter['Name'] = name;
            }

            if (code !== undefined) {
                localVarQueryParameter['Code'] = code;
            }

            if (parentId !== undefined) {
                localVarQueryParameter['ParentId'] = parentId;
            }

            if (menuType !== undefined) {
                localVarQueryParameter['MenuType'] = menuType;
            }

            if (status !== undefined) {
                localVarQueryParameter['Status'] = status;
            }

            const query = new URLSearchParams(localVarUrlObj.search);
            for (const key in localVarQueryParameter) {
                query.set(key, localVarQueryParameter[key]);
            }
            for (const key in options.params) {
                query.set(key, options.params[key]);
            }
            localVarUrlObj.search = (new URLSearchParams(query)).toString();
            let headersFromBaseOptions = baseOptions && baseOptions.headers ? baseOptions.headers : {};
            localVarRequestOptions.headers = {...localVarHeaderParameter, ...headersFromBaseOptions, ...options.headers};

            return {
                url: localVarUrlObj.pathname + localVarUrlObj.search + localVarUrlObj.hash,
                options: localVarRequestOptions,
            };
        },
        /**
         * 
         * @summary ?????
         * @param {*} [options] Override http request option.
         * @throws {RequiredError}
         */
        apiSysMenuTreeGet: async (options: AxiosRequestConfig = {}): Promise<RequestArgs> => {
            const localVarPath = `/api/sysMenu/tree`;
            // use dummy base URL string because the URL constructor only accepts absolute URLs.
            const localVarUrlObj = new URL(localVarPath, 'https://example.com');
            let baseOptions;
            if (configuration) {
                baseOptions = configuration.baseOptions;
            }
            const localVarRequestOptions :AxiosRequestConfig = { method: 'GET', ...baseOptions, ...options};
            const localVarHeaderParameter = {} as any;
            const localVarQueryParameter = {} as any;

            // authentication Bearer required
            // http bearer authentication required
            if (configuration && configuration.accessToken) {
                const accessToken = typeof configuration.accessToken === 'function'
                    ? await configuration.accessToken()
                    : await configuration.accessToken;
                localVarHeaderParameter["Authorization"] = "Bearer " + accessToken;
            }

            const query = new URLSearchParams(localVarUrlObj.search);
            for (const key in localVarQueryParameter) {
                query.set(key, localVarQueryParameter[key]);
            }
            for (const key in options.params) {
                query.set(key, options.params[key]);
            }
            localVarUrlObj.search = (new URLSearchParams(query)).toString();
            let headersFromBaseOptions = baseOptions && baseOptions.headers ? baseOptions.headers : {};
            localVarRequestOptions.headers = {...localVarHeaderParameter, ...headersFromBaseOptions, ...options.headers};

            return {
                url: localVarUrlObj.pathname + localVarUrlObj.search + localVarUrlObj.hash,
                options: localVarRequestOptions,
            };
        },
    }
};

/**
 * SysMenuApi - functional programming interface
 * @export
 */
export const SysMenuApiFp = function(configuration?: Configuration) {
    return {
        /**
         * 
         * @summary ????
         * @param {number} id 
         * @param {*} [options] Override http request option.
         * @throws {RequiredError}
         */
        async apiSysMenuIdDelete(id: number, options?: AxiosRequestConfig): Promise<(axios?: AxiosInstance, basePath?: string) => Promise<AxiosResponse<void>>> {
            const localVarAxiosArgs = await SysMenuApiAxiosParamCreator(configuration).apiSysMenuIdDelete(id, options);
            return (axios: AxiosInstance = globalAxios, basePath: string = BASE_PATH) => {
                const axiosRequestArgs :AxiosRequestConfig = {...localVarAxiosArgs.options, url: basePath + localVarAxiosArgs.url};
                return axios.request(axiosRequestArgs);
            };
        },
        /**
         * 
         * @summary ????
         * @param {MenuCreateInput} body 
         * @param {*} [options] Override http request option.
         * @throws {RequiredError}
         */
        async apiSysMenuMenuPost(body: MenuCreateInput, options?: AxiosRequestConfig): Promise<(axios?: AxiosInstance, basePath?: string) => Promise<AxiosResponse<RESTfulResultInt64>>> {
            const localVarAxiosArgs = await SysMenuApiAxiosParamCreator(configuration).apiSysMenuMenuPost(body, options);
            return (axios: AxiosInstance = globalAxios, basePath: string = BASE_PATH) => {
                const axiosRequestArgs :AxiosRequestConfig = {...localVarAxiosArgs.options, url: basePath + localVarAxiosArgs.url};
                return axios.request(axiosRequestArgs);
            };
        },
        /**
         * 
         * @summary ????
         * @param {MenuUpdateInput} body 
         * @param {*} [options] Override http request option.
         * @throws {RequiredError}
         */
        async apiSysMenuMenuPut(body: MenuUpdateInput, options?: AxiosRequestConfig): Promise<(axios?: AxiosInstance, basePath?: string) => Promise<AxiosResponse<void>>> {
            const localVarAxiosArgs = await SysMenuApiAxiosParamCreator(configuration).apiSysMenuMenuPut(body, options);
            return (axios: AxiosInstance = globalAxios, basePath: string = BASE_PATH) => {
                const axiosRequestArgs :AxiosRequestConfig = {...localVarAxiosArgs.options, url: basePath + localVarAxiosArgs.url};
                return axios.request(axiosRequestArgs);
            };
        },
        /**
         * 
         * @summary ????????
         * @param {number} [page] ??
         * @param {number} [pageSize] ???
         * @param {string} [name] ????
         * @param {string} [code] ????
         * @param {number} [parentId] ????ID
         * @param {MenuTypeEnum} [menuType] ????
         * @param {boolean} [status] ??
         * @param {*} [options] Override http request option.
         * @throws {RequiredError}
         */
        async apiSysMenuPageGet(page?: number, pageSize?: number, name?: string, code?: string, parentId?: number, menuType?: MenuTypeEnum, status?: boolean, options?: AxiosRequestConfig): Promise<(axios?: AxiosInstance, basePath?: string) => Promise<AxiosResponse<RESTfulResultSqlSugarPagedListMenuOutput>>> {
            const localVarAxiosArgs = await SysMenuApiAxiosParamCreator(configuration).apiSysMenuPageGet(page, pageSize, name, code, parentId, menuType, status, options);
            return (axios: AxiosInstance = globalAxios, basePath: string = BASE_PATH) => {
                const axiosRequestArgs :AxiosRequestConfig = {...localVarAxiosArgs.options, url: basePath + localVarAxiosArgs.url};
                return axios.request(axiosRequestArgs);
            };
        },
        /**
         * 
         * @summary ?????
         * @param {*} [options] Override http request option.
         * @throws {RequiredError}
         */
        async apiSysMenuTreeGet(options?: AxiosRequestConfig): Promise<(axios?: AxiosInstance, basePath?: string) => Promise<AxiosResponse<RESTfulResultListMenuTreeOutput>>> {
            const localVarAxiosArgs = await SysMenuApiAxiosParamCreator(configuration).apiSysMenuTreeGet(options);
            return (axios: AxiosInstance = globalAxios, basePath: string = BASE_PATH) => {
                const axiosRequestArgs :AxiosRequestConfig = {...localVarAxiosArgs.options, url: basePath + localVarAxiosArgs.url};
                return axios.request(axiosRequestArgs);
            };
        },
    }
};

/**
 * SysMenuApi - factory interface
 * @export
 */
export const SysMenuApiFactory = function (configuration?: Configuration, basePath?: string, axios?: AxiosInstance) {
    return {
        /**
         * 
         * @summary ????
         * @param {number} id 
         * @param {*} [options] Override http request option.
         * @throws {RequiredError}
         */
        async apiSysMenuIdDelete(id: number, options?: AxiosRequestConfig): Promise<AxiosResponse<void>> {
            return SysMenuApiFp(configuration).apiSysMenuIdDelete(id, options).then((request) => request(axios, basePath));
        },
        /**
         * 
         * @summary ????
         * @param {MenuCreateInput} body 
         * @param {*} [options] Override http request option.
         * @throws {RequiredError}
         */
        async apiSysMenuMenuPost(body: MenuCreateInput, options?: AxiosRequestConfig): Promise<AxiosResponse<RESTfulResultInt64>> {
            return SysMenuApiFp(configuration).apiSysMenuMenuPost(body, options).then((request) => request(axios, basePath));
        },
        /**
         * 
         * @summary ????
         * @param {MenuUpdateInput} body 
         * @param {*} [options] Override http request option.
         * @throws {RequiredError}
         */
        async apiSysMenuMenuPut(body: MenuUpdateInput, options?: AxiosRequestConfig): Promise<AxiosResponse<void>> {
            return SysMenuApiFp(configuration).apiSysMenuMenuPut(body, options).then((request) => request(axios, basePath));
        },
        /**
         * 
         * @summary ????????
         * @param {number} [page] ??
         * @param {number} [pageSize] ???
         * @param {string} [name] ????
         * @param {string} [code] ????
         * @param {number} [parentId] ????ID
         * @param {MenuTypeEnum} [menuType] ????
         * @param {boolean} [status] ??
         * @param {*} [options] Override http request option.
         * @throws {RequiredError}
         */
        async apiSysMenuPageGet(page?: number, pageSize?: number, name?: string, code?: string, parentId?: number, menuType?: MenuTypeEnum, status?: boolean, options?: AxiosRequestConfig): Promise<AxiosResponse<RESTfulResultSqlSugarPagedListMenuOutput>> {
            return SysMenuApiFp(configuration).apiSysMenuPageGet(page, pageSize, name, code, parentId, menuType, status, options).then((request) => request(axios, basePath));
        },
        /**
         * 
         * @summary ?????
         * @param {*} [options] Override http request option.
         * @throws {RequiredError}
         */
        async apiSysMenuTreeGet(options?: AxiosRequestConfig): Promise<AxiosResponse<RESTfulResultListMenuTreeOutput>> {
            return SysMenuApiFp(configuration).apiSysMenuTreeGet(options).then((request) => request(axios, basePath));
        },
    };
};

/**
 * SysMenuApi - object-oriented interface
 * @export
 * @class SysMenuApi
 * @extends {BaseAPI}
 */
export class SysMenuApi extends BaseAPI {
    /**
     * 
     * @summary ????
     * @param {number} id 
     * @param {*} [options] Override http request option.
     * @throws {RequiredError}
     * @memberof SysMenuApi
     */
    public async apiSysMenuIdDelete(id: number, options?: AxiosRequestConfig) : Promise<AxiosResponse<void>> {
        return SysMenuApiFp(this.configuration).apiSysMenuIdDelete(id, options).then((request) => request(this.axios, this.basePath));
    }
    /**
     * 
     * @summary ????
     * @param {MenuCreateInput} body 
     * @param {*} [options] Override http request option.
     * @throws {RequiredError}
     * @memberof SysMenuApi
     */
    public async apiSysMenuMenuPost(body: MenuCreateInput, options?: AxiosRequestConfig) : Promise<AxiosResponse<RESTfulResultInt64>> {
        return SysMenuApiFp(this.configuration).apiSysMenuMenuPost(body, options).then((request) => request(this.axios, this.basePath));
    }
    /**
     * 
     * @summary ????
     * @param {MenuUpdateInput} body 
     * @param {*} [options] Override http request option.
     * @throws {RequiredError}
     * @memberof SysMenuApi
     */
    public async apiSysMenuMenuPut(body: MenuUpdateInput, options?: AxiosRequestConfig) : Promise<AxiosResponse<void>> {
        return SysMenuApiFp(this.configuration).apiSysMenuMenuPut(body, options).then((request) => request(this.axios, this.basePath));
    }
    /**
     * 
     * @summary ????????
     * @param {number} [page] ??
     * @param {number} [pageSize] ???
     * @param {string} [name] ????
     * @param {string} [code] ????
     * @param {number} [parentId] ????ID
     * @param {MenuTypeEnum} [menuType] ????
     * @param {boolean} [status] ??
     * @param {*} [options] Override http request option.
     * @throws {RequiredError}
     * @memberof SysMenuApi
     */
    public async apiSysMenuPageGet(page?: number, pageSize?: number, name?: string, code?: string, parentId?: number, menuType?: MenuTypeEnum, status?: boolean, options?: AxiosRequestConfig) : Promise<AxiosResponse<RESTfulResultSqlSugarPagedListMenuOutput>> {
        return SysMenuApiFp(this.configuration).apiSysMenuPageGet(page, pageSize, name, code, parentId, menuType, status, options).then((request) => request(this.axios, this.basePath));
    }
    /**
     * 
     * @summary ?????
     * @param {*} [options] Override http request option.
     * @throws {RequiredError}
     * @memberof SysMenuApi
     */
    public async apiSysMenuTreeGet(options?: AxiosRequestConfig) : Promise<AxiosResponse<RESTfulResultListMenuTreeOutput>> {
        return SysMenuApiFp(this.configuration).apiSysMenuTreeGet(options).then((request) => request(this.axios, this.basePath));
    }
}
