/* tslint:disable */
/* eslint-disable */
/**
 * All Groups
 * No description provided (generated by Swagger Codegen https://github.com/swagger-api/swagger-codegen)
 *
 * OpenAPI spec version: 1.0.0
 * 
 *
 * NOTE: This class is auto generated by the swagger code generator program.
 * https://github.com/swagger-api/swagger-codegen.git
 * Do not edit the class manually.
 */

import globalAxios, { AxiosResponse, AxiosInstance, AxiosRequestConfig } from 'axios';
import { Configuration } from '../configuration';
// Some imports not used depending on template conditions
// @ts-ignore
import { BASE_PATH, COLLECTION_FORMATS, RequestArgs, BaseAPI, RequiredError } from '../base';
import { CreateEventRequest } from '../models';
import { RESTfulResultActionResultBoolean } from '../models';
import { RESTfulResultActionResultEventDetailResponse } from '../models';
import { RESTfulResultActionResultInt64 } from '../models';
import { RESTfulResultActionResultListEventResponse } from '../models';
import { UpdateEventRequest } from '../models';
/**
 * DeviceEventApi - axios parameter creator
 * @export
 */
export const DeviceEventApiAxiosParamCreator = function (configuration?: Configuration) {
    return {
        /**
         * 
         * @summary ??????????
         * @param {number} deviceId ??ID
         * @param {string} [eventName] ????
         * @param {number} [excludeEventId] ?????ID
         * @param {*} [options] Override http request option.
         * @throws {RequiredError}
         */
        apiDeviceEventDevicesDeviceIdEventsCheckNameGet: async (deviceId: number, eventName?: string, excludeEventId?: number, options: AxiosRequestConfig = {}): Promise<RequestArgs> => {
            // verify required parameter 'deviceId' is not null or undefined
            if (deviceId === null || deviceId === undefined) {
                throw new RequiredError('deviceId','Required parameter deviceId was null or undefined when calling apiDeviceEventDevicesDeviceIdEventsCheckNameGet.');
            }
            const localVarPath = `/api/deviceEvent/devices/{deviceId}/events/check-name`
                .replace(`{${"deviceId"}}`, encodeURIComponent(String(deviceId)));
            // use dummy base URL string because the URL constructor only accepts absolute URLs.
            const localVarUrlObj = new URL(localVarPath, 'https://example.com');
            let baseOptions;
            if (configuration) {
                baseOptions = configuration.baseOptions;
            }
            const localVarRequestOptions :AxiosRequestConfig = { method: 'GET', ...baseOptions, ...options};
            const localVarHeaderParameter = {} as any;
            const localVarQueryParameter = {} as any;

            // authentication Bearer required
            // http bearer authentication required
            if (configuration && configuration.accessToken) {
                const accessToken = typeof configuration.accessToken === 'function'
                    ? await configuration.accessToken()
                    : await configuration.accessToken;
                localVarHeaderParameter["Authorization"] = "Bearer " + accessToken;
            }

            if (eventName !== undefined) {
                localVarQueryParameter['eventName'] = eventName;
            }

            if (excludeEventId !== undefined) {
                localVarQueryParameter['excludeEventId'] = excludeEventId;
            }

            const query = new URLSearchParams(localVarUrlObj.search);
            for (const key in localVarQueryParameter) {
                query.set(key, localVarQueryParameter[key]);
            }
            for (const key in options.params) {
                query.set(key, options.params[key]);
            }
            localVarUrlObj.search = (new URLSearchParams(query)).toString();
            let headersFromBaseOptions = baseOptions && baseOptions.headers ? baseOptions.headers : {};
            localVarRequestOptions.headers = {...localVarHeaderParameter, ...headersFromBaseOptions, ...options.headers};

            return {
                url: localVarUrlObj.pathname + localVarUrlObj.search + localVarUrlObj.hash,
                options: localVarRequestOptions,
            };
        },
        /**
         * 
         * @summary ?????????
         * @param {number} deviceId ??ID
         * @param {*} [options] Override http request option.
         * @throws {RequiredError}
         */
        apiDeviceEventDevicesDeviceIdEventsGet: async (deviceId: number, options: AxiosRequestConfig = {}): Promise<RequestArgs> => {
            // verify required parameter 'deviceId' is not null or undefined
            if (deviceId === null || deviceId === undefined) {
                throw new RequiredError('deviceId','Required parameter deviceId was null or undefined when calling apiDeviceEventDevicesDeviceIdEventsGet.');
            }
            const localVarPath = `/api/deviceEvent/devices/{deviceId}/events`
                .replace(`{${"deviceId"}}`, encodeURIComponent(String(deviceId)));
            // use dummy base URL string because the URL constructor only accepts absolute URLs.
            const localVarUrlObj = new URL(localVarPath, 'https://example.com');
            let baseOptions;
            if (configuration) {
                baseOptions = configuration.baseOptions;
            }
            const localVarRequestOptions :AxiosRequestConfig = { method: 'GET', ...baseOptions, ...options};
            const localVarHeaderParameter = {} as any;
            const localVarQueryParameter = {} as any;

            // authentication Bearer required
            // http bearer authentication required
            if (configuration && configuration.accessToken) {
                const accessToken = typeof configuration.accessToken === 'function'
                    ? await configuration.accessToken()
                    : await configuration.accessToken;
                localVarHeaderParameter["Authorization"] = "Bearer " + accessToken;
            }

            const query = new URLSearchParams(localVarUrlObj.search);
            for (const key in localVarQueryParameter) {
                query.set(key, localVarQueryParameter[key]);
            }
            for (const key in options.params) {
                query.set(key, options.params[key]);
            }
            localVarUrlObj.search = (new URLSearchParams(query)).toString();
            let headersFromBaseOptions = baseOptions && baseOptions.headers ? baseOptions.headers : {};
            localVarRequestOptions.headers = {...localVarHeaderParameter, ...headersFromBaseOptions, ...options.headers};

            return {
                url: localVarUrlObj.pathname + localVarUrlObj.search + localVarUrlObj.hash,
                options: localVarRequestOptions,
            };
        },
        /**
         * 
         * @summary ??????
         * @param {number} eventId ??ID
         * @param {*} [options] Override http request option.
         * @throws {RequiredError}
         */
        apiDeviceEventEventsEventIdDelete: async (eventId: number, options: AxiosRequestConfig = {}): Promise<RequestArgs> => {
            // verify required parameter 'eventId' is not null or undefined
            if (eventId === null || eventId === undefined) {
                throw new RequiredError('eventId','Required parameter eventId was null or undefined when calling apiDeviceEventEventsEventIdDelete.');
            }
            const localVarPath = `/api/deviceEvent/events/{eventId}`
                .replace(`{${"eventId"}}`, encodeURIComponent(String(eventId)));
            // use dummy base URL string because the URL constructor only accepts absolute URLs.
            const localVarUrlObj = new URL(localVarPath, 'https://example.com');
            let baseOptions;
            if (configuration) {
                baseOptions = configuration.baseOptions;
            }
            const localVarRequestOptions :AxiosRequestConfig = { method: 'DELETE', ...baseOptions, ...options};
            const localVarHeaderParameter = {} as any;
            const localVarQueryParameter = {} as any;

            // authentication Bearer required
            // http bearer authentication required
            if (configuration && configuration.accessToken) {
                const accessToken = typeof configuration.accessToken === 'function'
                    ? await configuration.accessToken()
                    : await configuration.accessToken;
                localVarHeaderParameter["Authorization"] = "Bearer " + accessToken;
            }

            const query = new URLSearchParams(localVarUrlObj.search);
            for (const key in localVarQueryParameter) {
                query.set(key, localVarQueryParameter[key]);
            }
            for (const key in options.params) {
                query.set(key, options.params[key]);
            }
            localVarUrlObj.search = (new URLSearchParams(query)).toString();
            let headersFromBaseOptions = baseOptions && baseOptions.headers ? baseOptions.headers : {};
            localVarRequestOptions.headers = {...localVarHeaderParameter, ...headersFromBaseOptions, ...options.headers};

            return {
                url: localVarUrlObj.pathname + localVarUrlObj.search + localVarUrlObj.hash,
                options: localVarRequestOptions,
            };
        },
        /**
         * 
         * @summary ??????
         * @param {number} eventId ??ID
         * @param {*} [options] Override http request option.
         * @throws {RequiredError}
         */
        apiDeviceEventEventsEventIdDisablePost: async (eventId: number, options: AxiosRequestConfig = {}): Promise<RequestArgs> => {
            // verify required parameter 'eventId' is not null or undefined
            if (eventId === null || eventId === undefined) {
                throw new RequiredError('eventId','Required parameter eventId was null or undefined when calling apiDeviceEventEventsEventIdDisablePost.');
            }
            const localVarPath = `/api/deviceEvent/events/{eventId}/disable`
                .replace(`{${"eventId"}}`, encodeURIComponent(String(eventId)));
            // use dummy base URL string because the URL constructor only accepts absolute URLs.
            const localVarUrlObj = new URL(localVarPath, 'https://example.com');
            let baseOptions;
            if (configuration) {
                baseOptions = configuration.baseOptions;
            }
            const localVarRequestOptions :AxiosRequestConfig = { method: 'POST', ...baseOptions, ...options};
            const localVarHeaderParameter = {} as any;
            const localVarQueryParameter = {} as any;

            // authentication Bearer required
            // http bearer authentication required
            if (configuration && configuration.accessToken) {
                const accessToken = typeof configuration.accessToken === 'function'
                    ? await configuration.accessToken()
                    : await configuration.accessToken;
                localVarHeaderParameter["Authorization"] = "Bearer " + accessToken;
            }

            const query = new URLSearchParams(localVarUrlObj.search);
            for (const key in localVarQueryParameter) {
                query.set(key, localVarQueryParameter[key]);
            }
            for (const key in options.params) {
                query.set(key, options.params[key]);
            }
            localVarUrlObj.search = (new URLSearchParams(query)).toString();
            let headersFromBaseOptions = baseOptions && baseOptions.headers ? baseOptions.headers : {};
            localVarRequestOptions.headers = {...localVarHeaderParameter, ...headersFromBaseOptions, ...options.headers};

            return {
                url: localVarUrlObj.pathname + localVarUrlObj.search + localVarUrlObj.hash,
                options: localVarRequestOptions,
            };
        },
        /**
         * 
         * @summary ??????
         * @param {number} eventId ??ID
         * @param {*} [options] Override http request option.
         * @throws {RequiredError}
         */
        apiDeviceEventEventsEventIdEnablePost: async (eventId: number, options: AxiosRequestConfig = {}): Promise<RequestArgs> => {
            // verify required parameter 'eventId' is not null or undefined
            if (eventId === null || eventId === undefined) {
                throw new RequiredError('eventId','Required parameter eventId was null or undefined when calling apiDeviceEventEventsEventIdEnablePost.');
            }
            const localVarPath = `/api/deviceEvent/events/{eventId}/enable`
                .replace(`{${"eventId"}}`, encodeURIComponent(String(eventId)));
            // use dummy base URL string because the URL constructor only accepts absolute URLs.
            const localVarUrlObj = new URL(localVarPath, 'https://example.com');
            let baseOptions;
            if (configuration) {
                baseOptions = configuration.baseOptions;
            }
            const localVarRequestOptions :AxiosRequestConfig = { method: 'POST', ...baseOptions, ...options};
            const localVarHeaderParameter = {} as any;
            const localVarQueryParameter = {} as any;

            // authentication Bearer required
            // http bearer authentication required
            if (configuration && configuration.accessToken) {
                const accessToken = typeof configuration.accessToken === 'function'
                    ? await configuration.accessToken()
                    : await configuration.accessToken;
                localVarHeaderParameter["Authorization"] = "Bearer " + accessToken;
            }

            const query = new URLSearchParams(localVarUrlObj.search);
            for (const key in localVarQueryParameter) {
                query.set(key, localVarQueryParameter[key]);
            }
            for (const key in options.params) {
                query.set(key, options.params[key]);
            }
            localVarUrlObj.search = (new URLSearchParams(query)).toString();
            let headersFromBaseOptions = baseOptions && baseOptions.headers ? baseOptions.headers : {};
            localVarRequestOptions.headers = {...localVarHeaderParameter, ...headersFromBaseOptions, ...options.headers};

            return {
                url: localVarUrlObj.pathname + localVarUrlObj.search + localVarUrlObj.hash,
                options: localVarRequestOptions,
            };
        },
        /**
         * 
         * @summary ??????
         * @param {number} eventId ??ID
         * @param {*} [options] Override http request option.
         * @throws {RequiredError}
         */
        apiDeviceEventEventsEventIdGet: async (eventId: number, options: AxiosRequestConfig = {}): Promise<RequestArgs> => {
            // verify required parameter 'eventId' is not null or undefined
            if (eventId === null || eventId === undefined) {
                throw new RequiredError('eventId','Required parameter eventId was null or undefined when calling apiDeviceEventEventsEventIdGet.');
            }
            const localVarPath = `/api/deviceEvent/events/{eventId}`
                .replace(`{${"eventId"}}`, encodeURIComponent(String(eventId)));
            // use dummy base URL string because the URL constructor only accepts absolute URLs.
            const localVarUrlObj = new URL(localVarPath, 'https://example.com');
            let baseOptions;
            if (configuration) {
                baseOptions = configuration.baseOptions;
            }
            const localVarRequestOptions :AxiosRequestConfig = { method: 'GET', ...baseOptions, ...options};
            const localVarHeaderParameter = {} as any;
            const localVarQueryParameter = {} as any;

            // authentication Bearer required
            // http bearer authentication required
            if (configuration && configuration.accessToken) {
                const accessToken = typeof configuration.accessToken === 'function'
                    ? await configuration.accessToken()
                    : await configuration.accessToken;
                localVarHeaderParameter["Authorization"] = "Bearer " + accessToken;
            }

            const query = new URLSearchParams(localVarUrlObj.search);
            for (const key in localVarQueryParameter) {
                query.set(key, localVarQueryParameter[key]);
            }
            for (const key in options.params) {
                query.set(key, options.params[key]);
            }
            localVarUrlObj.search = (new URLSearchParams(query)).toString();
            let headersFromBaseOptions = baseOptions && baseOptions.headers ? baseOptions.headers : {};
            localVarRequestOptions.headers = {...localVarHeaderParameter, ...headersFromBaseOptions, ...options.headers};

            return {
                url: localVarUrlObj.pathname + localVarUrlObj.search + localVarUrlObj.hash,
                options: localVarRequestOptions,
            };
        },
        /**
         * 
         * @summary ??????
         * @param {number} eventId ??ID
         * @param {UpdateEventRequest} [body] ??????
         * @param {*} [options] Override http request option.
         * @throws {RequiredError}
         */
        apiDeviceEventEventsEventIdPut: async (eventId: number, body?: UpdateEventRequest, options: AxiosRequestConfig = {}): Promise<RequestArgs> => {
            // verify required parameter 'eventId' is not null or undefined
            if (eventId === null || eventId === undefined) {
                throw new RequiredError('eventId','Required parameter eventId was null or undefined when calling apiDeviceEventEventsEventIdPut.');
            }
            const localVarPath = `/api/deviceEvent/events/{eventId}`
                .replace(`{${"eventId"}}`, encodeURIComponent(String(eventId)));
            // use dummy base URL string because the URL constructor only accepts absolute URLs.
            const localVarUrlObj = new URL(localVarPath, 'https://example.com');
            let baseOptions;
            if (configuration) {
                baseOptions = configuration.baseOptions;
            }
            const localVarRequestOptions :AxiosRequestConfig = { method: 'PUT', ...baseOptions, ...options};
            const localVarHeaderParameter = {} as any;
            const localVarQueryParameter = {} as any;

            // authentication Bearer required
            // http bearer authentication required
            if (configuration && configuration.accessToken) {
                const accessToken = typeof configuration.accessToken === 'function'
                    ? await configuration.accessToken()
                    : await configuration.accessToken;
                localVarHeaderParameter["Authorization"] = "Bearer " + accessToken;
            }

            localVarHeaderParameter['Content-Type'] = 'application/json-patch+json';

            const query = new URLSearchParams(localVarUrlObj.search);
            for (const key in localVarQueryParameter) {
                query.set(key, localVarQueryParameter[key]);
            }
            for (const key in options.params) {
                query.set(key, options.params[key]);
            }
            localVarUrlObj.search = (new URLSearchParams(query)).toString();
            let headersFromBaseOptions = baseOptions && baseOptions.headers ? baseOptions.headers : {};
            localVarRequestOptions.headers = {...localVarHeaderParameter, ...headersFromBaseOptions, ...options.headers};
            const needsSerialization = (typeof body !== "string") || localVarRequestOptions.headers['Content-Type'] === 'application/json';
            localVarRequestOptions.data =  needsSerialization ? JSON.stringify(body !== undefined ? body : {}) : (body || "");

            return {
                url: localVarUrlObj.pathname + localVarUrlObj.search + localVarUrlObj.hash,
                options: localVarRequestOptions,
            };
        },
        /**
         * 
         * @summary ??????
         * @param {*} [options] Override http request option.
         * @throws {RequiredError}
         */
        apiDeviceEventEventsGet: async (options: AxiosRequestConfig = {}): Promise<RequestArgs> => {
            const localVarPath = `/api/deviceEvent/events`;
            // use dummy base URL string because the URL constructor only accepts absolute URLs.
            const localVarUrlObj = new URL(localVarPath, 'https://example.com');
            let baseOptions;
            if (configuration) {
                baseOptions = configuration.baseOptions;
            }
            const localVarRequestOptions :AxiosRequestConfig = { method: 'GET', ...baseOptions, ...options};
            const localVarHeaderParameter = {} as any;
            const localVarQueryParameter = {} as any;

            // authentication Bearer required
            // http bearer authentication required
            if (configuration && configuration.accessToken) {
                const accessToken = typeof configuration.accessToken === 'function'
                    ? await configuration.accessToken()
                    : await configuration.accessToken;
                localVarHeaderParameter["Authorization"] = "Bearer " + accessToken;
            }

            const query = new URLSearchParams(localVarUrlObj.search);
            for (const key in localVarQueryParameter) {
                query.set(key, localVarQueryParameter[key]);
            }
            for (const key in options.params) {
                query.set(key, options.params[key]);
            }
            localVarUrlObj.search = (new URLSearchParams(query)).toString();
            let headersFromBaseOptions = baseOptions && baseOptions.headers ? baseOptions.headers : {};
            localVarRequestOptions.headers = {...localVarHeaderParameter, ...headersFromBaseOptions, ...options.headers};

            return {
                url: localVarUrlObj.pathname + localVarUrlObj.search + localVarUrlObj.hash,
                options: localVarRequestOptions,
            };
        },
        /**
         * 
         * @summary ??????
         * @param {CreateEventRequest} [body] ??????
         * @param {*} [options] Override http request option.
         * @throws {RequiredError}
         */
        apiDeviceEventEventsPost: async (body?: CreateEventRequest, options: AxiosRequestConfig = {}): Promise<RequestArgs> => {
            const localVarPath = `/api/deviceEvent/events`;
            // use dummy base URL string because the URL constructor only accepts absolute URLs.
            const localVarUrlObj = new URL(localVarPath, 'https://example.com');
            let baseOptions;
            if (configuration) {
                baseOptions = configuration.baseOptions;
            }
            const localVarRequestOptions :AxiosRequestConfig = { method: 'POST', ...baseOptions, ...options};
            const localVarHeaderParameter = {} as any;
            const localVarQueryParameter = {} as any;

            // authentication Bearer required
            // http bearer authentication required
            if (configuration && configuration.accessToken) {
                const accessToken = typeof configuration.accessToken === 'function'
                    ? await configuration.accessToken()
                    : await configuration.accessToken;
                localVarHeaderParameter["Authorization"] = "Bearer " + accessToken;
            }

            localVarHeaderParameter['Content-Type'] = 'application/json-patch+json';

            const query = new URLSearchParams(localVarUrlObj.search);
            for (const key in localVarQueryParameter) {
                query.set(key, localVarQueryParameter[key]);
            }
            for (const key in options.params) {
                query.set(key, options.params[key]);
            }
            localVarUrlObj.search = (new URLSearchParams(query)).toString();
            let headersFromBaseOptions = baseOptions && baseOptions.headers ? baseOptions.headers : {};
            localVarRequestOptions.headers = {...localVarHeaderParameter, ...headersFromBaseOptions, ...options.headers};
            const needsSerialization = (typeof body !== "string") || localVarRequestOptions.headers['Content-Type'] === 'application/json';
            localVarRequestOptions.data =  needsSerialization ? JSON.stringify(body !== undefined ? body : {}) : (body || "");

            return {
                url: localVarUrlObj.pathname + localVarUrlObj.search + localVarUrlObj.hash,
                options: localVarRequestOptions,
            };
        },
    }
};

/**
 * DeviceEventApi - functional programming interface
 * @export
 */
export const DeviceEventApiFp = function(configuration?: Configuration) {
    return {
        /**
         * 
         * @summary ??????????
         * @param {number} deviceId ??ID
         * @param {string} [eventName] ????
         * @param {number} [excludeEventId] ?????ID
         * @param {*} [options] Override http request option.
         * @throws {RequiredError}
         */
        async apiDeviceEventDevicesDeviceIdEventsCheckNameGet(deviceId: number, eventName?: string, excludeEventId?: number, options?: AxiosRequestConfig): Promise<(axios?: AxiosInstance, basePath?: string) => Promise<AxiosResponse<RESTfulResultActionResultBoolean>>> {
            const localVarAxiosArgs = await DeviceEventApiAxiosParamCreator(configuration).apiDeviceEventDevicesDeviceIdEventsCheckNameGet(deviceId, eventName, excludeEventId, options);
            return (axios: AxiosInstance = globalAxios, basePath: string = BASE_PATH) => {
                const axiosRequestArgs :AxiosRequestConfig = {...localVarAxiosArgs.options, url: basePath + localVarAxiosArgs.url};
                return axios.request(axiosRequestArgs);
            };
        },
        /**
         * 
         * @summary ?????????
         * @param {number} deviceId ??ID
         * @param {*} [options] Override http request option.
         * @throws {RequiredError}
         */
        async apiDeviceEventDevicesDeviceIdEventsGet(deviceId: number, options?: AxiosRequestConfig): Promise<(axios?: AxiosInstance, basePath?: string) => Promise<AxiosResponse<RESTfulResultActionResultListEventResponse>>> {
            const localVarAxiosArgs = await DeviceEventApiAxiosParamCreator(configuration).apiDeviceEventDevicesDeviceIdEventsGet(deviceId, options);
            return (axios: AxiosInstance = globalAxios, basePath: string = BASE_PATH) => {
                const axiosRequestArgs :AxiosRequestConfig = {...localVarAxiosArgs.options, url: basePath + localVarAxiosArgs.url};
                return axios.request(axiosRequestArgs);
            };
        },
        /**
         * 
         * @summary ??????
         * @param {number} eventId ??ID
         * @param {*} [options] Override http request option.
         * @throws {RequiredError}
         */
        async apiDeviceEventEventsEventIdDelete(eventId: number, options?: AxiosRequestConfig): Promise<(axios?: AxiosInstance, basePath?: string) => Promise<AxiosResponse<void>>> {
            const localVarAxiosArgs = await DeviceEventApiAxiosParamCreator(configuration).apiDeviceEventEventsEventIdDelete(eventId, options);
            return (axios: AxiosInstance = globalAxios, basePath: string = BASE_PATH) => {
                const axiosRequestArgs :AxiosRequestConfig = {...localVarAxiosArgs.options, url: basePath + localVarAxiosArgs.url};
                return axios.request(axiosRequestArgs);
            };
        },
        /**
         * 
         * @summary ??????
         * @param {number} eventId ??ID
         * @param {*} [options] Override http request option.
         * @throws {RequiredError}
         */
        async apiDeviceEventEventsEventIdDisablePost(eventId: number, options?: AxiosRequestConfig): Promise<(axios?: AxiosInstance, basePath?: string) => Promise<AxiosResponse<void>>> {
            const localVarAxiosArgs = await DeviceEventApiAxiosParamCreator(configuration).apiDeviceEventEventsEventIdDisablePost(eventId, options);
            return (axios: AxiosInstance = globalAxios, basePath: string = BASE_PATH) => {
                const axiosRequestArgs :AxiosRequestConfig = {...localVarAxiosArgs.options, url: basePath + localVarAxiosArgs.url};
                return axios.request(axiosRequestArgs);
            };
        },
        /**
         * 
         * @summary ??????
         * @param {number} eventId ??ID
         * @param {*} [options] Override http request option.
         * @throws {RequiredError}
         */
        async apiDeviceEventEventsEventIdEnablePost(eventId: number, options?: AxiosRequestConfig): Promise<(axios?: AxiosInstance, basePath?: string) => Promise<AxiosResponse<void>>> {
            const localVarAxiosArgs = await DeviceEventApiAxiosParamCreator(configuration).apiDeviceEventEventsEventIdEnablePost(eventId, options);
            return (axios: AxiosInstance = globalAxios, basePath: string = BASE_PATH) => {
                const axiosRequestArgs :AxiosRequestConfig = {...localVarAxiosArgs.options, url: basePath + localVarAxiosArgs.url};
                return axios.request(axiosRequestArgs);
            };
        },
        /**
         * 
         * @summary ??????
         * @param {number} eventId ??ID
         * @param {*} [options] Override http request option.
         * @throws {RequiredError}
         */
        async apiDeviceEventEventsEventIdGet(eventId: number, options?: AxiosRequestConfig): Promise<(axios?: AxiosInstance, basePath?: string) => Promise<AxiosResponse<RESTfulResultActionResultEventDetailResponse>>> {
            const localVarAxiosArgs = await DeviceEventApiAxiosParamCreator(configuration).apiDeviceEventEventsEventIdGet(eventId, options);
            return (axios: AxiosInstance = globalAxios, basePath: string = BASE_PATH) => {
                const axiosRequestArgs :AxiosRequestConfig = {...localVarAxiosArgs.options, url: basePath + localVarAxiosArgs.url};
                return axios.request(axiosRequestArgs);
            };
        },
        /**
         * 
         * @summary ??????
         * @param {number} eventId ??ID
         * @param {UpdateEventRequest} [body] ??????
         * @param {*} [options] Override http request option.
         * @throws {RequiredError}
         */
        async apiDeviceEventEventsEventIdPut(eventId: number, body?: UpdateEventRequest, options?: AxiosRequestConfig): Promise<(axios?: AxiosInstance, basePath?: string) => Promise<AxiosResponse<void>>> {
            const localVarAxiosArgs = await DeviceEventApiAxiosParamCreator(configuration).apiDeviceEventEventsEventIdPut(eventId, body, options);
            return (axios: AxiosInstance = globalAxios, basePath: string = BASE_PATH) => {
                const axiosRequestArgs :AxiosRequestConfig = {...localVarAxiosArgs.options, url: basePath + localVarAxiosArgs.url};
                return axios.request(axiosRequestArgs);
            };
        },
        /**
         * 
         * @summary ??????
         * @param {*} [options] Override http request option.
         * @throws {RequiredError}
         */
        async apiDeviceEventEventsGet(options?: AxiosRequestConfig): Promise<(axios?: AxiosInstance, basePath?: string) => Promise<AxiosResponse<RESTfulResultActionResultListEventResponse>>> {
            const localVarAxiosArgs = await DeviceEventApiAxiosParamCreator(configuration).apiDeviceEventEventsGet(options);
            return (axios: AxiosInstance = globalAxios, basePath: string = BASE_PATH) => {
                const axiosRequestArgs :AxiosRequestConfig = {...localVarAxiosArgs.options, url: basePath + localVarAxiosArgs.url};
                return axios.request(axiosRequestArgs);
            };
        },
        /**
         * 
         * @summary ??????
         * @param {CreateEventRequest} [body] ??????
         * @param {*} [options] Override http request option.
         * @throws {RequiredError}
         */
        async apiDeviceEventEventsPost(body?: CreateEventRequest, options?: AxiosRequestConfig): Promise<(axios?: AxiosInstance, basePath?: string) => Promise<AxiosResponse<RESTfulResultActionResultInt64>>> {
            const localVarAxiosArgs = await DeviceEventApiAxiosParamCreator(configuration).apiDeviceEventEventsPost(body, options);
            return (axios: AxiosInstance = globalAxios, basePath: string = BASE_PATH) => {
                const axiosRequestArgs :AxiosRequestConfig = {...localVarAxiosArgs.options, url: basePath + localVarAxiosArgs.url};
                return axios.request(axiosRequestArgs);
            };
        },
    }
};

/**
 * DeviceEventApi - factory interface
 * @export
 */
export const DeviceEventApiFactory = function (configuration?: Configuration, basePath?: string, axios?: AxiosInstance) {
    return {
        /**
         * 
         * @summary ??????????
         * @param {number} deviceId ??ID
         * @param {string} [eventName] ????
         * @param {number} [excludeEventId] ?????ID
         * @param {*} [options] Override http request option.
         * @throws {RequiredError}
         */
        async apiDeviceEventDevicesDeviceIdEventsCheckNameGet(deviceId: number, eventName?: string, excludeEventId?: number, options?: AxiosRequestConfig): Promise<AxiosResponse<RESTfulResultActionResultBoolean>> {
            return DeviceEventApiFp(configuration).apiDeviceEventDevicesDeviceIdEventsCheckNameGet(deviceId, eventName, excludeEventId, options).then((request) => request(axios, basePath));
        },
        /**
         * 
         * @summary ?????????
         * @param {number} deviceId ??ID
         * @param {*} [options] Override http request option.
         * @throws {RequiredError}
         */
        async apiDeviceEventDevicesDeviceIdEventsGet(deviceId: number, options?: AxiosRequestConfig): Promise<AxiosResponse<RESTfulResultActionResultListEventResponse>> {
            return DeviceEventApiFp(configuration).apiDeviceEventDevicesDeviceIdEventsGet(deviceId, options).then((request) => request(axios, basePath));
        },
        /**
         * 
         * @summary ??????
         * @param {number} eventId ??ID
         * @param {*} [options] Override http request option.
         * @throws {RequiredError}
         */
        async apiDeviceEventEventsEventIdDelete(eventId: number, options?: AxiosRequestConfig): Promise<AxiosResponse<void>> {
            return DeviceEventApiFp(configuration).apiDeviceEventEventsEventIdDelete(eventId, options).then((request) => request(axios, basePath));
        },
        /**
         * 
         * @summary ??????
         * @param {number} eventId ??ID
         * @param {*} [options] Override http request option.
         * @throws {RequiredError}
         */
        async apiDeviceEventEventsEventIdDisablePost(eventId: number, options?: AxiosRequestConfig): Promise<AxiosResponse<void>> {
            return DeviceEventApiFp(configuration).apiDeviceEventEventsEventIdDisablePost(eventId, options).then((request) => request(axios, basePath));
        },
        /**
         * 
         * @summary ??????
         * @param {number} eventId ??ID
         * @param {*} [options] Override http request option.
         * @throws {RequiredError}
         */
        async apiDeviceEventEventsEventIdEnablePost(eventId: number, options?: AxiosRequestConfig): Promise<AxiosResponse<void>> {
            return DeviceEventApiFp(configuration).apiDeviceEventEventsEventIdEnablePost(eventId, options).then((request) => request(axios, basePath));
        },
        /**
         * 
         * @summary ??????
         * @param {number} eventId ??ID
         * @param {*} [options] Override http request option.
         * @throws {RequiredError}
         */
        async apiDeviceEventEventsEventIdGet(eventId: number, options?: AxiosRequestConfig): Promise<AxiosResponse<RESTfulResultActionResultEventDetailResponse>> {
            return DeviceEventApiFp(configuration).apiDeviceEventEventsEventIdGet(eventId, options).then((request) => request(axios, basePath));
        },
        /**
         * 
         * @summary ??????
         * @param {number} eventId ??ID
         * @param {UpdateEventRequest} [body] ??????
         * @param {*} [options] Override http request option.
         * @throws {RequiredError}
         */
        async apiDeviceEventEventsEventIdPut(eventId: number, body?: UpdateEventRequest, options?: AxiosRequestConfig): Promise<AxiosResponse<void>> {
            return DeviceEventApiFp(configuration).apiDeviceEventEventsEventIdPut(eventId, body, options).then((request) => request(axios, basePath));
        },
        /**
         * 
         * @summary ??????
         * @param {*} [options] Override http request option.
         * @throws {RequiredError}
         */
        async apiDeviceEventEventsGet(options?: AxiosRequestConfig): Promise<AxiosResponse<RESTfulResultActionResultListEventResponse>> {
            return DeviceEventApiFp(configuration).apiDeviceEventEventsGet(options).then((request) => request(axios, basePath));
        },
        /**
         * 
         * @summary ??????
         * @param {CreateEventRequest} [body] ??????
         * @param {*} [options] Override http request option.
         * @throws {RequiredError}
         */
        async apiDeviceEventEventsPost(body?: CreateEventRequest, options?: AxiosRequestConfig): Promise<AxiosResponse<RESTfulResultActionResultInt64>> {
            return DeviceEventApiFp(configuration).apiDeviceEventEventsPost(body, options).then((request) => request(axios, basePath));
        },
    };
};

/**
 * DeviceEventApi - object-oriented interface
 * @export
 * @class DeviceEventApi
 * @extends {BaseAPI}
 */
export class DeviceEventApi extends BaseAPI {
    /**
     * 
     * @summary ??????????
     * @param {number} deviceId ??ID
     * @param {string} [eventName] ????
     * @param {number} [excludeEventId] ?????ID
     * @param {*} [options] Override http request option.
     * @throws {RequiredError}
     * @memberof DeviceEventApi
     */
    public async apiDeviceEventDevicesDeviceIdEventsCheckNameGet(deviceId: number, eventName?: string, excludeEventId?: number, options?: AxiosRequestConfig) : Promise<AxiosResponse<RESTfulResultActionResultBoolean>> {
        return DeviceEventApiFp(this.configuration).apiDeviceEventDevicesDeviceIdEventsCheckNameGet(deviceId, eventName, excludeEventId, options).then((request) => request(this.axios, this.basePath));
    }
    /**
     * 
     * @summary ?????????
     * @param {number} deviceId ??ID
     * @param {*} [options] Override http request option.
     * @throws {RequiredError}
     * @memberof DeviceEventApi
     */
    public async apiDeviceEventDevicesDeviceIdEventsGet(deviceId: number, options?: AxiosRequestConfig) : Promise<AxiosResponse<RESTfulResultActionResultListEventResponse>> {
        return DeviceEventApiFp(this.configuration).apiDeviceEventDevicesDeviceIdEventsGet(deviceId, options).then((request) => request(this.axios, this.basePath));
    }
    /**
     * 
     * @summary ??????
     * @param {number} eventId ??ID
     * @param {*} [options] Override http request option.
     * @throws {RequiredError}
     * @memberof DeviceEventApi
     */
    public async apiDeviceEventEventsEventIdDelete(eventId: number, options?: AxiosRequestConfig) : Promise<AxiosResponse<void>> {
        return DeviceEventApiFp(this.configuration).apiDeviceEventEventsEventIdDelete(eventId, options).then((request) => request(this.axios, this.basePath));
    }
    /**
     * 
     * @summary ??????
     * @param {number} eventId ??ID
     * @param {*} [options] Override http request option.
     * @throws {RequiredError}
     * @memberof DeviceEventApi
     */
    public async apiDeviceEventEventsEventIdDisablePost(eventId: number, options?: AxiosRequestConfig) : Promise<AxiosResponse<void>> {
        return DeviceEventApiFp(this.configuration).apiDeviceEventEventsEventIdDisablePost(eventId, options).then((request) => request(this.axios, this.basePath));
    }
    /**
     * 
     * @summary ??????
     * @param {number} eventId ??ID
     * @param {*} [options] Override http request option.
     * @throws {RequiredError}
     * @memberof DeviceEventApi
     */
    public async apiDeviceEventEventsEventIdEnablePost(eventId: number, options?: AxiosRequestConfig) : Promise<AxiosResponse<void>> {
        return DeviceEventApiFp(this.configuration).apiDeviceEventEventsEventIdEnablePost(eventId, options).then((request) => request(this.axios, this.basePath));
    }
    /**
     * 
     * @summary ??????
     * @param {number} eventId ??ID
     * @param {*} [options] Override http request option.
     * @throws {RequiredError}
     * @memberof DeviceEventApi
     */
    public async apiDeviceEventEventsEventIdGet(eventId: number, options?: AxiosRequestConfig) : Promise<AxiosResponse<RESTfulResultActionResultEventDetailResponse>> {
        return DeviceEventApiFp(this.configuration).apiDeviceEventEventsEventIdGet(eventId, options).then((request) => request(this.axios, this.basePath));
    }
    /**
     * 
     * @summary ??????
     * @param {number} eventId ??ID
     * @param {UpdateEventRequest} [body] ??????
     * @param {*} [options] Override http request option.
     * @throws {RequiredError}
     * @memberof DeviceEventApi
     */
    public async apiDeviceEventEventsEventIdPut(eventId: number, body?: UpdateEventRequest, options?: AxiosRequestConfig) : Promise<AxiosResponse<void>> {
        return DeviceEventApiFp(this.configuration).apiDeviceEventEventsEventIdPut(eventId, body, options).then((request) => request(this.axios, this.basePath));
    }
    /**
     * 
     * @summary ??????
     * @param {*} [options] Override http request option.
     * @throws {RequiredError}
     * @memberof DeviceEventApi
     */
    public async apiDeviceEventEventsGet(options?: AxiosRequestConfig) : Promise<AxiosResponse<RESTfulResultActionResultListEventResponse>> {
        return DeviceEventApiFp(this.configuration).apiDeviceEventEventsGet(options).then((request) => request(this.axios, this.basePath));
    }
    /**
     * 
     * @summary ??????
     * @param {CreateEventRequest} [body] ??????
     * @param {*} [options] Override http request option.
     * @throws {RequiredError}
     * @memberof DeviceEventApi
     */
    public async apiDeviceEventEventsPost(body?: CreateEventRequest, options?: AxiosRequestConfig) : Promise<AxiosResponse<RESTfulResultActionResultInt64>> {
        return DeviceEventApiFp(this.configuration).apiDeviceEventEventsPost(body, options).then((request) => request(this.axios, this.basePath));
    }
}
