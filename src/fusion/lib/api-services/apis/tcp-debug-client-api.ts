/* tslint:disable */
/* eslint-disable */
/**
 * All Groups
 * No description provided (generated by Swagger Codegen https://github.com/swagger-api/swagger-codegen)
 *
 * OpenAPI spec version: 1.0.0
 * 
 *
 * NOTE: This class is auto generated by the swagger code generator program.
 * https://github.com/swagger-api/swagger-codegen.git
 * Do not edit the class manually.
 */

import globalAxios, { AxiosResponse, AxiosInstance, AxiosRequestConfig } from 'axios';
import { Configuration } from '../configuration';
// Some imports not used depending on template conditions
// @ts-ignore
import { BASE_PATH, COLLECTION_FORMATS, RequestArgs, BaseAPI, RequiredError } from '../base';
import { RESTfulResultObject } from '../models';
import { SendMessageInput } from '../models';
/**
 * TcpDebugClientApi - axios parameter creator
 * @export
 */
export const TcpDebugClientApiAxiosParamCreator = function (configuration?: Configuration) {
    return {
        /**
         * 
         * @summary ??????
         * @param {string} [ip] 
         * @param {number} [port] 
         * @param {*} [options] Override http request option.
         * @throws {RequiredError}
         */
        connectToServer: async (ip?: string, port?: number, options: AxiosRequestConfig = {}): Promise<RequestArgs> => {
            const localVarPath = `/api/debug/tcpClient/connect`;
            // use dummy base URL string because the URL constructor only accepts absolute URLs.
            const localVarUrlObj = new URL(localVarPath, 'https://example.com');
            let baseOptions;
            if (configuration) {
                baseOptions = configuration.baseOptions;
            }
            const localVarRequestOptions :AxiosRequestConfig = { method: 'POST', ...baseOptions, ...options};
            const localVarHeaderParameter = {} as any;
            const localVarQueryParameter = {} as any;

            // authentication Bearer required
            // http bearer authentication required
            if (configuration && configuration.accessToken) {
                const accessToken = typeof configuration.accessToken === 'function'
                    ? await configuration.accessToken()
                    : await configuration.accessToken;
                localVarHeaderParameter["Authorization"] = "Bearer " + accessToken;
            }

            if (ip !== undefined) {
                localVarQueryParameter['ip'] = ip;
            }

            if (port !== undefined) {
                localVarQueryParameter['port'] = port;
            }

            const query = new URLSearchParams(localVarUrlObj.search);
            for (const key in localVarQueryParameter) {
                query.set(key, localVarQueryParameter[key]);
            }
            for (const key in options.params) {
                query.set(key, options.params[key]);
            }
            localVarUrlObj.search = (new URLSearchParams(query)).toString();
            let headersFromBaseOptions = baseOptions && baseOptions.headers ? baseOptions.headers : {};
            localVarRequestOptions.headers = {...localVarHeaderParameter, ...headersFromBaseOptions, ...options.headers};

            return {
                url: localVarUrlObj.pathname + localVarUrlObj.search + localVarUrlObj.hash,
                options: localVarRequestOptions,
            };
        },
        /**
         * 
         * @summary ????
         * @param {*} [options] Override http request option.
         * @throws {RequiredError}
         */
        disconnectFromServer: async (options: AxiosRequestConfig = {}): Promise<RequestArgs> => {
            const localVarPath = `/api/debug/tcpClient/disconnect`;
            // use dummy base URL string because the URL constructor only accepts absolute URLs.
            const localVarUrlObj = new URL(localVarPath, 'https://example.com');
            let baseOptions;
            if (configuration) {
                baseOptions = configuration.baseOptions;
            }
            const localVarRequestOptions :AxiosRequestConfig = { method: 'POST', ...baseOptions, ...options};
            const localVarHeaderParameter = {} as any;
            const localVarQueryParameter = {} as any;

            // authentication Bearer required
            // http bearer authentication required
            if (configuration && configuration.accessToken) {
                const accessToken = typeof configuration.accessToken === 'function'
                    ? await configuration.accessToken()
                    : await configuration.accessToken;
                localVarHeaderParameter["Authorization"] = "Bearer " + accessToken;
            }

            const query = new URLSearchParams(localVarUrlObj.search);
            for (const key in localVarQueryParameter) {
                query.set(key, localVarQueryParameter[key]);
            }
            for (const key in options.params) {
                query.set(key, options.params[key]);
            }
            localVarUrlObj.search = (new URLSearchParams(query)).toString();
            let headersFromBaseOptions = baseOptions && baseOptions.headers ? baseOptions.headers : {};
            localVarRequestOptions.headers = {...localVarHeaderParameter, ...headersFromBaseOptions, ...options.headers};

            return {
                url: localVarUrlObj.pathname + localVarUrlObj.search + localVarUrlObj.hash,
                options: localVarRequestOptions,
            };
        },
        /**
         * 
         * @summary ??????
         * @param {*} [options] Override http request option.
         * @throws {RequiredError}
         */
        getStatus: async (options: AxiosRequestConfig = {}): Promise<RequestArgs> => {
            const localVarPath = `/api/debug/tcpClient/status`;
            // use dummy base URL string because the URL constructor only accepts absolute URLs.
            const localVarUrlObj = new URL(localVarPath, 'https://example.com');
            let baseOptions;
            if (configuration) {
                baseOptions = configuration.baseOptions;
            }
            const localVarRequestOptions :AxiosRequestConfig = { method: 'GET', ...baseOptions, ...options};
            const localVarHeaderParameter = {} as any;
            const localVarQueryParameter = {} as any;

            // authentication Bearer required
            // http bearer authentication required
            if (configuration && configuration.accessToken) {
                const accessToken = typeof configuration.accessToken === 'function'
                    ? await configuration.accessToken()
                    : await configuration.accessToken;
                localVarHeaderParameter["Authorization"] = "Bearer " + accessToken;
            }

            const query = new URLSearchParams(localVarUrlObj.search);
            for (const key in localVarQueryParameter) {
                query.set(key, localVarQueryParameter[key]);
            }
            for (const key in options.params) {
                query.set(key, options.params[key]);
            }
            localVarUrlObj.search = (new URLSearchParams(query)).toString();
            let headersFromBaseOptions = baseOptions && baseOptions.headers ? baseOptions.headers : {};
            localVarRequestOptions.headers = {...localVarHeaderParameter, ...headersFromBaseOptions, ...options.headers};

            return {
                url: localVarUrlObj.pathname + localVarUrlObj.search + localVarUrlObj.hash,
                options: localVarRequestOptions,
            };
        },
        /**
         * 
         * @summary ????
         * @param {SendMessageInput} [body] 
         * @param {*} [options] Override http request option.
         * @throws {RequiredError}
         */
        sendMessage: async (body?: SendMessageInput, options: AxiosRequestConfig = {}): Promise<RequestArgs> => {
            const localVarPath = `/api/debug/tcpClient/send`;
            // use dummy base URL string because the URL constructor only accepts absolute URLs.
            const localVarUrlObj = new URL(localVarPath, 'https://example.com');
            let baseOptions;
            if (configuration) {
                baseOptions = configuration.baseOptions;
            }
            const localVarRequestOptions :AxiosRequestConfig = { method: 'POST', ...baseOptions, ...options};
            const localVarHeaderParameter = {} as any;
            const localVarQueryParameter = {} as any;

            // authentication Bearer required
            // http bearer authentication required
            if (configuration && configuration.accessToken) {
                const accessToken = typeof configuration.accessToken === 'function'
                    ? await configuration.accessToken()
                    : await configuration.accessToken;
                localVarHeaderParameter["Authorization"] = "Bearer " + accessToken;
            }

            localVarHeaderParameter['Content-Type'] = 'application/json-patch+json';

            const query = new URLSearchParams(localVarUrlObj.search);
            for (const key in localVarQueryParameter) {
                query.set(key, localVarQueryParameter[key]);
            }
            for (const key in options.params) {
                query.set(key, options.params[key]);
            }
            localVarUrlObj.search = (new URLSearchParams(query)).toString();
            let headersFromBaseOptions = baseOptions && baseOptions.headers ? baseOptions.headers : {};
            localVarRequestOptions.headers = {...localVarHeaderParameter, ...headersFromBaseOptions, ...options.headers};
            const needsSerialization = (typeof body !== "string") || localVarRequestOptions.headers['Content-Type'] === 'application/json';
            localVarRequestOptions.data =  needsSerialization ? JSON.stringify(body !== undefined ? body : {}) : (body || "");

            return {
                url: localVarUrlObj.pathname + localVarUrlObj.search + localVarUrlObj.hash,
                options: localVarRequestOptions,
            };
        },
    }
};

/**
 * TcpDebugClientApi - functional programming interface
 * @export
 */
export const TcpDebugClientApiFp = function(configuration?: Configuration) {
    return {
        /**
         * 
         * @summary ??????
         * @param {string} [ip] 
         * @param {number} [port] 
         * @param {*} [options] Override http request option.
         * @throws {RequiredError}
         */
        async connectToServer(ip?: string, port?: number, options?: AxiosRequestConfig): Promise<(axios?: AxiosInstance, basePath?: string) => Promise<AxiosResponse<void>>> {
            const localVarAxiosArgs = await TcpDebugClientApiAxiosParamCreator(configuration).connectToServer(ip, port, options);
            return (axios: AxiosInstance = globalAxios, basePath: string = BASE_PATH) => {
                const axiosRequestArgs :AxiosRequestConfig = {...localVarAxiosArgs.options, url: basePath + localVarAxiosArgs.url};
                return axios.request(axiosRequestArgs);
            };
        },
        /**
         * 
         * @summary ????
         * @param {*} [options] Override http request option.
         * @throws {RequiredError}
         */
        async disconnectFromServer(options?: AxiosRequestConfig): Promise<(axios?: AxiosInstance, basePath?: string) => Promise<AxiosResponse<void>>> {
            const localVarAxiosArgs = await TcpDebugClientApiAxiosParamCreator(configuration).disconnectFromServer(options);
            return (axios: AxiosInstance = globalAxios, basePath: string = BASE_PATH) => {
                const axiosRequestArgs :AxiosRequestConfig = {...localVarAxiosArgs.options, url: basePath + localVarAxiosArgs.url};
                return axios.request(axiosRequestArgs);
            };
        },
        /**
         * 
         * @summary ??????
         * @param {*} [options] Override http request option.
         * @throws {RequiredError}
         */
        async getStatus(options?: AxiosRequestConfig): Promise<(axios?: AxiosInstance, basePath?: string) => Promise<AxiosResponse<RESTfulResultObject>>> {
            const localVarAxiosArgs = await TcpDebugClientApiAxiosParamCreator(configuration).getStatus(options);
            return (axios: AxiosInstance = globalAxios, basePath: string = BASE_PATH) => {
                const axiosRequestArgs :AxiosRequestConfig = {...localVarAxiosArgs.options, url: basePath + localVarAxiosArgs.url};
                return axios.request(axiosRequestArgs);
            };
        },
        /**
         * 
         * @summary ????
         * @param {SendMessageInput} [body] 
         * @param {*} [options] Override http request option.
         * @throws {RequiredError}
         */
        async sendMessage(body?: SendMessageInput, options?: AxiosRequestConfig): Promise<(axios?: AxiosInstance, basePath?: string) => Promise<AxiosResponse<void>>> {
            const localVarAxiosArgs = await TcpDebugClientApiAxiosParamCreator(configuration).sendMessage(body, options);
            return (axios: AxiosInstance = globalAxios, basePath: string = BASE_PATH) => {
                const axiosRequestArgs :AxiosRequestConfig = {...localVarAxiosArgs.options, url: basePath + localVarAxiosArgs.url};
                return axios.request(axiosRequestArgs);
            };
        },
    }
};

/**
 * TcpDebugClientApi - factory interface
 * @export
 */
export const TcpDebugClientApiFactory = function (configuration?: Configuration, basePath?: string, axios?: AxiosInstance) {
    return {
        /**
         * 
         * @summary ??????
         * @param {string} [ip] 
         * @param {number} [port] 
         * @param {*} [options] Override http request option.
         * @throws {RequiredError}
         */
        async connectToServer(ip?: string, port?: number, options?: AxiosRequestConfig): Promise<AxiosResponse<void>> {
            return TcpDebugClientApiFp(configuration).connectToServer(ip, port, options).then((request) => request(axios, basePath));
        },
        /**
         * 
         * @summary ????
         * @param {*} [options] Override http request option.
         * @throws {RequiredError}
         */
        async disconnectFromServer(options?: AxiosRequestConfig): Promise<AxiosResponse<void>> {
            return TcpDebugClientApiFp(configuration).disconnectFromServer(options).then((request) => request(axios, basePath));
        },
        /**
         * 
         * @summary ??????
         * @param {*} [options] Override http request option.
         * @throws {RequiredError}
         */
        async getStatus(options?: AxiosRequestConfig): Promise<AxiosResponse<RESTfulResultObject>> {
            return TcpDebugClientApiFp(configuration).getStatus(options).then((request) => request(axios, basePath));
        },
        /**
         * 
         * @summary ????
         * @param {SendMessageInput} [body] 
         * @param {*} [options] Override http request option.
         * @throws {RequiredError}
         */
        async sendMessage(body?: SendMessageInput, options?: AxiosRequestConfig): Promise<AxiosResponse<void>> {
            return TcpDebugClientApiFp(configuration).sendMessage(body, options).then((request) => request(axios, basePath));
        },
    };
};

/**
 * TcpDebugClientApi - object-oriented interface
 * @export
 * @class TcpDebugClientApi
 * @extends {BaseAPI}
 */
export class TcpDebugClientApi extends BaseAPI {
    /**
     * 
     * @summary ??????
     * @param {string} [ip] 
     * @param {number} [port] 
     * @param {*} [options] Override http request option.
     * @throws {RequiredError}
     * @memberof TcpDebugClientApi
     */
    public async connectToServer(ip?: string, port?: number, options?: AxiosRequestConfig) : Promise<AxiosResponse<void>> {
        return TcpDebugClientApiFp(this.configuration).connectToServer(ip, port, options).then((request) => request(this.axios, this.basePath));
    }
    /**
     * 
     * @summary ????
     * @param {*} [options] Override http request option.
     * @throws {RequiredError}
     * @memberof TcpDebugClientApi
     */
    public async disconnectFromServer(options?: AxiosRequestConfig) : Promise<AxiosResponse<void>> {
        return TcpDebugClientApiFp(this.configuration).disconnectFromServer(options).then((request) => request(this.axios, this.basePath));
    }
    /**
     * 
     * @summary ??????
     * @param {*} [options] Override http request option.
     * @throws {RequiredError}
     * @memberof TcpDebugClientApi
     */
    public async getStatus(options?: AxiosRequestConfig) : Promise<AxiosResponse<RESTfulResultObject>> {
        return TcpDebugClientApiFp(this.configuration).getStatus(options).then((request) => request(this.axios, this.basePath));
    }
    /**
     * 
     * @summary ????
     * @param {SendMessageInput} [body] 
     * @param {*} [options] Override http request option.
     * @throws {RequiredError}
     * @memberof TcpDebugClientApi
     */
    public async sendMessage(body?: SendMessageInput, options?: AxiosRequestConfig) : Promise<AxiosResponse<void>> {
        return TcpDebugClientApiFp(this.configuration).sendMessage(body, options).then((request) => request(this.axios, this.basePath));
    }
}
