/* tslint:disable */
/* eslint-disable */
/**
 * All Groups
 * No description provided (generated by Swagger Codegen https://github.com/swagger-api/swagger-codegen)
 *
 * OpenAPI spec version: 1.0.0
 * 
 *
 * NOTE: This class is auto generated by the swagger code generator program.
 * https://github.com/swagger-api/swagger-codegen.git
 * Do not edit the class manually.
 */

import globalAxios, { AxiosResponse, AxiosInstance, AxiosRequestConfig } from 'axios';
import { Configuration } from '../configuration';
// Some imports not used depending on template conditions
// @ts-ignore
import { BASE_PATH, COLLECTION_FORMATS, RequestArgs, BaseAPI, RequiredError } from '../base';
import { RESTfulResultActionResultEventScheduleTaskResponse } from '../models';
import { RESTfulResultActionResultListEventScheduleTaskResponse } from '../models';
import { RESTfulResultActionResultListEventTaskLog } from '../models';
/**
 * EventScheduleApi - axios parameter creator
 * @export
 */
export const EventScheduleApiAxiosParamCreator = function (configuration?: Configuration) {
    return {
        /**
         * 
         * @summary ?????????
         * @param {number} eventId ??ID
         * @param {*} [options] Override http request option.
         * @throws {RequiredError}
         */
        apiEventScheduleEventsEventIdScheduleTasksGet: async (eventId: number, options: AxiosRequestConfig = {}): Promise<RequestArgs> => {
            // verify required parameter 'eventId' is not null or undefined
            if (eventId === null || eventId === undefined) {
                throw new RequiredError('eventId','Required parameter eventId was null or undefined when calling apiEventScheduleEventsEventIdScheduleTasksGet.');
            }
            const localVarPath = `/api/eventSchedule/events/{eventId}/schedule-tasks`
                .replace(`{${"eventId"}}`, encodeURIComponent(String(eventId)));
            // use dummy base URL string because the URL constructor only accepts absolute URLs.
            const localVarUrlObj = new URL(localVarPath, 'https://example.com');
            let baseOptions;
            if (configuration) {
                baseOptions = configuration.baseOptions;
            }
            const localVarRequestOptions :AxiosRequestConfig = { method: 'GET', ...baseOptions, ...options};
            const localVarHeaderParameter = {} as any;
            const localVarQueryParameter = {} as any;

            // authentication Bearer required
            // http bearer authentication required
            if (configuration && configuration.accessToken) {
                const accessToken = typeof configuration.accessToken === 'function'
                    ? await configuration.accessToken()
                    : await configuration.accessToken;
                localVarHeaderParameter["Authorization"] = "Bearer " + accessToken;
            }

            const query = new URLSearchParams(localVarUrlObj.search);
            for (const key in localVarQueryParameter) {
                query.set(key, localVarQueryParameter[key]);
            }
            for (const key in options.params) {
                query.set(key, options.params[key]);
            }
            localVarUrlObj.search = (new URLSearchParams(query)).toString();
            let headersFromBaseOptions = baseOptions && baseOptions.headers ? baseOptions.headers : {};
            localVarRequestOptions.headers = {...localVarHeaderParameter, ...headersFromBaseOptions, ...options.headers};

            return {
                url: localVarUrlObj.pathname + localVarUrlObj.search + localVarUrlObj.hash,
                options: localVarRequestOptions,
            };
        },
        /**
         * 
         * @summary ????????
         * @param {number} [days] ????
         * @param {*} [options] Override http request option.
         * @throws {RequiredError}
         */
        apiEventScheduleScheduleTasksCleanLogsPost: async (days?: number, options: AxiosRequestConfig = {}): Promise<RequestArgs> => {
            const localVarPath = `/api/eventSchedule/schedule-tasks/clean-logs`;
            // use dummy base URL string because the URL constructor only accepts absolute URLs.
            const localVarUrlObj = new URL(localVarPath, 'https://example.com');
            let baseOptions;
            if (configuration) {
                baseOptions = configuration.baseOptions;
            }
            const localVarRequestOptions :AxiosRequestConfig = { method: 'POST', ...baseOptions, ...options};
            const localVarHeaderParameter = {} as any;
            const localVarQueryParameter = {} as any;

            // authentication Bearer required
            // http bearer authentication required
            if (configuration && configuration.accessToken) {
                const accessToken = typeof configuration.accessToken === 'function'
                    ? await configuration.accessToken()
                    : await configuration.accessToken;
                localVarHeaderParameter["Authorization"] = "Bearer " + accessToken;
            }

            if (days !== undefined) {
                localVarQueryParameter['days'] = days;
            }

            const query = new URLSearchParams(localVarUrlObj.search);
            for (const key in localVarQueryParameter) {
                query.set(key, localVarQueryParameter[key]);
            }
            for (const key in options.params) {
                query.set(key, options.params[key]);
            }
            localVarUrlObj.search = (new URLSearchParams(query)).toString();
            let headersFromBaseOptions = baseOptions && baseOptions.headers ? baseOptions.headers : {};
            localVarRequestOptions.headers = {...localVarHeaderParameter, ...headersFromBaseOptions, ...options.headers};

            return {
                url: localVarUrlObj.pathname + localVarUrlObj.search + localVarUrlObj.hash,
                options: localVarRequestOptions,
            };
        },
        /**
         * 
         * @summary ??????
         * @param {number} taskId ??ID
         * @param {*} [options] Override http request option.
         * @throws {RequiredError}
         */
        apiEventScheduleScheduleTasksTaskIdCancelPost: async (taskId: number, options: AxiosRequestConfig = {}): Promise<RequestArgs> => {
            // verify required parameter 'taskId' is not null or undefined
            if (taskId === null || taskId === undefined) {
                throw new RequiredError('taskId','Required parameter taskId was null or undefined when calling apiEventScheduleScheduleTasksTaskIdCancelPost.');
            }
            const localVarPath = `/api/eventSchedule/schedule-tasks/{taskId}/cancel`
                .replace(`{${"taskId"}}`, encodeURIComponent(String(taskId)));
            // use dummy base URL string because the URL constructor only accepts absolute URLs.
            const localVarUrlObj = new URL(localVarPath, 'https://example.com');
            let baseOptions;
            if (configuration) {
                baseOptions = configuration.baseOptions;
            }
            const localVarRequestOptions :AxiosRequestConfig = { method: 'POST', ...baseOptions, ...options};
            const localVarHeaderParameter = {} as any;
            const localVarQueryParameter = {} as any;

            // authentication Bearer required
            // http bearer authentication required
            if (configuration && configuration.accessToken) {
                const accessToken = typeof configuration.accessToken === 'function'
                    ? await configuration.accessToken()
                    : await configuration.accessToken;
                localVarHeaderParameter["Authorization"] = "Bearer " + accessToken;
            }

            const query = new URLSearchParams(localVarUrlObj.search);
            for (const key in localVarQueryParameter) {
                query.set(key, localVarQueryParameter[key]);
            }
            for (const key in options.params) {
                query.set(key, options.params[key]);
            }
            localVarUrlObj.search = (new URLSearchParams(query)).toString();
            let headersFromBaseOptions = baseOptions && baseOptions.headers ? baseOptions.headers : {};
            localVarRequestOptions.headers = {...localVarHeaderParameter, ...headersFromBaseOptions, ...options.headers};

            return {
                url: localVarUrlObj.pathname + localVarUrlObj.search + localVarUrlObj.hash,
                options: localVarRequestOptions,
            };
        },
        /**
         * 
         * @summary ????????
         * @param {number} taskId ??ID
         * @param {*} [options] Override http request option.
         * @throws {RequiredError}
         */
        apiEventScheduleScheduleTasksTaskIdGet: async (taskId: number, options: AxiosRequestConfig = {}): Promise<RequestArgs> => {
            // verify required parameter 'taskId' is not null or undefined
            if (taskId === null || taskId === undefined) {
                throw new RequiredError('taskId','Required parameter taskId was null or undefined when calling apiEventScheduleScheduleTasksTaskIdGet.');
            }
            const localVarPath = `/api/eventSchedule/schedule-tasks/{taskId}`
                .replace(`{${"taskId"}}`, encodeURIComponent(String(taskId)));
            // use dummy base URL string because the URL constructor only accepts absolute URLs.
            const localVarUrlObj = new URL(localVarPath, 'https://example.com');
            let baseOptions;
            if (configuration) {
                baseOptions = configuration.baseOptions;
            }
            const localVarRequestOptions :AxiosRequestConfig = { method: 'GET', ...baseOptions, ...options};
            const localVarHeaderParameter = {} as any;
            const localVarQueryParameter = {} as any;

            // authentication Bearer required
            // http bearer authentication required
            if (configuration && configuration.accessToken) {
                const accessToken = typeof configuration.accessToken === 'function'
                    ? await configuration.accessToken()
                    : await configuration.accessToken;
                localVarHeaderParameter["Authorization"] = "Bearer " + accessToken;
            }

            const query = new URLSearchParams(localVarUrlObj.search);
            for (const key in localVarQueryParameter) {
                query.set(key, localVarQueryParameter[key]);
            }
            for (const key in options.params) {
                query.set(key, options.params[key]);
            }
            localVarUrlObj.search = (new URLSearchParams(query)).toString();
            let headersFromBaseOptions = baseOptions && baseOptions.headers ? baseOptions.headers : {};
            localVarRequestOptions.headers = {...localVarHeaderParameter, ...headersFromBaseOptions, ...options.headers};

            return {
                url: localVarUrlObj.pathname + localVarUrlObj.search + localVarUrlObj.hash,
                options: localVarRequestOptions,
            };
        },
        /**
         * 
         * @summary ????????
         * @param {number} taskId ??ID
         * @param {number} [pageIndex] ??
         * @param {number} [pageSize] ????
         * @param {*} [options] Override http request option.
         * @throws {RequiredError}
         */
        apiEventScheduleScheduleTasksTaskIdLogsGet: async (taskId: number, pageIndex?: number, pageSize?: number, options: AxiosRequestConfig = {}): Promise<RequestArgs> => {
            // verify required parameter 'taskId' is not null or undefined
            if (taskId === null || taskId === undefined) {
                throw new RequiredError('taskId','Required parameter taskId was null or undefined when calling apiEventScheduleScheduleTasksTaskIdLogsGet.');
            }
            const localVarPath = `/api/eventSchedule/schedule-tasks/{taskId}/logs`
                .replace(`{${"taskId"}}`, encodeURIComponent(String(taskId)));
            // use dummy base URL string because the URL constructor only accepts absolute URLs.
            const localVarUrlObj = new URL(localVarPath, 'https://example.com');
            let baseOptions;
            if (configuration) {
                baseOptions = configuration.baseOptions;
            }
            const localVarRequestOptions :AxiosRequestConfig = { method: 'GET', ...baseOptions, ...options};
            const localVarHeaderParameter = {} as any;
            const localVarQueryParameter = {} as any;

            // authentication Bearer required
            // http bearer authentication required
            if (configuration && configuration.accessToken) {
                const accessToken = typeof configuration.accessToken === 'function'
                    ? await configuration.accessToken()
                    : await configuration.accessToken;
                localVarHeaderParameter["Authorization"] = "Bearer " + accessToken;
            }

            if (pageIndex !== undefined) {
                localVarQueryParameter['pageIndex'] = pageIndex;
            }

            if (pageSize !== undefined) {
                localVarQueryParameter['pageSize'] = pageSize;
            }

            const query = new URLSearchParams(localVarUrlObj.search);
            for (const key in localVarQueryParameter) {
                query.set(key, localVarQueryParameter[key]);
            }
            for (const key in options.params) {
                query.set(key, options.params[key]);
            }
            localVarUrlObj.search = (new URLSearchParams(query)).toString();
            let headersFromBaseOptions = baseOptions && baseOptions.headers ? baseOptions.headers : {};
            localVarRequestOptions.headers = {...localVarHeaderParameter, ...headersFromBaseOptions, ...options.headers};

            return {
                url: localVarUrlObj.pathname + localVarUrlObj.search + localVarUrlObj.hash,
                options: localVarRequestOptions,
            };
        },
        /**
         * 
         * @summary ??????
         * @param {number} taskId ??ID
         * @param {*} [options] Override http request option.
         * @throws {RequiredError}
         */
        apiEventScheduleScheduleTasksTaskIdPausePost: async (taskId: number, options: AxiosRequestConfig = {}): Promise<RequestArgs> => {
            // verify required parameter 'taskId' is not null or undefined
            if (taskId === null || taskId === undefined) {
                throw new RequiredError('taskId','Required parameter taskId was null or undefined when calling apiEventScheduleScheduleTasksTaskIdPausePost.');
            }
            const localVarPath = `/api/eventSchedule/schedule-tasks/{taskId}/pause`
                .replace(`{${"taskId"}}`, encodeURIComponent(String(taskId)));
            // use dummy base URL string because the URL constructor only accepts absolute URLs.
            const localVarUrlObj = new URL(localVarPath, 'https://example.com');
            let baseOptions;
            if (configuration) {
                baseOptions = configuration.baseOptions;
            }
            const localVarRequestOptions :AxiosRequestConfig = { method: 'POST', ...baseOptions, ...options};
            const localVarHeaderParameter = {} as any;
            const localVarQueryParameter = {} as any;

            // authentication Bearer required
            // http bearer authentication required
            if (configuration && configuration.accessToken) {
                const accessToken = typeof configuration.accessToken === 'function'
                    ? await configuration.accessToken()
                    : await configuration.accessToken;
                localVarHeaderParameter["Authorization"] = "Bearer " + accessToken;
            }

            const query = new URLSearchParams(localVarUrlObj.search);
            for (const key in localVarQueryParameter) {
                query.set(key, localVarQueryParameter[key]);
            }
            for (const key in options.params) {
                query.set(key, options.params[key]);
            }
            localVarUrlObj.search = (new URLSearchParams(query)).toString();
            let headersFromBaseOptions = baseOptions && baseOptions.headers ? baseOptions.headers : {};
            localVarRequestOptions.headers = {...localVarHeaderParameter, ...headersFromBaseOptions, ...options.headers};

            return {
                url: localVarUrlObj.pathname + localVarUrlObj.search + localVarUrlObj.hash,
                options: localVarRequestOptions,
            };
        },
        /**
         * 
         * @summary ??????
         * @param {number} taskId ??ID
         * @param {*} [options] Override http request option.
         * @throws {RequiredError}
         */
        apiEventScheduleScheduleTasksTaskIdResumePost: async (taskId: number, options: AxiosRequestConfig = {}): Promise<RequestArgs> => {
            // verify required parameter 'taskId' is not null or undefined
            if (taskId === null || taskId === undefined) {
                throw new RequiredError('taskId','Required parameter taskId was null or undefined when calling apiEventScheduleScheduleTasksTaskIdResumePost.');
            }
            const localVarPath = `/api/eventSchedule/schedule-tasks/{taskId}/resume`
                .replace(`{${"taskId"}}`, encodeURIComponent(String(taskId)));
            // use dummy base URL string because the URL constructor only accepts absolute URLs.
            const localVarUrlObj = new URL(localVarPath, 'https://example.com');
            let baseOptions;
            if (configuration) {
                baseOptions = configuration.baseOptions;
            }
            const localVarRequestOptions :AxiosRequestConfig = { method: 'POST', ...baseOptions, ...options};
            const localVarHeaderParameter = {} as any;
            const localVarQueryParameter = {} as any;

            // authentication Bearer required
            // http bearer authentication required
            if (configuration && configuration.accessToken) {
                const accessToken = typeof configuration.accessToken === 'function'
                    ? await configuration.accessToken()
                    : await configuration.accessToken;
                localVarHeaderParameter["Authorization"] = "Bearer " + accessToken;
            }

            const query = new URLSearchParams(localVarUrlObj.search);
            for (const key in localVarQueryParameter) {
                query.set(key, localVarQueryParameter[key]);
            }
            for (const key in options.params) {
                query.set(key, options.params[key]);
            }
            localVarUrlObj.search = (new URLSearchParams(query)).toString();
            let headersFromBaseOptions = baseOptions && baseOptions.headers ? baseOptions.headers : {};
            localVarRequestOptions.headers = {...localVarHeaderParameter, ...headersFromBaseOptions, ...options.headers};

            return {
                url: localVarUrlObj.pathname + localVarUrlObj.search + localVarUrlObj.hash,
                options: localVarRequestOptions,
            };
        },
    }
};

/**
 * EventScheduleApi - functional programming interface
 * @export
 */
export const EventScheduleApiFp = function(configuration?: Configuration) {
    return {
        /**
         * 
         * @summary ?????????
         * @param {number} eventId ??ID
         * @param {*} [options] Override http request option.
         * @throws {RequiredError}
         */
        async apiEventScheduleEventsEventIdScheduleTasksGet(eventId: number, options?: AxiosRequestConfig): Promise<(axios?: AxiosInstance, basePath?: string) => Promise<AxiosResponse<RESTfulResultActionResultListEventScheduleTaskResponse>>> {
            const localVarAxiosArgs = await EventScheduleApiAxiosParamCreator(configuration).apiEventScheduleEventsEventIdScheduleTasksGet(eventId, options);
            return (axios: AxiosInstance = globalAxios, basePath: string = BASE_PATH) => {
                const axiosRequestArgs :AxiosRequestConfig = {...localVarAxiosArgs.options, url: basePath + localVarAxiosArgs.url};
                return axios.request(axiosRequestArgs);
            };
        },
        /**
         * 
         * @summary ????????
         * @param {number} [days] ????
         * @param {*} [options] Override http request option.
         * @throws {RequiredError}
         */
        async apiEventScheduleScheduleTasksCleanLogsPost(days?: number, options?: AxiosRequestConfig): Promise<(axios?: AxiosInstance, basePath?: string) => Promise<AxiosResponse<void>>> {
            const localVarAxiosArgs = await EventScheduleApiAxiosParamCreator(configuration).apiEventScheduleScheduleTasksCleanLogsPost(days, options);
            return (axios: AxiosInstance = globalAxios, basePath: string = BASE_PATH) => {
                const axiosRequestArgs :AxiosRequestConfig = {...localVarAxiosArgs.options, url: basePath + localVarAxiosArgs.url};
                return axios.request(axiosRequestArgs);
            };
        },
        /**
         * 
         * @summary ??????
         * @param {number} taskId ??ID
         * @param {*} [options] Override http request option.
         * @throws {RequiredError}
         */
        async apiEventScheduleScheduleTasksTaskIdCancelPost(taskId: number, options?: AxiosRequestConfig): Promise<(axios?: AxiosInstance, basePath?: string) => Promise<AxiosResponse<void>>> {
            const localVarAxiosArgs = await EventScheduleApiAxiosParamCreator(configuration).apiEventScheduleScheduleTasksTaskIdCancelPost(taskId, options);
            return (axios: AxiosInstance = globalAxios, basePath: string = BASE_PATH) => {
                const axiosRequestArgs :AxiosRequestConfig = {...localVarAxiosArgs.options, url: basePath + localVarAxiosArgs.url};
                return axios.request(axiosRequestArgs);
            };
        },
        /**
         * 
         * @summary ????????
         * @param {number} taskId ??ID
         * @param {*} [options] Override http request option.
         * @throws {RequiredError}
         */
        async apiEventScheduleScheduleTasksTaskIdGet(taskId: number, options?: AxiosRequestConfig): Promise<(axios?: AxiosInstance, basePath?: string) => Promise<AxiosResponse<RESTfulResultActionResultEventScheduleTaskResponse>>> {
            const localVarAxiosArgs = await EventScheduleApiAxiosParamCreator(configuration).apiEventScheduleScheduleTasksTaskIdGet(taskId, options);
            return (axios: AxiosInstance = globalAxios, basePath: string = BASE_PATH) => {
                const axiosRequestArgs :AxiosRequestConfig = {...localVarAxiosArgs.options, url: basePath + localVarAxiosArgs.url};
                return axios.request(axiosRequestArgs);
            };
        },
        /**
         * 
         * @summary ????????
         * @param {number} taskId ??ID
         * @param {number} [pageIndex] ??
         * @param {number} [pageSize] ????
         * @param {*} [options] Override http request option.
         * @throws {RequiredError}
         */
        async apiEventScheduleScheduleTasksTaskIdLogsGet(taskId: number, pageIndex?: number, pageSize?: number, options?: AxiosRequestConfig): Promise<(axios?: AxiosInstance, basePath?: string) => Promise<AxiosResponse<RESTfulResultActionResultListEventTaskLog>>> {
            const localVarAxiosArgs = await EventScheduleApiAxiosParamCreator(configuration).apiEventScheduleScheduleTasksTaskIdLogsGet(taskId, pageIndex, pageSize, options);
            return (axios: AxiosInstance = globalAxios, basePath: string = BASE_PATH) => {
                const axiosRequestArgs :AxiosRequestConfig = {...localVarAxiosArgs.options, url: basePath + localVarAxiosArgs.url};
                return axios.request(axiosRequestArgs);
            };
        },
        /**
         * 
         * @summary ??????
         * @param {number} taskId ??ID
         * @param {*} [options] Override http request option.
         * @throws {RequiredError}
         */
        async apiEventScheduleScheduleTasksTaskIdPausePost(taskId: number, options?: AxiosRequestConfig): Promise<(axios?: AxiosInstance, basePath?: string) => Promise<AxiosResponse<void>>> {
            const localVarAxiosArgs = await EventScheduleApiAxiosParamCreator(configuration).apiEventScheduleScheduleTasksTaskIdPausePost(taskId, options);
            return (axios: AxiosInstance = globalAxios, basePath: string = BASE_PATH) => {
                const axiosRequestArgs :AxiosRequestConfig = {...localVarAxiosArgs.options, url: basePath + localVarAxiosArgs.url};
                return axios.request(axiosRequestArgs);
            };
        },
        /**
         * 
         * @summary ??????
         * @param {number} taskId ??ID
         * @param {*} [options] Override http request option.
         * @throws {RequiredError}
         */
        async apiEventScheduleScheduleTasksTaskIdResumePost(taskId: number, options?: AxiosRequestConfig): Promise<(axios?: AxiosInstance, basePath?: string) => Promise<AxiosResponse<void>>> {
            const localVarAxiosArgs = await EventScheduleApiAxiosParamCreator(configuration).apiEventScheduleScheduleTasksTaskIdResumePost(taskId, options);
            return (axios: AxiosInstance = globalAxios, basePath: string = BASE_PATH) => {
                const axiosRequestArgs :AxiosRequestConfig = {...localVarAxiosArgs.options, url: basePath + localVarAxiosArgs.url};
                return axios.request(axiosRequestArgs);
            };
        },
    }
};

/**
 * EventScheduleApi - factory interface
 * @export
 */
export const EventScheduleApiFactory = function (configuration?: Configuration, basePath?: string, axios?: AxiosInstance) {
    return {
        /**
         * 
         * @summary ?????????
         * @param {number} eventId ??ID
         * @param {*} [options] Override http request option.
         * @throws {RequiredError}
         */
        async apiEventScheduleEventsEventIdScheduleTasksGet(eventId: number, options?: AxiosRequestConfig): Promise<AxiosResponse<RESTfulResultActionResultListEventScheduleTaskResponse>> {
            return EventScheduleApiFp(configuration).apiEventScheduleEventsEventIdScheduleTasksGet(eventId, options).then((request) => request(axios, basePath));
        },
        /**
         * 
         * @summary ????????
         * @param {number} [days] ????
         * @param {*} [options] Override http request option.
         * @throws {RequiredError}
         */
        async apiEventScheduleScheduleTasksCleanLogsPost(days?: number, options?: AxiosRequestConfig): Promise<AxiosResponse<void>> {
            return EventScheduleApiFp(configuration).apiEventScheduleScheduleTasksCleanLogsPost(days, options).then((request) => request(axios, basePath));
        },
        /**
         * 
         * @summary ??????
         * @param {number} taskId ??ID
         * @param {*} [options] Override http request option.
         * @throws {RequiredError}
         */
        async apiEventScheduleScheduleTasksTaskIdCancelPost(taskId: number, options?: AxiosRequestConfig): Promise<AxiosResponse<void>> {
            return EventScheduleApiFp(configuration).apiEventScheduleScheduleTasksTaskIdCancelPost(taskId, options).then((request) => request(axios, basePath));
        },
        /**
         * 
         * @summary ????????
         * @param {number} taskId ??ID
         * @param {*} [options] Override http request option.
         * @throws {RequiredError}
         */
        async apiEventScheduleScheduleTasksTaskIdGet(taskId: number, options?: AxiosRequestConfig): Promise<AxiosResponse<RESTfulResultActionResultEventScheduleTaskResponse>> {
            return EventScheduleApiFp(configuration).apiEventScheduleScheduleTasksTaskIdGet(taskId, options).then((request) => request(axios, basePath));
        },
        /**
         * 
         * @summary ????????
         * @param {number} taskId ??ID
         * @param {number} [pageIndex] ??
         * @param {number} [pageSize] ????
         * @param {*} [options] Override http request option.
         * @throws {RequiredError}
         */
        async apiEventScheduleScheduleTasksTaskIdLogsGet(taskId: number, pageIndex?: number, pageSize?: number, options?: AxiosRequestConfig): Promise<AxiosResponse<RESTfulResultActionResultListEventTaskLog>> {
            return EventScheduleApiFp(configuration).apiEventScheduleScheduleTasksTaskIdLogsGet(taskId, pageIndex, pageSize, options).then((request) => request(axios, basePath));
        },
        /**
         * 
         * @summary ??????
         * @param {number} taskId ??ID
         * @param {*} [options] Override http request option.
         * @throws {RequiredError}
         */
        async apiEventScheduleScheduleTasksTaskIdPausePost(taskId: number, options?: AxiosRequestConfig): Promise<AxiosResponse<void>> {
            return EventScheduleApiFp(configuration).apiEventScheduleScheduleTasksTaskIdPausePost(taskId, options).then((request) => request(axios, basePath));
        },
        /**
         * 
         * @summary ??????
         * @param {number} taskId ??ID
         * @param {*} [options] Override http request option.
         * @throws {RequiredError}
         */
        async apiEventScheduleScheduleTasksTaskIdResumePost(taskId: number, options?: AxiosRequestConfig): Promise<AxiosResponse<void>> {
            return EventScheduleApiFp(configuration).apiEventScheduleScheduleTasksTaskIdResumePost(taskId, options).then((request) => request(axios, basePath));
        },
    };
};

/**
 * EventScheduleApi - object-oriented interface
 * @export
 * @class EventScheduleApi
 * @extends {BaseAPI}
 */
export class EventScheduleApi extends BaseAPI {
    /**
     * 
     * @summary ?????????
     * @param {number} eventId ??ID
     * @param {*} [options] Override http request option.
     * @throws {RequiredError}
     * @memberof EventScheduleApi
     */
    public async apiEventScheduleEventsEventIdScheduleTasksGet(eventId: number, options?: AxiosRequestConfig) : Promise<AxiosResponse<RESTfulResultActionResultListEventScheduleTaskResponse>> {
        return EventScheduleApiFp(this.configuration).apiEventScheduleEventsEventIdScheduleTasksGet(eventId, options).then((request) => request(this.axios, this.basePath));
    }
    /**
     * 
     * @summary ????????
     * @param {number} [days] ????
     * @param {*} [options] Override http request option.
     * @throws {RequiredError}
     * @memberof EventScheduleApi
     */
    public async apiEventScheduleScheduleTasksCleanLogsPost(days?: number, options?: AxiosRequestConfig) : Promise<AxiosResponse<void>> {
        return EventScheduleApiFp(this.configuration).apiEventScheduleScheduleTasksCleanLogsPost(days, options).then((request) => request(this.axios, this.basePath));
    }
    /**
     * 
     * @summary ??????
     * @param {number} taskId ??ID
     * @param {*} [options] Override http request option.
     * @throws {RequiredError}
     * @memberof EventScheduleApi
     */
    public async apiEventScheduleScheduleTasksTaskIdCancelPost(taskId: number, options?: AxiosRequestConfig) : Promise<AxiosResponse<void>> {
        return EventScheduleApiFp(this.configuration).apiEventScheduleScheduleTasksTaskIdCancelPost(taskId, options).then((request) => request(this.axios, this.basePath));
    }
    /**
     * 
     * @summary ????????
     * @param {number} taskId ??ID
     * @param {*} [options] Override http request option.
     * @throws {RequiredError}
     * @memberof EventScheduleApi
     */
    public async apiEventScheduleScheduleTasksTaskIdGet(taskId: number, options?: AxiosRequestConfig) : Promise<AxiosResponse<RESTfulResultActionResultEventScheduleTaskResponse>> {
        return EventScheduleApiFp(this.configuration).apiEventScheduleScheduleTasksTaskIdGet(taskId, options).then((request) => request(this.axios, this.basePath));
    }
    /**
     * 
     * @summary ????????
     * @param {number} taskId ??ID
     * @param {number} [pageIndex] ??
     * @param {number} [pageSize] ????
     * @param {*} [options] Override http request option.
     * @throws {RequiredError}
     * @memberof EventScheduleApi
     */
    public async apiEventScheduleScheduleTasksTaskIdLogsGet(taskId: number, pageIndex?: number, pageSize?: number, options?: AxiosRequestConfig) : Promise<AxiosResponse<RESTfulResultActionResultListEventTaskLog>> {
        return EventScheduleApiFp(this.configuration).apiEventScheduleScheduleTasksTaskIdLogsGet(taskId, pageIndex, pageSize, options).then((request) => request(this.axios, this.basePath));
    }
    /**
     * 
     * @summary ??????
     * @param {number} taskId ??ID
     * @param {*} [options] Override http request option.
     * @throws {RequiredError}
     * @memberof EventScheduleApi
     */
    public async apiEventScheduleScheduleTasksTaskIdPausePost(taskId: number, options?: AxiosRequestConfig) : Promise<AxiosResponse<void>> {
        return EventScheduleApiFp(this.configuration).apiEventScheduleScheduleTasksTaskIdPausePost(taskId, options).then((request) => request(this.axios, this.basePath));
    }
    /**
     * 
     * @summary ??????
     * @param {number} taskId ??ID
     * @param {*} [options] Override http request option.
     * @throws {RequiredError}
     * @memberof EventScheduleApi
     */
    public async apiEventScheduleScheduleTasksTaskIdResumePost(taskId: number, options?: AxiosRequestConfig) : Promise<AxiosResponse<void>> {
        return EventScheduleApiFp(this.configuration).apiEventScheduleScheduleTasksTaskIdResumePost(taskId, options).then((request) => request(this.axios, this.basePath));
    }
}
