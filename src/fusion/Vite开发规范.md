# FusionTrack 开发规范文档

## 目录

- [FusionTrack 开发规范文档](#fusiontrack-开发规范文档)
  - [目录](#目录)
  - [项目架构概述](#项目架构概述)
  - [API 调用规范](#api-调用规范)
    - [使用 API 服务](#使用-api-服务)
      - [1. 单例服务类模式（推荐）](#1-单例服务类模式推荐)
      - [2. 静态方法服务类模式](#2-静态方法服务类模式)
    - [错误处理](#错误处理)
    - [加载状态管理](#加载状态管理)
    - [API 封装最佳实践](#api-封装最佳实践)
  - [公共组件使用指南](#公共组件使用指南)
    - [UI 组件库](#ui-组件库)
    - [对话框组件](#对话框组件)
      - [基本使用方法](#基本使用方法)
      - [透明度问题解决方案](#透明度问题解决方案)
    - [页面布局组件](#页面布局组件)
    - [数据表格组件](#数据表格组件)
  - [代码风格指南](#代码风格指南)
    - [TypeScript 规范](#typescript-规范)
    - [React 组件规范](#react-组件规范)
    - [命名规范](#命名规范)
  - [日志管理规范](#日志管理规范)
    - [Logger 工具类使用](#logger-工具类使用)
    - [日志级别说明](#日志级别说明)
    - [配置管理](#配置管理)
    - [统一错误处理](#统一错误处理)
    - [最佳实践](#最佳实践)
  - [最佳实践和常见问题](#最佳实践和常见问题)
    - [API 调用最佳实践](#api-调用最佳实践)
    - [常见问题](#常见问题)

## 项目架构概述

FusionTrack 项目采用前后端分离架构，前端基于 React 框架开发。项目主要目录结构：

```
fusion/
├── components/       # 公共UI组件
├── lib/              # 核心库和工具函数
│   ├── api/          # API接口封装
│   ├── api-services/ # Swagger生成的API服务
│   └── utils/        # 通用工具函数
├── pages/            # 页面组件
└── public/           # 静态资源
```

## API 调用规范

### 使用 API 服务

所有后端 API 调用必须通过`lib/api-services`下生成的 API 服务类进行，禁止直接使用 fetch 或 axios 发起请求。

API 调用有两种推荐模式：

#### 1. 单例服务类模式（推荐）

创建一个服务类并导出其单例，如`auth-api.ts`：

```typescript
import { feature, LoadingManager, getAPI } from '../api-services/axios-utils'
import { SysAuthApi } from '../api-services/apis/sys-auth-api'
import { LoginInput } from '../api-services/models'
import { AxiosResponse } from 'axios'

// 加载状态键
const LOADING_KEY = {
  LOGIN: 'auth.login',
  LOGOUT: 'auth.logout',
}

// 认证API服务类
class AuthApiService {
  async login(
    data: LoginRequest
  ): Promise<AxiosResponse<RESTfulResultLoginOutput>> {
    try {
      LoadingManager.setLoading(LOADING_KEY.LOGIN, true)
      const [error, response] = await feature(
        getAPI(SysAuthApi).login(loginInput)
      )

      if (error) {
        throw error
      }

      return response
    } finally {
      LoadingManager.setLoading(LOADING_KEY.LOGIN, false)
    }
  }

  // 其他方法...

  isLoading(key: string): boolean {
    return LoadingManager.isLoading(key)
  }
}

// 导出单例实例
export const authApi = new AuthApiService()
```

#### 2. 静态方法服务类模式

创建包含静态方法的服务类，如`device-api.ts`：

```typescript
import { feature, LoadingManager, getAPI } from '../api-services/axios-utils'
import { DeviceApi } from '../api-services/apis/device-api'
import { RESTfulResultDevice } from '../api-services/models'
import { AxiosResponse } from 'axios'

// 加载状态键
const LOADING_KEY = {
  GET_DEVICES: 'device.getDevices',
}

// 设备服务类
export class DeviceService {
  static async getDevices(
    page: number = 1,
    pageSize: number = 10
  ): Promise<PaginatedResponse<Device>> {
    try {
      LoadingManager.setLoading(LOADING_KEY.GET_DEVICES, true)

      const [error, response] = await feature<
        AxiosResponse<RESTfulResultSqlSugarPagedListDevice>
      >(getAPI(DeviceApi).page(undefined, page, pageSize))

      if (error) {
        return {
          items: [],
          total: 0,
          page,
          pageSize,
          totalPages: 0,
          totalItems: 0,
        }
      }

      // 转换响应格式
      // ...处理响应数据...

      return result
    } finally {
      LoadingManager.setLoading(LOADING_KEY.GET_DEVICES, false)
    }
  }

  // 其他方法...

  static isLoading(key: string): boolean {
    return LoadingManager.isLoading(key)
  }
}
```

### 错误处理

所有 API 调用必须使用`feature`函数进行错误处理：

```typescript
const [error, response] = await feature(apiCall)

if (error) {
  // 处理错误情况
  console.error('操作失败:', error)
  return errorResult
}

// 处理成功情况
```

### 加载状态管理

使用`LoadingManager`管理 API 加载状态：

```typescript
try {
  // 设置加载状态为true
  LoadingManager.setLoading(LOADING_KEY, true)

  // 执行API调用
  // ...
} finally {
  // 无论成功失败，都设置加载状态为false
  LoadingManager.setLoading(LOADING_KEY, false)
}
```

检查加载状态：

```typescript
const isLoading = LoadingManager.isLoading(LOADING_KEY)
```

### API 封装最佳实践

1. **统一命名规范**：

   - API 状态键使用大写常量，如`LOADING_KEY.GET_DEVICES`
   - 方法名使用驼峰命名，如`getDevices`、`createDevice`

2. **统一返回格式**：

   - 返回格式应保持一致，推荐使用接口定义返回类型
   - 错误处理应返回合适的默认值和错误信息

3. **类型定义**：
   - 使用 TypeScript 接口定义请求参数和返回值类型
   - 利用 Swagger 生成的模型类型

## 公共组件使用指南

### UI 组件库

项目使用自定义 UI 组件库，位于`components/ui`目录。使用示例：

```tsx
import { Button } from '@/components/ui/button'
import { toast } from '@/components/ui/use-toast'

function MyComponent() {
  const handleClick = () => {
    toast({
      title: '操作成功',
      description: '数据已更新',
    })
  }

  return <Button onClick={handleClick}>点击</Button>
}
```

### 对话框组件

项目使用自定义对话框组件，位于`components/ui/dialog.tsx`。对话框组件支持背景透明度的自定义设置。

#### 基本使用方法

```tsx
import {
  Dialog,
  DialogContent,
  DialogHeader,
  DialogTitle,
  DialogDescription,
} from '@/components/ui/dialog'

function MyDialog({ open, onOpenChange }) {
  return (
    <Dialog open={open} onOpenChange={onOpenChange}>
      <DialogContent>
        <DialogHeader>
          <DialogTitle>对话框标题</DialogTitle>
          <DialogDescription>对话框描述</DialogDescription>
        </DialogHeader>
        {/* 对话框内容 */}
      </DialogContent>
    </Dialog>
  )
}
```

#### 对话框无障碍访问要求

**重要：** 每个 `DialogContent` 都必须包含 `DialogTitle` 以确保屏幕阅读器用户的可访问性。

如果您不希望显示标题，可以使用 `VisuallyHidden` 组件：

```tsx
import { VisuallyHidden } from '@/components/ui/visually-hidden'

function MyDialog({ open, onOpenChange }) {
  return (
    <Dialog open={open} onOpenChange={onOpenChange}>
      <DialogContent>
        <DialogHeader>
          <VisuallyHidden>
            <DialogTitle>对话框标题</DialogTitle>
          </VisuallyHidden>
          <DialogDescription>对话框描述</DialogDescription>
        </DialogHeader>
        {/* 对话框内容 */}
      </DialogContent>
    </Dialog>
  )
}
```

**无障碍访问最佳实践：**
- 始终为对话框提供有意义的标题
- 使用 `DialogDescription` 提供额外的上下文信息
- 确保对话框内容的焦点管理正确
- 支持键盘导航（ESC 键关闭对话框）

#### 透明度问题解决方案

在某些情况下，对话框的背景透明度可能导致内容不够清晰。解决此问题的最佳方案如下：

1. **明确设置背景色和边框样式**

通过在 `DialogContent` 组件中添加明确的样式类，增强对话框的可见性：

```tsx
<DialogContent
  className="max-w-4xl bg-white border-2 border-gray-300 shadow-xl"
  backgroundOpacity={0.9}>
  {/* 内容 */}
</DialogContent>
```

2. **关键样式说明**

- `bg-white`：设置对话框背景为纯白色，提高内容可见性
- `border-2 border-gray-300`：添加明显的边框，增强对比度
- `shadow-xl`：添加较大的阴影效果，使对话框在视觉上更突出
- `backgroundOpacity={0.9}`：将背景层透明度设为 0.9（接近不透明）

3. **样式组合示例**

```tsx
<Dialog open={showAddPointDialog} onOpenChange={setShowAddPointDialog}>
  <DialogContent
    className="max-w-4xl max-h-[90vh] overflow-auto bg-white border-2 border-gray-300 shadow-xl"
    backgroundOpacity={0.9}>
    <DialogHeader>
      <DialogTitle>新增点位</DialogTitle>
      <DialogDescription>为设备添加新的数据点位</DialogDescription>
    </DialogHeader>
    {/* 表单内容 */}
  </DialogContent>
</Dialog>
```

4. **全局透明度配置**

如果需要修改项目中所有对话框的默认透明度设置，可以在 `components/ui/dialog-with-opacity.tsx` 文件中修改 `DEFAULT_DIALOG_OPACITY` 常量的值：

```typescript
// 全局对话框透明度配置
export const DEFAULT_DIALOG_OPACITY = 0.3 // 可以根据需要调整此值
```

### 页面布局组件

使用标准布局组件，如`PageLayout`、`ContentContainer`等：

```tsx
import { PageLayout } from '@/components/layout/page-layout'
import { ContentContainer } from '@/components/layout/content-container'

function MyPage() {
  return (
    <PageLayout title="设备管理">
      <ContentContainer>{/* 页面内容 */}</ContentContainer>
    </PageLayout>
  )
}
```

### 数据表格组件

使用标准数据表格组件：

```tsx
import { DataTable } from '@/components/data-table'
import { columns } from './columns'

function DeviceList() {
  const { data, isLoading } = useDevices()

  return (
    <DataTable
      columns={columns}
      data={data}
      isLoading={isLoading}
      pagination={{ ... }}
    />
  )
}
```

## 代码风格指南

### TypeScript 规范

1. 使用接口定义数据结构，避免使用`any`类型
2. 使用可选链操作符（`?.`）和空值合并操作符（`??`）
3. 使用类型断言时，优先使用`as`语法而非尖括号语法

### React 组件规范

1. 使用函数组件和 React Hooks
2. 使用解构赋值提取 props
3. 抽取复杂逻辑到自定义 hooks
4. 使用 memo 优化渲染性能

### 命名规范

1. 文件命名：使用 kebab-case（如`device-api.ts`）
2. 组件命名：使用 PascalCase（如`DeviceList`）
3. 变量/函数命名：使用 camelCase（如`getDeviceList`）
4. 常量命名：使用 UPPER_SNAKE_CASE（如`API_BASE_URL`）

## 日志管理规范

FusionTrack 项目采用统一的日志管理系统，提供分级日志控制、性能优化和环境感知功能。

### Logger 工具类使用

项目提供了统一的 Logger 工具类，位于 `lib/utils/logger.ts`。

#### 基本使用方法

```typescript
import { logger, log } from '@/lib/utils/logger'

// 使用 logger 实例
logger.debug('调试信息', { data: 'some data' })
logger.info('一般信息', 'operation completed')
logger.warn('警告信息', 'deprecated feature used')
logger.error('错误信息', error)

// 使用便捷方法
log.debug('调试信息')
log.info('一般信息')
log.warn('警告信息')
log.error('错误信息')
```

#### 替换现有的 console.log

**禁止直接使用 console.log**，应该使用 Logger 工具类：

```typescript
// ❌ 错误做法
console.log('用户登录成功')
console.error('API 请求失败')

// ✅ 正确做法
import { log } from '@/lib/utils/logger'

log.info('用户登录成功')
log.error('API 请求失败', error)
```

### 日志级别说明

Logger 支持以下日志级别，按优先级从低到高：

1. **DEBUG (0)** - 详细的调试信息
   - 用于开发阶段的详细调试
   - 包含变量值、函数调用等详细信息
   - 生产环境默认不显示

2. **INFO (1)** - 一般信息
   - 用于记录正常的业务流程
   - 如用户操作、系统状态变化等
   - 默认日志级别

3. **WARN (2)** - 警告信息
   - 用于记录潜在问题或不推荐的操作
   - 如使用了废弃的功能、配置不当等
   - 生产环境默认显示

4. **ERROR (3)** - 错误信息
   - 用于记录错误和异常
   - 如 API 请求失败、数据验证错误等
   - 始终显示

5. **NONE (4)** - 禁用日志
   - 完全禁用日志输出

### 配置管理

日志配置通过系统配置管理，可在 **设置 > 系统配置 > 高级设置** 中进行配置：

#### 配置选项

1. **调试模式** - 开启后强制使用 DEBUG 级别
2. **日志级别** - 设置最低输出级别（调试模式下被忽略）
3. **控制台日志** - 是否在浏览器控制台输出日志
4. **日志持久化** - 是否将日志保存到本地存储
5. **最大日志条数** - 本地存储的最大日志条数

#### 程序化配置

```typescript
import { logger, LogLevel } from '@/lib/utils/logger'

// 设置日志级别
logger.setLevel(LogLevel.DEBUG)

// 启用/禁用日志
logger.setEnabled(true)

// 启用/禁用持久化
logger.setPersist(true)

// 设置最大日志条数
logger.setMaxLogs(2000)
```

### 统一错误处理

项目提供了统一的try-catch封装工具，位于 `lib/utils/safe-execute.ts`，自动处理错误日志记录。

#### 基本使用方法

```typescript
import {
  safeExecute,
  safeExecuteAsync,
  safeExecuteWithResult,
  safeExecuteWithRetry
} from '@/lib/utils/safe-execute'

// 同步方法安全执行
const success = safeExecute(() => {
  // 可能抛出异常的代码
  riskyOperation()
}, {
  operation: '执行风险操作',
  context: { userId: 123 }
})

// 异步方法安全执行
const success = await safeExecuteAsync(async () => {
  await riskyAsyncOperation()
}, {
  operation: '执行异步风险操作'
})

// 带返回值的安全执行
const result = safeExecuteWithResult(() => {
  return calculateSomething()
}, {
  operation: '计算操作',
  defaultValue: 0 // 出错时的默认值
})

// 带重试机制的异步执行
const result = await safeExecuteWithRetry(async () => {
  return await apiCall()
}, {
  operation: 'API调用',
  defaultValue: null,
  maxRetries: 3,
  retryDelay: 1000
})
```

#### 替换传统try-catch

**推荐使用统一封装**替代手动try-catch：

```typescript
// ❌ 传统做法
try {
  await apiCall()
  log.info('API调用成功')
} catch (error) {
  log.error('API调用失败', error)
  // 错误处理逻辑
}

// ✅ 推荐做法
const success = await safeExecuteAsync(async () => {
  await apiCall()
  log.info('API调用成功')
}, {
  operation: 'API调用',
  onError: (error) => {
    // 自定义错误处理逻辑
    handleApiError(error)
  }
})
```

#### 配置选项

```typescript
interface SafeExecuteOptions {
  operation?: string           // 操作描述
  silent?: boolean            // 静默模式（不记录错误日志）
  onError?: (error, context) => void  // 自定义错误处理
  context?: any               // 上下文信息
  rethrow?: boolean          // 是否重新抛出错误
}
```

#### 装饰器模式

```typescript
import { withErrorHandling, withAsyncErrorHandling } from '@/lib/utils/safe-execute'

// 同步函数装饰器
const safeCalculate = withErrorHandling(calculate, {
  operation: '数值计算',
  defaultValue: 0
})

// 异步函数装饰器
const safeApiCall = withAsyncErrorHandling(apiCall, {
  operation: 'API调用'
})
```

### 最佳实践

#### 1. 日志内容规范

```typescript
// ✅ 好的日志实践
log.info('用户登录', { userId: user.id, timestamp: Date.now() })
log.error('API 请求失败', { url: '/api/users', status: 500, error })
log.debug('组件渲染', { componentName: 'UserList', props })

// ❌ 避免的做法
log.info('something happened')  // 信息不明确
log.error('error')              // 缺少上下文
log.debug(user)                 // 直接输出对象，缺少说明
```

#### 2. 性能考虑

```typescript
// ✅ 延迟计算日志内容
log.debug(() => `复杂计算结果: ${expensiveCalculation()}`)

// ❌ 总是执行计算
log.debug(`复杂计算结果: ${expensiveCalculation()}`)  // 即使日志级别不满足也会执行
```

#### 3. 错误日志

```typescript
// ✅ 完整的错误信息
try {
  await apiCall()
} catch (error) {
  log.error('API 调用失败', {
    operation: 'getUserList',
    error: error.message,
    stack: error.stack,
    timestamp: Date.now()
  })
}
```

#### 4. 组件日志

```typescript
// ✅ 组件生命周期日志
function UserComponent({ userId }: { userId: string }) {
  useEffect(() => {
    log.debug('UserComponent 挂载', { userId })

    return () => {
      log.debug('UserComponent 卸载', { userId })
    }
  }, [userId])

  const handleClick = () => {
    log.info('用户点击操作', { userId, action: 'profile_view' })
  }

  // ...
}
```

#### 5. 日志管理

```typescript
// 获取历史日志
const logs = logger.getLogs()

// 获取特定级别的日志
const errorLogs = logger.getLogsByLevel(LogLevel.ERROR)

// 清空日志
logger.clearLogs()

// 导出日志
const logData = logger.exportLogs()
console.log(logData) // JSON 格式的日志数据
```

#### 6. 环境感知

Logger 会自动检测运行环境：

- **开发环境** - 默认显示所有级别的日志
- **生产环境** - 默认只显示 WARN 和 ERROR 级别的日志
- **调试模式** - 强制显示 DEBUG 级别的日志

#### 7. 配置同步

Logger 配置会自动与系统配置同步：

```typescript
// 配置变更时会自动同步到 Logger
// 无需手动调用，系统会自动处理
```

### 存储管理

项目提供了智能的localStorage管理功能，自动处理存储空间不足的问题。

#### 自动存储管理

```typescript
import { storage } from '@/lib/utils/storage-manager'

// 安全的存储操作，自动处理空间不足
storage.setItem('key', 'value')  // 返回 boolean 表示是否成功
const value = storage.getItem('key')  // 安全获取
storage.removeItem('key')  // 安全删除

// 获取存储使用情况
const info = storage.getInfo()
console.log(`使用率: ${info.usagePercentage}%`)

// 智能清理
const result = storage.cleanup()
console.log(`清理了 ${result.cleaned} 个项目`)
```

#### 存储空间不足处理

当localStorage空间不足时，系统会自动：

1. **清理过期缓存** - 删除带有过期时间的缓存数据
2. **清理临时数据** - 删除 `temp:` 和 `cache:` 前缀的数据
3. **压缩日志** - 减少日志存储数量
4. **清理大文件** - 删除占用空间最大的非保护项
5. **禁用持久化** - 最后手段，禁用日志持久化功能

#### 受保护的存储项

以下前缀的存储项不会被自动清理：
- `fusion:auth:` - 认证信息
- `fusion:user:` - 用户数据
- `fusion:config:` - 系统配置
- `fusion:settings:` - 用户设置
- `logger-settings` - Logger配置

#### 存储管理工具

访问 **调试工具 > 存储管理** 可以：
- 查看localStorage使用情况
- 手动清理特定类型的数据
- 导出存储使用报告
- 删除不需要的存储项

## 最佳实践和常见问题

### API 调用最佳实践

1. **减少重复代码**：使用 API 服务类封装 API 调用
2. **错误处理**：统一使用`feature`函数处理错误
3. **加载状态**：使用`LoadingManager`管理加载状态
4. **请求取消**：长时间运行的请求应支持取消功能

### 常见问题

1. **API 请求报 401 错误**

   - 检查 token 是否过期
   - 确认是否已登录

2. **API 返回数据格式与前端不一致**

   - 使用数据转换函数处理
   - 检查 API 文档确认格式

3. **组件渲染性能问题**

   - 使用 React.memo 减少不必要的重渲染
   - 使用 useCallback 和 useMemo 缓存函数和计算结果

4. **跨域问题**
   - 配置代理服务器
   - 联系后端添加 CORS 头
