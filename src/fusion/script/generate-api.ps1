# PowerShell script for generating API client
# Windows version equivalent to generate-api.sh

# Constants
$SWAGGER_URL = "http://127.0.0.1:5005/swagger/All%20Groups/swagger.json"
$GENERATOR_URL = "https://generator3.swagger.io/api/generate"
$OUTPUT_DIR = Join-Path -Path $PSScriptRoot -ChildPath "../lib/api-services"
$TEMP_FILE = Join-Path -Path $env:TEMP -ChildPath "api-client.zip"
$TEMP_UNZIP_DIR = Join-Path -Path $env:TEMP -ChildPath "api-client-temp"
$TEMP_PRESERVE_DIR = Join-Path -Path $env:TEMP -ChildPath "api-preserve"

Write-Host "Starting API client generation..." -ForegroundColor Cyan
Write-Host "Configuration:"
Write-Host "- Swagger URL: $SWAGGER_URL"
Write-Host "- Output directory: $OUTPUT_DIR"
Write-Host "- Temporary file: $TEMP_FILE"

# Files to be removed
$REMOVE_FILES = @(
    "$OUTPUT_DIR\.gitignore"
    "$OUTPUT_DIR\.npmignore"
    "$OUTPUT_DIR\.swagger-codegen-ignore"
    "$OUTPUT_DIR\git_push.sh"
    "$OUTPUT_DIR\package.json"
    "$OUTPUT_DIR\README.md"
    "$OUTPUT_DIR\tsconfig.json"
    "$OUTPUT_DIR\swagger-doc.ts"
)

# Create output directory
Write-Host "`nChecking and creating output directory..." -ForegroundColor Cyan
if (-not (Test-Path -Path $OUTPUT_DIR)) {
    Write-Host "- Creating directory: $OUTPUT_DIR"
    New-Item -Path $OUTPUT_DIR -ItemType Directory -Force | Out-Null
}
else {
    Write-Host "- Output directory already exists"
}

Write-Host "`nStep 1: Getting Swagger JSON..." -ForegroundColor Cyan
Write-Host "- Fetching data from $SWAGGER_URL..."
try {
    $SWAGGER_JSON = Invoke-WebRequest -Uri $SWAGGER_URL -UseBasicParsing
    $SWAGGER_CONTENT = $SWAGGER_JSON.Content
    Write-Host "- Swagger JSON retrieved successfully" -ForegroundColor Green
    Write-Host "- JSON data size: $($SWAGGER_CONTENT.Length) bytes"
}
catch {
    Write-Host "- Error: Cannot retrieve Swagger JSON" -ForegroundColor Red
    Write-Host $_.Exception.Message
    exit 1
}

Write-Host "`nStep 2: Generating TypeScript API client code..." -ForegroundColor Cyan
Write-Host "- Sending request to generator service..."
try {
    $swaggerObj = ConvertFrom-Json -InputObject $SWAGGER_CONTENT -ErrorAction Stop
    $RequestBody = @{
        spec = $swaggerObj
        type = "CLIENT"
        lang = "typescript-axios"
    } | ConvertTo-Json -Depth 10 -Compress
    
    Invoke-WebRequest -Uri $GENERATOR_URL -Method Post -Body $RequestBody -ContentType "application/json" -OutFile $TEMP_FILE -ErrorAction Stop
    if (Test-Path -Path $TEMP_FILE) {
        $fileInfo = Get-Item $TEMP_FILE
        Write-Host "- API client code generated successfully" -ForegroundColor Green
        Write-Host "- Generated zip size: $([Math]::Round($fileInfo.Length / 1KB, 2)) KB"
    }
    else {
        throw "Generated zip file not found"
    }
}
catch {
    Write-Host "- Error: API client code generation failed" -ForegroundColor Red
    Write-Host $_.Exception.Message
    exit 1
}

Write-Host "`nStep 3: Extracting files to $OUTPUT_DIR..." -ForegroundColor Cyan
# Create temporary directory for file statistics
Write-Host "- Cleaning temporary extraction directory..."
if (Test-Path -Path $TEMP_UNZIP_DIR) {
    Remove-Item -Path $TEMP_UNZIP_DIR -Recurse -Force
}
New-Item -Path $TEMP_UNZIP_DIR -ItemType Directory -Force | Out-Null

# Extract files
Write-Host "- Extracting files..."
try {
    Add-Type -AssemblyName System.IO.Compression.FileSystem
    [System.IO.Compression.ZipFile]::ExtractToDirectory($TEMP_FILE, $TEMP_UNZIP_DIR)
    if (Test-Path -Path $TEMP_UNZIP_DIR) {
        $fileCount = (Get-ChildItem -Path $TEMP_UNZIP_DIR -File -Recurse).Count
        Write-Host "- Extraction complete" -ForegroundColor Green
        Write-Host "- Extracted $fileCount files"
    }
    else {
        throw "Extraction directory not found"
    }
}
catch {
    Write-Host "- Error: Extraction failed" -ForegroundColor Red
    Write-Host $_.Exception.Message
    Remove-Item -Path $TEMP_UNZIP_DIR -Recurse -Force -ErrorAction SilentlyContinue
    exit 1
}

# Move files to target directory
Write-Host "- Cleaning target directory..."
# Save files to be preserved to temporary directory
$PRESERVE_FILES = @("axios-utils.ts")
if (Test-Path -Path $TEMP_PRESERVE_DIR) {
    Remove-Item -Path $TEMP_PRESERVE_DIR -Recurse -Force
}
New-Item -Path $TEMP_PRESERVE_DIR -ItemType Directory -Force | Out-Null

Write-Host "- Backing up files to be preserved..."
foreach ($file in $PRESERVE_FILES) {
    $filePath = Join-Path -Path $OUTPUT_DIR -ChildPath $file
    if (Test-Path -Path $filePath) {
        Copy-Item -Path $filePath -Destination $TEMP_PRESERVE_DIR -Force
        Write-Host "  - Backed up: $file" -ForegroundColor Green
    }
    else {
        Write-Host "  - Skipping non-existent file: $file"
    }
}

# Clean directory
if (Test-Path -Path $OUTPUT_DIR) {
    Get-ChildItem -Path $OUTPUT_DIR -Force | Remove-Item -Recurse -Force -ErrorAction SilentlyContinue
}

# Restore preserved files
Write-Host "- Restoring preserved files..."
foreach ($file in $PRESERVE_FILES) {
    $sourceFile = Join-Path -Path $TEMP_PRESERVE_DIR -ChildPath $file
    if (Test-Path -Path $sourceFile) {
        Copy-Item -Path $sourceFile -Destination $OUTPUT_DIR -Force
        Write-Host "  - Restored: $file" -ForegroundColor Green
    }
}

# Clean temporary preservation directory
Remove-Item -Path $TEMP_PRESERVE_DIR -Recurse -Force -ErrorAction SilentlyContinue

Write-Host "- Copying newly generated files to target directory..."
if (Test-Path -Path $TEMP_UNZIP_DIR) {
    Get-ChildItem -Path $TEMP_UNZIP_DIR -Force | Copy-Item -Destination $OUTPUT_DIR -Recurse -Force
} 
else {
    Write-Host "- Warning: Temporary extraction directory does not exist, cannot copy files" -ForegroundColor Yellow
}

Write-Host "`nStep 4: Cleaning unnecessary files..." -ForegroundColor Cyan
Write-Host "Starting to clean the following files:"
foreach ($file in $REMOVE_FILES) {
    if (Test-Path -Path $file) {
        Remove-Item -Path $file -Force
        Write-Host "- Deleted: $file" -ForegroundColor Green
    }
    else {
        Write-Host "- Skipping non-existent file: $file"
    }
}

# Safely delete .swagger-codegen directory
$swaggerCodegenDir = Join-Path -Path $OUTPUT_DIR -ChildPath ".swagger-codegen"
if (Test-Path -Path $swaggerCodegenDir) {
    Remove-Item -Path $swaggerCodegenDir -Recurse -Force
    Write-Host "- Deleted: $swaggerCodegenDir directory" -ForegroundColor Green
}

# Count final generated files
$finalFileCount = 0
if (Test-Path -Path $OUTPUT_DIR) {
    $finalFileCount = (Get-ChildItem -Path $OUTPUT_DIR -File -Recurse).Count
}
Write-Host "`nGeneration statistics:" -ForegroundColor Cyan
Write-Host "- Output directory: $OUTPUT_DIR"
Write-Host "- Final generated file count: $finalFileCount"
Write-Host "- Temporary file location: $TEMP_FILE"

# Clean temporary files
Write-Host "`nCleaning temporary files..." -ForegroundColor Cyan
if (Test-Path -Path $TEMP_FILE) {
    Remove-Item -Path $TEMP_FILE -Force
}
if (Test-Path -Path $TEMP_UNZIP_DIR) {
    Remove-Item -Path $TEMP_UNZIP_DIR -Recurse -Force
}
Write-Host "- Temporary files cleaned" -ForegroundColor Green

Write-Host "`nAPI client generation complete!" -ForegroundColor Cyan
Write-Host "You can view the generated files in the $OUTPUT_DIR directory" 