const fs = require('fs')
const path = require('path')
const archiver = require('archiver')

// 创建一个文件输出流
const output = fs.createWriteStream(path.join(__dirname, '../fusion-deploy.zip'))
const archive = archiver('zip', {
  zlib: { level: 9 } // 设置压缩级别
})

// 监听所有存档数据通过输出流传递完成
output.on('close', function () {
  console.log('打包完成！')
  console.log('总大小: ' + (archive.pointer() / 1024 / 1024).toFixed(2) + ' MB')
})

// 监听警告
archive.on('warning', function (err) {
  if (err.code === 'ENOENT') {
    console.warn('警告:', err)
  } else {
    throw err
  }
})

// 监听错误
archive.on('error', function (err) {
  throw err
})

// 将输出流管道连接到存档
archive.pipe(output)

// 添加文件和目录
const projectRoot = path.join(__dirname, '..')

console.log('开始打包 Vite+React 项目...')

// 检查是否存在 Vite 构建输出目录
const distDir = path.join(projectRoot, 'dist')
const hasViteBuild = fs.existsSync(distDir)

// 添加PM2配置文件
console.log('处理PM2配置文件...')
const ecosystemConfigPath = path.join(projectRoot, 'ecosystem.config.js')
if (fs.existsSync(ecosystemConfigPath)) {
  console.log('添加文件: ecosystem.config.js (从项目根目录)')
  archive.file(ecosystemConfigPath, { name: 'ecosystem.config.js' })
} else {
  console.warn('警告: ecosystem.config.js 不存在，将创建默认配置')
  // 创建默认的PM2配置 - 适配 Vite+React 静态文件服务
  const defaultConfig = `module.exports = {
  apps: [{
    name: 'fusionui',
    script: 'server.js',
    cwd: '/opt/fusionui',
    instances: 1,
    autorestart: true,
    watch: false,
    max_memory_restart: '1G',
    env: {
      NODE_ENV: 'production',
      PORT: 5004
    }
  }]
};`

  // 添加默认配置到压缩包
  console.log('添加文件: ecosystem.config.js (默认配置)')
  archive.append(defaultConfig, { name: 'ecosystem.config.js' })
}

if (!hasViteBuild) {
  console.error('错误: 未找到 Vite 构建输出目录 dist/')
  console.error('请先运行 npm run build 构建项目')
  process.exit(1)
}

console.log('检测到 Vite 构建输出，使用 Vite+React 打包结构...')

// 添加 Vite 构建输出目录（包含所有静态资源）
archive.directory(distDir, false)
console.log('添加目录: dist (根目录内容)')

// 添加 public 目录中的额外静态文件（如果有的话）
const publicDir = path.join(projectRoot, 'public')
if (fs.existsSync(publicDir)) {
  // 检查 public 目录中是否有 dist 中没有的文件
  const publicFiles = fs.readdirSync(publicDir)
  const distFiles = fs.readdirSync(distDir)

  publicFiles.forEach(file => {
    if (!distFiles.includes(file)) {
      const publicFilePath = path.join(publicDir, file)
      const stats = fs.statSync(publicFilePath)

      if (stats.isFile()) {
        archive.file(publicFilePath, { name: file })
        console.log(`添加文件: ${file} (来自 public 目录)`)
      } else if (stats.isDirectory()) {
        archive.directory(publicFilePath, file)
        console.log(`添加目录: ${file} (来自 public 目录)`)
      }
    }
  })
}

// 添加简单的静态文件服务器脚本
console.log('添加静态文件服务器脚本...')
const serverScript = `const express = require('express');
const path = require('path');
const app = express();
const port = process.env.PORT || 5004;

// 设置静态文件目录
app.use(express.static(path.join(__dirname)));

// 处理 SPA 路由，所有路由都返回 index.html
app.get('*', (req, res) => {
  res.sendFile(path.join(__dirname, 'index.html'));
});

app.listen(port, () => {
  console.log(\`FusionTrack 服务器运行在 http://localhost:\${port}\`);
});`

archive.append(serverScript, { name: 'server.js' })
console.log('添加文件: server.js (静态文件服务器)')

// 添加 package.json 用于生产环境依赖
console.log('添加生产环境 package.json...')
const productionPackageJson = {
  "name": "fusiontrack-production",
  "version": "1.0.0",
  "description": "FusionTrack 生产环境部署包",
  "main": "server.js",
  "scripts": {
    "start": "node server.js"
  },
  "dependencies": {
    "express": "^4.18.2"
  },
  "engines": {
    "node": ">=16.0.0"
  }
}

archive.append(JSON.stringify(productionPackageJson, null, 2), { name: 'package.json' })
console.log('添加文件: package.json (生产环境配置)')

// 完成归档
console.log('正在完成归档...')
archive.finalize() 