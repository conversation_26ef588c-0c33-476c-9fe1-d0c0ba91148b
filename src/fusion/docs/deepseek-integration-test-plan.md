# DeepSeek AI 集成测试计划

## 测试概述

本文档描述了 DeepSeek AI 集成的完整测试计划，包括后端 API、前端界面和端到端功能测试。

## 测试环境准备

### 1. 后端服务启动
```bash
# 启动后端服务
cd E:/projects/fusiontrack
dotnet run --project 00-Application/EdgeGateway
```

### 2. 前端服务启动
```bash
# 启动前端服务
cd E:/projects/fusiontrack/src/fusion
npm run dev
```

### 3. 配置文件检查
确认以下文件存在并配置正确：
- `00-Application/EdgeGateway/Configuration/DeepSeek.json`
- 配置文件包含所有必需的参数

## 后端 API 测试

### 1. 配置获取 API
**端点**: `GET /api/ai/config/info`

**预期响应**:
```json
{
  "data": {
    "apiKey": "sk-xxx",
    "baseUrl": "https://api.deepseek.com",
    "model": "deepseek-chat",
    "temperature": 1.0,
    "maxTokens": 4096,
    "topP": 1.0,
    "frequencyPenalty": 0,
    "presencePenalty": 0,
    "streamResponse": true,
    "maxContextMessages": 10,
    "contextExpirationMinutes": 30,
    "defaultSystemPrompt": "你是一个有帮助的 AI 助手。"
  }
}
```

**测试步骤**:
1. 使用 Postman 或 curl 调用 API
2. 验证返回的配置参数完整性
3. 确认数据类型正确

### 2. 配置更新 API
**端点**: `PUT /api/ai/config/update`

**请求体**:
```json
{
  "model": "deepseek-reasoner",
  "apiKey": "sk-new-key",
  "baseUrl": "https://api.deepseek.com",
  "temperature": 0.8,
  "maxTokens": 2048,
  "topP": 0.9,
  "frequencyPenalty": 0.1,
  "presencePenalty": 0.1,
  "streamResponse": true
}
```

**预期响应**:
```json
{
  "data": {
    "success": true,
    "message": "配置更新成功"
  }
}
```

**测试步骤**:
1. 发送配置更新请求
2. 验证响应成功
3. 调用获取配置 API 确认更新生效
4. 检查配置文件是否已更新

### 3. 配置重置 API
**端点**: `POST /api/ai/config/reset`

**预期响应**:
```json
{
  "data": {
    "success": true,
    "message": "配置已重置为默认值"
  }
}
```

**测试步骤**:
1. 发送重置请求
2. 验证响应成功
3. 确认配置恢复为默认值

### 4. 配置验证 API
**端点**: `POST /api/ai/config/validate`

**请求体**:
```json
{
  "model": "invalid-model",
  "apiKey": "",
  "temperature": 3.0
}
```

**预期响应**:
```json
{
  "data": {
    "success": false,
    "message": "配置验证失败",
    "errors": [
      "不支持的模型: invalid-model",
      "API Key 不能为空",
      "Temperature 必须在 0-2 范围内"
    ]
  }
}
```

## 前端界面测试

### 1. 配置面板加载测试
**测试步骤**:
1. 打开浮动助手
2. 切换到 AI 标签页
3. 验证配置面板正确显示
4. 确认所有参数值正确加载

**验证点**:
- [ ] 模型选择框显示正确选项
- [ ] API Key 字段显示（隐藏实际值）
- [ ] 所有滑块显示当前数值
- [ ] 保存和重置按钮可见

### 2. 配置参数修改测试
**测试步骤**:
1. 修改各个配置参数
2. 观察界面实时更新
3. 验证参数范围限制

**验证点**:
- [ ] 模型选择：deepseek-chat, deepseek-reasoner
- [ ] 温度滑块：0-2 范围
- [ ] Token 数量：1024, 2048, 4096, 8192 选项
- [ ] Top-P 滑块：0-1 范围
- [ ] 频率惩罚：-2 到 2 范围
- [ ] 存在惩罚：-2 到 2 范围
- [ ] 流式响应开关正常

### 3. 配置保存测试
**测试步骤**:
1. 修改配置参数
2. 点击保存按钮
3. 观察保存状态和提示

**验证点**:
- [ ] 保存按钮显示加载状态
- [ ] 成功时显示成功提示
- [ ] 失败时显示错误提示
- [ ] 配置持久化到后端

### 4. 配置重置测试
**测试步骤**:
1. 修改配置参数
2. 点击重置按钮
3. 确认配置恢复默认值

**验证点**:
- [ ] 重置按钮正常工作
- [ ] 配置恢复为默认值
- [ ] 界面正确更新

### 5. 错误处理测试
**测试步骤**:
1. 断开网络连接
2. 尝试保存配置
3. 观察错误处理

**验证点**:
- [ ] 网络错误时显示适当提示
- [ ] 服务器错误时显示错误信息
- [ ] 用户界面保持响应

## 端到端功能测试

### 1. 配置流程测试
**测试场景**: 完整的配置修改流程

**测试步骤**:
1. 打开浮动助手
2. 切换到 AI 标签页
3. 修改配置参数
4. 保存配置
5. 刷新页面
6. 验证配置保持

**验证点**:
- [ ] 配置修改正确保存
- [ ] 页面刷新后配置保持
- [ ] 后端配置文件正确更新

### 2. AI 对话测试
**测试场景**: 使用新配置进行 AI 对话

**测试步骤**:
1. 配置 DeepSeek API Key
2. 点击"开始对话"按钮
3. 发送测试消息
4. 验证 AI 响应

**验证点**:
- [ ] 对话框正确打开
- [ ] 配置信息正确显示
- [ ] AI 响应正常（如果 API Key 有效）

### 3. 配置验证测试
**测试场景**: 配置参数验证

**测试步骤**:
1. 输入无效的配置参数
2. 尝试保存配置
3. 观察验证结果

**验证点**:
- [ ] 无效参数被正确识别
- [ ] 显示具体的错误信息
- [ ] 阻止保存无效配置

## 性能测试

### 1. 配置加载性能
**测试指标**:
- 配置加载时间 < 500ms
- 界面响应时间 < 100ms

### 2. 配置保存性能
**测试指标**:
- 配置保存时间 < 1s
- 文件写入成功率 100%

## 兼容性测试

### 1. 浏览器兼容性
**测试浏览器**:
- [ ] Chrome (最新版本)
- [ ] Firefox (最新版本)
- [ ] Edge (最新版本)
- [ ] Safari (如果可用)

### 2. 响应式设计
**测试设备**:
- [ ] 桌面端 (1920x1080)
- [ ] 平板端 (768x1024)
- [ ] 移动端 (375x667)

## 安全测试

### 1. API Key 安全
**验证点**:
- [ ] API Key 在界面中正确隐藏
- [ ] API Key 安全传输到后端
- [ ] API Key 安全存储在配置文件中

### 2. 输入验证
**验证点**:
- [ ] 所有输入参数正确验证
- [ ] 防止 XSS 攻击
- [ ] 防止 SQL 注入（如果适用）

## 回归测试

### 1. 现有功能测试
**验证点**:
- [ ] 浮动助手其他功能正常
- [ ] 工具标签页功能正常
- [ ] 操作标签页功能正常

### 2. 系统集成测试
**验证点**:
- [ ] 不影响其他系统功能
- [ ] 配置服务正常启动
- [ ] 日志记录正常

## 测试报告模板

### 测试执行记录
```
测试日期: ____
测试人员: ____
测试环境: ____

后端 API 测试:
- 配置获取 API: ✅/❌
- 配置更新 API: ✅/❌
- 配置重置 API: ✅/❌
- 配置验证 API: ✅/❌

前端界面测试:
- 配置面板加载: ✅/❌
- 参数修改: ✅/❌
- 配置保存: ✅/❌
- 配置重置: ✅/❌
- 错误处理: ✅/❌

端到端测试:
- 配置流程: ✅/❌
- AI 对话: ✅/❌
- 配置验证: ✅/❌

发现的问题:
1. ____
2. ____

建议改进:
1. ____
2. ____
```

## 自动化测试

### 1. 单元测试
```bash
# 运行前端单元测试
npm run test

# 运行后端单元测试
dotnet test
```

### 2. 集成测试
```bash
# 运行集成测试
npm run test:integration
```

### 3. E2E 测试
```bash
# 运行端到端测试
npm run test:e2e
```

## 测试数据

### 有效配置示例
```json
{
  "model": "deepseek-chat",
  "apiKey": "sk-valid-key-here",
  "baseUrl": "https://api.deepseek.com",
  "temperature": 1.0,
  "maxTokens": 4096,
  "topP": 1.0,
  "frequencyPenalty": 0,
  "presencePenalty": 0,
  "streamResponse": true
}
```

### 无效配置示例
```json
{
  "model": "invalid-model",
  "apiKey": "",
  "baseUrl": "invalid-url",
  "temperature": 3.0,
  "maxTokens": 10000,
  "topP": 2.0,
  "frequencyPenalty": 5,
  "presencePenalty": -5,
  "streamResponse": "invalid"
}
```

## 测试完成标准

所有测试项目必须通过，包括：
- [ ] 所有 API 端点正常工作
- [ ] 前端界面功能完整
- [ ] 配置持久化正常
- [ ] 错误处理完善
- [ ] 性能指标达标
- [ ] 安全要求满足
- [ ] 兼容性良好
- [ ] 回归测试通过

## 后续维护

### 1. 监控指标
- API 响应时间
- 配置保存成功率
- 错误日志监控

### 2. 定期检查
- 每周检查配置文件完整性
- 每月进行功能回归测试
- 季度性能评估
