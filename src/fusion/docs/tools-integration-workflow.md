# 工具迁移与重构工作流程指南

## 概述

本文档提供了将浮动助手(floating-assistant)中现有工具迁移到可复用组件架构的详细工作流程。通过这个过程，我们可以提高代码质量、减少重复代码，并使工具在多个场景中可复用。

## 迁移流程图

```
┌────────────────────┐     ┌────────────────────┐     ┌────────────────────┐
│  1. 分析现有工具   │────▶│  2. 提取核心逻辑   │────▶│  3. 创建独立组件   │
└────────────────────┘     └────────────────────┘     └────────────────────┘
           │                                                     │
           │                                                     ▼
┌────────────────────┐                            ┌────────────────────┐
│  6. 测试与优化     │◀───────────────────────────│  4. 创建对话框     │
└────────────────────┘                            └────────────────────┘
           │                                                     │
           ▼                                                     ▼
┌────────────────────┐                            ┌────────────────────┐
│  7. 文档与示例     │                            │  5. 更新浮动助手   │
└────────────────────┘                            └────────────────────┘
```

## 详细步骤

### 1. 分析现有工具

**目标**: 了解工具的功能、状态管理和交互逻辑

- 分析工具在浮动助手中的实现
- 识别核心功能和状态
- 确定工具的依赖项和外部交互
- 记录用户交互流程

**示例检查项**:

- [ ] 工具的主要功能是什么？
- [ ] 工具管理了哪些状态？
- [ ] 工具依赖哪些外部库或 API？
- [ ] 工具如何处理错误和边缘情况？

### 2. 提取核心逻辑

**目标**: 将业务逻辑从 UI 中分离，创建可复用的 Hook

- 创建专用的 Hook 文件
- 将状态管理和业务逻辑迁移到 Hook 中
- 确保 Hook 是 UI 无关的
- 添加适当的类型定义

**示例代码**:

```typescript
// hooks/use-tool-name.ts

import { useState, useCallback } from 'react'

export interface ToolState {
  // 状态接口定义
}

export function useToolName() {
  // 从浮动助手中提取的状态
  const [state, setState] = useState<ToolState>({
    // 初始状态
  })

  // 从浮动助手中提取的方法
  const processData = useCallback(
    () => {
      // 实现逻辑
    },
    [
      /* 依赖项 */
    ]
  )

  return {
    // 返回状态和方法
    state,
    processData,
    // 其他方法...
  }
}
```

### 3. 创建独立组件

**目标**: 创建可在任何地方使用的 UI 组件

- 在 `components/debug-tools/` 目录下创建组件文件
- 使用步骤 2 中创建的 Hook
- 实现组件 UI 和交互逻辑
- 确保组件是自包含的，不依赖于特定上下文

**示例代码**:

```typescript
// components/debug-tools/tool-name.tsx

import { useToolName } from '@/hooks/use-tool-name'

export function ToolName() {
  const { state, processData } = useToolName()

  return (
    <div className="tool-container">
      {/* 从浮动助手迁移的UI */}
      <div className="tool-header">
        <h3>工具标题</h3>
      </div>

      <div className="tool-content">{/* 工具内容 */}</div>

      <div className="tool-footer">
        <button onClick={processData}>处理数据</button>
      </div>
    </div>
  )
}
```

### 4. 创建对话框包装器

**目标**: 创建可在浮动助手中使用的对话框版本

- 在 `components/ui/` 目录下创建对话框组件
- 使用步骤 3 中创建的组件
- 添加导航到完整工具页面的功能
- 实现全屏模式和其他对话框特定功能

**示例代码**:

```typescript
// components/ui/tool-name-dialog.tsx

import { useState } from 'react'
import {
  Dialog,
  DialogContent,
  DialogHeader,
  DialogTitle,
  DialogFooter,
} from '@/components/ui/dialog'
import { Button } from '@/components/ui/button'
import { ToolName } from '@/components/debug-tools/tool-name'
import { useNavigate } from 'react-router-dom'
import { ExternalLink } from 'lucide-react'

interface ToolNameDialogProps {
  open: boolean
  onOpenChange: (open: boolean) => void
  initialData?: any
}

export function ToolNameDialog({
  open,
  onOpenChange,
  initialData,
}: ToolNameDialogProps) {
  const navigate = useNavigate()
  const [isFullscreen, setIsFullscreen] = useState(false)

  // 跳转到完整工具页面
  const goToFullTool = () => {
    onOpenChange(false)
    navigate('/debug-tools/tool-name')
  }

  if (isFullscreen) {
    return (
      <div className="fixed inset-0 z-50 bg-background flex flex-col">
        {/* 全屏模式UI */}
        <div className="flex items-center justify-between p-4 border-b">
          <h2 className="text-lg font-semibold">工具名称</h2>
          <div className="flex items-center gap-2">
            <Button variant="outline" onClick={() => setIsFullscreen(false)}>
              退出全屏
            </Button>
            <Button variant="outline" onClick={goToFullTool}>
              <ExternalLink className="h-4 w-4 mr-2" />
              打开完整工具
            </Button>
          </div>
        </div>
        <div className="flex-1 p-4 overflow-auto">
          <ToolName />
        </div>
      </div>
    )
  }

  return (
    <Dialog open={open} onOpenChange={onOpenChange}>
      <DialogContent className="max-w-4xl">
        <DialogHeader>
          <DialogTitle>工具名称</DialogTitle>
        </DialogHeader>
        <div className="py-4">
          <ToolName />
        </div>
        <DialogFooter className="flex justify-between">
          <Button variant="outline" onClick={() => setIsFullscreen(true)}>
            全屏模式
          </Button>
          <Button variant="outline" onClick={goToFullTool}>
            <ExternalLink className="h-4 w-4 mr-2" />
            打开完整工具
          </Button>
        </DialogFooter>
      </DialogContent>
    </Dialog>
  )
}
```

### 5. 更新浮动助手

**目标**: 在浮动助手中使用新的对话框组件

- 导入步骤 4 中创建的对话框组件
- 删除原有的内联实现
- 更新工具列表和工具打开逻辑
- 确保功能与之前相同

**示例代码**:

```typescript
// components/ui/floating-assistant.tsx

import { ToolNameDialog } from '@/components/ui/tool-name-dialog'

// ... 现有代码 ...

export function FloatingAssistant() {
  // ... 现有代码 ...

  // 工具列表
  const tools = [
    {
      id: 'tool-name',
      name: '工具名称',
      category: 'utility',
      icon: <ToolIcon className="h-5 w-5" />,
      onClick: () => openToolDialog('tool-name'),
    },
    // ... 其他工具 ...
  ]

  return (
    <>
      {/* 主要浮动助手UI */}
      <div>{/* ... 现有UI ... */}</div>

      {/* 工具对话框 */}
      <ToolNameDialog
        open={activeToolDialog === 'tool-name'}
        onOpenChange={(open) => {
          if (!open) closeToolDialog()
        }}
      />

      {/* 其他对话框 */}
    </>
  )
}
```

### 6. 测试与优化

**目标**: 确保迁移后的工具功能正常且性能良好

- 测试独立组件的功能
- 测试对话框的功能
- 测试在浮动助手中的集成
- 测试在调试工具页面中的使用
- 优化性能和用户体验

**测试检查项**:

- [ ] 所有功能是否正常工作？
- [ ] 状态管理是否正确？
- [ ] 错误处理是否恰当？
- [ ] 用户体验是否一致？
- [ ] 性能是否可接受？

### 7. 文档与示例

**目标**: 提供清晰的文档和使用示例

- 创建工具文档，说明功能和用法
- 提供集成示例
- 记录 API 和参数
- 添加注释和类型定义

**文档内容示例**:

````markdown
# 工具名称

## 功能概述

简要描述工具的主要功能和用途。

## 使用方法

### 作为独立组件使用

```tsx
import { ToolName } from '@/components/debug-tools/tool-name'

function MyComponent() {
  return <ToolName />
}
```
````

### 作为对话框使用

```tsx
import { ToolNameDialog } from '@/components/ui/tool-name-dialog'

function MyComponent() {
  const [open, setOpen] = useState(false)

  return (
    <>
      <Button onClick={() => setOpen(true)}>打开工具</Button>
      <ToolNameDialog open={open} onOpenChange={setOpen} />
    </>
  )
}
```

## API 参考

### useToolName Hook

| 参数         | 类型      | 描述     |
| ------------ | --------- | -------- |
| initialState | ToolState | 初始状态 |

返回值:

| 名称        | 类型       | 描述           |
| ----------- | ---------- | -------------- |
| state       | ToolState  | 当前状态       |
| processData | () => void | 处理数据的方法 |

### ToolName 组件

| 属性  | 类型   | 默认值    | 描述        |
| ----- | ------ | --------- | ----------- |
| prop1 | string | undefined | 属性 1 描述 |
| prop2 | number | 0         | 属性 2 描述 |

### ToolNameDialog 组件

| 属性         | 类型                    | 默认值    | 描述               |
| ------------ | ----------------------- | --------- | ------------------ |
| open         | boolean                 | -         | 对话框是否打开     |
| onOpenChange | (open: boolean) => void | -         | 对话框状态变化回调 |
| initialData  | any                     | undefined | 初始数据           |

````

## 迁移案例：JSON格式化工具

以下是JSON格式化工具的迁移示例，可作为参考：

### 1. 分析现有工具

JSON格式化工具在浮动助手中的功能：
- 格式化JSON文本
- 验证JSON有效性
- 显示格式化结果
- 复制结果到剪贴板

### 2. 提取核心逻辑

```typescript
// hooks/use-json-formatter.ts

import { useState, useCallback } from 'react'

export function useJsonFormatter() {
  const [input, setInput] = useState('')
  const [formattedJson, setFormattedJson] = useState('')
  const [compactJson, setCompactJson] = useState('')
  const [error, setError] = useState<string | null>(null)

  const formatJson = useCallback(() => {
    try {
      if (!input.trim()) {
        setFormattedJson('')
        setCompactJson('')
        setError(null)
        return
      }

      const parsed = JSON.parse(input)
      setFormattedJson(JSON.stringify(parsed, null, 2))
      setCompactJson(JSON.stringify(parsed))
      setError(null)
    } catch (e) {
      setError(`JSON解析错误: ${(e as Error).message}`)
    }
  }, [input])

  return {
    input,
    formattedJson,
    compactJson,
    error,
    setInput,
    formatJson,
  }
}
````

### 3. 创建独立组件

```typescript
// components/debug-tools/json-formatter.tsx

import { useJsonFormatter } from '@/hooks/use-json-formatter'
import { Button } from '@/components/ui/button'
import { CodeEditor } from './code-editor'

export function JsonFormatter() {
  const { input, formattedJson, error, setInput, formatJson } =
    useJsonFormatter()

  return (
    <div className="grid grid-cols-1 lg:grid-cols-2 gap-6">
      <div className="space-y-4">
        <h3 className="text-lg font-medium">输入 JSON</h3>
        <CodeEditor value={input} onChange={setInput} language="json" />
        <div className="flex gap-2">
          <Button onClick={formatJson}>格式化</Button>
          <Button variant="outline" onClick={() => setInput('')}>
            清空
          </Button>
        </div>
        {error && <div className="text-red-500 text-sm">{error}</div>}
      </div>

      <div className="space-y-4">
        <h3 className="text-lg font-medium">格式化结果</h3>
        <CodeEditor value={formattedJson} readOnly language="json" />
        <Button
          variant="outline"
          onClick={() => navigator.clipboard.writeText(formattedJson)}>
          复制结果
        </Button>
      </div>
    </div>
  )
}
```

### 4-7. 完成迁移

按照上述步骤 4-7 完成 JSON 格式化工具的迁移过程。

## 迁移检查清单

使用以下检查清单确保迁移过程完整：

- [ ] 分析了工具的功能和状态
- [ ] 创建了核心逻辑 Hook
- [ ] 创建了独立 UI 组件
- [ ] 创建了对话框包装器
- [ ] 更新了浮动助手
- [ ] 创建了调试工具页面
- [ ] 更新了路由配置
- [ ] 测试了所有功能
- [ ] 编写了文档
- [ ] 优化了性能

## 常见问题与解决方案

### 1. 状态不同步

**问题**: 在不同实例间状态不同步。
**解决方案**: 考虑使用 Context API 或状态管理库来共享状态。

### 2. 组件依赖浮动助手特定功能

**问题**: 组件依赖于浮动助手中的特定功能或上下文。
**解决方案**: 将依赖作为 props 传入，或使用依赖注入模式。

### 3. 样式不一致

**问题**: 在不同场景下样式不一致。
**解决方案**: 使用一致的样式系统，避免硬编码样式。

### 4. 性能问题

**问题**: 组件在某些场景下性能不佳。
**解决方案**: 使用性能分析工具识别瓶颈，考虑使用记忆化和虚拟化技术。

## 结论

通过遵循本文档中的迁移流程，可以将浮动助手中的工具有效地转换为可复用组件。这不仅提高了代码质量和可维护性，还使工具可以在多个场景中使用，为用户提供一致的体验。
