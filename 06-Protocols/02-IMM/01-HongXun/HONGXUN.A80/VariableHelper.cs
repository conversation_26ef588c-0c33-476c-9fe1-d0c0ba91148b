using System.Collections.Generic;
using System.Linq;
using EdgeGateway.Driver.Entity.Const;
using EdgeGateway.Driver.Entity.Model;
using Newtonsoft.Json;

namespace HONGXUN.A80;

public static class VariableHelper
{
    /// <summary>
    ///     变量定义,统一维护所有数据点
    /// </summary>
    private static readonly Dictionary<string, (string Description, string Status, ushort Length)> VariableDefinitions = new()
    {
        // 合模相关
        { "ClosingPressure1", ("合模压力1", VariableStatus.TransitionInt, 0) },
        { "ClosingPressure2", ("合模压力2", VariableStatus.TransitionInt, 0) },
        { "ClosingPressure3", ("合模压力3", VariableStatus.TransitionInt, 0) },
        { "ClosingPressure4", ("合模压力4", VariableStatus.TransitionInt, 0) },
        { "ClosingSpeed1", ("合模速度1", VariableStatus.TransitionInt, 0) },
        { "ClosingSpeed2", ("合模速度2", VariableStatus.TransitionInt, 0) },
        { "ClosingSpeed3", ("合模速度3", VariableStatus.TransitionInt, 0) },
        { "ClosingSpeed4", ("合模速度4", VariableStatus.TransitionInt, 0) },

        // 开模相关
        { "OpeningPressure1", ("开模压力1", VariableStatus.TransitionInt, 0) },
        { "OpeningPressure2", ("开模压力2", VariableStatus.TransitionInt, 0) },
        { "OpeningPressure3", ("开模压力3", VariableStatus.TransitionInt, 0) },

        // 中子相关
        { "CoreAPressureIn", ("中子A进压力", VariableStatus.TransitionInt, 0) },
        { "CoreAPressureOut", ("中子A退压力", VariableStatus.TransitionInt, 0) },
        { "CoreBPressureIn", ("中子B进压力", VariableStatus.TransitionInt, 0) },
        { "CoreBPressureOut", ("中子B退压力", VariableStatus.TransitionInt, 0) },
        { "CoreCPressureIn", ("中子C进压力", VariableStatus.TransitionInt, 0) },
        { "CoreCPressureOut", ("中子C退压力", VariableStatus.TransitionInt, 0) },
        { "CoreASpeedIn", ("中子A进速度", VariableStatus.TransitionInt, 0) },
        { "CoreASpeedOut", ("中子A退速度", VariableStatus.TransitionInt, 0) },
        { "CoreBSpeedIn", ("中子B进速度", VariableStatus.TransitionInt, 0) },
        { "CoreBSpeedOut", ("中子B退速度", VariableStatus.TransitionInt, 0) },
        { "CoreCSpeedIn", ("中子C进速度", VariableStatus.TransitionInt, 0) },
        { "CoreCSpeedOut", ("中子C退速度", VariableStatus.TransitionInt, 0) },
        { "CoreATimeIn", ("中子A进时间", VariableStatus.TransitionNumber, 0) },
        { "CoreATimeOut", ("中子A退时间", VariableStatus.TransitionNumber, 0) },
        { "CoreBTimeIn", ("中子B进时间", VariableStatus.TransitionNumber, 0) },
        { "CoreBTimeOut", ("中子B退时间", VariableStatus.TransitionNumber, 0) },
        { "CoreCTimeIn", ("中子C进时间", VariableStatus.TransitionNumber, 0) },
        { "CoreCTimeOut", ("中子C退时间", VariableStatus.TransitionNumber, 0) },

        // 托模相关
        { "CarriagePressureIn1", ("托模进压力1", VariableStatus.TransitionInt, 0) },
        { "CarriagePressureIn2", ("托模进压力2", VariableStatus.TransitionInt, 0) },
        { "CarriagePressureOut1", ("托模退压力1", VariableStatus.TransitionInt, 0) },
        { "CarriageSpeedIn1", ("托模进速度1", VariableStatus.TransitionInt, 0) },
        { "CarriageSpeedIn2", ("托模进速度2", VariableStatus.TransitionInt, 0) },
        { "CarriageSpeedOut1", ("托模退速度1", VariableStatus.TransitionInt, 0) },
        { "CarriagePositionIn1", ("托模进位置1", VariableStatus.TransitionNumber, 0) },
        { "CarriagePositionIn2", ("托模进位置2", VariableStatus.TransitionNumber, 0) },
        { "CarriagePositionOut1", ("托模退位置1", VariableStatus.TransitionNumber, 0) },

        // 射出相关
        { "InjectionPressure1", ("射出压力1", VariableStatus.TransitionInt, 0) },
        { "InjectionPressure2", ("射出压力2", VariableStatus.TransitionInt, 0) },
        { "InjectionPressure3", ("射出压力3", VariableStatus.TransitionInt, 0) },
        { "InjectionPressure4", ("射出压力4", VariableStatus.TransitionInt, 0) },
        { "InjectionPressure5", ("射出压力5", VariableStatus.TransitionInt, 0) },
        { "InjectionPressure6", ("射出压力6", VariableStatus.TransitionInt, 0) },

        // 品质相关
        { "CycleTime", ("循环时间", VariableStatus.TransitionNumber, 0) },
        { "ClampTime", ("关模时间", VariableStatus.TransitionNumber, 0) },
        { "LowPressureTime", ("低压时间", VariableStatus.TransitionNumber, 0) },
        { "HighPressureTime", ("高压时间", VariableStatus.TransitionNumber, 0) },
        { "OpenTime", ("开模时间", VariableStatus.TransitionNumber, 0) },
        { "InjectionTime", ("射出时间", VariableStatus.TransitionNumber, 0) },
        { "VpSwitchTime", ("转保时间", VariableStatus.TransitionNumber, 0) },
        { "InjectionStartPosition", ("射出起点", VariableStatus.TransitionNumber, 0) },
        { "HoldingStartPosition", ("保压起点", VariableStatus.TransitionNumber, 0) },
        { "InjectionMonitor", ("射出监测", VariableStatus.TransitionNumber, 0) },
        { "PlastificationTime", ("储料时间", VariableStatus.TransitionNumber, 0) },
        { "TotalCount", ("累计计数", VariableStatus.TransitionInt, 0) }
    };

    /// <summary>
    ///    生成静态变量
    /// </summary>
    public static class DataPoints
    {
        public static IReadOnlyDictionary<string, string> All => VariableDefinitions.ToDictionary(
            kvp => kvp.Key,
            kvp => kvp.Key
        );
    }

    /// <summary>
    /// 变量
    /// </summary>
    public static readonly List<DriverLabel> Variables = GenerateVariables();

    /// <summary>
    /// 生成变量
    /// </summary>
    /// <returns></returns>
    private static List<DriverLabel> GenerateVariables()
    {
        return VariableDefinitions
            .Select(kvp => new DriverLabel(
                kvp.Value.Description,
                kvp.Key,
                kvp.Value.Status,
                kvp.Value.Length,
                kvp.Value.Description))
            .ToList();
    }


    /// <summary>
    /// 获取属性配置
    /// </summary>
    /// <returns></returns>
    public static List<PropertyConfiguration> GetPropertyConfigurations()
    {
        var methodOptions = Variables.Select(v => new { v.Name, v.Identifier })
            .ToDictionary(x => x.Name, x => x.Identifier);

        return new List<PropertyConfiguration>
        {
            new()
            {
                Type = "select",
                Code = "Method",
                Name = "读取方法",
                Value = JsonConvert.SerializeObject(methodOptions.Select(x => new Dictionary<string, string> { { x.Key, x.Value } })),
                Order = 2
            }
        };
    }
}