<Project Sdk="Microsoft.NET.Sdk">

    <PropertyGroup>
        <TargetFramework>net9.0</TargetFramework>
        <GenerateAssemblyInfo>false</GenerateAssemblyInfo>
        <RootNamespace>KEQIANG.T6F3</RootNamespace>
        <PublishAot>true</PublishAot>
        <IlcOptimizationPreference>Speed</IlcOptimizationPreference>
        <IlcFoldIdenticalMethodBodies>true</IlcFoldIdenticalMethodBodies>
        <TrimMode>full</TrimMode>
    </PropertyGroup>

    <PropertyGroup Condition="'$(Configuration)|$(Platform)'=='Release|AnyCPU'">
        <DebugType>none</DebugType>
    </PropertyGroup>

    <ItemGroup>
        <ProjectReference Include="..\..\..\EdgeGateway.Driver.Interface\EdgeGateway.Driver.Interface.csproj"/>
    </ItemGroup>

</Project>
