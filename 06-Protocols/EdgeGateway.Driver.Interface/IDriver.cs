namespace EdgeGateway.Driver.Interface;

/// <summary>
/// 驱动接口
/// </summary>
public interface IDriver : IDisposable
{
    /// <summary>
    /// 输出接收事件
    /// </summary>
    event EventHandler<string> OutputReceived;

    /// <summary>
    ///     反射给协议加载的实体对象
    /// </summary>
    public DriverInfoDto DriverInfo { get; set; }

    /// <summary>
    /// 协议支持方法
    /// </summary>
    public List<PropertyConfiguration>? PropertyConfiguration { get; set; }

    /// <summary>
    ///     设备连接状态
    /// </summary>
    public bool IsConnected { get; set; }
    
    /// <summary>
    ///     日志等级
    /// </summary>
    public DeviceLogLevelEnum LogLevel { get; set; }

    /// <summary>
    ///     连接信息
    /// </summary>
    /// <returns></returns>
    public string Connect();

    /// <summary>
    ///     是否关闭
    /// </summary>
    /// <returns></returns>
    public void Close();

    /// <summary>
    ///     获取协议更新日志
    /// </summary>
    /// <param name="format">日志格式，默认为Markdown</param>
    /// <returns>格式化后的更新日志</returns>
    public string GetVersionLogs(LogFormat format = LogFormat.Markdown);

    /// <summary>
    ///     写
    /// </summary>
    /// <param name="val"></param>
    /// <returns></returns>
    public Task<List<Dictionary<string, object>>> Write(List<DriverWriteInput> val);

    /// <summary>
    ///     读
    /// </summary>
    /// <param name="val"></param>
    /// <returns></returns>
    public Task<List<ReadDataResult>> Read(List<DriverReadInput> val);

    /// <summary>
    /// 默认提供变量
    /// </summary>
    public List<DriverLabel>? Variables { get; set; }

}