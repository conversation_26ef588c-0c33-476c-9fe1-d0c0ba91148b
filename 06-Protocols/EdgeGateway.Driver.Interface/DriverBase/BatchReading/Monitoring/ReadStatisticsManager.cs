using System.Text.Json;
using JsonSerializer = System.Text.Json.JsonSerializer;

namespace EdgeGateway.Driver.Interface.DriverBase.BatchReading.Monitoring;

/// <summary>
///     读取统计管理器
/// </summary>
public class ReadStatisticsManager
{
    private readonly ConcurrentDictionary<string, ReadStatistics> _statistics = new();
    private readonly string _statisticsFilePath;
    private readonly Timer _persistTimer;
    private readonly int _historySize;
    private readonly object _fileLock = new();

    /// <summary>
    ///     构造函数
    /// </summary>
    /// <param name="statisticsFilePath">统计文件路径</param>
    /// <param name="persistIntervalMs">持久化间隔(毫秒)</param>
    /// <param name="historySize">历史记录大小</param>
    public ReadStatisticsManager(string statisticsFilePath = "read_statistics.json", int persistIntervalMs = 60000, int historySize = 50)
    {
        _statisticsFilePath = statisticsFilePath;
        _historySize = historySize;

        // 加载历史统计数据
        LoadStatistics();

        // 定时持久化统计数据
        _persistTimer = new Timer(PersistStatisticsCallback, null, persistIntervalMs, persistIntervalMs);
    }

    /// <summary>
    ///     更新统计信息
    /// </summary>
    /// <param name="addressPrefix">地址前缀</param>
    /// <param name="isSuccess">是否成功</param>
    /// <param name="executionTime">执行时间</param>
    /// <param name="pointsRead">读取点数</param>
    /// <param name="chunkSize">分块大小</param>
    public void UpdateStatistics(string addressPrefix, bool isSuccess, long executionTime, int pointsRead, int chunkSize)
    {
        var stats = _statistics.GetOrAdd(addressPrefix, _ => new ReadStatistics { AddressPrefix = addressPrefix });
        stats.Update(isSuccess, executionTime, pointsRead, chunkSize);
    }

    /// <summary>
    ///     获取地址前缀的统计信息
    /// </summary>
    /// <param name="addressPrefix">地址前缀</param>
    /// <returns>统计信息</returns>
    public ReadStatistics? GetStatistics(string addressPrefix)
    {
        return _statistics.TryGetValue(addressPrefix, out var stats) ? stats : null;
    }

    /// <summary>
    ///     获取所有统计信息
    /// </summary>
    /// <returns>所有统计信息</returns>
    public Dictionary<string, ReadStatistics> GetAllStatistics()
    {
        return _statistics.ToDictionary(kv => kv.Key, kv => kv.Value);
    }

    /// <summary>
    ///     持久化统计数据
    /// </summary>
    public void PersistStatistics()
    {
        try
        {
            lock (_fileLock)
            {
                var json = JsonSerializer.Serialize(_statistics, new JsonSerializerOptions { WriteIndented = true });
                File.WriteAllText(_statisticsFilePath, json);
            }
        }
        catch (Exception ex)
        {
            Console.WriteLine($"Failed to persist statistics: {ex.Message}");
        }
    }

    /// <summary>
    ///     加载统计数据
    /// </summary>
    private void LoadStatistics()
    {
        try
        {
            if (File.Exists(_statisticsFilePath))
                lock (_fileLock)
                {
                    var json = File.ReadAllText(_statisticsFilePath);
                    var loadedStats = JsonSerializer.Deserialize<ConcurrentDictionary<string, ReadStatistics>>(json);
                    if (loadedStats != null)
                    {
                        _statistics.Clear();
                        foreach (var kv in loadedStats) _statistics.TryAdd(kv.Key, kv.Value);
                    }
                }
        }
        catch (Exception ex)
        {
            Console.WriteLine($"Failed to load statistics: {ex.Message}");
        }
    }

    /// <summary>
    ///     持久化统计数据回调
    /// </summary>
    private void PersistStatisticsCallback(object? state)
    {
        PersistStatistics();
    }

    /// <summary>
    ///     分析性能瓶颈
    /// </summary>
    /// <returns>瓶颈分析结果</returns>
    public List<string> AnalyzeBottlenecks()
    {
        var bottlenecks = new List<string>();

        foreach (var kvp in _statistics)
        {
            var prefix = kvp.Key;
            var stats = kvp.Value;

            // 成功率低于70%
            if (stats.SuccessRate < 0.7 && stats.TotalAttempts > 10)
                bottlenecks.Add($"地址前缀 {prefix} 成功率过低 ({stats.SuccessRate * 100:F2}%)，建议检查地址有效性或减小批量大小");

            // 平均执行时间过长（超过500毫秒）
            if (stats.AverageExecutionTime > 500 && stats.TotalAttempts > 10)
                bottlenecks.Add($"地址前缀 {prefix} 平均执行时间过长 ({stats.AverageExecutionTime:F2} ms)，建议优化网络或减小分块大小");

            // 平均每点时间过长（超过10毫秒/点）
            if (stats.AverageTimePerPoint > 10 && stats.TotalPointsRead > 100)
                bottlenecks.Add($"地址前缀 {prefix} 平均每点时间过长 ({stats.AverageTimePerPoint:F2} ms/点)，建议检查网络延迟或设备响应速度");
        }

        return bottlenecks;
    }

    /// <summary>
    ///     获取推荐的分块大小
    /// </summary>
    /// <param name="addressPrefix">地址前缀</param>
    /// <param name="defaultSize">默认大小</param>
    /// <returns>推荐的分块大小</returns>
    public int GetRecommendedChunkSize(string addressPrefix, int defaultSize = 100)
    {
        if (!_statistics.TryGetValue(addressPrefix, out var stats))
            return defaultSize;

        // 如果成功率低于50%，减小分块大小
        if (stats.SuccessRate < 0.5)
            return Math.Max(10, stats.LastChunkSize / 2);

        // 如果成功率高于90%，增加分块大小
        if (stats.SuccessRate > 0.9)
            return Math.Min(1000, stats.LastChunkSize * 2);

        // 否则使用最后一次的分块大小
        return stats.LastChunkSize > 0 ? stats.LastChunkSize : defaultSize;
    }

    /// <summary>
    ///     释放资源
    /// </summary>
    public void Dispose()
    {
        _persistTimer?.Dispose();
        PersistStatistics();
    }
}