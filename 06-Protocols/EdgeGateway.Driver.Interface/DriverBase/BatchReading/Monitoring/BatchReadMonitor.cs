using System;
using System.Collections.Concurrent;
using System.Collections.Generic;
using System.Linq;
using System.Text;
using System.Threading.Tasks;
using EdgeGateway.Driver.Entity.Model;

namespace EdgeGateway.Driver.Interface.DriverBase.BatchReading.Monitoring;

/// <summary>
///     批量读取监控组件
/// </summary>
public class BatchReadMonitor
{
  private readonly ConcurrentDictionary<string, ReadStatistics> _protocolStats = new();
  private readonly ConcurrentDictionary<string, List<ReadOperationLog>> _operationLogs = new();
  private readonly int _maxLogEntries;

  /// <summary>
  ///     构造函数
  /// </summary>
  /// <param name="maxLogEntries">最大日志条目数</param>
  public BatchReadMonitor(int maxLogEntries = 1000)
  {
    _maxLogEntries = maxLogEntries;
  }

  /// <summary>
  ///     记录读取操作
  /// </summary>
  /// <param name="protocolName">协议名称</param>
  /// <param name="addressCount">地址数量</param>
  /// <param name="elapsedMs">耗时(毫秒)</param>
  /// <param name="isSuccess">是否成功</param>
  /// <param name="errorMessage">错误信息</param>
  /// <param name="addressPrefix">地址前缀</param>
  public void LogOperation(string protocolName, int addressCount, long elapsedMs, bool isSuccess, string? errorMessage = null, string? addressPrefix = null)
  {
    // 更新统计信息
    var stats = _protocolStats.GetOrAdd(protocolName, _ => new ReadStatistics { AddressPrefix = protocolName });
    stats.Update(isSuccess, elapsedMs, addressCount, addressCount);

    // 记录操作日志
    var log = new ReadOperationLog
    {
      Timestamp = DateTime.Now,
      ProtocolName = protocolName,
      AddressPrefix = addressPrefix,
      AddressCount = addressCount,
      ElapsedMs = elapsedMs,
      IsSuccess = isSuccess,
      ErrorMessage = errorMessage
    };

    var logs = _operationLogs.GetOrAdd(protocolName, _ => new List<ReadOperationLog>());

    lock (logs)
    {
      logs.Add(log);

      // 限制日志条目数
      if (logs.Count > _maxLogEntries) logs.RemoveRange(0, logs.Count - _maxLogEntries);
    }
  }

  /// <summary>
  ///     获取协议性能统计
  /// </summary>
  /// <param name="protocolName">协议名称</param>
  /// <returns>性能统计信息</returns>
  public ReadStatistics? GetProtocolStats(string protocolName)
  {
    return _protocolStats.TryGetValue(protocolName, out var stats) ? stats : null;
  }

  /// <summary>
  ///     获取所有协议性能统计
  /// </summary>
  /// <returns>所有协议的性能统计信息</returns>
  public Dictionary<string, ReadStatistics> GetAllProtocolStats()
  {
    return _protocolStats.ToDictionary(kv => kv.Key, kv => kv.Value);
  }

  /// <summary>
  ///     获取操作日志
  /// </summary>
  /// <param name="protocolName">协议名称</param>
  /// <param name="count">日志条数</param>
  /// <returns>操作日志列表</returns>
  public List<ReadOperationLog> GetOperationLogs(string protocolName, int count = 100)
  {
    if (!_operationLogs.TryGetValue(protocolName, out var logs))
      return new List<ReadOperationLog>();

    lock (logs)
    {
      return logs.OrderByDescending(l => l.Timestamp).Take(count).ToList();
    }
  }

  /// <summary>
  ///     生成性能报告
  /// </summary>
  /// <returns>性能报告文本</returns>
  public string GeneratePerformanceReport()
  {
    var report = new StringBuilder();
    report.AppendLine("批量读取性能报告");
    report.AppendLine("============================");
    report.AppendLine($"生成时间: {DateTime.Now}");
    report.AppendLine();

    // 按平均响应时间排序
    var sortedStats = _protocolStats.OrderBy(kv => kv.Value.AverageExecutionTime).ToList();

    foreach (var kvp in sortedStats)
    {
      var protocol = kvp.Key;
      var stats = kvp.Value;
      report.AppendLine($"协议: {protocol}");
      report.AppendLine($"总读取次数: {stats.TotalAttempts}");
      report.AppendLine($"成功次数: {stats.SuccessfulAttempts}");
      report.AppendLine($"成功率: {stats.SuccessRate * 100:F2}%");
      report.AppendLine($"平均响应时间: {stats.AverageExecutionTime:F2} ms");
      report.AppendLine($"最大响应时间: {stats.TotalExecutionTime} ms");
      report.AppendLine($"最小响应时间: {stats.TotalExecutionTime} ms");
      report.AppendLine($"总读取点数: {stats.TotalPointsRead}");
      report.AppendLine("----------------------------");
    }

    return report.ToString();
  }

  /// <summary>
  ///     分析性能瓶颈
  /// </summary>
  /// <returns>瓶颈分析结果</returns>
  public List<string> AnalyzeBottlenecks()
  {
    var bottlenecks = new List<string>();

    foreach (var kvp in _protocolStats)
    {
      var protocol = kvp.Key;
      var stats = kvp.Value;

      // 成功率低于70%
      if (stats.SuccessRate < 0.7 && stats.TotalAttempts > 10) bottlenecks.Add($"协议 {protocol} 成功率过低 ({stats.SuccessRate * 100:F2}%)，建议检查连接稳定性或减小批量大小");

      // 响应时间过长（超过1秒）
      if (stats.AverageExecutionTime > 1000 && stats.TotalAttempts > 10) bottlenecks.Add($"协议 {protocol} 平均响应时间过长 ({stats.AverageExecutionTime:F2} ms)，建议优化网络或减小分块大小");
    }

    return bottlenecks;
  }
}

/// <summary>
///     读取操作日志
/// </summary>
public class ReadOperationLog
{
  /// <summary>
  ///     时间戳
  /// </summary>
  public DateTime Timestamp { get; set; }

  /// <summary>
  ///     协议名称
  /// </summary>
  public string ProtocolName { get; set; } = string.Empty;

  /// <summary>
  ///     地址前缀
  /// </summary>
  public string? AddressPrefix { get; set; }

  /// <summary>
  ///     地址数量
  /// </summary>
  public int AddressCount { get; set; }

  /// <summary>
  ///     耗时(毫秒)
  /// </summary>
  public long ElapsedMs { get; set; }

  /// <summary>
  ///     是否成功
  /// </summary>
  public bool IsSuccess { get; set; }

  /// <summary>
  ///     错误信息
  /// </summary>
  public string? ErrorMessage { get; set; }
}