using System.Diagnostics;

namespace EdgeGateway.Driver.Interface.DriverBase.AddressParsing;

/// <summary>
///     批量读取性能测试器
/// </summary>
public class BatchReadPerformanceTester
{
    private readonly CollectsTheCommonParentClass _collector;
    private readonly List<DriverReadInput> _testAddresses;
    private readonly ConcurrentBag<TestResult> _results = new();

    /// <summary>
    ///     构造函数
    /// </summary>
    /// <param name="collector">采集器实例</param>
    /// <param name="testAddresses">测试地址列表</param>
    public BatchReadPerformanceTester(CollectsTheCommonParentClass collector, List<DriverReadInput> testAddresses)
    {
        _collector = collector ?? throw new ArgumentNullException(nameof(collector));
        _testAddresses = testAddresses ?? throw new ArgumentNullException(nameof(testAddresses));
    }

    /// <summary>
    ///     运行性能测试
    /// </summary>
    /// <param name="testName">测试名称</param>
    /// <param name="options">批处理选项</param>
    /// <param name="iterations">重复次数</param>
    /// <returns>测试结果</returns>
    public async Task<TestResult> RunTest(string testName, SmartBatchOptions options, int iterations = 1)
    {
        if (iterations < 1)
            throw new ArgumentException("迭代次数必须大于0", nameof(iterations));

        var result = new TestResult
        {
            TestName = testName,
            Options = options,
            StartTime = DateTime.Now
        };

        var stopwatch = new Stopwatch();
        var successCount = 0;
        var totalElapsed = 0L;

        // 运行多轮测试
        for (var i = 0; i < iterations; i++)
        {
            try
            {
                stopwatch.Reset();
                stopwatch.Start();

                // 执行批量读取
                var testResults = await _collector.ReadWithSmartBatch(_testAddresses, options);

                stopwatch.Stop();
                totalElapsed += stopwatch.ElapsedMilliseconds;

                // 计算成功率 - 读取成功且无错误消息视为成功
                var successResults = testResults.Count(r => !string.IsNullOrEmpty(r.Value?.ToString()) && string.IsNullOrEmpty(r.ErrMsg));
                successCount += successResults;

                result.IterationResults.Add(new IterationResult
                {
                    IterationNumber = i + 1,
                    ElapsedMilliseconds = stopwatch.ElapsedMilliseconds,
                    SuccessCount = successResults,
                    TotalCount = testResults.Count
                });
            }
            catch (Exception ex)
            {
                result.Exceptions.Add(ex);
            }

            // 测试间隔，避免PLC过载
            await Task.Delay(500);
        }

        // 计算最终结果
        result.AverageResponseTimeMs = totalElapsed / (double)iterations;
        result.SuccessRate = successCount / (double)(iterations * _testAddresses.Count) * 100;
        result.EndTime = DateTime.Now;

        // 保存结果
        _results.Add(result);

        return result;
    }

    /// <summary>
    ///     运行多个方案对比测试
    /// </summary>
    /// <param name="testConfigurations">测试配置列表</param>
    /// <param name="iterations">每个配置的迭代次数</param>
    /// <returns>对比结果</returns>
    public async Task<List<TestResult>> RunComparison(List<(string Name, SmartBatchOptions Options)> testConfigurations, int iterations = 1)
    {
        var results = new List<TestResult>();

        foreach (var config in testConfigurations)
        {
            var result = await RunTest(config.Name, config.Options, iterations);
            results.Add(result);
        }

        return results;
    }

    /// <summary>
    ///     生成性能报告
    /// </summary>
    /// <returns>性能报告文本</returns>
    public string GenerateReport()
    {
        var report = new StringBuilder();

        report.AppendLine("批量读取性能测试报告");
        report.AppendLine("============================");
        report.AppendLine($"测试时间: {DateTime.Now}");
        report.AppendLine($"测试地址数量: {_testAddresses.Count}");
        report.AppendLine();

        // 按平均响应时间排序
        var sortedResults = _results.OrderBy(r => r.AverageResponseTimeMs).ToList();

        foreach (var result in sortedResults)
        {
            report.AppendLine($"测试名称: {result.TestName}");
            report.AppendLine($"处理模式: {result.Options.ProcessingMode}");
            report.AppendLine($"初始块大小: {result.Options.InitialChunkSize}");
            report.AppendLine($"最大并行度: {result.Options.MaxParallelism}");
            report.AppendLine($"平均响应时间: {result.AverageResponseTimeMs:F2} ms");
            report.AppendLine($"成功率: {result.SuccessRate:F2}%");

            if (result.Exceptions.Any())
                report.AppendLine($"异常数: {result.Exceptions.Count}");

            report.AppendLine("----------------------------");
        }

        report.AppendLine();
        report.AppendLine("性能对比结果(按响应时间排序):");
        report.AppendLine("============================");

        foreach (var result in sortedResults) report.AppendLine($"{result.TestName}: {result.AverageResponseTimeMs:F2} ms (成功率: {result.SuccessRate:F2}%)");

        return report.ToString();
    }

    /// <summary>
    ///     测试结果类
    /// </summary>
    public class TestResult
    {
      /// <summary>
      ///     测试名称
      /// </summary>
      public string TestName { get; set; } = string.Empty;

      /// <summary>
      ///     批处理选项
      /// </summary>
      public SmartBatchOptions Options { get; set; } = new();

      /// <summary>
      ///     开始时间
      /// </summary>
      public DateTime StartTime { get; set; }

      /// <summary>
      ///     结束时间
      /// </summary>
      public DateTime EndTime { get; set; }

      /// <summary>
      ///     平均响应时间(毫秒)
      /// </summary>
      public double AverageResponseTimeMs { get; set; }

      /// <summary>
      ///     成功率(%)
      /// </summary>
      public double SuccessRate { get; set; }

      /// <summary>
      ///     异常列表
      /// </summary>
      public List<Exception> Exceptions { get; set; } = new();

      /// <summary>
      ///     每次迭代的结果
      /// </summary>
      public List<IterationResult> IterationResults { get; set; } = new();
    }

    /// <summary>
    ///     单次迭代结果
    /// </summary>
    public class IterationResult
    {
      /// <summary>
      ///     迭代序号
      /// </summary>
      public int IterationNumber { get; set; }

      /// <summary>
      ///     耗时(毫秒)
      /// </summary>
      public long ElapsedMilliseconds { get; set; }

      /// <summary>
      ///     成功数量
      /// </summary>
      public int SuccessCount { get; set; }

      /// <summary>
      ///     总数量
      /// </summary>
      public int TotalCount { get; set; }
    }
}