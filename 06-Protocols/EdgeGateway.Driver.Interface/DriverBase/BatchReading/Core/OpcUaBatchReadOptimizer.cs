using System.Text.RegularExpressions;
using EdgeGateway.Driver.Entity.Model;

namespace EdgeGateway.Driver.Interface.DriverBase.AddressParsing.Optimizers;

/// <summary>
/// OPC UA专用批量读取优化器
/// </summary>
public class OpcUaBatchReadOptimizer : DefaultBatchReadOptimizer
{
    /// <summary>
    /// 获取优化器适用的协议名称
    /// </summary>
    public override string ProtocolName => "opcua";

    /// <summary>
    /// 默认分块大小，根据OPC UA通信规范优化
    /// </summary>
    protected override int DefaultChunkSize => 1000;

    /// <summary>
    /// OPC UA使用变长数据，设置为1作为基础单位
    /// </summary>
    protected override int DefaultByteMultiplier => 1;

    /// <summary>
    /// 用于匹配NodeId格式的正则表达式
    /// </summary>
    private static readonly Regex NodeIdRegex = new Regex(@"^(ns=(\d+);)?([isgb])=(.+)$", RegexOptions.IgnoreCase | RegexOptions.Compiled);

    /// <summary>
    /// 用于匹配数组索引的正则表达式
    /// </summary>
    private static readonly Regex ArrayIndexRegex = new Regex(@"\[(\d+(?::\d+)?)\]$", RegexOptions.Compiled);

    /// <summary>
    /// 获取推荐的最大分块大小
    /// </summary>
    /// <param name="addressPrefix">地址前缀</param>
    /// <returns>推荐的分块大小</returns>
    public override int GetRecommendedChunkSize(string addressPrefix)
    {
        // OPC UA支持大量节点的批量读取
        return DefaultChunkSize;
    }

    /// <summary>
    /// 判断两个地址是否可以合并成一个批量读取请求
    /// </summary>
    /// <param name="address1">第一个地址</param>
    /// <param name="address2">第二个地址</param>
    /// <param name="maxGap">最大允许的地址间隔</param>
    /// <returns>是否可以合并</returns>
    public override bool CanCombineAddresses(AddressParseResult address1, AddressParseResult address2, int maxGap = 50)
    {
        if (address1 == null || address2 == null)
            return false;

        // 解析OPC UA NodeId
        var result1 = ParseNodeId(address1.OriginalAddress);
        var result2 = ParseNodeId(address2.OriginalAddress);

        if (!result1.IsValid || !result2.IsValid)
            return false;

        // OPC UA中，只有相同命名空间和相同标识符类型的节点才考虑合并
        if (result1.NamespaceIndex != result2.NamespaceIndex ||
            result1.IdentifierType != result2.IdentifierType)
            return false;

        // 对于数值型标识符，检查是否连续
        if (result1.IdentifierType == "i" &&
            uint.TryParse(result1.BaseAddress, out var id1) &&
            uint.TryParse(result2.BaseAddress, out var id2))
        {
            return Math.Abs((int)(id2 - id1)) <= maxGap;
        }

        // 对于字符串型标识符，暂不支持合并
        return false;
    }

    /// <summary>
    /// 解析NodeId格式的地址
    /// </summary>
    /// <param name="address">地址字符串</param>
    /// <returns>解析结果</returns>
    private OpcUaNodeIdParseResult ParseNodeId(string address)
    {
        var trimmedAddress = address.Trim();
        var match = NodeIdRegex.Match(trimmedAddress);

        if (!match.Success)
        {
            return new OpcUaNodeIdParseResult
            {
                IsValid = false,
                ErrorMessage = $"无效的OPC UA NodeId格式: {address}"
            };
        }

        var namespaceIndex = 0;
        if (match.Groups[2].Success && int.TryParse(match.Groups[2].Value, out var nsIndex))
        {
            namespaceIndex = nsIndex;
        }

        var identifierType = match.Groups[3].Value.ToLower();
        var identifier = match.Groups[4].Value;

        // 检查是否有数组索引
        var arrayMatch = ArrayIndexRegex.Match(identifier);
        var hasArrayIndex = arrayMatch.Success;
        if (hasArrayIndex)
        {
            identifier = identifier.Substring(0, arrayMatch.Index);
        }

        return new OpcUaNodeIdParseResult
        {
            IsValid = true,
            BaseAddress = identifier,
            NamespaceIndex = namespaceIndex,
            IdentifierType = identifierType,
            HasArrayIndex = hasArrayIndex,
            ArrayIndex = hasArrayIndex ? arrayMatch.Groups[1].Value : null,
            FullAddress = trimmedAddress
        };
    }

    /// <summary>
    /// OPC UA NodeId解析结果
    /// </summary>
    private class OpcUaNodeIdParseResult
    {
        public bool IsValid { get; set; }
        public string? ErrorMessage { get; set; }
        public string BaseAddress { get; set; } = "";
        public int NamespaceIndex { get; set; }
        public string IdentifierType { get; set; } = "";
        public bool HasArrayIndex { get; set; }
        public string? ArrayIndex { get; set; }
        public string FullAddress { get; set; } = "";
    }
}
