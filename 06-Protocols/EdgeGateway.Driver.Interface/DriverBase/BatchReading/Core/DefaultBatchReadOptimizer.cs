using EdgeGateway.Driver.Entity.Model;

namespace EdgeGateway.Driver.Interface.DriverBase.AddressParsing.Optimizers;

/// <summary>
/// 默认批量读取优化器，适用于未指定专用优化器的协议
/// </summary>
public class DefaultBatchReadOptimizer : IBatchReadOptimizer
{
  /// <summary>
  /// 获取优化器适用的协议名称
  /// </summary>
  public virtual string ProtocolName => "default";

  /// <summary>
  /// 默认分块大小
  /// </summary>
  protected virtual int DefaultChunkSize => 100;

  /// <summary>
  /// 默认字节倍率
  /// </summary>
  protected virtual int DefaultByteMultiplier => 2;

  /// <summary>
  /// 优化批量读取的地址列表分组
  /// </summary>
  /// <param name="addresses">已解析的地址列表</param>
  /// <returns>优化后的地址分组</returns>
  public virtual List<List<AddressParseResult>> OptimizeAddressGroups(List<AddressParseResult> addresses)
  {
    if (addresses == null || addresses.Count == 0)
      return new List<List<AddressParseResult>>();

    // 按地址前缀分组
    var groups = addresses
        .GroupBy(a => a.Prefix)
        .Select(g => g.ToList())
        .ToList();

    var result = new List<List<AddressParseResult>>();

    // 对每个前缀组内的地址进行排序和连续性分组
    foreach (var group in groups)
    {
      if (group.Count == 0)
        continue;

      // 按地址数字排序
      var sortedAddresses = group.OrderBy(a => a.Number).ToList();

      // 连续性分组
      var subGroups = new List<List<AddressParseResult>>();
      var currentGroup = new List<AddressParseResult> { sortedAddresses[0] };

      for (int i = 1; i < sortedAddresses.Count; i++)
      {
        var prevAddress = sortedAddresses[i - 1];
        var currAddress = sortedAddresses[i];

        // 判断是否可以合并到一个批次中
        if (CanCombineAddresses(prevAddress, currAddress))
        {
          currentGroup.Add(currAddress);
        }
        else
        {
          // 不连续，创建新分组
          subGroups.Add(new List<AddressParseResult>(currentGroup));
          currentGroup = new List<AddressParseResult> { currAddress };
        }
      }

      // 添加最后一个分组
      if (currentGroup.Count > 0)
        subGroups.Add(currentGroup);

      result.AddRange(subGroups);
    }

    return result;
  }

  /// <summary>
  /// 计算读取长度，考虑协议特定的字节对齐和数据类型要求
  /// </summary>
  /// <param name="minNumber">最小地址数字</param>
  /// <param name="maxNumber">最大地址数字</param>
  /// <param name="dataTypes">数据类型列表，与地址一一对应</param>
  /// <returns>计算得到的读取长度</returns>
  public virtual ushort CalculateReadLength(decimal minNumber, decimal maxNumber, List<string> dataTypes)
  {
    // 基础长度：最大地址 - 最小地址 + 1
    var baseDiff = (ushort)Math.Ceiling(maxNumber - minNumber + 1);

    // 如果数据类型列表为空，直接返回基础差值
    if (dataTypes == null || dataTypes.Count == 0)
      return baseDiff;

    // 查找最大地址对应的数据类型
    var maxAddressIndex = dataTypes.Count - 1;
    var maxAddressDataType = dataTypes[maxAddressIndex].ToLower();

    // 根据数据类型调整长度
    switch (maxAddressDataType)
    {
      case "int32":
      case "uint32":
      case "float":
      case "dword":
        return (ushort)(baseDiff + 1); // 32位类型需要多读1个字

      case "int64":
      case "uint64":
      case "double":
      case "lword":
        return (ushort)(baseDiff + 3); // 64位类型需要多读3个字

      case "string":
        // 字符串类型，如果最后一个地址是字符串，可能需要更多字节
        // 默认实现简单处理，具体协议可以重写此方法
        return (ushort)(baseDiff + 5); // 默认字符串多读5个字

      default:
        return baseDiff; // 16位类型或位类型不需要额外字节
    }
  }

  /// <summary>
  /// 获取推荐的最大分块大小
  /// </summary>
  /// <param name="addressPrefix">地址前缀（如D、M等）</param>
  /// <returns>推荐的分块大小</returns>
  public virtual int GetRecommendedChunkSize(string addressPrefix)
  {
    // 默认实现返回固定大小，子类可以根据具体协议和地址前缀返回不同的值
    return DefaultChunkSize;
  }

  /// <summary>
  /// 获取协议特定的字节倍率
  /// </summary>
  /// <param name="addressPrefix">地址前缀</param>
  /// <returns>字节倍率</returns>
  public virtual int GetByteMultiplier(string addressPrefix)
  {
    // 大多数PLC协议每个字地址对应2字节，默认返回2
    return DefaultByteMultiplier;
  }

  /// <summary>
  /// 检查是否支持跨区域批量读取
  /// </summary>
  /// <param name="fromPrefix">起始区域前缀</param>
  /// <param name="toPrefix">目标区域前缀</param>
  /// <returns>是否支持跨区域读取</returns>
  public virtual bool SupportsCrossAreaReading(string fromPrefix, string toPrefix)
  {
    // 默认不支持跨区域读取
    return string.Equals(fromPrefix, toPrefix, StringComparison.OrdinalIgnoreCase);
  }

  /// <summary>
  /// 判断两个地址是否可以合并成一个批量读取请求
  /// </summary>
  /// <param name="address1">第一个地址</param>
  /// <param name="address2">第二个地址</param>
  /// <param name="maxGap">最大允许的地址间隔</param>
  /// <returns>是否可以合并</returns>
  public virtual bool CanCombineAddresses(AddressParseResult address1, AddressParseResult address2, int maxGap = 50)
  {
    // 检查前缀是否相同
    if (!string.Equals(address1.Prefix, address2.Prefix, StringComparison.OrdinalIgnoreCase))
      return false;

    // 检查位地址
    if (address1.BitPosition >= 0 || address2.BitPosition >= 0)
    {
      // 如果两者都是位地址，检查是否是同一个字中的不同位
      if (address1.Number == address2.Number)
        return true;
    }

    // 检查地址间隔
    var gap = address2.Number - address1.Number;
    return gap > 0 && gap <= maxGap;
  }
}
