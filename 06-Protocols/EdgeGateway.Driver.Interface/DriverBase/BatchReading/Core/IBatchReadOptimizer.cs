using EdgeGateway.Driver.Entity.Model;

namespace EdgeGateway.Driver.Interface.DriverBase.AddressParsing.Optimizers;

/// <summary>
/// 批量读取优化器接口，用于协议特定的地址分组和批量读取优化
/// </summary>
public interface IBatchReadOptimizer
{
  /// <summary>
  /// 获取优化器适用的协议名称
  /// </summary>
  string ProtocolName { get; }

  /// <summary>
  /// 优化批量读取的地址列表分组
  /// </summary>
  /// <param name="addresses">已解析的地址列表</param>
  /// <returns>优化后的地址分组</returns>
  List<List<AddressParseResult>> OptimizeAddressGroups(List<AddressParseResult> addresses);

  /// <summary>
  /// 计算读取长度，考虑协议特定的字节对齐和数据类型要求
  /// </summary>
  /// <param name="minNumber">最小地址数字</param>
  /// <param name="maxNumber">最大地址数字</param>
  /// <param name="dataTypes">数据类型列表，与地址一一对应</param>
  /// <returns>计算得到的读取长度</returns>
  ushort CalculateReadLength(decimal minNumber, decimal maxNumber, List<string> dataTypes);

  /// <summary>
  /// 获取推荐的最大分块大小
  /// </summary>
  /// <param name="addressPrefix">地址前缀（如D、M等）</param>
  /// <returns>推荐的分块大小</returns>
  int GetRecommendedChunkSize(string addressPrefix);

  /// <summary>
  /// 获取协议特定的字节倍率（如西门子S7每个地址占1字节，Modbus每个地址占2字节）
  /// </summary>
  /// <param name="addressPrefix">地址前缀</param>
  /// <returns>字节倍率</returns>
  int GetByteMultiplier(string addressPrefix);

  /// <summary>
  /// 检查是否支持跨区域批量读取
  /// </summary>
  /// <param name="fromPrefix">起始区域前缀</param>
  /// <param name="toPrefix">目标区域前缀</param>
  /// <returns>是否支持跨区域读取</returns>
  bool SupportsCrossAreaReading(string fromPrefix, string toPrefix);

  /// <summary>
  /// 判断两个地址是否可以合并成一个批量读取请求
  /// </summary>
  /// <param name="address1">第一个地址</param>
  /// <param name="address2">第二个地址</param>
  /// <param name="maxGap">最大允许的地址间隔</param>
  /// <returns>是否可以合并</returns>
  bool CanCombineAddresses(AddressParseResult address1, AddressParseResult address2, int maxGap = 50);
}