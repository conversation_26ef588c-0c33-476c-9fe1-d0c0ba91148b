using System.Text.RegularExpressions;
using EdgeGateway.Driver.Entity.Model;

namespace EdgeGateway.Driver.Interface.DriverBase.AddressParsing.Optimizers;

/// <summary>
/// Modbus协议专用批量读取优化器
/// </summary>
public class ModbusBatchReadOptimizer : DefaultBatchReadOptimizer
{
  /// <summary>
  /// 获取优化器适用的协议名称
  /// </summary>
  public override string ProtocolName => "modbus";

  /// <summary>
  /// Modbus默认最佳分块大小
  /// </summary>
  protected override int DefaultChunkSize => 100;

  /// <summary>
  /// Modbus使用2字节/地址
  /// </summary>
  protected override int DefaultByteMultiplier => 2;

  /// <summary>
  /// 用于匹配Modbus功能码地址的正则表达式
  /// </summary>
  private static readonly Regex FunctionCodeRegex = new(@"^([0-3])\w*[:x]?(\d+)", RegexOptions.IgnoreCase | RegexOptions.Compiled);

  /// <summary>
  /// 优化批量读取的地址列表分组
  /// </summary>
  /// <param name="addresses">已解析的地址列表</param>
  /// <returns>优化后的地址分组</returns>
  public override List<List<AddressParseResult>> OptimizeAddressGroups(List<AddressParseResult> addresses)
  {
    if (addresses == null || addresses.Count == 0)
      return new List<List<AddressParseResult>>();

    // 按寄存器类型分组（功能码）
    var registerGroups = new Dictionary<int, List<AddressParseResult>>();

    // 首先按功能码分组
    foreach (var address in addresses)
    {
      if (string.IsNullOrEmpty(address.OriginalAddress))
        continue;

      int functionCode = GetFunctionCode(address.OriginalAddress);

      if (!registerGroups.ContainsKey(functionCode))
        registerGroups[functionCode] = new List<AddressParseResult>();

      registerGroups[functionCode].Add(address);
    }

    var result = new List<List<AddressParseResult>>();

    // 对每个功能码组内的地址进行排序和连续性分组
    foreach (var registerGroup in registerGroups.Values)
    {
      // 按地址数字排序
      var sortedAddresses = registerGroup.OrderBy(a => a.Number).ToList();

      // 连续性分组
      var subGroups = new List<List<AddressParseResult>>();
      var currentGroup = new List<AddressParseResult> { sortedAddresses[0] };

      for (int i = 1; i < sortedAddresses.Count; i++)
      {
        var prevAddress = sortedAddresses[i - 1];
        var currAddress = sortedAddresses[i];

        // 判断是否可以合并到一个批次中
        if (CanCombineAddresses(prevAddress, currAddress))
        {
          currentGroup.Add(currAddress);
        }
        else
        {
          // 不连续，创建新分组
          subGroups.Add(new List<AddressParseResult>(currentGroup));
          currentGroup = new List<AddressParseResult> { currAddress };
        }
      }

      // 添加最后一个分组
      if (currentGroup.Count > 0)
        subGroups.Add(currentGroup);

      result.AddRange(subGroups);
    }

    return result;
  }

  /// <summary>
  /// 计算读取长度，考虑Modbus寄存器特性
  /// </summary>
  /// <param name="minNumber">最小地址数字</param>
  /// <param name="maxNumber">最大地址数字</param>
  /// <param name="dataTypes">数据类型列表</param>
  /// <returns>计算得到的读取长度</returns>
  public override ushort CalculateReadLength(decimal minNumber, decimal maxNumber, List<string> dataTypes)
  {
    // Modbus计算寄存器差值，每个寄存器是16位（2字节）
    var baseDiff = (ushort)Math.Ceiling(maxNumber - minNumber + 1);

    // 如果数据类型列表为空，直接返回基础差值
    if (dataTypes == null || dataTypes.Count == 0)
      return baseDiff;

    // 查找最大地址对应的数据类型
    var maxAddressIndex = dataTypes.Count - 1;
    var maxAddressDataType = dataTypes[maxAddressIndex].ToLower();

    // 根据数据类型调整长度（Modbus特定调整）
    switch (maxAddressDataType)
    {
      case "int32":
      case "uint32":
      case "float":
        return (ushort)(baseDiff + 1); // 32位类型需要多读1个寄存器

      case "int64":
      case "uint64":
      case "double":
        return (ushort)(baseDiff + 3); // 64位类型需要多读3个寄存器

      case "string":
        // 字符串类型，长度按需调整
        return (ushort)(baseDiff + 10); // 默认多读10个寄存器，可根据具体需求调整

      default:
        return baseDiff; // 16位或位类型不需要额外寄存器
    }
  }

  /// <summary>
  /// 获取推荐的最大分块大小，根据Modbus不同区域特性优化
  /// </summary>
  /// <param name="addressPrefix">地址前缀</param>
  /// <returns>推荐的分块大小</returns>
  public override int GetRecommendedChunkSize(string addressPrefix)
  {
    if (string.IsNullOrEmpty(addressPrefix))
      return DefaultChunkSize;

    // 线圈（0x）和离散输入（1x）状态推荐较大批量（按位读取）
    if (addressPrefix.StartsWith("0", StringComparison.OrdinalIgnoreCase) ||
        addressPrefix.StartsWith("1", StringComparison.OrdinalIgnoreCase))
      return 800;  // 按位读取可以一次读取800个位

    // 保持寄存器（4x）和输入寄存器（3x）推荐中等大小
    if (addressPrefix.StartsWith("3", StringComparison.OrdinalIgnoreCase) ||
        addressPrefix.StartsWith("4", StringComparison.OrdinalIgnoreCase))
      return 120;  // 通常一次读取100-125个寄存器比较合适

    // 其他情况使用默认值
    return DefaultChunkSize;
  }

  /// <summary>
  /// 获取协议特定的字节倍率
  /// </summary>
  /// <param name="addressPrefix">地址前缀</param>
  /// <returns>字节倍率</returns>
  public override int GetByteMultiplier(string addressPrefix)
  {
    // 线圈和离散输入按位读取，16位寄存器占用2字节
    if (addressPrefix.StartsWith("0", StringComparison.OrdinalIgnoreCase) ||
        addressPrefix.StartsWith("1", StringComparison.OrdinalIgnoreCase))
      return 1 / 8;  // 位操作

    // 默认是2字节/寄存器
    return 2;
  }

  /// <summary>
  /// 检查是否支持跨区域批量读取
  /// </summary>
  /// <param name="fromPrefix">起始区域前缀</param>
  /// <param name="toPrefix">目标区域前缀</param>
  /// <returns>是否支持跨区域读取</returns>
  public override bool SupportsCrossAreaReading(string fromPrefix, string toPrefix)
  {
    // Modbus不支持跨区域读取（0x/1x/3x/4x不能混合）
    return string.Equals(fromPrefix, toPrefix, StringComparison.OrdinalIgnoreCase) &&
           GetFunctionCode(fromPrefix) == GetFunctionCode(toPrefix);
  }

  /// <summary>
  /// 判断两个地址是否可以合并成一个批量读取请求
  /// </summary>
  /// <param name="address1">第一个地址</param>
  /// <param name="address2">第二个地址</param>
  /// <param name="maxGap">最大允许的地址间隔</param>
  /// <returns>是否可以合并</returns>
  public override bool CanCombineAddresses(AddressParseResult address1, AddressParseResult address2, int maxGap = 50)
  {
    // 检查功能码是否相同（不能跨区域读取）
    if (GetFunctionCode(address1.OriginalAddress) != GetFunctionCode(address2.OriginalAddress))
      return false;

    // 检查位地址
    if (address1.BitPosition >= 0 || address2.BitPosition >= 0)
    {
      // 如果是线圈或离散输入寄存器的不同位，可以合并
      if (address1.Number == address2.Number)
        return true;
    }

    // 检查地址间隔
    var gap = address2.Number - address1.Number;

    // 对于不同类型的寄存器，调整最大允许的间隔
    var adjustedMaxGap = maxGap;
    var functionCode = GetFunctionCode(address1.OriginalAddress);

    // 线圈和离散输入允许更大的间隔（因为按位读取）
    if (functionCode == 0 || functionCode == 1)
      adjustedMaxGap = maxGap * 16;  // 16倍间隔

    return gap > 0 && gap <= adjustedMaxGap;
  }

  /// <summary>
  /// 获取Modbus功能码
  /// </summary>
  /// <param name="address">地址字符串</param>
  /// <returns>功能码（0-3）</returns>
  private int GetFunctionCode(string address)
  {
    if (string.IsNullOrEmpty(address))
      return -1;

    // 尝试解析常见的功能码格式
    var match = FunctionCodeRegex.Match(address);
    if (match.Success && int.TryParse(match.Groups[1].Value, out int functionCode))
      return functionCode;

    // 处理常见的别名格式
    if (address.StartsWith("0X", StringComparison.OrdinalIgnoreCase) ||
        address.StartsWith("COIL", StringComparison.OrdinalIgnoreCase))
      return 0;  // 线圈

    if (address.StartsWith("1X", StringComparison.OrdinalIgnoreCase) ||
        address.StartsWith("DISCRETE", StringComparison.OrdinalIgnoreCase))
      return 1;  // 离散输入

    if (address.StartsWith("3X", StringComparison.OrdinalIgnoreCase) ||
        address.StartsWith("INPUT", StringComparison.OrdinalIgnoreCase))
      return 3;  // 输入寄存器

    if (address.StartsWith("4X", StringComparison.OrdinalIgnoreCase) ||
        address.StartsWith("HOLDING", StringComparison.OrdinalIgnoreCase) ||
        address.StartsWith("REGISTER", StringComparison.OrdinalIgnoreCase))
      return 4;  // 保持寄存器

    // 默认情况下，假设是保持寄存器
    return 4;
  }
}