# 智能批量读取功能架构

## 目录结构

```
BatchReading/
├── Core/                   # 核心批量读取功能
│   ├── IBatchReadOptimizer.cs          # 批量读取优化器接口
│   ├── DefaultBatchReadOptimizer.cs    # 默认批量读取优化器
│   ├── ModbusBatchReadOptimizer.cs     # Modbus协议优化器
│   ├── MelsecBatchReadOptimizer.cs     # 三菱协议优化器
│   ├── OmronBatchReadOptimizer.cs      # 欧姆龙协议优化器
│   └── SiemensBatchReadOptimizer.cs    # 西门子协议优化器
├── Diagnostics/            # 诊断和性能测试工具
│   ├── SmartBatchDiagnosticsExt.cs     # 智能批量读取诊断扩展
│   └── BatchReadPerformanceTester.cs   # 批量读取性能测试器
└── Monitoring/             # 监控和统计组件
    ├── BatchReadMonitor.cs             # 批量读取监控组件
    └── ReadStatisticsManager.cs        # 读取统计管理器
```

## 功能概述

智能批量读取（Smart Batch Reading）是一种优化的数据采集方法，专为工业通信协议设计，旨在提高数据读取的效率、稳定性和容错性。通过智能地将多个数据点分组处理，大幅减少通信次数，同时具备自适应能力，可根据实际通信环境自动调整读取策略。

## 核心组件

### 1. 批量读取优化器 (Core)

批量读取优化器负责根据协议特性对地址进行智能分组和优化，以减少通信次数并提高读取效率。

#### 接口与实现

- **IBatchReadOptimizer**: 定义了批量读取优化器的基本接口

  - `OptimizeAddressGroups`: 优化地址分组
  - `CalculateReadLength`: 计算读取长度
  - `GetRecommendedChunkSize`: 获取推荐的分块大小
  - `CanCombineAddresses`: 判断两个地址是否可以合并
  - `GetDataTypeSize`: 获取数据类型的字节数
  - `ProtocolName`: 获取优化器适用的协议名称

- **DefaultBatchReadOptimizer**: 默认批量读取优化器实现
- **ModbusBatchReadOptimizer**: Modbus 协议专用优化器
- **MelsecBatchReadOptimizer**: 三菱 PLC 专用优化器
- **OmronBatchReadOptimizer**: 欧姆龙 PLC 专用优化器
- **SiemensBatchReadOptimizer**: 西门子 PLC 专用优化器

### 2. 诊断工具 (Diagnostics)

诊断工具提供了对批量读取过程的监控、分析和优化建议。

- **SmartBatchDiagnosticsExt**: 智能批量读取诊断信息扩展

  - `AnalyzeBottlenecks`: 分析性能瓶颈并提供建议
  - `GenerateReport`: 生成诊断报告

- **BatchReadPerformanceTester**: 批量读取性能测试器
  - `RunTest`: 运行单个测试
  - `RunComparison`: 运行多个配置对比测试
  - `GenerateReport`: 生成性能报告

### 3. 监控组件 (Monitoring)

监控组件负责收集和管理批量读取的性能统计数据，提供实时监控和历史数据分析。

- **BatchReadMonitor**: 批量读取监控组件

  - `LogOperation`: 记录读取操作
  - `GetProtocolStats`: 获取协议性能统计
  - `GeneratePerformanceReport`: 生成性能报告
  - `AnalyzeBottlenecks`: 分析性能瓶颈

- **ReadStatisticsManager**: 读取统计管理器
  - `UpdateStatistics`: 更新统计信息
  - `GetStatistics`: 获取地址前缀的统计信息
  - `GetRecommendedChunkSize`: 获取推荐的分块大小
  - `AnalyzeBottlenecks`: 分析性能瓶颈

## 工作流程

### 1. 初始化阶段

1. 系统启动时，在`CollectsTheCommonParentClass`的静态构造函数中注册各协议的批量读取优化器
2. 各协议基类（如`ModbusBase`、`MelsecBase`等）重写`GetBatchReadOptimizer`方法，返回对应的优化器实例

### 2. 读取请求处理

1. 客户端调用`ReadWithSmartBatch`方法，传入需要读取的参数列表和可选的配置选项
2. 系统根据地址特征（前缀、数据类型等）将参数分组
3. 根据处理模式（顺序、分批并行、完全并行）执行读取操作
4. 对每个分组执行`TryReadAddressRange`方法，包含重试和降级逻辑
5. 收集读取结果并返回给客户端

### 3. 自适应优化

1. 系统记录每次读取的性能数据（成功率、执行时间等）
2. 根据历史性能数据动态调整分块大小和读取策略
3. 出现失败时自动降级为更小的读取操作

### 4. 诊断与监控

1. 收集读取过程中的诊断信息
2. 分析性能瓶颈并提供优化建议
3. 生成性能报告供用户参考

## 配置选项

智能批量读取提供多种配置选项，可根据不同设备和通信环境进行调整：

| 选项                   | 说明                 | 默认值          |
| ---------------------- | -------------------- | --------------- |
| MaxRetries             | 最大重试次数         | 3               |
| InitialChunkSize       | 初始分块大小         | 500             |
| MaxChunkSize           | 最大分块大小         | 5000            |
| MinChunkSize           | 最小分块大小         | 50              |
| RetryBaseDelay         | 重试基础延迟(ms)     | 100             |
| EnableAdaptiveGrouping | 是否启用自适应分组   | true            |
| EnableDegradedReading  | 是否启用降级读取     | true            |
| BackoffFactor          | 指数退避因子         | 2.0             |
| MaxRetryDelay          | 最大重试等待时间(ms) | 2000            |
| DegradationThreshold   | 降级阈值             | 2               |
| StatisticsHistorySize  | 性能统计历史记录数   | 50              |
| MaxParallelism         | 最大并行度           | CPU 核心数      |
| ProcessingMode         | 批处理模式           | BatchedParallel |
| MaxDegradationLevels   | 最大降级层数         | 3               |
| EnableAddressCache     | 是否启用地址缓存     | true            |
| AddressCacheTimeout    | 地址缓存过期时间(ms) | 60000           |

## 批处理模式

系统支持三种批处理模式，可根据实际需求选择合适的模式：

### 1. 顺序处理（Sequential）

- 按顺序处理所有组
- 适用于低性能设备或需要严格控制读取顺序的场景
- 资源占用最少，但执行时间可能较长

### 2. 分批并行（BatchedParallel）

- 控制并发数进行并行处理
- 平衡性能和资源消耗
- 默认模式，适用于大多数场景

### 3. 完全并行（FullParallel）

- 同时处理所有组
- 性能最高，但资源消耗也最大
- 适用于高性能设备和网络环境

## 使用方法

### 1. 基本调用

```csharp
// 创建需要读取的参数列表
List<DriverReadInput> paramList = new List<DriverReadInput>
{
    new DriverReadInput { Id = "tag1", Address = "D100", DataType = "int16" },
    new DriverReadInput { Id = "tag2", Address = "D101", DataType = "int16" },
    // ... 更多参数
};

// 使用默认配置调用智能批量读取
List<ReadDataResult> results = await driver.ReadWithSmartBatch(paramList);
```

### 2. 自定义配置调用

```csharp
// 创建自定义配置
var options = new SmartBatchOptions
{
    InitialChunkSize = 200,     // 初始分块大小
    MaxRetries = 2,             // 最大重试次数
    EnableAdaptiveGrouping = true,  // 启用自适应分组
    EnableDegradedReading = true,   // 启用降级读取
    ProcessingMode = BatchProcessingMode.BatchedParallel,  // 设置处理模式
    MaxParallelism = 4,         // 设置最大并行度
    EnableAddressCache = true,   // 启用地址缓存
    AddressCacheTimeout = 60000  // 设置缓存过期时间
};

// 使用自定义配置调用
List<ReadDataResult> results = await driver.ReadWithSmartBatch(paramList, options);
```

### 3. 协议特定优化

在各协议基类中重写 `GetProtocolHint` 和 `GetBatchReadOptimizer` 方法：

```csharp
// 在协议基类中重写方法
protected override string? GetProtocolHint()
{
    return "modbus";  // 返回协议名称
}

protected override IBatchReadOptimizer GetBatchReadOptimizer()
{
    return new ModbusBatchReadOptimizer();  // 返回协议特定的优化器
}
```

## 性能优化建议

1. **合理分组**：尽量将相同类型、连续地址的点位放在一起读取
2. **优化配置**：根据设备特性调整分块大小和重试参数
3. **监控性能**：定期检查读取统计数据，发现潜在问题
4. **平衡策略**：在稳定性和效率之间找到平衡点
5. **协议特化**：针对不同协议特性，实现专用的批量读取优化器

## 扩展方式

### 1. 添加新的协议优化器

1. 创建新的优化器类，继承`DefaultBatchReadOptimizer`
2. 重写必要的方法（如`GetRecommendedChunkSize`、`CanCombineAddresses`等）
3. 在`CollectsTheCommonParentClass`的静态构造函数中注册新的优化器
4. 在协议基类中重写`GetBatchReadOptimizer`方法，返回新的优化器实例

### 2. 增强诊断功能

1. 扩展`SmartBatchDiagnostics`类，添加新的诊断指标
2. 在`SmartBatchDiagnosticsExt`中添加新的分析方法
3. 在`ReadWithSmartBatch`方法中收集和使用新的诊断信息

### 3. 添加新的监控指标

1. 扩展`ReadStatistics`类，添加新的统计指标
2. 在`ReadStatisticsManager`中添加新的分析方法
3. 在性能报告中展示新的指标

## 注意事项

1. **地址解析**：确保地址解析准确，这是批量读取优化的基础
2. **分块大小**：不同协议和设备有不同的最佳分块大小，需要根据实际情况调整
3. **并行度**：过高的并行度可能导致设备负载过重，需要根据设备性能调整
4. **降级策略**：降级读取可以提高稳定性，但可能降低效率，需要权衡
5. **缓存策略**：地址缓存可以提高性能，但可能导致内存占用增加，需要合理设置过期时间

## 未来优化方向

1. **机器学习优化**：使用机器学习算法自动调整最佳分块大小和并行度
2. **预测性读取**：根据历史读取模式，预测下次可能读取的地址，提前读取
3. **动态超时**：根据网络状况和设备响应时间，动态调整超时时间
4. **分布式批量读取**：在多个网关间协调批量读取，进一步提高效率
5. **可视化监控**：提供可视化的性能监控和分析工具
