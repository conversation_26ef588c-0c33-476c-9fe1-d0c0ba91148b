using System.Text.RegularExpressions;
using EdgeGateway.Driver.Entity.Model;

namespace EdgeGateway.Driver.Interface.DriverBase.AddressParsing;

/// <summary>
/// 罗克韦尔PLC地址解析器
/// </summary>
public class RockwellAddressParser : IAddressParser
{
  // 罗克韦尔PLC地址格式的正则表达式
  private static readonly Regex GlobalTagRegex = new(@"^([A-Za-z_][A-Za-z0-9_]*)$", RegexOptions.Compiled);
  private static readonly Regex ProgramTagRegex = new(@"^Program:([A-Za-z0-9_]+)\.([A-Za-z_][A-Za-z0-9_]*)$", RegexOptions.Compiled);
  private static readonly Regex ArrayTagRegex = new(@"^(Program:([A-Za-z0-9_]+)\.)?([A-Za-z_][A-Za-z0-9_]*)\[(\d+)\]$", RegexOptions.Compiled);
  private static readonly Regex StructMemberRegex = new(@"^(Program:([A-Za-z0-9_]+)\.)?([A-Za-z_][A-Za-z0-9_]*)\.([A-Za-z_][A-Za-z0-9_]*)$", RegexOptions.Compiled);

  /// <summary>
  /// 解析单个罗克韦尔PLC地址
  /// </summary>
  public AddressParseResult Parse(string address, string dataType, int? length = null)
  {
    if (string.IsNullOrEmpty(address))
    {
      return new AddressParseResult
      {
        IsValid = false,
        RawAddress = address,
        ErrorMessage = "地址为空"
      };
    }

    try
    {
      // 全局标签 (例如: Tag1)
      var globalTagMatch = GlobalTagRegex.Match(address);
      if (globalTagMatch.Success)
      {
        var tagName = globalTagMatch.Groups[1].Value;

        return new AddressParseResult
        {
          IsValid = true,
          RawAddress = address,
          Prefix = "Global",
          Number = 0,
          BitPosition = -1,
          DataType = dataType,
          Length = length,
          Area = "Global",
          AddressType = "Tag",
          ProtocolSpecificData = new Dictionary<string, object>
                    {
                        { "TagName", tagName },
                        { "IsGlobalTag", true }
                    }
        };
      }

      // 程序标签 (例如: Program:MainProgram.Tag1)
      var programTagMatch = ProgramTagRegex.Match(address);
      if (programTagMatch.Success)
      {
        var programName = programTagMatch.Groups[1].Value;
        var tagName = programTagMatch.Groups[2].Value;

        return new AddressParseResult
        {
          IsValid = true,
          RawAddress = address,
          Prefix = "Program",
          Number = 0,
          BitPosition = -1,
          DataType = dataType,
          Length = length,
          Area = "Program",
          AddressType = "Tag",
          ProtocolSpecificData = new Dictionary<string, object>
                    {
                        { "ProgramName", programName },
                        { "TagName", tagName },
                        { "IsProgramTag", true }
                    }
        };
      }

      // 数组标签 (例如: Tag1[0] 或 Program:MainProgram.Tag1[0])
      var arrayTagMatch = ArrayTagRegex.Match(address);
      if (arrayTagMatch.Success)
      {
        var programName = arrayTagMatch.Groups[2].Success ? arrayTagMatch.Groups[2].Value : string.Empty;
        var tagName = arrayTagMatch.Groups[3].Value;
        var arrayIndex = int.Parse(arrayTagMatch.Groups[4].Value);

        return new AddressParseResult
        {
          IsValid = true,
          RawAddress = address,
          Prefix = string.IsNullOrEmpty(programName) ? "Global" : "Program",
          Number = arrayIndex,
          BitPosition = -1,
          DataType = dataType,
          Length = length,
          Area = string.IsNullOrEmpty(programName) ? "Global" : "Program",
          AddressType = "Array",
          ProtocolSpecificData = new Dictionary<string, object>
                    {
                        { "ProgramName", programName },
                        { "TagName", tagName },
                        { "ArrayIndex", arrayIndex },
                        { "IsArrayTag", true },
                        { "IsGlobalTag", string.IsNullOrEmpty(programName) }
                    }
        };
      }

      // 结构体成员 (例如: Tag1.Member1 或 Program:MainProgram.Tag1.Member1)
      var structMemberMatch = StructMemberRegex.Match(address);
      if (structMemberMatch.Success)
      {
        var programName = structMemberMatch.Groups[2].Success ? structMemberMatch.Groups[2].Value : string.Empty;
        var tagName = structMemberMatch.Groups[3].Value;
        var memberName = structMemberMatch.Groups[4].Value;

        return new AddressParseResult
        {
          IsValid = true,
          RawAddress = address,
          Prefix = string.IsNullOrEmpty(programName) ? "Global" : "Program",
          Number = 0,
          BitPosition = -1,
          DataType = dataType,
          Length = length,
          Area = string.IsNullOrEmpty(programName) ? "Global" : "Program",
          AddressType = "Struct",
          ProtocolSpecificData = new Dictionary<string, object>
                    {
                        { "ProgramName", programName },
                        { "TagName", tagName },
                        { "MemberName", memberName },
                        { "IsStructTag", true },
                        { "IsGlobalTag", string.IsNullOrEmpty(programName) }
                    }
        };
      }

      // 不支持的地址格式
      return new AddressParseResult
      {
        IsValid = false,
        RawAddress = address,
        ErrorMessage = $"不支持的罗克韦尔PLC地址格式: {address}"
      };
    }
    catch (Exception ex)
    {
      return new AddressParseResult
      {
        IsValid = false,
        RawAddress = address,
        ErrorMessage = $"解析罗克韦尔PLC地址时发生异常: {ex.Message}"
      };
    }
  }

  /// <summary>
  /// 批量解析罗克韦尔PLC地址
  /// </summary>
  public List<AddressParseResult> ParseBatch(List<(string Address, string DataType, int? Length)> addresses)
  {
    var results = new List<AddressParseResult>();
    foreach (var (address, dataType, length) in addresses)
    {
      results.Add(Parse(address, dataType, length));
    }
    return results;
  }

  /// <summary>
  /// 优化批量读取
  /// </summary>
  public List<List<AddressParseResult>> OptimizeForBatch(List<AddressParseResult> addresses)
  {
    var result = new List<List<AddressParseResult>>();

    // 按区域分组
    var groups = addresses
        .Where(a => a.IsValid)
        .GroupBy(a => GetGroupKey(a))
        .ToList();

    foreach (var group in groups)
    {
      // 将每个组作为一个批次
      var groupAddresses = group.ToList();
      if (groupAddresses.Any())
        result.Add(groupAddresses);
    }

    return result;
  }

  /// <summary>
  /// 获取分组键
  /// </summary>
  private string GetGroupKey(AddressParseResult address)
  {
    if (address.ProtocolSpecificData == null)
      return "Unknown";

    // 获取程序名称和标签名称
    string programName = address.ProtocolSpecificData.TryGetValue("ProgramName", out var program) ? program.ToString() : string.Empty;
    string tagName = address.ProtocolSpecificData.TryGetValue("TagName", out var tag) ? tag.ToString() : string.Empty;

    // 构建分组键
    if (string.IsNullOrEmpty(programName))
      return $"Global_{tagName}";
    else
      return $"Program_{programName}_{tagName}";
  }
}