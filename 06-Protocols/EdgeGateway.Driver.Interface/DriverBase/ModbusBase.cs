using System.IO.Ports;
using EdgeGateway.Driver.Interface.DriverBase.AddressParsing.Optimizers;
using EdgeGateway.Driver.Entity.Model;
using HslCommunication.BasicFramework;
using BatchProcessingMode = EdgeGateway.Driver.Entity.Model.BatchProcessingMode;

namespace EdgeGateway.Driver.Interface.DriverBase;

/// <summary>
///     modbus协议公共类
/// </summary>
public class ModbusBase : PlcProtocolCollector
{
    #region 连接配置

    [ConfigParameter("站号", GroupName = "连接配置", Order = 3, Type = "number")]
    public int Station { get; set; } = 1;

    [ConfigParameter("大小端", GroupName = "连接配置", Order = 4, Type = "select")]
    public DataFormat DataFormat { get; set; } = DataFormat.ABCD;

    [ConfigParameter("字符串颠倒", GroupName = "连接配置", Order = 5, Type = "boolean")]
    public bool IsStringReverse { get; set; }

    [ConfigParameter("首地址从0开始", GroupName = "连接配置", Order = 6, Type = "boolean")]
    public bool AddressStartWithZero { get; set; } = true;

    [ConfigParameter("批量读取", GroupName = "高级配置", Order = 7, Type = "boolean")]
    public bool CombinedRead { get; set; } = true;

    /// <summary>
    ///     获取协议提示
    /// </summary>
    /// <returns>返回modbus协议</returns>
    protected override string? GetProtocolHint()
    {
        return "modbus";
    }

    /// <summary>
    ///     获取Modbus批量读取优化器
    /// </summary>
    /// <returns>Modbus优化器实例</returns>
    protected override IBatchReadOptimizer GetBatchReadOptimizer()
    {
        return new ModbusBatchReadOptimizer();
    }

    /// <summary>
    ///     重写核心读取方法，直接调用Modbus驱动而不使用反射
    /// </summary>
    /// <param name="startAddress">起始地址</param>
    /// <param name="length">读取长度</param>
    /// <param name="addresses">地址列表</param>
    /// <returns>读取结果字典，键为地址，值为读取值</returns>
    protected override async Task<Dictionary<string, object>> ReadAddressRangeCore(
        string startAddress,
        ushort length,
        List<AddressParseResult> addresses)
    {
        var result = new Dictionary<string, object>();

        try
        {
            // 检查驱动是否可用
            if (Driver == null)
            {
                OnOutputReceived("Modbus驱动未初始化");
                throw new InvalidOperationException("Modbus驱动未初始化");
            }

            OnOutputReceived($"Modbus读取地址范围: {startAddress}, 长度: {length}");

            // 直接调用驱动的ReadAsync方法，不使用反射
            var readResult = await Driver.ReadAsync(startAddress, length);

            if (readResult == null)
            {
                OnOutputReceived("Modbus读取返回null结果");
                throw new InvalidOperationException("读取返回null结果");
            }

            if (!readResult.IsSuccess)
            {
                OnOutputReceived($"Modbus读取失败: {readResult.Message}");
                throw new InvalidOperationException($"读取失败: {readResult.Message}");
            }

            // 获取读取的字节数据
            var content = readResult.Content;
            if (content == null || content.Length == 0)
            {
                OnOutputReceived("Modbus读取返回空数据");
                throw new InvalidOperationException("读取返回空数据");
            }

            OnOutputReceived($"Modbus成功读取 {content.Length} 字节数据");

            // 解析每个地址的数据
            foreach (var addr in addresses)
            {
                try
                {
                    // 计算字节偏移量
                    var offset = CalculateByteOffset(addr, addresses.First(), content.Length);

                    // 根据数据类型解析值
                    var value = ParseValueFromBytes(addr, content, offset);

                    result[addr.OriginalAddress] = value;

                    OnOutputReceived($"地址 {addr.OriginalAddress} 解析成功，值: {value}");
                }
                catch (Exception ex)
                {
                    OnOutputReceived($"解析地址 {addr.OriginalAddress} 失败: {ex.Message}");
                    result[addr.OriginalAddress] = null;
                }
            }
        }
        catch (Exception ex)
        {
            OnOutputReceived($"Modbus ReadAddressRangeCore异常: {ex.Message}");
            throw;
        }

        return result;
    }

    /// <summary>
    ///     计算字节偏移量
    /// </summary>
    /// <param name="targetAddress">目标地址</param>
    /// <param name="startAddress">起始地址</param>
    /// <param name="contentLength">内容长度</param>
    /// <returns>字节偏移量</returns>
    private int CalculateByteOffset(AddressParseResult targetAddress, AddressParseResult startAddress, int contentLength)
    {
        // Modbus地址对应寄存器（2字节）
        var addressDiff = targetAddress.Number - startAddress.Number;

        // Modbus字偏移
        var byteOffset = (int)addressDiff * 2;

        // 确保偏移量在有效范围内
        if (byteOffset < 0 || byteOffset >= contentLength)
        {
            OnOutputReceived($"字节偏移量超出范围: {byteOffset}, 内容长度: {contentLength}");
            return 0;
        }

        return byteOffset;
    }

    /// <summary>
    ///     从字节数组中解析值
    /// </summary>
    /// <param name="address">地址信息</param>
    /// <param name="content">字节数组</param>
    /// <param name="offset">偏移量</param>
    /// <returns>解析后的值</returns>
    private object ParseValueFromBytes(AddressParseResult address, byte[] content, int offset)
    {
        if (Driver?.ByteTransform == null)
        {
            OnOutputReceived("ByteTransform未初始化");
            return null;
        }

        try
        {
            // 检查是否为位地址
            var isBitAddress = address.ProtocolSpecificData?.ContainsKey("IsBitAddress") == true &&
                              (bool)address.ProtocolSpecificData["IsBitAddress"];

            if (isBitAddress || address.DataType == "boolean" || address.DataType == "bit")
            {
                return ParseBooleanValue(address, content, offset);
            }

            // 根据数据类型解析
            return address.DataType?.ToLower() switch
            {
                "uint16" => Driver.ByteTransform.TransUInt16(content, offset),
                "int16" => Driver.ByteTransform.TransInt16(content, offset),
                "uint32" => Driver.ByteTransform.TransUInt32(content, offset),
                "int32" => Driver.ByteTransform.TransInt32(content, offset),
                "float" => Driver.ByteTransform.TransSingle(content, offset),
                "uint64" => Driver.ByteTransform.TransUInt64(content, offset),
                "int64" => Driver.ByteTransform.TransInt64(content, offset),
                "double" => Driver.ByteTransform.TransDouble(content, offset),
                "string" => ParseStringValue(address, content, offset),
                _ => Driver.ByteTransform.TransInt16(content, offset) // 默认为int16
            };
        }
        catch (Exception ex)
        {
            OnOutputReceived($"解析数据类型 {address.DataType} 失败: {ex.Message}");
            return null;
        }
    }

    /// <summary>
    ///     解析布尔值
    /// </summary>
    /// <param name="address">地址信息</param>
    /// <param name="content">字节数组</param>
    /// <param name="offset">偏移量</param>
    /// <returns>布尔值或位值</returns>
    private object ParseBooleanValue(AddressParseResult address, byte[] content, int offset)
    {
        try
        {
            // 获取位位置
            var bitPosition = address.BitPosition >= 0 ? address.BitPosition : 0;

            // 如果地址包含小数点，解析位位置
            if (address.RawAddress.Contains("."))
            {
                var parts = address.RawAddress.Split('.');
                if (parts.Length > 1 && int.TryParse(parts[1], out var bit))
                {
                    bitPosition = bit;
                }
            }

            // 确保有足够的字节
            if (offset + 1 >= content.Length)
            {
                OnOutputReceived($"布尔值解析：偏移量超出范围 {offset}");
                return false;
            }

            // 获取两个字节并按字反转（如果需要）
            var bytes = content.Length == 1 ? content : GetTwoBytesAt(content, offset);
            if (bytes.Length > 1)
            {
                bytes = SoftBasic.BytesReverseByWord(bytes);
            }

            var boolValue = Driver.ByteTransform.TransBool(bytes, bitPosition);

            // 根据数据类型返回不同格式
            return address.DataType == "bit" ? (boolValue ? 1 : 0) : (object)boolValue;
        }
        catch (Exception ex)
        {
            OnOutputReceived($"解析布尔值失败: {ex.Message}");
            return false;
        }
    }

    /// <summary>
    ///     解析字符串值
    /// </summary>
    /// <param name="address">地址信息</param>
    /// <param name="content">字节数组</param>
    /// <param name="offset">偏移量</param>
    /// <returns>字符串值</returns>
    private string ParseStringValue(AddressParseResult address, byte[] content, int offset)
    {
        try
        {
            var readLength = address.Length ?? 10;

            // 根据编码类型解析字符串
            // 注意：这里假设有一个Encoding属性，如果没有则使用UTF8
            var encoding = System.Text.Encoding.UTF8; // 默认编码

            return Driver.ByteTransform.TransString(content, offset, readLength, encoding);
        }
        catch (Exception ex)
        {
            OnOutputReceived($"解析字符串失败: {ex.Message}");
            return string.Empty;
        }
    }

    /// <summary>
    ///     获取指定位置的两个字节
    /// </summary>
    /// <param name="content">字节数组</param>
    /// <param name="offset">偏移量</param>
    /// <returns>两个字节的数组</returns>
    private byte[] GetTwoBytesAt(byte[] content, int offset)
    {
        if (offset + 1 >= content.Length)
        {
            return new byte[] { content[offset], 0 };
        }
        return new byte[] { content[offset], content[offset + 1] };
    }

    #endregion 连接配置
}

/// <summary>
///     modbus网络
/// </summary>
public class ModbusNetBase : ModbusBase
{
    [ConfigParameter("IP地址", GroupName = "连接配置", Order = 1)]
    public virtual string IpAddress { get; set; } = "127.0.0.1";

    [ConfigParameter("端口", GroupName = "连接配置", Order = 2, Type = "number")]
    public virtual int Port { get; set; } = 502;

    [ConfigParameter("心跳检测", GroupName = "连接配置", Order = 7, Type = "number")]
    public int KeepAlive { get; set; } = -1;
}

/// <summary>
///     modbus串口
/// </summary>
public class ModbusSerialBase : ModbusBase
{
    [ConfigParameter("串口号", GroupName = "连接配置", Order = 1)]
    public virtual string PortName { get; set; } = "COM1";

    [ConfigParameter("波特率", GroupName = "连接配置", Order = 2, Type = "number")]
    public virtual int BaudRate { get; set; } = 9600;

    [ConfigParameter("数据位", GroupName = "连接配置", Order = 3, Type = "number")]
    public virtual int DataBits { get; set; } = 8;

    [ConfigParameter("校验位", GroupName = "连接配置", Order = 4, Type = "select")]
    public virtual Parity Parity { get; set; } = Parity.None;

    [ConfigParameter("停止位", GroupName = "连接配置", Order = 5, Type = "number")]
    public virtual StopBits StopBits { get; set; } = StopBits.One;
}