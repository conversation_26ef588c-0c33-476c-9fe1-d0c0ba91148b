namespace EdgeGateway.Driver.Interface.DriverBase.AddressParsing;

/// <summary>
///     地址范围验证接口
/// </summary>
public interface IAddressRangeValidator
{
  /// <summary>
  ///     验证单个地址是否在有效范围内
  /// </summary>
  /// <param name="address">解析后的地址</param>
  /// <returns>验证结果</returns>
  AddressRangeValidationResult ValidateAddress(AddressParseResult address);

  /// <summary>
  ///     批量验证地址范围
  /// </summary>
  /// <param name="addresses">地址列表</param>
  /// <returns>验证结果列表</returns>
  List<AddressRangeValidationResult> ValidateBatch(List<AddressParseResult> addresses);
}

/// <summary>
///     地址范围验证结果
/// </summary>
public class AddressRangeValidationResult
{
  /// <summary>
  ///     是否有效
  /// </summary>
  public bool IsValid { get; set; }

  /// <summary>
  ///     原始地址
  /// </summary>
  public string RawAddress { get; set; } = string.Empty;

  /// <summary>
  ///     错误消息
  /// </summary>
  public string ErrorMessage { get; set; } = string.Empty;

  /// <summary>
  ///     区域名称
  /// </summary>
  public string Area { get; set; } = string.Empty;

  /// <summary>
  ///     区域最小值
  /// </summary>
  public decimal MinValue { get; set; }

  /// <summary>
  ///     区域最大值
  /// </summary>
  public decimal MaxValue { get; set; }

  /// <summary>
  ///     实际值
  /// </summary>
  public decimal ActualValue { get; set; }
}