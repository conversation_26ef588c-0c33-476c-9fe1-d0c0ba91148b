using System;
using System.Collections.Generic;
using System.Linq;
using EdgeGateway.Driver.Entity.Interface;
using EdgeGateway.Driver.Entity.Model;
using EdgeGateway.Driver.Interface.DriverBase.AddressParsing;
using AddressParserBase = EdgeGateway.Driver.Entity.Parsers.AddressParserBase;

namespace KeyenceNet;

/// <summary>
///     基恩士PLC地址解析器注册类
/// </summary>
public static class KeyenceAddressParserRegistration
{
  /// <summary>
  ///     注册基恩士PLC地址解析器
  /// </summary>
  public static void RegisterKeyenceAddressParser()
    {
        // 注册到接口层的解析器工厂
        var interfaceFactory = AddressParserFactory.Instance;
        interfaceFactory.RegisterParser("keyence", new KeyenceAddressParser());

        // 注册到实体层的解析器工厂
        var entityFactory = EdgeGateway.Driver.Entity.Services.AddressParserFactory.Instance;
        entityFactory.RegisterParser(new KeyenceAddressParserAdapter());
    }

  /// <summary>
  ///     基恩士PLC地址解析器适配器（用于实体层）
  /// </summary>
  private class KeyenceAddressParserAdapter : AddressParserBase
    {
        public override string ProtocolName => "Keyence";
        public override int Priority => 70;

        public override string[] SupportedFormats => new[]
        {
            "B0", // 继电器
            "MR0", // 内部辅助继电器
            "LR0", // 锁存继电器
            "CR0", // 控制继电器
            "VB0", // 虚拟继电器
            "DM0", // 数据存储器
            "EM0", // 扩展数据存储器
            "FM0", // 临时数据存储器
            "ZF0", // 文件寄存器
            "W0", // 工作寄存器
            "T0", // 定时器
            "C0", // 计数器
            "TC0", // 定时器线圈
            "CC0", // 计数器线圈
            "B0.1", // 位地址
            "MR0.1", // 位地址
            "LR0.1", // 位地址
            "CR0.1", // 位地址
            "VB0.1" // 位地址
        };

        public override bool CanParse(string address)
        {
            if (!base.CanParse(address))
                return false;

            var upperAddress = address.ToUpper().Trim();
            return upperAddress.StartsWith("B") ||
                   upperAddress.StartsWith("MR") ||
                   upperAddress.StartsWith("LR") ||
                   upperAddress.StartsWith("CR") ||
                   upperAddress.StartsWith("VB") ||
                   upperAddress.StartsWith("DM") ||
                   upperAddress.StartsWith("EM") ||
                   upperAddress.StartsWith("FM") ||
                   upperAddress.StartsWith("ZF") ||
                   upperAddress.StartsWith("W") ||
                   upperAddress.StartsWith("T") ||
                   upperAddress.StartsWith("C") ||
                   upperAddress.StartsWith("TC") ||
                   upperAddress.StartsWith("CC");
        }

        public override UniversalAddress Parse(string address, string dataType = null, int? length = null)
        {
            var result = new UniversalAddress
            {
                RawAddress = address,
                Protocol = "Keyence",
                DataType = dataType,
                Length = length
            };

            try
            {
                var upperAddress = address.ToUpper().Trim();

                // 提取区域类型
                string areaType;
                int startIndex;

                // 处理2字符前缀的区域类型
                if (upperAddress.StartsWith("MR") || upperAddress.StartsWith("LR") || upperAddress.StartsWith("CR") ||
                    upperAddress.StartsWith("VB") || upperAddress.StartsWith("DM") || upperAddress.StartsWith("EM") ||
                    upperAddress.StartsWith("FM") || upperAddress.StartsWith("ZF") || upperAddress.StartsWith("TC") ||
                    upperAddress.StartsWith("CC"))
                {
                    areaType = upperAddress.Substring(0, 2);
                    startIndex = 2;
                }
                // 处理1字符前缀的区域类型
                else
                {
                    areaType = upperAddress.Substring(0, 1);
                    startIndex = 1;
                }

                result.AreaType = areaType;

                // 检查是否有位地址
                var dotIndex = upperAddress.IndexOf('.');
                if (dotIndex > 0)
                {
                    // 解析主地址
                    if (decimal.TryParse(upperAddress.Substring(startIndex, dotIndex - startIndex), out var mainAddr))
                    {
                        result.BaseAddress = (long)mainAddr;
                    }
                    else
                    {
                        result.IsValid = false;
                        result.ErrorMessage = $"无效的基恩士PLC主地址: {upperAddress}";
                        return result;
                    }

                    // 解析位地址
                    if (int.TryParse(upperAddress.Substring(dotIndex + 1), out var bitPos))
                    {
                        result.BitPosition = bitPos;
                    }
                    else
                    {
                        result.IsValid = false;
                        result.ErrorMessage = $"无效的基恩士PLC位地址: {upperAddress}";
                        return result;
                    }
                }
                else
                {
                    // 只有主地址
                    if (decimal.TryParse(upperAddress.Substring(startIndex), out var mainAddr))
                    {
                        result.BaseAddress = (long)mainAddr;
                    }
                    else
                    {
                        result.IsValid = false;
                        result.ErrorMessage = $"无效的基恩士PLC地址: {upperAddress}";
                        return result;
                    }
                }

                result.IsValid = true;
                return result;
            }
            catch (Exception ex)
            {
                result.IsValid = false;
                result.ErrorMessage = $"解析基恩士PLC地址时发生异常: {ex.Message}";
                return result;
            }
        }

        public override AddressValidationResult Validate(UniversalAddress universalAddress)
        {
            var baseResult = base.Validate(universalAddress);
            if (!baseResult.IsValid)
                return baseResult;

            var warnings = new List<string>();

            // 检查区域类型
            var validAreaTypes = new[] { "B", "MR", "LR", "CR", "VB", "DM", "EM", "FM", "ZF", "W", "T", "C", "TC", "CC" };
            if (!validAreaTypes.Contains(universalAddress.AreaType)) return AddressValidationResult.Failure($"不支持的基恩士PLC区域类型: {universalAddress.AreaType}");

            // 检查地址范围
            var maxAddresses = new Dictionary<string, long>
            {
                { "B", 9999 },
                { "MR", 9999 },
                { "LR", 9999 },
                { "CR", 9999 },
                { "VB", 9999 },
                { "DM", 9999 },
                { "EM", 9999 },
                { "FM", 9999 },
                { "ZF", 9999 },
                { "W", 9999 },
                { "T", 511 },
                { "C", 511 },
                { "TC", 511 },
                { "CC", 511 }
            };

            if (maxAddresses.TryGetValue(universalAddress.AreaType, out var maxAddr) &&
                universalAddress.BaseAddress > maxAddr)
                return AddressValidationResult.Failure(
                    $"基恩士PLC {universalAddress.AreaType} 区域地址超出范围: {universalAddress.BaseAddress} (最大值: {maxAddr})");

            // 检查位地址范围
            if (universalAddress.BitPosition.HasValue)
            {
                // 只有B、MR、LR、CR、VB支持位地址
                var supportBitAddressing = new[] { "B", "MR", "LR", "CR", "VB" };
                if (!supportBitAddressing.Contains(universalAddress.AreaType))
                    return AddressValidationResult.Failure(
                        $"基恩士PLC {universalAddress.AreaType} 区域不支持位地址访问");

                var maxBit = 15; // 基恩士PLC位地址范围为0-15
                if (universalAddress.BitPosition.Value < 0 || universalAddress.BitPosition.Value > maxBit)
                    return AddressValidationResult.Failure(
                        $"基恩士PLC位地址超出范围: {universalAddress.BitPosition.Value} (最大值: {maxBit})");
            }

            return new AddressValidationResult
            {
                IsValid = true,
                Warnings = warnings
            };
        }
    }
}