using System;
using System.Collections.Generic;
using EdgeGateway.Driver.Entity.Attributes;
using EdgeGateway.Driver.Entity.Model;
using EdgeGateway.Driver.Interface;
using EdgeGateway.Driver.Interface.DriverBase;
using HslCommunication.Core.Pipe;
using HslCommunication.LogNet;
using HslCommunication.Profinet.Keyence;

namespace KeyenceNet;

/// <summary>
///     KeyenceNet
/// </summary>
[DriverInfo("KeyenceNet", "v1.0", "基恩士(Keyence Plc)", "PLC", false, true, "基恩士PLC MC协议，用于与基恩士KV系列PLC进行网络通信")]
public class KeyenceNet : PlcProtocolCollector, IDriver
{
  /// <summary>
  ///     日志提供器
  /// </summary>
  private readonly KeyenceNetLogProvider _logProvider;

  /// <summary>
  ///     KeyenceNet
  /// </summary>
  /// <param name="driverInfo"></param>
  public KeyenceNet(DriverInfoDto driverInfo)
  {
    DriverInfo = driverInfo;
    _logProvider = new KeyenceNetLogProvider();

    // 注册基恩士PLC地址解析器
    KeyenceAddressParserRegistration.RegisterKeyenceAddressParser();
  }

  /// <summary>
  ///     KeyenceNet
  /// </summary>
  private KeyenceMcNet _driver;

  /// <summary>
  /// </summary>
  public override dynamic Driver
  {
    get => _driver;
    set => _driver = (KeyenceMcNet)value;
  }

  /// <summary>
  ///     IP地址
  /// </summary>
  [ConfigParameter("IP地址", GroupName = "连接配置", Order = 1)]
  public string IpAddress { get; set; } = "127.0.0.1";

  /// <summary>
  ///     端口
  /// </summary>
  [ConfigParameter("端口", GroupName = "连接配置", Order = 2, Type = "number")]
  public int Port { get; set; } = 8501;

  /// <summary>
  ///     网络编号
  /// </summary>
  [ConfigParameter("网络编号", GroupName = "连接配置", Order = 3, Type = "number")]
  public byte NetworkNumber { get; set; } = 0;

  /// <summary>
  ///     节点编号
  /// </summary>
  [ConfigParameter("节点编号", GroupName = "连接配置", Order = 4, Type = "number")]
  public byte NodeNumber { get; set; } = 0;

  /// <summary>
  ///     心跳检测间隔
  /// </summary>
  [ConfigParameter("心跳检测间隔(毫秒)", GroupName = "连接配置", Order = 5, Type = "number")]
  public int KeepAlive { get; set; } = 3000;

  /// <summary>
  ///     连接
  /// </summary>
  /// <returns></returns>
  public string Connect()
  {
    _driver = new KeyenceMcNet
    {
      IpAddress = IpAddress,
      Port = Port,
      NetworkNumber = NetworkNumber,
      NetworkStationNumber = NodeNumber
    };

    _driver.CommunicationPipe = new PipeTcpNet(IpAddress, Port)
    {
      ConnectTimeOut = Timeout, // 连接超时时间，单位毫秒
      ReceiveTimeOut = Timeout, // 接收设备数据反馈的超时时间
      SleepTime = 0,
      SocketKeepAliveTime = KeepAlive,
      IsPersistentConnection = true
    };

    _driver.LogNet = new LogNetSingle("");
    _driver.LogNet.BeforeSaveToFile += (sender, e) => { OnOutputReceived(e); };
    OperateResult = _driver.ConnectServer();
    IsConnected = OperateResult.IsSuccess;
    return OperateResult.Message;
  }

  /// <summary>
  ///     获取协议更新日志
  /// </summary>
  /// <returns></returns>
  public string GetVersionLogs(LogFormat format = LogFormat.Markdown)
  {
    return _logProvider.GetFormattedVersionLogs(format);
  }

  /// <summary>
  /// 获取协议提示
  /// </summary>
  /// <returns>协议名称提示</returns>
  protected override string? GetProtocolHint()
  {
    return "Keyence";
  }

  /// <summary>
  /// </summary>
  public override void Dispose()
  {
    _driver?.ConnectClose();
    _driver?.Dispose();
  }

  /// <summary>
  ///     基恩士PLC日志提供器
  /// </summary>
  private class KeyenceNetLogProvider : DriverLogBase
  {
    public override List<DriverVersionLog> GetVersionLogs()
    {
      return
      [
          new DriverVersionLog()
                {
                    Version = "1.0.0",
                    ReleaseDate = new DateTime(2025, 6, 22),
                    Description = "基恩士PLC MC协议初始版本",
                    Changes = new List<VersionChange>
                    {
                        new() { Type = ChangeType.Addition, Description = "实现基本的协议通信功能" },
                        new() { Type = ChangeType.Addition, Description = "支持B、MR、LR、CR、VB、DM等地址类型" },
                        new() { Type = ChangeType.Addition, Description = "支持批量读取优化" }
                    }
                }
      ];
    }
  }
}