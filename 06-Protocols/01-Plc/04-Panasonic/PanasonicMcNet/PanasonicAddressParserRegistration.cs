using System;
using System.Collections.Generic;
using System.Linq;
using EdgeGateway.Driver.Interface.DriverBase.AddressParsing;
using EdgeGateway.Driver.Entity.Interface;

namespace PanasonicMcNet;

/// <summary>
/// 松下PLC地址解析器注册类
/// </summary>
public static class PanasonicAddressParserRegistration
{
  /// <summary>
  /// 注册松下PLC地址解析器
  /// </summary>
  public static void RegisterPanasonicAddressParser()
  {
    // 注册到接口层的解析器工厂
    var interfaceFactory = EdgeGateway.Driver.Interface.DriverBase.AddressParsing.AddressParserFactory.Instance;
    interfaceFactory.RegisterParser("panasonic", new PanasonicAddressParser());

    // 注册到实体层的解析器工厂
    var entityFactory = EdgeGateway.Driver.Entity.Services.AddressParserFactory.Instance;
    entityFactory.RegisterParser(new PanasonicAddressParserAdapter());
  }

  /// <summary>
  /// 松下PLC地址解析器适配器（用于实体层）
  /// </summary>
  private class PanasonicAddressParserAdapter : EdgeGateway.Driver.Entity.Parsers.AddressParserBase
  {
    public override string ProtocolName => "Panasonic";
    public override int Priority => 50;
    public override string[] SupportedFormats => new[]
    {
            "X0",       // 输入继电器
            "Y0",       // 输出继电器
            "R0",       // 内部继电器
            "D0",       // 数据寄存器
            "L0",       // 锁存继电器
            "T0",       // 定时器
            "C0",       // 计数器
            "X0.1",     // 位地址
            "Y0.1",     // 位地址
            "R0.1",     // 位地址
            "L0.1"      // 位地址
        };

    public override bool CanParse(string address)
    {
      if (!base.CanParse(address))
        return false;

      var upperAddress = address.ToUpper().Trim();
      return upperAddress.StartsWith("X") ||
             upperAddress.StartsWith("Y") ||
             upperAddress.StartsWith("R") ||
             upperAddress.StartsWith("D") ||
             upperAddress.StartsWith("L") ||
             upperAddress.StartsWith("T") ||
             upperAddress.StartsWith("C");
    }

    public override EdgeGateway.Driver.Entity.Model.UniversalAddress Parse(string address, string dataType = null, int? length = null)
    {
      var result = new EdgeGateway.Driver.Entity.Model.UniversalAddress
      {
        RawAddress = address,
        Protocol = "Panasonic",
        DataType = dataType,
        Length = length
      };

      try
      {
        var upperAddress = address.ToUpper().Trim();

        // 提取区域类型
        char areaChar = upperAddress[0];
        result.AreaType = areaChar.ToString();

        // 检查是否有位地址
        var dotIndex = upperAddress.IndexOf('.');
        if (dotIndex > 0)
        {
          // 解析主地址
          if (decimal.TryParse(upperAddress.Substring(1, dotIndex - 1), out decimal mainAddr))
          {
            result.BaseAddress = (long)mainAddr;
          }
          else
          {
            result.IsValid = false;
            result.ErrorMessage = $"无效的松下PLC主地址: {upperAddress}";
            return result;
          }

          // 解析位地址
          if (int.TryParse(upperAddress.Substring(dotIndex + 1), out int bitPos))
          {
            result.BitPosition = bitPos;
          }
          else
          {
            result.IsValid = false;
            result.ErrorMessage = $"无效的松下PLC位地址: {upperAddress}";
            return result;
          }
        }
        else
        {
          // 只有主地址
          if (decimal.TryParse(upperAddress.Substring(1), out decimal mainAddr))
          {
            result.BaseAddress = (long)mainAddr;
          }
          else
          {
            result.IsValid = false;
            result.ErrorMessage = $"无效的松下PLC地址: {upperAddress}";
            return result;
          }
        }

        result.IsValid = true;
        return result;
      }
      catch (Exception ex)
      {
        result.IsValid = false;
        result.ErrorMessage = $"解析松下PLC地址时发生异常: {ex.Message}";
        return result;
      }
    }

    public override AddressValidationResult Validate(EdgeGateway.Driver.Entity.Model.UniversalAddress universalAddress)
    {
      var baseResult = base.Validate(universalAddress);
      if (!baseResult.IsValid)
        return baseResult;

      var warnings = new List<string>();

      // 检查区域类型
      var validAreaTypes = new[] { "X", "Y", "R", "D", "L", "T", "C" };
      if (!validAreaTypes.Contains(universalAddress.AreaType))
      {
        return AddressValidationResult.Failure($"不支持的松下PLC区域类型: {universalAddress.AreaType}");
      }

      // 检查地址范围
      var maxAddresses = new Dictionary<string, long>
            {
                { "X", 9999 },
                { "Y", 9999 },
                { "R", 9999 },
                { "D", 9999 },
                { "L", 9999 },
                { "T", 255 },
                { "C", 255 }
            };

      if (maxAddresses.TryGetValue(universalAddress.AreaType, out long maxAddr) &&
          universalAddress.BaseAddress > maxAddr)
      {
        return AddressValidationResult.Failure(
            $"松下PLC {universalAddress.AreaType} 区域地址超出范围: {universalAddress.BaseAddress} (最大值: {maxAddr})");
      }

      // 检查位地址范围
      if (universalAddress.BitPosition.HasValue)
      {
        int maxBit = universalAddress.AreaType == "D" || universalAddress.AreaType == "T" || universalAddress.AreaType == "C" ? 15 : 7;
        if (universalAddress.BitPosition.Value < 0 || universalAddress.BitPosition.Value > maxBit)
        {
          return AddressValidationResult.Failure(
              $"松下PLC位地址超出范围: {universalAddress.BitPosition.Value} (最大值: {maxBit})");
        }
      }

      return new AddressValidationResult
      {
        IsValid = true,
        Warnings = warnings
      };
    }
  }
}