using System;
using System.Collections.Generic;
using EdgeGateway.Driver.Entity.Attributes;
using EdgeGateway.Driver.Entity.Model;
using EdgeGateway.Driver.Interface;
using EdgeGateway.Driver.Interface.DriverBase;
using HslCommunication.Core.Pipe;
using HslCommunication.LogNet;
using HslCommunication.Profinet.Panasonic;

namespace PanasonicMcNet;

/// <summary>
///     PanasonicMcNet
/// </summary>
[DriverInfo("PanasonicMcNet", "v1.0", "松下(Panasonic Plc)", "PLC", false, true, "松下PLC MEWTOCOL协议，用于与松下FP系列PLC进行网络通信")]
public class PanasonicMcNet : PlcProtocolCollector, IDriver
{
  /// <summary>
  ///     日志提供器
  /// </summary>
  private readonly PanasonicMcNetLogProvider _logProvider;

  /// <summary>
  ///     PanasonicMcNet
  /// </summary>
  /// <param name="driverInfo"></param>
  public PanasonicMcNet(DriverInfoDto driverInfo)
  {
    DriverInfo = driverInfo;
    _logProvider = new PanasonicMcNetLogProvider();

    // 注册松下PLC地址解析器
    PanasonicAddressParserRegistration.RegisterPanasonicAddressParser();
  }

  /// <summary>
  ///     PanasonicMcNet
  /// </summary>
  private PanasonicMewtocolOverTcp _driver;

  /// <summary>
  /// </summary>
  public override dynamic Driver
  {
    get => _driver;
    set => _driver = (PanasonicMewtocolOverTcp)value;
  }

  /// <summary>
  ///     IP地址
  /// </summary>
  [ConfigParameter("IP地址", GroupName = "连接配置", Order = 1)]
  public string IpAddress { get; set; } = "127.0.0.1";

  /// <summary>
  ///     端口
  /// </summary>
  [ConfigParameter("端口", GroupName = "连接配置", Order = 2, Type = "number")]
  public int Port { get; set; } = 9094;

  /// <summary>
  ///     站号
  /// </summary>
  [ConfigParameter("站号", GroupName = "连接配置", Order = 3, Type = "number")]
  public byte Station { get; set; } = 238;

  /// <summary>
  ///     心跳检测间隔
  /// </summary>
  [ConfigParameter("心跳检测间隔(毫秒)", GroupName = "连接配置", Order = 4, Type = "number")]
  public int KeepAlive { get; set; } = 3000;

  /// <summary>
  ///     连接
  /// </summary>
  /// <returns></returns>
  public string Connect()
  {
    _driver = new PanasonicMewtocolOverTcp
    {
      IpAddress = IpAddress,
      Port = Port,
      Station = Station
    };

    _driver.CommunicationPipe = new PipeTcpNet(IpAddress, Port)
    {
      ConnectTimeOut = Timeout, // 连接超时时间，单位毫秒
      ReceiveTimeOut = Timeout, // 接收设备数据反馈的超时时间
      SleepTime = 0,
      SocketKeepAliveTime = KeepAlive,
      IsPersistentConnection = true
    };

    _driver.LogNet = new LogNetSingle("");
    _driver.LogNet.BeforeSaveToFile += (sender, e) => { OnOutputReceived(e); };
    OperateResult = _driver.ConnectServer();
    IsConnected = OperateResult.IsSuccess;
    return OperateResult.Message;
  }

  /// <summary>
  ///     获取协议更新日志
  /// </summary>
  /// <returns></returns>
  public string GetVersionLogs(LogFormat format = LogFormat.Markdown)
  {
    return _logProvider.GetFormattedVersionLogs(format);
  }

  /// <summary>
  /// 获取协议提示
  /// </summary>
  /// <returns>协议名称提示</returns>
  protected override string? GetProtocolHint()
  {
    return "Panasonic";
  }

  /// <summary>
  /// </summary>
  public override void Dispose()
  {
    _driver?.ConnectClose();
    _driver?.Dispose();
  }

  /// <summary>
  ///     松下PLC日志提供器
  /// </summary>
  private class PanasonicMcNetLogProvider : DriverLogBase
  {
    public override List<DriverVersionLog> GetVersionLogs()
    {
      return
      [
          new DriverVersionLog()
                {
                    Version = "1.0.0",
                    ReleaseDate = new DateTime(2025, 6, 22),
                    Description = "松下PLC MEWTOCOL协议初始版本",
                    Changes = new List<VersionChange>
                    {
                        new() { Type = ChangeType.Addition, Description = "实现基本的协议通信功能" },
                        new() { Type = ChangeType.Addition, Description = "支持X、Y、R、D、L、T、C等地址类型" },
                        new() { Type = ChangeType.Addition, Description = "支持批量读取优化" }
                    }
                }
      ];
    }
  }
}