using System;
using System.Collections.Generic;
using EdgeGateway.Driver.Entity.Services;
using EdgeGateway.Driver.Entity.Model;
using EdgeGateway.Driver.Entity.Interface;
using EdgeGateway.Driver.Interface.DriverBase.AddressParsing;

namespace ModbusUdp;

/// <summary>
/// Modbus UDP协议地址解析器注册类
/// </summary>
public static class ModbusAddressParserRegistration
{
  /// <summary>
  /// 注册Modbus UDP协议地址解析器
  /// </summary>
  public static void RegisterModbusAddressParser()
  {
    // 注册到接口层的解析器工厂
    var interfaceFactory = EdgeGateway.Driver.Interface.DriverBase.AddressParsing.AddressParserFactory.Instance;
    interfaceFactory.RegisterParser("modbus-udp", new ModbusAddressParser());

    // 注册到实体层的解析器工厂
    var entityFactory = EdgeGateway.Driver.Entity.Services.AddressParserFactory.Instance;
    entityFactory.RegisterParser(new ModbusAddressParserAdapter());
  }

  /// <summary>
  /// Modbus UDP协议地址解析器适配器（用于实体层）
  /// </summary>
  private class ModbusAddressParserAdapter : EdgeGateway.Driver.Entity.Parsers.AddressParserBase
  {
    public override string ProtocolName => "ModbusUdp";
    public override int Priority => 50;
    public override string[] SupportedFormats => new[]
    {
            "0",          // 线圈状态
            "100",        // 离散输入状态
            "400001",     // 保持寄存器
            "300001",     // 输入寄存器
            "x=3;1",      // 扩展格式
            "s=1;100"     // 扩展格式
        };

    public override bool CanParse(string address)
    {
      if (!base.CanParse(address))
        return false;

      // Modbus地址通常是纯数字或者特定格式的字符串
      return System.Text.RegularExpressions.Regex.IsMatch(address, @"^\d+$") ||
             address.Contains("=") && address.Contains(";");
    }

    public override EdgeGateway.Driver.Entity.Model.UniversalAddress Parse(string address, string dataType = null, int? length = null)
    {
      var result = new EdgeGateway.Driver.Entity.Model.UniversalAddress
      {
        RawAddress = address,
        Protocol = "ModbusUdp",
        DataType = dataType,
        Length = length
      };

      try
      {
        // 处理扩展格式地址 (例如: x=3;1)
        if (address.Contains("=") && address.Contains(";"))
        {
          var parts = address.Split(';');
          if (parts.Length >= 2)
          {
            var addressType = parts[0].Split('=')[0].Trim().ToLower();
            var addressValue = parts[1];

            switch (addressType)
            {
              case "x":
                result.AreaType = "Coil";
                break;
              case "y":
                result.AreaType = "Input";
                break;
              case "d":
              case "s":
                result.AreaType = "Holding";
                break;
              case "r":
              case "m":
                result.AreaType = "Input Register";
                break;
              default:
                result.AreaType = "Unknown";
                break;
            }

            if (long.TryParse(addressValue, out long addr))
            {
              result.BaseAddress = addr;
              result.IsValid = true;
              return result;
            }
          }
        }
        // 处理标准格式地址
        else if (long.TryParse(address, out long addr))
        {
          // 根据地址范围确定区域类型
          if (addr >= 0 && addr <= 9999)
          {
            result.AreaType = "Coil";
            result.BaseAddress = addr;
          }
          else if (addr >= 10000 && addr <= 19999)
          {
            result.AreaType = "Input";
            result.BaseAddress = addr - 10000;
          }
          else if (addr >= 30001 && addr <= 39999)
          {
            result.AreaType = "Input Register";
            result.BaseAddress = addr - 30001;
          }
          else if (addr >= 40001 && addr <= 49999)
          {
            result.AreaType = "Holding";
            result.BaseAddress = addr - 40001;
          }
          else
          {
            result.AreaType = "Unknown";
            result.BaseAddress = addr;
          }

          result.IsValid = true;
          return result;
        }

        result.IsValid = false;
        result.ErrorMessage = $"无效的Modbus UDP地址格式: {address}";
        return result;
      }
      catch (Exception ex)
      {
        result.IsValid = false;
        result.ErrorMessage = $"解析Modbus UDP地址时发生异常: {ex.Message}";
        return result;
      }
    }

    public override EdgeGateway.Driver.Entity.Interface.AddressValidationResult Validate(EdgeGateway.Driver.Entity.Model.UniversalAddress universalAddress)
    {
      var baseResult = base.Validate(universalAddress);
      if (!baseResult.IsValid)
        return baseResult;

      var warnings = new List<string>();

      // 验证地址范围
      switch (universalAddress.AreaType)
      {
        case "Coil":
          if (universalAddress.BaseAddress < 0 || universalAddress.BaseAddress > 9999)
          {
            return EdgeGateway.Driver.Entity.Interface.AddressValidationResult.Failure(
                $"线圈地址超出范围: {universalAddress.BaseAddress} (有效范围: 0-9999)");
          }
          break;
        case "Input":
          if (universalAddress.BaseAddress < 0 || universalAddress.BaseAddress > 9999)
          {
            return EdgeGateway.Driver.Entity.Interface.AddressValidationResult.Failure(
                $"离散输入地址超出范围: {universalAddress.BaseAddress} (有效范围: 0-9999)");
          }
          break;
        case "Input Register":
          if (universalAddress.BaseAddress < 0 || universalAddress.BaseAddress > 9999)
          {
            return EdgeGateway.Driver.Entity.Interface.AddressValidationResult.Failure(
                $"输入寄存器地址超出范围: {universalAddress.BaseAddress} (有效范围: 0-9999)");
          }
          break;
        case "Holding":
          if (universalAddress.BaseAddress < 0 || universalAddress.BaseAddress > 9999)
          {
            return EdgeGateway.Driver.Entity.Interface.AddressValidationResult.Failure(
                $"保持寄存器地址超出范围: {universalAddress.BaseAddress} (有效范围: 0-9999)");
          }
          break;
      }

      return new EdgeGateway.Driver.Entity.Interface.AddressValidationResult
      {
        IsValid = true,
        Warnings = warnings
      };
    }
  }
}