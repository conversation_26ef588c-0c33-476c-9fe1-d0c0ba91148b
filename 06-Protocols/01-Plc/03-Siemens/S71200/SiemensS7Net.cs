using System;
using System.Collections.Generic;
using System.Diagnostics;
using System.Text;
using System.Threading.Tasks;
using EdgeGateway.Driver.Entity.Attributes;
using EdgeGateway.Driver.Entity.Const;
using EdgeGateway.Driver.Entity.Model;
using EdgeGateway.Driver.Interface;
using EdgeGateway.Driver.Interface.DriverBase;
using HslCommunication;
using HslCommunication.Core.Pipe;
using HslCommunication.LogNet;
using HslCommunication.Profinet.Siemens;

namespace S71200;

/// <summary>
///     西门子S7-1200协议驱动
/// </summary>
[DriverInfo("SiemensS7Net", "v1.0", "西门子(Siemens)", "PLC", false, true, "西门子S7-1200 PLC的以太网通信协议，支持基础连接与数据读写")]
public class SiemensS7Net : SiemensPlcNetBase, IDriver
{
    /// <summary>
    ///     日志提供器
    /// </summary>
    private readonly SiemensS7NetLogProvider _logProvider;

    /// <summary>
    ///     构造函数
    /// </summary>
    /// <param name="driverInfo"></param>
    public SiemensS7Net(DriverInfoDto driverInfo)
    {
        DriverInfo = driverInfo;
        _logProvider = new SiemensS7NetLogProvider();

        // 注册西门子PLC地址解析器
        SiemensAddressParserRegistration.RegisterSiemensAddressParser();
    }

    /// <summary>
    ///     协议实例
    /// </summary>
    private HslCommunication.Profinet.Siemens.SiemensS7Net _driver;

    /// <summary>
    ///     协议实例属性
    /// </summary>
    public override dynamic Driver
    {
        get => _driver;
        set => _driver = (HslCommunication.Profinet.Siemens.SiemensS7Net)value;
    }

    /// <summary>
    ///     连接
    /// </summary>
    /// <returns></returns>
    public string Connect()
    {
        _driver = new HslCommunication.Profinet.Siemens.SiemensS7Net(SiemensPLCS.S1200);
        _driver.Rack = Rack;
        _driver.Slot = Slot;
        _driver.CommunicationPipe = new PipeTcpNet(IpAddress, Port)
        {
            ConnectTimeOut = Timeout,
            ReceiveTimeOut = Timeout,
            SleepTime = 0,
            SocketKeepAliveTime = -1,
            IsPersistentConnection = true
        };
        _driver.LogNet = new LogNetSingle("");
        _driver.LogNet.BeforeSaveToFile += (sender, e) => { OnOutputReceived(e); };
        OperateResult = _driver.ConnectServer();
        IsConnected = OperateResult.IsSuccess;
        return OperateResult.Message;
    }

    /// <summary>
    ///     读取地址（重写，支持批量读取）
    /// </summary>
    /// <param name="paramList">待读取的地址参数列表</param>
    /// <returns>读取结果列表</returns>
    public override async Task<List<ReadDataResult>> Read(List<DriverReadInput> paramList)
    {
        // 调用批量读取方法，统一入口
        return await ReadBatchAsync(paramList);
    }

    /// <summary>
    ///     批量读取实现，支持复杂地址格式
    /// </summary>
    /// <param name="paramList">待批量读取的地址参数列表</param>
    /// <returns>批量读取结果列表</returns>
    public override async Task<List<ReadDataResult>> ReadBatchAsync(List<DriverReadInput> paramList)
    {
        // 开始计时 - 总方法执行时间
        var totalStopwatch = new Stopwatch();
        totalStopwatch.Start();

        // 结果输出列表
        var output = new List<ReadDataResult>();
        if (paramList == null || paramList.Count == 0) return output;

        // 构建地址和长度数组，供底层批量读取接口使用
        var addressList = new List<string>();
        var lengthList = new List<ushort>();

        foreach (var param in paramList)
        {
            addressList.Add(param.Address);
            ushort len = param.DataType switch
            {
                "int32" or "uint32" or "float" => 4,
                "double" or "long" or "ulong" => 8,
                "int16" or "uint16" or "ushort" or "short" => 2,
                "bool" or "byte" => 1,
                "string" => param.Length ?? 10,
                _ => 2
            };
            lengthList.Add(len);
        }

        // 调用HslCommunication批量读取接口
        OnOutputReceived($"【批量读取】开始执行批量读取，地址数量: {addressList.Count}");

        // 开始计时 - 读取操作
        var readStopwatch = new Stopwatch();
        readStopwatch.Start();

        // 执行批量读取
        var read = await _driver.ReadAsync(addressList.ToArray(), lengthList.ToArray());

        // 停止计时 - 读取操作
        readStopwatch.Stop();
        OnOutputReceived($"【性能监控】读取操作耗时: {readStopwatch.ElapsedMilliseconds}ms");

        // 开始计时 - 数据匹配/解析
        var parseStopwatch = new Stopwatch();
        parseStopwatch.Start();

        if (!read.IsSuccess)
        {
            // 读取失败，所有地址均标记为失败
            foreach (var param in paramList)
            {
                var result = new ReadDataResult
                {
                    Id = param.Id,
                    Status = VariableStatus.VariableStatusBad,
                    ErrMsg = read.Message,
                    DataType = param.DataType
                };
                output.Add(result);
            }
        }
        else
        {
            // 读取成功，需按顺序解析Content字节数组
            var offset = 0; // 当前字节偏移量
            for (var i = 0; i < paramList.Count; i++)
            {
                var param = paramList[i];
                var len = lengthList[i];

                try
                {
                    // 根据DataType类型进行字节转换
                    object value;

                    // 特殊处理布尔类型
                    if (param.DataType == "bool")
                    {
                        // 解析地址格式
                        var addressParts = param.Address.Split('.');

                        // 对于DB2.210.0这种三段式地址（包含位索引）
                        if (addressParts.Length >= 3 && int.TryParse(addressParts[addressParts.Length - 1], out var bitIndex))
                        {
                            // 读取字节值
                            var byteValue = read.Content[offset];

                            // 检查指定位是否为1 (从右到左，第0位是最右边的位)
                            value = ((byteValue >> bitIndex) & 0x01) == 0x01;

                            OnOutputReceived($"【解析数据】位地址解析: 地址={param.Address}, 字节值={byteValue:X2}, 位索引={bitIndex}, 结果={value}");
                        }
                        // 对于DB2.202这种两段式地址（整个字节作为布尔值）
                        else
                        {
                            // 读取字节值
                            var byteValue = read.Content[offset];

                            // 对于两段式地址，我们检查第0位（最低位）
                            value = (byteValue & 0x01) == 0x01;

                            OnOutputReceived($"【解析数据】字节地址作为布尔值: 地址={param.Address}, 字节值={byteValue:X2}, 结果={value}");
                        }
                    }
                    else
                    {
                        // 其他数据类型的处理保持不变
                        value = param.DataType switch
                        {
                            "int32" => _driver.ByteTransform.TransInt32(read.Content, offset),
                            "uint32" => _driver.ByteTransform.TransUInt32(read.Content, offset),
                            "float" => _driver.ByteTransform.TransSingle(read.Content, offset),
                            "double" => _driver.ByteTransform.TransDouble(read.Content, offset),
                            "long" => _driver.ByteTransform.TransInt64(read.Content, offset),
                            "ulong" => _driver.ByteTransform.TransUInt64(read.Content, offset),
                            "int16" or "short" => _driver.ByteTransform.TransInt16(read.Content, offset),
                            "uint16" or "ushort" => _driver.ByteTransform.TransUInt16(read.Content, offset),
                            "byte" => read.Content[offset],
                            "string" => Encoding.ASCII.GetString(read.Content, offset, len),
                            _ => _driver.ByteTransform.TransInt16(read.Content, offset)
                        };
                    }

                    var result = new ReadDataResult
                    {
                        Id = param.Id,
                        Status = VariableStatus.VariableStatusGood,
                        ErrMsg = read.Message,
                        DataType = param.DataType,
                        Value = value
                    };

                    output.Add(result);
                }
                catch (Exception ex)
                {
                    var result = new ReadDataResult
                    {
                        Id = param.Id,
                        Status = VariableStatus.VariableStatusBad,
                        ErrMsg = $"解析异常: {ex.Message}",
                        DataType = param.DataType,
                        Value = null
                    };

                    output.Add(result);
                }

                offset += len;
            }
        }

        // 停止计时 - 数据匹配/解析
        parseStopwatch.Stop();

        // 停止计时 - 总方法执行时间
        totalStopwatch.Stop();

        return output;
    }

    /// <summary>
    ///     获取协议更新日志
    /// </summary>
    /// <returns></returns>
    public string GetVersionLogs(LogFormat format = LogFormat.Markdown)
    {
        return _logProvider.GetFormattedVersionLogs(format);
    }

    /// <summary>
    ///     释放资源
    /// </summary>
    public override void Dispose()
    {
        _driver?.ConnectClose();
        _driver?.Dispose();
    }

    /// <summary>
    ///     SiemensS7Net日志提供器
    /// </summary>
    private class SiemensS7NetLogProvider : DriverLogBase
    {
        public override List<DriverVersionLog> GetVersionLogs()
        {
            return new List<DriverVersionLog>
            {
                new()
                {
                    Version = "1.0.0",
                    ReleaseDate = new DateTime(2025, 3, 20),
                    Description = "SiemensS7Net协议初始版本",
                    Changes = new List<VersionChange>
                    {
                        new() { Type = ChangeType.Addition, Description = "实现基本的西门子S7-1200协议通信功能" }
                    }
                }
            };
        }
    }
}