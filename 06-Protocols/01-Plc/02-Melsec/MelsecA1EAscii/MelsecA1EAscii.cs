using System;
using System.Collections.Generic;
using EdgeGateway.Driver.Entity.Attributes;
using EdgeGateway.Driver.Entity.Model;
using EdgeGateway.Driver.Interface;
using EdgeGateway.Driver.Interface.DriverBase;
using HslCommunication.Core.Pipe;
using HslCommunication.LogNet;
using HslCommunication.Profinet.Melsec;

namespace MelsecA1EAscii;

/// <summary>
///     MelsecA1EAscii
/// </summary>
[DriverInfo("MelsecA1EAscii", "v1.0", "三菱(Melsec Plc)", "PLC", false, true, "三菱PLC A系列ASCII通信协议，使用ASCII码传输数据")]
public class MelsecA1EAscii : MelsecNetBase, IDriver
{
    /// <summary>
    ///     日志提供器
    /// </summary>
    private readonly MelsecA1ELogProvider _logProvider;

    /// <summary>
    ///     melsecA1EAscii
    /// </summary>
    /// <param name="driverInfo"></param>
    public MelsecA1EAscii(DriverInfoDto driverInfo)
    {
        DriverInfo = driverInfo;
        _logProvider = new MelsecA1ELogProvider();
    }

    /// <summary>
    ///     MelsecA1EAscii
    /// </summary>
    private MelsecA1EAsciiNet _driver;

    /// <summary>
    /// </summary>
    public override dynamic Driver
    {
        get => _driver;
        set => _driver = (MelsecA1EAsciiNet)value;
    }

    /// <summary>
    ///     连接
    /// </summary>
    /// <returns></returns>
    public string Connect()
    {
        _driver = new MelsecA1EAsciiNet();
        _driver.CommunicationPipe = new PipeTcpNet(IpAddress, Port)
        {
            ConnectTimeOut = Timeout, // 连接超时时间，单位毫秒
            ReceiveTimeOut = Timeout, // 接收设备数据反馈的超时时间
            SleepTime = 0,
            SocketKeepAliveTime = KeepAlive,
            IsPersistentConnection = true
        };

        _driver.LogNet = new LogNetSingle("");
        _driver.LogNet.BeforeSaveToFile += (sender, e) => { OnOutputReceived(e.ToString()); };
        OperateResult = _driver.ConnectServer();
        IsConnected = OperateResult.IsSuccess;
        return OperateResult.Message;
    }

    /// <summary>
    ///     获取协议更新日志
    /// </summary>
    /// <returns></returns>
    public string GetVersionLogs(LogFormat format = LogFormat.Markdown)
    {
        return _logProvider.GetFormattedVersionLogs(format);
    }

    /// <summary>
    /// </summary>
    public override void Dispose()
    {
        _driver?.ConnectClose();
        _driver?.Dispose();
    }

    /// <summary>
    ///     melsecA1EAscii日志提供器
    /// </summary>
    private class MelsecA1ELogProvider : DriverLogBase
    {
        public override List<DriverVersionLog> GetVersionLogs()
        {
            return
            [
                new DriverVersionLog
                {
                    Version = "1.0.0",
                    ReleaseDate = new DateTime(2025, 3, 01),
                    Description = " Melsec A-1E（Ascii） 协议初始版本",
                    Changes = [new VersionChange { Type = ChangeType.Addition, Description = "实现基本的协议通信功能" }]
                }
                // // v1.1.0 版本日志
                // new DriverVersionLog
                // {
                //     Version = "1.1.0",
                //     ReleaseDate = new DateTime(2025, 3, 10),
                //     Description = "欧姆龙FinsNet协议更新版本",
                //     Changes = new List<VersionChange>
                //     {
                //         new VersionChange { Type = ChangeType.Addition, Description = "支持批量读取" },
                //     }
                // }
            ];
        }
    }
}