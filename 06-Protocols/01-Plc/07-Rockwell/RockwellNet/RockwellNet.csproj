<Project Sdk="Microsoft.NET.Sdk">

    <PropertyGroup>
        <TargetFramework>net9.0</TargetFramework>
        <GenerateAssemblyInfo>false</GenerateAssemblyInfo>
        <RootNamespace>RockwellNet</RootNamespace>
    </PropertyGroup>

    <PropertyGroup Condition="'$(Configuration)|$(Platform)'=='Release|AnyCPU'">
        <DebugType>none</DebugType>
    </PropertyGroup>

    <ItemGroup>
        <ProjectReference Include="..\..\..\EdgeGateway.Driver.Interface\EdgeGateway.Driver.Interface.csproj"/>
    </ItemGroup>

    <ItemGroup>

    </ItemGroup>

</Project>
