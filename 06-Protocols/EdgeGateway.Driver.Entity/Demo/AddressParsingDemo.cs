using EdgeGateway.Driver.Entity.Services;
using EdgeGateway.Driver.Entity.Model;
using System.Text.Json;

namespace EdgeGateway.Driver.Entity.Demo;

/// <summary>
/// 地址解析框架演示程序
/// 展示统一地址解析框架的功能和性能
/// </summary>
public class AddressParsingDemo
{
  /// <summary>
  /// 运行演示
  /// </summary>
  public static void RunDemo()
  {
    Console.WriteLine("=== 统一地址解析框架演示 ===\n");

    // 1. 演示各协议地址解析
    DemonstrateProtocolParsing();

    // 2. 演示批量解析优化
    DemonstrateBatchOptimization();

    // 3. 演示地址验证
    DemonstrateAddressValidation();

    // 4. 演示缓存统计
    DemonstrateCacheStatistics();

    Console.WriteLine("\n=== 演示完成 ===");
  }

  /// <summary>
  /// 演示各协议地址解析
  /// </summary>
  private static void DemonstrateProtocolParsing()
  {
    Console.WriteLine("1. 各协议地址解析演示：");
    Console.WriteLine("─────────────────────────────────────");

    var factory = AddressParserFactory.Instance;

    // 测试地址样例
    var testAddresses = new[]
    {
            // Modbus地址
            ("100", "Modbus", "int16"),
            ("D100", "Modbus", "int16"),
            ("M100", "Modbus", "boolean"),
            ("40001", "Modbus", "int16"),
            ("D100.5", "Modbus", "boolean"),

            // 西门子S7地址
            ("DB1.DBW100", "Siemens", "int16"),
            ("M100.5", "Siemens", "boolean"),
            ("I0.0", "Siemens", "boolean"),
            ("DB1.DBD100", "Siemens", "int32"),
            ("V100", "Siemens", "int16"),

            // 三菱地址
            ("D100", "Mitsubishi", "int16"),
            ("M100", "Mitsubishi", "boolean"),
            ("X0", "Mitsubishi", "boolean"),    // 八进制
            ("Y7", "Mitsubishi", "boolean"),    // 八进制
            ("TS100", "Mitsubishi", "int16"),

            // 欧姆龙地址
            ("DM100", "Omron", "int16"),
            ("CIO100", "Omron", "int16"),
            ("DM100.11", "Omron", "boolean"),
            ("EM0.100", "Omron", "int16"),
            ("C100", "Omron", "int16"),
        };

    foreach (var (address, protocol, dataType) in testAddresses)
    {
      var result = factory.Parse(address, protocol, dataType);

      Console.WriteLine($"地址: {address,12} | 协议: {protocol,10} | 解析结果: {(result.IsValid ? "✓" : "✗")}");

      if (result.IsValid)
      {
        Console.WriteLine($"    区域: {result.AreaType,8} | 基址: {result.BaseAddress,6} | 位: {result.BitPosition?.ToString() ?? "N/A",3} | 数据类型: {result.DataType}");

        if (result.Protocol == "Mitsubishi" && result.NumberSystem == NumberSystem.Octal)
        {
          Console.WriteLine($"    八进制地址 - 原始: {address} → 十进制: {result.BaseAddress}");
        }
      }
      else
      {
        Console.WriteLine($"    错误: {result.ErrorMessage}");
      }

      Console.WriteLine();
    }
  }

  /// <summary>
  /// 演示批量解析优化
  /// </summary>
  private static void DemonstrateBatchOptimization()
  {
    Console.WriteLine("2. 批量解析优化演示：");
    Console.WriteLine("─────────────────────────────────────");

    var factory = AddressParserFactory.Instance;

    // 模拟不同协议的地址混合批次
    var mixedAddresses = new List<(string Address, string DataType, int? Length)>
        {
            // Modbus连续地址
            ("D100", "int16", null),
            ("D101", "int16", null),
            ("D102", "int16", null),
            ("D105", "int16", null),

            // S7 DB块地址
            ("DB1.DBW100", "int16", null),
            ("DB1.DBW102", "int16", null),
            ("DB1.DBW104", "int16", null),

            // 三菱地址
            ("D200", "int16", null),
            ("D201", "int16", null),
            ("M100", "boolean", null),
            ("M101", "boolean", null),

            // 欧姆龙地址
            ("DM300", "int16", null),
            ("DM301", "int16", null),
            ("CIO100", "int16", null),
        };

    Console.WriteLine($"原始地址数量: {mixedAddresses.Count}");

    // 批量解析
    var parsedAddresses = factory.ParseBatch(mixedAddresses);
    Console.WriteLine($"解析成功: {parsedAddresses.Count(a => a.IsValid)} 个");

    // 批量优化
    var optimizedGroups = factory.OptimizeForBatch(parsedAddresses.Where(a => a.IsValid).ToList());
    Console.WriteLine($"优化后分组数: {optimizedGroups.Count}");

    Console.WriteLine("\n优化分组详情:");
    for (int i = 0; i < optimizedGroups.Count; i++)
    {
      var group = optimizedGroups[i];
      var protocol = group.First().Protocol;
      var areaType = group.First().AreaType;
      var addressRange = $"{group.Min(a => a.BaseAddress)}-{group.Max(a => a.BaseAddress)}";

      Console.WriteLine($"  组 {i + 1}: {protocol} {areaType} [{addressRange}] ({group.Count} 个地址)");

      // 显示该协议的批量限制
      var limits = factory.GetBatchLimits(protocol);
      Console.WriteLine($"       协议限制: 最大{limits.MaxReadLength}字节, 间隔{limits.MaxAddressGap}");
    }

    Console.WriteLine();
  }

  /// <summary>
  /// 演示地址验证
  /// </summary>
  private static void DemonstrateAddressValidation()
  {
    Console.WriteLine("3. 地址验证演示：");
    Console.WriteLine("─────────────────────────────────────");

    var factory = AddressParserFactory.Instance;

    // 测试各种边界情况
    var validationTests = new[]
    {
            ("D100", "Modbus"),         // 正常地址
            ("D70000", "Modbus"),       // 超出范围
            ("DB1.DBW100", "Siemens"),  // 正常S7地址
            ("DB0.DBW100", "Siemens"),  // DB号为0（无效）
            ("M100.8", "Siemens"),      // 位位置超出范围
            ("X77", "Mitsubishi"),      // 八进制地址（正常）
            ("X88", "Mitsubishi"),      // 八进制地址（无效）
            ("DM32768", "Omron"),       // 超出范围
        };

    foreach (var (address, protocol) in validationTests)
    {
      var parsedAddress = factory.Parse(address, protocol);

      if (parsedAddress.IsValid)
      {
        var validation = factory.Validate(parsedAddress);
        Console.WriteLine($"地址: {address,12} | 协议: {protocol,10} | 验证: {(validation.IsValid ? "✓" : "✗")}");

        if (!validation.IsValid)
        {
          Console.WriteLine($"    错误: {validation.ErrorMessage}");
        }

        if (validation.Warnings.Any())
        {
          Console.WriteLine($"    警告: {string.Join(", ", validation.Warnings)}");
        }
      }
      else
      {
        Console.WriteLine($"地址: {address,12} | 协议: {protocol,10} | 解析失败: {parsedAddress.ErrorMessage}");
      }
    }

    Console.WriteLine();
  }

  /// <summary>
  /// 演示缓存统计
  /// </summary>
  private static void DemonstrateCacheStatistics()
  {
    Console.WriteLine("4. 缓存统计演示：");
    Console.WriteLine("─────────────────────────────────────");

    var factory = AddressParserFactory.Instance;

    // 进行一些解析操作以填充缓存
    for (int i = 0; i < 50; i++)
    {
      factory.Parse($"D{i}", "Modbus", "int16");
      factory.Parse($"M{i}", "Siemens", "boolean");
    }

    var stats = factory.GetCacheStatistics();
    Console.WriteLine($"缓存条目数: {stats.TotalEntries}");
    Console.WriteLine($"最大容量: {stats.MaxEntries}");
    Console.WriteLine($"使用率: {stats.UsagePercentage:F1}%");
    Console.WriteLine($"过期时间: {stats.ExpiryMinutes} 分钟");

    // 演示缓存性能
    var testAddress = "D123";
    var stopwatch = System.Diagnostics.Stopwatch.StartNew();

    // 第一次解析（缓存未命中）
    var result1 = factory.Parse(testAddress, "Modbus");
    var firstParseTime = stopwatch.ElapsedTicks;

    stopwatch.Restart();

    // 第二次解析（缓存命中）
    var result2 = factory.Parse(testAddress, "Modbus");
    var secondParseTime = stopwatch.ElapsedTicks;

    Console.WriteLine($"\n缓存性能测试 (地址: {testAddress}):");
    Console.WriteLine($"首次解析: {firstParseTime} ticks");
    Console.WriteLine($"缓存命中: {secondParseTime} ticks");
    Console.WriteLine($"性能提升: {(double)firstParseTime / secondParseTime:F1}x");

    Console.WriteLine();
  }

  /// <summary>
  /// 演示协议特定功能
  /// </summary>
  public static void DemonstrateProtocolSpecific()
  {
    Console.WriteLine("5. 协议特定功能演示：");
    Console.WriteLine("─────────────────────────────────────");

    var factory = AddressParserFactory.Instance;

    // 获取所有注册的解析器
    var parsers = factory.GetAllParsers();
    Console.WriteLine($"已注册解析器数量: {parsers.Count}");

    foreach (var parser in parsers)
    {
      Console.WriteLine($"\n协议: {parser.ProtocolName} (优先级: {parser.Priority})");
      Console.WriteLine($"支持格式: {string.Join(", ", parser.SupportedFormats.Take(3))}...");

      var limits = parser.GetBatchLimits();
      Console.WriteLine($"批量限制: 最大{limits.MaxReadLength}字节, 最大间隔{limits.MaxAddressGap}");
      Console.WriteLine($"描述: {limits.Description}");
    }
  }
}

/// <summary>
/// 性能测试工具
/// </summary>
public class PerformanceTest
{
  /// <summary>
  /// 运行地址解析性能测试
  /// </summary>
  public static void RunPerformanceTest()
  {
    Console.WriteLine("=== 地址解析性能测试 ===\n");

    var factory = AddressParserFactory.Instance;
    var testCount = 10000;

    // 准备测试数据
    var testAddresses = new List<(string, string)>();
    for (int i = 0; i < testCount; i++)
    {
      testAddresses.Add(($"D{i % 1000}", "Modbus"));
      testAddresses.Add(($"DB1.DBW{i % 500}", "Siemens"));
      testAddresses.Add(($"M{i % 100}", "Mitsubishi"));
      testAddresses.Add(($"DM{i % 200}", "Omron"));
    }

    Console.WriteLine($"测试地址数量: {testAddresses.Count * 4}");

    // 测试单个解析性能
    var stopwatch = System.Diagnostics.Stopwatch.StartNew();

    foreach (var (address, protocol) in testAddresses)
    {
      factory.Parse(address, protocol);
    }

    stopwatch.Stop();

    Console.WriteLine($"单个解析总时间: {stopwatch.ElapsedMilliseconds} ms");
    Console.WriteLine($"平均解析时间: {(double)stopwatch.ElapsedMilliseconds / testAddresses.Count:F3} ms/地址");

    // 测试批量解析性能
    var batchAddresses = testAddresses.Select(t => (t.Item1, "int16", (int?)null)).ToList();

    stopwatch.Restart();
    var results = factory.ParseBatch(batchAddresses);
    stopwatch.Stop();

    Console.WriteLine($"批量解析总时间: {stopwatch.ElapsedMilliseconds} ms");
    Console.WriteLine($"成功解析数量: {results.Count(r => r.IsValid)}");

    // 测试缓存效果
    stopwatch.Restart();

    // 重复解析相同地址（应该命中缓存）
    foreach (var (address, protocol) in testAddresses.Take(1000))
    {
      factory.Parse(address, protocol);
    }

    stopwatch.Stop();

    Console.WriteLine($"缓存命中解析时间: {stopwatch.ElapsedMilliseconds} ms");
    Console.WriteLine($"缓存效果提升: {((double)stopwatch.ElapsedMilliseconds / testAddresses.Count):F3} ms/地址");

    Console.WriteLine("\n=== 性能测试完成 ===");
  }
}