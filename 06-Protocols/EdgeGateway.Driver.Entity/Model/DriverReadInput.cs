namespace EdgeGateway.Driver.Entity.Model;

/// <summary>
/// </summary>
public class DriverReadInput
{
    /// <summary>
    /// </summary>
    /// <param name="identifier">标识</param>
    /// <param name="address">地址</param>
    /// <param name="length">长度</param>
    /// <param name="dataType">数据类型</param>
    /// <param name="method">方法</param>
    /// <param name="encoding">字符编码</param>
    public DriverReadInput(string identifier, string? address = null, string? dataType = null, string? method = null, ushort? length = null, string? encoding = "utf8")
    {
        Address = address;
        Length = length;
        DataType = dataType;
        Encoding = encoding;
        Id = identifier;
        Method = method;
    }

    /// <summary>
    ///     方法
    /// </summary>
    public string? Method { get; set; }

    /// <summary>
    ///   标识
    /// </summary>
    public string Id { get; set; }

    /// <summary>
    ///     地址
    /// </summary>
    public string? Address { get; set; }

    /// <summary>
    ///     长度
    /// </summary>
    public ushort? Length { get; set; }

    /// <summary>
    ///     数据类型
    /// </summary>
    public string? DataType { get; set; }

    /// <summary>
    ///     字符串编码
    /// </summary>
    public string? Encoding { get; set; }
}

/// <summary>
///     写入数据
/// </summary>
public class DriverWriteInput
{
    /// <summary>
    ///     写入数据
    /// </summary>
    /// <param name="address"> 地址 </param>
    /// <param name="value"> 值 </param>
    /// <param name="dataType"> 数据类型 </param>
    /// <param name="length"> 长度 </param>
    /// <param name="encoding"> 字符串编码 </param>
    /// <param name="identifier"> 标识 </param>
    public DriverWriteInput(string? identifier, string address, string value, string? dataType = null, int? length = null, string? encoding = "utf8")
    {
        Address = address;
        Value = value;
        Length = length ?? 0;
        DataType = dataType;
        Encoding = encoding;
        Id = identifier ?? string.Empty;
    }
    /// <summary>
    ///     标识
    /// </summary>
    public string Id { get; set; }

    /// <summary>
    ///     地址
    /// </summary>
    public string Address { get; set; }

    /// <summary>
    ///     值
    /// </summary>
    public string Value { get; set; }

    /// <summary>
    ///     长度
    /// </summary>
    public int Length { get; set; }

    /// <summary>
    ///     数据类型
    /// </summary>
    public string? DataType { get; set; }

    /// <summary>
    ///     字符串编码
    /// </summary>
    public string? Encoding { get; set; }
}
