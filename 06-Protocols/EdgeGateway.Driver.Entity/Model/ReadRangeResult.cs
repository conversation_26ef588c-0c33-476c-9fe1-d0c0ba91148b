namespace EdgeGateway.Driver.Entity.Model;

/// <summary>
/// 读取范围结果
/// </summary>
public class ReadRangeResult
{
  /// <summary>
  /// 是否成功
  /// </summary>
  public bool Success { get; set; }

  /// <summary>
  /// 错误信息
  /// </summary>
  public string ErrorMessage { get; set; }

  /// <summary>
  /// 读取的数据
  /// </summary>
  public byte[] Data { get; set; }

  /// <summary>
  /// 执行时间（毫秒）
  /// </summary>
  public long ExecutionTime { get; set; }
}