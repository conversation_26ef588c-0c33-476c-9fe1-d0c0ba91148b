namespace EdgeGateway.Driver.Entity.Model;

/// <summary>
/// 地址解析结果
/// </summary>
public class AddressParseResult
{
  /// <summary>
  /// 原始地址字符串
  /// </summary>
  public string RawAddress { get; set; }

  /// <summary>
  /// 地址前缀
  /// </summary>
  public string Prefix { get; set; }

  /// <summary>
  /// 地址数字部分
  /// </summary>
  public decimal Number { get; set; }

  /// <summary>
  /// 位地址（若有）
  /// </summary>
  public int BitPosition { get; set; } = -1;

  /// <summary>
  /// DB块号（Siemens）
  /// </summary>
  public int DbBlock { get; set; } = -1;

  /// <summary>
  /// 地址类型（如字、位、双字等）
  /// </summary>
  public string AddressType { get; set; }

  /// <summary>
  /// 数据类型
  /// </summary>
  public string DataType { get; set; }

  /// <summary>
  /// 字符串长度（如适用）
  /// </summary>
  public int? Length { get; set; }

  /// <summary>
  /// 地址区域（如D、M、X等）
  /// </summary>
  public string Area { get; set; }

  /// <summary>
  /// 解析是否成功
  /// </summary>
  public bool IsValid { get; set; }

  /// <summary>
  /// 错误信息（解析失败时）
  /// </summary>
  public string ErrorMessage { get; set; }

  /// <summary>
  /// 协议特定字段，用于存储协议特定的解析结果
  /// </summary>
  public Dictionary<string, object> ProtocolSpecificData { get; set; } = new Dictionary<string, object>();

  /// <summary>
  /// 原始地址（用于兼容旧代码）
  /// </summary>
  public string OriginalAddress
  {
    get => RawAddress;
    set => RawAddress = value;
  }
}