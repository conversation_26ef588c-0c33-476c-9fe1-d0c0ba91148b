namespace EdgeGateway.Driver.Entity.Model;

/// <summary>
/// 读取统计信息 - 用于自适应调整和性能监控
/// </summary>
public class ReadStatistics
{
  /// <summary>
  /// 地址前缀（如'D'、'M'等）
  /// </summary>
  public string AddressPrefix { get; set; }

  /// <summary>
  /// 总读取尝试次数
  /// </summary>
  public int TotalAttempts { get; set; }

  /// <summary>
  /// 成功的读取次数
  /// </summary>
  public int SuccessfulAttempts { get; set; }

  /// <summary>
  /// 失败的读取次数
  /// </summary>
  public int FailedAttempts { get; set; }

  /// <summary>
  /// 总读取点数
  /// </summary>
  public int TotalPointsRead { get; set; }

  /// <summary>
  /// 总执行时间（毫秒）
  /// </summary>
  public long TotalExecutionTime { get; set; }

  /// <summary>
  /// 最近一次分块大小
  /// </summary>
  public int LastChunkSize { get; set; }

  /// <summary>
  /// 成功率
  /// </summary>
  public double SuccessRate => TotalAttempts > 0 ? (double)SuccessfulAttempts / TotalAttempts : 0;

  /// <summary>
  /// 平均执行时间（毫秒/次）
  /// </summary>
  public double AverageExecutionTime => TotalAttempts > 0 ? (double)TotalExecutionTime / TotalAttempts : 0;

  /// <summary>
  /// 每点平均时间（毫秒/点）
  /// </summary>
  public double AverageTimePerPoint => TotalPointsRead > 0 ? (double)TotalExecutionTime / TotalPointsRead : 0;

  /// <summary>
  /// 最近更新时间
  /// </summary>
  public DateTime LastUpdated { get; set; } = DateTime.Now;

  /// <summary>
  /// 重置统计信息
  /// </summary>
  public void Reset()
  {
    TotalAttempts = 0;
    SuccessfulAttempts = 0;
    FailedAttempts = 0;
    TotalPointsRead = 0;
    TotalExecutionTime = 0;
    LastUpdated = DateTime.Now;
  }

  /// <summary>
  /// 更新统计信息
  /// </summary>
  /// <param name="success">是否成功</param>
  /// <param name="executionTime">执行时间</param>
  /// <param name="pointCount">读取点数</param>
  /// <param name="chunkSize">分块大小</param>
  public void Update(bool success, long executionTime, int pointCount, int chunkSize)
  {
    TotalAttempts++;
    if (success)
      SuccessfulAttempts++;
    else
      FailedAttempts++;

    TotalPointsRead += pointCount;
    TotalExecutionTime += executionTime;
    LastChunkSize = chunkSize;
    LastUpdated = DateTime.Now;
  }
}