using EdgeGateway.Driver.Entity.Enums;

namespace EdgeGateway.Driver.Entity.Model;

/// <summary>
///     反射给协议加载的实体对象
/// </summary>
public class DriverInfoDto
{
    /// <summary>
    ///     设备Id
    /// </summary>
    public long DeviceId { get; set; }

    /// <summary>
    ///     设备名称
    /// </summary>
    public string Identifier { get; set; }
    
    /// <summary>
    ///     日志等级
    /// </summary>
    public DeviceLogLevelEnum LogLevel { get; set; } = DeviceLogLevelEnum.Info;
}

/// <summary>
/// 设备变量
/// </summary>
public class DriverLabel
{
    public DriverLabel(string name, string identifier, string transitionType, ushort? length = null, string? description = null, Dictionary<string, object>? custom = null)
    {
        Name = name;
        Identifier = identifier;
        TransitionType = transitionType;
        Length = length;
        Custom = custom;
        Description = description;
    }

    /// <summary>
    ///     方法名称
    /// </summary>
    public string Name { get; set; }

    /// <summary>
    ///     唯一标识
    /// </summary>
    public string Identifier { get; set; }

    /// <summary>
    ///     数据类型
    /// </summary>
    public string TransitionType { get; set; }

    /// <summary>
    ///     描述
    /// </summary>
    public string? Description { get; set; }
    
    /// <summary>
    ///     自定义映射规则
    /// </summary>
    public Dictionary<string, object>? Custom { get; set; }
    
    /// <summary>
    ///     长度
    /// </summary>
    public ushort? Length { get; set; }
}