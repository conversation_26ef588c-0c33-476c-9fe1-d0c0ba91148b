using System.ComponentModel;
using System.ComponentModel.DataAnnotations;

namespace EdgeGateway.Driver.Entity.Enums;

/// <summary>
/// OPC UA用户身份验证类型枚举
/// </summary>
public enum OpcUaUserIdentityTypeEnum
{
    /// <summary>
    /// 匿名身份验证
    /// </summary>
    [Description("匿名身份验证")]
    [Display(Name = "匿名身份验证")]
    Anonymous = 1,

    /// <summary>
    /// 用户名密码身份验证
    /// </summary>
    [Description("用户名密码身份验证")]
    [Display(Name = "用户名密码身份验证")]
    UserName = 2,

    /// <summary>
    /// 证书身份验证
    /// </summary>
    [Description("证书身份验证")]
    [Display(Name = "证书身份验证")]
    Certificate = 3,

    /// <summary>
    /// 发行令牌身份验证
    /// </summary>
    [Description("发行令牌身份验证")]
    [Display(Name = "发行令牌身份验证")]
    IssuedToken = 4
}
