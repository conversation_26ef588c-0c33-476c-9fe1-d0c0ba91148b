using System.Text.RegularExpressions;
using EdgeGateway.Driver.Entity.Model;

namespace EdgeGateway.Driver.Entity.Parsers;

/// <summary>
/// OPC UA地址解析器
/// 支持标准OPC UA NodeId格式解析
/// </summary>
public class OpcUaAddressParser : AddressParserBase
{
    /// <summary>
    /// 协议名称
    /// </summary>
    public override string ProtocolName => "OpcUA";

    /// <summary>
    /// 解析器优先级
    /// </summary>
    public override int Priority => 50;

    /// <summary>
    /// 支持的地址格式示例
    /// </summary>
    public override string[] SupportedFormats => new[]
    {
        "i=2258",                    // 数值型NodeId
        "ns=2;i=2258",              // 带命名空间的数值型NodeId
        "s=MyVariable",             // 字符串型NodeId
        "ns=2;s=MyVariable",        // 带命名空间的字符串型NodeId
        "g=09087e75-8e5e-499b-954f-f2a9603db28a", // GUID型NodeId
        "ns=2;g=09087e75-8e5e-499b-954f-f2a9603db28a", // 带命名空间的GUID型NodeId
        "b=M/RbKBsRVkePCePcx24oRA==", // ByteString型NodeId
        "ns=2;b=M/RbKBsRVkePCePcx24oRA==", // 带命名空间的ByteString型NodeId
        "i=2258[0]",                // 数组访问
        "ns=2;s=MyArray[0:5]"       // 数组范围访问
    };

    /// <summary>
    /// 预编译的正则表达式（提高性能）
    /// </summary>
    private static readonly Regex NodeIdRegex = new Regex(
        @"^(?:ns=(?<namespace>\d+);)?(?<type>[isgb])=(?<identifier>[^[\]]+)(?:\[(?<arrayIndex>\d+(?::\d+)?)\])?$",
        RegexOptions.Compiled | RegexOptions.IgnoreCase
    );

    /// <summary>
    /// 检查地址是否可以被此解析器解析
    /// </summary>
    /// <param name="address">地址字符串</param>
    /// <returns>是否可以解析</returns>
    public override bool CanParse(string address)
    {
        if (string.IsNullOrWhiteSpace(address))
            return false;

        // 检查是否匹配OPC UA NodeId格式
        return NodeIdRegex.IsMatch(address.Trim());
    }

    /// <summary>
    /// 解析地址
    /// </summary>
    /// <param name="address">原始地址</param>
    /// <param name="dataType">数据类型</param>
    /// <param name="length">数据长度</param>
    /// <returns>通用地址对象</returns>
    public override UniversalAddress Parse(string address, string dataType = null, int? length = null)
    {
        if (!CanParse(address))
        {
            return new UniversalAddress
            {
                RawAddress = address,
                IsValid = false,
                ErrorMessage = $"不支持的OPC UA地址格式: {address}",
                Protocol = ProtocolName
            };
        }

        var trimmedAddress = address.Trim();

        try
        {
            var match = NodeIdRegex.Match(trimmedAddress);
            if (!match.Success)
            {
                return new UniversalAddress
                {
                    RawAddress = address,
                    IsValid = false,
                    ErrorMessage = $"无法解析OPC UA地址: {address}",
                    Protocol = ProtocolName
                };
            }

            return ParseNodeIdFormat(match, trimmedAddress, dataType, length);
        }
        catch (Exception ex)
        {
            return new UniversalAddress
            {
                RawAddress = address,
                IsValid = false,
                ErrorMessage = $"解析OPC UA地址时发生错误: {ex.Message}",
                Protocol = ProtocolName
            };
        }
    }

    /// <summary>
    /// 解析NodeId格式的地址
    /// </summary>
    /// <param name="match">正则匹配结果</param>
    /// <param name="address">原始地址</param>
    /// <param name="dataType">数据类型</param>
    /// <param name="length">数据长度</param>
    /// <returns>通用地址对象</returns>
    private UniversalAddress ParseNodeIdFormat(Match match, string address, string dataType, int? length)
    {
        var namespaceGroup = match.Groups["namespace"];
        var typeGroup = match.Groups["type"];
        var identifierGroup = match.Groups["identifier"];
        var arrayIndexGroup = match.Groups["arrayIndex"];

        // 解析命名空间索引
        var namespaceIndex = 0;
        if (namespaceGroup.Success && int.TryParse(namespaceGroup.Value, out var nsIndex))
        {
            namespaceIndex = nsIndex;
        }

        // 解析标识符类型
        var identifierType = typeGroup.Value.ToLower();
        var identifier = identifierGroup.Value;

        // 解析数组索引
        string arrayIndex = null;
        if (arrayIndexGroup.Success)
        {
            arrayIndex = arrayIndexGroup.Value;
        }

        // 确定区域类型（OPC UA中主要用于分类）
        var areaType = identifierType switch
        {
            "i" => "Numeric",      // 数值型
            "s" => "String",       // 字符串型
            "g" => "Guid",         // GUID型
            "b" => "ByteString",   // 字节字符串型
            _ => "Unknown"
        };

        // 验证标识符格式
        var validationResult = ValidateIdentifier(identifierType, identifier);
        if (!validationResult.IsValid)
        {
            return new UniversalAddress
            {
                RawAddress = address,
                IsValid = false,
                ErrorMessage = validationResult.ErrorMessage,
                Protocol = ProtocolName
            };
        }

        // 对于数值型标识符，转换为long；对于其他类型，使用哈希码
        long baseAddress = 0;
        if (identifierType == "i" && long.TryParse(identifier, out var numericId))
        {
            baseAddress = numericId;
        }
        else
        {
            // 对于非数值型标识符，使用哈希码作为BaseAddress
            baseAddress = identifier.GetHashCode();
        }

        return new UniversalAddress
        {
            Protocol = ProtocolName,
            AreaType = areaType,
            BaseAddress = baseAddress,
            SubAddress = namespaceIndex,
            BitPosition = arrayIndex != null ? ParseArrayIndex(arrayIndex) : null,
            RawAddress = address,
            DataLength = length,
            IsValid = true
        };
    }

    /// <summary>
    /// 验证标识符格式
    /// </summary>
    /// <param name="type">标识符类型</param>
    /// <param name="identifier">标识符值</param>
    /// <returns>验证结果</returns>
    private (bool IsValid, string? ErrorMessage) ValidateIdentifier(string type, string identifier)
    {
        switch (type.ToLower())
        {
            case "i": // 数值型
                if (!uint.TryParse(identifier, out _))
                {
                    return (false, $"数值型NodeId标识符必须是有效的无符号整数: {identifier}");
                }
                break;

            case "s": // 字符串型
                if (string.IsNullOrEmpty(identifier))
                {
                    return (false, "字符串型NodeId标识符不能为空");
                }
                break;

            case "g": // GUID型
                if (!Guid.TryParse(identifier, out _))
                {
                    return (false, $"GUID型NodeId标识符必须是有效的GUID格式: {identifier}");
                }
                break;

            case "b": // 字节字符串型
                try
                {
                    Convert.FromBase64String(identifier);
                }
                catch
                {
                    return (false, $"字节字符串型NodeId标识符必须是有效的Base64编码: {identifier}");
                }
                break;

            default:
                return (false, $"不支持的NodeId标识符类型: {type}");
        }

        return (true, null);
    }

    /// <summary>
    /// 解析数组索引
    /// </summary>
    /// <param name="arrayIndex">数组索引字符串</param>
    /// <returns>解析后的索引值</returns>
    private int? ParseArrayIndex(string arrayIndex)
    {
        if (string.IsNullOrEmpty(arrayIndex))
            return null;

        // 处理单个索引
        if (int.TryParse(arrayIndex, out var singleIndex))
        {
            return singleIndex;
        }

        // 处理范围索引（取起始索引）
        if (arrayIndex.Contains(':'))
        {
            var parts = arrayIndex.Split(':');
            if (parts.Length == 2 && int.TryParse(parts[0], out var startIndex))
            {
                return startIndex;
            }
        }

        return null;
    }
}
