namespace EdgeGateway.Driver.Entity.Attributes;

/// <summary>
/// </summary>
[AttributeUsage(AttributeTargets.Class, Inherited = false)]
public class DriverInfoAttribute : Attribute
{
    /// <summary>
    ///     驱动信息
    /// </summary>
    /// <param name="name">名称</param>
    /// <param name="version">版本</param>
    /// <param name="typeName">分类</param>
    /// <param name="driverType">类型</param>
    /// <param name="createVariables">是否生成点位</param>
    /// <param name="isNet">是否为网口设备</param>
    /// <param name="description">协议描述</param>
    public DriverInfoAttribute(string name, string version, string typeName, string driverType ,
     bool createVariables , bool isNet , string description )
    {
        Name = name;
        Version = version;
        TypeName = typeName;
        DriverType = driverType;
        CreateVariables = createVariables;
        IsNet = isNet;
        Description = description;
    }

    /// <summary>
    ///     协议名称
    /// </summary>
    public string Name { get; }

    /// <summary>
    ///     版本号
    /// </summary>
    public string Version { get; }

    /// <summary>
    ///     分类名称
    /// </summary>
    public string TypeName { get; }

    /// <summary>
    ///     协议类型：PLC；CNC
    /// </summary>
    public string DriverType { get; set; }

    /// <summary>
    /// 默认生成点位
    /// </summary>
    public bool CreateVariables { get; set; }

    /// <summary>
    /// 是否为网口设备
    /// </summary>
    public bool IsNet { get; set; }

    /// <summary>
    /// 协议描述信息
    /// </summary>
    public string Description { get; set; }
}