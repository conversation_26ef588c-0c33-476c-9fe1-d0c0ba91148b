using Microsoft.Data.Sqlite;
using Dapper;
using EdgeGateway.TimeSeriesStorage.Abstractions.Models;
using EdgeGateway.TimeSeriesStorage.Abstractions.Configurations;
using Microsoft.Extensions.Options;
using Microsoft.Extensions.Logging;
using System.Text.Json;
using EdgeGateway.Core.Extension;
using System.Diagnostics;

namespace EdgeGateway.TimeSeriesStorage.SQLite.Services
{
  /// <summary>
  /// SQLite表管理器
  /// </summary>
  public class TableManager
  {
    private readonly SQLiteStorageOptions _options;
    private const string TABLE_PREFIX = "ts_data_";
    private const int MAX_CACHE_SIZE = 1000;

    /// <summary>
    /// 设备标签配置缓存
    /// key: deviceId, value: 标签列表
    /// </summary>
    private readonly Dictionary<string, HashSet<string>> _deviceTagsCache = new();

    /// <summary>
    /// 查询优化器
    /// </summary>
    private readonly QueryOptimizer _queryOptimizer;

    /// <summary>
    /// 存储监控器
    /// </summary>
    private readonly StorageMonitor _monitor;

    /// <summary>
    /// 日志记录器
    /// </summary>
    private readonly ILogger<TableManager> _logger;

    /// <summary>
    /// 设备表结构缓存项
    /// </summary>
    private class TableCacheItem
    {
      public HashSet<string> Columns { get; set; }
      public DateTime LastAccessed { get; set; }
      public DateTime CacheTime { get; set; }
    }

    /// <summary>
    /// 设备表结构缓存
    /// key: deviceId, value: 表结构信息
    /// </summary>
    private readonly Dictionary<string, TableCacheItem> _tableStructureCache = new();

    /// <summary>
    /// 缓存过期时间(分钟)
    /// </summary>
    private const int CACHE_EXPIRATION_MINUTES = 30;

    /// <summary>
    /// 缓存清理间隔(分钟)
    /// </summary>
    private const int CACHE_CLEANUP_INTERVAL_MINUTES = 5;

    /// <summary>
    /// 上次缓存清理时间
    /// </summary>
    private DateTime _lastCacheCleanup = DateTime.UtcNow;

    /// <summary>
    /// 构造函数
    /// </summary>
    /// <param name="options">SQLite存储选项</param>
    /// <param name="queryOptimizer">查询优化器</param>
    /// <param name="monitor">存储监控器</param>
    /// <param name="logger">日志记录器</param>
    public TableManager(
        IOptions<SQLiteStorageOptions> options,
        QueryOptimizer queryOptimizer,
        StorageMonitor monitor,
        ILogger<TableManager> logger)
    {
      _options = options.Value;
      _queryOptimizer = queryOptimizer;
      _monitor = monitor;
      _logger = logger;
    }

    /// <summary>
    /// 获取设备表名
    /// </summary>
    public string GetDeviceTableName(string deviceId)
    {
      return $"device_{deviceId.ToLower().Replace("-", "_")}";
    }

    /// <summary>
    /// 确保设备表已创建，并处理新增的标签列
    /// </summary>
    /// <param name="connection">SQLite数据库连接</param>
    /// <param name="deviceId">设备ID</param>
    /// <param name="tagNames">需要创建的标签列表</param>
    /// <exception cref="SqliteException">当数据库操作出错时抛出</exception>
    private void EnsureDeviceTableCreated(SqliteConnection connection, string deviceId, IEnumerable<string> tagNames)
    {
      try
      {
        var tableName = GetDeviceTableName(deviceId);
        var currentTime = DateTime.UtcNow;

        // 检查是否需要清理缓存
        if ((currentTime - _lastCacheCleanup).TotalMinutes >= CACHE_CLEANUP_INTERVAL_MINUTES)
        {
          CleanupCache();
          _lastCacheCleanup = currentTime;
        }

        // 尝试从缓存获取表结构
        var cachedColumns = GetCachedTableStructure(deviceId);
        if (cachedColumns != null)
        {
          // 检查是否有新的标签需要添加
          var newColumns = tagNames.Where(tag => !cachedColumns.Contains(NormalizeColumnName(tag))).ToList();
          if (!newColumns.Any())
          {
            return; // 所有列都存在，直接返回
          }
        }

        // 检查表是否存在
        var tableExists = connection.ExecuteScalar<int>(
            "SELECT count(*) FROM sqlite_master WHERE type='table' AND name=@tableName",
            new { tableName });

        if (tableExists == 0)
        {
          // 创建新表
          CreateNewTable(connection, tableName, tagNames);
          // 更新缓存
          UpdateTableStructureCache(deviceId, tagNames.Select(t => NormalizeColumnName(t)).ToHashSet());
        }
        else
        {
          // 获取现有列
          var existingColumns = cachedColumns ?? GetExistingColumns(connection, tableName);

          // 添加新列
          var missingColumns = tagNames
              .Select(tag => NormalizeColumnName(tag))
              .Where(col => !existingColumns.Contains(col))
              .ToList();

          if (missingColumns.Any())
          {
            AddNewColumns(connection, tableName, missingColumns);
            // 更新缓存
            existingColumns.UnionWith(missingColumns);
            UpdateTableStructureCache(deviceId, existingColumns);
          }
        }
      }
      catch (Exception ex)
      {
        _logger.LogError(ex, "创建或更新设备表 {DeviceId} 时发生错误", deviceId);
        _monitor.TrackError($"创建或更新设备表失败: {deviceId}", ex);
        throw;
      }
    }

    /// <summary>
    /// 获取缓存的表结构
    /// </summary>
    /// <param name="deviceId">设备ID</param>
    /// <returns>表结构</returns>
    private HashSet<string> GetCachedTableStructure(string deviceId)
    {
      // 尝试从缓存获取表结构
      if (_tableStructureCache.TryGetValue(deviceId, out var cacheItem))
      {
        // 如果缓存未过期
        if ((DateTime.UtcNow - cacheItem.CacheTime).TotalMinutes < CACHE_EXPIRATION_MINUTES)
        {
          cacheItem.LastAccessed = DateTime.UtcNow;
          return cacheItem.Columns;
        }
        // 缓存过期，移除
        _tableStructureCache.Remove(deviceId);
      }
      return null;
    }

    /// <summary>
    /// 更新表结构缓存
    /// </summary>
    private void UpdateTableStructureCache(string deviceId, HashSet<string> columns)
    {
      var currentTime = DateTime.UtcNow; // 当前时间
      _tableStructureCache[deviceId] = new TableCacheItem
      {
        Columns = columns,
        LastAccessed = currentTime,
        CacheTime = currentTime
      };
    }

    /// <summary>
    /// 清理过期缓存
    /// </summary>
    private void CleanupCache()
    {
      var currentTime = DateTime.UtcNow; // 当前时间
      var expiredKeys = _tableStructureCache
          .Where(kvp =>
              (currentTime - kvp.Value.CacheTime).TotalMinutes >= CACHE_EXPIRATION_MINUTES ||
              (currentTime - kvp.Value.LastAccessed).TotalMinutes >= CACHE_EXPIRATION_MINUTES)
          .Select(kvp => kvp.Key)
          .ToList(); // 过期键列表

      foreach (var key in expiredKeys)
      {
        _tableStructureCache.Remove(key); // 移除过期项
      }

      if (expiredKeys.Any()) // 如果有过期项
      {
        _logger.LogInformation("已清理 {Count} 个过期的表结构缓存项", expiredKeys.Count); // 日志记录
      }
    }

    /// <summary>
    /// 获取表的现有列
    /// </summary>
    /// <param name="connection">数据库连接</param>
    /// <param name="tableName">表名</param>
    /// <returns>列名</returns>
    private HashSet<string> GetExistingColumns(SqliteConnection connection, string tableName)
    {
      return connection.Query<string>(
          $"SELECT name FROM pragma_table_info('{tableName}')")
          .ToHashSet();
    }

    /// <summary>
    /// 创建新表
    /// </summary>
    /// <param name="connection">数据库连接</param>
    /// <param name="tableName">表名</param>
    /// <param name="tagNames">列名</param>
    private void CreateNewTable(SqliteConnection connection, string tableName, IEnumerable<string> tagNames)
    {
      var columnDefs = new List<string>
      {
        "timestamp INTEGER NOT NULL PRIMARY KEY",
        "created_at INTEGER NOT NULL"
      };

      foreach (var tagName in tagNames)
      {
        var normalizedName = NormalizeColumnName(tagName);
        columnDefs.Add($"{normalizedName} TEXT");
      }

      var createTableSql = $@"
        CREATE TABLE IF NOT EXISTS {tableName} (
            {string.Join(",\n", columnDefs)}
        );
        CREATE INDEX IF NOT EXISTS idx_{tableName}_timestamp 
            ON {tableName}(timestamp);";

      connection.Execute(createTableSql);
    }

    /// <summary>
    /// 添加新列
    /// </summary>
    /// <param name="connection">数据库连接</param>
    /// <param name="tableName">表名</param>
    /// <param name="columnNames">列名</param>
    private void AddNewColumns(SqliteConnection connection, string tableName, IEnumerable<string> columnNames)
    {
      foreach (var columnName in columnNames)
      {
        try
        {
          var alterTableSql = $"ALTER TABLE {tableName} ADD COLUMN {columnName} TEXT";
          connection.Execute(alterTableSql);
          _logger.LogInformation("已为表 {TableName} 添加新列: {Column}", tableName, columnName);
        }
        catch (SqliteException ex)
        {
          // 如果列已存在，忽略错误（处理并发情况）
          if (!ex.Message.Contains("duplicate column name"))
          {
            throw;
          }
        }
      }
    }

    /// <summary>
    /// 处理标签值，将复杂类型转换为可存储的字符串
    /// </summary>
    private string ProcessTagValue(object value)
    {
      try
      {
        if (value == null)
          return null;

        // 处理 ExpandoObject
        if (value is System.Dynamic.ExpandoObject expando)
        {
          return JsonSerializer.Serialize(expando);
        }

        // 处理数组
        if (value.GetType().IsArray || (value is System.Collections.IEnumerable && !(value is string)))
        {
          return JsonSerializer.Serialize(value);
        }

        // 处理其他复杂对象
        if (!value.GetType().IsPrimitive && !(value is string) && !(value is DateTime))
        {
          return JsonSerializer.Serialize(value);
        }

        return value.ToString();
      }
      catch (Exception ex)
      {
        _logger.LogWarning(ex, "处理标签值时发生错误，值类型: {ValueType}", value?.GetType()?.FullName ?? "null");
        return null;
      }
    }

    /// <summary>
    /// 批量写入时序数据，支持事务和错误恢复
    /// </summary>
    /// <param name="connection">数据库连接</param>
    /// <param name="transaction">数据库事务</param>
    /// <param name="dataList">要写入的时序数据列表</param>
    public async Task WriteBatchAsync(
        SqliteConnection connection,
        SqliteTransaction transaction,
        IEnumerable<TimeSeriesData> dataList)
    {
      // 写入前检查存储空间
      await _monitor.CheckStorageSpaceAsync();

      var sw = Stopwatch.StartNew();
      try
      {
        // 按设备ID分组处理数据
        var deviceGroups = dataList.GroupBy(d => d.DeviceId);

        foreach (var deviceGroup in deviceGroups)
        {
          try
          {
            var deviceId = deviceGroup.Key;
            // 收集该设备的所有唯一标签
            var allTags = deviceGroup.SelectMany(d => d.ExtraTags.Keys).Distinct();

            // 确保设备表存在且包含所需的列
            EnsureDeviceTableCreated(connection, deviceId, allTags);
            var tableName = GetDeviceTableName(deviceId);

            // 处理每条数据记录
            foreach (var data in deviceGroup)
            {
              try
              {
                var columns = new List<string> { "timestamp", "created_at" };
                var values = new List<string> { "@timestamp", "@created_at" };
                var parameters = new DynamicParameters();

                parameters.Add("@timestamp", ObjectExtension.ToShanghaiTimeUnixTimeMilliseconds(data.Timestamp));
                parameters.Add("@created_at", ObjectExtension.ToShanghaiTimeUnixTimeMilliseconds(DateTime.UtcNow));

                // 处理额外的标签数据
                foreach (var tag in data.ExtraTags)
                {
                  var normalizedName = NormalizeColumnName(tag.Key);
                  var paramName = $"@{normalizedName}";

                  // 处理标签值
                  var processedValue = ProcessTagValue(tag.Value);

                  columns.Add(normalizedName);
                  values.Add(paramName);
                  parameters.Add(paramName, processedValue);

                  // 记录复杂类型的处理
                  if (tag.Value != null && !(tag.Value is string) && !tag.Value.GetType().IsPrimitive)
                  {
                    _logger.LogDebug("处理复杂类型标签值 - 设备: {DeviceId}, 标签: {TagName}, 类型: {ValueType}, 处理后: {ProcessedValue}",
                        deviceId, tag.Key, tag.Value.GetType().Name, processedValue);
                  }
                }

                var sql = $@"INSERT OR REPLACE INTO {tableName} 
                      ({string.Join(", ", columns)}) 
                      VALUES ({string.Join(", ", values)})";

                await connection.ExecuteAsync(sql, parameters, transaction);
              }
              catch (Exception ex)
              {
                _logger.LogError(ex, "处理设备 {DeviceId} 的单条数据时发生错误", deviceId);
                _monitor.TrackError($"处理数据记录失败: {deviceId}", ex);
                // 继续处理下一条数据
              }
            }
          }
          catch (Exception ex)
          {
            _logger.LogError(ex, "处理设备组 {DeviceId} 时发生错误", deviceGroup.Key);
            _monitor.TrackError($"处理设备组失败: {deviceGroup.Key}", ex);
            // 继续处理下一个设备组
          }
        }

        // 记录写入性能
        _monitor.TrackWritePerformance(dataList.Count(), sw.ElapsedMilliseconds);
      }
      catch (Exception ex)
      {
        _logger.LogError(ex, "批量写入数据时发生严重错误");
        _monitor.TrackError("批量写入数据失败", ex);
        throw;
      }
    }

    /// <summary>
    /// 查询时序数据
    /// </summary>
    /// <param name="connection">数据库连接</param>
    /// <param name="deviceId">设备ID</param>
    /// <param name="tagNames">标签列表</param>
    /// <param name="startTime">开始时间</param>
    /// <param name="endTime">结束时间</param>
    /// <returns>时序数据列表</returns>
    public async Task<IEnumerable<TimeSeriesData>> QueryAsync(
        SqliteConnection connection,
        string deviceId,
        IEnumerable<string> tagNames,
        DateTime startTime,
        DateTime endTime)
    {
      // 使用 QueryOptimizer 包装查询
      return await _queryOptimizer.OptimizeQuery(
          async () =>
          {
            if (string.IsNullOrEmpty(deviceId))
            {
              throw new ArgumentNullException(nameof(deviceId));
            }

            var tableName = GetDeviceTableName(deviceId);

            // 检查表是否存在
            var tableExists = await connection.ExecuteScalarAsync<int>(
                "SELECT count(*) FROM sqlite_master WHERE type='table' AND name=@tableName",
                new { tableName });

            if (tableExists == 0)
            {
              _logger.LogWarning("查询的表 {TableName} 不存在", tableName);
              return Enumerable.Empty<TimeSeriesData>();
            }

            // 如果tagNames为空，获取所有列名
            if (tagNames == null || !tagNames.Any())
            {
              var schema = await connection.QueryAsync<string>($@"
                      SELECT name FROM pragma_table_info('{tableName}')
                      WHERE name LIKE 'tag_%'");
              tagNames = schema.Select(col => DeNormalizeColumnName(col));
            }

            var columns = tagNames.Select(t => NormalizeColumnName(t));
            var sql = $@"
                  SELECT timestamp, {string.Join(", ", columns)}
                  FROM {tableName}
                  WHERE CAST(timestamp AS INTEGER) BETWEEN @startTicks AND @endTicks
                  ORDER BY timestamp";

            // 打印完整的sql语句，替换参数的sql
            Console.WriteLine(sql.Replace("@startTicks", ObjectExtension.ToShanghaiTimeUnixTimeMilliseconds(startTime).ToString()).Replace("@endTicks", ObjectExtension.ToShanghaiTimeUnixTimeMilliseconds(endTime).ToString()));

            var data = await connection.QueryAsync(sql, new
            {
              startTicks = ObjectExtension.ToShanghaiTimeUnixTimeMilliseconds(startTime),
              endTicks = ObjectExtension.ToShanghaiTimeUnixTimeMilliseconds(endTime)
            });

            return MapQueryResults(data, deviceId, tagNames);
          },
          deviceId,
          new[] { tagNames.FirstOrDefault() ?? "all" },
          startTime,
          endTime
      );
    }

    /// <summary>
    /// 清理过期数据
    /// </summary>
    /// <param name="connection">数据库连接</param>
    public void CleanExpiredData(SqliteConnection connection)
    {
      try
      {
        var expireTime = ObjectExtension.ToShanghaiTimeUnixTimeMilliseconds(DateTime.UtcNow.AddDays(-_options.RetentionDays));

        // 获取所有设备表
        var tables = connection.Query<string>(
            "SELECT name FROM sqlite_master WHERE type='table' AND name LIKE 'device_%'");

        foreach (var table in tables)
        {
          // 删除过期数据
          connection.Execute(
              $"DELETE FROM {table} WHERE timestamp < @expireTime",
              new { expireTime });

          _logger.LogInformation("已清理表 {TableName} 中的过期数据", table);
        }
      }
      catch (Exception ex)
      {
        _logger.LogError(ex, "清理过期数据失败");
        throw;
      }
    }

    /// <summary>
    /// 确保设备的标签列都存在
    /// </summary>
    /// <param name="connection">数据库连接</param>
    /// <param name="deviceId">设备ID</param>
    /// <param name="data">时序数据</param>
    /// <returns>标签列表</returns>
    private HashSet<string> EnsureDeviceTagColumns(
        SqliteConnection connection,
        string deviceId,
        IEnumerable<TimeSeriesData> data)
    {
      var tags = data.SelectMany(d => d.ExtraTags.Keys).ToHashSet();

      if (!_deviceTagsCache.TryGetValue(deviceId, out var cachedTags))
      {
        // 检查缓存大小
        if (_deviceTagsCache.Count >= MAX_CACHE_SIZE)
        {
          // 移除最早的20%的缓存
          var removeCount = MAX_CACHE_SIZE / 5;
          var keysToRemove = _deviceTagsCache.Keys.Take(removeCount).ToList();
          foreach (var key in keysToRemove)
          {
            _deviceTagsCache.Remove(key);
          }
          _monitor.TrackEvent("CacheCleanup", new Dictionary<string, string>
          {
            { "RemovedCount", removeCount.ToString() },
            { "RemainingCount", _deviceTagsCache.Count.ToString() }
          });
        }

        cachedTags = new HashSet<string>();
        _deviceTagsCache[deviceId] = cachedTags;
      }

      // 添加新的标签
      if (tags.Except(cachedTags).Any())
      {
        cachedTags.UnionWith(tags);
      }

      return cachedTags;
    }

    /// <summary>
    /// 分页查询时序数据
    /// </summary>
    /// <param name="connection"> </param>
    /// <param name="deviceId"></param>
    /// <param name="tagNames"></param>
    /// <param name="startTime"></param>
    /// <param name="endTime"></param>
    /// <param name="pageSize"></param>
    /// <param name="pageNumber"></param>
    /// <param name="descending"></param>
    /// <returns></returns>
    /// <exception cref="ArgumentNullException">设备ID为空</exception>
    /// <exception cref="ArgumentException">开始时间大于结束时间</exception>
    /// <exception cref="ArgumentException">页大小必须大于0</exception>
    /// <exception cref="ArgumentException">页码必须大于0</exception>
    public async Task<PagedResult<TimeSeriesData>> QueryPagedAsync(
    SqliteConnection connection,
    string deviceId,
    IEnumerable<string> tagNames,
    DateTime startTime,
    DateTime endTime,
    int pageSize,
    int pageNumber,
    bool descending = false)
    {
      // 获取查询建议
      var suggestion = _queryOptimizer.GetQuerySuggestion(deviceId, new[] { tagNames.FirstOrDefault() ?? "all" });

      // 应用建议的页大小,如果建议的页大小大于请求的页大小，则使用建议的页大小
      if (pageSize > suggestion.RecommendedPageSize && pageSize > 1000)
      {
        pageSize = suggestion.RecommendedPageSize;
      }

      // 如果建议创建索引
      if (suggestion.IndexSuggestions.Any())
      {
        foreach (var indexSuggestion in suggestion.IndexSuggestions)
        {
          _logger.LogWarning($"查询优化建议: {indexSuggestion}");
        }
      }

      // 获取总记录数
      var tableName = GetDeviceTableName(deviceId);
      var totalCount = await connection.ExecuteScalarAsync<int>($@"
          SELECT COUNT(*) 
          FROM {tableName} 
          WHERE timestamp BETWEEN @startTicks AND @endTicks",
          new
          {
            startTicks = startTime.ToShanghaiTimeUnixTimeMilliseconds(),
            endTicks = endTime.ToShanghaiTimeUnixTimeMilliseconds()
          });

      // 执行分页查询
      var pagedData = await _queryOptimizer.QueryPaged(
          async (size, number) =>
          {
            var offset = (size - 1) * number;

            // 如果tagNames为空，获取所有列名
            if (tagNames == null || !tagNames.Any())
            {
              var schema = await connection.QueryAsync<string>($@"
                          SELECT name FROM pragma_table_info('{tableName}')
                          WHERE name LIKE 'tag_%'");
              tagNames = schema.Select(col => DeNormalizeColumnName(col));
            }

            var columns = tagNames.Select(t => NormalizeColumnName(t));

            var sql = $@"
                  SELECT timestamp, {string.Join(", ", columns)}
                  FROM {tableName}
                  WHERE timestamp BETWEEN @startTicks AND @endTicks
                  ORDER BY timestamp {(descending ? "DESC" : "ASC")}
                  LIMIT @offset,@number";

            var data = await connection.QueryAsync(sql, new
            {
              startTicks = startTime.ToShanghaiTimeUnixTimeMilliseconds(),
              endTicks = endTime.ToShanghaiTimeUnixTimeMilliseconds(),
              offset,
              number
            });

            return MapQueryResults(data, deviceId, tagNames);
          },
          pageSize,
          pageNumber
      );

      // 创建包含总数的分页结果
      return new PagedResult<TimeSeriesData>
      {
        Items = pagedData.Items,
        PageNumber = pageNumber,
        PageSize = pageSize,
        TotalCount = totalCount,
      };
    }

    /// <summary>
    /// 获取数据总数
    /// </summary>
    /// <param name="connection">数据库连接</param>
    /// <param name="tableName">表名</param>
    /// <param name="startTime">开始时间</param>
    /// <param name="endTime">结束时间</param>
    /// <returns></returns>
    private async Task<int> GetTotalCount(
        SqliteConnection connection,
        string tableName,
        DateTime startTime,
        DateTime endTime)
    {
      var sql = $@"
        SELECT COUNT(*) 
        FROM {tableName}
        WHERE timestamp BETWEEN @startTicks AND @endTicks";

      return await connection.ExecuteScalarAsync<int>(sql, new
      {
        startTicks = ObjectExtension.ToShanghaiTimeUnixTimeMilliseconds(startTime),
        endTicks = ObjectExtension.ToShanghaiTimeUnixTimeMilliseconds(endTime)
      });
    }

    /// <summary>
    /// 规范化列名
    /// </summary>
    public string NormalizeColumnName(string tagId)
    {
      return $"tag_{tagId.ToLower().Replace("-", "_")}";
    }

    /// <summary>
    /// 还原标签ID
    /// </summary>
    private string DeNormalizeColumnName(string columnName)
    {
      return columnName.Substring(4).Replace("_", "-");
    }

    private void CleanCache()
    {
      if (_deviceTagsCache.Count >= MAX_CACHE_SIZE)
      {
        try
        {
          // 移除最早的20%的缓存
          var removeCount = MAX_CACHE_SIZE / 5;
          var keysToRemove = _deviceTagsCache.Keys.Take(removeCount).ToList();
          foreach (var key in keysToRemove)
          {
            _deviceTagsCache.Remove(key);
          }
          _monitor.TrackEvent("CacheCleanup", new Dictionary<string, string>
          {
            { "RemovedCount", removeCount.ToString() },
            { "RemainingCount", _deviceTagsCache.Count.ToString() }
          });
        }
        catch (Exception ex)
        {
          _monitor.TrackError("清理缓存失败", ex);
        }
      }
    }

    /// <summary>
    /// 映射查询结果
    /// </summary>
    /// <param name="data"></param>
    /// <param name="deviceId"></param>
    /// <param name="tagNames"></param>
    /// <returns></returns>
    private IEnumerable<TimeSeriesData> MapQueryResults(
        IEnumerable<dynamic> data,
        string deviceId,
        IEnumerable<string> tagNames)
    {
      // 确保参数非空
      if (data == null) return Enumerable.Empty<TimeSeriesData>();
      if (tagNames == null) tagNames = Enumerable.Empty<string>();

      var results = new List<TimeSeriesData>();
      foreach (var row in data)
      {
        try
        {
          var timeSeriesData = new TimeSeriesData
          {
            DeviceId = deviceId,
            Timestamp = ObjectExtension.FromUnixTimeMilliseconds(Convert.ToInt64(row.timestamp)),
            ExtraTags = new Dictionary<string, object>()
          };

          // 使用IDictionary<string,object>来访问动态对象的属性
          var rowDict = (IDictionary<string, object>)row;

          foreach (var tagName in tagNames)
          {
            var normalizedName = NormalizeColumnName(tagName);
            if (rowDict.ContainsKey(normalizedName))
            {
              var value = rowDict[normalizedName];
              if (value != null)
              {
                timeSeriesData.ExtraTags[tagName] = value;
              }
            }
          }

          results.Add(timeSeriesData);
        }
        catch (Exception ex)
        {
          _logger.LogError(ex, "处理查询结果行时发生错误");
          // 继续处理下一行
        }
      }
      return results;
    }

    /// <summary>
    /// 查询最新数据
    /// </summary>
    /// <param name="connection">数据库连接</param>
    /// <param name="deviceId">设备ID</param>
    /// <param name="tagNames">标签列表</param>
    /// <returns>最新数据</returns>
    public async Task<TimeSeriesData?> QueryLatestAsync(
        SqliteConnection connection,
        string deviceId,
        IEnumerable<string> tagNames)
    {
      try
      {
        var result = await _queryOptimizer.OptimizeQuery(
            async () =>
            {
              var tableName = GetDeviceTableName(deviceId);

              // 如果tagNames为空，获取所有标签列
              if (tagNames == null || !tagNames.Any())
              {
                var schema = await connection.QueryAsync<string>($@"
                            SELECT name FROM pragma_table_info('{tableName}')
                            WHERE name LIKE 'tag_%'");
                tagNames = schema?.Select(col => DeNormalizeColumnName(col)) ?? Enumerable.Empty<string>();
              }

              var columns = tagNames.Select(t => NormalizeColumnName(t));
              var columnList = columns.Any()
                      ? $"timestamp, {string.Join(", ", columns)}"
                      : "timestamp";

              var sql = $@"
                        SELECT {columnList}
                        FROM {tableName}
                        ORDER BY timestamp DESC
                        LIMIT 1";

              var data = await connection.QueryAsync(sql);
              return MapQueryResults(data, deviceId, tagNames);
            },
            deviceId,
            new[] { "latest" },
            DateTime.UtcNow.AddMinutes(-5),
            DateTime.UtcNow
        );

        return result?.FirstOrDefault();
      }
      catch (Exception ex)
      {
        _logger.LogError(ex, "查询设备 {DeviceId} 最新数据失败", deviceId);
        throw;
      }
    }

    /// <summary>
    /// 删除设备数据
    /// </summary>
    public async Task<bool> DeleteDeviceDataAsync(
        SqliteConnection connection,
        string deviceId)
    {
      try
      {
        var tableName = GetDeviceTableName(deviceId);
        var sql = $"DROP TABLE IF EXISTS {tableName}";
        await connection.ExecuteAsync(sql);
        return true;
      }
      catch (Exception ex)
      {
        _logger.LogError(ex, "删除设备 {DeviceId} 数据失败", deviceId);
        return false;
      }
    }

    /// <summary>
    /// 获取统计信息
    /// </summary>
    /// <param name="connection">数据库连接</param>
    /// <param name="deviceId">设备ID</param>
    /// <param name="tagNames">标签列表</param>
    /// <param name="startTime">开始时间</param>
    /// <param name="endTime">结束时间</param>
    /// <returns>时序数据统计信息，包含每个标签的最大值、最小值和平均值</returns>
    public async Task<TimeSeriesStatistics> GetStatisticsAsync(
        SqliteConnection connection,
        string deviceId,
        IEnumerable<string> tagNames,
        DateTime startTime,
        DateTime endTime)
    {
      var tableName = GetDeviceTableName(deviceId);
      var stats = new TimeSeriesStatistics { DeviceId = deviceId };

      // 获取基本统计信息（数据点数量、第一条和最后一条数据时间）
      var basicStatsSql = $@"
            SELECT COUNT(*) as count,
                   MIN(timestamp) as first_time,
                   MAX(timestamp) as last_time
            FROM {tableName}
            WHERE timestamp BETWEEN @startTicks AND @endTicks";

      var basicStatsResult = await connection.QueryFirstAsync(basicStatsSql, new
      {
        startTicks = startTime.ToShanghaiTimeUnixTimeMilliseconds(),
        endTicks = endTime.ToShanghaiTimeUnixTimeMilliseconds()
      });

      stats.DataPointCount = basicStatsResult.count;
      stats.FirstDataTime = basicStatsResult.first_time != null ? ObjectExtension.FromUnixTimeMilliseconds(basicStatsResult.first_time) : null;
      stats.LastDataTime = basicStatsResult.last_time != null ? ObjectExtension.FromUnixTimeMilliseconds(basicStatsResult.last_time) : null;

      // 如果没有数据或没有指定标签，则直接返回
      if (stats.DataPointCount == 0 || tagNames == null || !tagNames.Any())
      {
        return stats;
      }

      // 获取表的现有列
      var existingColumns = GetExistingColumns(connection, tableName);

      // 处理每个标签的统计信息
      foreach (var tagName in tagNames)
      {
        // 规范化列名
        var columnName = NormalizeColumnName(tagName);

        // 检查列是否存在
        if (!existingColumns.Contains(columnName))
        {
          continue;
        }

        // 查询该标签的统计信息
        var tagStatsSql = $@"
              SELECT 
                  COUNT({columnName}) as count,
                  MIN({columnName}) as min_value,
                  MAX({columnName}) as max_value,
                  AVG({columnName}) as avg_value
              FROM {tableName}
              WHERE timestamp BETWEEN @startTicks AND @endTicks
              AND {columnName} IS NOT NULL";

        var tagStatsResult = await connection.QueryFirstAsync(tagStatsSql, new
        {
          startTicks = startTime.ToShanghaiTimeUnixTimeMilliseconds(),
          endTicks = endTime.ToShanghaiTimeUnixTimeMilliseconds()
        });

        // 只有当有数据时才添加标签统计信息
        if (tagStatsResult.count > 0)
        {
          stats.TagStats[tagName] = new TagStatistics
          {
            DataPointCount = tagStatsResult.count,
            MinValue = tagStatsResult.min_value != null ? Convert.ToDouble(tagStatsResult.min_value) : null,
            MaxValue = tagStatsResult.max_value != null ? Convert.ToDouble(tagStatsResult.max_value) : null,
            AverageValue = tagStatsResult.avg_value != null ? Convert.ToDouble(tagStatsResult.avg_value) : null
          };
        }
      }

      return stats;
    }

    /// <summary>
    /// 获取数据总数
    /// </summary>
    public async Task<int> GetDataCountAsync(
        SqliteConnection connection,
        string deviceId,
        DateTime startTime,
        DateTime endTime)
    {
      var tableName = GetDeviceTableName(deviceId);
      var sql = $@"SELECT COUNT(*) FROM {tableName} 
                     WHERE timestamp BETWEEN @startTicks AND @endTicks";

      return await connection.ExecuteScalarAsync<int>(sql, new
      {
        startTicks = startTime.ToShanghaiTimeUnixTimeMilliseconds(),
        endTicks = endTime.ToShanghaiTimeUnixTimeMilliseconds()
      });
    }

    /// <summary>
    /// 获取设备总数
    /// </summary>
    public async Task<int> GetDeviceCountAsync(SqliteConnection connection)
    {
      var sql = @"SELECT COUNT(*) FROM sqlite_master 
                    WHERE type='table' AND name LIKE 'device_%'";
      return await connection.ExecuteScalarAsync<int>(sql);
    }

    /// <summary>
    /// 获取数据点总数
    /// </summary>
    public async Task<long> GetTotalDataPointCountAsync(SqliteConnection connection)
    {
      // 使用 PRAGMA 获取所有设备表
      var tables = await connection.QueryAsync<string>(
          "SELECT name FROM sqlite_master WHERE type='table' AND name LIKE 'device_%'");

      long totalCount = 0;
      foreach (var table in tables)
      {
        var count = await connection.ExecuteScalarAsync<long>($"SELECT COUNT(*) FROM {table}");
        totalCount += count;
      }
      return totalCount;
    }

    /// <summary>
    /// 获取最早数据时间
    /// </summary>
    public async Task<DateTime?> GetEarliestDataTimeAsync(SqliteConnection connection)
    {
      var tables = await connection.QueryAsync<string>(
          "SELECT name FROM sqlite_master WHERE type='table' AND name LIKE 'device_%'");

      long? earliestTicks = null;
      foreach (var table in tables)
      {
        var ticks = await connection.ExecuteScalarAsync<long?>($@"
                SELECT MIN(timestamp) FROM {table}");
        if (ticks.HasValue && (!earliestTicks.HasValue || ticks < earliestTicks))
        {
          earliestTicks = ticks;
        }
      }

      return earliestTicks.HasValue ? earliestTicks.Value.FromUnixTimeMilliseconds() : null;
    }

    /// <summary>
    /// 获取最新数据时间
    /// </summary>
    public async Task<DateTime?> GetLatestDataTimeAsync(SqliteConnection connection)
    {
      var tables = await connection.QueryAsync<string>(
          "SELECT name FROM sqlite_master WHERE type='table' AND name LIKE 'device_%'");

      long? latestTicks = null;
      foreach (var table in tables)
      {
        var ticks = await connection.ExecuteScalarAsync<long?>($@"
                SELECT MAX(timestamp) FROM {table}");
        if (ticks.HasValue && (!latestTicks.HasValue || ticks > latestTicks))
        {
          latestTicks = ticks;
        }
      }

      return latestTicks.HasValue ? latestTicks.Value.FromUnixTimeMilliseconds() : null;
    }
  }
}