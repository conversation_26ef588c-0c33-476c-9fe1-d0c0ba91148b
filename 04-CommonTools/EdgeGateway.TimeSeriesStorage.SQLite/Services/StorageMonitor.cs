using System.Collections.Concurrent;
using Dapper;
using EdgeGateway.TimeSeriesStorage.Abstractions.Configurations;
using EdgeGateway.TimeSeriesStorage.Abstractions.Models;
using EdgeGateway.TimeSeriesStorage.SQLite.Models;
using Microsoft.Data.Sqlite;
using Microsoft.Extensions.Logging;
using Microsoft.Extensions.Options;

namespace EdgeGateway.TimeSeriesStorage.SQLite.Services;

/// <summary>
///     存储监控服务
/// </summary>
public class StorageMonitor
{
  /// <summary>
  ///     日志记录器
  /// </summary>
  private readonly ILogger<StorageMonitor> _logger;

  /// <summary>
  ///     SQLite存储选项
  /// </summary>
  private readonly SQLiteStorageOptions _options;

  /// <summary>
  ///     性能指标缓存
  /// </summary>
  private readonly ConcurrentDictionary<string, PerformanceMetrics> _metricsCache = new();

    // 性能指标阈值
    private const int WARNING_QUERY_TIME = 1000; // 1秒
    private const int WARNING_WRITE_TIME = 500; // 500毫秒

    public StorageMonitor(
        ILogger<StorageMonitor> logger,
        IOptions<SQLiteStorageOptions> options)
    {
        _logger = logger;
        _options = options.Value;
    }

    /// <summary>
    ///     检查存储空间
    /// </summary>
    public async Task CheckStorageSpaceAsync()
    {
        try
        {
            var dbPath = _options.DatabasePath; // 数据库路径
            var directory = Path.GetDirectoryName(dbPath); // 目录路径

            if (string.IsNullOrEmpty(directory)) throw new ArgumentException("Invalid database path");

            var driveInfo = new DriveInfo(Path.GetPathRoot(Path.GetFullPath(directory))); // 驱动信息
            var freeSpaceGB = driveInfo.AvailableFreeSpace / (1024.0 * 1024 * 1024);

            // 如果可用空间小于最小空间
            if (freeSpaceGB < _options.MinimumFreeSpaceGB)
            {
                _logger.LogWarning("存储空间不足: {FreeSpace:F2}GB", freeSpaceGB);
                // 触发紧急清理
                await EmergencyCleanupAsync();
            }
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "检查存储空间失败");
        }
    }

    /// <summary>
    ///     记录查询性能
    /// </summary>
    public void TrackQueryPerformance(string query, long elapsedMs, int resultCount)
    {
        var metrics = _metricsCache.GetOrAdd(query, _ => new PerformanceMetrics());
        metrics.RecordQuery(elapsedMs, resultCount);

        if (elapsedMs > WARNING_QUERY_TIME) _logger.LogWarning($"查询性能警告: {query} 耗时{elapsedMs}ms, 结果数{resultCount}");
    }

    /// <summary>
    ///     记录写入性能
    /// </summary>
    public void TrackWritePerformance(int batchSize, long elapsedMs)
    {
        if (elapsedMs > WARNING_WRITE_TIME) _logger.LogWarning($"写入性能警告: 批次大小{batchSize}, 耗时{elapsedMs}ms");
    }

    /// <summary>
    ///     获取性能报告
    /// </summary>
    public PerformanceReport GetPerformanceReport()
    {
        var report = new PerformanceReport
        {
            QueryMetrics = _metricsCache.ToDictionary(
                kvp => kvp.Key,
                kvp => kvp.Value.GetSnapshot())
        };

        return report;
    }

    /// <summary>
    ///     紧急清理
    /// </summary>
    private async Task EmergencyCleanupAsync()
    {
        try
        {
            await using var connection = new SqliteConnection(_options.DatabasePath);
            await connection.OpenAsync();
            // 压缩数据库
            await connection.ExecuteAsync("VACUUM");
            _logger.LogInformation("紧急清理完成");
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "紧急清理失败");
        }
    }

    /// <summary>
    ///     跟踪错误
    /// </summary>
    /// <param name="message">消息</param>
    /// <param name="ex">异常</param>
    public void TrackError(string message, Exception ex)
    {
        _logger.LogError(ex, message);
    }

    /// <summary>
    ///     跟踪事件
    /// </summary>
    /// <param name="eventName">事件名称</param>
    /// <param name="properties">属性</param>
    public void TrackEvent(string eventName, Dictionary<string, string> properties)
    {
        _logger.LogInformation($"{eventName}: {string.Join(", ", properties.Select(p => $"{p.Key}={p.Value}"))}");
    }
}

/// <summary>
///     性能指标
/// </summary>
public class PerformanceMetrics
{
  /// <summary>
  ///     性能指标队列
  /// </summary>
  private readonly ConcurrentQueue<QueryMetric> _metrics = new();

  /// <summary>
  ///     最大指标数
  /// </summary>
  private const int MAX_METRICS = 1000;

  /// <summary>
  ///     记录查询性能
  /// </summary>
  /// <param name="elapsedMs">耗时</param>
  /// <param name="resultCount">结果数</param>
  public void RecordQuery(long elapsedMs, int resultCount)
    {
        _metrics.Enqueue(new QueryMetric
        {
            Timestamp = DateTime.UtcNow,
            ElapsedMs = elapsedMs,
            ResultCount = resultCount
        });

        // 保持队列大小
        while (_metrics.Count > MAX_METRICS) _metrics.TryDequeue(out _);
    }

  /// <summary>
  ///     获取性能快照
  /// </summary>
  /// <returns>性能快照</returns>
  public MetricsSnapshot GetSnapshot()
    {
        var metrics = _metrics.ToArray();
        return new MetricsSnapshot
        {
            AverageElapsedMs = metrics.Average(m => m.ElapsedMs),
            MaxElapsedMs = metrics.Max(m => m.ElapsedMs),
            TotalQueries = metrics.Length,
            TotalResults = metrics.Sum(m => m.ResultCount)
        };
    }
}