<Project Sdk="Microsoft.NET.Sdk">

    <PropertyGroup>
        <TargetFramework>net9.0</TargetFramework>
        <ImplicitUsings>enable</ImplicitUsings>
        <Nullable>enable</Nullable>
    </PropertyGroup>

    <ItemGroup>
        <PackageReference Include="Microsoft.Data.Sqlite" Version="9.0.7" />
        <PackageReference Include="Dapper" Version="2.1.66" />
        <PackageReference Include="Microsoft.Extensions.Options" Version="9.0.7" />
        <PackageReference Include="Microsoft.Extensions.Logging.Abstractions" Version="9.0.7" />
        <PackageReference Include="Microsoft.Extensions.Hosting.Abstractions" Version="9.0.7" />
    </ItemGroup>

    <ItemGroup>
        <ProjectReference Include="..\..\01-Business\03-Storage\EdgeGateway.TimeSeriesStorage.Abstractions\EdgeGateway.TimeSeriesStorage.Abstractions.csproj" />
    </ItemGroup>

</Project>
