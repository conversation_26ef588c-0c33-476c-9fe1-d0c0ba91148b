using System;
using System.IO;
using System.Threading.Tasks;
using Microsoft.Data.Sqlite;
using Microsoft.Extensions.Logging;

namespace EdgeGateway.Preferences.Data;

/// <summary>
///     个性化数据库上下文，负责数据库操作
/// </summary>
public class PreferenceDbContext : IDisposable
{
    /// <summary>
    /// 日志记录器
    /// </summary>
    private readonly ILogger _logger;

    /// <summary>
    /// 数据库路径
    /// </summary>
    private readonly string _dbPath;

    /// <summary>
    /// 数据库连接
    /// </summary>
    private SqliteConnection _connection;

    /// <summary>
    ///     构造函数
    /// </summary>
    /// <param name="logger">日志记录器</param>
    /// <param name="dbPath">数据库路径</param>
    public PreferenceDbContext(ILogger logger, string dbPath)
    {
        _logger = logger;
        _dbPath = dbPath;
    }

    /// <summary>
    ///     初始化数据库
    /// </summary>
    public async Task InitializeAsync()
    {
        try
        {
            // 确保目录存在
            var directory = Path.GetDirectoryName(_dbPath);
            if (!string.IsNullOrEmpty(directory) && !Directory.Exists(directory)) Directory.CreateDirectory(directory);

            // 创建连接
            var connectionString = new SqliteConnectionStringBuilder
            {
                DataSource = _dbPath,
                Mode = SqliteOpenMode.ReadWriteCreate
            }.ToString();

            _connection = new SqliteConnection(connectionString);
            await _connection.OpenAsync();

            // 创建表
            await CreateTablesAsync();

            _logger.LogInformation("个性化数据库初始化完成");
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "初始化个性化数据库失败");
            throw;
        }
    }

    /// <summary>
    ///     创建数据库表
    /// </summary>
    private async Task CreateTablesAsync()
    {
        // 创建设备信息表
        var createDeviceTableCmd = @"
                CREATE TABLE IF NOT EXISTS Devices (
                    DeviceId TEXT PRIMARY KEY,
                    DeviceName TEXT NOT NULL,
                    DeviceType TEXT NOT NULL,
                    Properties TEXT NOT NULL,
                    Tags TEXT NOT NULL,
                    LastModified TEXT NOT NULL,
                    Version INTEGER NOT NULL
                );";

        // 创建标签信息表
        var createTagTableCmd = @"
                CREATE TABLE IF NOT EXISTS Tags (
                    TagId TEXT PRIMARY KEY,
                    TagName TEXT NOT NULL,
                    TagColor TEXT NOT NULL,
                    AssociatedDevices TEXT NOT NULL,
                    LastModified TEXT NOT NULL,
                    Version INTEGER NOT NULL
                );";

        // 创建变更记录表
        var createChangeRecordTableCmd = @"
                CREATE TABLE IF NOT EXISTS ChangeRecords (
                    Id INTEGER PRIMARY KEY AUTOINCREMENT,
                    EntityType TEXT NOT NULL,
                    EntityId TEXT NOT NULL,
                    Operation TEXT NOT NULL,
                    JsonData TEXT NOT NULL,
                    ChangeTime TEXT NOT NULL,
                    Version INTEGER NOT NULL,
                    IsSynced INTEGER NOT NULL DEFAULT 0
                );";

        // 创建同步元数据表
        var createSyncMetadataTableCmd = @"
                CREATE TABLE IF NOT EXISTS SyncMetadata (
                    Key TEXT PRIMARY KEY,
                    Value TEXT NOT NULL
                );";

        using (var command = _connection.CreateCommand())
        {
            command.CommandText = createDeviceTableCmd;
            await command.ExecuteNonQueryAsync();

            command.CommandText = createTagTableCmd;
            await command.ExecuteNonQueryAsync();

            command.CommandText = createChangeRecordTableCmd;
            await command.ExecuteNonQueryAsync();

            command.CommandText = createSyncMetadataTableCmd;
            await command.ExecuteNonQueryAsync();
        }

        // 初始化同步元数据
        await InitializeSyncMetadataAsync();
    }

    /// <summary>
    ///     初始化同步元数据
    /// </summary>
    private async Task InitializeSyncMetadataAsync()
    {
        using (var command = _connection.CreateCommand())
        {
            // 检查LastSyncTime是否存在
            command.CommandText = "SELECT COUNT(*) FROM SyncMetadata WHERE Key = 'LastSyncTime'";
            var count = Convert.ToInt32(await command.ExecuteScalarAsync());

            if (count == 0)
            {
                // 插入初始同步时间
                command.CommandText = "INSERT INTO SyncMetadata (Key, Value) VALUES ('LastSyncTime', @value)";
                command.Parameters.AddWithValue("@value", DateTime.UtcNow.ToString("o"));
                await command.ExecuteNonQueryAsync();
            }
        }
    }

    /// <summary>
    ///     获取数据库连接
    /// </summary>
    public SqliteConnection GetConnection()
    {
        return _connection;
    }

    /// <summary>
    ///     释放资源
    /// </summary>
    public void Dispose()
    {
        _connection?.Dispose();
    }
}