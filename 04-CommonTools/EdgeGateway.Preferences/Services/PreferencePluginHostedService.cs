using System;
using System.Threading;
using System.Threading.Tasks;
using Microsoft.Extensions.Hosting;
using Microsoft.Extensions.Logging;
using Microsoft.Extensions.Configuration;

namespace EdgeGateway.Preferences.Services
{
    /// <summary>
    /// 个性化插件宿主服务，作为IHostedService实现
    /// </summary>
    public class PreferencePluginHostedService : IHostedService, IDisposable
    {
        /// <summary>
        /// 日志记录器
        /// </summary>
        private readonly ILogger<PreferencePluginHostedService> _logger;
        /// <summary>
        /// 个性化数据管理器
        /// </summary>
        private readonly PreferenceManager _preferenceManager;
        /// <summary>
        /// 同步客户端
        /// </summary>
        private readonly SyncClient _syncClient;
        /// <summary>
        /// 事件监听器
        /// </summary>
        private readonly EventListener _eventListener;
        /// <summary>
        /// 配置
        /// </summary>
        private readonly IConfiguration _configuration;
        /// <summary>
        /// 同步定时器
        /// </summary>
        private Timer _syncTimer;
        /// <summary>
        /// 是否已释放
        /// </summary>
        private bool _disposed = false;

        /// <summary>
        /// 构造函数
        /// </summary>
        /// <param name="logger">日志记录器</param>
        /// <param name="preferenceManager">个性化数据管理器</param>
        /// <param name="syncClient">同步客户端</param>
        /// <param name="eventListener">事件监听器</param>
        /// <param name="configuration">配置</param>
        public PreferencePluginHostedService(
            ILogger<PreferencePluginHostedService> logger,
            PreferenceManager preferenceManager,
            SyncClient syncClient,
            EventListener eventListener,
            IConfiguration configuration)
        {
            _logger = logger;
            _preferenceManager = preferenceManager;
            _syncClient = syncClient;
            _eventListener = eventListener;
            _configuration = configuration;
        }

        /// <summary>
        /// 启动服务
        /// </summary>
        /// <param name="cancellationToken">取消令牌</param>
        public async Task StartAsync(CancellationToken cancellationToken)
        {
            _logger.LogInformation("个性化服务插件启动中...");

            // 检查是否启用个性化服务
            var isEnabled = _configuration.GetValue<bool>("Preferences:Enabled", false);
            if (!isEnabled)
            {
                _logger.LogInformation("个性化服务插件已禁用，将不会启动");
                return;
            }

            try
            {
                // 初始化个性化数据管理器
                await _preferenceManager.InitializeAsync();

                // 启动事件监听
                _eventListener.StartListening();

                // 执行初始同步
                await _syncClient.SyncChangesAsync();

                // 设置定时同步
                var syncInterval = _configuration.GetValue<int>("Preferences:SyncInterval", 15);
                _syncTimer = new Timer(SyncDataAsync, null, TimeSpan.FromMinutes(syncInterval), TimeSpan.FromMinutes(syncInterval));

                _logger.LogInformation($"个性化服务插件已启动，同步间隔: {syncInterval} 分钟");
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "启动个性化服务插件失败");
                throw;
            }
        }

        /// <summary>
        /// 停止服务
        /// </summary>
        /// <param name="cancellationToken">取消令牌</param>
        public async Task StopAsync(CancellationToken cancellationToken)
        {
            _logger.LogInformation("个性化服务插件停止中...");

            try
            {
                // 停止定时器
                _syncTimer?.Change(Timeout.Infinite, 0);

                // 停止事件监听
                _eventListener.StopListening();

                // 执行最后一次同步
                await _syncClient.SyncChangesAsync();

                _logger.LogInformation("个性化服务插件已停止");
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "停止个性化服务插件失败");
            }
        }

        /// <summary>
        /// 同步数据
        /// </summary>
        /// <param name="state">状态对象</param>
        private async void SyncDataAsync(object state)
        {
            try
            {
                _logger.LogDebug("开始定时同步个性化数据");
                await _syncClient.SyncChangesAsync();
                _logger.LogDebug("定时同步个性化数据完成");
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "定时同步个性化数据失败");
            }
        }

        /// <summary>
        /// 释放资源
        /// </summary>
        public void Dispose()
        {
            Dispose(true);
            GC.SuppressFinalize(this);
        }

        /// <summary>
        /// 释放资源
        /// </summary>
        /// <param name="disposing">是否正在释放</param>
        protected virtual void Dispose(bool disposing)
        {
            if (_disposed)
            {
                return;
            }

            if (disposing)
            {
                _syncTimer?.Dispose();
            }

            _disposed = true;
        }
    }
}