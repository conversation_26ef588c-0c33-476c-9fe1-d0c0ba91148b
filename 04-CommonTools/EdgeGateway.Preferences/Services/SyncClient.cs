using System;
using System.Net.Http;
using System.Net.Http.Json;
using System.Threading.Tasks;
using EdgeGateway.Preferences.Models;
using Microsoft.Extensions.Configuration;
using Microsoft.Extensions.Logging;

namespace EdgeGateway.Preferences.Services
{
    /// <summary>
    /// 同步客户端，负责与中央服务器通信
    /// </summary>
    public class SyncClient
    {
        /// <summary>
        /// 日志记录器  
        /// </summary>
        private readonly ILogger<SyncClient> _logger;
        /// <summary>
        /// HTTP客户端
        /// </summary>
        private readonly HttpClient _httpClient;
        /// <summary>
        /// 个性化数据管理器
        /// </summary>
        private readonly PreferenceManager _preferenceManager;
        /// <summary>
        /// 网关ID  
        /// +-+-+
        /// </summary>
        private readonly string _gatewayId;
        /// <summary>
        /// 同步端点
        /// </summary>
        private readonly string _syncEndpoint;

        /// <summary>
        /// 构造函数
        /// </summary>
        /// <param name="logger">日志记录器</param>
        /// <param name="httpClientFactory">HTTP客户端工厂</param>
        /// <param name="preferenceManager">个性化数据管理器</param>
        /// <param name="configuration">配置</param>
        public SyncClient(
            ILogger<SyncClient> logger,
            IHttpClientFactory httpClientFactory,
            PreferenceManager preferenceManager,
            IConfiguration configuration)
        {
            _logger = logger;
            _httpClient = httpClientFactory.CreateClient("PreferenceSync");
            _preferenceManager = preferenceManager;
            _gatewayId = configuration["Gateway:Id"] ?? Guid.NewGuid().ToString();
            _syncEndpoint = configuration["Preferences:SyncEndpoint"] ?? "/api/sync";
        }

        /// <summary>
        /// 同步变更
        /// </summary>
        public async Task<SyncResult> SyncChangesAsync()
        {
            try
            {
                _logger.LogInformation("开始同步个性化数据");

                // 获取上次同步时间
                var lastSyncTime = await _preferenceManager.GetLastSyncTimeAsync();

                // 获取本地待同步的变更
                var pendingChanges = await _preferenceManager.GetPendingChangesAsync();
                _logger.LogInformation($"发现 {pendingChanges.Count} 条待同步的变更");

                // 构建同步请求
                var request = new SyncRequest
                {
                    GatewayId = _gatewayId,
                    Changes = pendingChanges,
                    LastSyncTime = lastSyncTime
                };

                // 发送同步请求
                var response = await _httpClient.PostAsJsonAsync(_syncEndpoint, request);

                // 如果请求失败，返回错误结果
                if (!response.IsSuccessStatusCode)
                {
                    var errorMessage = await response.Content.ReadAsStringAsync();
                    _logger.LogError($"同步请求失败: {response.StatusCode}, {errorMessage}");
                    return new SyncResult
                    {
                        Success = false,
                        ErrorMessage = $"同步请求失败: {response.StatusCode}, {errorMessage}",
                        SyncTime = DateTime.UtcNow
                    };
                }

                // 处理同步响应
                var syncResult = await response.Content.ReadFromJsonAsync<SyncResult>();
                if (syncResult == null)
                {
                    _logger.LogError("无法解析同步响应");
                    return new SyncResult
                    {
                        Success = false,
                        ErrorMessage = "无法解析同步响应",
                        SyncTime = DateTime.UtcNow
                    };
                }

                // 如果同步成功
                if (syncResult.Success)
                {
                    // 应用服务器返回的变更
                    if (syncResult.ServerChanges?.Count > 0)
                    {
                        _logger.LogInformation($"收到 {syncResult.ServerChanges.Count} 条服务器变更");
                        await _preferenceManager.ApplyServerChangesAsync(syncResult.ServerChanges);
                    }

                    // 标记本地变更为已同步
                    await _preferenceManager.MarkChangesAsSyncedAsync();

                    // 更新上次同步时间
                    await _preferenceManager.UpdateLastSyncTimeAsync(syncResult.SyncTime);

                    _logger.LogInformation("同步个性化数据完成");
                }
                else
                {
                    _logger.LogWarning($"同步失败: {syncResult.ErrorMessage}");
                }

                return syncResult;
            }
            catch (HttpRequestException ex)
            {
                _logger.LogError(ex, "网络请求异常");
                return new SyncResult
                {
                    Success = false,
                    ErrorMessage = $"网络请求异常: {ex.Message}",
                    SyncTime = DateTime.UtcNow
                };
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "同步个性化数据失败");
                return new SyncResult
                {
                    Success = false,
                    ErrorMessage = ex.Message,
                    SyncTime = DateTime.UtcNow
                };
            }
        }
    }
}