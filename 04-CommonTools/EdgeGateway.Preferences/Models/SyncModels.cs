using System;
using System.Collections.Generic;

namespace EdgeGateway.Preferences.Models
{
    /// <summary>
    /// 同步请求模型，用于向中央服务器发送同步请求
    /// </summary>
    public class SyncRequest
    {
        /// <summary>
        /// 网关ID
        /// </summary>
        public string GatewayId { get; set; }

        /// <summary>
        /// 本地变更记录集合
        /// </summary>
        public List<ChangeRecord> Changes { get; set; } = new List<ChangeRecord>();

        /// <summary>
        /// 上次同步时间
        /// </summary>
        public DateTime LastSyncTime { get; set; }
    }

    /// <summary>
    /// 同步结果模型，用于接收中央服务器的同步响应
    /// </summary>
    public class SyncResult
    {
        /// <summary>
        /// 同步是否成功
        /// </summary>
        public bool Success { get; set; }

        /// <summary>
        /// 错误信息
        /// </summary>
        public string ErrorMessage { get; set; }

        /// <summary>
        /// 服务器端变更记录集合
        /// </summary>
        public List<ChangeRecord> ServerChanges { get; set; } = new List<ChangeRecord>();

        /// <summary>
        /// 同步时间
        /// </summary>
        public DateTime SyncTime { get; set; }
    }
}