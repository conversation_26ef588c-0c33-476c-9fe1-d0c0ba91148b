// using System.Collections.Concurrent;
// using EdgeGateway.TimeSeriesStorage.TDengine.Configurations;
// using Microsoft.Extensions.Logging;
// using TDengineDriver;
// using TDengineDriver.Driver;
//
// namespace EdgeGateway.TimeSeriesStorage.TDengine.Services;
//
// /// <summary>
// /// TDengine 连接管理器
// /// </summary>
// public class ConnectionManager : IDisposable
// {
//   private readonly TDengineStorageOptions _options;
//   private readonly ILogger<ConnectionManager> _logger;
//   private readonly ConcurrentBag<ITDengineConnection> _connectionPool = new();
//   private readonly SemaphoreSlim _poolSemaphore;
//   private bool _isDisposed;
//
//   /// <summary>
//   /// 构造函数
//   /// </summary>
//   /// <param name="options">存储配置选项</param>
//   /// <param name="logger">日志记录器</param>
//   public ConnectionManager(TDengineStorageOptions options, ILogger<ConnectionManager> logger)
//   {
//     _options = options ?? throw new ArgumentNullException(nameof(options));
//     _logger = logger ?? throw new ArgumentNullException(nameof(logger));
//     _poolSemaphore = new SemaphoreSlim(_options.MaxPoolSize, _options.MaxPoolSize);
//
//     // 初始化连接池
//     InitializeConnectionPool();
//   }
//
//   /// <summary>
//   /// 初始化连接池
//   /// </summary>
//   private void InitializeConnectionPool()
//   {
//     try
//     {
//       // 预热连接池，创建一部分连接备用
//       int preWarmCount = Math.Min(5, _options.MaxPoolSize);
//       for (int i = 0; i < preWarmCount; i++)
//       {
//         var connection = CreateNewConnection();
//         if (connection != null)
//         {
//           _connectionPool.Add(connection);
//         }
//       }
//
//       _logger.LogInformation("TDengine连接池初始化完成，预热连接数: {count}", preWarmCount);
//     }
//     catch (Exception ex)
//     {
//       _logger.LogError(ex, "初始化TDengine连接池失败");
//       throw;
//     }
//   }
//
//   /// <summary>
//   /// 创建新的数据库连接
//   /// </summary>
//   /// <returns>数据库连接</returns>
//   private ITDengineConnection CreateNewConnection()
//   {
//     try
//     {
//       var connOptions = new ConnectorOptions
//       {
//         Host = _options.Host,
//         Port = _options.Port,
//         Username = _options.Username,
//         Password = _options.Password,
//         Database = _options.Database,
//         ConnectTimeout = _options.ConnectionTimeout * 1000, // 毫秒
//       };
//
//       ITDengineConnection connection;
//       if (_options.UseRESTful)
//       {
//         // 使用 REST 连接
//         connection = new RestfulConnection(connOptions);
//       }
//       else
//       {
//         // 使用原生连接
//         connection = new NativeConnection(connOptions);
//       }
//
//       // 确保连接可用
//       connection.Open();
//
//       return connection;
//     }
//     catch (Exception ex)
//     {
//       _logger.LogError(ex, "创建TDengine连接失败");
//       throw;
//     }
//   }
//
//   /// <summary>
//   /// 获取连接
//   /// </summary>
//   /// <param name="cancellationToken">取消令牌</param>
//   /// <returns>数据库连接</returns>
//   public async Task<ITDengineConnection> GetConnectionAsync(CancellationToken cancellationToken = default)
//   {
//     try
//     {
//       // 等待获取连接池信号量
//       await _poolSemaphore.WaitAsync(cancellationToken);
//
//       // 尝试从连接池获取连接
//       if (_connectionPool.TryTake(out var connection))
//       {
//         // 检测连接是否有效
//         if (IsConnectionValid(connection))
//         {
//           return connection;
//         }
//
//         // 无效连接，释放并创建新连接
//         connection.Dispose();
//       }
//
//       // 创建新连接并返回
//       return CreateNewConnection();
//     }
//     catch (Exception ex)
//     {
//       _logger.LogError(ex, "获取TDengine连接失败");
//       _poolSemaphore.Release();
//       throw;
//     }
//   }
//
//   /// <summary>
//   /// 释放连接回连接池
//   /// </summary>
//   /// <param name="connection">数据库连接</param>
//   public void ReleaseConnection(ITDengineConnection connection)
//   {
//     if (connection == null)
//     {
//       return;
//     }
//
//     try
//     {
//       // 检查连接是否有效
//       if (IsConnectionValid(connection))
//       {
//         // 有效连接放回池中
//         _connectionPool.Add(connection);
//       }
//       else
//       {
//         // 无效连接释放
//         connection.Dispose();
//       }
//     }
//     catch (Exception ex)
//     {
//       _logger.LogError(ex, "释放TDengine连接失败");
//       try
//       {
//         connection.Dispose();
//       }
//       catch
//       {
//         // 忽略二次异常
//       }
//     }
//     finally
//     {
//       // 释放信号量
//       _poolSemaphore.Release();
//     }
//   }
//
//   /// <summary>
//   /// 检查连接是否有效
//   /// </summary>
//   /// <param name="connection">数据库连接</param>
//   /// <returns>连接是否有效</returns>
//   private bool IsConnectionValid(ITDengineConnection connection)
//   {
//     if (connection == null)
//     {
//       return false;
//     }
//
//     try
//     {
//       // 执行简单查询验证连接
//       using var cmd = connection.CreateCommand("SELECT SERVER_STATUS()");
//       cmd.ExecuteScalar();
//       return true;
//     }
//     catch
//     {
//       return false;
//     }
//   }
//
//   /// <summary>
//   /// 释放资源
//   /// </summary>
//   public void Dispose()
//   {
//     Dispose(true);
//     GC.SuppressFinalize(this);
//   }
//
//   /// <summary>
//   /// 释放资源
//   /// </summary>
//   /// <param name="disposing">是否正在释放托管资源</param>
//   protected virtual void Dispose(bool disposing)
//   {
//     if (_isDisposed)
//     {
//       return;
//     }
//
//     if (disposing)
//     {
//       // 释放所有连接
//       foreach (var connection in _connectionPool)
//       {
//         try
//         {
//           connection.Dispose();
//         }
//         catch (Exception ex)
//         {
//           _logger.LogError(ex, "释放TDengine连接失败");
//         }
//       }
//
//       _connectionPool.Clear();
//       _poolSemaphore.Dispose();
//     }
//
//     _isDisposed = true;
//   }
// }