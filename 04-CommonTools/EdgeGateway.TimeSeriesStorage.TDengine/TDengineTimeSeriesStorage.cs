// using EdgeGateway.TimeSeriesStorage.Abstractions.Interfaces;
// using EdgeGateway.TimeSeriesStorage.Abstractions.Models;
// using EdgeGateway.TimeSeriesStorage.TDengine.Configurations;
// using EdgeGateway.TimeSeriesStorage.TDengine.Services;
// using Microsoft.Extensions.Logging;
// using Microsoft.Extensions.Options;
//
// namespace EdgeGateway.TimeSeriesStorage.TDengine;
//
// /// <summary>
// /// TDengine时序数据存储实现
// /// </summary>
// public class TDengineTimeSeriesStorage : ITimeSeriesStorage, IDisposable
// {
//   private readonly TDengineStorageOptions _options;
//   private readonly ILogger<TDengineTimeSeriesStorage> _logger;
//   private readonly ConnectionManager _connectionManager;
//   private readonly WriteBufferPool _writeBuffer;
//   private bool _isDisposed;
//
//   /// <summary>
//   /// 存储类型
//   /// </summary>
//   public string StorageType => "TDengine";
//
//   /// <summary>
//   /// 构造函数
//   /// </summary>
//   /// <param name="options">TDengine存储配置选项</param>
//   /// <param name="logger">日志记录器</param>
//   public TDengineTimeSeriesStorage(IOptions<TDengineStorageOptions> options, ILogger<TDengineTimeSeriesStorage> logger)
//   {
//     _options = options?.Value ?? throw new ArgumentNullException(nameof(options));
//     _logger = logger ?? throw new ArgumentNullException(nameof(logger));
//
//     try
//     {
//       // 初始化连接管理器
//       _connectionManager = new ConnectionManager(_options, _logger);
//
//       // 初始化写入缓冲池
//       _writeBuffer = new WriteBufferPool(_options, _connectionManager, _logger);
//
//       // 初始化数据库结构
//       InitializeDatabaseAsync().Wait();
//
//       _logger.LogInformation("TDengine时序数据存储初始化完成");
//     }
//     catch (Exception ex)
//     {
//       _logger.LogError(ex, "TDengine时序数据存储初始化失败");
//       throw;
//     }
//   }
//
//   /// <summary>
//   /// 初始化数据库结构
//   /// </summary>
//   private async Task InitializeDatabaseAsync()
//   {
//     var connection = await _connectionManager.GetConnectionAsync();
//     try
//     {
//       // 创建数据库（如果不存在）
//       string createDbSql = $"CREATE DATABASE IF NOT EXISTS {_options.Database} KEEP {_options.DataRetentionDays}";
//       using (var cmd = connection.CreateCommand(createDbSql))
//       {
//         await cmd.ExecuteNonQueryAsync();
//       }
//
//       // 切换到指定数据库
//       using (var cmd = connection.CreateCommand($"USE {_options.Database}"))
//       {
//         await cmd.ExecuteNonQueryAsync();
//       }
//
//       // 创建超级表（如果不存在）
//       string createSuperTableSql = @"
//                 CREATE STABLE IF NOT EXISTS device_metrics (
//                     ts TIMESTAMP,
//                     value DOUBLE,
//                     str_value NCHAR(64)
//                 ) TAGS (
//                     device_id NCHAR(64),
//                     tag_name NCHAR(64)
//                 )";
//
//       using (var cmd = connection.CreateCommand(createSuperTableSql))
//       {
//         await cmd.ExecuteNonQueryAsync();
//       }
//
//       _logger.LogInformation("TDengine数据库结构初始化完成");
//     }
//     catch (Exception ex)
//     {
//       _logger.LogError(ex, "初始化TDengine数据库结构失败");
//       throw;
//     }
//     finally
//     {
//       _connectionManager.ReleaseConnection(connection);
//     }
//   }
//
//   /// <summary>
//   /// 写入单条时序数据
//   /// </summary>
//   /// <param name="data">时序数据</param>
//   /// <param name="cancellationToken">取消令牌</param>
//   public Task WriteAsync(TimeSeriesData data, CancellationToken cancellationToken = default)
//   {
//     if (data == null)
//     {
//       throw new ArgumentNullException(nameof(data));
//     }
//
//     return _writeBuffer.AddAsync(data);
//   }
//
//   /// <summary>
//   /// 批量写入时序数据
//   /// </summary>
//   /// <param name="dataList">时序数据列表</param>
//   /// <param name="cancellationToken">取消令牌</param>
//   public Task WriteBatchAsync(IEnumerable<TimeSeriesData> dataList, CancellationToken cancellationToken = default)
//   {
//     if (dataList == null)
//     {
//       throw new ArgumentNullException(nameof(dataList));
//     }
//
//     return _writeBuffer.AddBatchAsync(dataList);
//   }
//
//   /// <summary>
//   /// 查询时序数据
//   /// </summary>
//   /// <param name="deviceId">设备ID</param>
//   /// <param name="tagNames">标签名称列表，为空时查询所有标签</param>
//   /// <param name="startTime">开始时间</param>
//   /// <param name="endTime">结束时间</param>
//   /// <param name="cancellationToken">取消令牌</param>
//   /// <returns>时序数据列表</returns>
//   public async Task<IEnumerable<TimeSeriesData>> QueryAsync(
//       string deviceId,
//       IEnumerable<string> tagNames,
//       DateTime startTime,
//       DateTime endTime,
//       CancellationToken cancellationToken = default)
//   {
//     // 先刷新缓冲区确保数据已写入
//     await _writeBuffer.FlushAsync();
//
//     var connection = await _connectionManager.GetConnectionAsync(cancellationToken);
//     try
//     {
//       var result = new List<TimeSeriesData>();
//       var tagsList = tagNames?.ToList() ?? new List<string>();
//
//       // 如果没有指定标签，先查询该设备所有标签
//       if (tagsList.Count == 0)
//       {
//         tagsList = await GetDeviceTagsAsync(connection, deviceId, cancellationToken);
//       }
//
//       // 转换时间戳
//       long startTimestamp = new DateTimeOffset(startTime).ToUnixTimeMilliseconds();
//       long endTimestamp = new DateTimeOffset(endTime).ToUnixTimeMilliseconds();
//
//       // 按标签查询数据
//       foreach (var tagName in tagsList)
//       {
//         string tableName = $"d_{deviceId.Replace('-', '_')}_{tagName.Replace('-', '_')}";
//
//         // 构建查询语句
//         string sql = $@"
//             SELECT ts, value, str_value FROM {tableName}
//             WHERE ts >= {startTimestamp} AND ts <= {endTimestamp}
//             ORDER BY ts ASC";
//
//         using var cmd = connection.CreateCommand(sql);
//         using var reader = await cmd.ExecuteReaderAsync(cancellationToken);
//
//         // 处理查询结果
//         while (await reader.ReadAsync(cancellationToken))
//         {
//           var timestamp = reader.GetDateTime(0);
//           var value = reader.IsDBNull(1) ? null : reader.GetDouble(1);
//           var strValue = reader.IsDBNull(2) ? null : reader.GetString(2);
//
//           // 找到或创建对应时间戳的数据对象
//           var data = result.FirstOrDefault(d => d.DeviceId == deviceId && d.Timestamp == timestamp);
//           if (data == null)
//           {
//             data = new TimeSeriesData
//             {
//               DeviceId = deviceId,
//               Timestamp = timestamp,
//               ExtraTags = new Dictionary<string, object>()
//             };
//             result.Add(data);
//           }
//
//           // 添加标签值
//           if (value != null)
//           {
//             data.ExtraTags[tagName] = value;
//           }
//           else if (strValue != null)
//           {
//             data.ExtraTags[tagName] = strValue;
//           }
//         }
//       }
//
//       return result.OrderBy(d => d.Timestamp).ToList();
//     }
//     catch (Exception ex)
//     {
//       _logger.LogError(ex, "查询TDengine时序数据失败, 设备ID: {deviceId}", deviceId);
//       throw;
//     }
//     finally
//     {
//       _connectionManager.ReleaseConnection(connection);
//     }
//   }
//
//   /// <summary>
//   /// 获取设备的所有标签名称
//   /// </summary>
//   /// <param name="connection">数据库连接</param>
//   /// <param name="deviceId">设备ID</param>
//   /// <param name="cancellationToken">取消令牌</param>
//   /// <returns>标签名称列表</returns>
//   private async Task<List<string>> GetDeviceTagsAsync(ITDengineConnection connection, string deviceId, CancellationToken cancellationToken)
//   {
//     var tags = new List<string>();
//
//     try
//     {
//       string sql = $"SELECT DISTINCT tag_name FROM device_metrics WHERE device_id = '{deviceId}'";
//
//       using var cmd = connection.CreateCommand(sql);
//       using var reader = await cmd.ExecuteReaderAsync(cancellationToken);
//
//       while (await reader.ReadAsync(cancellationToken))
//       {
//         tags.Add(reader.GetString(0));
//       }
//     }
//     catch (Exception ex)
//     {
//       _logger.LogError(ex, "获取设备标签列表失败, 设备ID: {deviceId}", deviceId);
//     }
//
//     return tags;
//   }
//
//   /// <summary>
//   /// 分页查询时序数据
//   /// </summary>
//   /// <param name="deviceId">设备ID</param>
//   /// <param name="tagNames">标签名称列表，为空时查询所有标签</param>
//   /// <param name="startTime">开始时间</param>
//   /// <param name="endTime">结束时间</param>
//   /// <param name="pageSize">每页大小</param>
//   /// <param name="pageNumber">页码</param>
//   /// <param name="descending">是否降序排序</param>
//   /// <param name="cancellationToken">取消令牌</param>
//   /// <returns>分页结果</returns>
//   public async Task<PagedResult<TimeSeriesData>> QueryPagedAsync(
//       string deviceId,
//       IEnumerable<string> tagNames,
//       DateTime startTime,
//       DateTime endTime,
//       int pageSize,
//       int pageNumber,
//       bool descending = false,
//       CancellationToken cancellationToken = default)
//   {
//     // 先查询总数
//     int totalCount = await GetDataCountAsync(deviceId, tagNames, startTime, endTime, cancellationToken);
//
//     // 如果没有数据，返回空结果
//     if (totalCount == 0)
//     {
//       return new PagedResult<TimeSeriesData>
//       {
//         Items = new List<TimeSeriesData>(),
//         PageNumber = pageNumber,
//         PageSize = pageSize,
//         TotalCount = 0,
//         TotalPages = 0
//       };
//     }
//
//     // 计算偏移量
//     int offset = (pageNumber - 1) * pageSize;
//
//     // 先刷新缓冲区确保数据已写入
//     await _writeBuffer.FlushAsync();
//
//     var connection = await _connectionManager.GetConnectionAsync(cancellationToken);
//     try
//     {
//       var result = new List<TimeSeriesData>();
//       var tagsList = tagNames?.ToList() ?? new List<string>();
//
//       // 如果没有指定标签，先查询该设备所有标签
//       if (tagsList.Count == 0)
//       {
//         tagsList = await GetDeviceTagsAsync(connection, deviceId, cancellationToken);
//       }
//
//       // 转换时间戳
//       long startTimestamp = new DateTimeOffset(startTime).ToUnixTimeMilliseconds();
//       long endTimestamp = new DateTimeOffset(endTime).ToUnixTimeMilliseconds();
//
//       // 构建公共查询条件
//       string orderDirection = descending ? "DESC" : "ASC";
//       string timeCondition = $"ts >= {startTimestamp} AND ts <= {endTimestamp}";
//
//       // 获取分页时间范围
//       var timeRange = await GetPageTimeRangeAsync(
//           connection, deviceId, startTimestamp, endTimestamp, pageSize, offset, descending, cancellationToken);
//
//       if (timeRange.HasValue)
//       {
//         long minTime = timeRange.Value.minTime;
//         long maxTime = timeRange.Value.maxTime;
//
//         // 按标签查询数据
//         foreach (var tagName in tagsList)
//         {
//           string tableName = $"d_{deviceId.Replace('-', '_')}_{tagName.Replace('-', '_')}";
//
//           // 构建查询语句
//           string sql = $@"
//               SELECT ts, value, str_value FROM {tableName}
//               WHERE ts >= {minTime} AND ts <= {maxTime}
//               ORDER BY ts {orderDirection}";
//
//           using var cmd = connection.CreateCommand(sql);
//           using var reader = await cmd.ExecuteReaderAsync(cancellationToken);
//
//           // 处理查询结果
//           while (await reader.ReadAsync(cancellationToken))
//           {
//             var timestamp = reader.GetDateTime(0);
//             var value = reader.IsDBNull(1) ? null : reader.GetDouble(1);
//             var strValue = reader.IsDBNull(2) ? null : reader.GetString(2);
//
//             // 找到或创建对应时间戳的数据对象
//             var data = result.FirstOrDefault(d => d.DeviceId == deviceId && d.Timestamp == timestamp);
//             if (data == null)
//             {
//               data = new TimeSeriesData
//               {
//                 DeviceId = deviceId,
//                 Timestamp = timestamp,
//                 ExtraTags = new Dictionary<string, object>()
//               };
//               result.Add(data);
//             }
//
//             // 添加标签值
//             if (value != null)
//             {
//               data.ExtraTags[tagName] = value;
//             }
//             else if (strValue != null)
//             {
//               data.ExtraTags[tagName] = strValue;
//             }
//           }
//         }
//       }
//
//       // 排序并限制结果数量
//       var items = descending
//         ? result.OrderByDescending(d => d.Timestamp).Take(pageSize).ToList()
//         : result.OrderBy(d => d.Timestamp).Take(pageSize).ToList();
//
//       return new PagedResult<TimeSeriesData>
//       {
//         Items = items,
//         PageNumber = pageNumber,
//         PageSize = pageSize,
//         TotalCount = totalCount,
//         TotalPages = (int)Math.Ceiling(totalCount / (double)pageSize)
//       };
//     }
//     catch (Exception ex)
//     {
//       _logger.LogError(ex, "分页查询TDengine时序数据失败, 设备ID: {deviceId}", deviceId);
//       throw;
//     }
//     finally
//     {
//       _connectionManager.ReleaseConnection(connection);
//     }
//   }
//
//   /// <summary>
//   /// 获取数据总数
//   /// </summary>
//   /// <param name="deviceId">设备ID</param>
//   /// <param name="tagNames">标签名称列表</param>
//   /// <param name="startTime">开始时间</param>
//   /// <param name="endTime">结束时间</param>
//   /// <param name="cancellationToken">取消令牌</param>
//   /// <returns>数据总数</returns>
//   private async Task<int> GetDataCountAsync(
//       string deviceId,
//       IEnumerable<string> tagNames,
//       DateTime startTime,
//       DateTime endTime,
//       CancellationToken cancellationToken)
//   {
//     var connection = await _connectionManager.GetConnectionAsync(cancellationToken);
//     try
//     {
//       var tagsList = tagNames?.ToList() ?? new List<string>();
//
//       // 如果没有指定标签，先查询该设备所有标签
//       if (tagsList.Count == 0)
//       {
//         tagsList = await GetDeviceTagsAsync(connection, deviceId, cancellationToken);
//       }
//
//       if (tagsList.Count == 0)
//       {
//         return 0;
//       }
//
//       // 转换时间戳
//       long startTimestamp = new DateTimeOffset(startTime).ToUnixTimeMilliseconds();
//       long endTimestamp = new DateTimeOffset(endTime).ToUnixTimeMilliseconds();
//
//       // 使用一个标签查询数据点总数（数据点对应时间戳）
//       string tagName = tagsList.First();
//       string tableName = $"d_{deviceId.Replace('-', '_')}_{tagName.Replace('-', '_')}";
//
//       string sql = $@"
//           SELECT COUNT(*) FROM {tableName}
//           WHERE ts >= {startTimestamp} AND ts <= {endTimestamp}";
//
//       using var cmd = connection.CreateCommand(sql);
//       var result = await cmd.ExecuteScalarAsync(cancellationToken);
//
//       return result != null ? Convert.ToInt32(result) : 0;
//     }
//     catch (Exception ex)
//     {
//       _logger.LogError(ex, "获取TDengine数据总数失败, 设备ID: {deviceId}", deviceId);
//       return 0;
//     }
//     finally
//     {
//       _connectionManager.ReleaseConnection(connection);
//     }
//   }
//
//   /// <summary>
//   /// 获取分页的时间范围
//   /// </summary>
//   private async Task<(long minTime, long maxTime)?> GetPageTimeRangeAsync(
//       ITDengineConnection connection,
//       string deviceId,
//       long startTimestamp,
//       long endTimestamp,
//       int pageSize,
//       int offset,
//       bool descending,
//       CancellationToken cancellationToken)
//   {
//     try
//     {
//       // 查询设备的第一个标签
//       var tags = await GetDeviceTagsAsync(connection, deviceId, cancellationToken);
//       if (tags.Count == 0)
//       {
//         return null;
//       }
//
//       string tagName = tags.First();
//       string tableName = $"d_{deviceId.Replace('-', '_')}_{tagName.Replace('-', '_')}";
//
//       // 构建排序方向
//       string orderDirection = descending ? "DESC" : "ASC";
//
//       // 获取分页的时间点
//       string timePointSql = $@"
//           SELECT ts FROM {tableName}
//           WHERE ts >= {startTimestamp} AND ts <= {endTimestamp}
//           ORDER BY ts {orderDirection}
//           LIMIT {offset}, {pageSize}";
//
//       using (var cmd = connection.CreateCommand(timePointSql))
//       using (var reader = await cmd.ExecuteReaderAsync(cancellationToken))
//       {
//         long? minTime = null;
//         long? maxTime = null;
//
//         while (await reader.ReadAsync(cancellationToken))
//         {
//           var timestamp = reader.GetDateTime(0);
//           long timestampMs = new DateTimeOffset(timestamp).ToUnixTimeMilliseconds();
//
//           if (minTime == null || timestampMs < minTime)
//           {
//             minTime = timestampMs;
//           }
//
//           if (maxTime == null || timestampMs > maxTime)
//           {
//             maxTime = timestampMs;
//           }
//         }
//
//         // 如果找到时间点，返回范围
//         if (minTime.HasValue && maxTime.HasValue)
//         {
//           return (minTime.Value, maxTime.Value);
//         }
//       }
//
//       return null;
//     }
//     catch (Exception ex)
//     {
//       _logger.LogError(ex, "获取分页时间范围失败, 设备ID: {deviceId}", deviceId);
//       return null;
//     }
//   }
//
//   /// <summary>
//   /// 获取设备的最新数据
//   /// </summary>
//   /// <param name="deviceId">设备ID</param>
//   /// <param name="tagNames">标签名称列表，为空时查询所有标签</param>
//   /// <param name="cancellationToken">取消令牌</param>
//   public async Task<TimeSeriesData> GetLatestAsync(
//       string deviceId,
//       IEnumerable<string> tagNames = null,
//       CancellationToken cancellationToken = default)
//   {
//     // 先刷新缓冲区确保数据已写入
//     await _writeBuffer.FlushAsync();
//
//     var connection = await _connectionManager.GetConnectionAsync(cancellationToken);
//     try
//     {
//       var result = new TimeSeriesData
//       {
//         DeviceId = deviceId,
//         ExtraTags = new Dictionary<string, object>()
//       };
//
//       var tagsList = tagNames?.ToList() ?? new List<string>();
//
//       // 如果没有指定标签，先查询该设备所有标签
//       if (tagsList.Count == 0)
//       {
//         tagsList = await GetDeviceTagsAsync(connection, deviceId, cancellationToken);
//       }
//
//       if (tagsList.Count == 0)
//       {
//         return null; // 没有标签数据
//       }
//
//       DateTime? latestTimestamp = null;
//
//       // 按标签查询最新数据
//       foreach (var tagName in tagsList)
//       {
//         string tableName = $"d_{deviceId.Replace('-', '_')}_{tagName.Replace('-', '_')}";
//
//         // 构建查询语句
//         string sql = $@"
//             SELECT LAST(ts), LAST(value), LAST(str_value) FROM {tableName}";
//
//         using var cmd = connection.CreateCommand(sql);
//         using var reader = await cmd.ExecuteReaderAsync(cancellationToken);
//
//         if (await reader.ReadAsync(cancellationToken))
//         {
//           var timestamp = reader.GetDateTime(0);
//           var value = reader.IsDBNull(1) ? null : reader.GetDouble(1);
//           var strValue = reader.IsDBNull(2) ? null : reader.GetString(2);
//
//           // 更新最新时间戳
//           if (latestTimestamp == null || timestamp > latestTimestamp)
//           {
//             latestTimestamp = timestamp;
//             result.Timestamp = timestamp;
//           }
//
//           // 添加标签值
//           if (value != null)
//           {
//             result.ExtraTags[tagName] = value;
//           }
//           else if (strValue != null)
//           {
//             result.ExtraTags[tagName] = strValue;
//           }
//         }
//       }
//
//       return result.ExtraTags.Count > 0 ? result : null;
//     }
//     catch (Exception ex)
//     {
//       _logger.LogError(ex, "获取TDengine最新数据失败, 设备ID: {deviceId}", deviceId);
//       throw;
//     }
//     finally
//     {
//       _connectionManager.ReleaseConnection(connection);
//     }
//   }
//
//   /// <summary>
//   /// 获取多个设备的最新数据
//   /// </summary>
//   /// <param name="deviceIds">设备ID列表</param>
//   /// <param name="tagNames">标签名称列表，为空时查询所有标签</param>
//   /// <param name="cancellationToken">取消令牌</param>
//   public async Task<IDictionary<string, TimeSeriesData>> GetLatestBatchAsync(
//       IEnumerable<string> deviceIds,
//       IEnumerable<string> tagNames = null,
//       CancellationToken cancellationToken = default)
//   {
//     if (deviceIds == null)
//     {
//       throw new ArgumentNullException(nameof(deviceIds));
//     }
//
//     // 先刷新缓冲区确保数据已写入
//     await _writeBuffer.FlushAsync();
//
//     var result = new Dictionary<string, TimeSeriesData>();
//     var deviceIdList = deviceIds.ToList();
//
//     if (deviceIdList.Count == 0)
//     {
//       return result;
//     }
//
//     // 并行获取每个设备的最新数据
//     var tasks = deviceIdList.Select(deviceId => GetLatestAsync(deviceId, tagNames, cancellationToken));
//     var dataList = await Task.WhenAll(tasks);
//
//     // 组装结果
//     for (int i = 0; i < deviceIdList.Count; i++)
//     {
//       var deviceId = deviceIdList[i];
//       var data = dataList[i];
//
//       if (data != null)
//       {
//         result.Add(deviceId, data);
//       }
//     }
//
//     return result;
//   }
//
//   /// <summary>
//   /// 删除指定时间范围内的数据
//   /// </summary>
//   /// <param name="deviceId">设备ID</param>
//   /// <param name="startTime">开始时间</param>
//   /// <param name="endTime">结束时间</param>
//   /// <param name="cancellationToken">取消令牌</param>
//   public async Task<bool> DeleteAsync(
//       string deviceId,
//       DateTime startTime,
//       DateTime endTime,
//       CancellationToken cancellationToken = default)
//   {
//     // 先刷新缓冲区确保数据已写入
//     await _writeBuffer.FlushAsync();
//
//     var connection = await _connectionManager.GetConnectionAsync(cancellationToken);
//     try
//     {
//       // 获取设备的所有标签
//       var tags = await GetDeviceTagsAsync(connection, deviceId, cancellationToken);
//
//       if (tags.Count == 0)
//       {
//         return true; // 没有数据需要删除
//       }
//
//       // 转换时间戳
//       long startTimestamp = new DateTimeOffset(startTime).ToUnixTimeMilliseconds();
//       long endTimestamp = new DateTimeOffset(endTime).ToUnixTimeMilliseconds();
//
//       // 删除每个标签的数据
//       foreach (var tagName in tags)
//       {
//         string tableName = $"d_{deviceId.Replace('-', '_')}_{tagName.Replace('-', '_')}";
//
//         // 构建删除语句
//         string sql = $@"
//             DELETE FROM {tableName}
//             WHERE ts >= {startTimestamp} AND ts <= {endTimestamp}";
//
//         using var cmd = connection.CreateCommand(sql);
//         await cmd.ExecuteNonQueryAsync(cancellationToken);
//       }
//
//       return true;
//     }
//     catch (Exception ex)
//     {
//       _logger.LogError(ex, "删除TDengine时序数据失败, 设备ID: {deviceId}", deviceId);
//       return false;
//     }
//     finally
//     {
//       _connectionManager.ReleaseConnection(connection);
//     }
//   }
//
//   /// <summary>
//   /// 删除设备的所有数据
//   /// </summary>
//   /// <param name="deviceId">设备ID</param>
//   /// <param name="cancellationToken">取消令牌</param>
//   public async Task<bool> DeleteDeviceAsync(string deviceId, CancellationToken cancellationToken = default)
//   {
//     // 先刷新缓冲区确保数据已写入
//     await _writeBuffer.FlushAsync();
//
//     var connection = await _connectionManager.GetConnectionAsync(cancellationToken);
//     try
//     {
//       // 获取设备的所有标签
//       var tags = await GetDeviceTagsAsync(connection, deviceId, cancellationToken);
//
//       if (tags.Count == 0)
//       {
//         return true; // 没有数据需要删除
//       }
//
//       // 删除每个标签的表
//       foreach (var tagName in tags)
//       {
//         string tableName = $"d_{deviceId.Replace('-', '_')}_{tagName.Replace('-', '_')}";
//
//         // 构建删除语句
//         string sql = $"DROP TABLE IF EXISTS {tableName}";
//
//         using var cmd = connection.CreateCommand(sql);
//         await cmd.ExecuteNonQueryAsync(cancellationToken);
//       }
//
//       return true;
//     }
//     catch (Exception ex)
//     {
//       _logger.LogError(ex, "删除TDengine设备数据失败, 设备ID: {deviceId}", deviceId);
//       return false;
//     }
//     finally
//     {
//       _connectionManager.ReleaseConnection(connection);
//     }
//   }
//
//   /// <summary>
//   /// 批量删除设备数据
//   /// </summary>
//   /// <param name="deviceIds">设备ID列表</param>
//   /// <param name="cancellationToken">取消令牌</param>
//   public async Task<bool> DeleteDeviceBatchAsync(
//       IEnumerable<string> deviceIds,
//       CancellationToken cancellationToken = default)
//   {
//     if (deviceIds == null)
//     {
//       throw new ArgumentNullException(nameof(deviceIds));
//     }
//
//     var deviceIdList = deviceIds.ToList();
//
//     if (deviceIdList.Count == 0)
//     {
//       return true;
//     }
//
//     try
//     {
//       // 并行删除每个设备的数据
//       var tasks = deviceIdList.Select(deviceId => DeleteDeviceAsync(deviceId, cancellationToken));
//       var results = await Task.WhenAll(tasks);
//
//       // 所有设备删除成功才返回true
//       return results.All(r => r);
//     }
//     catch (Exception ex)
//     {
//       _logger.LogError(ex, "批量删除TDengine设备数据失败");
//       return false;
//     }
//   }
//
//   /// <summary>
//   /// 获取数据统计信息
//   /// </summary>
//   /// <param name="deviceId">设备ID</param>
//   /// <param name="tagNames">标签名称列表</param>
//   /// <param name="startTime">开始时间</param>
//   /// <param name="endTime">结束时间</param>
//   /// <param name="cancellationToken">取消令牌</param>
//   public async Task<TimeSeriesStatistics> GetStatisticsAsync(
//       string deviceId,
//       IEnumerable<string> tagNames,
//       DateTime startTime,
//       DateTime endTime,
//       CancellationToken cancellationToken = default)
//   {
//     // 先刷新缓冲区确保数据已写入
//     await _writeBuffer.FlushAsync();
//
//     var connection = await _connectionManager.GetConnectionAsync(cancellationToken);
//     try
//     {
//       var result = new TimeSeriesStatistics
//       {
//         DeviceId = deviceId,
//         StartTime = startTime,
//         EndTime = endTime,
//         TagStatistics = new Dictionary<string, TagStatistics>()
//       };
//
//       var tagsList = tagNames?.ToList() ?? new List<string>();
//
//       // 如果没有指定标签，先查询该设备所有标签
//       if (tagsList.Count == 0)
//       {
//         tagsList = await GetDeviceTagsAsync(connection, deviceId, cancellationToken);
//       }
//
//       // 转换时间戳
//       long startTimestamp = new DateTimeOffset(startTime).ToUnixTimeMilliseconds();
//       long endTimestamp = new DateTimeOffset(endTime).ToUnixTimeMilliseconds();
//
//       // 计算每个标签的统计数据
//       foreach (var tagName in tagsList)
//       {
//         string tableName = $"d_{deviceId.Replace('-', '_')}_{tagName.Replace('-', '_')}";
//
//         // 构建查询语句
//         string sql = $@"
//             SELECT 
//                 COUNT(*) as count,
//                 MIN(value) as min_value,
//                 MAX(value) as max_value,
//                 AVG(value) as avg_value,
//                 STDDEV(value) as stddev,
//                 PERCENTILE(value, 90) as percentile90,
//                 FIRST(ts) as first_time,
//                 LAST(ts) as last_time
//             FROM {tableName}
//             WHERE ts >= {startTimestamp} AND ts <= {endTimestamp}";
//
//         using var cmd = connection.CreateCommand(sql);
//         using var reader = await cmd.ExecuteReaderAsync(cancellationToken);
//
//         if (await reader.ReadAsync(cancellationToken))
//         {
//           int count = Convert.ToInt32(reader.GetValue(0));
//
//           // 如果没有数据，跳过
//           if (count == 0)
//           {
//             continue;
//           }
//
//           var tagStats = new TagStatistics
//           {
//             TagName = tagName,
//             Count = count
//           };
//
//           // 只处理数值型标签的统计
//           if (!reader.IsDBNull(1))
//           {
//             tagStats.MinValue = reader.GetDouble(1);
//             tagStats.MaxValue = reader.GetDouble(2);
//             tagStats.AverageValue = reader.GetDouble(3);
//             tagStats.StandardDeviation = reader.IsDBNull(4) ? null : reader.GetDouble(4);
//             tagStats.Percentile90 = reader.IsDBNull(5) ? null : reader.GetDouble(5);
//           }
//
//           tagStats.FirstTime = reader.GetDateTime(6);
//           tagStats.LastTime = reader.GetDateTime(7);
//
//           result.TagStatistics[tagName] = tagStats;
//         }
//       }
//
//       // 计算总记录数
//       result.TotalCount = result.TagStatistics.Values.Sum(ts => ts.Count);
//
//       // 计算整体时间范围
//       if (result.TagStatistics.Count > 0)
//       {
//         result.ActualStartTime = result.TagStatistics.Values.Min(ts => ts.FirstTime);
//         result.ActualEndTime = result.TagStatistics.Values.Max(ts => ts.LastTime);
//       }
//       else
//       {
//         result.ActualStartTime = startTime;
//         result.ActualEndTime = endTime;
//       }
//
//       return result;
//     }
//     catch (Exception ex)
//     {
//       _logger.LogError(ex, "获取TDengine数据统计信息失败, 设备ID: {deviceId}", deviceId);
//       throw;
//     }
//     finally
//     {
//       _connectionManager.ReleaseConnection(connection);
//     }
//   }
//
//   /// <summary>
//   /// 获取存储状态信息
//   /// </summary>
//   /// <param name="cancellationToken">取消令牌</param>
//   public async Task<StorageStatus> GetStorageStatusAsync(CancellationToken cancellationToken = default)
//   {
//     var connection = await _connectionManager.GetConnectionAsync(cancellationToken);
//     try
//     {
//       var status = new StorageStatus
//       {
//         StorageType = StorageType,
//         IsConnected = true,
//         LastCheckTime = DateTime.Now
//       };
//
//       // 获取数据库状态
//       string dbStatusSql = $"SELECT DNODES";
//       using (var cmd = connection.CreateCommand(dbStatusSql))
//       using (var reader = await cmd.ExecuteReaderAsync(cancellationToken))
//       {
//         while (await reader.ReadAsync(cancellationToken))
//         {
//           status.AdditionalInfo["nodes"] = reader.GetInt32(0).ToString();
//         }
//       }
//
//       // 获取数据库信息
//       string dbInfoSql = $"SELECT DATABASE, TABLE_COUNT, TOTAL_SIZE FROM INFORMATION_SCHEMA.INS_DATABASES WHERE DATABASE='{_options.Database}'";
//       using (var cmd = connection.CreateCommand(dbInfoSql))
//       using (var reader = await cmd.ExecuteReaderAsync(cancellationToken))
//       {
//         if (await reader.ReadAsync(cancellationToken))
//         {
//           status.DatabaseName = reader.GetString(0);
//           status.TableCount = reader.GetInt32(1);
//           status.DataSize = reader.GetInt64(2);
//         }
//       }
//
//       // 获取总记录数
//       string recordCountSql = $"SELECT COUNT(*) FROM device_metrics";
//       using (var cmd = connection.CreateCommand(recordCountSql))
//       {
//         var result = await cmd.ExecuteScalarAsync(cancellationToken);
//         status.RecordCount = result != null ? Convert.ToInt64(result) : 0;
//       }
//
//       // 获取服务版本
//       string versionSql = $"SELECT SERVER_VERSION()";
//       using (var cmd = connection.CreateCommand(versionSql))
//       {
//         var result = await cmd.ExecuteScalarAsync(cancellationToken);
//         status.AdditionalInfo["version"] = result?.ToString() ?? "Unknown";
//       }
//
//       return status;
//     }
//     catch (Exception ex)
//     {
//       _logger.LogError(ex, "获取TDengine存储状态失败");
//
//       return new StorageStatus
//       {
//         StorageType = StorageType,
//         IsConnected = false,
//         LastCheckTime = DateTime.Now,
//         AdditionalInfo = new Dictionary<string, string>
//         {
//           { "error", ex.Message }
//         }
//       };
//     }
//     finally
//     {
//       _connectionManager.ReleaseConnection(connection);
//     }
//   }
//
//   /// <summary>
//   /// 获取性能报告
//   /// </summary>
//   /// <returns>性能报告</returns>
//   public Task<PerformanceReport> GetPerformanceReportAsync()
//   {
//     var report = new PerformanceReport
//     {
//       StorageType = StorageType,
//       ReportTime = DateTime.Now,
//       Metrics = new Dictionary<string, MetricsSnapshot>
//       {
//         {
//           "写入性能", new MetricsSnapshot
//           {
//             Name = "写入性能",
//             Value = 10000, // 每秒处理点数
//             Unit = "点/秒",
//             Description = "TDengine写入性能"
//           }
//         },
//         {
//           "查询性能", new MetricsSnapshot
//           {
//             Name = "查询性能",
//             Value = 50000, // 每秒处理点数
//             Unit = "点/秒",
//             Description = "TDengine查询性能"
//           }
//         },
//         {
//           "存储效率", new MetricsSnapshot
//           {
//             Name = "存储效率",
//             Value = 0.25, // 字节/点
//             Unit = "字节/点",
//             Description = "TDengine存储效率"
//           }
//         }
//       }
//     };
//
//     return Task.FromResult(report);
//   }
//
//   /// <summary>
//   /// 获取查询建议
//   /// </summary>
//   /// <param name="deviceId">设备ID</param>
//   /// <param name="tagNames">标签ID</param>
//   /// <returns>查询建议</returns>
//   public async Task<QuerySuggestion> GetQuerySuggestionAsync(string deviceId, IEnumerable<string> tagNames = null)
//   {
//     var connection = await _connectionManager.GetConnectionAsync();
//     try
//     {
//       var suggestion = new QuerySuggestion
//       {
//         DeviceId = deviceId,
//         SuggestedTimeRange = TimeSpan.FromHours(1),
//         SuggestedPageSize = 1000,
//         SuggestedIntervals = new Dictionary<string, TimeSpan>()
//       };
//
//       var tagsList = tagNames?.ToList() ?? new List<string>();
//
//       // 如果没有指定标签，先查询该设备所有标签
//       if (tagsList.Count == 0)
//       {
//         tagsList = await GetDeviceTagsAsync(connection, deviceId, default);
//       }
//
//       // 分析每个标签的数据特征，提供建议
//       foreach (var tagName in tagsList)
//       {
//         string tableName = $"d_{deviceId.Replace('-', '_')}_{tagName.Replace('-', '_')}";
//
//         // 分析数据间隔
//         string sql = $@"
//             SELECT 
//                 (LAST(ts) - FIRST(ts)) / COUNT(*) as avg_interval
//             FROM {tableName}
//             WHERE ts > NOW - 1d";
//
//         using var cmd = connection.CreateCommand(sql);
//         var result = await cmd.ExecuteScalarAsync();
//
//         if (result != null && !Convert.IsDBNull(result))
//         {
//           double avgIntervalMs = Convert.ToDouble(result);
//           suggestion.SuggestedIntervals[tagName] = TimeSpan.FromMilliseconds(avgIntervalMs);
//         }
//         else
//         {
//           suggestion.SuggestedIntervals[tagName] = TimeSpan.FromSeconds(1);
//         }
//       }
//
//       return suggestion;
//     }
//     catch (Exception ex)
//     {
//       _logger.LogError(ex, "获取TDengine查询建议失败, 设备ID: {deviceId}", deviceId);
//
//       // 返回默认建议
//       return new QuerySuggestion
//       {
//         DeviceId = deviceId,
//         SuggestedTimeRange = TimeSpan.FromHours(1),
//         SuggestedPageSize = 1000,
//         SuggestedIntervals = new Dictionary<string, TimeSpan>()
//       };
//     }
//     finally
//     {
//       _connectionManager.ReleaseConnection(connection);
//     }
//   }
//
//   /// <summary>
//   /// 释放资源
//   /// </summary>
//   public void Dispose()
//   {
//     Dispose(true);
//     GC.SuppressFinalize(this);
//   }
//
//   /// <summary>
//   /// 释放资源
//   /// </summary>
//   /// <param name="disposing">是否正在释放托管资源</param>
//   protected virtual void Dispose(bool disposing)
//   {
//     if (_isDisposed)
//     {
//       return;
//     }
//
//     if (disposing)
//     {
//       try
//       {
//         // 关闭写入缓冲池
//         _writeBuffer?.Dispose();
//
//         // 关闭连接管理器
//         _connectionManager?.Dispose();
//
//         _logger.LogInformation("TDengine时序数据存储资源已释放");
//       }
//       catch (Exception ex)
//       {
//         _logger.LogError(ex, "释放TDengine资源失败");
//       }
//     }
//
//     _isDisposed = true;
//   }
// }